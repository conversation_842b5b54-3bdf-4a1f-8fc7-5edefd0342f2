{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, j as Host } from './index-B_U9CtaY.js';\nconst segmentContentCss = \":host{scroll-snap-align:center;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%;min-height:1px;overflow-y:scroll;scrollbar-width:none;-ms-overflow-style:none;}:host::-webkit-scrollbar{display:none}\";\nconst SegmentContent = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n  }\n  render() {\n    return h(Host, {\n      key: 'db6876f2aee7afa1ea8bc147337670faa68fae1c'\n    }, h(\"slot\", {\n      key: 'bc05714a973a5655668679033f5809a1da6db8cc'\n    }));\n  }\n};\nSegmentContent.style = segmentContentCss;\nexport { SegmentContent as ion_segment_content };", "map": {"version": 3, "names": ["r", "registerInstance", "h", "j", "Host", "segmentContentCss", "SegmentContent", "constructor", "hostRef", "render", "key", "style", "ion_segment_content"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@ionic/core/dist/esm/ion-segment-content.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, j as Host } from './index-B_U9CtaY.js';\n\nconst segmentContentCss = \":host{scroll-snap-align:center;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%;min-height:1px;overflow-y:scroll;scrollbar-width:none;-ms-overflow-style:none;}:host::-webkit-scrollbar{display:none}\";\n\nconst SegmentContent = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    render() {\n        return (h(Host, { key: 'db6876f2aee7afa1ea8bc147337670faa68fae1c' }, h(\"slot\", { key: 'bc05714a973a5655668679033f5809a1da6db8cc' })));\n    }\n};\nSegmentContent.style = segmentContentCss;\n\nexport { SegmentContent as ion_segment_content };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,QAAQ,qBAAqB;AAEzE,MAAMC,iBAAiB,GAAG,2NAA2N;AAErP,MAAMC,cAAc,GAAG,MAAM;EACzBC,WAAWA,CAACC,OAAO,EAAE;IACjBP,gBAAgB,CAAC,IAAI,EAAEO,OAAO,CAAC;EACnC;EACAC,MAAMA,CAAA,EAAG;IACL,OAAQP,CAAC,CAACE,IAAI,EAAE;MAAEM,GAAG,EAAE;IAA2C,CAAC,EAAER,CAAC,CAAC,MAAM,EAAE;MAAEQ,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EACxI;AACJ,CAAC;AACDJ,cAAc,CAACK,KAAK,GAAGN,iBAAiB;AAExC,SAASC,cAAc,IAAIM,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}