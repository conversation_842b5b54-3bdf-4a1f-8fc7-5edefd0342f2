{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst NAMESPACE = 'ionic';\nconst BUILD = /* ionic */{\n  experimentalSlotFixes: true,\n  hydratedSelectorName: \"hydrated\",\n  lazyLoad: true,\n  shadowDom: true,\n  slotRelocation: true,\n  updatable: true\n};\n\n// TODO(FW-2832): types\nclass Config {\n  constructor() {\n    this.m = new Map();\n  }\n  reset(configObj) {\n    this.m = new Map(Object.entries(configObj));\n  }\n  get(key, fallback) {\n    const value = this.m.get(key);\n    return value !== undefined ? value : fallback;\n  }\n  getBoolean(key, fallback = false) {\n    const val = this.m.get(key);\n    if (val === undefined) {\n      return fallback;\n    }\n    if (typeof val === 'string') {\n      return val === 'true';\n    }\n    return !!val;\n  }\n  getNumber(key, fallback) {\n    const val = parseFloat(this.m.get(key));\n    return isNaN(val) ? fallback !== undefined ? fallback : NaN : val;\n  }\n  set(key, value) {\n    this.m.set(key, value);\n  }\n}\nconst config = /*@__PURE__*/new Config();\nconst configFromSession = win => {\n  try {\n    const configStr = win.sessionStorage.getItem(IONIC_SESSION_KEY);\n    return configStr !== null ? JSON.parse(configStr) : {};\n  } catch (e) {\n    return {};\n  }\n};\nconst saveConfig = (win, c) => {\n  try {\n    win.sessionStorage.setItem(IONIC_SESSION_KEY, JSON.stringify(c));\n  } catch (e) {\n    return;\n  }\n};\nconst configFromURL = win => {\n  const configObj = {};\n  win.location.search.slice(1).split('&').map(entry => entry.split('=')).map(([key, value]) => {\n    try {\n      return [decodeURIComponent(key), decodeURIComponent(value)];\n    } catch (e) {\n      return ['', ''];\n    }\n  }).filter(([key]) => startsWith(key, IONIC_PREFIX)).map(([key, value]) => [key.slice(IONIC_PREFIX.length), value]).forEach(([key, value]) => {\n    configObj[key] = value;\n  });\n  return configObj;\n};\nconst startsWith = (input, search) => {\n  return input.substr(0, search.length) === search;\n};\nconst IONIC_PREFIX = 'ionic:';\nconst IONIC_SESSION_KEY = 'ionic-persist-config';\nvar LogLevel;\n(function (LogLevel) {\n  LogLevel[\"OFF\"] = \"OFF\";\n  LogLevel[\"ERROR\"] = \"ERROR\";\n  LogLevel[\"WARN\"] = \"WARN\";\n})(LogLevel || (LogLevel = {}));\n/**\n * Logs a warning to the console with an Ionic prefix\n * to indicate the library that is warning the developer.\n *\n * @param message - The string message to be logged to the console.\n */\nconst printIonWarning = (message, ...params) => {\n  const logLevel = config.get('logLevel', LogLevel.WARN);\n  if ([LogLevel.WARN].includes(logLevel)) {\n    return console.warn(`[Ionic Warning]: ${message}`, ...params);\n  }\n};\n/**\n * Logs an error to the console with an Ionic prefix\n * to indicate the library that is warning the developer.\n *\n * @param message - The string message to be logged to the console.\n * @param params - Additional arguments to supply to the console.error.\n */\nconst printIonError = (message, ...params) => {\n  const logLevel = config.get('logLevel', LogLevel.ERROR);\n  if ([LogLevel.ERROR, LogLevel.WARN].includes(logLevel)) {\n    return console.error(`[Ionic Error]: ${message}`, ...params);\n  }\n};\n/**\n * Prints an error informing developers that an implementation requires an element to be used\n * within a specific selector.\n *\n * @param el The web component element this is requiring the element.\n * @param targetSelectors The selector or selectors that were not found.\n */\nconst printRequiredElementError = (el, ...targetSelectors) => {\n  return console.error(`<${el.tagName.toLowerCase()}> must be used inside ${targetSelectors.join(' or ')}.`);\n};\nconst getPlatforms = win => setupPlatforms(win);\nconst isPlatform = (winOrPlatform, platform) => {\n  if (typeof winOrPlatform === 'string') {\n    platform = winOrPlatform;\n    winOrPlatform = undefined;\n  }\n  return getPlatforms(winOrPlatform).includes(platform);\n};\nconst setupPlatforms = (win = window) => {\n  if (typeof win === 'undefined') {\n    return [];\n  }\n  win.Ionic = win.Ionic || {};\n  let platforms = win.Ionic.platforms;\n  if (platforms == null) {\n    platforms = win.Ionic.platforms = detectPlatforms(win);\n    platforms.forEach(p => win.document.documentElement.classList.add(`plt-${p}`));\n  }\n  return platforms;\n};\nconst detectPlatforms = win => {\n  const customPlatformMethods = config.get('platform');\n  return Object.keys(PLATFORMS_MAP).filter(p => {\n    const customMethod = customPlatformMethods === null || customPlatformMethods === void 0 ? void 0 : customPlatformMethods[p];\n    return typeof customMethod === 'function' ? customMethod(win) : PLATFORMS_MAP[p](win);\n  });\n};\nconst isMobileWeb = win => isMobile(win) && !isHybrid(win);\nconst isIpad = win => {\n  // iOS 12 and below\n  if (testUserAgent(win, /iPad/i)) {\n    return true;\n  }\n  // iOS 13+\n  if (testUserAgent(win, /Macintosh/i) && isMobile(win)) {\n    return true;\n  }\n  return false;\n};\nconst isIphone = win => testUserAgent(win, /iPhone/i);\nconst isIOS = win => testUserAgent(win, /iPhone|iPod/i) || isIpad(win);\nconst isAndroid = win => testUserAgent(win, /android|sink/i);\nconst isAndroidTablet = win => {\n  return isAndroid(win) && !testUserAgent(win, /mobile/i);\n};\nconst isPhablet = win => {\n  const width = win.innerWidth;\n  const height = win.innerHeight;\n  const smallest = Math.min(width, height);\n  const largest = Math.max(width, height);\n  return smallest > 390 && smallest < 520 && largest > 620 && largest < 800;\n};\nconst isTablet = win => {\n  const width = win.innerWidth;\n  const height = win.innerHeight;\n  const smallest = Math.min(width, height);\n  const largest = Math.max(width, height);\n  return isIpad(win) || isAndroidTablet(win) || smallest > 460 && smallest < 820 && largest > 780 && largest < 1400;\n};\nconst isMobile = win => matchMedia(win, '(any-pointer:coarse)');\nconst isDesktop = win => !isMobile(win);\nconst isHybrid = win => isCordova(win) || isCapacitorNative(win);\nconst isCordova = win => !!(win['cordova'] || win['phonegap'] || win['PhoneGap']);\nconst isCapacitorNative = win => {\n  const capacitor = win['Capacitor'];\n  // TODO(ROU-11693): Remove when we no longer support Capacitor 2, which does not have isNativePlatform\n  return !!((capacitor === null || capacitor === void 0 ? void 0 : capacitor.isNative) || (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isNativePlatform) && !!capacitor.isNativePlatform());\n};\nconst isElectron = win => testUserAgent(win, /electron/i);\nconst isPWA = win => {\n  var _a;\n  return !!(((_a = win.matchMedia) === null || _a === void 0 ? void 0 : _a.call(win, '(display-mode: standalone)').matches) || win.navigator.standalone);\n};\nconst testUserAgent = (win, expr) => expr.test(win.navigator.userAgent);\nconst matchMedia = (win, query) => {\n  var _a;\n  return (_a = win.matchMedia) === null || _a === void 0 ? void 0 : _a.call(win, query).matches;\n};\nconst PLATFORMS_MAP = {\n  ipad: isIpad,\n  iphone: isIphone,\n  ios: isIOS,\n  android: isAndroid,\n  phablet: isPhablet,\n  tablet: isTablet,\n  cordova: isCordova,\n  capacitor: isCapacitorNative,\n  electron: isElectron,\n  pwa: isPWA,\n  mobile: isMobile,\n  mobileweb: isMobileWeb,\n  desktop: isDesktop,\n  hybrid: isHybrid\n};\n\n// TODO(FW-2832): types\nlet defaultMode;\nconst getIonMode = ref => {\n  return ref && getMode(ref) || defaultMode;\n};\nconst initialize = (userConfig = {}) => {\n  if (typeof window === 'undefined') {\n    return;\n  }\n  const doc = window.document;\n  const win = window;\n  const Ionic = win.Ionic = win.Ionic || {};\n  // create the Ionic.config from raw config object (if it exists)\n  // and convert Ionic.config into a ConfigApi that has a get() fn\n  const configObj = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, configFromSession(win)), {\n    persistConfig: false\n  }), Ionic.config), configFromURL(win)), userConfig);\n  config.reset(configObj);\n  if (config.getBoolean('persistConfig')) {\n    saveConfig(win, configObj);\n  }\n  // Setup platforms\n  setupPlatforms(win);\n  // first see if the mode was set as an attribute on <html>\n  // which could have been set by the user, or by pre-rendering\n  // otherwise get the mode via config settings, and fallback to md\n  Ionic.config = config;\n  Ionic.mode = defaultMode = config.get('mode', doc.documentElement.getAttribute('mode') || (isPlatform(win, 'ios') ? 'ios' : 'md'));\n  config.set('mode', defaultMode);\n  doc.documentElement.setAttribute('mode', defaultMode);\n  doc.documentElement.classList.add(defaultMode);\n  if (config.getBoolean('_testing')) {\n    config.set('animated', false);\n  }\n  const isIonicElement = elm => {\n    var _a;\n    return (_a = elm.tagName) === null || _a === void 0 ? void 0 : _a.startsWith('ION-');\n  };\n  const isAllowedIonicModeValue = elmMode => ['ios', 'md'].includes(elmMode);\n  setMode(elm => {\n    while (elm) {\n      const elmMode = elm.mode || elm.getAttribute('mode');\n      if (elmMode) {\n        if (isAllowedIonicModeValue(elmMode)) {\n          return elmMode;\n        } else if (isIonicElement(elm)) {\n          printIonWarning('Invalid ionic mode: \"' + elmMode + '\", expected: \"ios\" or \"md\"');\n        }\n      }\n      elm = elm.parentElement;\n    }\n    return defaultMode;\n  });\n};\nconst globalScripts = initialize;\nconst globalStyles = \"\";\n\n/*\n Stencil Client Platform v4.33.1 | MIT Licensed | https://stenciljs.com\n */\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all) __defProp(target, name, {\n    get: all[name],\n    enumerable: true\n  });\n};\nvar Build = {\n  isBrowser: true\n};\n\n// src/utils/constants.ts\nvar SVG_NS = \"http://www.w3.org/2000/svg\";\nvar HTML_NS = \"http://www.w3.org/1999/xhtml\";\nvar PrimitiveType = /* @__PURE__ */(PrimitiveType2 => {\n  PrimitiveType2[\"Undefined\"] = \"undefined\";\n  PrimitiveType2[\"Null\"] = \"null\";\n  PrimitiveType2[\"String\"] = \"string\";\n  PrimitiveType2[\"Number\"] = \"number\";\n  PrimitiveType2[\"SpecialNumber\"] = \"number\";\n  PrimitiveType2[\"Boolean\"] = \"boolean\";\n  PrimitiveType2[\"BigInt\"] = \"bigint\";\n  return PrimitiveType2;\n})(PrimitiveType || {});\nvar NonPrimitiveType = /* @__PURE__ */(NonPrimitiveType2 => {\n  NonPrimitiveType2[\"Array\"] = \"array\";\n  NonPrimitiveType2[\"Date\"] = \"date\";\n  NonPrimitiveType2[\"Map\"] = \"map\";\n  NonPrimitiveType2[\"Object\"] = \"object\";\n  NonPrimitiveType2[\"RegularExpression\"] = \"regexp\";\n  NonPrimitiveType2[\"Set\"] = \"set\";\n  NonPrimitiveType2[\"Channel\"] = \"channel\";\n  NonPrimitiveType2[\"Symbol\"] = \"symbol\";\n  return NonPrimitiveType2;\n})(NonPrimitiveType || {});\nvar TYPE_CONSTANT = \"type\";\nvar VALUE_CONSTANT = \"value\";\nvar SERIALIZED_PREFIX = \"serialized:\";\n\n// src/client/client-host-ref.ts\nvar getHostRef = ref => {\n  if (ref.__stencil__getHostRef) {\n    return ref.__stencil__getHostRef();\n  }\n  return void 0;\n};\nvar registerInstance = (lazyInstance, hostRef) => {\n  lazyInstance.__stencil__getHostRef = () => hostRef;\n  hostRef.$lazyInstance$ = lazyInstance;\n};\nvar registerHost = (hostElement, cmpMeta) => {\n  const hostRef = {\n    $flags$: 0,\n    $hostElement$: hostElement,\n    $cmpMeta$: cmpMeta,\n    $instanceValues$: /* @__PURE__ */new Map()\n  };\n  {\n    hostRef.$onInstancePromise$ = new Promise(r => hostRef.$onInstanceResolve$ = r);\n  }\n  {\n    hostRef.$onReadyPromise$ = new Promise(r => hostRef.$onReadyResolve$ = r);\n    hostElement[\"s-p\"] = [];\n    hostElement[\"s-rc\"] = [];\n  }\n  const ref = hostRef;\n  hostElement.__stencil__getHostRef = () => ref;\n  return ref;\n};\nvar isMemberInElement = (elm, memberName) => memberName in elm;\nvar consoleError = (e, el) => (0, console.error)(e, el);\n\n// src/client/client-load-module.ts\nvar cmpModules = /* @__PURE__ */new Map();\nvar loadModule = (cmpMeta, hostRef, hmrVersionId) => {\n  const exportName = cmpMeta.$tagName$.replace(/-/g, \"_\");\n  const bundleId = cmpMeta.$lazyBundleId$;\n  if (!bundleId) {\n    return void 0;\n  }\n  const module = cmpModules.get(bundleId);\n  if (module) {\n    return module[exportName];\n  }\n  /*!__STENCIL_STATIC_IMPORT_SWITCH__*/\n  return import(/* @vite-ignore */\n  /* webpackInclude: /\\.entry\\.js$/ */\n  /* webpackExclude: /\\.system\\.entry\\.js$/ */\n  /* webpackMode: \"lazy\" */\n  `./${bundleId}.entry.js${\"\"}`).then(importedModule => {\n    {\n      cmpModules.set(bundleId, importedModule);\n    }\n    return importedModule[exportName];\n  }, e => {\n    consoleError(e, hostRef.$hostElement$);\n  });\n};\n\n// src/client/client-style.ts\nvar styles = /* @__PURE__ */new Map();\nvar modeResolutionChain = [];\n\n// src/runtime/runtime-constants.ts\nvar CONTENT_REF_ID = \"r\";\nvar ORG_LOCATION_ID = \"o\";\nvar SLOT_NODE_ID = \"s\";\nvar TEXT_NODE_ID = \"t\";\nvar COMMENT_NODE_ID = \"c\";\nvar HYDRATE_ID = \"s-id\";\nvar HYDRATED_STYLE_ID = \"sty-id\";\nvar HYDRATE_CHILD_ID = \"c-id\";\nvar HYDRATED_CSS = \"{visibility:hidden}.hydrated{visibility:inherit}\";\nvar SLOT_FB_CSS = \"slot-fb{display:contents}slot-fb[hidden]{display:none}\";\nvar XLINK_NS = \"http://www.w3.org/1999/xlink\";\nvar win = typeof window !== \"undefined\" ? window : {};\nvar H = win.HTMLElement || class {};\nvar plt = {\n  $flags$: 0,\n  $resourcesUrl$: \"\",\n  jmp: h2 => h2(),\n  raf: h2 => requestAnimationFrame(h2),\n  ael: (el, eventName, listener, opts) => el.addEventListener(eventName, listener, opts),\n  rel: (el, eventName, listener, opts) => el.removeEventListener(eventName, listener, opts),\n  ce: (eventName, opts) => new CustomEvent(eventName, opts)\n};\nvar supportsShadow = BUILD.shadowDom;\nvar supportsListenerOptions = /* @__PURE__ */(() => {\n  var _a;\n  let supportsListenerOptions2 = false;\n  try {\n    (_a = win.document) == null ? void 0 : _a.addEventListener(\"e\", null, Object.defineProperty({}, \"passive\", {\n      get() {\n        supportsListenerOptions2 = true;\n      }\n    }));\n  } catch (e) {}\n  return supportsListenerOptions2;\n})();\nvar promiseResolve = v => Promise.resolve(v);\nvar supportsConstructableStylesheets = /* @__PURE__ */(() => {\n  try {\n    new CSSStyleSheet();\n    return typeof new CSSStyleSheet().replaceSync === \"function\";\n  } catch (e) {}\n  return false;\n})();\nvar queuePending = false;\nvar queueDomReads = [];\nvar queueDomWrites = [];\nvar queueTask = (queue, write) => cb => {\n  queue.push(cb);\n  if (!queuePending) {\n    queuePending = true;\n    if (write && plt.$flags$ & 4 /* queueSync */) {\n      nextTick(flush);\n    } else {\n      plt.raf(flush);\n    }\n  }\n};\nvar consume = queue => {\n  for (let i2 = 0; i2 < queue.length; i2++) {\n    try {\n      queue[i2](performance.now());\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  queue.length = 0;\n};\nvar flush = () => {\n  consume(queueDomReads);\n  {\n    consume(queueDomWrites);\n    if (queuePending = queueDomReads.length > 0) {\n      plt.raf(flush);\n    }\n  }\n};\nvar nextTick = cb => promiseResolve().then(cb);\nvar readTask = /* @__PURE__ */queueTask(queueDomReads, false);\nvar writeTask = /* @__PURE__ */queueTask(queueDomWrites, true);\n\n// src/runtime/asset-path.ts\nvar getAssetPath = path => {\n  const assetUrl = new URL(path, plt.$resourcesUrl$);\n  return assetUrl.origin !== win.location.origin ? assetUrl.href : assetUrl.pathname;\n};\n\n// src/utils/helpers.ts\nvar isDef = v => v != null && v !== void 0;\nvar isComplexType = o => {\n  o = typeof o;\n  return o === \"object\" || o === \"function\";\n};\n\n// src/utils/query-nonce-meta-tag-content.ts\nfunction queryNonceMetaTagContent(doc) {\n  var _a, _b, _c;\n  return (_c = (_b = (_a = doc.head) == null ? void 0 : _a.querySelector('meta[name=\"csp-nonce\"]')) == null ? void 0 : _b.getAttribute(\"content\")) != null ? _c : void 0;\n}\n\n// src/utils/regular-expression.ts\nvar escapeRegExpSpecialCharacters = text => {\n  return text.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n};\n\n// src/utils/remote-value.ts\nvar RemoteValue = class _RemoteValue {\n  /**\n   * Deserializes a LocalValue serialized object back to its original JavaScript representation\n   *\n   * @param serialized The serialized LocalValue object\n   * @returns The original JavaScript value/object\n   */\n  static fromLocalValue(serialized) {\n    const type = serialized[TYPE_CONSTANT];\n    const value = VALUE_CONSTANT in serialized ? serialized[VALUE_CONSTANT] : void 0;\n    switch (type) {\n      case \"string\" /* String */:\n        return value;\n      case \"boolean\" /* Boolean */:\n        return value;\n      case \"bigint\" /* BigInt */:\n        return BigInt(value);\n      case \"undefined\" /* Undefined */:\n        return void 0;\n      case \"null\" /* Null */:\n        return null;\n      case \"number\" /* Number */:\n        if (value === \"NaN\") return NaN;\n        if (value === \"-0\") return -0;\n        if (value === \"Infinity\") return Infinity;\n        if (value === \"-Infinity\") return -Infinity;\n        return value;\n      case \"array\" /* Array */:\n        return value.map(item => _RemoteValue.fromLocalValue(item));\n      case \"date\" /* Date */:\n        return new Date(value);\n      case \"map\" /* Map */:\n        const map2 = /* @__PURE__ */new Map();\n        for (const [key, val] of value) {\n          const deserializedKey = typeof key === \"object\" && key !== null ? _RemoteValue.fromLocalValue(key) : key;\n          const deserializedValue = _RemoteValue.fromLocalValue(val);\n          map2.set(deserializedKey, deserializedValue);\n        }\n        return map2;\n      case \"object\" /* Object */:\n        const obj = {};\n        for (const [key, val] of value) {\n          obj[key] = _RemoteValue.fromLocalValue(val);\n        }\n        return obj;\n      case \"regexp\" /* RegularExpression */:\n        const {\n          pattern,\n          flags\n        } = value;\n        return new RegExp(pattern, flags);\n      case \"set\" /* Set */:\n        const set = /* @__PURE__ */new Set();\n        for (const item of value) {\n          set.add(_RemoteValue.fromLocalValue(item));\n        }\n        return set;\n      case \"symbol\" /* Symbol */:\n        return Symbol(value);\n      default:\n        throw new Error(`Unsupported type: ${type}`);\n    }\n  }\n  /**\n   * Utility method to deserialize multiple LocalValues at once\n   *\n   * @param serializedValues Array of serialized LocalValue objects\n   * @returns Array of deserialized JavaScript values\n   */\n  static fromLocalValueArray(serializedValues) {\n    return serializedValues.map(value => _RemoteValue.fromLocalValue(value));\n  }\n  /**\n   * Verifies if the given object matches the structure of a serialized LocalValue\n   *\n   * @param obj Object to verify\n   * @returns boolean indicating if the object has LocalValue structure\n   */\n  static isLocalValueObject(obj) {\n    if (typeof obj !== \"object\" || obj === null) {\n      return false;\n    }\n    if (!obj.hasOwnProperty(TYPE_CONSTANT)) {\n      return false;\n    }\n    const type = obj[TYPE_CONSTANT];\n    const hasTypeProperty = Object.values({\n      ...PrimitiveType,\n      ...NonPrimitiveType\n    }).includes(type);\n    if (!hasTypeProperty) {\n      return false;\n    }\n    if (type !== \"null\" /* Null */ && type !== \"undefined\" /* Undefined */) {\n      return obj.hasOwnProperty(VALUE_CONSTANT);\n    }\n    return true;\n  }\n};\n\n// src/utils/result.ts\nvar result_exports = {};\n__export(result_exports, {\n  err: () => err,\n  map: () => map,\n  ok: () => ok,\n  unwrap: () => unwrap,\n  unwrapErr: () => unwrapErr\n});\nvar ok = value => ({\n  isOk: true,\n  isErr: false,\n  value\n});\nvar err = value => ({\n  isOk: false,\n  isErr: true,\n  value\n});\nfunction map(result, fn) {\n  if (result.isOk) {\n    const val = fn(result.value);\n    if (val instanceof Promise) {\n      return val.then(newVal => ok(newVal));\n    } else {\n      return ok(val);\n    }\n  }\n  if (result.isErr) {\n    const value = result.value;\n    return err(value);\n  }\n  throw \"should never get here\";\n}\nvar unwrap = result => {\n  if (result.isOk) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\nvar unwrapErr = result => {\n  if (result.isErr) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\n\n// src/utils/serialize.ts\nfunction deserializeProperty(value) {\n  if (typeof value !== \"string\" || !value.startsWith(SERIALIZED_PREFIX)) {\n    return value;\n  }\n  return RemoteValue.fromLocalValue(JSON.parse(atob(value.slice(SERIALIZED_PREFIX.length))));\n}\nfunction createShadowRoot(cmpMeta) {\n  const shadowRoot = this.attachShadow({\n    mode: \"open\",\n    delegatesFocus: !!(cmpMeta.$flags$ & 16 /* shadowDelegatesFocus */)\n  });\n  if (supportsConstructableStylesheets) {\n    const sheet = new CSSStyleSheet();\n    sheet.replaceSync(globalStyles);\n    shadowRoot.adoptedStyleSheets.push(sheet);\n  }\n}\nvar updateFallbackSlotVisibility = elm => {\n  const childNodes = internalCall(elm, \"childNodes\");\n  if (elm.tagName && elm.tagName.includes(\"-\") && elm[\"s-cr\"] && elm.tagName !== \"SLOT-FB\") {\n    getHostSlotNodes(childNodes, elm.tagName).forEach(slotNode => {\n      if (slotNode.nodeType === 1 /* ElementNode */ && slotNode.tagName === \"SLOT-FB\") {\n        if (getSlotChildSiblings(slotNode, getSlotName(slotNode), false).length) {\n          slotNode.hidden = true;\n        } else {\n          slotNode.hidden = false;\n        }\n      }\n    });\n  }\n  let i2 = 0;\n  for (i2 = 0; i2 < childNodes.length; i2++) {\n    const childNode = childNodes[i2];\n    if (childNode.nodeType === 1 /* ElementNode */ && internalCall(childNode, \"childNodes\").length) {\n      updateFallbackSlotVisibility(childNode);\n    }\n  }\n};\nvar getSlottedChildNodes = childNodes => {\n  const result = [];\n  for (let i2 = 0; i2 < childNodes.length; i2++) {\n    const slottedNode = childNodes[i2][\"s-nr\"] || void 0;\n    if (slottedNode && slottedNode.isConnected) {\n      result.push(slottedNode);\n    }\n  }\n  return result;\n};\nfunction getHostSlotNodes(childNodes, hostName, slotName) {\n  let i2 = 0;\n  let slottedNodes = [];\n  let childNode;\n  for (; i2 < childNodes.length; i2++) {\n    childNode = childNodes[i2];\n    if (childNode[\"s-sr\"] && (!hostName || childNode[\"s-hn\"] === hostName) && (slotName === void 0 || getSlotName(childNode) === slotName)) {\n      slottedNodes.push(childNode);\n      if (typeof slotName !== \"undefined\") return slottedNodes;\n    }\n    slottedNodes = [...slottedNodes, ...getHostSlotNodes(childNode.childNodes, hostName, slotName)];\n  }\n  return slottedNodes;\n}\nvar getSlotChildSiblings = (slot, slotName, includeSlot = true) => {\n  const childNodes = [];\n  if (includeSlot && slot[\"s-sr\"] || !slot[\"s-sr\"]) childNodes.push(slot);\n  let node = slot;\n  while (node = node.nextSibling) {\n    if (getSlotName(node) === slotName && (includeSlot || !node[\"s-sr\"])) childNodes.push(node);\n  }\n  return childNodes;\n};\nvar isNodeLocatedInSlot = (nodeToRelocate, slotName) => {\n  if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n    if (nodeToRelocate.getAttribute(\"slot\") === null && slotName === \"\") {\n      return true;\n    }\n    if (nodeToRelocate.getAttribute(\"slot\") === slotName) {\n      return true;\n    }\n    return false;\n  }\n  if (nodeToRelocate[\"s-sn\"] === slotName) {\n    return true;\n  }\n  return slotName === \"\";\n};\nvar addSlotRelocateNode = (newChild, slotNode, prepend, position) => {\n  if (newChild[\"s-ol\"] && newChild[\"s-ol\"].isConnected) {\n    return;\n  }\n  const slottedNodeLocation = document.createTextNode(\"\");\n  slottedNodeLocation[\"s-nr\"] = newChild;\n  if (!slotNode[\"s-cr\"] || !slotNode[\"s-cr\"].parentNode) return;\n  const parent = slotNode[\"s-cr\"].parentNode;\n  const appendMethod = prepend ? internalCall(parent, \"prepend\") : internalCall(parent, \"appendChild\");\n  if (typeof position !== \"undefined\") {\n    slottedNodeLocation[\"s-oo\"] = position;\n    const childNodes = internalCall(parent, \"childNodes\");\n    const slotRelocateNodes = [slottedNodeLocation];\n    childNodes.forEach(n => {\n      if (n[\"s-nr\"]) slotRelocateNodes.push(n);\n    });\n    slotRelocateNodes.sort((a, b) => {\n      if (!a[\"s-oo\"] || a[\"s-oo\"] < (b[\"s-oo\"] || 0)) return -1;else if (!b[\"s-oo\"] || b[\"s-oo\"] < a[\"s-oo\"]) return 1;\n      return 0;\n    });\n    slotRelocateNodes.forEach(n => appendMethod.call(parent, n));\n  } else {\n    appendMethod.call(parent, slottedNodeLocation);\n  }\n  newChild[\"s-ol\"] = slottedNodeLocation;\n  newChild[\"s-sh\"] = slotNode[\"s-hn\"];\n};\nvar getSlotName = node => typeof node[\"s-sn\"] === \"string\" ? node[\"s-sn\"] : node.nodeType === 1 && node.getAttribute(\"slot\") || void 0;\nfunction patchSlotNode(node) {\n  if (node.assignedElements || node.assignedNodes || !node[\"s-sr\"]) return;\n  const assignedFactory = elementsOnly => function (opts) {\n    const toReturn = [];\n    const slotName = this[\"s-sn\"];\n    if (opts == null ? void 0 : opts.flatten) {\n      console.error(`\n          Flattening is not supported for Stencil non-shadow slots. \n          You can use \\`.childNodes\\` to nested slot fallback content.\n          If you have a particular use case, please open an issue on the Stencil repo.\n        `);\n    }\n    const parent = this[\"s-cr\"].parentElement;\n    const slottedNodes = parent.__childNodes ? parent.childNodes : getSlottedChildNodes(parent.childNodes);\n    slottedNodes.forEach(n => {\n      if (slotName === getSlotName(n)) {\n        toReturn.push(n);\n      }\n    });\n    if (elementsOnly) {\n      return toReturn.filter(n => n.nodeType === 1 /* ElementNode */);\n    }\n    return toReturn;\n  }.bind(node);\n  node.assignedElements = assignedFactory(true);\n  node.assignedNodes = assignedFactory(false);\n}\nfunction dispatchSlotChangeEvent(elm) {\n  elm.dispatchEvent(new CustomEvent(\"slotchange\", {\n    bubbles: false,\n    cancelable: false,\n    composed: false\n  }));\n}\nfunction findSlotFromSlottedNode(slottedNode, parentHost) {\n  var _a;\n  parentHost = parentHost || ((_a = slottedNode[\"s-ol\"]) == null ? void 0 : _a.parentElement);\n  if (!parentHost) return {\n    slotNode: null,\n    slotName: \"\"\n  };\n  const slotName = slottedNode[\"s-sn\"] = getSlotName(slottedNode) || \"\";\n  const childNodes = internalCall(parentHost, \"childNodes\");\n  const slotNode = getHostSlotNodes(childNodes, parentHost.tagName, slotName)[0];\n  return {\n    slotNode,\n    slotName\n  };\n}\n\n// src/runtime/dom-extras.ts\nvar patchPseudoShadowDom = hostElementPrototype => {\n  patchCloneNode(hostElementPrototype);\n  patchSlotAppendChild(hostElementPrototype);\n  patchSlotAppend(hostElementPrototype);\n  patchSlotPrepend(hostElementPrototype);\n  patchSlotInsertAdjacentElement(hostElementPrototype);\n  patchSlotInsertAdjacentHTML(hostElementPrototype);\n  patchSlotInsertAdjacentText(hostElementPrototype);\n  patchInsertBefore(hostElementPrototype);\n  patchTextContent(hostElementPrototype);\n  patchChildSlotNodes(hostElementPrototype);\n  patchSlotRemoveChild(hostElementPrototype);\n};\nvar patchCloneNode = HostElementPrototype => {\n  const orgCloneNode = HostElementPrototype.cloneNode;\n  HostElementPrototype.cloneNode = function (deep) {\n    const srcNode = this;\n    const isShadowDom = srcNode.shadowRoot && supportsShadow;\n    const clonedNode = orgCloneNode.call(srcNode, isShadowDom ? deep : false);\n    if (!isShadowDom && deep) {\n      let i2 = 0;\n      let slotted, nonStencilNode;\n      const stencilPrivates = [\"s-id\", \"s-cr\", \"s-lr\", \"s-rc\", \"s-sc\", \"s-p\", \"s-cn\", \"s-sr\", \"s-sn\", \"s-hn\", \"s-ol\", \"s-nr\", \"s-si\", \"s-rf\", \"s-scs\"];\n      const childNodes = this.__childNodes || this.childNodes;\n      for (; i2 < childNodes.length; i2++) {\n        slotted = childNodes[i2][\"s-nr\"];\n        nonStencilNode = stencilPrivates.every(privateField => !childNodes[i2][privateField]);\n        if (slotted) {\n          if (clonedNode.__appendChild) {\n            clonedNode.__appendChild(slotted.cloneNode(true));\n          } else {\n            clonedNode.appendChild(slotted.cloneNode(true));\n          }\n        }\n        if (nonStencilNode) {\n          clonedNode.appendChild(childNodes[i2].cloneNode(true));\n        }\n      }\n    }\n    return clonedNode;\n  };\n};\nvar patchSlotAppendChild = HostElementPrototype => {\n  HostElementPrototype.__appendChild = HostElementPrototype.appendChild;\n  HostElementPrototype.appendChild = function (newChild) {\n    const {\n      slotName,\n      slotNode\n    } = findSlotFromSlottedNode(newChild, this);\n    if (slotNode) {\n      addSlotRelocateNode(newChild, slotNode);\n      const slotChildNodes = getSlotChildSiblings(slotNode, slotName);\n      const appendAfter = slotChildNodes[slotChildNodes.length - 1];\n      const parent = internalCall(appendAfter, \"parentNode\");\n      const insertedNode = internalCall(parent, \"insertBefore\")(newChild, appendAfter.nextSibling);\n      dispatchSlotChangeEvent(slotNode);\n      updateFallbackSlotVisibility(this);\n      return insertedNode;\n    }\n    return this.__appendChild(newChild);\n  };\n};\nvar patchSlotRemoveChild = ElementPrototype => {\n  ElementPrototype.__removeChild = ElementPrototype.removeChild;\n  ElementPrototype.removeChild = function (toRemove) {\n    if (toRemove && typeof toRemove[\"s-sn\"] !== \"undefined\") {\n      const childNodes = this.__childNodes || this.childNodes;\n      const slotNode = getHostSlotNodes(childNodes, this.tagName, toRemove[\"s-sn\"]);\n      if (slotNode && toRemove.isConnected) {\n        toRemove.remove();\n        updateFallbackSlotVisibility(this);\n        return;\n      }\n    }\n    return this.__removeChild(toRemove);\n  };\n};\nvar patchSlotPrepend = HostElementPrototype => {\n  HostElementPrototype.__prepend = HostElementPrototype.prepend;\n  HostElementPrototype.prepend = function (...newChildren) {\n    newChildren.forEach(newChild => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      const slotName = (newChild[\"s-sn\"] = getSlotName(newChild)) || \"\";\n      const childNodes = internalCall(this, \"childNodes\");\n      const slotNode = getHostSlotNodes(childNodes, this.tagName, slotName)[0];\n      if (slotNode) {\n        addSlotRelocateNode(newChild, slotNode, true);\n        const slotChildNodes = getSlotChildSiblings(slotNode, slotName);\n        const appendAfter = slotChildNodes[0];\n        const parent = internalCall(appendAfter, \"parentNode\");\n        const toReturn = internalCall(parent, \"insertBefore\")(newChild, internalCall(appendAfter, \"nextSibling\"));\n        dispatchSlotChangeEvent(slotNode);\n        return toReturn;\n      }\n      if (newChild.nodeType === 1 && !!newChild.getAttribute(\"slot\")) {\n        newChild.hidden = true;\n      }\n      return HostElementPrototype.__prepend(newChild);\n    });\n  };\n};\nvar patchSlotAppend = HostElementPrototype => {\n  HostElementPrototype.__append = HostElementPrototype.append;\n  HostElementPrototype.append = function (...newChildren) {\n    newChildren.forEach(newChild => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      this.appendChild(newChild);\n    });\n  };\n};\nvar patchSlotInsertAdjacentHTML = HostElementPrototype => {\n  const originalInsertAdjacentHtml = HostElementPrototype.insertAdjacentHTML;\n  HostElementPrototype.insertAdjacentHTML = function (position, text) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentHtml.call(this, position, text);\n    }\n    const container = this.ownerDocument.createElement(\"_\");\n    let node;\n    container.innerHTML = text;\n    if (position === \"afterbegin\") {\n      while (node = container.firstChild) {\n        this.prepend(node);\n      }\n    } else if (position === \"beforeend\") {\n      while (node = container.firstChild) {\n        this.append(node);\n      }\n    }\n  };\n};\nvar patchSlotInsertAdjacentText = HostElementPrototype => {\n  HostElementPrototype.insertAdjacentText = function (position, text) {\n    this.insertAdjacentHTML(position, text);\n  };\n};\nvar patchInsertBefore = HostElementPrototype => {\n  const eleProto = HostElementPrototype;\n  if (eleProto.__insertBefore) return;\n  eleProto.__insertBefore = HostElementPrototype.insertBefore;\n  HostElementPrototype.insertBefore = function (newChild, currentChild) {\n    const {\n      slotName,\n      slotNode\n    } = findSlotFromSlottedNode(newChild, this);\n    const slottedNodes = this.__childNodes ? this.childNodes : getSlottedChildNodes(this.childNodes);\n    if (slotNode) {\n      let found = false;\n      slottedNodes.forEach(childNode => {\n        if (childNode === currentChild || currentChild === null) {\n          found = true;\n          if (currentChild === null || slotName !== currentChild[\"s-sn\"]) {\n            this.appendChild(newChild);\n            return;\n          }\n          if (slotName === currentChild[\"s-sn\"]) {\n            addSlotRelocateNode(newChild, slotNode);\n            const parent = internalCall(currentChild, \"parentNode\");\n            internalCall(parent, \"insertBefore\")(newChild, currentChild);\n            dispatchSlotChangeEvent(slotNode);\n          }\n          return;\n        }\n      });\n      if (found) return newChild;\n    }\n    const parentNode = currentChild == null ? void 0 : currentChild.__parentNode;\n    if (parentNode && !this.isSameNode(parentNode)) {\n      return this.appendChild(newChild);\n    }\n    return this.__insertBefore(newChild, currentChild);\n  };\n};\nvar patchSlotInsertAdjacentElement = HostElementPrototype => {\n  const originalInsertAdjacentElement = HostElementPrototype.insertAdjacentElement;\n  HostElementPrototype.insertAdjacentElement = function (position, element) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentElement.call(this, position, element);\n    }\n    if (position === \"afterbegin\") {\n      this.prepend(element);\n      return element;\n    } else if (position === \"beforeend\") {\n      this.append(element);\n      return element;\n    }\n    return element;\n  };\n};\nvar patchTextContent = hostElementPrototype => {\n  patchHostOriginalAccessor(\"textContent\", hostElementPrototype);\n  Object.defineProperty(hostElementPrototype, \"textContent\", {\n    get: function () {\n      let text = \"\";\n      const childNodes = this.__childNodes ? this.childNodes : getSlottedChildNodes(this.childNodes);\n      childNodes.forEach(node => text += node.textContent || \"\");\n      return text;\n    },\n    set: function (value) {\n      const childNodes = this.__childNodes ? this.childNodes : getSlottedChildNodes(this.childNodes);\n      childNodes.forEach(node => {\n        if (node[\"s-ol\"]) node[\"s-ol\"].remove();\n        node.remove();\n      });\n      this.insertAdjacentHTML(\"beforeend\", value);\n    }\n  });\n};\nvar patchChildSlotNodes = elm => {\n  class FakeNodeList extends Array {\n    item(n) {\n      return this[n];\n    }\n  }\n  patchHostOriginalAccessor(\"children\", elm);\n  Object.defineProperty(elm, \"children\", {\n    get() {\n      return this.childNodes.filter(n => n.nodeType === 1);\n    }\n  });\n  Object.defineProperty(elm, \"childElementCount\", {\n    get() {\n      return this.children.length;\n    }\n  });\n  patchHostOriginalAccessor(\"firstChild\", elm);\n  Object.defineProperty(elm, \"firstChild\", {\n    get() {\n      return this.childNodes[0];\n    }\n  });\n  patchHostOriginalAccessor(\"lastChild\", elm);\n  Object.defineProperty(elm, \"lastChild\", {\n    get() {\n      return this.childNodes[this.childNodes.length - 1];\n    }\n  });\n  patchHostOriginalAccessor(\"childNodes\", elm);\n  Object.defineProperty(elm, \"childNodes\", {\n    get() {\n      const result = new FakeNodeList();\n      result.push(...getSlottedChildNodes(this.__childNodes));\n      return result;\n    }\n  });\n};\nvar patchSlottedNode = node => {\n  if (!node || node.__nextSibling !== void 0 || !globalThis.Node) return;\n  patchNextSibling(node);\n  patchPreviousSibling(node);\n  patchParentNode(node);\n  if (node.nodeType === Node.ELEMENT_NODE) {\n    patchNextElementSibling(node);\n    patchPreviousElementSibling(node);\n  }\n};\nvar patchNextSibling = node => {\n  if (!node || node.__nextSibling) return;\n  patchHostOriginalAccessor(\"nextSibling\", node);\n  Object.defineProperty(node, \"nextSibling\", {\n    get: function () {\n      var _a;\n      const parentNodes = (_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode.childNodes;\n      const index = parentNodes == null ? void 0 : parentNodes.indexOf(this);\n      if (parentNodes && index > -1) {\n        return parentNodes[index + 1];\n      }\n      return this.__nextSibling;\n    }\n  });\n};\nvar patchNextElementSibling = element => {\n  if (!element || element.__nextElementSibling) return;\n  patchHostOriginalAccessor(\"nextElementSibling\", element);\n  Object.defineProperty(element, \"nextElementSibling\", {\n    get: function () {\n      var _a;\n      const parentEles = (_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode.children;\n      const index = parentEles == null ? void 0 : parentEles.indexOf(this);\n      if (parentEles && index > -1) {\n        return parentEles[index + 1];\n      }\n      return this.__nextElementSibling;\n    }\n  });\n};\nvar patchPreviousSibling = node => {\n  if (!node || node.__previousSibling) return;\n  patchHostOriginalAccessor(\"previousSibling\", node);\n  Object.defineProperty(node, \"previousSibling\", {\n    get: function () {\n      var _a;\n      const parentNodes = (_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode.childNodes;\n      const index = parentNodes == null ? void 0 : parentNodes.indexOf(this);\n      if (parentNodes && index > -1) {\n        return parentNodes[index - 1];\n      }\n      return this.__previousSibling;\n    }\n  });\n};\nvar patchPreviousElementSibling = element => {\n  if (!element || element.__previousElementSibling) return;\n  patchHostOriginalAccessor(\"previousElementSibling\", element);\n  Object.defineProperty(element, \"previousElementSibling\", {\n    get: function () {\n      var _a;\n      const parentNodes = (_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode.children;\n      const index = parentNodes == null ? void 0 : parentNodes.indexOf(this);\n      if (parentNodes && index > -1) {\n        return parentNodes[index - 1];\n      }\n      return this.__previousElementSibling;\n    }\n  });\n};\nvar patchParentNode = node => {\n  if (!node || node.__parentNode) return;\n  patchHostOriginalAccessor(\"parentNode\", node);\n  Object.defineProperty(node, \"parentNode\", {\n    get: function () {\n      var _a;\n      return ((_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode) || this.__parentNode;\n    },\n    set: function (value) {\n      this.__parentNode = value;\n    }\n  });\n};\nvar validElementPatches = [\"children\", \"nextElementSibling\", \"previousElementSibling\"];\nvar validNodesPatches = [\"childNodes\", \"firstChild\", \"lastChild\", \"nextSibling\", \"previousSibling\", \"textContent\", \"parentNode\"];\nfunction patchHostOriginalAccessor(accessorName, node) {\n  let accessor;\n  if (validElementPatches.includes(accessorName)) {\n    accessor = Object.getOwnPropertyDescriptor(Element.prototype, accessorName);\n  } else if (validNodesPatches.includes(accessorName)) {\n    accessor = Object.getOwnPropertyDescriptor(Node.prototype, accessorName);\n  }\n  if (!accessor) {\n    accessor = Object.getOwnPropertyDescriptor(node, accessorName);\n  }\n  if (accessor) Object.defineProperty(node, \"__\" + accessorName, accessor);\n}\nfunction internalCall(node, method) {\n  if (\"__\" + method in node) {\n    const toReturn = node[\"__\" + method];\n    if (typeof toReturn !== \"function\") return toReturn;\n    return toReturn.bind(node);\n  } else {\n    if (typeof node[method] !== \"function\") return node[method];\n    return node[method].bind(node);\n  }\n}\nvar createTime = (fnName, tagName = \"\") => {\n  {\n    return () => {\n      return;\n    };\n  }\n};\nvar uniqueTime = (key, measureText) => {\n  {\n    return () => {\n      return;\n    };\n  }\n};\nvar h = (nodeName, vnodeData, ...children) => {\n  let child = null;\n  let key = null;\n  let slotName = null;\n  let simple = false;\n  let lastSimple = false;\n  const vNodeChildren = [];\n  const walk = c => {\n    for (let i2 = 0; i2 < c.length; i2++) {\n      child = c[i2];\n      if (Array.isArray(child)) {\n        walk(child);\n      } else if (child != null && typeof child !== \"boolean\") {\n        if (simple = typeof nodeName !== \"function\" && !isComplexType(child)) {\n          child = String(child);\n        }\n        if (simple && lastSimple) {\n          vNodeChildren[vNodeChildren.length - 1].$text$ += child;\n        } else {\n          vNodeChildren.push(simple ? newVNode(null, child) : child);\n        }\n        lastSimple = simple;\n      }\n    }\n  };\n  walk(children);\n  if (vnodeData) {\n    if (vnodeData.key) {\n      key = vnodeData.key;\n    }\n    if (vnodeData.name) {\n      slotName = vnodeData.name;\n    }\n    {\n      const classData = vnodeData.className || vnodeData.class;\n      if (classData) {\n        vnodeData.class = typeof classData !== \"object\" ? classData : Object.keys(classData).filter(k => classData[k]).join(\" \");\n      }\n    }\n  }\n  if (typeof nodeName === \"function\") {\n    return nodeName(vnodeData === null ? {} : vnodeData, vNodeChildren, vdomFnUtils);\n  }\n  const vnode = newVNode(nodeName, null);\n  vnode.$attrs$ = vnodeData;\n  if (vNodeChildren.length > 0) {\n    vnode.$children$ = vNodeChildren;\n  }\n  {\n    vnode.$key$ = key;\n  }\n  {\n    vnode.$name$ = slotName;\n  }\n  return vnode;\n};\nvar newVNode = (tag, text) => {\n  const vnode = {\n    $flags$: 0,\n    $tag$: tag,\n    $text$: text,\n    $elm$: null,\n    $children$: null\n  };\n  {\n    vnode.$attrs$ = null;\n  }\n  {\n    vnode.$key$ = null;\n  }\n  {\n    vnode.$name$ = null;\n  }\n  return vnode;\n};\nvar Host = {};\nvar isHost = node => node && node.$tag$ === Host;\nvar vdomFnUtils = {\n  forEach: (children, cb) => children.map(convertToPublic).forEach(cb),\n  map: (children, cb) => children.map(convertToPublic).map(cb).map(convertToPrivate)\n};\nvar convertToPublic = node => ({\n  vattrs: node.$attrs$,\n  vchildren: node.$children$,\n  vkey: node.$key$,\n  vname: node.$name$,\n  vtag: node.$tag$,\n  vtext: node.$text$\n});\nvar convertToPrivate = node => {\n  if (typeof node.vtag === \"function\") {\n    const vnodeData = {\n      ...node.vattrs\n    };\n    if (node.vkey) {\n      vnodeData.key = node.vkey;\n    }\n    if (node.vname) {\n      vnodeData.name = node.vname;\n    }\n    return h(node.vtag, vnodeData, ...(node.vchildren || []));\n  }\n  const vnode = newVNode(node.vtag, node.vtext);\n  vnode.$attrs$ = node.vattrs;\n  vnode.$children$ = node.vchildren;\n  vnode.$key$ = node.vkey;\n  vnode.$name$ = node.vname;\n  return vnode;\n};\n\n// src/runtime/client-hydrate.ts\nvar initializeClientHydrate = (hostElm, tagName, hostId, hostRef) => {\n  var _a;\n  const endHydrate = createTime(\"hydrateClient\", tagName);\n  const shadowRoot = hostElm.shadowRoot;\n  const childRenderNodes = [];\n  const slotNodes = [];\n  const slottedNodes = [];\n  const shadowRootNodes = shadowRoot ? [] : null;\n  const vnode = newVNode(tagName, null);\n  vnode.$elm$ = hostElm;\n  const members = Object.entries(((_a = hostRef.$cmpMeta$) == null ? void 0 : _a.$members$) || {});\n  members.forEach(([memberName, [memberFlags, metaAttributeName]]) => {\n    var _a2;\n    if (!(memberFlags & 31 /* Prop */)) {\n      return;\n    }\n    const attributeName = metaAttributeName || memberName;\n    const attrVal = hostElm.getAttribute(attributeName);\n    if (attrVal !== null) {\n      const attrPropVal = parsePropertyValue(attrVal, memberFlags);\n      (_a2 = hostRef == null ? void 0 : hostRef.$instanceValues$) == null ? void 0 : _a2.set(memberName, attrPropVal);\n    }\n  });\n  let scopeId2;\n  {\n    const cmpMeta = hostRef.$cmpMeta$;\n    if (cmpMeta && cmpMeta.$flags$ & 10 /* needsScopedEncapsulation */ && hostElm[\"s-sc\"]) {\n      scopeId2 = hostElm[\"s-sc\"];\n      hostElm.classList.add(scopeId2 + \"-h\");\n    } else if (hostElm[\"s-sc\"]) {\n      delete hostElm[\"s-sc\"];\n    }\n  }\n  if (win.document && (!plt.$orgLocNodes$ || !plt.$orgLocNodes$.size)) {\n    initializeDocumentHydrate(win.document.body, plt.$orgLocNodes$ = /* @__PURE__ */new Map());\n  }\n  hostElm[HYDRATE_ID] = hostId;\n  hostElm.removeAttribute(HYDRATE_ID);\n  hostRef.$vnode$ = clientHydrate(vnode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, hostElm, hostId, slottedNodes);\n  let crIndex = 0;\n  const crLength = childRenderNodes.length;\n  let childRenderNode;\n  for (crIndex; crIndex < crLength; crIndex++) {\n    childRenderNode = childRenderNodes[crIndex];\n    const orgLocationId = childRenderNode.$hostId$ + \".\" + childRenderNode.$nodeId$;\n    const orgLocationNode = plt.$orgLocNodes$.get(orgLocationId);\n    const node = childRenderNode.$elm$;\n    if (!shadowRoot) {\n      node[\"s-hn\"] = tagName.toUpperCase();\n      if (childRenderNode.$tag$ === \"slot\") {\n        node[\"s-cr\"] = hostElm[\"s-cr\"];\n      }\n    }\n    if (childRenderNode.$tag$ === \"slot\") {\n      childRenderNode.$name$ = childRenderNode.$elm$[\"s-sn\"] || childRenderNode.$elm$[\"name\"] || null;\n      if (childRenderNode.$children$) {\n        childRenderNode.$flags$ |= 2 /* isSlotFallback */;\n        if (!childRenderNode.$elm$.childNodes.length) {\n          childRenderNode.$children$.forEach(c => {\n            childRenderNode.$elm$.appendChild(c.$elm$);\n          });\n        }\n      } else {\n        childRenderNode.$flags$ |= 1 /* isSlotReference */;\n      }\n    }\n    if (orgLocationNode && orgLocationNode.isConnected) {\n      if (shadowRoot && orgLocationNode[\"s-en\"] === \"\") {\n        orgLocationNode.parentNode.insertBefore(node, orgLocationNode.nextSibling);\n      }\n      orgLocationNode.parentNode.removeChild(orgLocationNode);\n      if (!shadowRoot) {\n        node[\"s-oo\"] = parseInt(childRenderNode.$nodeId$);\n      }\n    }\n    plt.$orgLocNodes$.delete(orgLocationId);\n  }\n  const hosts = [];\n  const snLen = slottedNodes.length;\n  let snIndex = 0;\n  let slotGroup;\n  let snGroupIdx;\n  let snGroupLen;\n  let slottedItem;\n  for (snIndex; snIndex < snLen; snIndex++) {\n    slotGroup = slottedNodes[snIndex];\n    if (!slotGroup || !slotGroup.length) continue;\n    snGroupLen = slotGroup.length;\n    snGroupIdx = 0;\n    for (snGroupIdx; snGroupIdx < snGroupLen; snGroupIdx++) {\n      slottedItem = slotGroup[snGroupIdx];\n      if (!hosts[slottedItem.hostId]) {\n        hosts[slottedItem.hostId] = plt.$orgLocNodes$.get(slottedItem.hostId);\n      }\n      if (!hosts[slottedItem.hostId]) continue;\n      const hostEle = hosts[slottedItem.hostId];\n      if (!hostEle.shadowRoot || !shadowRoot) {\n        slottedItem.slot[\"s-cr\"] = hostEle[\"s-cr\"];\n        if (!slottedItem.slot[\"s-cr\"] && hostEle.shadowRoot) {\n          slottedItem.slot[\"s-cr\"] = hostEle;\n        } else {\n          slottedItem.slot[\"s-cr\"] = (hostEle.__childNodes || hostEle.childNodes)[0];\n        }\n        addSlotRelocateNode(slottedItem.node, slottedItem.slot, false, slottedItem.node[\"s-oo\"]);\n        {\n          patchSlottedNode(slottedItem.node);\n        }\n      }\n      if (hostEle.shadowRoot && slottedItem.node.parentElement !== hostEle) {\n        hostEle.appendChild(slottedItem.node);\n      }\n    }\n  }\n  if (scopeId2 && slotNodes.length) {\n    slotNodes.forEach(slot => {\n      slot.$elm$.parentElement.classList.add(scopeId2 + \"-s\");\n    });\n  }\n  if (shadowRoot && !shadowRoot.childNodes.length) {\n    let rnIdex = 0;\n    const rnLen = shadowRootNodes.length;\n    if (rnLen) {\n      for (rnIdex; rnIdex < rnLen; rnIdex++) {\n        shadowRoot.appendChild(shadowRootNodes[rnIdex]);\n      }\n      Array.from(hostElm.childNodes).forEach(node => {\n        if (typeof node[\"s-sn\"] !== \"string\") {\n          if (node.nodeType === 1 /* ElementNode */ && node.slot && node.hidden) {\n            node.removeAttribute(\"hidden\");\n          } else if (node.nodeType === 8 /* CommentNode */ || node.nodeType === 3 /* TextNode */ && !node.wholeText.trim()) {\n            node.parentNode.removeChild(node);\n          }\n        }\n      });\n    }\n  }\n  plt.$orgLocNodes$.delete(hostElm[\"s-id\"]);\n  hostRef.$hostElement$ = hostElm;\n  endHydrate();\n};\nvar clientHydrate = (parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node, hostId, slottedNodes = []) => {\n  let childNodeType;\n  let childIdSplt;\n  let childVNode;\n  let i2;\n  const scopeId2 = hostElm[\"s-sc\"];\n  if (node.nodeType === 1 /* ElementNode */) {\n    childNodeType = node.getAttribute(HYDRATE_CHILD_ID);\n    if (childNodeType) {\n      childIdSplt = childNodeType.split(\".\");\n      if (childIdSplt[0] === hostId || childIdSplt[0] === \"0\") {\n        childVNode = createSimpleVNode({\n          $flags$: 0,\n          $hostId$: childIdSplt[0],\n          $nodeId$: childIdSplt[1],\n          $depth$: childIdSplt[2],\n          $index$: childIdSplt[3],\n          $tag$: node.tagName.toLowerCase(),\n          $elm$: node,\n          // If we don't add the initial classes to the VNode, the first `vdom-render.ts` patch\n          // won't try to reconcile them. Classes set on the node will be blown away.\n          $attrs$: {\n            class: node.className || \"\"\n          }\n        });\n        childRenderNodes.push(childVNode);\n        node.removeAttribute(HYDRATE_CHILD_ID);\n        if (!parentVNode.$children$) {\n          parentVNode.$children$ = [];\n        }\n        if (scopeId2) {\n          node[\"s-si\"] = scopeId2;\n          childVNode.$attrs$.class += \" \" + scopeId2;\n        }\n        const slotName = childVNode.$elm$.getAttribute(\"s-sn\");\n        if (typeof slotName === \"string\") {\n          if (childVNode.$tag$ === \"slot-fb\") {\n            addSlot(slotName, childIdSplt[2], childVNode, node, parentVNode, childRenderNodes, slotNodes, shadowRootNodes, slottedNodes);\n            if (scopeId2) {\n              node.classList.add(scopeId2);\n            }\n          }\n          childVNode.$elm$[\"s-sn\"] = slotName;\n          childVNode.$elm$.removeAttribute(\"s-sn\");\n        }\n        if (childVNode.$index$ !== void 0) {\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n        }\n        parentVNode = childVNode;\n        if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n          shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n        }\n      }\n    }\n    if (node.shadowRoot) {\n      for (i2 = node.shadowRoot.childNodes.length - 1; i2 >= 0; i2--) {\n        clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node.shadowRoot.childNodes[i2], hostId, slottedNodes);\n      }\n    }\n    const nonShadowNodes = node.__childNodes || node.childNodes;\n    for (i2 = nonShadowNodes.length - 1; i2 >= 0; i2--) {\n      clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, nonShadowNodes[i2], hostId, slottedNodes);\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[1] === hostId || childIdSplt[1] === \"0\") {\n      childNodeType = childIdSplt[0];\n      childVNode = createSimpleVNode({\n        $hostId$: childIdSplt[1],\n        $nodeId$: childIdSplt[2],\n        $depth$: childIdSplt[3],\n        $index$: childIdSplt[4] || \"0\",\n        $elm$: node,\n        $attrs$: null,\n        $children$: null,\n        $key$: null,\n        $name$: null,\n        $tag$: null,\n        $text$: null\n      });\n      if (childNodeType === TEXT_NODE_ID) {\n        childVNode.$elm$ = findCorrespondingNode(node, 3 /* TextNode */);\n        if (childVNode.$elm$ && childVNode.$elm$.nodeType === 3 /* TextNode */) {\n          childVNode.$text$ = childVNode.$elm$.textContent;\n          childRenderNodes.push(childVNode);\n          node.remove();\n          if (hostId === childVNode.$hostId$) {\n            if (!parentVNode.$children$) {\n              parentVNode.$children$ = [];\n            }\n            parentVNode.$children$[childVNode.$index$] = childVNode;\n          }\n          if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n            shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n          }\n        }\n      } else if (childNodeType === COMMENT_NODE_ID) {\n        childVNode.$elm$ = findCorrespondingNode(node, 8 /* CommentNode */);\n        if (childVNode.$elm$ && childVNode.$elm$.nodeType === 8 /* CommentNode */) {\n          childRenderNodes.push(childVNode);\n          node.remove();\n        }\n      } else if (childVNode.$hostId$ === hostId) {\n        if (childNodeType === SLOT_NODE_ID) {\n          const slotName = node[\"s-sn\"] = childIdSplt[5] || \"\";\n          addSlot(slotName, childIdSplt[2], childVNode, node, parentVNode, childRenderNodes, slotNodes, shadowRootNodes, slottedNodes);\n        } else if (childNodeType === CONTENT_REF_ID) {\n          if (shadowRootNodes) {\n            node.remove();\n          } else {\n            hostElm[\"s-cr\"] = node;\n            node[\"s-cn\"] = true;\n          }\n        }\n      }\n    }\n  } else if (parentVNode && parentVNode.$tag$ === \"style\") {\n    const vnode = newVNode(null, node.textContent);\n    vnode.$elm$ = node;\n    vnode.$index$ = \"0\";\n    parentVNode.$children$ = [vnode];\n  } else {\n    if (node.nodeType === 3 /* TextNode */ && !node.wholeText.trim()) {\n      node.remove();\n    }\n  }\n  return parentVNode;\n};\nvar initializeDocumentHydrate = (node, orgLocNodes) => {\n  if (node.nodeType === 1 /* ElementNode */) {\n    const componentId = node[HYDRATE_ID] || node.getAttribute(HYDRATE_ID);\n    if (componentId) {\n      orgLocNodes.set(componentId, node);\n    }\n    let i2 = 0;\n    if (node.shadowRoot) {\n      for (; i2 < node.shadowRoot.childNodes.length; i2++) {\n        initializeDocumentHydrate(node.shadowRoot.childNodes[i2], orgLocNodes);\n      }\n    }\n    const nonShadowNodes = node.__childNodes || node.childNodes;\n    for (i2 = 0; i2 < nonShadowNodes.length; i2++) {\n      initializeDocumentHydrate(nonShadowNodes[i2], orgLocNodes);\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    const childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[0] === ORG_LOCATION_ID) {\n      orgLocNodes.set(childIdSplt[1] + \".\" + childIdSplt[2], node);\n      node.nodeValue = \"\";\n      node[\"s-en\"] = childIdSplt[3];\n    }\n  }\n};\nvar createSimpleVNode = vnode => {\n  const defaultVNode = {\n    $flags$: 0,\n    $hostId$: null,\n    $nodeId$: null,\n    $depth$: null,\n    $index$: \"0\",\n    $elm$: null,\n    $attrs$: null,\n    $children$: null,\n    $key$: null,\n    $name$: null,\n    $tag$: null,\n    $text$: null\n  };\n  return {\n    ...defaultVNode,\n    ...vnode\n  };\n};\nfunction addSlot(slotName, slotId, childVNode, node, parentVNode, childRenderNodes, slotNodes, shadowRootNodes, slottedNodes) {\n  node[\"s-sr\"] = true;\n  childVNode.$name$ = slotName || null;\n  childVNode.$tag$ = \"slot\";\n  const parentNodeId = (parentVNode == null ? void 0 : parentVNode.$elm$) ? parentVNode.$elm$[\"s-id\"] || parentVNode.$elm$.getAttribute(\"s-id\") : \"\";\n  if (shadowRootNodes && win.document) {\n    const slot = childVNode.$elm$ = win.document.createElement(childVNode.$tag$);\n    if (childVNode.$name$) {\n      childVNode.$elm$.setAttribute(\"name\", slotName);\n    }\n    if (parentNodeId && parentNodeId !== childVNode.$hostId$) {\n      parentVNode.$elm$.insertBefore(slot, parentVNode.$elm$.children[0]);\n    } else {\n      node.parentNode.insertBefore(childVNode.$elm$, node);\n    }\n    addSlottedNodes(slottedNodes, slotId, slotName, node, childVNode.$hostId$);\n    node.remove();\n    if (childVNode.$depth$ === \"0\") {\n      shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n    }\n  } else {\n    const slot = childVNode.$elm$;\n    const shouldMove = parentNodeId && parentNodeId !== childVNode.$hostId$ && parentVNode.$elm$.shadowRoot;\n    addSlottedNodes(slottedNodes, slotId, slotName, node, shouldMove ? parentNodeId : childVNode.$hostId$);\n    patchSlotNode(node);\n    if (shouldMove) {\n      parentVNode.$elm$.insertBefore(slot, parentVNode.$elm$.children[0]);\n    }\n    childRenderNodes.push(childVNode);\n  }\n  slotNodes.push(childVNode);\n  if (!parentVNode.$children$) {\n    parentVNode.$children$ = [];\n  }\n  parentVNode.$children$[childVNode.$index$] = childVNode;\n}\nvar addSlottedNodes = (slottedNodes, slotNodeId, slotName, slotNode, hostId) => {\n  let slottedNode = slotNode.nextSibling;\n  slottedNodes[slotNodeId] = slottedNodes[slotNodeId] || [];\n  while (slottedNode && ((slottedNode[\"getAttribute\"] && slottedNode.getAttribute(\"slot\") || slottedNode[\"s-sn\"]) === slotName || slotName === \"\" && !slottedNode[\"s-sn\"] && (slottedNode.nodeType === 8 /* CommentNode */ && slottedNode.nodeValue.indexOf(\".\") !== 1 || slottedNode.nodeType === 3 /* TextNode */))) {\n    slottedNode[\"s-sn\"] = slotName;\n    slottedNodes[slotNodeId].push({\n      slot: slotNode,\n      node: slottedNode,\n      hostId\n    });\n    slottedNode = slottedNode.nextSibling;\n  }\n};\nvar findCorrespondingNode = (node, type) => {\n  let sibling = node;\n  do {\n    sibling = sibling.nextSibling;\n  } while (sibling && (sibling.nodeType !== type || !sibling.nodeValue));\n  return sibling;\n};\nvar createSupportsRuleRe = selector => {\n  const safeSelector2 = escapeRegExpSpecialCharacters(selector);\n  return new RegExp(\n  // First capture group: match any context before the selector that's not inside @supports selector()\n  // Using negative lookahead to avoid matching inside @supports selector(...) condition\n  `(^|[^@]|@(?!supports\\\\s+selector\\\\s*\\\\([^{]*?${safeSelector2}))(${safeSelector2}\\\\b)`, \"g\");\n};\ncreateSupportsRuleRe(\"::slotted\");\ncreateSupportsRuleRe(\":host\");\ncreateSupportsRuleRe(\":host-context\");\n\n// src/runtime/mode.ts\nvar computeMode = elm => modeResolutionChain.map(h2 => h2(elm)).find(m => !!m);\nvar setMode = handler => modeResolutionChain.push(handler);\nvar getMode = ref => getHostRef(ref).$modeName$;\nvar parsePropertyValue = (propValue, propType) => {\n  if (typeof propValue === \"string\" && (propValue.startsWith(\"{\") && propValue.endsWith(\"}\") || propValue.startsWith(\"[\") && propValue.endsWith(\"]\"))) {\n    try {\n      propValue = JSON.parse(propValue);\n      return propValue;\n    } catch (e) {}\n  }\n  if (typeof propValue === \"string\" && propValue.startsWith(SERIALIZED_PREFIX)) {\n    propValue = deserializeProperty(propValue);\n    return propValue;\n  }\n  if (propValue != null && !isComplexType(propValue)) {\n    if (propType & 4 /* Boolean */) {\n      return propValue === \"false\" ? false : propValue === \"\" || !!propValue;\n    }\n    if (propType & 2 /* Number */) {\n      return typeof propValue === \"string\" ? parseFloat(propValue) : typeof propValue === \"number\" ? propValue : NaN;\n    }\n    if (propType & 1 /* String */) {\n      return String(propValue);\n    }\n    return propValue;\n  }\n  return propValue;\n};\nvar getElement = ref => getHostRef(ref).$hostElement$;\n\n// src/runtime/event-emitter.ts\nvar createEvent = (ref, name, flags) => {\n  const elm = getElement(ref);\n  return {\n    emit: detail => {\n      return emitEvent(elm, name, {\n        bubbles: !!(flags & 4 /* Bubbles */),\n        composed: !!(flags & 2 /* Composed */),\n        cancelable: !!(flags & 1 /* Cancellable */),\n        detail\n      });\n    }\n  };\n};\nvar emitEvent = (elm, name, opts) => {\n  const ev = plt.ce(name, opts);\n  elm.dispatchEvent(ev);\n  return ev;\n};\nvar rootAppliedStyles = /* @__PURE__ */new WeakMap();\nvar registerStyle = (scopeId2, cssText, allowCS) => {\n  let style = styles.get(scopeId2);\n  if (supportsConstructableStylesheets && allowCS) {\n    style = style || new CSSStyleSheet();\n    if (typeof style === \"string\") {\n      style = cssText;\n    } else {\n      style.replaceSync(cssText);\n    }\n  } else {\n    style = cssText;\n  }\n  styles.set(scopeId2, style);\n};\nvar addStyle = (styleContainerNode, cmpMeta, mode) => {\n  var _a;\n  const scopeId2 = getScopeId(cmpMeta, mode);\n  const style = styles.get(scopeId2);\n  if (!win.document) {\n    return scopeId2;\n  }\n  styleContainerNode = styleContainerNode.nodeType === 11 /* DocumentFragment */ ? styleContainerNode : win.document;\n  if (style) {\n    if (typeof style === \"string\") {\n      styleContainerNode = styleContainerNode.head || styleContainerNode;\n      let appliedStyles = rootAppliedStyles.get(styleContainerNode);\n      let styleElm;\n      if (!appliedStyles) {\n        rootAppliedStyles.set(styleContainerNode, appliedStyles = /* @__PURE__ */new Set());\n      }\n      if (!appliedStyles.has(scopeId2)) {\n        if (styleContainerNode.host && (styleElm = styleContainerNode.querySelector(`[${HYDRATED_STYLE_ID}=\"${scopeId2}\"]`))) {\n          styleElm.innerHTML = style;\n        } else {\n          styleElm = document.querySelector(`[${HYDRATED_STYLE_ID}=\"${scopeId2}\"]`) || win.document.createElement(\"style\");\n          styleElm.innerHTML = style;\n          const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(win.document);\n          if (nonce != null) {\n            styleElm.setAttribute(\"nonce\", nonce);\n          }\n          if (!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */)) {\n            if (styleContainerNode.nodeName === \"HEAD\") {\n              const preconnectLinks = styleContainerNode.querySelectorAll(\"link[rel=preconnect]\");\n              const referenceNode2 = preconnectLinks.length > 0 ? preconnectLinks[preconnectLinks.length - 1].nextSibling : styleContainerNode.querySelector(\"style\");\n              styleContainerNode.insertBefore(styleElm, (referenceNode2 == null ? void 0 : referenceNode2.parentNode) === styleContainerNode ? referenceNode2 : null);\n            } else if (\"host\" in styleContainerNode) {\n              if (supportsConstructableStylesheets) {\n                const stylesheet = new CSSStyleSheet();\n                stylesheet.replaceSync(style);\n                styleContainerNode.adoptedStyleSheets = [stylesheet, ...styleContainerNode.adoptedStyleSheets];\n              } else {\n                const existingStyleContainer = styleContainerNode.querySelector(\"style\");\n                if (existingStyleContainer) {\n                  existingStyleContainer.innerHTML = style + existingStyleContainer.innerHTML;\n                } else {\n                  styleContainerNode.prepend(styleElm);\n                }\n              }\n            } else {\n              styleContainerNode.append(styleElm);\n            }\n          }\n          if (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            styleContainerNode.insertBefore(styleElm, null);\n          }\n        }\n        if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n          styleElm.innerHTML += SLOT_FB_CSS;\n        }\n        if (appliedStyles) {\n          appliedStyles.add(scopeId2);\n        }\n      }\n    } else if (!styleContainerNode.adoptedStyleSheets.includes(style)) {\n      styleContainerNode.adoptedStyleSheets = [...styleContainerNode.adoptedStyleSheets, style];\n    }\n  }\n  return scopeId2;\n};\nvar attachStyles = hostRef => {\n  const cmpMeta = hostRef.$cmpMeta$;\n  const elm = hostRef.$hostElement$;\n  const flags = cmpMeta.$flags$;\n  const endAttachStyles = createTime(\"attachStyles\", cmpMeta.$tagName$);\n  const scopeId2 = addStyle(elm.shadowRoot ? elm.shadowRoot : elm.getRootNode(), cmpMeta, hostRef.$modeName$);\n  if (flags & 10 /* needsScopedEncapsulation */) {\n    elm[\"s-sc\"] = scopeId2;\n    elm.classList.add(scopeId2 + \"-h\");\n  }\n  endAttachStyles();\n};\nvar getScopeId = (cmp, mode) => \"sc-\" + (mode && cmp.$flags$ & 32 /* hasMode */ ? cmp.$tagName$ + \"-\" + mode : cmp.$tagName$);\nvar convertScopedToShadow = css => css.replace(/\\/\\*!@([^\\/]+)\\*\\/[^\\{]+\\{/g, \"$1{\");\nvar hydrateScopedToShadow = () => {\n  if (!win.document) {\n    return;\n  }\n  const styles2 = win.document.querySelectorAll(`[${HYDRATED_STYLE_ID}]`);\n  let i2 = 0;\n  for (; i2 < styles2.length; i2++) {\n    registerStyle(styles2[i2].getAttribute(HYDRATED_STYLE_ID), convertScopedToShadow(styles2[i2].innerHTML), true);\n  }\n};\nvar setAccessor = (elm, memberName, oldValue, newValue, isSvg, flags, initialRender) => {\n  if (oldValue === newValue) {\n    return;\n  }\n  let isProp = isMemberInElement(elm, memberName);\n  let ln = memberName.toLowerCase();\n  if (memberName === \"class\") {\n    const classList = elm.classList;\n    const oldClasses = parseClassList(oldValue);\n    let newClasses = parseClassList(newValue);\n    if (elm[\"s-si\"] && initialRender) {\n      newClasses.push(elm[\"s-si\"]);\n      oldClasses.forEach(c => {\n        if (c.startsWith(elm[\"s-si\"])) newClasses.push(c);\n      });\n      newClasses = [...new Set(newClasses)];\n      classList.add(...newClasses);\n    } else {\n      classList.remove(...oldClasses.filter(c => c && !newClasses.includes(c)));\n      classList.add(...newClasses.filter(c => c && !oldClasses.includes(c)));\n    }\n  } else if (memberName === \"style\") {\n    {\n      for (const prop in oldValue) {\n        if (!newValue || newValue[prop] == null) {\n          if (prop.includes(\"-\")) {\n            elm.style.removeProperty(prop);\n          } else {\n            elm.style[prop] = \"\";\n          }\n        }\n      }\n    }\n    for (const prop in newValue) {\n      if (!oldValue || newValue[prop] !== oldValue[prop]) {\n        if (prop.includes(\"-\")) {\n          elm.style.setProperty(prop, newValue[prop]);\n        } else {\n          elm.style[prop] = newValue[prop];\n        }\n      }\n    }\n  } else if (memberName === \"key\") ;else if (memberName === \"ref\") {\n    if (newValue) {\n      newValue(elm);\n    }\n  } else if (!isProp && memberName[0] === \"o\" && memberName[1] === \"n\") {\n    if (memberName[2] === \"-\") {\n      memberName = memberName.slice(3);\n    } else if (isMemberInElement(win, ln)) {\n      memberName = ln.slice(2);\n    } else {\n      memberName = ln[2] + memberName.slice(3);\n    }\n    if (oldValue || newValue) {\n      const capture = memberName.endsWith(CAPTURE_EVENT_SUFFIX);\n      memberName = memberName.replace(CAPTURE_EVENT_REGEX, \"\");\n      if (oldValue) {\n        plt.rel(elm, memberName, oldValue, capture);\n      }\n      if (newValue) {\n        plt.ael(elm, memberName, newValue, capture);\n      }\n    }\n  } else {\n    const isComplex = isComplexType(newValue);\n    if ((isProp || isComplex && newValue !== null) && !isSvg) {\n      try {\n        if (!elm.tagName.includes(\"-\")) {\n          const n = newValue == null ? \"\" : newValue;\n          if (memberName === \"list\") {\n            isProp = false;\n          } else if (oldValue == null || elm[memberName] != n) {\n            if (typeof elm.__lookupSetter__(memberName) === \"function\") {\n              elm[memberName] = n;\n            } else {\n              elm.setAttribute(memberName, n);\n            }\n          }\n        } else if (elm[memberName] !== newValue) {\n          elm[memberName] = newValue;\n        }\n      } catch (e) {}\n    }\n    let xlink = false;\n    {\n      if (ln !== (ln = ln.replace(/^xlink\\:?/, \"\"))) {\n        memberName = ln;\n        xlink = true;\n      }\n    }\n    if (newValue == null || newValue === false) {\n      if (newValue !== false || elm.getAttribute(memberName) === \"\") {\n        if (xlink) {\n          elm.removeAttributeNS(XLINK_NS, memberName);\n        } else {\n          elm.removeAttribute(memberName);\n        }\n      }\n    } else if ((!isProp || flags & 4 /* isHost */ || isSvg) && !isComplex && elm.nodeType === 1 /* ElementNode */) {\n      newValue = newValue === true ? \"\" : newValue;\n      if (xlink) {\n        elm.setAttributeNS(XLINK_NS, memberName, newValue);\n      } else {\n        elm.setAttribute(memberName, newValue);\n      }\n    }\n  }\n};\nvar parseClassListRegex = /\\s/;\nvar parseClassList = value => {\n  if (typeof value === \"object\" && value && \"baseVal\" in value) {\n    value = value.baseVal;\n  }\n  if (!value || typeof value !== \"string\") {\n    return [];\n  }\n  return value.split(parseClassListRegex);\n};\nvar CAPTURE_EVENT_SUFFIX = \"Capture\";\nvar CAPTURE_EVENT_REGEX = new RegExp(CAPTURE_EVENT_SUFFIX + \"$\");\n\n// src/runtime/vdom/update-element.ts\nvar updateElement = (oldVnode, newVnode, isSvgMode2, isInitialRender) => {\n  const elm = newVnode.$elm$.nodeType === 11 /* DocumentFragment */ && newVnode.$elm$.host ? newVnode.$elm$.host : newVnode.$elm$;\n  const oldVnodeAttrs = oldVnode && oldVnode.$attrs$ || {};\n  const newVnodeAttrs = newVnode.$attrs$ || {};\n  {\n    for (const memberName of sortedAttrNames(Object.keys(oldVnodeAttrs))) {\n      if (!(memberName in newVnodeAttrs)) {\n        setAccessor(elm, memberName, oldVnodeAttrs[memberName], void 0, isSvgMode2, newVnode.$flags$, isInitialRender);\n      }\n    }\n  }\n  for (const memberName of sortedAttrNames(Object.keys(newVnodeAttrs))) {\n    setAccessor(elm, memberName, oldVnodeAttrs[memberName], newVnodeAttrs[memberName], isSvgMode2, newVnode.$flags$, isInitialRender);\n  }\n};\nfunction sortedAttrNames(attrNames) {\n  return attrNames.includes(\"ref\") ?\n  // we need to sort these to ensure that `'ref'` is the last attr\n  [...attrNames.filter(attr => attr !== \"ref\"), \"ref\"] :\n  // no need to sort, return the original array\n  attrNames;\n}\n\n// src/runtime/vdom/vdom-render.ts\nvar scopeId;\nvar contentRef;\nvar hostTagName;\nvar useNativeShadowDom = false;\nvar checkSlotFallbackVisibility = false;\nvar checkSlotRelocate = false;\nvar isSvgMode = false;\nvar createElm = (oldParentVNode, newParentVNode, childIndex) => {\n  var _a;\n  const newVNode2 = newParentVNode.$children$[childIndex];\n  let i2 = 0;\n  let elm;\n  let childNode;\n  let oldVNode;\n  if (!useNativeShadowDom) {\n    checkSlotRelocate = true;\n    if (newVNode2.$tag$ === \"slot\") {\n      newVNode2.$flags$ |= newVNode2.$children$ ?\n      // slot element has fallback content\n      // still create an element that \"mocks\" the slot element\n      2 /* isSlotFallback */ :\n      // slot element does not have fallback content\n      // create an html comment we'll use to always reference\n      // where actual slot content should sit next to\n      1 /* isSlotReference */;\n    }\n  }\n  if (newVNode2.$text$ !== null) {\n    elm = newVNode2.$elm$ = win.document.createTextNode(newVNode2.$text$);\n  } else if (newVNode2.$flags$ & 1 /* isSlotReference */) {\n    elm = newVNode2.$elm$ = win.document.createTextNode(\"\");\n    {\n      updateElement(null, newVNode2, isSvgMode);\n    }\n  } else {\n    if (!isSvgMode) {\n      isSvgMode = newVNode2.$tag$ === \"svg\";\n    }\n    if (!win.document) {\n      throw new Error(\"You are trying to render a Stencil component in an environment that doesn't support the DOM. Make sure to populate the [`window`](https://developer.mozilla.org/en-US/docs/Web/API/Window/window) object before rendering a component.\");\n    }\n    elm = newVNode2.$elm$ = win.document.createElementNS(isSvgMode ? SVG_NS : HTML_NS, !useNativeShadowDom && BUILD.slotRelocation && newVNode2.$flags$ & 2 /* isSlotFallback */ ? \"slot-fb\" : newVNode2.$tag$);\n    if (isSvgMode && newVNode2.$tag$ === \"foreignObject\") {\n      isSvgMode = false;\n    }\n    {\n      updateElement(null, newVNode2, isSvgMode);\n    }\n    if (isDef(scopeId) && elm[\"s-si\"] !== scopeId) {\n      elm.classList.add(elm[\"s-si\"] = scopeId);\n    }\n    if (newVNode2.$children$) {\n      for (i2 = 0; i2 < newVNode2.$children$.length; ++i2) {\n        childNode = createElm(oldParentVNode, newVNode2, i2);\n        if (childNode) {\n          elm.appendChild(childNode);\n        }\n      }\n    }\n    {\n      if (newVNode2.$tag$ === \"svg\") {\n        isSvgMode = false;\n      } else if (elm.tagName === \"foreignObject\") {\n        isSvgMode = true;\n      }\n    }\n  }\n  elm[\"s-hn\"] = hostTagName;\n  {\n    if (newVNode2.$flags$ & (2 /* isSlotFallback */ | 1 /* isSlotReference */)) {\n      elm[\"s-sr\"] = true;\n      elm[\"s-cr\"] = contentRef;\n      elm[\"s-sn\"] = newVNode2.$name$ || \"\";\n      elm[\"s-rf\"] = (_a = newVNode2.$attrs$) == null ? void 0 : _a.ref;\n      patchSlotNode(elm);\n      oldVNode = oldParentVNode && oldParentVNode.$children$ && oldParentVNode.$children$[childIndex];\n      if (oldVNode && oldVNode.$tag$ === newVNode2.$tag$ && oldParentVNode.$elm$) {\n        {\n          relocateToHostRoot(oldParentVNode.$elm$);\n        }\n      }\n      {\n        addRemoveSlotScopedClass(contentRef, elm, newParentVNode.$elm$, oldParentVNode == null ? void 0 : oldParentVNode.$elm$);\n      }\n    }\n  }\n  return elm;\n};\nvar relocateToHostRoot = parentElm => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const host = parentElm.closest(hostTagName.toLowerCase());\n  if (host != null) {\n    const contentRefNode = Array.from(host.__childNodes || host.childNodes).find(ref => ref[\"s-cr\"]);\n    const childNodeArray = Array.from(parentElm.__childNodes || parentElm.childNodes);\n    for (const childNode of contentRefNode ? childNodeArray.reverse() : childNodeArray) {\n      if (childNode[\"s-sh\"] != null) {\n        insertBefore(host, childNode, contentRefNode != null ? contentRefNode : null);\n        childNode[\"s-sh\"] = void 0;\n        checkSlotRelocate = true;\n      }\n    }\n  }\n  plt.$flags$ &= -2 /* isTmpDisconnected */;\n};\nvar putBackInOriginalLocation = (parentElm, recursive) => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const oldSlotChildNodes = Array.from(parentElm.__childNodes || parentElm.childNodes);\n  if (parentElm[\"s-sr\"] && BUILD.experimentalSlotFixes) {\n    let node = parentElm;\n    while (node = node.nextSibling) {\n      if (node && node[\"s-sn\"] === parentElm[\"s-sn\"] && node[\"s-sh\"] === hostTagName) {\n        oldSlotChildNodes.push(node);\n      }\n    }\n  }\n  for (let i2 = oldSlotChildNodes.length - 1; i2 >= 0; i2--) {\n    const childNode = oldSlotChildNodes[i2];\n    if (childNode[\"s-hn\"] !== hostTagName && childNode[\"s-ol\"]) {\n      insertBefore(referenceNode(childNode).parentNode, childNode, referenceNode(childNode));\n      childNode[\"s-ol\"].remove();\n      childNode[\"s-ol\"] = void 0;\n      childNode[\"s-sh\"] = void 0;\n      checkSlotRelocate = true;\n    }\n    if (recursive) {\n      putBackInOriginalLocation(childNode, recursive);\n    }\n  }\n  plt.$flags$ &= -2 /* isTmpDisconnected */;\n};\nvar addVnodes = (parentElm, before, parentVNode, vnodes, startIdx, endIdx) => {\n  let containerElm = parentElm[\"s-cr\"] && parentElm[\"s-cr\"].parentNode || parentElm;\n  let childNode;\n  if (containerElm.shadowRoot && containerElm.tagName === hostTagName) {\n    containerElm = containerElm.shadowRoot;\n  }\n  for (; startIdx <= endIdx; ++startIdx) {\n    if (vnodes[startIdx]) {\n      childNode = createElm(null, parentVNode, startIdx);\n      if (childNode) {\n        vnodes[startIdx].$elm$ = childNode;\n        insertBefore(containerElm, childNode, referenceNode(before));\n      }\n    }\n  }\n};\nvar removeVnodes = (vnodes, startIdx, endIdx) => {\n  for (let index = startIdx; index <= endIdx; ++index) {\n    const vnode = vnodes[index];\n    if (vnode) {\n      const elm = vnode.$elm$;\n      nullifyVNodeRefs(vnode);\n      if (elm) {\n        {\n          checkSlotFallbackVisibility = true;\n          if (elm[\"s-ol\"]) {\n            elm[\"s-ol\"].remove();\n          } else {\n            putBackInOriginalLocation(elm, true);\n          }\n        }\n        elm.remove();\n      }\n    }\n  }\n};\nvar updateChildren = (parentElm, oldCh, newVNode2, newCh, isInitialRender = false) => {\n  let oldStartIdx = 0;\n  let newStartIdx = 0;\n  let idxInOld = 0;\n  let i2 = 0;\n  let oldEndIdx = oldCh.length - 1;\n  let oldStartVnode = oldCh[0];\n  let oldEndVnode = oldCh[oldEndIdx];\n  let newEndIdx = newCh.length - 1;\n  let newStartVnode = newCh[0];\n  let newEndVnode = newCh[newEndIdx];\n  let node;\n  let elmToMove;\n  while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n    if (oldStartVnode == null) {\n      oldStartVnode = oldCh[++oldStartIdx];\n    } else if (oldEndVnode == null) {\n      oldEndVnode = oldCh[--oldEndIdx];\n    } else if (newStartVnode == null) {\n      newStartVnode = newCh[++newStartIdx];\n    } else if (newEndVnode == null) {\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newStartVnode, isInitialRender)) {\n      patch(oldStartVnode, newStartVnode, isInitialRender);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else if (isSameVnode(oldEndVnode, newEndVnode, isInitialRender)) {\n      patch(oldEndVnode, newEndVnode, isInitialRender);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newEndVnode, isInitialRender)) {\n      if (oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\") {\n        putBackInOriginalLocation(oldStartVnode.$elm$.parentNode, false);\n      }\n      patch(oldStartVnode, newEndVnode, isInitialRender);\n      insertBefore(parentElm, oldStartVnode.$elm$, oldEndVnode.$elm$.nextSibling);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldEndVnode, newStartVnode, isInitialRender)) {\n      if (oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\") {\n        putBackInOriginalLocation(oldEndVnode.$elm$.parentNode, false);\n      }\n      patch(oldEndVnode, newStartVnode, isInitialRender);\n      insertBefore(parentElm, oldEndVnode.$elm$, oldStartVnode.$elm$);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else {\n      idxInOld = -1;\n      {\n        for (i2 = oldStartIdx; i2 <= oldEndIdx; ++i2) {\n          if (oldCh[i2] && oldCh[i2].$key$ !== null && oldCh[i2].$key$ === newStartVnode.$key$) {\n            idxInOld = i2;\n            break;\n          }\n        }\n      }\n      if (idxInOld >= 0) {\n        elmToMove = oldCh[idxInOld];\n        if (elmToMove.$tag$ !== newStartVnode.$tag$) {\n          node = createElm(oldCh && oldCh[newStartIdx], newVNode2, idxInOld);\n        } else {\n          patch(elmToMove, newStartVnode, isInitialRender);\n          oldCh[idxInOld] = void 0;\n          node = elmToMove.$elm$;\n        }\n        newStartVnode = newCh[++newStartIdx];\n      } else {\n        node = createElm(oldCh && oldCh[newStartIdx], newVNode2, newStartIdx);\n        newStartVnode = newCh[++newStartIdx];\n      }\n      if (node) {\n        {\n          insertBefore(referenceNode(oldStartVnode.$elm$).parentNode, node, referenceNode(oldStartVnode.$elm$));\n        }\n      }\n    }\n  }\n  if (oldStartIdx > oldEndIdx) {\n    addVnodes(parentElm, newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].$elm$, newVNode2, newCh, newStartIdx, newEndIdx);\n  } else if (newStartIdx > newEndIdx) {\n    removeVnodes(oldCh, oldStartIdx, oldEndIdx);\n  }\n};\nvar isSameVnode = (leftVNode, rightVNode, isInitialRender = false) => {\n  if (leftVNode.$tag$ === rightVNode.$tag$) {\n    if (leftVNode.$tag$ === \"slot\") {\n      return leftVNode.$name$ === rightVNode.$name$;\n    }\n    if (!isInitialRender) {\n      return leftVNode.$key$ === rightVNode.$key$;\n    }\n    if (isInitialRender && !leftVNode.$key$ && rightVNode.$key$) {\n      leftVNode.$key$ = rightVNode.$key$;\n    }\n    return true;\n  }\n  return false;\n};\nvar referenceNode = node => node && node[\"s-ol\"] || node;\nvar patch = (oldVNode, newVNode2, isInitialRender = false) => {\n  const elm = newVNode2.$elm$ = oldVNode.$elm$;\n  const oldChildren = oldVNode.$children$;\n  const newChildren = newVNode2.$children$;\n  const tag = newVNode2.$tag$;\n  const text = newVNode2.$text$;\n  let defaultHolder;\n  if (text === null) {\n    {\n      isSvgMode = tag === \"svg\" ? true : tag === \"foreignObject\" ? false : isSvgMode;\n    }\n    {\n      if (tag === \"slot\" && !useNativeShadowDom) {\n        if (oldVNode.$name$ !== newVNode2.$name$) {\n          newVNode2.$elm$[\"s-sn\"] = newVNode2.$name$ || \"\";\n          relocateToHostRoot(newVNode2.$elm$.parentElement);\n        }\n      }\n      updateElement(oldVNode, newVNode2, isSvgMode, isInitialRender);\n    }\n    if (oldChildren !== null && newChildren !== null) {\n      updateChildren(elm, oldChildren, newVNode2, newChildren, isInitialRender);\n    } else if (newChildren !== null) {\n      if (oldVNode.$text$ !== null) {\n        elm.textContent = \"\";\n      }\n      addVnodes(elm, null, newVNode2, newChildren, 0, newChildren.length - 1);\n    } else if (\n    // don't do this on initial render as it can cause non-hydrated content to be removed\n    !isInitialRender && BUILD.updatable && oldChildren !== null) {\n      removeVnodes(oldChildren, 0, oldChildren.length - 1);\n    }\n    if (isSvgMode && tag === \"svg\") {\n      isSvgMode = false;\n    }\n  } else if (defaultHolder = elm[\"s-cr\"]) {\n    defaultHolder.parentNode.textContent = text;\n  } else if (oldVNode.$text$ !== text) {\n    elm.data = text;\n  }\n};\nvar relocateNodes = [];\nvar markSlotContentForRelocation = elm => {\n  let node;\n  let hostContentNodes;\n  let j;\n  const children = elm.__childNodes || elm.childNodes;\n  for (const childNode of children) {\n    if (childNode[\"s-sr\"] && (node = childNode[\"s-cr\"]) && node.parentNode) {\n      hostContentNodes = node.parentNode.__childNodes || node.parentNode.childNodes;\n      const slotName = childNode[\"s-sn\"];\n      for (j = hostContentNodes.length - 1; j >= 0; j--) {\n        node = hostContentNodes[j];\n        if (!node[\"s-cn\"] && !node[\"s-nr\"] && node[\"s-hn\"] !== childNode[\"s-hn\"] && (!node[\"s-sh\"] || node[\"s-sh\"] !== childNode[\"s-hn\"])) {\n          if (isNodeLocatedInSlot(node, slotName)) {\n            let relocateNodeData = relocateNodes.find(r => r.$nodeToRelocate$ === node);\n            checkSlotFallbackVisibility = true;\n            node[\"s-sn\"] = node[\"s-sn\"] || slotName;\n            if (relocateNodeData) {\n              relocateNodeData.$nodeToRelocate$[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodeData.$slotRefNode$ = childNode;\n            } else {\n              node[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodes.push({\n                $slotRefNode$: childNode,\n                $nodeToRelocate$: node\n              });\n            }\n            if (node[\"s-sr\"]) {\n              relocateNodes.map(relocateNode => {\n                if (isNodeLocatedInSlot(relocateNode.$nodeToRelocate$, node[\"s-sn\"])) {\n                  relocateNodeData = relocateNodes.find(r => r.$nodeToRelocate$ === node);\n                  if (relocateNodeData && !relocateNode.$slotRefNode$) {\n                    relocateNode.$slotRefNode$ = relocateNodeData.$slotRefNode$;\n                  }\n                }\n              });\n            }\n          } else if (!relocateNodes.some(r => r.$nodeToRelocate$ === node)) {\n            relocateNodes.push({\n              $nodeToRelocate$: node\n            });\n          }\n        }\n      }\n    }\n    if (childNode.nodeType === 1 /* ElementNode */) {\n      markSlotContentForRelocation(childNode);\n    }\n  }\n};\nvar nullifyVNodeRefs = vNode => {\n  {\n    vNode.$attrs$ && vNode.$attrs$.ref && vNode.$attrs$.ref(null);\n    vNode.$children$ && vNode.$children$.map(nullifyVNodeRefs);\n  }\n};\nvar insertBefore = (parent, newNode, reference) => {\n  if (typeof newNode[\"s-sn\"] === \"string\" && !!newNode[\"s-sr\"] && !!newNode[\"s-cr\"]) {\n    addRemoveSlotScopedClass(newNode[\"s-cr\"], newNode, parent, newNode.parentElement);\n  } else if (typeof newNode[\"s-sn\"] === \"string\") {\n    if (parent.getRootNode().nodeType !== 11 /* DOCUMENT_FRAGMENT_NODE */) {\n      patchParentNode(newNode);\n    }\n    parent.insertBefore(newNode, reference);\n    const {\n      slotNode\n    } = findSlotFromSlottedNode(newNode);\n    if (slotNode) dispatchSlotChangeEvent(slotNode);\n    return newNode;\n  }\n  if (parent.__insertBefore) {\n    return parent.__insertBefore(newNode, reference);\n  } else {\n    return parent == null ? void 0 : parent.insertBefore(newNode, reference);\n  }\n};\nfunction addRemoveSlotScopedClass(reference, slotNode, newParent, oldParent) {\n  var _a, _b;\n  let scopeId2;\n  if (reference && typeof slotNode[\"s-sn\"] === \"string\" && !!slotNode[\"s-sr\"] && reference.parentNode && reference.parentNode[\"s-sc\"] && (scopeId2 = slotNode[\"s-si\"] || reference.parentNode[\"s-sc\"])) {\n    const scopeName = slotNode[\"s-sn\"];\n    const hostName = slotNode[\"s-hn\"];\n    (_a = newParent.classList) == null ? void 0 : _a.add(scopeId2 + \"-s\");\n    if (oldParent && ((_b = oldParent.classList) == null ? void 0 : _b.contains(scopeId2 + \"-s\"))) {\n      let child = (oldParent.__childNodes || oldParent.childNodes)[0];\n      let found = false;\n      while (child) {\n        if (child[\"s-sn\"] !== scopeName && child[\"s-hn\"] === hostName && !!child[\"s-sr\"]) {\n          found = true;\n          break;\n        }\n        child = child.nextSibling;\n      }\n      if (!found) oldParent.classList.remove(scopeId2 + \"-s\");\n    }\n  }\n}\nvar renderVdom = (hostRef, renderFnResults, isInitialLoad = false) => {\n  var _a, _b, _c, _d, _e;\n  const hostElm = hostRef.$hostElement$;\n  const cmpMeta = hostRef.$cmpMeta$;\n  const oldVNode = hostRef.$vnode$ || newVNode(null, null);\n  const isHostElement = isHost(renderFnResults);\n  const rootVnode = isHostElement ? renderFnResults : h(null, null, renderFnResults);\n  hostTagName = hostElm.tagName;\n  if (cmpMeta.$attrsToReflect$) {\n    rootVnode.$attrs$ = rootVnode.$attrs$ || {};\n    cmpMeta.$attrsToReflect$.map(([propName, attribute]) => rootVnode.$attrs$[attribute] = hostElm[propName]);\n  }\n  if (isInitialLoad && rootVnode.$attrs$) {\n    for (const key of Object.keys(rootVnode.$attrs$)) {\n      if (hostElm.hasAttribute(key) && ![\"key\", \"ref\", \"style\", \"class\"].includes(key)) {\n        rootVnode.$attrs$[key] = hostElm[key];\n      }\n    }\n  }\n  rootVnode.$tag$ = null;\n  rootVnode.$flags$ |= 4 /* isHost */;\n  hostRef.$vnode$ = rootVnode;\n  rootVnode.$elm$ = oldVNode.$elm$ = hostElm.shadowRoot || hostElm;\n  {\n    scopeId = hostElm[\"s-sc\"];\n  }\n  useNativeShadowDom = !!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) && !(cmpMeta.$flags$ & 128 /* shadowNeedsScopedCss */);\n  {\n    contentRef = hostElm[\"s-cr\"];\n    checkSlotFallbackVisibility = false;\n  }\n  patch(oldVNode, rootVnode, isInitialLoad);\n  {\n    plt.$flags$ |= 1 /* isTmpDisconnected */;\n    if (checkSlotRelocate) {\n      markSlotContentForRelocation(rootVnode.$elm$);\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        if (!nodeToRelocate[\"s-ol\"] && win.document) {\n          const orgLocationNode = win.document.createTextNode(\"\");\n          orgLocationNode[\"s-nr\"] = nodeToRelocate;\n          insertBefore(nodeToRelocate.parentNode, nodeToRelocate[\"s-ol\"] = orgLocationNode, nodeToRelocate);\n        }\n      }\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        const slotRefNode = relocateData.$slotRefNode$;\n        if (slotRefNode) {\n          const parentNodeRef = slotRefNode.parentNode;\n          let insertBeforeNode = slotRefNode.nextSibling;\n          if (insertBeforeNode && insertBeforeNode.nodeType === 1 /* ElementNode */) {\n            let orgLocationNode = (_a = nodeToRelocate[\"s-ol\"]) == null ? void 0 : _a.previousSibling;\n            while (orgLocationNode) {\n              let refNode = (_b = orgLocationNode[\"s-nr\"]) != null ? _b : null;\n              if (refNode && refNode[\"s-sn\"] === nodeToRelocate[\"s-sn\"] && parentNodeRef === (refNode.__parentNode || refNode.parentNode)) {\n                refNode = refNode.nextSibling;\n                while (refNode === nodeToRelocate || (refNode == null ? void 0 : refNode[\"s-sr\"])) {\n                  refNode = refNode == null ? void 0 : refNode.nextSibling;\n                }\n                if (!refNode || !refNode[\"s-nr\"]) {\n                  insertBeforeNode = refNode;\n                  break;\n                }\n              }\n              orgLocationNode = orgLocationNode.previousSibling;\n            }\n          }\n          const parent = nodeToRelocate.__parentNode || nodeToRelocate.parentNode;\n          const nextSibling = nodeToRelocate.__nextSibling || nodeToRelocate.nextSibling;\n          if (!insertBeforeNode && parentNodeRef !== parent || nextSibling !== insertBeforeNode) {\n            if (nodeToRelocate !== insertBeforeNode) {\n              insertBefore(parentNodeRef, nodeToRelocate, insertBeforeNode);\n              if (nodeToRelocate.nodeType === 1 /* ElementNode */ && nodeToRelocate.tagName !== \"SLOT-FB\") {\n                nodeToRelocate.hidden = (_c = nodeToRelocate[\"s-ih\"]) != null ? _c : false;\n              }\n            }\n          }\n          nodeToRelocate && typeof slotRefNode[\"s-rf\"] === \"function\" && slotRefNode[\"s-rf\"](slotRefNode);\n        } else {\n          if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n            if (isInitialLoad) {\n              nodeToRelocate[\"s-ih\"] = (_d = nodeToRelocate.hidden) != null ? _d : false;\n            }\n            nodeToRelocate.hidden = true;\n          }\n        }\n      }\n    }\n    if (checkSlotFallbackVisibility) {\n      updateFallbackSlotVisibility(rootVnode.$elm$);\n    }\n    plt.$flags$ &= -2 /* isTmpDisconnected */;\n    relocateNodes.length = 0;\n  }\n  if (cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n    const children = rootVnode.$elm$.__childNodes || rootVnode.$elm$.childNodes;\n    for (const childNode of children) {\n      if (childNode[\"s-hn\"] !== hostTagName && !childNode[\"s-sh\"]) {\n        if (isInitialLoad && childNode[\"s-ih\"] == null) {\n          childNode[\"s-ih\"] = (_e = childNode.hidden) != null ? _e : false;\n        }\n        childNode.hidden = true;\n      }\n    }\n  }\n  contentRef = void 0;\n};\n\n// src/runtime/update-component.ts\nvar attachToAncestor = (hostRef, ancestorComponent) => {\n  if (ancestorComponent && !hostRef.$onRenderResolve$ && ancestorComponent[\"s-p\"]) {\n    const index = ancestorComponent[\"s-p\"].push(new Promise(r => hostRef.$onRenderResolve$ = () => {\n      ancestorComponent[\"s-p\"].splice(index - 1, 1);\n      r();\n    }));\n  }\n};\nvar scheduleUpdate = (hostRef, isInitialLoad) => {\n  {\n    hostRef.$flags$ |= 16 /* isQueuedForUpdate */;\n  }\n  if (hostRef.$flags$ & 4 /* isWaitingForChildren */) {\n    hostRef.$flags$ |= 512 /* needsRerender */;\n    return;\n  }\n  attachToAncestor(hostRef, hostRef.$ancestorComponent$);\n  const dispatch = () => dispatchHooks(hostRef, isInitialLoad);\n  return writeTask(dispatch);\n};\nvar dispatchHooks = (hostRef, isInitialLoad) => {\n  const elm = hostRef.$hostElement$;\n  const endSchedule = createTime(\"scheduleUpdate\", hostRef.$cmpMeta$.$tagName$);\n  const instance = hostRef.$lazyInstance$;\n  if (!instance) {\n    throw new Error(`Can't render component <${elm.tagName.toLowerCase()} /> with invalid Stencil runtime! Make sure this imported component is compiled with a \\`externalRuntime: true\\` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime`);\n  }\n  let maybePromise;\n  if (isInitialLoad) {\n    {\n      hostRef.$flags$ |= 256 /* isListenReady */;\n      if (hostRef.$queuedListeners$) {\n        hostRef.$queuedListeners$.map(([methodName, event]) => safeCall(instance, methodName, event, elm));\n        hostRef.$queuedListeners$ = void 0;\n      }\n    }\n    maybePromise = safeCall(instance, \"componentWillLoad\", void 0, elm);\n  } else {\n    maybePromise = safeCall(instance, \"componentWillUpdate\", void 0, elm);\n  }\n  maybePromise = enqueue(maybePromise, () => safeCall(instance, \"componentWillRender\", void 0, elm));\n  endSchedule();\n  return enqueue(maybePromise, () => updateComponent(hostRef, instance, isInitialLoad));\n};\nvar enqueue = (maybePromise, fn) => isPromisey(maybePromise) ? maybePromise.then(fn).catch(err2 => {\n  console.error(err2);\n  fn();\n}) : fn();\nvar isPromisey = maybePromise => maybePromise instanceof Promise || maybePromise && maybePromise.then && typeof maybePromise.then === \"function\";\nvar updateComponent = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (hostRef, instance, isInitialLoad) {\n    var _a;\n    const elm = hostRef.$hostElement$;\n    const endUpdate = createTime(\"update\", hostRef.$cmpMeta$.$tagName$);\n    const rc = elm[\"s-rc\"];\n    if (isInitialLoad) {\n      attachStyles(hostRef);\n    }\n    const endRender = createTime(\"render\", hostRef.$cmpMeta$.$tagName$);\n    {\n      callRender(hostRef, instance, elm, isInitialLoad);\n    }\n    if (rc) {\n      rc.map(cb => cb());\n      elm[\"s-rc\"] = void 0;\n    }\n    endRender();\n    endUpdate();\n    {\n      const childrenPromises = (_a = elm[\"s-p\"]) != null ? _a : [];\n      const postUpdate = () => postUpdateComponent(hostRef);\n      if (childrenPromises.length === 0) {\n        postUpdate();\n      } else {\n        Promise.all(childrenPromises).then(postUpdate);\n        hostRef.$flags$ |= 4 /* isWaitingForChildren */;\n        childrenPromises.length = 0;\n      }\n    }\n  });\n  return function updateComponent(_x, _x2, _x3) {\n    return _ref.apply(this, arguments);\n  };\n}();\nvar callRender = (hostRef, instance, elm, isInitialLoad) => {\n  try {\n    instance = instance.render && instance.render();\n    {\n      hostRef.$flags$ &= -17 /* isQueuedForUpdate */;\n    }\n    {\n      hostRef.$flags$ |= 2 /* hasRendered */;\n    }\n    {\n      {\n        {\n          renderVdom(hostRef, instance, isInitialLoad);\n        }\n      }\n    }\n  } catch (e) {\n    consoleError(e, hostRef.$hostElement$);\n  }\n  return null;\n};\nvar postUpdateComponent = hostRef => {\n  const tagName = hostRef.$cmpMeta$.$tagName$;\n  const elm = hostRef.$hostElement$;\n  const endPostUpdate = createTime(\"postUpdate\", tagName);\n  const instance = hostRef.$lazyInstance$;\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  safeCall(instance, \"componentDidRender\", void 0, elm);\n  if (!(hostRef.$flags$ & 64 /* hasLoadedComponent */)) {\n    hostRef.$flags$ |= 64 /* hasLoadedComponent */;\n    {\n      addHydratedFlag(elm);\n    }\n    safeCall(instance, \"componentDidLoad\", void 0, elm);\n    endPostUpdate();\n    {\n      hostRef.$onReadyResolve$(elm);\n      if (!ancestorComponent) {\n        appDidLoad();\n      }\n    }\n  } else {\n    safeCall(instance, \"componentDidUpdate\", void 0, elm);\n    endPostUpdate();\n  }\n  {\n    hostRef.$onInstanceResolve$(elm);\n  }\n  {\n    if (hostRef.$onRenderResolve$) {\n      hostRef.$onRenderResolve$();\n      hostRef.$onRenderResolve$ = void 0;\n    }\n    if (hostRef.$flags$ & 512 /* needsRerender */) {\n      nextTick(() => scheduleUpdate(hostRef, false));\n    }\n    hostRef.$flags$ &= -517;\n  }\n};\nvar forceUpdate = ref => {\n  {\n    const hostRef = getHostRef(ref);\n    const isConnected = hostRef.$hostElement$.isConnected;\n    if (isConnected && (hostRef.$flags$ & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n      scheduleUpdate(hostRef, false);\n    }\n    return isConnected;\n  }\n};\nvar appDidLoad = who => {\n  nextTick(() => emitEvent(win, \"appload\", {\n    detail: {\n      namespace: NAMESPACE\n    }\n  }));\n};\nvar safeCall = (instance, method, arg, elm) => {\n  if (instance && instance[method]) {\n    try {\n      return instance[method](arg);\n    } catch (e) {\n      consoleError(e, elm);\n    }\n  }\n  return void 0;\n};\nvar addHydratedFlag = elm => {\n  var _a;\n  return elm.classList.add((_a = BUILD.hydratedSelectorName) != null ? _a : \"hydrated\");\n};\n\n// src/runtime/set-value.ts\nvar getValue = (ref, propName) => getHostRef(ref).$instanceValues$.get(propName);\nvar setValue = (ref, propName, newVal, cmpMeta) => {\n  const hostRef = getHostRef(ref);\n  if (!hostRef) {\n    throw new Error(`Couldn't find host element for \"${cmpMeta.$tagName$}\" as it is unknown to this Stencil runtime. This usually happens when integrating a 3rd party Stencil component with another Stencil component or application. Please reach out to the maintainers of the 3rd party Stencil component or report this on the Stencil Discord server (https://chat.stenciljs.com) or comment on this similar [GitHub issue](https://github.com/stenciljs/core/issues/5457).`);\n  }\n  const elm = hostRef.$hostElement$;\n  const oldVal = hostRef.$instanceValues$.get(propName);\n  const flags = hostRef.$flags$;\n  const instance = hostRef.$lazyInstance$;\n  newVal = parsePropertyValue(newVal, cmpMeta.$members$[propName][0]);\n  const areBothNaN = Number.isNaN(oldVal) && Number.isNaN(newVal);\n  const didValueChange = newVal !== oldVal && !areBothNaN;\n  if ((!(flags & 8 /* isConstructingInstance */) || oldVal === void 0) && didValueChange) {\n    hostRef.$instanceValues$.set(propName, newVal);\n    if (instance) {\n      if (cmpMeta.$watchers$ && flags & 128 /* isWatchReady */) {\n        const watchMethods = cmpMeta.$watchers$[propName];\n        if (watchMethods) {\n          watchMethods.map(watchMethodName => {\n            try {\n              instance[watchMethodName](newVal, oldVal, propName);\n            } catch (e) {\n              consoleError(e, elm);\n            }\n          });\n        }\n      }\n      if ((flags & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n        if (instance.componentShouldUpdate) {\n          if (instance.componentShouldUpdate(newVal, oldVal, propName) === false) {\n            return;\n          }\n        }\n        scheduleUpdate(hostRef, false);\n      }\n    }\n  }\n};\n\n// src/runtime/proxy-component.ts\nvar proxyComponent = (Cstr, cmpMeta, flags) => {\n  var _a, _b;\n  const prototype = Cstr.prototype;\n  if (cmpMeta.$members$ || cmpMeta.$watchers$ || Cstr.watchers) {\n    if (Cstr.watchers && !cmpMeta.$watchers$) {\n      cmpMeta.$watchers$ = Cstr.watchers;\n    }\n    const members = Object.entries((_a = cmpMeta.$members$) != null ? _a : {});\n    members.map(([memberName, [memberFlags]]) => {\n      if (memberFlags & 31 /* Prop */ || flags & 2 /* proxyState */ && memberFlags & 32 /* State */) {\n        const {\n          get: origGetter,\n          set: origSetter\n        } = Object.getOwnPropertyDescriptor(prototype, memberName) || {};\n        if (origGetter) cmpMeta.$members$[memberName][0] |= 2048 /* Getter */;\n        if (origSetter) cmpMeta.$members$[memberName][0] |= 4096 /* Setter */;\n        if (flags & 1 /* isElementConstructor */ || !origGetter) {\n          Object.defineProperty(prototype, memberName, {\n            get() {\n              {\n                if ((cmpMeta.$members$[memberName][0] & 2048 /* Getter */) === 0) {\n                  return getValue(this, memberName);\n                }\n                const ref = getHostRef(this);\n                const instance = ref ? ref.$lazyInstance$ : prototype;\n                if (!instance) return;\n                return instance[memberName];\n              }\n            },\n            configurable: true,\n            enumerable: true\n          });\n        }\n        Object.defineProperty(prototype, memberName, {\n          set(newValue) {\n            const ref = getHostRef(this);\n            if (origSetter) {\n              const currentValue = memberFlags & 32 /* State */ ? this[memberName] : ref.$hostElement$[memberName];\n              if (typeof currentValue === \"undefined\" && ref.$instanceValues$.get(memberName)) {\n                newValue = ref.$instanceValues$.get(memberName);\n              } else if (!ref.$instanceValues$.get(memberName) && currentValue) {\n                ref.$instanceValues$.set(memberName, currentValue);\n              }\n              origSetter.apply(this, [parsePropertyValue(newValue, memberFlags)]);\n              newValue = memberFlags & 32 /* State */ ? this[memberName] : ref.$hostElement$[memberName];\n              setValue(this, memberName, newValue, cmpMeta);\n              return;\n            }\n            {\n              if ((flags & 1 /* isElementConstructor */) === 0 || (cmpMeta.$members$[memberName][0] & 4096 /* Setter */) === 0) {\n                setValue(this, memberName, newValue, cmpMeta);\n                if (flags & 1 /* isElementConstructor */ && !ref.$lazyInstance$) {\n                  ref.$onReadyPromise$.then(() => {\n                    if (cmpMeta.$members$[memberName][0] & 4096 /* Setter */ && ref.$lazyInstance$[memberName] !== ref.$instanceValues$.get(memberName)) {\n                      ref.$lazyInstance$[memberName] = newValue;\n                    }\n                  });\n                }\n                return;\n              }\n              const setterSetVal = () => {\n                const currentValue = ref.$lazyInstance$[memberName];\n                if (!ref.$instanceValues$.get(memberName) && currentValue) {\n                  ref.$instanceValues$.set(memberName, currentValue);\n                }\n                ref.$lazyInstance$[memberName] = parsePropertyValue(newValue, memberFlags);\n                setValue(this, memberName, ref.$lazyInstance$[memberName], cmpMeta);\n              };\n              if (ref.$lazyInstance$) {\n                setterSetVal();\n              } else {\n                ref.$onReadyPromise$.then(() => setterSetVal());\n              }\n            }\n          }\n        });\n      } else if (flags & 1 /* isElementConstructor */ && memberFlags & 64 /* Method */) {\n        Object.defineProperty(prototype, memberName, {\n          value(...args) {\n            var _a2;\n            const ref = getHostRef(this);\n            return (_a2 = ref == null ? void 0 : ref.$onInstancePromise$) == null ? void 0 : _a2.then(() => {\n              var _a3;\n              return (_a3 = ref.$lazyInstance$) == null ? void 0 : _a3[memberName](...args);\n            });\n          }\n        });\n      }\n    });\n    if (flags & 1 /* isElementConstructor */) {\n      const attrNameToPropName = /* @__PURE__ */new Map();\n      prototype.attributeChangedCallback = function (attrName, oldValue, newValue) {\n        plt.jmp(() => {\n          var _a2;\n          const propName = attrNameToPropName.get(attrName);\n          if (this.hasOwnProperty(propName) && BUILD.lazyLoad) {\n            newValue = this[propName];\n            delete this[propName];\n          } else if (prototype.hasOwnProperty(propName) && typeof this[propName] === \"number\" &&\n          // cast type to number to avoid TS compiler issues\n          this[propName] == newValue) {\n            return;\n          } else if (propName == null) {\n            const hostRef = getHostRef(this);\n            const flags2 = hostRef == null ? void 0 : hostRef.$flags$;\n            if (flags2 && !(flags2 & 8 /* isConstructingInstance */) && flags2 & 128 /* isWatchReady */ && newValue !== oldValue) {\n              const instance = hostRef.$lazyInstance$;\n              const entry = (_a2 = cmpMeta.$watchers$) == null ? void 0 : _a2[attrName];\n              entry == null ? void 0 : entry.forEach(callbackName => {\n                if (instance[callbackName] != null) {\n                  instance[callbackName].call(instance, newValue, oldValue, attrName);\n                }\n              });\n            }\n            return;\n          }\n          const propDesc = Object.getOwnPropertyDescriptor(prototype, propName);\n          newValue = newValue === null && typeof this[propName] === \"boolean\" ? false : newValue;\n          if (newValue !== this[propName] && (!propDesc.get || !!propDesc.set)) {\n            this[propName] = newValue;\n          }\n        });\n      };\n      Cstr.observedAttributes = Array.from(/* @__PURE__ */new Set([...Object.keys((_b = cmpMeta.$watchers$) != null ? _b : {}), ...members.filter(([_, m]) => m[0] & 15 /* HasAttribute */).map(([propName, m]) => {\n        var _a2;\n        const attrName = m[1] || propName;\n        attrNameToPropName.set(attrName, propName);\n        if (m[0] & 512 /* ReflectAttr */) {\n          (_a2 = cmpMeta.$attrsToReflect$) == null ? void 0 : _a2.push([propName, attrName]);\n        }\n        return attrName;\n      })]));\n    }\n  }\n  return Cstr;\n};\n\n// src/runtime/initialize-component.ts\nvar initializeComponent = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(function* (elm, hostRef, cmpMeta, hmrVersionId) {\n    let Cstr;\n    if ((hostRef.$flags$ & 32 /* hasInitializedComponent */) === 0) {\n      hostRef.$flags$ |= 32 /* hasInitializedComponent */;\n      const bundleId = cmpMeta.$lazyBundleId$;\n      if (bundleId) {\n        const CstrImport = loadModule(cmpMeta, hostRef);\n        if (CstrImport && \"then\" in CstrImport) {\n          const endLoad = uniqueTime();\n          Cstr = yield CstrImport;\n          endLoad();\n        } else {\n          Cstr = CstrImport;\n        }\n        if (!Cstr) {\n          throw new Error(`Constructor for \"${cmpMeta.$tagName$}#${hostRef.$modeName$}\" was not found`);\n        }\n        if (!Cstr.isProxied) {\n          {\n            cmpMeta.$watchers$ = Cstr.watchers;\n          }\n          proxyComponent(Cstr, cmpMeta, 2 /* proxyState */);\n          Cstr.isProxied = true;\n        }\n        const endNewInstance = createTime(\"createInstance\", cmpMeta.$tagName$);\n        {\n          hostRef.$flags$ |= 8 /* isConstructingInstance */;\n        }\n        try {\n          new Cstr(hostRef);\n        } catch (e) {\n          consoleError(e, elm);\n        }\n        {\n          hostRef.$flags$ &= -9 /* isConstructingInstance */;\n        }\n        {\n          hostRef.$flags$ |= 128 /* isWatchReady */;\n        }\n        endNewInstance();\n        fireConnectedCallback(hostRef.$lazyInstance$, elm);\n      } else {\n        Cstr = elm.constructor;\n        const cmpTag = elm.localName;\n        customElements.whenDefined(cmpTag).then(() => hostRef.$flags$ |= 128 /* isWatchReady */);\n      }\n      if (Cstr && Cstr.style) {\n        let style;\n        if (typeof Cstr.style === \"string\") {\n          style = Cstr.style;\n        } else if (typeof Cstr.style !== \"string\") {\n          hostRef.$modeName$ = computeMode(elm);\n          if (hostRef.$modeName$) {\n            style = Cstr.style[hostRef.$modeName$];\n          }\n        }\n        const scopeId2 = getScopeId(cmpMeta, hostRef.$modeName$);\n        if (!styles.has(scopeId2)) {\n          const endRegisterStyles = createTime(\"registerStyles\", cmpMeta.$tagName$);\n          registerStyle(scopeId2, style, !!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */));\n          endRegisterStyles();\n        }\n      }\n    }\n    const ancestorComponent = hostRef.$ancestorComponent$;\n    const schedule = () => scheduleUpdate(hostRef, true);\n    if (ancestorComponent && ancestorComponent[\"s-rc\"]) {\n      ancestorComponent[\"s-rc\"].push(schedule);\n    } else {\n      schedule();\n    }\n  });\n  return function initializeComponent(_x4, _x5, _x6, _x7) {\n    return _ref2.apply(this, arguments);\n  };\n}();\nvar fireConnectedCallback = (instance, elm) => {\n  {\n    safeCall(instance, \"connectedCallback\", void 0, elm);\n  }\n};\n\n// src/runtime/connected-callback.ts\nvar connectedCallback = elm => {\n  if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    const cmpMeta = hostRef.$cmpMeta$;\n    const endConnected = createTime(\"connectedCallback\", cmpMeta.$tagName$);\n    if (!(hostRef.$flags$ & 1 /* hasConnected */)) {\n      hostRef.$flags$ |= 1 /* hasConnected */;\n      let hostId;\n      {\n        hostId = elm.getAttribute(HYDRATE_ID);\n        if (hostId) {\n          if (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            const scopeId2 = addStyle(elm.shadowRoot, cmpMeta, elm.getAttribute(\"s-mode\"));\n            elm.classList.remove(scopeId2 + \"-h\", scopeId2 + \"-s\");\n          } else if (cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n            const scopeId2 = getScopeId(cmpMeta, elm.getAttribute(\"s-mode\"));\n            elm[\"s-sc\"] = scopeId2;\n          }\n          initializeClientHydrate(elm, cmpMeta.$tagName$, hostId, hostRef);\n        }\n      }\n      if (!hostId) {\n        if (\n        // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n        cmpMeta.$flags$ & (4 /* hasSlotRelocation */ | 8 /* needsShadowDomShim */)) {\n          setContentReference(elm);\n        }\n      }\n      {\n        let ancestorComponent = elm;\n        while (ancestorComponent = ancestorComponent.parentNode || ancestorComponent.host) {\n          if (ancestorComponent.nodeType === 1 /* ElementNode */ && ancestorComponent.hasAttribute(\"s-id\") && ancestorComponent[\"s-p\"] || ancestorComponent[\"s-p\"]) {\n            attachToAncestor(hostRef, hostRef.$ancestorComponent$ = ancestorComponent);\n            break;\n          }\n        }\n      }\n      if (cmpMeta.$members$) {\n        Object.entries(cmpMeta.$members$).map(([memberName, [memberFlags]]) => {\n          if (memberFlags & 31 /* Prop */ && elm.hasOwnProperty(memberName)) {\n            const value = elm[memberName];\n            delete elm[memberName];\n            elm[memberName] = value;\n          }\n        });\n      }\n      {\n        initializeComponent(elm, hostRef, cmpMeta);\n      }\n    } else {\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$);\n      if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n        fireConnectedCallback(hostRef.$lazyInstance$, elm);\n      } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => fireConnectedCallback(hostRef.$lazyInstance$, elm));\n      }\n    }\n    endConnected();\n  }\n};\nvar setContentReference = elm => {\n  if (!win.document) {\n    return;\n  }\n  const contentRefElm = elm[\"s-cr\"] = win.document.createComment(\"\");\n  contentRefElm[\"s-cn\"] = true;\n  insertBefore(elm, contentRefElm, elm.firstChild);\n};\nvar disconnectInstance = (instance, elm) => {\n  {\n    safeCall(instance, \"disconnectedCallback\", void 0, elm || instance);\n  }\n};\nvar disconnectedCallback = /*#__PURE__*/function () {\n  var _ref3 = _asyncToGenerator(function* (elm) {\n    if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n      const hostRef = getHostRef(elm);\n      {\n        if (hostRef.$rmListeners$) {\n          hostRef.$rmListeners$.map(rmListener => rmListener());\n          hostRef.$rmListeners$ = void 0;\n        }\n      }\n      if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n        disconnectInstance(hostRef.$lazyInstance$, elm);\n      } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => disconnectInstance(hostRef.$lazyInstance$, elm));\n      }\n    }\n    if (rootAppliedStyles.has(elm)) {\n      rootAppliedStyles.delete(elm);\n    }\n    if (elm.shadowRoot && rootAppliedStyles.has(elm.shadowRoot)) {\n      rootAppliedStyles.delete(elm.shadowRoot);\n    }\n  });\n  return function disconnectedCallback(_x8) {\n    return _ref3.apply(this, arguments);\n  };\n}();\n\n// src/runtime/bootstrap-lazy.ts\nvar bootstrapLazy = (lazyBundles, options = {}) => {\n  var _a;\n  if (!win.document) {\n    console.warn(\"Stencil: No document found. Skipping bootstrapping lazy components.\");\n    return;\n  }\n  const endBootstrap = createTime();\n  const cmpTags = [];\n  const exclude = options.exclude || [];\n  const customElements2 = win.customElements;\n  const head = win.document.head;\n  const metaCharset = /* @__PURE__ */head.querySelector(\"meta[charset]\");\n  const dataStyles = /* @__PURE__ */win.document.createElement(\"style\");\n  const deferredConnectedCallbacks = [];\n  let appLoadFallback;\n  let isBootstrapping = true;\n  Object.assign(plt, options);\n  plt.$resourcesUrl$ = new URL(options.resourcesUrl || \"./\", win.document.baseURI).href;\n  {\n    plt.$flags$ |= 2 /* appLoaded */;\n  }\n  {\n    hydrateScopedToShadow();\n  }\n  let hasSlotRelocation = false;\n  lazyBundles.map(lazyBundle => {\n    lazyBundle[1].map(compactMeta => {\n      var _a2;\n      const cmpMeta = {\n        $flags$: compactMeta[0],\n        $tagName$: compactMeta[1],\n        $members$: compactMeta[2],\n        $listeners$: compactMeta[3]\n      };\n      if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n        hasSlotRelocation = true;\n      }\n      {\n        cmpMeta.$members$ = compactMeta[2];\n      }\n      {\n        cmpMeta.$listeners$ = compactMeta[3];\n      }\n      {\n        cmpMeta.$attrsToReflect$ = [];\n      }\n      {\n        cmpMeta.$watchers$ = (_a2 = compactMeta[4]) != null ? _a2 : {};\n      }\n      const tagName = cmpMeta.$tagName$;\n      const HostElement = class extends HTMLElement {\n        // StencilLazyHost\n        constructor(self) {\n          super(self);\n          this.hasRegisteredEventListeners = false;\n          self = this;\n          registerHost(self, cmpMeta);\n          if (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            {\n              if (!self.shadowRoot) {\n                createShadowRoot.call(self, cmpMeta);\n              } else {\n                if (self.shadowRoot.mode !== \"open\") {\n                  throw new Error(`Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${self.shadowRoot.mode} but Stencil only supports open shadow roots.`);\n                }\n              }\n            }\n          }\n        }\n        connectedCallback() {\n          const hostRef = getHostRef(this);\n          if (!this.hasRegisteredEventListeners) {\n            this.hasRegisteredEventListeners = true;\n            addHostEventListeners(this, hostRef, cmpMeta.$listeners$);\n          }\n          if (appLoadFallback) {\n            clearTimeout(appLoadFallback);\n            appLoadFallback = null;\n          }\n          if (isBootstrapping) {\n            deferredConnectedCallbacks.push(this);\n          } else {\n            plt.jmp(() => connectedCallback(this));\n          }\n        }\n        disconnectedCallback() {\n          plt.jmp(() => disconnectedCallback(this));\n          plt.raf(() => {\n            var _a3;\n            const hostRef = getHostRef(this);\n            const i2 = deferredConnectedCallbacks.findIndex(host => host === this);\n            if (i2 > -1) {\n              deferredConnectedCallbacks.splice(i2, 1);\n            }\n            if (((_a3 = hostRef == null ? void 0 : hostRef.$vnode$) == null ? void 0 : _a3.$elm$) instanceof Node && !hostRef.$vnode$.$elm$.isConnected) {\n              delete hostRef.$vnode$.$elm$;\n            }\n          });\n        }\n        componentOnReady() {\n          return getHostRef(this).$onReadyPromise$;\n        }\n      };\n      {\n        if (cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n          patchPseudoShadowDom(HostElement.prototype);\n        }\n      }\n      cmpMeta.$lazyBundleId$ = lazyBundle[0];\n      if (!exclude.includes(tagName) && !customElements2.get(tagName)) {\n        cmpTags.push(tagName);\n        customElements2.define(tagName, proxyComponent(HostElement, cmpMeta, 1 /* isElementConstructor */));\n      }\n    });\n  });\n  if (cmpTags.length > 0) {\n    if (hasSlotRelocation) {\n      dataStyles.textContent += SLOT_FB_CSS;\n    }\n    {\n      dataStyles.textContent += cmpTags.sort() + HYDRATED_CSS;\n    }\n    if (dataStyles.innerHTML.length) {\n      dataStyles.setAttribute(\"data-styles\", \"\");\n      const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(win.document);\n      if (nonce != null) {\n        dataStyles.setAttribute(\"nonce\", nonce);\n      }\n      head.insertBefore(dataStyles, metaCharset ? metaCharset.nextSibling : head.firstChild);\n    }\n  }\n  isBootstrapping = false;\n  if (deferredConnectedCallbacks.length) {\n    deferredConnectedCallbacks.map(host => host.connectedCallback());\n  } else {\n    {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30));\n    }\n  }\n  endBootstrap();\n};\n\n// src/runtime/fragment.ts\nvar Fragment = (_, children) => children;\nvar addHostEventListeners = (elm, hostRef, listeners, attachParentListeners) => {\n  if (listeners && win.document) {\n    listeners.map(([flags, name, method]) => {\n      const target = getHostListenerTarget(win.document, elm, flags);\n      const handler = hostListenerProxy(hostRef, method);\n      const opts = hostListenerOpts(flags);\n      plt.ael(target, name, handler, opts);\n      (hostRef.$rmListeners$ = hostRef.$rmListeners$ || []).push(() => plt.rel(target, name, handler, opts));\n    });\n  }\n};\nvar hostListenerProxy = (hostRef, methodName) => ev => {\n  var _a;\n  try {\n    {\n      if (hostRef.$flags$ & 256 /* isListenReady */) {\n        (_a = hostRef.$lazyInstance$) == null ? void 0 : _a[methodName](ev);\n      } else {\n        (hostRef.$queuedListeners$ = hostRef.$queuedListeners$ || []).push([methodName, ev]);\n      }\n    }\n  } catch (e) {\n    consoleError(e, hostRef.$hostElement$);\n  }\n};\nvar getHostListenerTarget = (doc, elm, flags) => {\n  if (flags & 4 /* TargetDocument */) {\n    return doc;\n  }\n  if (flags & 8 /* TargetWindow */) {\n    return win;\n  }\n  if (flags & 16 /* TargetBody */) {\n    return doc.body;\n  }\n  return elm;\n};\nvar hostListenerOpts = flags => supportsListenerOptions ? {\n  passive: (flags & 1 /* Passive */) !== 0,\n  capture: (flags & 2 /* Capture */) !== 0\n} : (flags & 2 /* Capture */) !== 0;\n\n// src/runtime/nonce.ts\nvar setNonce = nonce => plt.$nonce$ = nonce;\nexport { Build as B, Fragment as F, H, LogLevel as L, isPlatform as a, bootstrapLazy as b, getPlatforms as c, createEvent as d, getIonMode as e, readTask as f, globalScripts as g, h, initialize as i, Host as j, getElement as k, config as l, printIonWarning as m, forceUpdate as n, printIonError as o, promiseResolve as p, getAssetPath as q, registerInstance as r, setNonce as s, printRequiredElementError as t, writeTask as w };", "map": {"version": 3, "names": ["NAMESPACE", "BUILD", "experimentalSlotFixes", "hydratedSelectorName", "lazyLoad", "shadowDom", "slotRelocation", "updatable", "Config", "constructor", "m", "Map", "reset", "config<PERSON><PERSON><PERSON>", "Object", "entries", "get", "key", "fallback", "value", "undefined", "getBoolean", "val", "getNumber", "parseFloat", "isNaN", "NaN", "set", "config", "configFromSession", "win", "configStr", "sessionStorage", "getItem", "IONIC_SESSION_KEY", "JSON", "parse", "e", "saveConfig", "c", "setItem", "stringify", "configFromURL", "location", "search", "slice", "split", "map", "entry", "decodeURIComponent", "filter", "startsWith", "IONIC_PREFIX", "length", "for<PERSON>ach", "input", "substr", "LogLevel", "printIonWarning", "message", "params", "logLevel", "WARN", "includes", "console", "warn", "printIonError", "ERROR", "error", "printRequiredElementError", "el", "targetSelectors", "tagName", "toLowerCase", "join", "getPlatforms", "setupPlatforms", "isPlatform", "winOrPlatform", "platform", "window", "<PERSON><PERSON>", "platforms", "detectPlatforms", "p", "document", "documentElement", "classList", "add", "customPlatformMethods", "keys", "PLATFORMS_MAP", "customMethod", "isMobileWeb", "isMobile", "isHybrid", "isIpad", "testUserAgent", "isIphone", "isIOS", "isAndroid", "isAndroidTablet", "isPhablet", "width", "innerWidth", "height", "innerHeight", "smallest", "Math", "min", "largest", "max", "isTablet", "matchMedia", "isDesktop", "<PERSON><PERSON><PERSON><PERSON>", "isCapacitorNative", "capacitor", "isNative", "isNativePlatform", "isElectron", "isPWA", "_a", "call", "matches", "navigator", "standalone", "expr", "test", "userAgent", "query", "ipad", "iphone", "ios", "android", "phablet", "tablet", "<PERSON><PERSON>", "electron", "pwa", "mobile", "mobileweb", "desktop", "hybrid", "defaultMode", "getIonMode", "ref", "getMode", "initialize", "userConfig", "doc", "assign", "persistConfig", "mode", "getAttribute", "setAttribute", "isIonicElement", "elm", "isAllowedIonicModeValue", "elmMode", "setMode", "parentElement", "globalScripts", "globalStyles", "__defProp", "defineProperty", "__export", "target", "all", "name", "enumerable", "Build", "<PERSON><PERSON><PERSON><PERSON>", "SVG_NS", "HTML_NS", "PrimitiveType", "PrimitiveType2", "NonPrimitiveType", "NonPrimitiveType2", "TYPE_CONSTANT", "VALUE_CONSTANT", "SERIALIZED_PREFIX", "getHostRef", "__stencil__getHostRef", "registerInstance", "lazyInstance", "hostRef", "$lazyInstance$", "registerHost", "hostElement", "cmpMeta", "$flags$", "$hostElement$", "$cmpMeta$", "$instanceValues$", "$onInstancePromise$", "Promise", "r", "$onInstanceResolve$", "$onReadyPromise$", "$onReadyResolve$", "isMemberInElement", "memberName", "consoleError", "cmpModules", "loadModule", "hmrVersionId", "exportName", "$tagName$", "replace", "bundleId", "$lazyBundleId$", "module", "then", "importedModule", "styles", "modeResolutionChain", "CONTENT_REF_ID", "ORG_LOCATION_ID", "SLOT_NODE_ID", "TEXT_NODE_ID", "COMMENT_NODE_ID", "HYDRATE_ID", "HYDRATED_STYLE_ID", "HYDRATE_CHILD_ID", "HYDRATED_CSS", "SLOT_FB_CSS", "XLINK_NS", "H", "HTMLElement", "plt", "$resourcesUrl$", "jmp", "h2", "raf", "requestAnimationFrame", "ael", "eventName", "listener", "opts", "addEventListener", "rel", "removeEventListener", "ce", "CustomEvent", "supportsShadow", "supportsListenerOptions", "supportsListenerOptions2", "promiseResolve", "v", "resolve", "supportsConstructableStylesheets", "CSSStyleSheet", "replaceSync", "queuePending", "queueDomReads", "queueDomWrites", "queueTask", "queue", "write", "cb", "push", "nextTick", "flush", "consume", "i2", "performance", "now", "readTask", "writeTask", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "path", "assetUrl", "URL", "origin", "href", "pathname", "isDef", "isComplexType", "o", "queryNonceMetaTagContent", "_b", "_c", "head", "querySelector", "escapeRegExpSpecialCharacters", "text", "RemoteValue", "_RemoteValue", "fromLocalValue", "serialized", "type", "BigInt", "Infinity", "item", "Date", "map2", "deserializedKey", "deserializedValue", "obj", "pattern", "flags", "RegExp", "Set", "Symbol", "Error", "fromLocalValueArray", "serializedValues", "isLocalValueObject", "hasOwnProperty", "hasTypeProperty", "values", "result_exports", "err", "ok", "unwrap", "unwrapErr", "isOk", "isErr", "result", "fn", "newVal", "deserializeProperty", "atob", "createShadowRoot", "shadowRoot", "attachShadow", "delegatesFocus", "sheet", "adoptedStyleSheets", "updateFallbackSlotVisibility", "childNodes", "internalCall", "getHostSlotNodes", "slotNode", "nodeType", "getSlot<PERSON><PERSON>d<PERSON>", "getSlotName", "hidden", "childNode", "getSlottedChildNodes", "slottedNode", "isConnected", "hostName", "slotName", "slottedNodes", "slot", "includeSlot", "node", "nextS<PERSON>ling", "isNodeLocatedInSlot", "nodeToRelocate", "addSlotRelocateNode", "<PERSON><PERSON><PERSON><PERSON>", "prepend", "position", "slottedNodeLocation", "createTextNode", "parentNode", "parent", "append<PERSON><PERSON><PERSON>", "slotRelocateNodes", "n", "sort", "a", "b", "patchSlotNode", "assignedElements", "assignedNodes", "assignedFactory", "elementsOnly", "toReturn", "flatten", "__childNodes", "bind", "dispatchSlotChangeEvent", "dispatchEvent", "bubbles", "cancelable", "composed", "findSlotFromSlottedNode", "parentHost", "patchPseudoShadowDom", "hostElementPrototype", "patchCloneNode", "patchSlotAppendChild", "patchSlotAppend", "patchSlotPrepend", "patchSlotInsertAdjacentElement", "patchSlotInsertAdjacentHTML", "patchSlotInsertAdjacentText", "patchInsertBefore", "patchTextContent", "patchChildSlotNodes", "patchSlotRemoveChild", "HostElementPrototype", "orgCloneNode", "cloneNode", "deep", "srcNode", "isShadowDom", "clonedNode", "slotted", "nonStencilNode", "stencilPrivates", "every", "privateField", "__append<PERSON><PERSON>d", "append<PERSON><PERSON><PERSON>", "slotChildNodes", "appendAfter", "insertedNode", "ElementPrototype", "__remove<PERSON><PERSON>d", "<PERSON><PERSON><PERSON><PERSON>", "toRemove", "remove", "__prepend", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ownerDocument", "__append", "append", "originalInsertAdjacentHtml", "insertAdjacentHTML", "container", "createElement", "innerHTML", "<PERSON><PERSON><PERSON><PERSON>", "insertAdjacentText", "eleProto", "__insertBefore", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "found", "__parentNode", "isSameNode", "originalInsertAdjacentElement", "insertAdjacentElement", "element", "patchHostOriginalAccessor", "textContent", "FakeNodeList", "Array", "children", "patchSlottedNode", "__nextSibling", "globalThis", "Node", "patchNextSibling", "patchPreviousSibling", "patchParentNode", "ELEMENT_NODE", "patchNextElementSibling", "patchPreviousElementSibling", "parentNodes", "index", "indexOf", "__nextElementSibling", "parent<PERSON>les", "__previousSibling", "__previousElementSibling", "validElementPatches", "validNodesPatches", "accessorName", "accessor", "getOwnPropertyDescriptor", "Element", "prototype", "method", "createTime", "fnName", "uniqueTime", "measureText", "h", "nodeName", "vnodeData", "child", "simple", "lastSimple", "vNodeChildren", "walk", "isArray", "String", "$text$", "newVNode", "classData", "className", "class", "k", "vdomFnUtils", "vnode", "$attrs$", "$children$", "$key$", "$name$", "tag", "$tag$", "$elm$", "Host", "isHost", "convertToPublic", "convertToPrivate", "vattrs", "vchildren", "vkey", "vname", "vtag", "vtext", "initializeClientHydrate", "hostElm", "hostId", "endHydrate", "childRenderNodes", "slotNodes", "shadowRootNodes", "members", "$members$", "memberFlags", "metaAttributeName", "_a2", "attributeName", "attrVal", "attrPropVal", "parsePropertyValue", "scopeId2", "$orgLocNodes$", "size", "initializeDocumentHydrate", "body", "removeAttribute", "$vnode$", "clientHydrate", "crIndex", "cr<PERSON><PERSON><PERSON>", "childRenderNode", "orgLocationId", "$hostId$", "$nodeId$", "orgLocationNode", "toUpperCase", "parseInt", "delete", "hosts", "snLen", "snIndex", "slotGroup", "snGroupIdx", "snGroupLen", "slottedItem", "hostEle", "rnIdex", "rnLen", "from", "wholeText", "trim", "parentVNode", "childNodeType", "childIdSplt", "childVNode", "createSimpleVNode", "$depth$", "$index$", "addSlot", "nonShadowNodes", "nodeValue", "findCorrespondingNode", "orgLocNodes", "componentId", "defaultVNode", "slotId", "parentNodeId", "addSlottedNodes", "<PERSON><PERSON><PERSON>", "slotNodeId", "sibling", "createSupportsRuleRe", "selector", "safeSelector2", "computeMode", "find", "handler", "$modeName$", "propValue", "propType", "endsWith", "getElement", "createEvent", "emit", "detail", "emitEvent", "ev", "rootAppliedStyles", "WeakMap", "registerStyle", "cssText", "allowCS", "style", "addStyle", "styleContainerNode", "getScopeId", "appliedStyles", "styleElm", "has", "host", "nonce", "$nonce$", "preconnectLinks", "querySelectorAll", "referenceNode2", "stylesheet", "existingStyleContainer", "attachStyles", "endAttachStyles", "getRootNode", "cmp", "convertScopedToShadow", "css", "hydrateScopedToShadow", "styles2", "setAccessor", "oldValue", "newValue", "isSvg", "initialRender", "isProp", "ln", "oldClasses", "parseClassList", "newClasses", "prop", "removeProperty", "setProperty", "capture", "CAPTURE_EVENT_SUFFIX", "CAPTURE_EVENT_REGEX", "isComplex", "__lookupSetter__", "xlink", "removeAttributeNS", "setAttributeNS", "parseClassListRegex", "baseVal", "updateElement", "oldVnode", "newVnode", "isSvgMode2", "isInitialRender", "oldVnodeAttrs", "newVnodeAttrs", "sortedAttrNames", "attrNames", "attr", "scopeId", "contentRef", "hostTagName", "useNativeShadowDom", "checkSlotFallbackVisibility", "checkSlotRelocate", "isSvgMode", "createElm", "oldParentVNode", "newParentVNode", "childIndex", "newVNode2", "oldVNode", "createElementNS", "relocateToHostRoot", "addRemoveSlotScopedClass", "parentElm", "closest", "contentRefNode", "child<PERSON>odeA<PERSON>y", "reverse", "putBackInOriginalLocation", "recursive", "oldSlotChildNodes", "referenceNode", "addVnodes", "before", "vnodes", "startIdx", "endIdx", "containerElm", "removeVnodes", "nullifyVNodeRefs", "update<PERSON><PERSON><PERSON>n", "oldCh", "newCh", "oldStartIdx", "newStartIdx", "idxInOld", "oldEndIdx", "oldStartVnode", "oldEndVnode", "newEndIdx", "newStartVnode", "newEndVnode", "elmToMove", "isSameVnode", "patch", "leftVNode", "rightVNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultHolder", "data", "relocateNodes", "markSlotContentForRelocation", "hostContentNodes", "j", "relocateNodeData", "$nodeToRelocate$", "$slotRefNode$", "relocateNode", "some", "vNode", "newNode", "reference", "newParent", "old<PERSON>arent", "scopeName", "contains", "renderVdom", "renderFnResults", "isInitialLoad", "_d", "_e", "isHostElement", "rootVnode", "$attrsToReflect$", "propName", "attribute", "hasAttribute", "relocateData", "slotRefNode", "parentNodeRef", "insertBeforeNode", "previousSibling", "refNode", "attachToAncestor", "ancestorComponent", "$onRenderResolve$", "splice", "scheduleUpdate", "$ancestorComponent$", "dispatch", "dispatchHooks", "endSchedule", "instance", "<PERSON><PERSON><PERSON><PERSON>", "$queuedListeners$", "methodName", "event", "safeCall", "enqueue", "updateComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "catch", "err2", "_ref", "_asyncToGenerator", "endUpdate", "rc", "endRender", "callRender", "childrenPromises", "postUpdate", "postUpdateComponent", "_x", "_x2", "_x3", "apply", "arguments", "render", "endPostUpdate", "addHydratedFlag", "appDidLoad", "forceUpdate", "who", "namespace", "arg", "getValue", "setValue", "oldVal", "areBothNaN", "Number", "didValueChange", "$watchers$", "watchMethods", "watchMethodName", "componentShouldUpdate", "proxyComponent", "Cstr", "watchers", "origGetter", "origSetter", "configurable", "currentValue", "setterSetVal", "args", "_a3", "attrNameToPropName", "attributeChangedCallback", "attrName", "flags2", "callback<PERSON><PERSON>", "propDesc", "observedAttributes", "_", "initializeComponent", "_ref2", "CstrImport", "endLoad", "isProxied", "endNewInstance", "fireConnectedCallback", "cmpTag", "localName", "customElements", "whenDefined", "endRegisterStyles", "schedule", "_x4", "_x5", "_x6", "_x7", "connectedCallback", "endConnected", "setContentReference", "addHostEventListeners", "$listeners$", "contentRefElm", "createComment", "disconnectInstance", "disconnectedCallback", "_ref3", "$rmListeners$", "rmListener", "_x8", "bootstrapLazy", "lazyB<PERSON>les", "options", "endBootstrap", "cmpTags", "exclude", "customElements2", "metaCharset", "dataStyles", "deferredConnectedCallbacks", "appLoadFallback", "isBootstrapping", "resourcesUrl", "baseURI", "hasSlotRelocation", "lazyBundle", "compactMeta", "HostElement", "self", "hasRegisteredEventListeners", "clearTimeout", "findIndex", "componentOnReady", "define", "setTimeout", "Fragment", "listeners", "attachParentListeners", "getHostListenerTarget", "hostListenerProxy", "hostListenerOpts", "passive", "setNonce", "B", "F", "L", "d", "f", "g", "i", "l", "q", "s", "t", "w"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@ionic/core/dist/esm/index-B_U9CtaY.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst NAMESPACE = 'ionic';\nconst BUILD = /* ionic */ { experimentalSlotFixes: true, hydratedSelectorName: \"hydrated\", lazyLoad: true, shadowDom: true, slotRelocation: true, updatable: true};\n\n// TODO(FW-2832): types\nclass Config {\n    constructor() {\n        this.m = new Map();\n    }\n    reset(configObj) {\n        this.m = new Map(Object.entries(configObj));\n    }\n    get(key, fallback) {\n        const value = this.m.get(key);\n        return value !== undefined ? value : fallback;\n    }\n    getBoolean(key, fallback = false) {\n        const val = this.m.get(key);\n        if (val === undefined) {\n            return fallback;\n        }\n        if (typeof val === 'string') {\n            return val === 'true';\n        }\n        return !!val;\n    }\n    getNumber(key, fallback) {\n        const val = parseFloat(this.m.get(key));\n        return isNaN(val) ? (fallback !== undefined ? fallback : NaN) : val;\n    }\n    set(key, value) {\n        this.m.set(key, value);\n    }\n}\nconst config = /*@__PURE__*/ new Config();\nconst configFromSession = (win) => {\n    try {\n        const configStr = win.sessionStorage.getItem(IONIC_SESSION_KEY);\n        return configStr !== null ? JSON.parse(configStr) : {};\n    }\n    catch (e) {\n        return {};\n    }\n};\nconst saveConfig = (win, c) => {\n    try {\n        win.sessionStorage.setItem(IONIC_SESSION_KEY, JSON.stringify(c));\n    }\n    catch (e) {\n        return;\n    }\n};\nconst configFromURL = (win) => {\n    const configObj = {};\n    win.location.search\n        .slice(1)\n        .split('&')\n        .map((entry) => entry.split('='))\n        .map(([key, value]) => {\n        try {\n            return [decodeURIComponent(key), decodeURIComponent(value)];\n        }\n        catch (e) {\n            return ['', ''];\n        }\n    })\n        .filter(([key]) => startsWith(key, IONIC_PREFIX))\n        .map(([key, value]) => [key.slice(IONIC_PREFIX.length), value])\n        .forEach(([key, value]) => {\n        configObj[key] = value;\n    });\n    return configObj;\n};\nconst startsWith = (input, search) => {\n    return input.substr(0, search.length) === search;\n};\nconst IONIC_PREFIX = 'ionic:';\nconst IONIC_SESSION_KEY = 'ionic-persist-config';\n\nvar LogLevel;\n(function (LogLevel) {\n    LogLevel[\"OFF\"] = \"OFF\";\n    LogLevel[\"ERROR\"] = \"ERROR\";\n    LogLevel[\"WARN\"] = \"WARN\";\n})(LogLevel || (LogLevel = {}));\n/**\n * Logs a warning to the console with an Ionic prefix\n * to indicate the library that is warning the developer.\n *\n * @param message - The string message to be logged to the console.\n */\nconst printIonWarning = (message, ...params) => {\n    const logLevel = config.get('logLevel', LogLevel.WARN);\n    if ([LogLevel.WARN].includes(logLevel)) {\n        return console.warn(`[Ionic Warning]: ${message}`, ...params);\n    }\n};\n/**\n * Logs an error to the console with an Ionic prefix\n * to indicate the library that is warning the developer.\n *\n * @param message - The string message to be logged to the console.\n * @param params - Additional arguments to supply to the console.error.\n */\nconst printIonError = (message, ...params) => {\n    const logLevel = config.get('logLevel', LogLevel.ERROR);\n    if ([LogLevel.ERROR, LogLevel.WARN].includes(logLevel)) {\n        return console.error(`[Ionic Error]: ${message}`, ...params);\n    }\n};\n/**\n * Prints an error informing developers that an implementation requires an element to be used\n * within a specific selector.\n *\n * @param el The web component element this is requiring the element.\n * @param targetSelectors The selector or selectors that were not found.\n */\nconst printRequiredElementError = (el, ...targetSelectors) => {\n    return console.error(`<${el.tagName.toLowerCase()}> must be used inside ${targetSelectors.join(' or ')}.`);\n};\n\nconst getPlatforms = (win) => setupPlatforms(win);\nconst isPlatform = (winOrPlatform, platform) => {\n    if (typeof winOrPlatform === 'string') {\n        platform = winOrPlatform;\n        winOrPlatform = undefined;\n    }\n    return getPlatforms(winOrPlatform).includes(platform);\n};\nconst setupPlatforms = (win = window) => {\n    if (typeof win === 'undefined') {\n        return [];\n    }\n    win.Ionic = win.Ionic || {};\n    let platforms = win.Ionic.platforms;\n    if (platforms == null) {\n        platforms = win.Ionic.platforms = detectPlatforms(win);\n        platforms.forEach((p) => win.document.documentElement.classList.add(`plt-${p}`));\n    }\n    return platforms;\n};\nconst detectPlatforms = (win) => {\n    const customPlatformMethods = config.get('platform');\n    return Object.keys(PLATFORMS_MAP).filter((p) => {\n        const customMethod = customPlatformMethods === null || customPlatformMethods === void 0 ? void 0 : customPlatformMethods[p];\n        return typeof customMethod === 'function' ? customMethod(win) : PLATFORMS_MAP[p](win);\n    });\n};\nconst isMobileWeb = (win) => isMobile(win) && !isHybrid(win);\nconst isIpad = (win) => {\n    // iOS 12 and below\n    if (testUserAgent(win, /iPad/i)) {\n        return true;\n    }\n    // iOS 13+\n    if (testUserAgent(win, /Macintosh/i) && isMobile(win)) {\n        return true;\n    }\n    return false;\n};\nconst isIphone = (win) => testUserAgent(win, /iPhone/i);\nconst isIOS = (win) => testUserAgent(win, /iPhone|iPod/i) || isIpad(win);\nconst isAndroid = (win) => testUserAgent(win, /android|sink/i);\nconst isAndroidTablet = (win) => {\n    return isAndroid(win) && !testUserAgent(win, /mobile/i);\n};\nconst isPhablet = (win) => {\n    const width = win.innerWidth;\n    const height = win.innerHeight;\n    const smallest = Math.min(width, height);\n    const largest = Math.max(width, height);\n    return smallest > 390 && smallest < 520 && largest > 620 && largest < 800;\n};\nconst isTablet = (win) => {\n    const width = win.innerWidth;\n    const height = win.innerHeight;\n    const smallest = Math.min(width, height);\n    const largest = Math.max(width, height);\n    return isIpad(win) || isAndroidTablet(win) || (smallest > 460 && smallest < 820 && largest > 780 && largest < 1400);\n};\nconst isMobile = (win) => matchMedia(win, '(any-pointer:coarse)');\nconst isDesktop = (win) => !isMobile(win);\nconst isHybrid = (win) => isCordova(win) || isCapacitorNative(win);\nconst isCordova = (win) => !!(win['cordova'] || win['phonegap'] || win['PhoneGap']);\nconst isCapacitorNative = (win) => {\n    const capacitor = win['Capacitor'];\n    // TODO(ROU-11693): Remove when we no longer support Capacitor 2, which does not have isNativePlatform\n    return !!((capacitor === null || capacitor === void 0 ? void 0 : capacitor.isNative) || ((capacitor === null || capacitor === void 0 ? void 0 : capacitor.isNativePlatform) && !!capacitor.isNativePlatform()));\n};\nconst isElectron = (win) => testUserAgent(win, /electron/i);\nconst isPWA = (win) => { var _a; return !!(((_a = win.matchMedia) === null || _a === void 0 ? void 0 : _a.call(win, '(display-mode: standalone)').matches) || win.navigator.standalone); };\nconst testUserAgent = (win, expr) => expr.test(win.navigator.userAgent);\nconst matchMedia = (win, query) => { var _a; return (_a = win.matchMedia) === null || _a === void 0 ? void 0 : _a.call(win, query).matches; };\nconst PLATFORMS_MAP = {\n    ipad: isIpad,\n    iphone: isIphone,\n    ios: isIOS,\n    android: isAndroid,\n    phablet: isPhablet,\n    tablet: isTablet,\n    cordova: isCordova,\n    capacitor: isCapacitorNative,\n    electron: isElectron,\n    pwa: isPWA,\n    mobile: isMobile,\n    mobileweb: isMobileWeb,\n    desktop: isDesktop,\n    hybrid: isHybrid,\n};\n\n// TODO(FW-2832): types\nlet defaultMode;\nconst getIonMode = (ref) => {\n    return (ref && getMode(ref)) || defaultMode;\n};\nconst initialize = (userConfig = {}) => {\n    if (typeof window === 'undefined') {\n        return;\n    }\n    const doc = window.document;\n    const win = window;\n    const Ionic = (win.Ionic = win.Ionic || {});\n    // create the Ionic.config from raw config object (if it exists)\n    // and convert Ionic.config into a ConfigApi that has a get() fn\n    const configObj = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, configFromSession(win)), { persistConfig: false }), Ionic.config), configFromURL(win)), userConfig);\n    config.reset(configObj);\n    if (config.getBoolean('persistConfig')) {\n        saveConfig(win, configObj);\n    }\n    // Setup platforms\n    setupPlatforms(win);\n    // first see if the mode was set as an attribute on <html>\n    // which could have been set by the user, or by pre-rendering\n    // otherwise get the mode via config settings, and fallback to md\n    Ionic.config = config;\n    Ionic.mode = defaultMode = config.get('mode', doc.documentElement.getAttribute('mode') || (isPlatform(win, 'ios') ? 'ios' : 'md'));\n    config.set('mode', defaultMode);\n    doc.documentElement.setAttribute('mode', defaultMode);\n    doc.documentElement.classList.add(defaultMode);\n    if (config.getBoolean('_testing')) {\n        config.set('animated', false);\n    }\n    const isIonicElement = (elm) => { var _a; return (_a = elm.tagName) === null || _a === void 0 ? void 0 : _a.startsWith('ION-'); };\n    const isAllowedIonicModeValue = (elmMode) => ['ios', 'md'].includes(elmMode);\n    setMode((elm) => {\n        while (elm) {\n            const elmMode = elm.mode || elm.getAttribute('mode');\n            if (elmMode) {\n                if (isAllowedIonicModeValue(elmMode)) {\n                    return elmMode;\n                }\n                else if (isIonicElement(elm)) {\n                    printIonWarning('Invalid ionic mode: \"' + elmMode + '\", expected: \"ios\" or \"md\"');\n                }\n            }\n            elm = elm.parentElement;\n        }\n        return defaultMode;\n    });\n};\n\nconst globalScripts = initialize;\nconst globalStyles = \"\";\n\n/*\n Stencil Client Platform v4.33.1 | MIT Licensed | https://stenciljs.com\n */\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar Build = {\n  isBrowser: true};\n\n// src/utils/constants.ts\nvar SVG_NS = \"http://www.w3.org/2000/svg\";\nvar HTML_NS = \"http://www.w3.org/1999/xhtml\";\nvar PrimitiveType = /* @__PURE__ */ ((PrimitiveType2) => {\n  PrimitiveType2[\"Undefined\"] = \"undefined\";\n  PrimitiveType2[\"Null\"] = \"null\";\n  PrimitiveType2[\"String\"] = \"string\";\n  PrimitiveType2[\"Number\"] = \"number\";\n  PrimitiveType2[\"SpecialNumber\"] = \"number\";\n  PrimitiveType2[\"Boolean\"] = \"boolean\";\n  PrimitiveType2[\"BigInt\"] = \"bigint\";\n  return PrimitiveType2;\n})(PrimitiveType || {});\nvar NonPrimitiveType = /* @__PURE__ */ ((NonPrimitiveType2) => {\n  NonPrimitiveType2[\"Array\"] = \"array\";\n  NonPrimitiveType2[\"Date\"] = \"date\";\n  NonPrimitiveType2[\"Map\"] = \"map\";\n  NonPrimitiveType2[\"Object\"] = \"object\";\n  NonPrimitiveType2[\"RegularExpression\"] = \"regexp\";\n  NonPrimitiveType2[\"Set\"] = \"set\";\n  NonPrimitiveType2[\"Channel\"] = \"channel\";\n  NonPrimitiveType2[\"Symbol\"] = \"symbol\";\n  return NonPrimitiveType2;\n})(NonPrimitiveType || {});\nvar TYPE_CONSTANT = \"type\";\nvar VALUE_CONSTANT = \"value\";\nvar SERIALIZED_PREFIX = \"serialized:\";\n\n// src/client/client-host-ref.ts\nvar getHostRef = (ref) => {\n  if (ref.__stencil__getHostRef) {\n    return ref.__stencil__getHostRef();\n  }\n  return void 0;\n};\nvar registerInstance = (lazyInstance, hostRef) => {\n  lazyInstance.__stencil__getHostRef = () => hostRef;\n  hostRef.$lazyInstance$ = lazyInstance;\n};\nvar registerHost = (hostElement, cmpMeta) => {\n  const hostRef = {\n    $flags$: 0,\n    $hostElement$: hostElement,\n    $cmpMeta$: cmpMeta,\n    $instanceValues$: /* @__PURE__ */ new Map()\n  };\n  {\n    hostRef.$onInstancePromise$ = new Promise((r) => hostRef.$onInstanceResolve$ = r);\n  }\n  {\n    hostRef.$onReadyPromise$ = new Promise((r) => hostRef.$onReadyResolve$ = r);\n    hostElement[\"s-p\"] = [];\n    hostElement[\"s-rc\"] = [];\n  }\n  const ref = hostRef;\n  hostElement.__stencil__getHostRef = () => ref;\n  return ref;\n};\nvar isMemberInElement = (elm, memberName) => memberName in elm;\nvar consoleError = (e, el) => (0, console.error)(e, el);\n\n// src/client/client-load-module.ts\nvar cmpModules = /* @__PURE__ */ new Map();\nvar loadModule = (cmpMeta, hostRef, hmrVersionId) => {\n  const exportName = cmpMeta.$tagName$.replace(/-/g, \"_\");\n  const bundleId = cmpMeta.$lazyBundleId$;\n  if (!bundleId) {\n    return void 0;\n  }\n  const module = cmpModules.get(bundleId) ;\n  if (module) {\n    return module[exportName];\n  }\n  /*!__STENCIL_STATIC_IMPORT_SWITCH__*/\n  return import(\n    /* @vite-ignore */\n    /* webpackInclude: /\\.entry\\.js$/ */\n    /* webpackExclude: /\\.system\\.entry\\.js$/ */\n    /* webpackMode: \"lazy\" */\n    `./${bundleId}.entry.js${\"\"}`\n  ).then(\n    (importedModule) => {\n      {\n        cmpModules.set(bundleId, importedModule);\n      }\n      return importedModule[exportName];\n    },\n    (e) => {\n      consoleError(e, hostRef.$hostElement$);\n    }\n  );\n};\n\n// src/client/client-style.ts\nvar styles = /* @__PURE__ */ new Map();\nvar modeResolutionChain = [];\n\n// src/runtime/runtime-constants.ts\nvar CONTENT_REF_ID = \"r\";\nvar ORG_LOCATION_ID = \"o\";\nvar SLOT_NODE_ID = \"s\";\nvar TEXT_NODE_ID = \"t\";\nvar COMMENT_NODE_ID = \"c\";\nvar HYDRATE_ID = \"s-id\";\nvar HYDRATED_STYLE_ID = \"sty-id\";\nvar HYDRATE_CHILD_ID = \"c-id\";\nvar HYDRATED_CSS = \"{visibility:hidden}.hydrated{visibility:inherit}\";\nvar SLOT_FB_CSS = \"slot-fb{display:contents}slot-fb[hidden]{display:none}\";\nvar XLINK_NS = \"http://www.w3.org/1999/xlink\";\nvar win = typeof window !== \"undefined\" ? window : {};\nvar H = win.HTMLElement || class {\n};\nvar plt = {\n  $flags$: 0,\n  $resourcesUrl$: \"\",\n  jmp: (h2) => h2(),\n  raf: (h2) => requestAnimationFrame(h2),\n  ael: (el, eventName, listener, opts) => el.addEventListener(eventName, listener, opts),\n  rel: (el, eventName, listener, opts) => el.removeEventListener(eventName, listener, opts),\n  ce: (eventName, opts) => new CustomEvent(eventName, opts)\n};\nvar supportsShadow = BUILD.shadowDom;\nvar supportsListenerOptions = /* @__PURE__ */ (() => {\n  var _a;\n  let supportsListenerOptions2 = false;\n  try {\n    (_a = win.document) == null ? void 0 : _a.addEventListener(\n      \"e\",\n      null,\n      Object.defineProperty({}, \"passive\", {\n        get() {\n          supportsListenerOptions2 = true;\n        }\n      })\n    );\n  } catch (e) {\n  }\n  return supportsListenerOptions2;\n})();\nvar promiseResolve = (v) => Promise.resolve(v);\nvar supportsConstructableStylesheets = /* @__PURE__ */ (() => {\n  try {\n    new CSSStyleSheet();\n    return typeof new CSSStyleSheet().replaceSync === \"function\";\n  } catch (e) {\n  }\n  return false;\n})() ;\nvar queuePending = false;\nvar queueDomReads = [];\nvar queueDomWrites = [];\nvar queueTask = (queue, write) => (cb) => {\n  queue.push(cb);\n  if (!queuePending) {\n    queuePending = true;\n    if (write && plt.$flags$ & 4 /* queueSync */) {\n      nextTick(flush);\n    } else {\n      plt.raf(flush);\n    }\n  }\n};\nvar consume = (queue) => {\n  for (let i2 = 0; i2 < queue.length; i2++) {\n    try {\n      queue[i2](performance.now());\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  queue.length = 0;\n};\nvar flush = () => {\n  consume(queueDomReads);\n  {\n    consume(queueDomWrites);\n    if (queuePending = queueDomReads.length > 0) {\n      plt.raf(flush);\n    }\n  }\n};\nvar nextTick = (cb) => promiseResolve().then(cb);\nvar readTask = /* @__PURE__ */ queueTask(queueDomReads, false);\nvar writeTask = /* @__PURE__ */ queueTask(queueDomWrites, true);\n\n// src/runtime/asset-path.ts\nvar getAssetPath = (path) => {\n  const assetUrl = new URL(path, plt.$resourcesUrl$);\n  return assetUrl.origin !== win.location.origin ? assetUrl.href : assetUrl.pathname;\n};\n\n// src/utils/helpers.ts\nvar isDef = (v) => v != null && v !== void 0;\nvar isComplexType = (o) => {\n  o = typeof o;\n  return o === \"object\" || o === \"function\";\n};\n\n// src/utils/query-nonce-meta-tag-content.ts\nfunction queryNonceMetaTagContent(doc) {\n  var _a, _b, _c;\n  return (_c = (_b = (_a = doc.head) == null ? void 0 : _a.querySelector('meta[name=\"csp-nonce\"]')) == null ? void 0 : _b.getAttribute(\"content\")) != null ? _c : void 0;\n}\n\n// src/utils/regular-expression.ts\nvar escapeRegExpSpecialCharacters = (text) => {\n  return text.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n};\n\n// src/utils/remote-value.ts\nvar RemoteValue = class _RemoteValue {\n  /**\n   * Deserializes a LocalValue serialized object back to its original JavaScript representation\n   *\n   * @param serialized The serialized LocalValue object\n   * @returns The original JavaScript value/object\n   */\n  static fromLocalValue(serialized) {\n    const type = serialized[TYPE_CONSTANT];\n    const value = VALUE_CONSTANT in serialized ? serialized[VALUE_CONSTANT] : void 0;\n    switch (type) {\n      case \"string\" /* String */:\n        return value;\n      case \"boolean\" /* Boolean */:\n        return value;\n      case \"bigint\" /* BigInt */:\n        return BigInt(value);\n      case \"undefined\" /* Undefined */:\n        return void 0;\n      case \"null\" /* Null */:\n        return null;\n      case \"number\" /* Number */:\n        if (value === \"NaN\") return NaN;\n        if (value === \"-0\") return -0;\n        if (value === \"Infinity\") return Infinity;\n        if (value === \"-Infinity\") return -Infinity;\n        return value;\n      case \"array\" /* Array */:\n        return value.map((item) => _RemoteValue.fromLocalValue(item));\n      case \"date\" /* Date */:\n        return new Date(value);\n      case \"map\" /* Map */:\n        const map2 = /* @__PURE__ */ new Map();\n        for (const [key, val] of value) {\n          const deserializedKey = typeof key === \"object\" && key !== null ? _RemoteValue.fromLocalValue(key) : key;\n          const deserializedValue = _RemoteValue.fromLocalValue(val);\n          map2.set(deserializedKey, deserializedValue);\n        }\n        return map2;\n      case \"object\" /* Object */:\n        const obj = {};\n        for (const [key, val] of value) {\n          obj[key] = _RemoteValue.fromLocalValue(val);\n        }\n        return obj;\n      case \"regexp\" /* RegularExpression */:\n        const { pattern, flags } = value;\n        return new RegExp(pattern, flags);\n      case \"set\" /* Set */:\n        const set = /* @__PURE__ */ new Set();\n        for (const item of value) {\n          set.add(_RemoteValue.fromLocalValue(item));\n        }\n        return set;\n      case \"symbol\" /* Symbol */:\n        return Symbol(value);\n      default:\n        throw new Error(`Unsupported type: ${type}`);\n    }\n  }\n  /**\n   * Utility method to deserialize multiple LocalValues at once\n   *\n   * @param serializedValues Array of serialized LocalValue objects\n   * @returns Array of deserialized JavaScript values\n   */\n  static fromLocalValueArray(serializedValues) {\n    return serializedValues.map((value) => _RemoteValue.fromLocalValue(value));\n  }\n  /**\n   * Verifies if the given object matches the structure of a serialized LocalValue\n   *\n   * @param obj Object to verify\n   * @returns boolean indicating if the object has LocalValue structure\n   */\n  static isLocalValueObject(obj) {\n    if (typeof obj !== \"object\" || obj === null) {\n      return false;\n    }\n    if (!obj.hasOwnProperty(TYPE_CONSTANT)) {\n      return false;\n    }\n    const type = obj[TYPE_CONSTANT];\n    const hasTypeProperty = Object.values({ ...PrimitiveType, ...NonPrimitiveType }).includes(type);\n    if (!hasTypeProperty) {\n      return false;\n    }\n    if (type !== \"null\" /* Null */ && type !== \"undefined\" /* Undefined */) {\n      return obj.hasOwnProperty(VALUE_CONSTANT);\n    }\n    return true;\n  }\n};\n\n// src/utils/result.ts\nvar result_exports = {};\n__export(result_exports, {\n  err: () => err,\n  map: () => map,\n  ok: () => ok,\n  unwrap: () => unwrap,\n  unwrapErr: () => unwrapErr\n});\nvar ok = (value) => ({\n  isOk: true,\n  isErr: false,\n  value\n});\nvar err = (value) => ({\n  isOk: false,\n  isErr: true,\n  value\n});\nfunction map(result, fn) {\n  if (result.isOk) {\n    const val = fn(result.value);\n    if (val instanceof Promise) {\n      return val.then((newVal) => ok(newVal));\n    } else {\n      return ok(val);\n    }\n  }\n  if (result.isErr) {\n    const value = result.value;\n    return err(value);\n  }\n  throw \"should never get here\";\n}\nvar unwrap = (result) => {\n  if (result.isOk) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\nvar unwrapErr = (result) => {\n  if (result.isErr) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\n\n// src/utils/serialize.ts\nfunction deserializeProperty(value) {\n  if (typeof value !== \"string\" || !value.startsWith(SERIALIZED_PREFIX)) {\n    return value;\n  }\n  return RemoteValue.fromLocalValue(JSON.parse(atob(value.slice(SERIALIZED_PREFIX.length))));\n}\nfunction createShadowRoot(cmpMeta) {\n  const shadowRoot = this.attachShadow({\n    mode: \"open\",\n    delegatesFocus: !!(cmpMeta.$flags$ & 16 /* shadowDelegatesFocus */)\n  }) ;\n  if (supportsConstructableStylesheets) {\n    const sheet = new CSSStyleSheet();\n    sheet.replaceSync(globalStyles);\n    shadowRoot.adoptedStyleSheets.push(sheet);\n  }\n}\nvar updateFallbackSlotVisibility = (elm) => {\n  const childNodes = internalCall(elm, \"childNodes\");\n  if (elm.tagName && elm.tagName.includes(\"-\") && elm[\"s-cr\"] && elm.tagName !== \"SLOT-FB\") {\n    getHostSlotNodes(childNodes, elm.tagName).forEach((slotNode) => {\n      if (slotNode.nodeType === 1 /* ElementNode */ && slotNode.tagName === \"SLOT-FB\") {\n        if (getSlotChildSiblings(slotNode, getSlotName(slotNode), false).length) {\n          slotNode.hidden = true;\n        } else {\n          slotNode.hidden = false;\n        }\n      }\n    });\n  }\n  let i2 = 0;\n  for (i2 = 0; i2 < childNodes.length; i2++) {\n    const childNode = childNodes[i2];\n    if (childNode.nodeType === 1 /* ElementNode */ && internalCall(childNode, \"childNodes\").length) {\n      updateFallbackSlotVisibility(childNode);\n    }\n  }\n};\nvar getSlottedChildNodes = (childNodes) => {\n  const result = [];\n  for (let i2 = 0; i2 < childNodes.length; i2++) {\n    const slottedNode = childNodes[i2][\"s-nr\"] || void 0;\n    if (slottedNode && slottedNode.isConnected) {\n      result.push(slottedNode);\n    }\n  }\n  return result;\n};\nfunction getHostSlotNodes(childNodes, hostName, slotName) {\n  let i2 = 0;\n  let slottedNodes = [];\n  let childNode;\n  for (; i2 < childNodes.length; i2++) {\n    childNode = childNodes[i2];\n    if (childNode[\"s-sr\"] && (!hostName || childNode[\"s-hn\"] === hostName) && (slotName === void 0 || getSlotName(childNode) === slotName)) {\n      slottedNodes.push(childNode);\n      if (typeof slotName !== \"undefined\") return slottedNodes;\n    }\n    slottedNodes = [...slottedNodes, ...getHostSlotNodes(childNode.childNodes, hostName, slotName)];\n  }\n  return slottedNodes;\n}\nvar getSlotChildSiblings = (slot, slotName, includeSlot = true) => {\n  const childNodes = [];\n  if (includeSlot && slot[\"s-sr\"] || !slot[\"s-sr\"]) childNodes.push(slot);\n  let node = slot;\n  while (node = node.nextSibling) {\n    if (getSlotName(node) === slotName && (includeSlot || !node[\"s-sr\"])) childNodes.push(node);\n  }\n  return childNodes;\n};\nvar isNodeLocatedInSlot = (nodeToRelocate, slotName) => {\n  if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n    if (nodeToRelocate.getAttribute(\"slot\") === null && slotName === \"\") {\n      return true;\n    }\n    if (nodeToRelocate.getAttribute(\"slot\") === slotName) {\n      return true;\n    }\n    return false;\n  }\n  if (nodeToRelocate[\"s-sn\"] === slotName) {\n    return true;\n  }\n  return slotName === \"\";\n};\nvar addSlotRelocateNode = (newChild, slotNode, prepend, position) => {\n  if (newChild[\"s-ol\"] && newChild[\"s-ol\"].isConnected) {\n    return;\n  }\n  const slottedNodeLocation = document.createTextNode(\"\");\n  slottedNodeLocation[\"s-nr\"] = newChild;\n  if (!slotNode[\"s-cr\"] || !slotNode[\"s-cr\"].parentNode) return;\n  const parent = slotNode[\"s-cr\"].parentNode;\n  const appendMethod = prepend ? internalCall(parent, \"prepend\") : internalCall(parent, \"appendChild\");\n  if (typeof position !== \"undefined\") {\n    slottedNodeLocation[\"s-oo\"] = position;\n    const childNodes = internalCall(parent, \"childNodes\");\n    const slotRelocateNodes = [slottedNodeLocation];\n    childNodes.forEach((n) => {\n      if (n[\"s-nr\"]) slotRelocateNodes.push(n);\n    });\n    slotRelocateNodes.sort((a, b) => {\n      if (!a[\"s-oo\"] || a[\"s-oo\"] < (b[\"s-oo\"] || 0)) return -1;\n      else if (!b[\"s-oo\"] || b[\"s-oo\"] < a[\"s-oo\"]) return 1;\n      return 0;\n    });\n    slotRelocateNodes.forEach((n) => appendMethod.call(parent, n));\n  } else {\n    appendMethod.call(parent, slottedNodeLocation);\n  }\n  newChild[\"s-ol\"] = slottedNodeLocation;\n  newChild[\"s-sh\"] = slotNode[\"s-hn\"];\n};\nvar getSlotName = (node) => typeof node[\"s-sn\"] === \"string\" ? node[\"s-sn\"] : node.nodeType === 1 && node.getAttribute(\"slot\") || void 0;\nfunction patchSlotNode(node) {\n  if (node.assignedElements || node.assignedNodes || !node[\"s-sr\"]) return;\n  const assignedFactory = (elementsOnly) => (function(opts) {\n    const toReturn = [];\n    const slotName = this[\"s-sn\"];\n    if (opts == null ? void 0 : opts.flatten) {\n      console.error(`\n          Flattening is not supported for Stencil non-shadow slots. \n          You can use \\`.childNodes\\` to nested slot fallback content.\n          If you have a particular use case, please open an issue on the Stencil repo.\n        `);\n    }\n    const parent = this[\"s-cr\"].parentElement;\n    const slottedNodes = parent.__childNodes ? parent.childNodes : getSlottedChildNodes(parent.childNodes);\n    slottedNodes.forEach((n) => {\n      if (slotName === getSlotName(n)) {\n        toReturn.push(n);\n      }\n    });\n    if (elementsOnly) {\n      return toReturn.filter((n) => n.nodeType === 1 /* ElementNode */);\n    }\n    return toReturn;\n  }).bind(node);\n  node.assignedElements = assignedFactory(true);\n  node.assignedNodes = assignedFactory(false);\n}\nfunction dispatchSlotChangeEvent(elm) {\n  elm.dispatchEvent(new CustomEvent(\"slotchange\", { bubbles: false, cancelable: false, composed: false }));\n}\nfunction findSlotFromSlottedNode(slottedNode, parentHost) {\n  var _a;\n  parentHost = parentHost || ((_a = slottedNode[\"s-ol\"]) == null ? void 0 : _a.parentElement);\n  if (!parentHost) return { slotNode: null, slotName: \"\" };\n  const slotName = slottedNode[\"s-sn\"] = getSlotName(slottedNode) || \"\";\n  const childNodes = internalCall(parentHost, \"childNodes\");\n  const slotNode = getHostSlotNodes(childNodes, parentHost.tagName, slotName)[0];\n  return { slotNode, slotName };\n}\n\n// src/runtime/dom-extras.ts\nvar patchPseudoShadowDom = (hostElementPrototype) => {\n  patchCloneNode(hostElementPrototype);\n  patchSlotAppendChild(hostElementPrototype);\n  patchSlotAppend(hostElementPrototype);\n  patchSlotPrepend(hostElementPrototype);\n  patchSlotInsertAdjacentElement(hostElementPrototype);\n  patchSlotInsertAdjacentHTML(hostElementPrototype);\n  patchSlotInsertAdjacentText(hostElementPrototype);\n  patchInsertBefore(hostElementPrototype);\n  patchTextContent(hostElementPrototype);\n  patchChildSlotNodes(hostElementPrototype);\n  patchSlotRemoveChild(hostElementPrototype);\n};\nvar patchCloneNode = (HostElementPrototype) => {\n  const orgCloneNode = HostElementPrototype.cloneNode;\n  HostElementPrototype.cloneNode = function(deep) {\n    const srcNode = this;\n    const isShadowDom = srcNode.shadowRoot && supportsShadow ;\n    const clonedNode = orgCloneNode.call(srcNode, isShadowDom ? deep : false);\n    if (!isShadowDom && deep) {\n      let i2 = 0;\n      let slotted, nonStencilNode;\n      const stencilPrivates = [\n        \"s-id\",\n        \"s-cr\",\n        \"s-lr\",\n        \"s-rc\",\n        \"s-sc\",\n        \"s-p\",\n        \"s-cn\",\n        \"s-sr\",\n        \"s-sn\",\n        \"s-hn\",\n        \"s-ol\",\n        \"s-nr\",\n        \"s-si\",\n        \"s-rf\",\n        \"s-scs\"\n      ];\n      const childNodes = this.__childNodes || this.childNodes;\n      for (; i2 < childNodes.length; i2++) {\n        slotted = childNodes[i2][\"s-nr\"];\n        nonStencilNode = stencilPrivates.every((privateField) => !childNodes[i2][privateField]);\n        if (slotted) {\n          if (clonedNode.__appendChild) {\n            clonedNode.__appendChild(slotted.cloneNode(true));\n          } else {\n            clonedNode.appendChild(slotted.cloneNode(true));\n          }\n        }\n        if (nonStencilNode) {\n          clonedNode.appendChild(childNodes[i2].cloneNode(true));\n        }\n      }\n    }\n    return clonedNode;\n  };\n};\nvar patchSlotAppendChild = (HostElementPrototype) => {\n  HostElementPrototype.__appendChild = HostElementPrototype.appendChild;\n  HostElementPrototype.appendChild = function(newChild) {\n    const { slotName, slotNode } = findSlotFromSlottedNode(newChild, this);\n    if (slotNode) {\n      addSlotRelocateNode(newChild, slotNode);\n      const slotChildNodes = getSlotChildSiblings(slotNode, slotName);\n      const appendAfter = slotChildNodes[slotChildNodes.length - 1];\n      const parent = internalCall(appendAfter, \"parentNode\");\n      const insertedNode = internalCall(parent, \"insertBefore\")(newChild, appendAfter.nextSibling);\n      dispatchSlotChangeEvent(slotNode);\n      updateFallbackSlotVisibility(this);\n      return insertedNode;\n    }\n    return this.__appendChild(newChild);\n  };\n};\nvar patchSlotRemoveChild = (ElementPrototype) => {\n  ElementPrototype.__removeChild = ElementPrototype.removeChild;\n  ElementPrototype.removeChild = function(toRemove) {\n    if (toRemove && typeof toRemove[\"s-sn\"] !== \"undefined\") {\n      const childNodes = this.__childNodes || this.childNodes;\n      const slotNode = getHostSlotNodes(childNodes, this.tagName, toRemove[\"s-sn\"]);\n      if (slotNode && toRemove.isConnected) {\n        toRemove.remove();\n        updateFallbackSlotVisibility(this);\n        return;\n      }\n    }\n    return this.__removeChild(toRemove);\n  };\n};\nvar patchSlotPrepend = (HostElementPrototype) => {\n  HostElementPrototype.__prepend = HostElementPrototype.prepend;\n  HostElementPrototype.prepend = function(...newChildren) {\n    newChildren.forEach((newChild) => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      const slotName = (newChild[\"s-sn\"] = getSlotName(newChild)) || \"\";\n      const childNodes = internalCall(this, \"childNodes\");\n      const slotNode = getHostSlotNodes(childNodes, this.tagName, slotName)[0];\n      if (slotNode) {\n        addSlotRelocateNode(newChild, slotNode, true);\n        const slotChildNodes = getSlotChildSiblings(slotNode, slotName);\n        const appendAfter = slotChildNodes[0];\n        const parent = internalCall(appendAfter, \"parentNode\");\n        const toReturn = internalCall(parent, \"insertBefore\")(newChild, internalCall(appendAfter, \"nextSibling\"));\n        dispatchSlotChangeEvent(slotNode);\n        return toReturn;\n      }\n      if (newChild.nodeType === 1 && !!newChild.getAttribute(\"slot\")) {\n        newChild.hidden = true;\n      }\n      return HostElementPrototype.__prepend(newChild);\n    });\n  };\n};\nvar patchSlotAppend = (HostElementPrototype) => {\n  HostElementPrototype.__append = HostElementPrototype.append;\n  HostElementPrototype.append = function(...newChildren) {\n    newChildren.forEach((newChild) => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      this.appendChild(newChild);\n    });\n  };\n};\nvar patchSlotInsertAdjacentHTML = (HostElementPrototype) => {\n  const originalInsertAdjacentHtml = HostElementPrototype.insertAdjacentHTML;\n  HostElementPrototype.insertAdjacentHTML = function(position, text) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentHtml.call(this, position, text);\n    }\n    const container = this.ownerDocument.createElement(\"_\");\n    let node;\n    container.innerHTML = text;\n    if (position === \"afterbegin\") {\n      while (node = container.firstChild) {\n        this.prepend(node);\n      }\n    } else if (position === \"beforeend\") {\n      while (node = container.firstChild) {\n        this.append(node);\n      }\n    }\n  };\n};\nvar patchSlotInsertAdjacentText = (HostElementPrototype) => {\n  HostElementPrototype.insertAdjacentText = function(position, text) {\n    this.insertAdjacentHTML(position, text);\n  };\n};\nvar patchInsertBefore = (HostElementPrototype) => {\n  const eleProto = HostElementPrototype;\n  if (eleProto.__insertBefore) return;\n  eleProto.__insertBefore = HostElementPrototype.insertBefore;\n  HostElementPrototype.insertBefore = function(newChild, currentChild) {\n    const { slotName, slotNode } = findSlotFromSlottedNode(newChild, this);\n    const slottedNodes = this.__childNodes ? this.childNodes : getSlottedChildNodes(this.childNodes);\n    if (slotNode) {\n      let found = false;\n      slottedNodes.forEach((childNode) => {\n        if (childNode === currentChild || currentChild === null) {\n          found = true;\n          if (currentChild === null || slotName !== currentChild[\"s-sn\"]) {\n            this.appendChild(newChild);\n            return;\n          }\n          if (slotName === currentChild[\"s-sn\"]) {\n            addSlotRelocateNode(newChild, slotNode);\n            const parent = internalCall(currentChild, \"parentNode\");\n            internalCall(parent, \"insertBefore\")(newChild, currentChild);\n            dispatchSlotChangeEvent(slotNode);\n          }\n          return;\n        }\n      });\n      if (found) return newChild;\n    }\n    const parentNode = currentChild == null ? void 0 : currentChild.__parentNode;\n    if (parentNode && !this.isSameNode(parentNode)) {\n      return this.appendChild(newChild);\n    }\n    return this.__insertBefore(newChild, currentChild);\n  };\n};\nvar patchSlotInsertAdjacentElement = (HostElementPrototype) => {\n  const originalInsertAdjacentElement = HostElementPrototype.insertAdjacentElement;\n  HostElementPrototype.insertAdjacentElement = function(position, element) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentElement.call(this, position, element);\n    }\n    if (position === \"afterbegin\") {\n      this.prepend(element);\n      return element;\n    } else if (position === \"beforeend\") {\n      this.append(element);\n      return element;\n    }\n    return element;\n  };\n};\nvar patchTextContent = (hostElementPrototype) => {\n  patchHostOriginalAccessor(\"textContent\", hostElementPrototype);\n  Object.defineProperty(hostElementPrototype, \"textContent\", {\n    get: function() {\n      let text = \"\";\n      const childNodes = this.__childNodes ? this.childNodes : getSlottedChildNodes(this.childNodes);\n      childNodes.forEach((node) => text += node.textContent || \"\");\n      return text;\n    },\n    set: function(value) {\n      const childNodes = this.__childNodes ? this.childNodes : getSlottedChildNodes(this.childNodes);\n      childNodes.forEach((node) => {\n        if (node[\"s-ol\"]) node[\"s-ol\"].remove();\n        node.remove();\n      });\n      this.insertAdjacentHTML(\"beforeend\", value);\n    }\n  });\n};\nvar patchChildSlotNodes = (elm) => {\n  class FakeNodeList extends Array {\n    item(n) {\n      return this[n];\n    }\n  }\n  patchHostOriginalAccessor(\"children\", elm);\n  Object.defineProperty(elm, \"children\", {\n    get() {\n      return this.childNodes.filter((n) => n.nodeType === 1);\n    }\n  });\n  Object.defineProperty(elm, \"childElementCount\", {\n    get() {\n      return this.children.length;\n    }\n  });\n  patchHostOriginalAccessor(\"firstChild\", elm);\n  Object.defineProperty(elm, \"firstChild\", {\n    get() {\n      return this.childNodes[0];\n    }\n  });\n  patchHostOriginalAccessor(\"lastChild\", elm);\n  Object.defineProperty(elm, \"lastChild\", {\n    get() {\n      return this.childNodes[this.childNodes.length - 1];\n    }\n  });\n  patchHostOriginalAccessor(\"childNodes\", elm);\n  Object.defineProperty(elm, \"childNodes\", {\n    get() {\n      const result = new FakeNodeList();\n      result.push(...getSlottedChildNodes(this.__childNodes));\n      return result;\n    }\n  });\n};\nvar patchSlottedNode = (node) => {\n  if (!node || node.__nextSibling !== void 0 || !globalThis.Node) return;\n  patchNextSibling(node);\n  patchPreviousSibling(node);\n  patchParentNode(node);\n  if (node.nodeType === Node.ELEMENT_NODE) {\n    patchNextElementSibling(node);\n    patchPreviousElementSibling(node);\n  }\n};\nvar patchNextSibling = (node) => {\n  if (!node || node.__nextSibling) return;\n  patchHostOriginalAccessor(\"nextSibling\", node);\n  Object.defineProperty(node, \"nextSibling\", {\n    get: function() {\n      var _a;\n      const parentNodes = (_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode.childNodes;\n      const index = parentNodes == null ? void 0 : parentNodes.indexOf(this);\n      if (parentNodes && index > -1) {\n        return parentNodes[index + 1];\n      }\n      return this.__nextSibling;\n    }\n  });\n};\nvar patchNextElementSibling = (element) => {\n  if (!element || element.__nextElementSibling) return;\n  patchHostOriginalAccessor(\"nextElementSibling\", element);\n  Object.defineProperty(element, \"nextElementSibling\", {\n    get: function() {\n      var _a;\n      const parentEles = (_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode.children;\n      const index = parentEles == null ? void 0 : parentEles.indexOf(this);\n      if (parentEles && index > -1) {\n        return parentEles[index + 1];\n      }\n      return this.__nextElementSibling;\n    }\n  });\n};\nvar patchPreviousSibling = (node) => {\n  if (!node || node.__previousSibling) return;\n  patchHostOriginalAccessor(\"previousSibling\", node);\n  Object.defineProperty(node, \"previousSibling\", {\n    get: function() {\n      var _a;\n      const parentNodes = (_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode.childNodes;\n      const index = parentNodes == null ? void 0 : parentNodes.indexOf(this);\n      if (parentNodes && index > -1) {\n        return parentNodes[index - 1];\n      }\n      return this.__previousSibling;\n    }\n  });\n};\nvar patchPreviousElementSibling = (element) => {\n  if (!element || element.__previousElementSibling) return;\n  patchHostOriginalAccessor(\"previousElementSibling\", element);\n  Object.defineProperty(element, \"previousElementSibling\", {\n    get: function() {\n      var _a;\n      const parentNodes = (_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode.children;\n      const index = parentNodes == null ? void 0 : parentNodes.indexOf(this);\n      if (parentNodes && index > -1) {\n        return parentNodes[index - 1];\n      }\n      return this.__previousElementSibling;\n    }\n  });\n};\nvar patchParentNode = (node) => {\n  if (!node || node.__parentNode) return;\n  patchHostOriginalAccessor(\"parentNode\", node);\n  Object.defineProperty(node, \"parentNode\", {\n    get: function() {\n      var _a;\n      return ((_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode) || this.__parentNode;\n    },\n    set: function(value) {\n      this.__parentNode = value;\n    }\n  });\n};\nvar validElementPatches = [\"children\", \"nextElementSibling\", \"previousElementSibling\"];\nvar validNodesPatches = [\n  \"childNodes\",\n  \"firstChild\",\n  \"lastChild\",\n  \"nextSibling\",\n  \"previousSibling\",\n  \"textContent\",\n  \"parentNode\"\n];\nfunction patchHostOriginalAccessor(accessorName, node) {\n  let accessor;\n  if (validElementPatches.includes(accessorName)) {\n    accessor = Object.getOwnPropertyDescriptor(Element.prototype, accessorName);\n  } else if (validNodesPatches.includes(accessorName)) {\n    accessor = Object.getOwnPropertyDescriptor(Node.prototype, accessorName);\n  }\n  if (!accessor) {\n    accessor = Object.getOwnPropertyDescriptor(node, accessorName);\n  }\n  if (accessor) Object.defineProperty(node, \"__\" + accessorName, accessor);\n}\nfunction internalCall(node, method) {\n  if (\"__\" + method in node) {\n    const toReturn = node[\"__\" + method];\n    if (typeof toReturn !== \"function\") return toReturn;\n    return toReturn.bind(node);\n  } else {\n    if (typeof node[method] !== \"function\") return node[method];\n    return node[method].bind(node);\n  }\n}\nvar createTime = (fnName, tagName = \"\") => {\n  {\n    return () => {\n      return;\n    };\n  }\n};\nvar uniqueTime = (key, measureText) => {\n  {\n    return () => {\n      return;\n    };\n  }\n};\nvar h = (nodeName, vnodeData, ...children) => {\n  let child = null;\n  let key = null;\n  let slotName = null;\n  let simple = false;\n  let lastSimple = false;\n  const vNodeChildren = [];\n  const walk = (c) => {\n    for (let i2 = 0; i2 < c.length; i2++) {\n      child = c[i2];\n      if (Array.isArray(child)) {\n        walk(child);\n      } else if (child != null && typeof child !== \"boolean\") {\n        if (simple = typeof nodeName !== \"function\" && !isComplexType(child)) {\n          child = String(child);\n        }\n        if (simple && lastSimple) {\n          vNodeChildren[vNodeChildren.length - 1].$text$ += child;\n        } else {\n          vNodeChildren.push(simple ? newVNode(null, child) : child);\n        }\n        lastSimple = simple;\n      }\n    }\n  };\n  walk(children);\n  if (vnodeData) {\n    if (vnodeData.key) {\n      key = vnodeData.key;\n    }\n    if (vnodeData.name) {\n      slotName = vnodeData.name;\n    }\n    {\n      const classData = vnodeData.className || vnodeData.class;\n      if (classData) {\n        vnodeData.class = typeof classData !== \"object\" ? classData : Object.keys(classData).filter((k) => classData[k]).join(\" \");\n      }\n    }\n  }\n  if (typeof nodeName === \"function\") {\n    return nodeName(\n      vnodeData === null ? {} : vnodeData,\n      vNodeChildren,\n      vdomFnUtils\n    );\n  }\n  const vnode = newVNode(nodeName, null);\n  vnode.$attrs$ = vnodeData;\n  if (vNodeChildren.length > 0) {\n    vnode.$children$ = vNodeChildren;\n  }\n  {\n    vnode.$key$ = key;\n  }\n  {\n    vnode.$name$ = slotName;\n  }\n  return vnode;\n};\nvar newVNode = (tag, text) => {\n  const vnode = {\n    $flags$: 0,\n    $tag$: tag,\n    $text$: text,\n    $elm$: null,\n    $children$: null\n  };\n  {\n    vnode.$attrs$ = null;\n  }\n  {\n    vnode.$key$ = null;\n  }\n  {\n    vnode.$name$ = null;\n  }\n  return vnode;\n};\nvar Host = {};\nvar isHost = (node) => node && node.$tag$ === Host;\nvar vdomFnUtils = {\n  forEach: (children, cb) => children.map(convertToPublic).forEach(cb),\n  map: (children, cb) => children.map(convertToPublic).map(cb).map(convertToPrivate)\n};\nvar convertToPublic = (node) => ({\n  vattrs: node.$attrs$,\n  vchildren: node.$children$,\n  vkey: node.$key$,\n  vname: node.$name$,\n  vtag: node.$tag$,\n  vtext: node.$text$\n});\nvar convertToPrivate = (node) => {\n  if (typeof node.vtag === \"function\") {\n    const vnodeData = { ...node.vattrs };\n    if (node.vkey) {\n      vnodeData.key = node.vkey;\n    }\n    if (node.vname) {\n      vnodeData.name = node.vname;\n    }\n    return h(node.vtag, vnodeData, ...node.vchildren || []);\n  }\n  const vnode = newVNode(node.vtag, node.vtext);\n  vnode.$attrs$ = node.vattrs;\n  vnode.$children$ = node.vchildren;\n  vnode.$key$ = node.vkey;\n  vnode.$name$ = node.vname;\n  return vnode;\n};\n\n// src/runtime/client-hydrate.ts\nvar initializeClientHydrate = (hostElm, tagName, hostId, hostRef) => {\n  var _a;\n  const endHydrate = createTime(\"hydrateClient\", tagName);\n  const shadowRoot = hostElm.shadowRoot;\n  const childRenderNodes = [];\n  const slotNodes = [];\n  const slottedNodes = [];\n  const shadowRootNodes = shadowRoot ? [] : null;\n  const vnode = newVNode(tagName, null);\n  vnode.$elm$ = hostElm;\n  const members = Object.entries(((_a = hostRef.$cmpMeta$) == null ? void 0 : _a.$members$) || {});\n  members.forEach(([memberName, [memberFlags, metaAttributeName]]) => {\n    var _a2;\n    if (!(memberFlags & 31 /* Prop */)) {\n      return;\n    }\n    const attributeName = metaAttributeName || memberName;\n    const attrVal = hostElm.getAttribute(attributeName);\n    if (attrVal !== null) {\n      const attrPropVal = parsePropertyValue(attrVal, memberFlags);\n      (_a2 = hostRef == null ? void 0 : hostRef.$instanceValues$) == null ? void 0 : _a2.set(memberName, attrPropVal);\n    }\n  });\n  let scopeId2;\n  {\n    const cmpMeta = hostRef.$cmpMeta$;\n    if (cmpMeta && cmpMeta.$flags$ & 10 /* needsScopedEncapsulation */ && hostElm[\"s-sc\"]) {\n      scopeId2 = hostElm[\"s-sc\"];\n      hostElm.classList.add(scopeId2 + \"-h\");\n    } else if (hostElm[\"s-sc\"]) {\n      delete hostElm[\"s-sc\"];\n    }\n  }\n  if (win.document && (!plt.$orgLocNodes$ || !plt.$orgLocNodes$.size)) {\n    initializeDocumentHydrate(win.document.body, plt.$orgLocNodes$ = /* @__PURE__ */ new Map());\n  }\n  hostElm[HYDRATE_ID] = hostId;\n  hostElm.removeAttribute(HYDRATE_ID);\n  hostRef.$vnode$ = clientHydrate(\n    vnode,\n    childRenderNodes,\n    slotNodes,\n    shadowRootNodes,\n    hostElm,\n    hostElm,\n    hostId,\n    slottedNodes\n  );\n  let crIndex = 0;\n  const crLength = childRenderNodes.length;\n  let childRenderNode;\n  for (crIndex; crIndex < crLength; crIndex++) {\n    childRenderNode = childRenderNodes[crIndex];\n    const orgLocationId = childRenderNode.$hostId$ + \".\" + childRenderNode.$nodeId$;\n    const orgLocationNode = plt.$orgLocNodes$.get(orgLocationId);\n    const node = childRenderNode.$elm$;\n    if (!shadowRoot) {\n      node[\"s-hn\"] = tagName.toUpperCase();\n      if (childRenderNode.$tag$ === \"slot\") {\n        node[\"s-cr\"] = hostElm[\"s-cr\"];\n      }\n    }\n    if (childRenderNode.$tag$ === \"slot\") {\n      childRenderNode.$name$ = childRenderNode.$elm$[\"s-sn\"] || childRenderNode.$elm$[\"name\"] || null;\n      if (childRenderNode.$children$) {\n        childRenderNode.$flags$ |= 2 /* isSlotFallback */;\n        if (!childRenderNode.$elm$.childNodes.length) {\n          childRenderNode.$children$.forEach((c) => {\n            childRenderNode.$elm$.appendChild(c.$elm$);\n          });\n        }\n      } else {\n        childRenderNode.$flags$ |= 1 /* isSlotReference */;\n      }\n    }\n    if (orgLocationNode && orgLocationNode.isConnected) {\n      if (shadowRoot && orgLocationNode[\"s-en\"] === \"\") {\n        orgLocationNode.parentNode.insertBefore(node, orgLocationNode.nextSibling);\n      }\n      orgLocationNode.parentNode.removeChild(orgLocationNode);\n      if (!shadowRoot) {\n        node[\"s-oo\"] = parseInt(childRenderNode.$nodeId$);\n      }\n    }\n    plt.$orgLocNodes$.delete(orgLocationId);\n  }\n  const hosts = [];\n  const snLen = slottedNodes.length;\n  let snIndex = 0;\n  let slotGroup;\n  let snGroupIdx;\n  let snGroupLen;\n  let slottedItem;\n  for (snIndex; snIndex < snLen; snIndex++) {\n    slotGroup = slottedNodes[snIndex];\n    if (!slotGroup || !slotGroup.length) continue;\n    snGroupLen = slotGroup.length;\n    snGroupIdx = 0;\n    for (snGroupIdx; snGroupIdx < snGroupLen; snGroupIdx++) {\n      slottedItem = slotGroup[snGroupIdx];\n      if (!hosts[slottedItem.hostId]) {\n        hosts[slottedItem.hostId] = plt.$orgLocNodes$.get(slottedItem.hostId);\n      }\n      if (!hosts[slottedItem.hostId]) continue;\n      const hostEle = hosts[slottedItem.hostId];\n      if (!hostEle.shadowRoot || !shadowRoot) {\n        slottedItem.slot[\"s-cr\"] = hostEle[\"s-cr\"];\n        if (!slottedItem.slot[\"s-cr\"] && hostEle.shadowRoot) {\n          slottedItem.slot[\"s-cr\"] = hostEle;\n        } else {\n          slottedItem.slot[\"s-cr\"] = (hostEle.__childNodes || hostEle.childNodes)[0];\n        }\n        addSlotRelocateNode(slottedItem.node, slottedItem.slot, false, slottedItem.node[\"s-oo\"]);\n        {\n          patchSlottedNode(slottedItem.node);\n        }\n      }\n      if (hostEle.shadowRoot && slottedItem.node.parentElement !== hostEle) {\n        hostEle.appendChild(slottedItem.node);\n      }\n    }\n  }\n  if (scopeId2 && slotNodes.length) {\n    slotNodes.forEach((slot) => {\n      slot.$elm$.parentElement.classList.add(scopeId2 + \"-s\");\n    });\n  }\n  if (shadowRoot && !shadowRoot.childNodes.length) {\n    let rnIdex = 0;\n    const rnLen = shadowRootNodes.length;\n    if (rnLen) {\n      for (rnIdex; rnIdex < rnLen; rnIdex++) {\n        shadowRoot.appendChild(shadowRootNodes[rnIdex]);\n      }\n      Array.from(hostElm.childNodes).forEach((node) => {\n        if (typeof node[\"s-sn\"] !== \"string\") {\n          if (node.nodeType === 1 /* ElementNode */ && node.slot && node.hidden) {\n            node.removeAttribute(\"hidden\");\n          } else if (node.nodeType === 8 /* CommentNode */ || node.nodeType === 3 /* TextNode */ && !node.wholeText.trim()) {\n            node.parentNode.removeChild(node);\n          }\n        }\n      });\n    }\n  }\n  plt.$orgLocNodes$.delete(hostElm[\"s-id\"]);\n  hostRef.$hostElement$ = hostElm;\n  endHydrate();\n};\nvar clientHydrate = (parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node, hostId, slottedNodes = []) => {\n  let childNodeType;\n  let childIdSplt;\n  let childVNode;\n  let i2;\n  const scopeId2 = hostElm[\"s-sc\"];\n  if (node.nodeType === 1 /* ElementNode */) {\n    childNodeType = node.getAttribute(HYDRATE_CHILD_ID);\n    if (childNodeType) {\n      childIdSplt = childNodeType.split(\".\");\n      if (childIdSplt[0] === hostId || childIdSplt[0] === \"0\") {\n        childVNode = createSimpleVNode({\n          $flags$: 0,\n          $hostId$: childIdSplt[0],\n          $nodeId$: childIdSplt[1],\n          $depth$: childIdSplt[2],\n          $index$: childIdSplt[3],\n          $tag$: node.tagName.toLowerCase(),\n          $elm$: node,\n          // If we don't add the initial classes to the VNode, the first `vdom-render.ts` patch\n          // won't try to reconcile them. Classes set on the node will be blown away.\n          $attrs$: { class: node.className || \"\" }\n        });\n        childRenderNodes.push(childVNode);\n        node.removeAttribute(HYDRATE_CHILD_ID);\n        if (!parentVNode.$children$) {\n          parentVNode.$children$ = [];\n        }\n        if (scopeId2) {\n          node[\"s-si\"] = scopeId2;\n          childVNode.$attrs$.class += \" \" + scopeId2;\n        }\n        const slotName = childVNode.$elm$.getAttribute(\"s-sn\");\n        if (typeof slotName === \"string\") {\n          if (childVNode.$tag$ === \"slot-fb\") {\n            addSlot(\n              slotName,\n              childIdSplt[2],\n              childVNode,\n              node,\n              parentVNode,\n              childRenderNodes,\n              slotNodes,\n              shadowRootNodes,\n              slottedNodes\n            );\n            if (scopeId2) {\n              node.classList.add(scopeId2);\n            }\n          }\n          childVNode.$elm$[\"s-sn\"] = slotName;\n          childVNode.$elm$.removeAttribute(\"s-sn\");\n        }\n        if (childVNode.$index$ !== void 0) {\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n        }\n        parentVNode = childVNode;\n        if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n          shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n        }\n      }\n    }\n    if (node.shadowRoot) {\n      for (i2 = node.shadowRoot.childNodes.length - 1; i2 >= 0; i2--) {\n        clientHydrate(\n          parentVNode,\n          childRenderNodes,\n          slotNodes,\n          shadowRootNodes,\n          hostElm,\n          node.shadowRoot.childNodes[i2],\n          hostId,\n          slottedNodes\n        );\n      }\n    }\n    const nonShadowNodes = node.__childNodes || node.childNodes;\n    for (i2 = nonShadowNodes.length - 1; i2 >= 0; i2--) {\n      clientHydrate(\n        parentVNode,\n        childRenderNodes,\n        slotNodes,\n        shadowRootNodes,\n        hostElm,\n        nonShadowNodes[i2],\n        hostId,\n        slottedNodes\n      );\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[1] === hostId || childIdSplt[1] === \"0\") {\n      childNodeType = childIdSplt[0];\n      childVNode = createSimpleVNode({\n        $hostId$: childIdSplt[1],\n        $nodeId$: childIdSplt[2],\n        $depth$: childIdSplt[3],\n        $index$: childIdSplt[4] || \"0\",\n        $elm$: node,\n        $attrs$: null,\n        $children$: null,\n        $key$: null,\n        $name$: null,\n        $tag$: null,\n        $text$: null\n      });\n      if (childNodeType === TEXT_NODE_ID) {\n        childVNode.$elm$ = findCorrespondingNode(node, 3 /* TextNode */);\n        if (childVNode.$elm$ && childVNode.$elm$.nodeType === 3 /* TextNode */) {\n          childVNode.$text$ = childVNode.$elm$.textContent;\n          childRenderNodes.push(childVNode);\n          node.remove();\n          if (hostId === childVNode.$hostId$) {\n            if (!parentVNode.$children$) {\n              parentVNode.$children$ = [];\n            }\n            parentVNode.$children$[childVNode.$index$] = childVNode;\n          }\n          if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n            shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n          }\n        }\n      } else if (childNodeType === COMMENT_NODE_ID) {\n        childVNode.$elm$ = findCorrespondingNode(node, 8 /* CommentNode */);\n        if (childVNode.$elm$ && childVNode.$elm$.nodeType === 8 /* CommentNode */) {\n          childRenderNodes.push(childVNode);\n          node.remove();\n        }\n      } else if (childVNode.$hostId$ === hostId) {\n        if (childNodeType === SLOT_NODE_ID) {\n          const slotName = node[\"s-sn\"] = childIdSplt[5] || \"\";\n          addSlot(\n            slotName,\n            childIdSplt[2],\n            childVNode,\n            node,\n            parentVNode,\n            childRenderNodes,\n            slotNodes,\n            shadowRootNodes,\n            slottedNodes\n          );\n        } else if (childNodeType === CONTENT_REF_ID) {\n          if (shadowRootNodes) {\n            node.remove();\n          } else {\n            hostElm[\"s-cr\"] = node;\n            node[\"s-cn\"] = true;\n          }\n        }\n      }\n    }\n  } else if (parentVNode && parentVNode.$tag$ === \"style\") {\n    const vnode = newVNode(null, node.textContent);\n    vnode.$elm$ = node;\n    vnode.$index$ = \"0\";\n    parentVNode.$children$ = [vnode];\n  } else {\n    if (node.nodeType === 3 /* TextNode */ && !node.wholeText.trim()) {\n      node.remove();\n    }\n  }\n  return parentVNode;\n};\nvar initializeDocumentHydrate = (node, orgLocNodes) => {\n  if (node.nodeType === 1 /* ElementNode */) {\n    const componentId = node[HYDRATE_ID] || node.getAttribute(HYDRATE_ID);\n    if (componentId) {\n      orgLocNodes.set(componentId, node);\n    }\n    let i2 = 0;\n    if (node.shadowRoot) {\n      for (; i2 < node.shadowRoot.childNodes.length; i2++) {\n        initializeDocumentHydrate(node.shadowRoot.childNodes[i2], orgLocNodes);\n      }\n    }\n    const nonShadowNodes = node.__childNodes || node.childNodes;\n    for (i2 = 0; i2 < nonShadowNodes.length; i2++) {\n      initializeDocumentHydrate(nonShadowNodes[i2], orgLocNodes);\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    const childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[0] === ORG_LOCATION_ID) {\n      orgLocNodes.set(childIdSplt[1] + \".\" + childIdSplt[2], node);\n      node.nodeValue = \"\";\n      node[\"s-en\"] = childIdSplt[3];\n    }\n  }\n};\nvar createSimpleVNode = (vnode) => {\n  const defaultVNode = {\n    $flags$: 0,\n    $hostId$: null,\n    $nodeId$: null,\n    $depth$: null,\n    $index$: \"0\",\n    $elm$: null,\n    $attrs$: null,\n    $children$: null,\n    $key$: null,\n    $name$: null,\n    $tag$: null,\n    $text$: null\n  };\n  return { ...defaultVNode, ...vnode };\n};\nfunction addSlot(slotName, slotId, childVNode, node, parentVNode, childRenderNodes, slotNodes, shadowRootNodes, slottedNodes) {\n  node[\"s-sr\"] = true;\n  childVNode.$name$ = slotName || null;\n  childVNode.$tag$ = \"slot\";\n  const parentNodeId = (parentVNode == null ? void 0 : parentVNode.$elm$) ? parentVNode.$elm$[\"s-id\"] || parentVNode.$elm$.getAttribute(\"s-id\") : \"\";\n  if (shadowRootNodes && win.document) {\n    const slot = childVNode.$elm$ = win.document.createElement(childVNode.$tag$);\n    if (childVNode.$name$) {\n      childVNode.$elm$.setAttribute(\"name\", slotName);\n    }\n    if (parentNodeId && parentNodeId !== childVNode.$hostId$) {\n      parentVNode.$elm$.insertBefore(slot, parentVNode.$elm$.children[0]);\n    } else {\n      node.parentNode.insertBefore(childVNode.$elm$, node);\n    }\n    addSlottedNodes(slottedNodes, slotId, slotName, node, childVNode.$hostId$);\n    node.remove();\n    if (childVNode.$depth$ === \"0\") {\n      shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n    }\n  } else {\n    const slot = childVNode.$elm$;\n    const shouldMove = parentNodeId && parentNodeId !== childVNode.$hostId$ && parentVNode.$elm$.shadowRoot;\n    addSlottedNodes(slottedNodes, slotId, slotName, node, shouldMove ? parentNodeId : childVNode.$hostId$);\n    patchSlotNode(node);\n    if (shouldMove) {\n      parentVNode.$elm$.insertBefore(slot, parentVNode.$elm$.children[0]);\n    }\n    childRenderNodes.push(childVNode);\n  }\n  slotNodes.push(childVNode);\n  if (!parentVNode.$children$) {\n    parentVNode.$children$ = [];\n  }\n  parentVNode.$children$[childVNode.$index$] = childVNode;\n}\nvar addSlottedNodes = (slottedNodes, slotNodeId, slotName, slotNode, hostId) => {\n  let slottedNode = slotNode.nextSibling;\n  slottedNodes[slotNodeId] = slottedNodes[slotNodeId] || [];\n  while (slottedNode && ((slottedNode[\"getAttribute\"] && slottedNode.getAttribute(\"slot\") || slottedNode[\"s-sn\"]) === slotName || slotName === \"\" && !slottedNode[\"s-sn\"] && (slottedNode.nodeType === 8 /* CommentNode */ && slottedNode.nodeValue.indexOf(\".\") !== 1 || slottedNode.nodeType === 3 /* TextNode */))) {\n    slottedNode[\"s-sn\"] = slotName;\n    slottedNodes[slotNodeId].push({ slot: slotNode, node: slottedNode, hostId });\n    slottedNode = slottedNode.nextSibling;\n  }\n};\nvar findCorrespondingNode = (node, type) => {\n  let sibling = node;\n  do {\n    sibling = sibling.nextSibling;\n  } while (sibling && (sibling.nodeType !== type || !sibling.nodeValue));\n  return sibling;\n};\nvar createSupportsRuleRe = (selector) => {\n  const safeSelector2 = escapeRegExpSpecialCharacters(selector);\n  return new RegExp(\n    // First capture group: match any context before the selector that's not inside @supports selector()\n    // Using negative lookahead to avoid matching inside @supports selector(...) condition\n    `(^|[^@]|@(?!supports\\\\s+selector\\\\s*\\\\([^{]*?${safeSelector2}))(${safeSelector2}\\\\b)`,\n    \"g\"\n  );\n};\ncreateSupportsRuleRe(\"::slotted\");\ncreateSupportsRuleRe(\":host\");\ncreateSupportsRuleRe(\":host-context\");\n\n// src/runtime/mode.ts\nvar computeMode = (elm) => modeResolutionChain.map((h2) => h2(elm)).find((m) => !!m);\nvar setMode = (handler) => modeResolutionChain.push(handler);\nvar getMode = (ref) => getHostRef(ref).$modeName$;\nvar parsePropertyValue = (propValue, propType) => {\n  if (typeof propValue === \"string\" && (propValue.startsWith(\"{\") && propValue.endsWith(\"}\") || propValue.startsWith(\"[\") && propValue.endsWith(\"]\"))) {\n    try {\n      propValue = JSON.parse(propValue);\n      return propValue;\n    } catch (e) {\n    }\n  }\n  if (typeof propValue === \"string\" && propValue.startsWith(SERIALIZED_PREFIX)) {\n    propValue = deserializeProperty(propValue);\n    return propValue;\n  }\n  if (propValue != null && !isComplexType(propValue)) {\n    if (propType & 4 /* Boolean */) {\n      return propValue === \"false\" ? false : propValue === \"\" || !!propValue;\n    }\n    if (propType & 2 /* Number */) {\n      return typeof propValue === \"string\" ? parseFloat(propValue) : typeof propValue === \"number\" ? propValue : NaN;\n    }\n    if (propType & 1 /* String */) {\n      return String(propValue);\n    }\n    return propValue;\n  }\n  return propValue;\n};\nvar getElement = (ref) => getHostRef(ref).$hostElement$ ;\n\n// src/runtime/event-emitter.ts\nvar createEvent = (ref, name, flags) => {\n  const elm = getElement(ref);\n  return {\n    emit: (detail) => {\n      return emitEvent(elm, name, {\n        bubbles: !!(flags & 4 /* Bubbles */),\n        composed: !!(flags & 2 /* Composed */),\n        cancelable: !!(flags & 1 /* Cancellable */),\n        detail\n      });\n    }\n  };\n};\nvar emitEvent = (elm, name, opts) => {\n  const ev = plt.ce(name, opts);\n  elm.dispatchEvent(ev);\n  return ev;\n};\nvar rootAppliedStyles = /* @__PURE__ */ new WeakMap();\nvar registerStyle = (scopeId2, cssText, allowCS) => {\n  let style = styles.get(scopeId2);\n  if (supportsConstructableStylesheets && allowCS) {\n    style = style || new CSSStyleSheet();\n    if (typeof style === \"string\") {\n      style = cssText;\n    } else {\n      style.replaceSync(cssText);\n    }\n  } else {\n    style = cssText;\n  }\n  styles.set(scopeId2, style);\n};\nvar addStyle = (styleContainerNode, cmpMeta, mode) => {\n  var _a;\n  const scopeId2 = getScopeId(cmpMeta, mode);\n  const style = styles.get(scopeId2);\n  if (!win.document) {\n    return scopeId2;\n  }\n  styleContainerNode = styleContainerNode.nodeType === 11 /* DocumentFragment */ ? styleContainerNode : win.document;\n  if (style) {\n    if (typeof style === \"string\") {\n      styleContainerNode = styleContainerNode.head || styleContainerNode;\n      let appliedStyles = rootAppliedStyles.get(styleContainerNode);\n      let styleElm;\n      if (!appliedStyles) {\n        rootAppliedStyles.set(styleContainerNode, appliedStyles = /* @__PURE__ */ new Set());\n      }\n      if (!appliedStyles.has(scopeId2)) {\n        if (styleContainerNode.host && (styleElm = styleContainerNode.querySelector(`[${HYDRATED_STYLE_ID}=\"${scopeId2}\"]`))) {\n          styleElm.innerHTML = style;\n        } else {\n          styleElm = document.querySelector(`[${HYDRATED_STYLE_ID}=\"${scopeId2}\"]`) || win.document.createElement(\"style\");\n          styleElm.innerHTML = style;\n          const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(win.document);\n          if (nonce != null) {\n            styleElm.setAttribute(\"nonce\", nonce);\n          }\n          if (!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */)) {\n            if (styleContainerNode.nodeName === \"HEAD\") {\n              const preconnectLinks = styleContainerNode.querySelectorAll(\"link[rel=preconnect]\");\n              const referenceNode2 = preconnectLinks.length > 0 ? preconnectLinks[preconnectLinks.length - 1].nextSibling : styleContainerNode.querySelector(\"style\");\n              styleContainerNode.insertBefore(\n                styleElm,\n                (referenceNode2 == null ? void 0 : referenceNode2.parentNode) === styleContainerNode ? referenceNode2 : null\n              );\n            } else if (\"host\" in styleContainerNode) {\n              if (supportsConstructableStylesheets) {\n                const stylesheet = new CSSStyleSheet();\n                stylesheet.replaceSync(style);\n                styleContainerNode.adoptedStyleSheets = [stylesheet, ...styleContainerNode.adoptedStyleSheets];\n              } else {\n                const existingStyleContainer = styleContainerNode.querySelector(\"style\");\n                if (existingStyleContainer) {\n                  existingStyleContainer.innerHTML = style + existingStyleContainer.innerHTML;\n                } else {\n                  styleContainerNode.prepend(styleElm);\n                }\n              }\n            } else {\n              styleContainerNode.append(styleElm);\n            }\n          }\n          if (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            styleContainerNode.insertBefore(styleElm, null);\n          }\n        }\n        if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n          styleElm.innerHTML += SLOT_FB_CSS;\n        }\n        if (appliedStyles) {\n          appliedStyles.add(scopeId2);\n        }\n      }\n    } else if (!styleContainerNode.adoptedStyleSheets.includes(style)) {\n      styleContainerNode.adoptedStyleSheets = [...styleContainerNode.adoptedStyleSheets, style];\n    }\n  }\n  return scopeId2;\n};\nvar attachStyles = (hostRef) => {\n  const cmpMeta = hostRef.$cmpMeta$;\n  const elm = hostRef.$hostElement$;\n  const flags = cmpMeta.$flags$;\n  const endAttachStyles = createTime(\"attachStyles\", cmpMeta.$tagName$);\n  const scopeId2 = addStyle(\n    elm.shadowRoot ? elm.shadowRoot : elm.getRootNode(),\n    cmpMeta,\n    hostRef.$modeName$\n  );\n  if (flags & 10 /* needsScopedEncapsulation */) {\n    elm[\"s-sc\"] = scopeId2;\n    elm.classList.add(scopeId2 + \"-h\");\n  }\n  endAttachStyles();\n};\nvar getScopeId = (cmp, mode) => \"sc-\" + (mode && cmp.$flags$ & 32 /* hasMode */ ? cmp.$tagName$ + \"-\" + mode : cmp.$tagName$);\nvar convertScopedToShadow = (css) => css.replace(/\\/\\*!@([^\\/]+)\\*\\/[^\\{]+\\{/g, \"$1{\");\nvar hydrateScopedToShadow = () => {\n  if (!win.document) {\n    return;\n  }\n  const styles2 = win.document.querySelectorAll(`[${HYDRATED_STYLE_ID}]`);\n  let i2 = 0;\n  for (; i2 < styles2.length; i2++) {\n    registerStyle(styles2[i2].getAttribute(HYDRATED_STYLE_ID), convertScopedToShadow(styles2[i2].innerHTML), true);\n  }\n};\nvar setAccessor = (elm, memberName, oldValue, newValue, isSvg, flags, initialRender) => {\n  if (oldValue === newValue) {\n    return;\n  }\n  let isProp = isMemberInElement(elm, memberName);\n  let ln = memberName.toLowerCase();\n  if (memberName === \"class\") {\n    const classList = elm.classList;\n    const oldClasses = parseClassList(oldValue);\n    let newClasses = parseClassList(newValue);\n    if (elm[\"s-si\"] && initialRender) {\n      newClasses.push(elm[\"s-si\"]);\n      oldClasses.forEach((c) => {\n        if (c.startsWith(elm[\"s-si\"])) newClasses.push(c);\n      });\n      newClasses = [...new Set(newClasses)];\n      classList.add(...newClasses);\n    } else {\n      classList.remove(...oldClasses.filter((c) => c && !newClasses.includes(c)));\n      classList.add(...newClasses.filter((c) => c && !oldClasses.includes(c)));\n    }\n  } else if (memberName === \"style\") {\n    {\n      for (const prop in oldValue) {\n        if (!newValue || newValue[prop] == null) {\n          if (prop.includes(\"-\")) {\n            elm.style.removeProperty(prop);\n          } else {\n            elm.style[prop] = \"\";\n          }\n        }\n      }\n    }\n    for (const prop in newValue) {\n      if (!oldValue || newValue[prop] !== oldValue[prop]) {\n        if (prop.includes(\"-\")) {\n          elm.style.setProperty(prop, newValue[prop]);\n        } else {\n          elm.style[prop] = newValue[prop];\n        }\n      }\n    }\n  } else if (memberName === \"key\") ; else if (memberName === \"ref\") {\n    if (newValue) {\n      newValue(elm);\n    }\n  } else if ((!isProp ) && memberName[0] === \"o\" && memberName[1] === \"n\") {\n    if (memberName[2] === \"-\") {\n      memberName = memberName.slice(3);\n    } else if (isMemberInElement(win, ln)) {\n      memberName = ln.slice(2);\n    } else {\n      memberName = ln[2] + memberName.slice(3);\n    }\n    if (oldValue || newValue) {\n      const capture = memberName.endsWith(CAPTURE_EVENT_SUFFIX);\n      memberName = memberName.replace(CAPTURE_EVENT_REGEX, \"\");\n      if (oldValue) {\n        plt.rel(elm, memberName, oldValue, capture);\n      }\n      if (newValue) {\n        plt.ael(elm, memberName, newValue, capture);\n      }\n    }\n  } else {\n    const isComplex = isComplexType(newValue);\n    if ((isProp || isComplex && newValue !== null) && !isSvg) {\n      try {\n        if (!elm.tagName.includes(\"-\")) {\n          const n = newValue == null ? \"\" : newValue;\n          if (memberName === \"list\") {\n            isProp = false;\n          } else if (oldValue == null || elm[memberName] != n) {\n            if (typeof elm.__lookupSetter__(memberName) === \"function\") {\n              elm[memberName] = n;\n            } else {\n              elm.setAttribute(memberName, n);\n            }\n          }\n        } else if (elm[memberName] !== newValue) {\n          elm[memberName] = newValue;\n        }\n      } catch (e) {\n      }\n    }\n    let xlink = false;\n    {\n      if (ln !== (ln = ln.replace(/^xlink\\:?/, \"\"))) {\n        memberName = ln;\n        xlink = true;\n      }\n    }\n    if (newValue == null || newValue === false) {\n      if (newValue !== false || elm.getAttribute(memberName) === \"\") {\n        if (xlink) {\n          elm.removeAttributeNS(XLINK_NS, memberName);\n        } else {\n          elm.removeAttribute(memberName);\n        }\n      }\n    } else if ((!isProp || flags & 4 /* isHost */ || isSvg) && !isComplex && elm.nodeType === 1 /* ElementNode */) {\n      newValue = newValue === true ? \"\" : newValue;\n      if (xlink) {\n        elm.setAttributeNS(XLINK_NS, memberName, newValue);\n      } else {\n        elm.setAttribute(memberName, newValue);\n      }\n    }\n  }\n};\nvar parseClassListRegex = /\\s/;\nvar parseClassList = (value) => {\n  if (typeof value === \"object\" && value && \"baseVal\" in value) {\n    value = value.baseVal;\n  }\n  if (!value || typeof value !== \"string\") {\n    return [];\n  }\n  return value.split(parseClassListRegex);\n};\nvar CAPTURE_EVENT_SUFFIX = \"Capture\";\nvar CAPTURE_EVENT_REGEX = new RegExp(CAPTURE_EVENT_SUFFIX + \"$\");\n\n// src/runtime/vdom/update-element.ts\nvar updateElement = (oldVnode, newVnode, isSvgMode2, isInitialRender) => {\n  const elm = newVnode.$elm$.nodeType === 11 /* DocumentFragment */ && newVnode.$elm$.host ? newVnode.$elm$.host : newVnode.$elm$;\n  const oldVnodeAttrs = oldVnode && oldVnode.$attrs$ || {};\n  const newVnodeAttrs = newVnode.$attrs$ || {};\n  {\n    for (const memberName of sortedAttrNames(Object.keys(oldVnodeAttrs))) {\n      if (!(memberName in newVnodeAttrs)) {\n        setAccessor(\n          elm,\n          memberName,\n          oldVnodeAttrs[memberName],\n          void 0,\n          isSvgMode2,\n          newVnode.$flags$,\n          isInitialRender\n        );\n      }\n    }\n  }\n  for (const memberName of sortedAttrNames(Object.keys(newVnodeAttrs))) {\n    setAccessor(\n      elm,\n      memberName,\n      oldVnodeAttrs[memberName],\n      newVnodeAttrs[memberName],\n      isSvgMode2,\n      newVnode.$flags$,\n      isInitialRender\n    );\n  }\n};\nfunction sortedAttrNames(attrNames) {\n  return attrNames.includes(\"ref\") ? (\n    // we need to sort these to ensure that `'ref'` is the last attr\n    [...attrNames.filter((attr) => attr !== \"ref\"), \"ref\"]\n  ) : (\n    // no need to sort, return the original array\n    attrNames\n  );\n}\n\n// src/runtime/vdom/vdom-render.ts\nvar scopeId;\nvar contentRef;\nvar hostTagName;\nvar useNativeShadowDom = false;\nvar checkSlotFallbackVisibility = false;\nvar checkSlotRelocate = false;\nvar isSvgMode = false;\nvar createElm = (oldParentVNode, newParentVNode, childIndex) => {\n  var _a;\n  const newVNode2 = newParentVNode.$children$[childIndex];\n  let i2 = 0;\n  let elm;\n  let childNode;\n  let oldVNode;\n  if (!useNativeShadowDom) {\n    checkSlotRelocate = true;\n    if (newVNode2.$tag$ === \"slot\") {\n      newVNode2.$flags$ |= newVNode2.$children$ ? (\n        // slot element has fallback content\n        // still create an element that \"mocks\" the slot element\n        2 /* isSlotFallback */\n      ) : (\n        // slot element does not have fallback content\n        // create an html comment we'll use to always reference\n        // where actual slot content should sit next to\n        1 /* isSlotReference */\n      );\n    }\n  }\n  if (newVNode2.$text$ !== null) {\n    elm = newVNode2.$elm$ = win.document.createTextNode(newVNode2.$text$);\n  } else if (newVNode2.$flags$ & 1 /* isSlotReference */) {\n    elm = newVNode2.$elm$ = win.document.createTextNode(\"\");\n    {\n      updateElement(null, newVNode2, isSvgMode);\n    }\n  } else {\n    if (!isSvgMode) {\n      isSvgMode = newVNode2.$tag$ === \"svg\";\n    }\n    if (!win.document) {\n      throw new Error(\n        \"You are trying to render a Stencil component in an environment that doesn't support the DOM. Make sure to populate the [`window`](https://developer.mozilla.org/en-US/docs/Web/API/Window/window) object before rendering a component.\"\n      );\n    }\n    elm = newVNode2.$elm$ = win.document.createElementNS(\n      isSvgMode ? SVG_NS : HTML_NS,\n      !useNativeShadowDom && BUILD.slotRelocation && newVNode2.$flags$ & 2 /* isSlotFallback */ ? \"slot-fb\" : newVNode2.$tag$\n    ) ;\n    if (isSvgMode && newVNode2.$tag$ === \"foreignObject\") {\n      isSvgMode = false;\n    }\n    {\n      updateElement(null, newVNode2, isSvgMode);\n    }\n    if (isDef(scopeId) && elm[\"s-si\"] !== scopeId) {\n      elm.classList.add(elm[\"s-si\"] = scopeId);\n    }\n    if (newVNode2.$children$) {\n      for (i2 = 0; i2 < newVNode2.$children$.length; ++i2) {\n        childNode = createElm(oldParentVNode, newVNode2, i2);\n        if (childNode) {\n          elm.appendChild(childNode);\n        }\n      }\n    }\n    {\n      if (newVNode2.$tag$ === \"svg\") {\n        isSvgMode = false;\n      } else if (elm.tagName === \"foreignObject\") {\n        isSvgMode = true;\n      }\n    }\n  }\n  elm[\"s-hn\"] = hostTagName;\n  {\n    if (newVNode2.$flags$ & (2 /* isSlotFallback */ | 1 /* isSlotReference */)) {\n      elm[\"s-sr\"] = true;\n      elm[\"s-cr\"] = contentRef;\n      elm[\"s-sn\"] = newVNode2.$name$ || \"\";\n      elm[\"s-rf\"] = (_a = newVNode2.$attrs$) == null ? void 0 : _a.ref;\n      patchSlotNode(elm);\n      oldVNode = oldParentVNode && oldParentVNode.$children$ && oldParentVNode.$children$[childIndex];\n      if (oldVNode && oldVNode.$tag$ === newVNode2.$tag$ && oldParentVNode.$elm$) {\n        {\n          relocateToHostRoot(oldParentVNode.$elm$);\n        }\n      }\n      {\n        addRemoveSlotScopedClass(contentRef, elm, newParentVNode.$elm$, oldParentVNode == null ? void 0 : oldParentVNode.$elm$);\n      }\n    }\n  }\n  return elm;\n};\nvar relocateToHostRoot = (parentElm) => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const host = parentElm.closest(hostTagName.toLowerCase());\n  if (host != null) {\n    const contentRefNode = Array.from(host.__childNodes || host.childNodes).find(\n      (ref) => ref[\"s-cr\"]\n    );\n    const childNodeArray = Array.from(\n      parentElm.__childNodes || parentElm.childNodes\n    );\n    for (const childNode of contentRefNode ? childNodeArray.reverse() : childNodeArray) {\n      if (childNode[\"s-sh\"] != null) {\n        insertBefore(host, childNode, contentRefNode != null ? contentRefNode : null);\n        childNode[\"s-sh\"] = void 0;\n        checkSlotRelocate = true;\n      }\n    }\n  }\n  plt.$flags$ &= -2 /* isTmpDisconnected */;\n};\nvar putBackInOriginalLocation = (parentElm, recursive) => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const oldSlotChildNodes = Array.from(parentElm.__childNodes || parentElm.childNodes);\n  if (parentElm[\"s-sr\"] && BUILD.experimentalSlotFixes) {\n    let node = parentElm;\n    while (node = node.nextSibling) {\n      if (node && node[\"s-sn\"] === parentElm[\"s-sn\"] && node[\"s-sh\"] === hostTagName) {\n        oldSlotChildNodes.push(node);\n      }\n    }\n  }\n  for (let i2 = oldSlotChildNodes.length - 1; i2 >= 0; i2--) {\n    const childNode = oldSlotChildNodes[i2];\n    if (childNode[\"s-hn\"] !== hostTagName && childNode[\"s-ol\"]) {\n      insertBefore(referenceNode(childNode).parentNode, childNode, referenceNode(childNode));\n      childNode[\"s-ol\"].remove();\n      childNode[\"s-ol\"] = void 0;\n      childNode[\"s-sh\"] = void 0;\n      checkSlotRelocate = true;\n    }\n    if (recursive) {\n      putBackInOriginalLocation(childNode, recursive);\n    }\n  }\n  plt.$flags$ &= -2 /* isTmpDisconnected */;\n};\nvar addVnodes = (parentElm, before, parentVNode, vnodes, startIdx, endIdx) => {\n  let containerElm = parentElm[\"s-cr\"] && parentElm[\"s-cr\"].parentNode || parentElm;\n  let childNode;\n  if (containerElm.shadowRoot && containerElm.tagName === hostTagName) {\n    containerElm = containerElm.shadowRoot;\n  }\n  for (; startIdx <= endIdx; ++startIdx) {\n    if (vnodes[startIdx]) {\n      childNode = createElm(null, parentVNode, startIdx);\n      if (childNode) {\n        vnodes[startIdx].$elm$ = childNode;\n        insertBefore(containerElm, childNode, referenceNode(before) );\n      }\n    }\n  }\n};\nvar removeVnodes = (vnodes, startIdx, endIdx) => {\n  for (let index = startIdx; index <= endIdx; ++index) {\n    const vnode = vnodes[index];\n    if (vnode) {\n      const elm = vnode.$elm$;\n      nullifyVNodeRefs(vnode);\n      if (elm) {\n        {\n          checkSlotFallbackVisibility = true;\n          if (elm[\"s-ol\"]) {\n            elm[\"s-ol\"].remove();\n          } else {\n            putBackInOriginalLocation(elm, true);\n          }\n        }\n        elm.remove();\n      }\n    }\n  }\n};\nvar updateChildren = (parentElm, oldCh, newVNode2, newCh, isInitialRender = false) => {\n  let oldStartIdx = 0;\n  let newStartIdx = 0;\n  let idxInOld = 0;\n  let i2 = 0;\n  let oldEndIdx = oldCh.length - 1;\n  let oldStartVnode = oldCh[0];\n  let oldEndVnode = oldCh[oldEndIdx];\n  let newEndIdx = newCh.length - 1;\n  let newStartVnode = newCh[0];\n  let newEndVnode = newCh[newEndIdx];\n  let node;\n  let elmToMove;\n  while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n    if (oldStartVnode == null) {\n      oldStartVnode = oldCh[++oldStartIdx];\n    } else if (oldEndVnode == null) {\n      oldEndVnode = oldCh[--oldEndIdx];\n    } else if (newStartVnode == null) {\n      newStartVnode = newCh[++newStartIdx];\n    } else if (newEndVnode == null) {\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newStartVnode, isInitialRender)) {\n      patch(oldStartVnode, newStartVnode, isInitialRender);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else if (isSameVnode(oldEndVnode, newEndVnode, isInitialRender)) {\n      patch(oldEndVnode, newEndVnode, isInitialRender);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newEndVnode, isInitialRender)) {\n      if ((oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\")) {\n        putBackInOriginalLocation(oldStartVnode.$elm$.parentNode, false);\n      }\n      patch(oldStartVnode, newEndVnode, isInitialRender);\n      insertBefore(parentElm, oldStartVnode.$elm$, oldEndVnode.$elm$.nextSibling);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldEndVnode, newStartVnode, isInitialRender)) {\n      if ((oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\")) {\n        putBackInOriginalLocation(oldEndVnode.$elm$.parentNode, false);\n      }\n      patch(oldEndVnode, newStartVnode, isInitialRender);\n      insertBefore(parentElm, oldEndVnode.$elm$, oldStartVnode.$elm$);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else {\n      idxInOld = -1;\n      {\n        for (i2 = oldStartIdx; i2 <= oldEndIdx; ++i2) {\n          if (oldCh[i2] && oldCh[i2].$key$ !== null && oldCh[i2].$key$ === newStartVnode.$key$) {\n            idxInOld = i2;\n            break;\n          }\n        }\n      }\n      if (idxInOld >= 0) {\n        elmToMove = oldCh[idxInOld];\n        if (elmToMove.$tag$ !== newStartVnode.$tag$) {\n          node = createElm(oldCh && oldCh[newStartIdx], newVNode2, idxInOld);\n        } else {\n          patch(elmToMove, newStartVnode, isInitialRender);\n          oldCh[idxInOld] = void 0;\n          node = elmToMove.$elm$;\n        }\n        newStartVnode = newCh[++newStartIdx];\n      } else {\n        node = createElm(oldCh && oldCh[newStartIdx], newVNode2, newStartIdx);\n        newStartVnode = newCh[++newStartIdx];\n      }\n      if (node) {\n        {\n          insertBefore(\n            referenceNode(oldStartVnode.$elm$).parentNode,\n            node,\n            referenceNode(oldStartVnode.$elm$)\n          );\n        }\n      }\n    }\n  }\n  if (oldStartIdx > oldEndIdx) {\n    addVnodes(\n      parentElm,\n      newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].$elm$,\n      newVNode2,\n      newCh,\n      newStartIdx,\n      newEndIdx\n    );\n  } else if (newStartIdx > newEndIdx) {\n    removeVnodes(oldCh, oldStartIdx, oldEndIdx);\n  }\n};\nvar isSameVnode = (leftVNode, rightVNode, isInitialRender = false) => {\n  if (leftVNode.$tag$ === rightVNode.$tag$) {\n    if (leftVNode.$tag$ === \"slot\") {\n      return leftVNode.$name$ === rightVNode.$name$;\n    }\n    if (!isInitialRender) {\n      return leftVNode.$key$ === rightVNode.$key$;\n    }\n    if (isInitialRender && !leftVNode.$key$ && rightVNode.$key$) {\n      leftVNode.$key$ = rightVNode.$key$;\n    }\n    return true;\n  }\n  return false;\n};\nvar referenceNode = (node) => node && node[\"s-ol\"] || node;\nvar patch = (oldVNode, newVNode2, isInitialRender = false) => {\n  const elm = newVNode2.$elm$ = oldVNode.$elm$;\n  const oldChildren = oldVNode.$children$;\n  const newChildren = newVNode2.$children$;\n  const tag = newVNode2.$tag$;\n  const text = newVNode2.$text$;\n  let defaultHolder;\n  if (text === null) {\n    {\n      isSvgMode = tag === \"svg\" ? true : tag === \"foreignObject\" ? false : isSvgMode;\n    }\n    {\n      if (tag === \"slot\" && !useNativeShadowDom) {\n        if (oldVNode.$name$ !== newVNode2.$name$) {\n          newVNode2.$elm$[\"s-sn\"] = newVNode2.$name$ || \"\";\n          relocateToHostRoot(newVNode2.$elm$.parentElement);\n        }\n      }\n      updateElement(oldVNode, newVNode2, isSvgMode, isInitialRender);\n    }\n    if (oldChildren !== null && newChildren !== null) {\n      updateChildren(elm, oldChildren, newVNode2, newChildren, isInitialRender);\n    } else if (newChildren !== null) {\n      if (oldVNode.$text$ !== null) {\n        elm.textContent = \"\";\n      }\n      addVnodes(elm, null, newVNode2, newChildren, 0, newChildren.length - 1);\n    } else if (\n      // don't do this on initial render as it can cause non-hydrated content to be removed\n      !isInitialRender && BUILD.updatable && oldChildren !== null\n    ) {\n      removeVnodes(oldChildren, 0, oldChildren.length - 1);\n    }\n    if (isSvgMode && tag === \"svg\") {\n      isSvgMode = false;\n    }\n  } else if ((defaultHolder = elm[\"s-cr\"])) {\n    defaultHolder.parentNode.textContent = text;\n  } else if (oldVNode.$text$ !== text) {\n    elm.data = text;\n  }\n};\nvar relocateNodes = [];\nvar markSlotContentForRelocation = (elm) => {\n  let node;\n  let hostContentNodes;\n  let j;\n  const children = elm.__childNodes || elm.childNodes;\n  for (const childNode of children) {\n    if (childNode[\"s-sr\"] && (node = childNode[\"s-cr\"]) && node.parentNode) {\n      hostContentNodes = node.parentNode.__childNodes || node.parentNode.childNodes;\n      const slotName = childNode[\"s-sn\"];\n      for (j = hostContentNodes.length - 1; j >= 0; j--) {\n        node = hostContentNodes[j];\n        if (!node[\"s-cn\"] && !node[\"s-nr\"] && node[\"s-hn\"] !== childNode[\"s-hn\"] && (!node[\"s-sh\"] || node[\"s-sh\"] !== childNode[\"s-hn\"])) {\n          if (isNodeLocatedInSlot(node, slotName)) {\n            let relocateNodeData = relocateNodes.find((r) => r.$nodeToRelocate$ === node);\n            checkSlotFallbackVisibility = true;\n            node[\"s-sn\"] = node[\"s-sn\"] || slotName;\n            if (relocateNodeData) {\n              relocateNodeData.$nodeToRelocate$[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodeData.$slotRefNode$ = childNode;\n            } else {\n              node[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodes.push({\n                $slotRefNode$: childNode,\n                $nodeToRelocate$: node\n              });\n            }\n            if (node[\"s-sr\"]) {\n              relocateNodes.map((relocateNode) => {\n                if (isNodeLocatedInSlot(relocateNode.$nodeToRelocate$, node[\"s-sn\"])) {\n                  relocateNodeData = relocateNodes.find((r) => r.$nodeToRelocate$ === node);\n                  if (relocateNodeData && !relocateNode.$slotRefNode$) {\n                    relocateNode.$slotRefNode$ = relocateNodeData.$slotRefNode$;\n                  }\n                }\n              });\n            }\n          } else if (!relocateNodes.some((r) => r.$nodeToRelocate$ === node)) {\n            relocateNodes.push({\n              $nodeToRelocate$: node\n            });\n          }\n        }\n      }\n    }\n    if (childNode.nodeType === 1 /* ElementNode */) {\n      markSlotContentForRelocation(childNode);\n    }\n  }\n};\nvar nullifyVNodeRefs = (vNode) => {\n  {\n    vNode.$attrs$ && vNode.$attrs$.ref && vNode.$attrs$.ref(null);\n    vNode.$children$ && vNode.$children$.map(nullifyVNodeRefs);\n  }\n};\nvar insertBefore = (parent, newNode, reference) => {\n  if (typeof newNode[\"s-sn\"] === \"string\" && !!newNode[\"s-sr\"] && !!newNode[\"s-cr\"]) {\n    addRemoveSlotScopedClass(newNode[\"s-cr\"], newNode, parent, newNode.parentElement);\n  } else if (typeof newNode[\"s-sn\"] === \"string\") {\n    if (parent.getRootNode().nodeType !== 11 /* DOCUMENT_FRAGMENT_NODE */) {\n      patchParentNode(newNode);\n    }\n    parent.insertBefore(newNode, reference);\n    const { slotNode } = findSlotFromSlottedNode(newNode);\n    if (slotNode) dispatchSlotChangeEvent(slotNode);\n    return newNode;\n  }\n  if (parent.__insertBefore) {\n    return parent.__insertBefore(newNode, reference);\n  } else {\n    return parent == null ? void 0 : parent.insertBefore(newNode, reference);\n  }\n};\nfunction addRemoveSlotScopedClass(reference, slotNode, newParent, oldParent) {\n  var _a, _b;\n  let scopeId2;\n  if (reference && typeof slotNode[\"s-sn\"] === \"string\" && !!slotNode[\"s-sr\"] && reference.parentNode && reference.parentNode[\"s-sc\"] && (scopeId2 = slotNode[\"s-si\"] || reference.parentNode[\"s-sc\"])) {\n    const scopeName = slotNode[\"s-sn\"];\n    const hostName = slotNode[\"s-hn\"];\n    (_a = newParent.classList) == null ? void 0 : _a.add(scopeId2 + \"-s\");\n    if (oldParent && ((_b = oldParent.classList) == null ? void 0 : _b.contains(scopeId2 + \"-s\"))) {\n      let child = (oldParent.__childNodes || oldParent.childNodes)[0];\n      let found = false;\n      while (child) {\n        if (child[\"s-sn\"] !== scopeName && child[\"s-hn\"] === hostName && !!child[\"s-sr\"]) {\n          found = true;\n          break;\n        }\n        child = child.nextSibling;\n      }\n      if (!found) oldParent.classList.remove(scopeId2 + \"-s\");\n    }\n  }\n}\nvar renderVdom = (hostRef, renderFnResults, isInitialLoad = false) => {\n  var _a, _b, _c, _d, _e;\n  const hostElm = hostRef.$hostElement$;\n  const cmpMeta = hostRef.$cmpMeta$;\n  const oldVNode = hostRef.$vnode$ || newVNode(null, null);\n  const isHostElement = isHost(renderFnResults);\n  const rootVnode = isHostElement ? renderFnResults : h(null, null, renderFnResults);\n  hostTagName = hostElm.tagName;\n  if (cmpMeta.$attrsToReflect$) {\n    rootVnode.$attrs$ = rootVnode.$attrs$ || {};\n    cmpMeta.$attrsToReflect$.map(\n      ([propName, attribute]) => rootVnode.$attrs$[attribute] = hostElm[propName]\n    );\n  }\n  if (isInitialLoad && rootVnode.$attrs$) {\n    for (const key of Object.keys(rootVnode.$attrs$)) {\n      if (hostElm.hasAttribute(key) && ![\"key\", \"ref\", \"style\", \"class\"].includes(key)) {\n        rootVnode.$attrs$[key] = hostElm[key];\n      }\n    }\n  }\n  rootVnode.$tag$ = null;\n  rootVnode.$flags$ |= 4 /* isHost */;\n  hostRef.$vnode$ = rootVnode;\n  rootVnode.$elm$ = oldVNode.$elm$ = hostElm.shadowRoot || hostElm ;\n  {\n    scopeId = hostElm[\"s-sc\"];\n  }\n  useNativeShadowDom = !!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) && !(cmpMeta.$flags$ & 128 /* shadowNeedsScopedCss */);\n  {\n    contentRef = hostElm[\"s-cr\"];\n    checkSlotFallbackVisibility = false;\n  }\n  patch(oldVNode, rootVnode, isInitialLoad);\n  {\n    plt.$flags$ |= 1 /* isTmpDisconnected */;\n    if (checkSlotRelocate) {\n      markSlotContentForRelocation(rootVnode.$elm$);\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        if (!nodeToRelocate[\"s-ol\"] && win.document) {\n          const orgLocationNode = win.document.createTextNode(\"\");\n          orgLocationNode[\"s-nr\"] = nodeToRelocate;\n          insertBefore(nodeToRelocate.parentNode, nodeToRelocate[\"s-ol\"] = orgLocationNode, nodeToRelocate);\n        }\n      }\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        const slotRefNode = relocateData.$slotRefNode$;\n        if (slotRefNode) {\n          const parentNodeRef = slotRefNode.parentNode;\n          let insertBeforeNode = slotRefNode.nextSibling;\n          if ((insertBeforeNode && insertBeforeNode.nodeType === 1 /* ElementNode */)) {\n            let orgLocationNode = (_a = nodeToRelocate[\"s-ol\"]) == null ? void 0 : _a.previousSibling;\n            while (orgLocationNode) {\n              let refNode = (_b = orgLocationNode[\"s-nr\"]) != null ? _b : null;\n              if (refNode && refNode[\"s-sn\"] === nodeToRelocate[\"s-sn\"] && parentNodeRef === (refNode.__parentNode || refNode.parentNode)) {\n                refNode = refNode.nextSibling;\n                while (refNode === nodeToRelocate || (refNode == null ? void 0 : refNode[\"s-sr\"])) {\n                  refNode = refNode == null ? void 0 : refNode.nextSibling;\n                }\n                if (!refNode || !refNode[\"s-nr\"]) {\n                  insertBeforeNode = refNode;\n                  break;\n                }\n              }\n              orgLocationNode = orgLocationNode.previousSibling;\n            }\n          }\n          const parent = nodeToRelocate.__parentNode || nodeToRelocate.parentNode;\n          const nextSibling = nodeToRelocate.__nextSibling || nodeToRelocate.nextSibling;\n          if (!insertBeforeNode && parentNodeRef !== parent || nextSibling !== insertBeforeNode) {\n            if (nodeToRelocate !== insertBeforeNode) {\n              insertBefore(parentNodeRef, nodeToRelocate, insertBeforeNode);\n              if (nodeToRelocate.nodeType === 1 /* ElementNode */ && nodeToRelocate.tagName !== \"SLOT-FB\") {\n                nodeToRelocate.hidden = (_c = nodeToRelocate[\"s-ih\"]) != null ? _c : false;\n              }\n            }\n          }\n          nodeToRelocate && typeof slotRefNode[\"s-rf\"] === \"function\" && slotRefNode[\"s-rf\"](slotRefNode);\n        } else {\n          if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n            if (isInitialLoad) {\n              nodeToRelocate[\"s-ih\"] = (_d = nodeToRelocate.hidden) != null ? _d : false;\n            }\n            nodeToRelocate.hidden = true;\n          }\n        }\n      }\n    }\n    if (checkSlotFallbackVisibility) {\n      updateFallbackSlotVisibility(rootVnode.$elm$);\n    }\n    plt.$flags$ &= -2 /* isTmpDisconnected */;\n    relocateNodes.length = 0;\n  }\n  if (cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n    const children = rootVnode.$elm$.__childNodes || rootVnode.$elm$.childNodes;\n    for (const childNode of children) {\n      if (childNode[\"s-hn\"] !== hostTagName && !childNode[\"s-sh\"]) {\n        if (isInitialLoad && childNode[\"s-ih\"] == null) {\n          childNode[\"s-ih\"] = (_e = childNode.hidden) != null ? _e : false;\n        }\n        childNode.hidden = true;\n      }\n    }\n  }\n  contentRef = void 0;\n};\n\n// src/runtime/update-component.ts\nvar attachToAncestor = (hostRef, ancestorComponent) => {\n  if (ancestorComponent && !hostRef.$onRenderResolve$ && ancestorComponent[\"s-p\"]) {\n    const index = ancestorComponent[\"s-p\"].push(\n      new Promise(\n        (r) => hostRef.$onRenderResolve$ = () => {\n          ancestorComponent[\"s-p\"].splice(index - 1, 1);\n          r();\n        }\n      )\n    );\n  }\n};\nvar scheduleUpdate = (hostRef, isInitialLoad) => {\n  {\n    hostRef.$flags$ |= 16 /* isQueuedForUpdate */;\n  }\n  if (hostRef.$flags$ & 4 /* isWaitingForChildren */) {\n    hostRef.$flags$ |= 512 /* needsRerender */;\n    return;\n  }\n  attachToAncestor(hostRef, hostRef.$ancestorComponent$);\n  const dispatch = () => dispatchHooks(hostRef, isInitialLoad);\n  return writeTask(dispatch) ;\n};\nvar dispatchHooks = (hostRef, isInitialLoad) => {\n  const elm = hostRef.$hostElement$;\n  const endSchedule = createTime(\"scheduleUpdate\", hostRef.$cmpMeta$.$tagName$);\n  const instance = hostRef.$lazyInstance$ ;\n  if (!instance) {\n    throw new Error(\n      `Can't render component <${elm.tagName.toLowerCase()} /> with invalid Stencil runtime! Make sure this imported component is compiled with a \\`externalRuntime: true\\` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime`\n    );\n  }\n  let maybePromise;\n  if (isInitialLoad) {\n    {\n      hostRef.$flags$ |= 256 /* isListenReady */;\n      if (hostRef.$queuedListeners$) {\n        hostRef.$queuedListeners$.map(([methodName, event]) => safeCall(instance, methodName, event, elm));\n        hostRef.$queuedListeners$ = void 0;\n      }\n    }\n    maybePromise = safeCall(instance, \"componentWillLoad\", void 0, elm);\n  } else {\n    maybePromise = safeCall(instance, \"componentWillUpdate\", void 0, elm);\n  }\n  maybePromise = enqueue(maybePromise, () => safeCall(instance, \"componentWillRender\", void 0, elm));\n  endSchedule();\n  return enqueue(maybePromise, () => updateComponent(hostRef, instance, isInitialLoad));\n};\nvar enqueue = (maybePromise, fn) => isPromisey(maybePromise) ? maybePromise.then(fn).catch((err2) => {\n  console.error(err2);\n  fn();\n}) : fn();\nvar isPromisey = (maybePromise) => maybePromise instanceof Promise || maybePromise && maybePromise.then && typeof maybePromise.then === \"function\";\nvar updateComponent = async (hostRef, instance, isInitialLoad) => {\n  var _a;\n  const elm = hostRef.$hostElement$;\n  const endUpdate = createTime(\"update\", hostRef.$cmpMeta$.$tagName$);\n  const rc = elm[\"s-rc\"];\n  if (isInitialLoad) {\n    attachStyles(hostRef);\n  }\n  const endRender = createTime(\"render\", hostRef.$cmpMeta$.$tagName$);\n  {\n    callRender(hostRef, instance, elm, isInitialLoad);\n  }\n  if (rc) {\n    rc.map((cb) => cb());\n    elm[\"s-rc\"] = void 0;\n  }\n  endRender();\n  endUpdate();\n  {\n    const childrenPromises = (_a = elm[\"s-p\"]) != null ? _a : [];\n    const postUpdate = () => postUpdateComponent(hostRef);\n    if (childrenPromises.length === 0) {\n      postUpdate();\n    } else {\n      Promise.all(childrenPromises).then(postUpdate);\n      hostRef.$flags$ |= 4 /* isWaitingForChildren */;\n      childrenPromises.length = 0;\n    }\n  }\n};\nvar callRender = (hostRef, instance, elm, isInitialLoad) => {\n  try {\n    instance = instance.render && instance.render();\n    {\n      hostRef.$flags$ &= -17 /* isQueuedForUpdate */;\n    }\n    {\n      hostRef.$flags$ |= 2 /* hasRendered */;\n    }\n    {\n      {\n        {\n          renderVdom(hostRef, instance, isInitialLoad);\n        }\n      }\n    }\n  } catch (e) {\n    consoleError(e, hostRef.$hostElement$);\n  }\n  return null;\n};\nvar postUpdateComponent = (hostRef) => {\n  const tagName = hostRef.$cmpMeta$.$tagName$;\n  const elm = hostRef.$hostElement$;\n  const endPostUpdate = createTime(\"postUpdate\", tagName);\n  const instance = hostRef.$lazyInstance$ ;\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  safeCall(instance, \"componentDidRender\", void 0, elm);\n  if (!(hostRef.$flags$ & 64 /* hasLoadedComponent */)) {\n    hostRef.$flags$ |= 64 /* hasLoadedComponent */;\n    {\n      addHydratedFlag(elm);\n    }\n    safeCall(instance, \"componentDidLoad\", void 0, elm);\n    endPostUpdate();\n    {\n      hostRef.$onReadyResolve$(elm);\n      if (!ancestorComponent) {\n        appDidLoad();\n      }\n    }\n  } else {\n    safeCall(instance, \"componentDidUpdate\", void 0, elm);\n    endPostUpdate();\n  }\n  {\n    hostRef.$onInstanceResolve$(elm);\n  }\n  {\n    if (hostRef.$onRenderResolve$) {\n      hostRef.$onRenderResolve$();\n      hostRef.$onRenderResolve$ = void 0;\n    }\n    if (hostRef.$flags$ & 512 /* needsRerender */) {\n      nextTick(() => scheduleUpdate(hostRef, false));\n    }\n    hostRef.$flags$ &= -517;\n  }\n};\nvar forceUpdate = (ref) => {\n  {\n    const hostRef = getHostRef(ref);\n    const isConnected = hostRef.$hostElement$.isConnected;\n    if (isConnected && (hostRef.$flags$ & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n      scheduleUpdate(hostRef, false);\n    }\n    return isConnected;\n  }\n};\nvar appDidLoad = (who) => {\n  nextTick(() => emitEvent(win, \"appload\", { detail: { namespace: NAMESPACE } }));\n};\nvar safeCall = (instance, method, arg, elm) => {\n  if (instance && instance[method]) {\n    try {\n      return instance[method](arg);\n    } catch (e) {\n      consoleError(e, elm);\n    }\n  }\n  return void 0;\n};\nvar addHydratedFlag = (elm) => {\n  var _a;\n  return elm.classList.add((_a = BUILD.hydratedSelectorName) != null ? _a : \"hydrated\") ;\n};\n\n// src/runtime/set-value.ts\nvar getValue = (ref, propName) => getHostRef(ref).$instanceValues$.get(propName);\nvar setValue = (ref, propName, newVal, cmpMeta) => {\n  const hostRef = getHostRef(ref);\n  if (!hostRef) {\n    throw new Error(\n      `Couldn't find host element for \"${cmpMeta.$tagName$}\" as it is unknown to this Stencil runtime. This usually happens when integrating a 3rd party Stencil component with another Stencil component or application. Please reach out to the maintainers of the 3rd party Stencil component or report this on the Stencil Discord server (https://chat.stenciljs.com) or comment on this similar [GitHub issue](https://github.com/stenciljs/core/issues/5457).`\n    );\n  }\n  const elm = hostRef.$hostElement$ ;\n  const oldVal = hostRef.$instanceValues$.get(propName);\n  const flags = hostRef.$flags$;\n  const instance = hostRef.$lazyInstance$ ;\n  newVal = parsePropertyValue(newVal, cmpMeta.$members$[propName][0]);\n  const areBothNaN = Number.isNaN(oldVal) && Number.isNaN(newVal);\n  const didValueChange = newVal !== oldVal && !areBothNaN;\n  if ((!(flags & 8 /* isConstructingInstance */) || oldVal === void 0) && didValueChange) {\n    hostRef.$instanceValues$.set(propName, newVal);\n    if (instance) {\n      if (cmpMeta.$watchers$ && flags & 128 /* isWatchReady */) {\n        const watchMethods = cmpMeta.$watchers$[propName];\n        if (watchMethods) {\n          watchMethods.map((watchMethodName) => {\n            try {\n              instance[watchMethodName](newVal, oldVal, propName);\n            } catch (e) {\n              consoleError(e, elm);\n            }\n          });\n        }\n      }\n      if ((flags & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n        if (instance.componentShouldUpdate) {\n          if (instance.componentShouldUpdate(newVal, oldVal, propName) === false) {\n            return;\n          }\n        }\n        scheduleUpdate(hostRef, false);\n      }\n    }\n  }\n};\n\n// src/runtime/proxy-component.ts\nvar proxyComponent = (Cstr, cmpMeta, flags) => {\n  var _a, _b;\n  const prototype = Cstr.prototype;\n  if (cmpMeta.$members$ || (cmpMeta.$watchers$ || Cstr.watchers)) {\n    if (Cstr.watchers && !cmpMeta.$watchers$) {\n      cmpMeta.$watchers$ = Cstr.watchers;\n    }\n    const members = Object.entries((_a = cmpMeta.$members$) != null ? _a : {});\n    members.map(([memberName, [memberFlags]]) => {\n      if ((memberFlags & 31 /* Prop */ || (flags & 2 /* proxyState */) && memberFlags & 32 /* State */)) {\n        const { get: origGetter, set: origSetter } = Object.getOwnPropertyDescriptor(prototype, memberName) || {};\n        if (origGetter) cmpMeta.$members$[memberName][0] |= 2048 /* Getter */;\n        if (origSetter) cmpMeta.$members$[memberName][0] |= 4096 /* Setter */;\n        if (flags & 1 /* isElementConstructor */ || !origGetter) {\n          Object.defineProperty(prototype, memberName, {\n            get() {\n              {\n                if ((cmpMeta.$members$[memberName][0] & 2048 /* Getter */) === 0) {\n                  return getValue(this, memberName);\n                }\n                const ref = getHostRef(this);\n                const instance = ref ? ref.$lazyInstance$ : prototype;\n                if (!instance) return;\n                return instance[memberName];\n              }\n            },\n            configurable: true,\n            enumerable: true\n          });\n        }\n        Object.defineProperty(prototype, memberName, {\n          set(newValue) {\n            const ref = getHostRef(this);\n            if (origSetter) {\n              const currentValue = memberFlags & 32 /* State */ ? this[memberName] : ref.$hostElement$[memberName];\n              if (typeof currentValue === \"undefined\" && ref.$instanceValues$.get(memberName)) {\n                newValue = ref.$instanceValues$.get(memberName);\n              } else if (!ref.$instanceValues$.get(memberName) && currentValue) {\n                ref.$instanceValues$.set(memberName, currentValue);\n              }\n              origSetter.apply(this, [parsePropertyValue(newValue, memberFlags)]);\n              newValue = memberFlags & 32 /* State */ ? this[memberName] : ref.$hostElement$[memberName];\n              setValue(this, memberName, newValue, cmpMeta);\n              return;\n            }\n            {\n              if ((flags & 1 /* isElementConstructor */) === 0 || (cmpMeta.$members$[memberName][0] & 4096 /* Setter */) === 0) {\n                setValue(this, memberName, newValue, cmpMeta);\n                if (flags & 1 /* isElementConstructor */ && !ref.$lazyInstance$) {\n                  ref.$onReadyPromise$.then(() => {\n                    if (cmpMeta.$members$[memberName][0] & 4096 /* Setter */ && ref.$lazyInstance$[memberName] !== ref.$instanceValues$.get(memberName)) {\n                      ref.$lazyInstance$[memberName] = newValue;\n                    }\n                  });\n                }\n                return;\n              }\n              const setterSetVal = () => {\n                const currentValue = ref.$lazyInstance$[memberName];\n                if (!ref.$instanceValues$.get(memberName) && currentValue) {\n                  ref.$instanceValues$.set(memberName, currentValue);\n                }\n                ref.$lazyInstance$[memberName] = parsePropertyValue(newValue, memberFlags);\n                setValue(this, memberName, ref.$lazyInstance$[memberName], cmpMeta);\n              };\n              if (ref.$lazyInstance$) {\n                setterSetVal();\n              } else {\n                ref.$onReadyPromise$.then(() => setterSetVal());\n              }\n            }\n          }\n        });\n      } else if (flags & 1 /* isElementConstructor */ && memberFlags & 64 /* Method */) {\n        Object.defineProperty(prototype, memberName, {\n          value(...args) {\n            var _a2;\n            const ref = getHostRef(this);\n            return (_a2 = ref == null ? void 0 : ref.$onInstancePromise$) == null ? void 0 : _a2.then(() => {\n              var _a3;\n              return (_a3 = ref.$lazyInstance$) == null ? void 0 : _a3[memberName](...args);\n            });\n          }\n        });\n      }\n    });\n    if ((flags & 1 /* isElementConstructor */)) {\n      const attrNameToPropName = /* @__PURE__ */ new Map();\n      prototype.attributeChangedCallback = function(attrName, oldValue, newValue) {\n        plt.jmp(() => {\n          var _a2;\n          const propName = attrNameToPropName.get(attrName);\n          if (this.hasOwnProperty(propName) && BUILD.lazyLoad) {\n            newValue = this[propName];\n            delete this[propName];\n          } else if (prototype.hasOwnProperty(propName) && typeof this[propName] === \"number\" && // cast type to number to avoid TS compiler issues\n          this[propName] == newValue) {\n            return;\n          } else if (propName == null) {\n            const hostRef = getHostRef(this);\n            const flags2 = hostRef == null ? void 0 : hostRef.$flags$;\n            if (flags2 && !(flags2 & 8 /* isConstructingInstance */) && flags2 & 128 /* isWatchReady */ && newValue !== oldValue) {\n              const instance = hostRef.$lazyInstance$ ;\n              const entry = (_a2 = cmpMeta.$watchers$) == null ? void 0 : _a2[attrName];\n              entry == null ? void 0 : entry.forEach((callbackName) => {\n                if (instance[callbackName] != null) {\n                  instance[callbackName].call(instance, newValue, oldValue, attrName);\n                }\n              });\n            }\n            return;\n          }\n          const propDesc = Object.getOwnPropertyDescriptor(prototype, propName);\n          newValue = newValue === null && typeof this[propName] === \"boolean\" ? false : newValue;\n          if (newValue !== this[propName] && (!propDesc.get || !!propDesc.set)) {\n            this[propName] = newValue;\n          }\n        });\n      };\n      Cstr.observedAttributes = Array.from(\n        /* @__PURE__ */ new Set([\n          ...Object.keys((_b = cmpMeta.$watchers$) != null ? _b : {}),\n          ...members.filter(([_, m]) => m[0] & 15 /* HasAttribute */).map(([propName, m]) => {\n            var _a2;\n            const attrName = m[1] || propName;\n            attrNameToPropName.set(attrName, propName);\n            if (m[0] & 512 /* ReflectAttr */) {\n              (_a2 = cmpMeta.$attrsToReflect$) == null ? void 0 : _a2.push([propName, attrName]);\n            }\n            return attrName;\n          })\n        ])\n      );\n    }\n  }\n  return Cstr;\n};\n\n// src/runtime/initialize-component.ts\nvar initializeComponent = async (elm, hostRef, cmpMeta, hmrVersionId) => {\n  let Cstr;\n  if ((hostRef.$flags$ & 32 /* hasInitializedComponent */) === 0) {\n    hostRef.$flags$ |= 32 /* hasInitializedComponent */;\n    const bundleId = cmpMeta.$lazyBundleId$;\n    if (bundleId) {\n      const CstrImport = loadModule(cmpMeta, hostRef);\n      if (CstrImport && \"then\" in CstrImport) {\n        const endLoad = uniqueTime();\n        Cstr = await CstrImport;\n        endLoad();\n      } else {\n        Cstr = CstrImport;\n      }\n      if (!Cstr) {\n        throw new Error(`Constructor for \"${cmpMeta.$tagName$}#${hostRef.$modeName$}\" was not found`);\n      }\n      if (!Cstr.isProxied) {\n        {\n          cmpMeta.$watchers$ = Cstr.watchers;\n        }\n        proxyComponent(Cstr, cmpMeta, 2 /* proxyState */);\n        Cstr.isProxied = true;\n      }\n      const endNewInstance = createTime(\"createInstance\", cmpMeta.$tagName$);\n      {\n        hostRef.$flags$ |= 8 /* isConstructingInstance */;\n      }\n      try {\n        new Cstr(hostRef);\n      } catch (e) {\n        consoleError(e, elm);\n      }\n      {\n        hostRef.$flags$ &= -9 /* isConstructingInstance */;\n      }\n      {\n        hostRef.$flags$ |= 128 /* isWatchReady */;\n      }\n      endNewInstance();\n      fireConnectedCallback(hostRef.$lazyInstance$, elm);\n    } else {\n      Cstr = elm.constructor;\n      const cmpTag = elm.localName;\n      customElements.whenDefined(cmpTag).then(() => hostRef.$flags$ |= 128 /* isWatchReady */);\n    }\n    if (Cstr && Cstr.style) {\n      let style;\n      if (typeof Cstr.style === \"string\") {\n        style = Cstr.style;\n      } else if (typeof Cstr.style !== \"string\") {\n        hostRef.$modeName$ = computeMode(elm);\n        if (hostRef.$modeName$) {\n          style = Cstr.style[hostRef.$modeName$];\n        }\n      }\n      const scopeId2 = getScopeId(cmpMeta, hostRef.$modeName$);\n      if (!styles.has(scopeId2)) {\n        const endRegisterStyles = createTime(\"registerStyles\", cmpMeta.$tagName$);\n        registerStyle(scopeId2, style, !!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */));\n        endRegisterStyles();\n      }\n    }\n  }\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  const schedule = () => scheduleUpdate(hostRef, true);\n  if (ancestorComponent && ancestorComponent[\"s-rc\"]) {\n    ancestorComponent[\"s-rc\"].push(schedule);\n  } else {\n    schedule();\n  }\n};\nvar fireConnectedCallback = (instance, elm) => {\n  {\n    safeCall(instance, \"connectedCallback\", void 0, elm);\n  }\n};\n\n// src/runtime/connected-callback.ts\nvar connectedCallback = (elm) => {\n  if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    const cmpMeta = hostRef.$cmpMeta$;\n    const endConnected = createTime(\"connectedCallback\", cmpMeta.$tagName$);\n    if (!(hostRef.$flags$ & 1 /* hasConnected */)) {\n      hostRef.$flags$ |= 1 /* hasConnected */;\n      let hostId;\n      {\n        hostId = elm.getAttribute(HYDRATE_ID);\n        if (hostId) {\n          if (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            const scopeId2 = addStyle(elm.shadowRoot, cmpMeta, elm.getAttribute(\"s-mode\")) ;\n            elm.classList.remove(scopeId2 + \"-h\", scopeId2 + \"-s\");\n          } else if (cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n            const scopeId2 = getScopeId(cmpMeta, elm.getAttribute(\"s-mode\") );\n            elm[\"s-sc\"] = scopeId2;\n          }\n          initializeClientHydrate(elm, cmpMeta.$tagName$, hostId, hostRef);\n        }\n      }\n      if (!hostId) {\n        if (// TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n        cmpMeta.$flags$ & (4 /* hasSlotRelocation */ | 8 /* needsShadowDomShim */)) {\n          setContentReference(elm);\n        }\n      }\n      {\n        let ancestorComponent = elm;\n        while (ancestorComponent = ancestorComponent.parentNode || ancestorComponent.host) {\n          if (ancestorComponent.nodeType === 1 /* ElementNode */ && ancestorComponent.hasAttribute(\"s-id\") && ancestorComponent[\"s-p\"] || ancestorComponent[\"s-p\"]) {\n            attachToAncestor(hostRef, hostRef.$ancestorComponent$ = ancestorComponent);\n            break;\n          }\n        }\n      }\n      if (cmpMeta.$members$) {\n        Object.entries(cmpMeta.$members$).map(([memberName, [memberFlags]]) => {\n          if (memberFlags & 31 /* Prop */ && elm.hasOwnProperty(memberName)) {\n            const value = elm[memberName];\n            delete elm[memberName];\n            elm[memberName] = value;\n          }\n        });\n      }\n      {\n        initializeComponent(elm, hostRef, cmpMeta);\n      }\n    } else {\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$);\n      if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n        fireConnectedCallback(hostRef.$lazyInstance$, elm);\n      } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => fireConnectedCallback(hostRef.$lazyInstance$, elm));\n      }\n    }\n    endConnected();\n  }\n};\nvar setContentReference = (elm) => {\n  if (!win.document) {\n    return;\n  }\n  const contentRefElm = elm[\"s-cr\"] = win.document.createComment(\n    \"\"\n  );\n  contentRefElm[\"s-cn\"] = true;\n  insertBefore(elm, contentRefElm, elm.firstChild);\n};\nvar disconnectInstance = (instance, elm) => {\n  {\n    safeCall(instance, \"disconnectedCallback\", void 0, elm || instance);\n  }\n};\nvar disconnectedCallback = async (elm) => {\n  if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    {\n      if (hostRef.$rmListeners$) {\n        hostRef.$rmListeners$.map((rmListener) => rmListener());\n        hostRef.$rmListeners$ = void 0;\n      }\n    }\n    if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n      disconnectInstance(hostRef.$lazyInstance$, elm);\n    } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n      hostRef.$onReadyPromise$.then(() => disconnectInstance(hostRef.$lazyInstance$, elm));\n    }\n  }\n  if (rootAppliedStyles.has(elm)) {\n    rootAppliedStyles.delete(elm);\n  }\n  if (elm.shadowRoot && rootAppliedStyles.has(elm.shadowRoot)) {\n    rootAppliedStyles.delete(elm.shadowRoot);\n  }\n};\n\n// src/runtime/bootstrap-lazy.ts\nvar bootstrapLazy = (lazyBundles, options = {}) => {\n  var _a;\n  if (!win.document) {\n    console.warn(\"Stencil: No document found. Skipping bootstrapping lazy components.\");\n    return;\n  }\n  const endBootstrap = createTime();\n  const cmpTags = [];\n  const exclude = options.exclude || [];\n  const customElements2 = win.customElements;\n  const head = win.document.head;\n  const metaCharset = /* @__PURE__ */ head.querySelector(\"meta[charset]\");\n  const dataStyles = /* @__PURE__ */ win.document.createElement(\"style\");\n  const deferredConnectedCallbacks = [];\n  let appLoadFallback;\n  let isBootstrapping = true;\n  Object.assign(plt, options);\n  plt.$resourcesUrl$ = new URL(options.resourcesUrl || \"./\", win.document.baseURI).href;\n  {\n    plt.$flags$ |= 2 /* appLoaded */;\n  }\n  {\n    hydrateScopedToShadow();\n  }\n  let hasSlotRelocation = false;\n  lazyBundles.map((lazyBundle) => {\n    lazyBundle[1].map((compactMeta) => {\n      var _a2;\n      const cmpMeta = {\n        $flags$: compactMeta[0],\n        $tagName$: compactMeta[1],\n        $members$: compactMeta[2],\n        $listeners$: compactMeta[3]\n      };\n      if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n        hasSlotRelocation = true;\n      }\n      {\n        cmpMeta.$members$ = compactMeta[2];\n      }\n      {\n        cmpMeta.$listeners$ = compactMeta[3];\n      }\n      {\n        cmpMeta.$attrsToReflect$ = [];\n      }\n      {\n        cmpMeta.$watchers$ = (_a2 = compactMeta[4]) != null ? _a2 : {};\n      }\n      const tagName = cmpMeta.$tagName$;\n      const HostElement = class extends HTMLElement {\n        // StencilLazyHost\n        constructor(self) {\n          super(self);\n          this.hasRegisteredEventListeners = false;\n          self = this;\n          registerHost(self, cmpMeta);\n          if (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            {\n              if (!self.shadowRoot) {\n                createShadowRoot.call(self, cmpMeta);\n              } else {\n                if (self.shadowRoot.mode !== \"open\") {\n                  throw new Error(\n                    `Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${self.shadowRoot.mode} but Stencil only supports open shadow roots.`\n                  );\n                }\n              }\n            }\n          }\n        }\n        connectedCallback() {\n          const hostRef = getHostRef(this);\n          if (!this.hasRegisteredEventListeners) {\n            this.hasRegisteredEventListeners = true;\n            addHostEventListeners(this, hostRef, cmpMeta.$listeners$);\n          }\n          if (appLoadFallback) {\n            clearTimeout(appLoadFallback);\n            appLoadFallback = null;\n          }\n          if (isBootstrapping) {\n            deferredConnectedCallbacks.push(this);\n          } else {\n            plt.jmp(() => connectedCallback(this));\n          }\n        }\n        disconnectedCallback() {\n          plt.jmp(() => disconnectedCallback(this));\n          plt.raf(() => {\n            var _a3;\n            const hostRef = getHostRef(this);\n            const i2 = deferredConnectedCallbacks.findIndex((host) => host === this);\n            if (i2 > -1) {\n              deferredConnectedCallbacks.splice(i2, 1);\n            }\n            if (((_a3 = hostRef == null ? void 0 : hostRef.$vnode$) == null ? void 0 : _a3.$elm$) instanceof Node && !hostRef.$vnode$.$elm$.isConnected) {\n              delete hostRef.$vnode$.$elm$;\n            }\n          });\n        }\n        componentOnReady() {\n          return getHostRef(this).$onReadyPromise$;\n        }\n      };\n      {\n        if (cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n          patchPseudoShadowDom(HostElement.prototype);\n        }\n      }\n      cmpMeta.$lazyBundleId$ = lazyBundle[0];\n      if (!exclude.includes(tagName) && !customElements2.get(tagName)) {\n        cmpTags.push(tagName);\n        customElements2.define(\n          tagName,\n          proxyComponent(HostElement, cmpMeta, 1 /* isElementConstructor */)\n        );\n      }\n    });\n  });\n  if (cmpTags.length > 0) {\n    if (hasSlotRelocation) {\n      dataStyles.textContent += SLOT_FB_CSS;\n    }\n    {\n      dataStyles.textContent += cmpTags.sort() + HYDRATED_CSS;\n    }\n    if (dataStyles.innerHTML.length) {\n      dataStyles.setAttribute(\"data-styles\", \"\");\n      const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(win.document);\n      if (nonce != null) {\n        dataStyles.setAttribute(\"nonce\", nonce);\n      }\n      head.insertBefore(dataStyles, metaCharset ? metaCharset.nextSibling : head.firstChild);\n    }\n  }\n  isBootstrapping = false;\n  if (deferredConnectedCallbacks.length) {\n    deferredConnectedCallbacks.map((host) => host.connectedCallback());\n  } else {\n    {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30));\n    }\n  }\n  endBootstrap();\n};\n\n// src/runtime/fragment.ts\nvar Fragment = (_, children) => children;\nvar addHostEventListeners = (elm, hostRef, listeners, attachParentListeners) => {\n  if (listeners && win.document) {\n    listeners.map(([flags, name, method]) => {\n      const target = getHostListenerTarget(win.document, elm, flags) ;\n      const handler = hostListenerProxy(hostRef, method);\n      const opts = hostListenerOpts(flags);\n      plt.ael(target, name, handler, opts);\n      (hostRef.$rmListeners$ = hostRef.$rmListeners$ || []).push(() => plt.rel(target, name, handler, opts));\n    });\n  }\n};\nvar hostListenerProxy = (hostRef, methodName) => (ev) => {\n  var _a;\n  try {\n    {\n      if (hostRef.$flags$ & 256 /* isListenReady */) {\n        (_a = hostRef.$lazyInstance$) == null ? void 0 : _a[methodName](ev);\n      } else {\n        (hostRef.$queuedListeners$ = hostRef.$queuedListeners$ || []).push([methodName, ev]);\n      }\n    }\n  } catch (e) {\n    consoleError(e, hostRef.$hostElement$);\n  }\n};\nvar getHostListenerTarget = (doc, elm, flags) => {\n  if (flags & 4 /* TargetDocument */) {\n    return doc;\n  }\n  if (flags & 8 /* TargetWindow */) {\n    return win;\n  }\n  if (flags & 16 /* TargetBody */) {\n    return doc.body;\n  }\n  return elm;\n};\nvar hostListenerOpts = (flags) => supportsListenerOptions ? {\n  passive: (flags & 1 /* Passive */) !== 0,\n  capture: (flags & 2 /* Capture */) !== 0\n} : (flags & 2 /* Capture */) !== 0;\n\n// src/runtime/nonce.ts\nvar setNonce = (nonce) => plt.$nonce$ = nonce;\n\nexport { Build as B, Fragment as F, H, LogLevel as L, isPlatform as a, bootstrapLazy as b, getPlatforms as c, createEvent as d, getIonMode as e, readTask as f, globalScripts as g, h, initialize as i, Host as j, getElement as k, config as l, printIonWarning as m, forceUpdate as n, printIonError as o, promiseResolve as p, getAssetPath as q, registerInstance as r, setNonce as s, printRequiredElementError as t, writeTask as w };\n"], "mappings": ";AAAA;AACA;AACA;AACA,MAAMA,SAAS,GAAG,OAAO;AACzB,MAAMC,KAAK,GAAG,WAAY;EAAEC,qBAAqB,EAAE,IAAI;EAAEC,oBAAoB,EAAE,UAAU;EAAEC,QAAQ,EAAE,IAAI;EAAEC,SAAS,EAAE,IAAI;EAAEC,cAAc,EAAE,IAAI;EAAEC,SAAS,EAAE;AAAI,CAAC;;AAElK;AACA,MAAMC,MAAM,CAAC;EACTC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,CAAC,GAAG,IAAIC,GAAG,CAAC,CAAC;EACtB;EACAC,KAAKA,CAACC,SAAS,EAAE;IACb,IAAI,CAACH,CAAC,GAAG,IAAIC,GAAG,CAACG,MAAM,CAACC,OAAO,CAACF,SAAS,CAAC,CAAC;EAC/C;EACAG,GAAGA,CAACC,GAAG,EAAEC,QAAQ,EAAE;IACf,MAAMC,KAAK,GAAG,IAAI,CAACT,CAAC,CAACM,GAAG,CAACC,GAAG,CAAC;IAC7B,OAAOE,KAAK,KAAKC,SAAS,GAAGD,KAAK,GAAGD,QAAQ;EACjD;EACAG,UAAUA,CAACJ,GAAG,EAAEC,QAAQ,GAAG,KAAK,EAAE;IAC9B,MAAMI,GAAG,GAAG,IAAI,CAACZ,CAAC,CAACM,GAAG,CAACC,GAAG,CAAC;IAC3B,IAAIK,GAAG,KAAKF,SAAS,EAAE;MACnB,OAAOF,QAAQ;IACnB;IACA,IAAI,OAAOI,GAAG,KAAK,QAAQ,EAAE;MACzB,OAAOA,GAAG,KAAK,MAAM;IACzB;IACA,OAAO,CAAC,CAACA,GAAG;EAChB;EACAC,SAASA,CAACN,GAAG,EAAEC,QAAQ,EAAE;IACrB,MAAMI,GAAG,GAAGE,UAAU,CAAC,IAAI,CAACd,CAAC,CAACM,GAAG,CAACC,GAAG,CAAC,CAAC;IACvC,OAAOQ,KAAK,CAACH,GAAG,CAAC,GAAIJ,QAAQ,KAAKE,SAAS,GAAGF,QAAQ,GAAGQ,GAAG,GAAIJ,GAAG;EACvE;EACAK,GAAGA,CAACV,GAAG,EAAEE,KAAK,EAAE;IACZ,IAAI,CAACT,CAAC,CAACiB,GAAG,CAACV,GAAG,EAAEE,KAAK,CAAC;EAC1B;AACJ;AACA,MAAMS,MAAM,GAAG,aAAc,IAAIpB,MAAM,CAAC,CAAC;AACzC,MAAMqB,iBAAiB,GAAIC,GAAG,IAAK;EAC/B,IAAI;IACA,MAAMC,SAAS,GAAGD,GAAG,CAACE,cAAc,CAACC,OAAO,CAACC,iBAAiB,CAAC;IAC/D,OAAOH,SAAS,KAAK,IAAI,GAAGI,IAAI,CAACC,KAAK,CAACL,SAAS,CAAC,GAAG,CAAC,CAAC;EAC1D,CAAC,CACD,OAAOM,CAAC,EAAE;IACN,OAAO,CAAC,CAAC;EACb;AACJ,CAAC;AACD,MAAMC,UAAU,GAAGA,CAACR,GAAG,EAAES,CAAC,KAAK;EAC3B,IAAI;IACAT,GAAG,CAACE,cAAc,CAACQ,OAAO,CAACN,iBAAiB,EAAEC,IAAI,CAACM,SAAS,CAACF,CAAC,CAAC,CAAC;EACpE,CAAC,CACD,OAAOF,CAAC,EAAE;IACN;EACJ;AACJ,CAAC;AACD,MAAMK,aAAa,GAAIZ,GAAG,IAAK;EAC3B,MAAMjB,SAAS,GAAG,CAAC,CAAC;EACpBiB,GAAG,CAACa,QAAQ,CAACC,MAAM,CACdC,KAAK,CAAC,CAAC,CAAC,CACRC,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAEC,KAAK,IAAKA,KAAK,CAACF,KAAK,CAAC,GAAG,CAAC,CAAC,CAChCC,GAAG,CAAC,CAAC,CAAC9B,GAAG,EAAEE,KAAK,CAAC,KAAK;IACvB,IAAI;MACA,OAAO,CAAC8B,kBAAkB,CAAChC,GAAG,CAAC,EAAEgC,kBAAkB,CAAC9B,KAAK,CAAC,CAAC;IAC/D,CAAC,CACD,OAAOkB,CAAC,EAAE;MACN,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC;IACnB;EACJ,CAAC,CAAC,CACGa,MAAM,CAAC,CAAC,CAACjC,GAAG,CAAC,KAAKkC,UAAU,CAAClC,GAAG,EAAEmC,YAAY,CAAC,CAAC,CAChDL,GAAG,CAAC,CAAC,CAAC9B,GAAG,EAAEE,KAAK,CAAC,KAAK,CAACF,GAAG,CAAC4B,KAAK,CAACO,YAAY,CAACC,MAAM,CAAC,EAAElC,KAAK,CAAC,CAAC,CAC9DmC,OAAO,CAAC,CAAC,CAACrC,GAAG,EAAEE,KAAK,CAAC,KAAK;IAC3BN,SAAS,CAACI,GAAG,CAAC,GAAGE,KAAK;EAC1B,CAAC,CAAC;EACF,OAAON,SAAS;AACpB,CAAC;AACD,MAAMsC,UAAU,GAAGA,CAACI,KAAK,EAAEX,MAAM,KAAK;EAClC,OAAOW,KAAK,CAACC,MAAM,CAAC,CAAC,EAAEZ,MAAM,CAACS,MAAM,CAAC,KAAKT,MAAM;AACpD,CAAC;AACD,MAAMQ,YAAY,GAAG,QAAQ;AAC7B,MAAMlB,iBAAiB,GAAG,sBAAsB;AAEhD,IAAIuB,QAAQ;AACZ,CAAC,UAAUA,QAAQ,EAAE;EACjBA,QAAQ,CAAC,KAAK,CAAC,GAAG,KAAK;EACvBA,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO;EAC3BA,QAAQ,CAAC,MAAM,CAAC,GAAG,MAAM;AAC7B,CAAC,EAAEA,QAAQ,KAAKA,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAGA,CAACC,OAAO,EAAE,GAAGC,MAAM,KAAK;EAC5C,MAAMC,QAAQ,GAAGjC,MAAM,CAACZ,GAAG,CAAC,UAAU,EAAEyC,QAAQ,CAACK,IAAI,CAAC;EACtD,IAAI,CAACL,QAAQ,CAACK,IAAI,CAAC,CAACC,QAAQ,CAACF,QAAQ,CAAC,EAAE;IACpC,OAAOG,OAAO,CAACC,IAAI,CAAC,oBAAoBN,OAAO,EAAE,EAAE,GAAGC,MAAM,CAAC;EACjE;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,aAAa,GAAGA,CAACP,OAAO,EAAE,GAAGC,MAAM,KAAK;EAC1C,MAAMC,QAAQ,GAAGjC,MAAM,CAACZ,GAAG,CAAC,UAAU,EAAEyC,QAAQ,CAACU,KAAK,CAAC;EACvD,IAAI,CAACV,QAAQ,CAACU,KAAK,EAAEV,QAAQ,CAACK,IAAI,CAAC,CAACC,QAAQ,CAACF,QAAQ,CAAC,EAAE;IACpD,OAAOG,OAAO,CAACI,KAAK,CAAC,kBAAkBT,OAAO,EAAE,EAAE,GAAGC,MAAM,CAAC;EAChE;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,yBAAyB,GAAGA,CAACC,EAAE,EAAE,GAAGC,eAAe,KAAK;EAC1D,OAAOP,OAAO,CAACI,KAAK,CAAC,IAAIE,EAAE,CAACE,OAAO,CAACC,WAAW,CAAC,CAAC,yBAAyBF,eAAe,CAACG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;AAC9G,CAAC;AAED,MAAMC,YAAY,GAAI7C,GAAG,IAAK8C,cAAc,CAAC9C,GAAG,CAAC;AACjD,MAAM+C,UAAU,GAAGA,CAACC,aAAa,EAAEC,QAAQ,KAAK;EAC5C,IAAI,OAAOD,aAAa,KAAK,QAAQ,EAAE;IACnCC,QAAQ,GAAGD,aAAa;IACxBA,aAAa,GAAG1D,SAAS;EAC7B;EACA,OAAOuD,YAAY,CAACG,aAAa,CAAC,CAACf,QAAQ,CAACgB,QAAQ,CAAC;AACzD,CAAC;AACD,MAAMH,cAAc,GAAGA,CAAC9C,GAAG,GAAGkD,MAAM,KAAK;EACrC,IAAI,OAAOlD,GAAG,KAAK,WAAW,EAAE;IAC5B,OAAO,EAAE;EACb;EACAA,GAAG,CAACmD,KAAK,GAAGnD,GAAG,CAACmD,KAAK,IAAI,CAAC,CAAC;EAC3B,IAAIC,SAAS,GAAGpD,GAAG,CAACmD,KAAK,CAACC,SAAS;EACnC,IAAIA,SAAS,IAAI,IAAI,EAAE;IACnBA,SAAS,GAAGpD,GAAG,CAACmD,KAAK,CAACC,SAAS,GAAGC,eAAe,CAACrD,GAAG,CAAC;IACtDoD,SAAS,CAAC5B,OAAO,CAAE8B,CAAC,IAAKtD,GAAG,CAACuD,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACC,GAAG,CAAC,OAAOJ,CAAC,EAAE,CAAC,CAAC;EACpF;EACA,OAAOF,SAAS;AACpB,CAAC;AACD,MAAMC,eAAe,GAAIrD,GAAG,IAAK;EAC7B,MAAM2D,qBAAqB,GAAG7D,MAAM,CAACZ,GAAG,CAAC,UAAU,CAAC;EACpD,OAAOF,MAAM,CAAC4E,IAAI,CAACC,aAAa,CAAC,CAACzC,MAAM,CAAEkC,CAAC,IAAK;IAC5C,MAAMQ,YAAY,GAAGH,qBAAqB,KAAK,IAAI,IAAIA,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACL,CAAC,CAAC;IAC3H,OAAO,OAAOQ,YAAY,KAAK,UAAU,GAAGA,YAAY,CAAC9D,GAAG,CAAC,GAAG6D,aAAa,CAACP,CAAC,CAAC,CAACtD,GAAG,CAAC;EACzF,CAAC,CAAC;AACN,CAAC;AACD,MAAM+D,WAAW,GAAI/D,GAAG,IAAKgE,QAAQ,CAAChE,GAAG,CAAC,IAAI,CAACiE,QAAQ,CAACjE,GAAG,CAAC;AAC5D,MAAMkE,MAAM,GAAIlE,GAAG,IAAK;EACpB;EACA,IAAImE,aAAa,CAACnE,GAAG,EAAE,OAAO,CAAC,EAAE;IAC7B,OAAO,IAAI;EACf;EACA;EACA,IAAImE,aAAa,CAACnE,GAAG,EAAE,YAAY,CAAC,IAAIgE,QAAQ,CAAChE,GAAG,CAAC,EAAE;IACnD,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB,CAAC;AACD,MAAMoE,QAAQ,GAAIpE,GAAG,IAAKmE,aAAa,CAACnE,GAAG,EAAE,SAAS,CAAC;AACvD,MAAMqE,KAAK,GAAIrE,GAAG,IAAKmE,aAAa,CAACnE,GAAG,EAAE,cAAc,CAAC,IAAIkE,MAAM,CAAClE,GAAG,CAAC;AACxE,MAAMsE,SAAS,GAAItE,GAAG,IAAKmE,aAAa,CAACnE,GAAG,EAAE,eAAe,CAAC;AAC9D,MAAMuE,eAAe,GAAIvE,GAAG,IAAK;EAC7B,OAAOsE,SAAS,CAACtE,GAAG,CAAC,IAAI,CAACmE,aAAa,CAACnE,GAAG,EAAE,SAAS,CAAC;AAC3D,CAAC;AACD,MAAMwE,SAAS,GAAIxE,GAAG,IAAK;EACvB,MAAMyE,KAAK,GAAGzE,GAAG,CAAC0E,UAAU;EAC5B,MAAMC,MAAM,GAAG3E,GAAG,CAAC4E,WAAW;EAC9B,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACN,KAAK,EAAEE,MAAM,CAAC;EACxC,MAAMK,OAAO,GAAGF,IAAI,CAACG,GAAG,CAACR,KAAK,EAAEE,MAAM,CAAC;EACvC,OAAOE,QAAQ,GAAG,GAAG,IAAIA,QAAQ,GAAG,GAAG,IAAIG,OAAO,GAAG,GAAG,IAAIA,OAAO,GAAG,GAAG;AAC7E,CAAC;AACD,MAAME,QAAQ,GAAIlF,GAAG,IAAK;EACtB,MAAMyE,KAAK,GAAGzE,GAAG,CAAC0E,UAAU;EAC5B,MAAMC,MAAM,GAAG3E,GAAG,CAAC4E,WAAW;EAC9B,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACN,KAAK,EAAEE,MAAM,CAAC;EACxC,MAAMK,OAAO,GAAGF,IAAI,CAACG,GAAG,CAACR,KAAK,EAAEE,MAAM,CAAC;EACvC,OAAOT,MAAM,CAAClE,GAAG,CAAC,IAAIuE,eAAe,CAACvE,GAAG,CAAC,IAAK6E,QAAQ,GAAG,GAAG,IAAIA,QAAQ,GAAG,GAAG,IAAIG,OAAO,GAAG,GAAG,IAAIA,OAAO,GAAG,IAAK;AACvH,CAAC;AACD,MAAMhB,QAAQ,GAAIhE,GAAG,IAAKmF,UAAU,CAACnF,GAAG,EAAE,sBAAsB,CAAC;AACjE,MAAMoF,SAAS,GAAIpF,GAAG,IAAK,CAACgE,QAAQ,CAAChE,GAAG,CAAC;AACzC,MAAMiE,QAAQ,GAAIjE,GAAG,IAAKqF,SAAS,CAACrF,GAAG,CAAC,IAAIsF,iBAAiB,CAACtF,GAAG,CAAC;AAClE,MAAMqF,SAAS,GAAIrF,GAAG,IAAK,CAAC,EAAEA,GAAG,CAAC,SAAS,CAAC,IAAIA,GAAG,CAAC,UAAU,CAAC,IAAIA,GAAG,CAAC,UAAU,CAAC,CAAC;AACnF,MAAMsF,iBAAiB,GAAItF,GAAG,IAAK;EAC/B,MAAMuF,SAAS,GAAGvF,GAAG,CAAC,WAAW,CAAC;EAClC;EACA,OAAO,CAAC,EAAE,CAACuF,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,QAAQ,KAAM,CAACD,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACE,gBAAgB,KAAK,CAAC,CAACF,SAAS,CAACE,gBAAgB,CAAC,CAAE,CAAC;AACnN,CAAC;AACD,MAAMC,UAAU,GAAI1F,GAAG,IAAKmE,aAAa,CAACnE,GAAG,EAAE,WAAW,CAAC;AAC3D,MAAM2F,KAAK,GAAI3F,GAAG,IAAK;EAAE,IAAI4F,EAAE;EAAE,OAAO,CAAC,EAAE,CAAC,CAACA,EAAE,GAAG5F,GAAG,CAACmF,UAAU,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAAC7F,GAAG,EAAE,4BAA4B,CAAC,CAAC8F,OAAO,KAAK9F,GAAG,CAAC+F,SAAS,CAACC,UAAU,CAAC;AAAE,CAAC;AAC1L,MAAM7B,aAAa,GAAGA,CAACnE,GAAG,EAAEiG,IAAI,KAAKA,IAAI,CAACC,IAAI,CAAClG,GAAG,CAAC+F,SAAS,CAACI,SAAS,CAAC;AACvE,MAAMhB,UAAU,GAAGA,CAACnF,GAAG,EAAEoG,KAAK,KAAK;EAAE,IAAIR,EAAE;EAAE,OAAO,CAACA,EAAE,GAAG5F,GAAG,CAACmF,UAAU,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAAC7F,GAAG,EAAEoG,KAAK,CAAC,CAACN,OAAO;AAAE,CAAC;AAC7I,MAAMjC,aAAa,GAAG;EAClBwC,IAAI,EAAEnC,MAAM;EACZoC,MAAM,EAAElC,QAAQ;EAChBmC,GAAG,EAAElC,KAAK;EACVmC,OAAO,EAAElC,SAAS;EAClBmC,OAAO,EAAEjC,SAAS;EAClBkC,MAAM,EAAExB,QAAQ;EAChByB,OAAO,EAAEtB,SAAS;EAClBE,SAAS,EAAED,iBAAiB;EAC5BsB,QAAQ,EAAElB,UAAU;EACpBmB,GAAG,EAAElB,KAAK;EACVmB,MAAM,EAAE9C,QAAQ;EAChB+C,SAAS,EAAEhD,WAAW;EACtBiD,OAAO,EAAE5B,SAAS;EAClB6B,MAAM,EAAEhD;AACZ,CAAC;;AAED;AACA,IAAIiD,WAAW;AACf,MAAMC,UAAU,GAAIC,GAAG,IAAK;EACxB,OAAQA,GAAG,IAAIC,OAAO,CAACD,GAAG,CAAC,IAAKF,WAAW;AAC/C,CAAC;AACD,MAAMI,UAAU,GAAGA,CAACC,UAAU,GAAG,CAAC,CAAC,KAAK;EACpC,IAAI,OAAOrE,MAAM,KAAK,WAAW,EAAE;IAC/B;EACJ;EACA,MAAMsE,GAAG,GAAGtE,MAAM,CAACK,QAAQ;EAC3B,MAAMvD,GAAG,GAAGkD,MAAM;EAClB,MAAMC,KAAK,GAAInD,GAAG,CAACmD,KAAK,GAAGnD,GAAG,CAACmD,KAAK,IAAI,CAAC,CAAE;EAC3C;EACA;EACA,MAAMpE,SAAS,GAAGC,MAAM,CAACyI,MAAM,CAACzI,MAAM,CAACyI,MAAM,CAACzI,MAAM,CAACyI,MAAM,CAACzI,MAAM,CAACyI,MAAM,CAACzI,MAAM,CAACyI,MAAM,CAAC,CAAC,CAAC,EAAE1H,iBAAiB,CAACC,GAAG,CAAC,CAAC,EAAE;IAAE0H,aAAa,EAAE;EAAM,CAAC,CAAC,EAAEvE,KAAK,CAACrD,MAAM,CAAC,EAAEc,aAAa,CAACZ,GAAG,CAAC,CAAC,EAAEuH,UAAU,CAAC;EAC/LzH,MAAM,CAAChB,KAAK,CAACC,SAAS,CAAC;EACvB,IAAIe,MAAM,CAACP,UAAU,CAAC,eAAe,CAAC,EAAE;IACpCiB,UAAU,CAACR,GAAG,EAAEjB,SAAS,CAAC;EAC9B;EACA;EACA+D,cAAc,CAAC9C,GAAG,CAAC;EACnB;EACA;EACA;EACAmD,KAAK,CAACrD,MAAM,GAAGA,MAAM;EACrBqD,KAAK,CAACwE,IAAI,GAAGT,WAAW,GAAGpH,MAAM,CAACZ,GAAG,CAAC,MAAM,EAAEsI,GAAG,CAAChE,eAAe,CAACoE,YAAY,CAAC,MAAM,CAAC,KAAK7E,UAAU,CAAC/C,GAAG,EAAE,KAAK,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;EAClIF,MAAM,CAACD,GAAG,CAAC,MAAM,EAAEqH,WAAW,CAAC;EAC/BM,GAAG,CAAChE,eAAe,CAACqE,YAAY,CAAC,MAAM,EAAEX,WAAW,CAAC;EACrDM,GAAG,CAAChE,eAAe,CAACC,SAAS,CAACC,GAAG,CAACwD,WAAW,CAAC;EAC9C,IAAIpH,MAAM,CAACP,UAAU,CAAC,UAAU,CAAC,EAAE;IAC/BO,MAAM,CAACD,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC;EACjC;EACA,MAAMiI,cAAc,GAAIC,GAAG,IAAK;IAAE,IAAInC,EAAE;IAAE,OAAO,CAACA,EAAE,GAAGmC,GAAG,CAACrF,OAAO,MAAM,IAAI,IAAIkD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACvE,UAAU,CAAC,MAAM,CAAC;EAAE,CAAC;EACjI,MAAM2G,uBAAuB,GAAIC,OAAO,IAAK,CAAC,KAAK,EAAE,IAAI,CAAC,CAAChG,QAAQ,CAACgG,OAAO,CAAC;EAC5EC,OAAO,CAAEH,GAAG,IAAK;IACb,OAAOA,GAAG,EAAE;MACR,MAAME,OAAO,GAAGF,GAAG,CAACJ,IAAI,IAAII,GAAG,CAACH,YAAY,CAAC,MAAM,CAAC;MACpD,IAAIK,OAAO,EAAE;QACT,IAAID,uBAAuB,CAACC,OAAO,CAAC,EAAE;UAClC,OAAOA,OAAO;QAClB,CAAC,MACI,IAAIH,cAAc,CAACC,GAAG,CAAC,EAAE;UAC1BnG,eAAe,CAAC,uBAAuB,GAAGqG,OAAO,GAAG,4BAA4B,CAAC;QACrF;MACJ;MACAF,GAAG,GAAGA,GAAG,CAACI,aAAa;IAC3B;IACA,OAAOjB,WAAW;EACtB,CAAC,CAAC;AACN,CAAC;AAED,MAAMkB,aAAa,GAAGd,UAAU;AAChC,MAAMe,YAAY,GAAG,EAAE;;AAEvB;AACA;AACA;AACA,IAAIC,SAAS,GAAGtJ,MAAM,CAACuJ,cAAc;AACrC,IAAIC,QAAQ,GAAGA,CAACC,MAAM,EAAEC,GAAG,KAAK;EAC9B,KAAK,IAAIC,IAAI,IAAID,GAAG,EAClBJ,SAAS,CAACG,MAAM,EAAEE,IAAI,EAAE;IAAEzJ,GAAG,EAAEwJ,GAAG,CAACC,IAAI,CAAC;IAAEC,UAAU,EAAE;EAAK,CAAC,CAAC;AACjE,CAAC;AACD,IAAIC,KAAK,GAAG;EACVC,SAAS,EAAE;AAAI,CAAC;;AAElB;AACA,IAAIC,MAAM,GAAG,4BAA4B;AACzC,IAAIC,OAAO,GAAG,8BAA8B;AAC5C,IAAIC,aAAa,GAAG,eAAgB,CAAEC,cAAc,IAAK;EACvDA,cAAc,CAAC,WAAW,CAAC,GAAG,WAAW;EACzCA,cAAc,CAAC,MAAM,CAAC,GAAG,MAAM;EAC/BA,cAAc,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACnCA,cAAc,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACnCA,cAAc,CAAC,eAAe,CAAC,GAAG,QAAQ;EAC1CA,cAAc,CAAC,SAAS,CAAC,GAAG,SAAS;EACrCA,cAAc,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACnC,OAAOA,cAAc;AACvB,CAAC,EAAED,aAAa,IAAI,CAAC,CAAC,CAAC;AACvB,IAAIE,gBAAgB,GAAG,eAAgB,CAAEC,iBAAiB,IAAK;EAC7DA,iBAAiB,CAAC,OAAO,CAAC,GAAG,OAAO;EACpCA,iBAAiB,CAAC,MAAM,CAAC,GAAG,MAAM;EAClCA,iBAAiB,CAAC,KAAK,CAAC,GAAG,KAAK;EAChCA,iBAAiB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACtCA,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,QAAQ;EACjDA,iBAAiB,CAAC,KAAK,CAAC,GAAG,KAAK;EAChCA,iBAAiB,CAAC,SAAS,CAAC,GAAG,SAAS;EACxCA,iBAAiB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACtC,OAAOA,iBAAiB;AAC1B,CAAC,EAAED,gBAAgB,IAAI,CAAC,CAAC,CAAC;AAC1B,IAAIE,aAAa,GAAG,MAAM;AAC1B,IAAIC,cAAc,GAAG,OAAO;AAC5B,IAAIC,iBAAiB,GAAG,aAAa;;AAErC;AACA,IAAIC,UAAU,GAAIpC,GAAG,IAAK;EACxB,IAAIA,GAAG,CAACqC,qBAAqB,EAAE;IAC7B,OAAOrC,GAAG,CAACqC,qBAAqB,CAAC,CAAC;EACpC;EACA,OAAO,KAAK,CAAC;AACf,CAAC;AACD,IAAIC,gBAAgB,GAAGA,CAACC,YAAY,EAAEC,OAAO,KAAK;EAChDD,YAAY,CAACF,qBAAqB,GAAG,MAAMG,OAAO;EAClDA,OAAO,CAACC,cAAc,GAAGF,YAAY;AACvC,CAAC;AACD,IAAIG,YAAY,GAAGA,CAACC,WAAW,EAAEC,OAAO,KAAK;EAC3C,MAAMJ,OAAO,GAAG;IACdK,OAAO,EAAE,CAAC;IACVC,aAAa,EAAEH,WAAW;IAC1BI,SAAS,EAAEH,OAAO;IAClBI,gBAAgB,EAAE,eAAgB,IAAIvL,GAAG,CAAC;EAC5C,CAAC;EACD;IACE+K,OAAO,CAACS,mBAAmB,GAAG,IAAIC,OAAO,CAAEC,CAAC,IAAKX,OAAO,CAACY,mBAAmB,GAAGD,CAAC,CAAC;EACnF;EACA;IACEX,OAAO,CAACa,gBAAgB,GAAG,IAAIH,OAAO,CAAEC,CAAC,IAAKX,OAAO,CAACc,gBAAgB,GAAGH,CAAC,CAAC;IAC3ER,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE;IACvBA,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE;EAC1B;EACA,MAAM3C,GAAG,GAAGwC,OAAO;EACnBG,WAAW,CAACN,qBAAqB,GAAG,MAAMrC,GAAG;EAC7C,OAAOA,GAAG;AACZ,CAAC;AACD,IAAIuD,iBAAiB,GAAGA,CAAC5C,GAAG,EAAE6C,UAAU,KAAKA,UAAU,IAAI7C,GAAG;AAC9D,IAAI8C,YAAY,GAAGA,CAACtK,CAAC,EAAEiC,EAAE,KAAK,CAAC,CAAC,EAAEN,OAAO,CAACI,KAAK,EAAE/B,CAAC,EAAEiC,EAAE,CAAC;;AAEvD;AACA,IAAIsI,UAAU,GAAG,eAAgB,IAAIjM,GAAG,CAAC,CAAC;AAC1C,IAAIkM,UAAU,GAAGA,CAACf,OAAO,EAAEJ,OAAO,EAAEoB,YAAY,KAAK;EACnD,MAAMC,UAAU,GAAGjB,OAAO,CAACkB,SAAS,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;EACvD,MAAMC,QAAQ,GAAGpB,OAAO,CAACqB,cAAc;EACvC,IAAI,CAACD,QAAQ,EAAE;IACb,OAAO,KAAK,CAAC;EACf;EACA,MAAME,MAAM,GAAGR,UAAU,CAAC5L,GAAG,CAACkM,QAAQ,CAAC;EACvC,IAAIE,MAAM,EAAE;IACV,OAAOA,MAAM,CAACL,UAAU,CAAC;EAC3B;EACA;EACA,OAAO,MAAM,CACX;EACA;EACA;EACA;EACA,KAAKG,QAAQ,YAAY,EAAE,EAC7B,CAAC,CAACG,IAAI,CACHC,cAAc,IAAK;IAClB;MACEV,UAAU,CAACjL,GAAG,CAACuL,QAAQ,EAAEI,cAAc,CAAC;IAC1C;IACA,OAAOA,cAAc,CAACP,UAAU,CAAC;EACnC,CAAC,EACA1K,CAAC,IAAK;IACLsK,YAAY,CAACtK,CAAC,EAAEqJ,OAAO,CAACM,aAAa,CAAC;EACxC,CACF,CAAC;AACH,CAAC;;AAED;AACA,IAAIuB,MAAM,GAAG,eAAgB,IAAI5M,GAAG,CAAC,CAAC;AACtC,IAAI6M,mBAAmB,GAAG,EAAE;;AAE5B;AACA,IAAIC,cAAc,GAAG,GAAG;AACxB,IAAIC,eAAe,GAAG,GAAG;AACzB,IAAIC,YAAY,GAAG,GAAG;AACtB,IAAIC,YAAY,GAAG,GAAG;AACtB,IAAIC,eAAe,GAAG,GAAG;AACzB,IAAIC,UAAU,GAAG,MAAM;AACvB,IAAIC,iBAAiB,GAAG,QAAQ;AAChC,IAAIC,gBAAgB,GAAG,MAAM;AAC7B,IAAIC,YAAY,GAAG,kDAAkD;AACrE,IAAIC,WAAW,GAAG,wDAAwD;AAC1E,IAAIC,QAAQ,GAAG,8BAA8B;AAC7C,IAAIrM,GAAG,GAAG,OAAOkD,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,CAAC,CAAC;AACrD,IAAIoJ,CAAC,GAAGtM,GAAG,CAACuM,WAAW,IAAI,MAAM,EAChC;AACD,IAAIC,GAAG,GAAG;EACRvC,OAAO,EAAE,CAAC;EACVwC,cAAc,EAAE,EAAE;EAClBC,GAAG,EAAGC,EAAE,IAAKA,EAAE,CAAC,CAAC;EACjBC,GAAG,EAAGD,EAAE,IAAKE,qBAAqB,CAACF,EAAE,CAAC;EACtCG,GAAG,EAAEA,CAACtK,EAAE,EAAEuK,SAAS,EAAEC,QAAQ,EAAEC,IAAI,KAAKzK,EAAE,CAAC0K,gBAAgB,CAACH,SAAS,EAAEC,QAAQ,EAAEC,IAAI,CAAC;EACtFE,GAAG,EAAEA,CAAC3K,EAAE,EAAEuK,SAAS,EAAEC,QAAQ,EAAEC,IAAI,KAAKzK,EAAE,CAAC4K,mBAAmB,CAACL,SAAS,EAAEC,QAAQ,EAAEC,IAAI,CAAC;EACzFI,EAAE,EAAEA,CAACN,SAAS,EAAEE,IAAI,KAAK,IAAIK,WAAW,CAACP,SAAS,EAAEE,IAAI;AAC1D,CAAC;AACD,IAAIM,cAAc,GAAGpP,KAAK,CAACI,SAAS;AACpC,IAAIiP,uBAAuB,GAAG,eAAgB,CAAC,MAAM;EACnD,IAAI5H,EAAE;EACN,IAAI6H,wBAAwB,GAAG,KAAK;EACpC,IAAI;IACF,CAAC7H,EAAE,GAAG5F,GAAG,CAACuD,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGqC,EAAE,CAACsH,gBAAgB,CACxD,GAAG,EACH,IAAI,EACJlO,MAAM,CAACuJ,cAAc,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE;MACnCrJ,GAAGA,CAAA,EAAG;QACJuO,wBAAwB,GAAG,IAAI;MACjC;IACF,CAAC,CACH,CAAC;EACH,CAAC,CAAC,OAAOlN,CAAC,EAAE,CACZ;EACA,OAAOkN,wBAAwB;AACjC,CAAC,EAAE,CAAC;AACJ,IAAIC,cAAc,GAAIC,CAAC,IAAKrD,OAAO,CAACsD,OAAO,CAACD,CAAC,CAAC;AAC9C,IAAIE,gCAAgC,GAAG,eAAgB,CAAC,MAAM;EAC5D,IAAI;IACF,IAAIC,aAAa,CAAC,CAAC;IACnB,OAAO,OAAO,IAAIA,aAAa,CAAC,CAAC,CAACC,WAAW,KAAK,UAAU;EAC9D,CAAC,CAAC,OAAOxN,CAAC,EAAE,CACZ;EACA,OAAO,KAAK;AACd,CAAC,EAAE,CAAC;AACJ,IAAIyN,YAAY,GAAG,KAAK;AACxB,IAAIC,aAAa,GAAG,EAAE;AACtB,IAAIC,cAAc,GAAG,EAAE;AACvB,IAAIC,SAAS,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAMC,EAAE,IAAK;EACxCF,KAAK,CAACG,IAAI,CAACD,EAAE,CAAC;EACd,IAAI,CAACN,YAAY,EAAE;IACjBA,YAAY,GAAG,IAAI;IACnB,IAAIK,KAAK,IAAI7B,GAAG,CAACvC,OAAO,GAAG,CAAC,CAAC,iBAAiB;MAC5CuE,QAAQ,CAACC,KAAK,CAAC;IACjB,CAAC,MAAM;MACLjC,GAAG,CAACI,GAAG,CAAC6B,KAAK,CAAC;IAChB;EACF;AACF,CAAC;AACD,IAAIC,OAAO,GAAIN,KAAK,IAAK;EACvB,KAAK,IAAIO,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGP,KAAK,CAAC7M,MAAM,EAAEoN,EAAE,EAAE,EAAE;IACxC,IAAI;MACFP,KAAK,CAACO,EAAE,CAAC,CAACC,WAAW,CAACC,GAAG,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOtO,CAAC,EAAE;MACVsK,YAAY,CAACtK,CAAC,CAAC;IACjB;EACF;EACA6N,KAAK,CAAC7M,MAAM,GAAG,CAAC;AAClB,CAAC;AACD,IAAIkN,KAAK,GAAGA,CAAA,KAAM;EAChBC,OAAO,CAACT,aAAa,CAAC;EACtB;IACES,OAAO,CAACR,cAAc,CAAC;IACvB,IAAIF,YAAY,GAAGC,aAAa,CAAC1M,MAAM,GAAG,CAAC,EAAE;MAC3CiL,GAAG,CAACI,GAAG,CAAC6B,KAAK,CAAC;IAChB;EACF;AACF,CAAC;AACD,IAAID,QAAQ,GAAIF,EAAE,IAAKZ,cAAc,CAAC,CAAC,CAACnC,IAAI,CAAC+C,EAAE,CAAC;AAChD,IAAIQ,QAAQ,GAAG,eAAgBX,SAAS,CAACF,aAAa,EAAE,KAAK,CAAC;AAC9D,IAAIc,SAAS,GAAG,eAAgBZ,SAAS,CAACD,cAAc,EAAE,IAAI,CAAC;;AAE/D;AACA,IAAIc,YAAY,GAAIC,IAAI,IAAK;EAC3B,MAAMC,QAAQ,GAAG,IAAIC,GAAG,CAACF,IAAI,EAAEzC,GAAG,CAACC,cAAc,CAAC;EAClD,OAAOyC,QAAQ,CAACE,MAAM,KAAKpP,GAAG,CAACa,QAAQ,CAACuO,MAAM,GAAGF,QAAQ,CAACG,IAAI,GAAGH,QAAQ,CAACI,QAAQ;AACpF,CAAC;;AAED;AACA,IAAIC,KAAK,GAAI5B,CAAC,IAAKA,CAAC,IAAI,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC;AAC5C,IAAI6B,aAAa,GAAIC,CAAC,IAAK;EACzBA,CAAC,GAAG,OAAOA,CAAC;EACZ,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,UAAU;AAC3C,CAAC;;AAED;AACA,SAASC,wBAAwBA,CAAClI,GAAG,EAAE;EACrC,IAAI5B,EAAE,EAAE+J,EAAE,EAAEC,EAAE;EACd,OAAO,CAACA,EAAE,GAAG,CAACD,EAAE,GAAG,CAAC/J,EAAE,GAAG4B,GAAG,CAACqI,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGjK,EAAE,CAACkK,aAAa,CAAC,wBAAwB,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,EAAE,CAAC/H,YAAY,CAAC,SAAS,CAAC,KAAK,IAAI,GAAGgI,EAAE,GAAG,KAAK,CAAC;AACxK;;AAEA;AACA,IAAIG,6BAA6B,GAAIC,IAAI,IAAK;EAC5C,OAAOA,IAAI,CAAC7E,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;AACpD,CAAC;;AAED;AACA,IAAI8E,WAAW,GAAG,MAAMC,YAAY,CAAC;EACnC;AACF;AACA;AACA;AACA;AACA;EACE,OAAOC,cAAcA,CAACC,UAAU,EAAE;IAChC,MAAMC,IAAI,GAAGD,UAAU,CAAC/G,aAAa,CAAC;IACtC,MAAMhK,KAAK,GAAGiK,cAAc,IAAI8G,UAAU,GAAGA,UAAU,CAAC9G,cAAc,CAAC,GAAG,KAAK,CAAC;IAChF,QAAQ+G,IAAI;MACV,KAAK,QAAQ,CAAC;QACZ,OAAOhR,KAAK;MACd,KAAK,SAAS,CAAC;QACb,OAAOA,KAAK;MACd,KAAK,QAAQ,CAAC;QACZ,OAAOiR,MAAM,CAACjR,KAAK,CAAC;MACtB,KAAK,WAAW,CAAC;QACf,OAAO,KAAK,CAAC;MACf,KAAK,MAAM,CAAC;QACV,OAAO,IAAI;MACb,KAAK,QAAQ,CAAC;QACZ,IAAIA,KAAK,KAAK,KAAK,EAAE,OAAOO,GAAG;QAC/B,IAAIP,KAAK,KAAK,IAAI,EAAE,OAAO,CAAC,CAAC;QAC7B,IAAIA,KAAK,KAAK,UAAU,EAAE,OAAOkR,QAAQ;QACzC,IAAIlR,KAAK,KAAK,WAAW,EAAE,OAAO,CAACkR,QAAQ;QAC3C,OAAOlR,KAAK;MACd,KAAK,OAAO,CAAC;QACX,OAAOA,KAAK,CAAC4B,GAAG,CAAEuP,IAAI,IAAKN,YAAY,CAACC,cAAc,CAACK,IAAI,CAAC,CAAC;MAC/D,KAAK,MAAM,CAAC;QACV,OAAO,IAAIC,IAAI,CAACpR,KAAK,CAAC;MACxB,KAAK,KAAK,CAAC;QACT,MAAMqR,IAAI,GAAG,eAAgB,IAAI7R,GAAG,CAAC,CAAC;QACtC,KAAK,MAAM,CAACM,GAAG,EAAEK,GAAG,CAAC,IAAIH,KAAK,EAAE;UAC9B,MAAMsR,eAAe,GAAG,OAAOxR,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,GAAG+Q,YAAY,CAACC,cAAc,CAAChR,GAAG,CAAC,GAAGA,GAAG;UACxG,MAAMyR,iBAAiB,GAAGV,YAAY,CAACC,cAAc,CAAC3Q,GAAG,CAAC;UAC1DkR,IAAI,CAAC7Q,GAAG,CAAC8Q,eAAe,EAAEC,iBAAiB,CAAC;QAC9C;QACA,OAAOF,IAAI;MACb,KAAK,QAAQ,CAAC;QACZ,MAAMG,GAAG,GAAG,CAAC,CAAC;QACd,KAAK,MAAM,CAAC1R,GAAG,EAAEK,GAAG,CAAC,IAAIH,KAAK,EAAE;UAC9BwR,GAAG,CAAC1R,GAAG,CAAC,GAAG+Q,YAAY,CAACC,cAAc,CAAC3Q,GAAG,CAAC;QAC7C;QACA,OAAOqR,GAAG;MACZ,KAAK,QAAQ,CAAC;QACZ,MAAM;UAAEC,OAAO;UAAEC;QAAM,CAAC,GAAG1R,KAAK;QAChC,OAAO,IAAI2R,MAAM,CAACF,OAAO,EAAEC,KAAK,CAAC;MACnC,KAAK,KAAK,CAAC;QACT,MAAMlR,GAAG,GAAG,eAAgB,IAAIoR,GAAG,CAAC,CAAC;QACrC,KAAK,MAAMT,IAAI,IAAInR,KAAK,EAAE;UACxBQ,GAAG,CAAC6D,GAAG,CAACwM,YAAY,CAACC,cAAc,CAACK,IAAI,CAAC,CAAC;QAC5C;QACA,OAAO3Q,GAAG;MACZ,KAAK,QAAQ,CAAC;QACZ,OAAOqR,MAAM,CAAC7R,KAAK,CAAC;MACtB;QACE,MAAM,IAAI8R,KAAK,CAAC,qBAAqBd,IAAI,EAAE,CAAC;IAChD;EACF;EACA;AACF;AACA;AACA;AACA;AACA;EACE,OAAOe,mBAAmBA,CAACC,gBAAgB,EAAE;IAC3C,OAAOA,gBAAgB,CAACpQ,GAAG,CAAE5B,KAAK,IAAK6Q,YAAY,CAACC,cAAc,CAAC9Q,KAAK,CAAC,CAAC;EAC5E;EACA;AACF;AACA;AACA;AACA;AACA;EACE,OAAOiS,kBAAkBA,CAACT,GAAG,EAAE;IAC7B,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;MAC3C,OAAO,KAAK;IACd;IACA,IAAI,CAACA,GAAG,CAACU,cAAc,CAAClI,aAAa,CAAC,EAAE;MACtC,OAAO,KAAK;IACd;IACA,MAAMgH,IAAI,GAAGQ,GAAG,CAACxH,aAAa,CAAC;IAC/B,MAAMmI,eAAe,GAAGxS,MAAM,CAACyS,MAAM,CAAC;MAAE,GAAGxI,aAAa;MAAE,GAAGE;IAAiB,CAAC,CAAC,CAAClH,QAAQ,CAACoO,IAAI,CAAC;IAC/F,IAAI,CAACmB,eAAe,EAAE;MACpB,OAAO,KAAK;IACd;IACA,IAAInB,IAAI,KAAK,MAAM,CAAC,cAAcA,IAAI,KAAK,WAAW,CAAC,iBAAiB;MACtE,OAAOQ,GAAG,CAACU,cAAc,CAACjI,cAAc,CAAC;IAC3C;IACA,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA,IAAIoI,cAAc,GAAG,CAAC,CAAC;AACvBlJ,QAAQ,CAACkJ,cAAc,EAAE;EACvBC,GAAG,EAAEA,CAAA,KAAMA,GAAG;EACd1Q,GAAG,EAAEA,CAAA,KAAMA,GAAG;EACd2Q,EAAE,EAAEA,CAAA,KAAMA,EAAE;EACZC,MAAM,EAAEA,CAAA,KAAMA,MAAM;EACpBC,SAAS,EAAEA,CAAA,KAAMA;AACnB,CAAC,CAAC;AACF,IAAIF,EAAE,GAAIvS,KAAK,KAAM;EACnB0S,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,KAAK;EACZ3S;AACF,CAAC,CAAC;AACF,IAAIsS,GAAG,GAAItS,KAAK,KAAM;EACpB0S,IAAI,EAAE,KAAK;EACXC,KAAK,EAAE,IAAI;EACX3S;AACF,CAAC,CAAC;AACF,SAAS4B,GAAGA,CAACgR,MAAM,EAAEC,EAAE,EAAE;EACvB,IAAID,MAAM,CAACF,IAAI,EAAE;IACf,MAAMvS,GAAG,GAAG0S,EAAE,CAACD,MAAM,CAAC5S,KAAK,CAAC;IAC5B,IAAIG,GAAG,YAAY8K,OAAO,EAAE;MAC1B,OAAO9K,GAAG,CAAC+L,IAAI,CAAE4G,MAAM,IAAKP,EAAE,CAACO,MAAM,CAAC,CAAC;IACzC,CAAC,MAAM;MACL,OAAOP,EAAE,CAACpS,GAAG,CAAC;IAChB;EACF;EACA,IAAIyS,MAAM,CAACD,KAAK,EAAE;IAChB,MAAM3S,KAAK,GAAG4S,MAAM,CAAC5S,KAAK;IAC1B,OAAOsS,GAAG,CAACtS,KAAK,CAAC;EACnB;EACA,MAAM,uBAAuB;AAC/B;AACA,IAAIwS,MAAM,GAAII,MAAM,IAAK;EACvB,IAAIA,MAAM,CAACF,IAAI,EAAE;IACf,OAAOE,MAAM,CAAC5S,KAAK;EACrB,CAAC,MAAM;IACL,MAAM4S,MAAM,CAAC5S,KAAK;EACpB;AACF,CAAC;AACD,IAAIyS,SAAS,GAAIG,MAAM,IAAK;EAC1B,IAAIA,MAAM,CAACD,KAAK,EAAE;IAChB,OAAOC,MAAM,CAAC5S,KAAK;EACrB,CAAC,MAAM;IACL,MAAM4S,MAAM,CAAC5S,KAAK;EACpB;AACF,CAAC;;AAED;AACA,SAAS+S,mBAAmBA,CAAC/S,KAAK,EAAE;EAClC,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,CAACgC,UAAU,CAACkI,iBAAiB,CAAC,EAAE;IACrE,OAAOlK,KAAK;EACd;EACA,OAAO4Q,WAAW,CAACE,cAAc,CAAC9P,IAAI,CAACC,KAAK,CAAC+R,IAAI,CAAChT,KAAK,CAAC0B,KAAK,CAACwI,iBAAiB,CAAChI,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5F;AACA,SAAS+Q,gBAAgBA,CAACtI,OAAO,EAAE;EACjC,MAAMuI,UAAU,GAAG,IAAI,CAACC,YAAY,CAAC;IACnC7K,IAAI,EAAE,MAAM;IACZ8K,cAAc,EAAE,CAAC,EAAEzI,OAAO,CAACC,OAAO,GAAG,EAAE,CAAC;EAC1C,CAAC,CAAC;EACF,IAAI4D,gCAAgC,EAAE;IACpC,MAAM6E,KAAK,GAAG,IAAI5E,aAAa,CAAC,CAAC;IACjC4E,KAAK,CAAC3E,WAAW,CAAC1F,YAAY,CAAC;IAC/BkK,UAAU,CAACI,kBAAkB,CAACpE,IAAI,CAACmE,KAAK,CAAC;EAC3C;AACF;AACA,IAAIE,4BAA4B,GAAI7K,GAAG,IAAK;EAC1C,MAAM8K,UAAU,GAAGC,YAAY,CAAC/K,GAAG,EAAE,YAAY,CAAC;EAClD,IAAIA,GAAG,CAACrF,OAAO,IAAIqF,GAAG,CAACrF,OAAO,CAACT,QAAQ,CAAC,GAAG,CAAC,IAAI8F,GAAG,CAAC,MAAM,CAAC,IAAIA,GAAG,CAACrF,OAAO,KAAK,SAAS,EAAE;IACxFqQ,gBAAgB,CAACF,UAAU,EAAE9K,GAAG,CAACrF,OAAO,CAAC,CAAClB,OAAO,CAAEwR,QAAQ,IAAK;MAC9D,IAAIA,QAAQ,CAACC,QAAQ,KAAK,CAAC,CAAC,qBAAqBD,QAAQ,CAACtQ,OAAO,KAAK,SAAS,EAAE;QAC/E,IAAIwQ,oBAAoB,CAACF,QAAQ,EAAEG,WAAW,CAACH,QAAQ,CAAC,EAAE,KAAK,CAAC,CAACzR,MAAM,EAAE;UACvEyR,QAAQ,CAACI,MAAM,GAAG,IAAI;QACxB,CAAC,MAAM;UACLJ,QAAQ,CAACI,MAAM,GAAG,KAAK;QACzB;MACF;IACF,CAAC,CAAC;EACJ;EACA,IAAIzE,EAAE,GAAG,CAAC;EACV,KAAKA,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGkE,UAAU,CAACtR,MAAM,EAAEoN,EAAE,EAAE,EAAE;IACzC,MAAM0E,SAAS,GAAGR,UAAU,CAAClE,EAAE,CAAC;IAChC,IAAI0E,SAAS,CAACJ,QAAQ,KAAK,CAAC,CAAC,qBAAqBH,YAAY,CAACO,SAAS,EAAE,YAAY,CAAC,CAAC9R,MAAM,EAAE;MAC9FqR,4BAA4B,CAACS,SAAS,CAAC;IACzC;EACF;AACF,CAAC;AACD,IAAIC,oBAAoB,GAAIT,UAAU,IAAK;EACzC,MAAMZ,MAAM,GAAG,EAAE;EACjB,KAAK,IAAItD,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGkE,UAAU,CAACtR,MAAM,EAAEoN,EAAE,EAAE,EAAE;IAC7C,MAAM4E,WAAW,GAAGV,UAAU,CAAClE,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC;IACpD,IAAI4E,WAAW,IAAIA,WAAW,CAACC,WAAW,EAAE;MAC1CvB,MAAM,CAAC1D,IAAI,CAACgF,WAAW,CAAC;IAC1B;EACF;EACA,OAAOtB,MAAM;AACf,CAAC;AACD,SAASc,gBAAgBA,CAACF,UAAU,EAAEY,QAAQ,EAAEC,QAAQ,EAAE;EACxD,IAAI/E,EAAE,GAAG,CAAC;EACV,IAAIgF,YAAY,GAAG,EAAE;EACrB,IAAIN,SAAS;EACb,OAAO1E,EAAE,GAAGkE,UAAU,CAACtR,MAAM,EAAEoN,EAAE,EAAE,EAAE;IACnC0E,SAAS,GAAGR,UAAU,CAAClE,EAAE,CAAC;IAC1B,IAAI0E,SAAS,CAAC,MAAM,CAAC,KAAK,CAACI,QAAQ,IAAIJ,SAAS,CAAC,MAAM,CAAC,KAAKI,QAAQ,CAAC,KAAKC,QAAQ,KAAK,KAAK,CAAC,IAAIP,WAAW,CAACE,SAAS,CAAC,KAAKK,QAAQ,CAAC,EAAE;MACtIC,YAAY,CAACpF,IAAI,CAAC8E,SAAS,CAAC;MAC5B,IAAI,OAAOK,QAAQ,KAAK,WAAW,EAAE,OAAOC,YAAY;IAC1D;IACAA,YAAY,GAAG,CAAC,GAAGA,YAAY,EAAE,GAAGZ,gBAAgB,CAACM,SAAS,CAACR,UAAU,EAAEY,QAAQ,EAAEC,QAAQ,CAAC,CAAC;EACjG;EACA,OAAOC,YAAY;AACrB;AACA,IAAIT,oBAAoB,GAAGA,CAACU,IAAI,EAAEF,QAAQ,EAAEG,WAAW,GAAG,IAAI,KAAK;EACjE,MAAMhB,UAAU,GAAG,EAAE;EACrB,IAAIgB,WAAW,IAAID,IAAI,CAAC,MAAM,CAAC,IAAI,CAACA,IAAI,CAAC,MAAM,CAAC,EAAEf,UAAU,CAACtE,IAAI,CAACqF,IAAI,CAAC;EACvE,IAAIE,IAAI,GAAGF,IAAI;EACf,OAAOE,IAAI,GAAGA,IAAI,CAACC,WAAW,EAAE;IAC9B,IAAIZ,WAAW,CAACW,IAAI,CAAC,KAAKJ,QAAQ,KAAKG,WAAW,IAAI,CAACC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAEjB,UAAU,CAACtE,IAAI,CAACuF,IAAI,CAAC;EAC7F;EACA,OAAOjB,UAAU;AACnB,CAAC;AACD,IAAImB,mBAAmB,GAAGA,CAACC,cAAc,EAAEP,QAAQ,KAAK;EACtD,IAAIO,cAAc,CAAChB,QAAQ,KAAK,CAAC,CAAC,mBAAmB;IACnD,IAAIgB,cAAc,CAACrM,YAAY,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI8L,QAAQ,KAAK,EAAE,EAAE;MACnE,OAAO,IAAI;IACb;IACA,IAAIO,cAAc,CAACrM,YAAY,CAAC,MAAM,CAAC,KAAK8L,QAAQ,EAAE;MACpD,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EACA,IAAIO,cAAc,CAAC,MAAM,CAAC,KAAKP,QAAQ,EAAE;IACvC,OAAO,IAAI;EACb;EACA,OAAOA,QAAQ,KAAK,EAAE;AACxB,CAAC;AACD,IAAIQ,mBAAmB,GAAGA,CAACC,QAAQ,EAAEnB,QAAQ,EAAEoB,OAAO,EAAEC,QAAQ,KAAK;EACnE,IAAIF,QAAQ,CAAC,MAAM,CAAC,IAAIA,QAAQ,CAAC,MAAM,CAAC,CAACX,WAAW,EAAE;IACpD;EACF;EACA,MAAMc,mBAAmB,GAAG/Q,QAAQ,CAACgR,cAAc,CAAC,EAAE,CAAC;EACvDD,mBAAmB,CAAC,MAAM,CAAC,GAAGH,QAAQ;EACtC,IAAI,CAACnB,QAAQ,CAAC,MAAM,CAAC,IAAI,CAACA,QAAQ,CAAC,MAAM,CAAC,CAACwB,UAAU,EAAE;EACvD,MAAMC,MAAM,GAAGzB,QAAQ,CAAC,MAAM,CAAC,CAACwB,UAAU;EAC1C,MAAME,YAAY,GAAGN,OAAO,GAAGtB,YAAY,CAAC2B,MAAM,EAAE,SAAS,CAAC,GAAG3B,YAAY,CAAC2B,MAAM,EAAE,aAAa,CAAC;EACpG,IAAI,OAAOJ,QAAQ,KAAK,WAAW,EAAE;IACnCC,mBAAmB,CAAC,MAAM,CAAC,GAAGD,QAAQ;IACtC,MAAMxB,UAAU,GAAGC,YAAY,CAAC2B,MAAM,EAAE,YAAY,CAAC;IACrD,MAAME,iBAAiB,GAAG,CAACL,mBAAmB,CAAC;IAC/CzB,UAAU,CAACrR,OAAO,CAAEoT,CAAC,IAAK;MACxB,IAAIA,CAAC,CAAC,MAAM,CAAC,EAAED,iBAAiB,CAACpG,IAAI,CAACqG,CAAC,CAAC;IAC1C,CAAC,CAAC;IACFD,iBAAiB,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC/B,IAAI,CAACD,CAAC,CAAC,MAAM,CAAC,IAAIA,CAAC,CAAC,MAAM,CAAC,IAAIC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,KACrD,IAAI,CAACA,CAAC,CAAC,MAAM,CAAC,IAAIA,CAAC,CAAC,MAAM,CAAC,GAAGD,CAAC,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC;MACtD,OAAO,CAAC;IACV,CAAC,CAAC;IACFH,iBAAiB,CAACnT,OAAO,CAAEoT,CAAC,IAAKF,YAAY,CAAC7O,IAAI,CAAC4O,MAAM,EAAEG,CAAC,CAAC,CAAC;EAChE,CAAC,MAAM;IACLF,YAAY,CAAC7O,IAAI,CAAC4O,MAAM,EAAEH,mBAAmB,CAAC;EAChD;EACAH,QAAQ,CAAC,MAAM,CAAC,GAAGG,mBAAmB;EACtCH,QAAQ,CAAC,MAAM,CAAC,GAAGnB,QAAQ,CAAC,MAAM,CAAC;AACrC,CAAC;AACD,IAAIG,WAAW,GAAIW,IAAI,IAAK,OAAOA,IAAI,CAAC,MAAM,CAAC,KAAK,QAAQ,GAAGA,IAAI,CAAC,MAAM,CAAC,GAAGA,IAAI,CAACb,QAAQ,KAAK,CAAC,IAAIa,IAAI,CAAClM,YAAY,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC;AACxI,SAASoN,aAAaA,CAAClB,IAAI,EAAE;EAC3B,IAAIA,IAAI,CAACmB,gBAAgB,IAAInB,IAAI,CAACoB,aAAa,IAAI,CAACpB,IAAI,CAAC,MAAM,CAAC,EAAE;EAClE,MAAMqB,eAAe,GAAIC,YAAY,IAAM,UAASnI,IAAI,EAAE;IACxD,MAAMoI,QAAQ,GAAG,EAAE;IACnB,MAAM3B,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;IAC7B,IAAIzG,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACqI,OAAO,EAAE;MACxCpT,OAAO,CAACI,KAAK,CAAC;AACpB;AACA;AACA;AACA,SAAS,CAAC;IACN;IACA,MAAMmS,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAACtM,aAAa;IACzC,MAAMwL,YAAY,GAAGc,MAAM,CAACc,YAAY,GAAGd,MAAM,CAAC5B,UAAU,GAAGS,oBAAoB,CAACmB,MAAM,CAAC5B,UAAU,CAAC;IACtGc,YAAY,CAACnS,OAAO,CAAEoT,CAAC,IAAK;MAC1B,IAAIlB,QAAQ,KAAKP,WAAW,CAACyB,CAAC,CAAC,EAAE;QAC/BS,QAAQ,CAAC9G,IAAI,CAACqG,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;IACF,IAAIQ,YAAY,EAAE;MAChB,OAAOC,QAAQ,CAACjU,MAAM,CAAEwT,CAAC,IAAKA,CAAC,CAAC3B,QAAQ,KAAK,CAAC,CAAC,iBAAiB,CAAC;IACnE;IACA,OAAOoC,QAAQ;EACjB,CAAC,CAAEG,IAAI,CAAC1B,IAAI,CAAC;EACbA,IAAI,CAACmB,gBAAgB,GAAGE,eAAe,CAAC,IAAI,CAAC;EAC7CrB,IAAI,CAACoB,aAAa,GAAGC,eAAe,CAAC,KAAK,CAAC;AAC7C;AACA,SAASM,uBAAuBA,CAAC1N,GAAG,EAAE;EACpCA,GAAG,CAAC2N,aAAa,CAAC,IAAIpI,WAAW,CAAC,YAAY,EAAE;IAAEqI,OAAO,EAAE,KAAK;IAAEC,UAAU,EAAE,KAAK;IAAEC,QAAQ,EAAE;EAAM,CAAC,CAAC,CAAC;AAC1G;AACA,SAASC,uBAAuBA,CAACvC,WAAW,EAAEwC,UAAU,EAAE;EACxD,IAAInQ,EAAE;EACNmQ,UAAU,GAAGA,UAAU,KAAK,CAACnQ,EAAE,GAAG2N,WAAW,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG3N,EAAE,CAACuC,aAAa,CAAC;EAC3F,IAAI,CAAC4N,UAAU,EAAE,OAAO;IAAE/C,QAAQ,EAAE,IAAI;IAAEU,QAAQ,EAAE;EAAG,CAAC;EACxD,MAAMA,QAAQ,GAAGH,WAAW,CAAC,MAAM,CAAC,GAAGJ,WAAW,CAACI,WAAW,CAAC,IAAI,EAAE;EACrE,MAAMV,UAAU,GAAGC,YAAY,CAACiD,UAAU,EAAE,YAAY,CAAC;EACzD,MAAM/C,QAAQ,GAAGD,gBAAgB,CAACF,UAAU,EAAEkD,UAAU,CAACrT,OAAO,EAAEgR,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9E,OAAO;IAAEV,QAAQ;IAAEU;EAAS,CAAC;AAC/B;;AAEA;AACA,IAAIsC,oBAAoB,GAAIC,oBAAoB,IAAK;EACnDC,cAAc,CAACD,oBAAoB,CAAC;EACpCE,oBAAoB,CAACF,oBAAoB,CAAC;EAC1CG,eAAe,CAACH,oBAAoB,CAAC;EACrCI,gBAAgB,CAACJ,oBAAoB,CAAC;EACtCK,8BAA8B,CAACL,oBAAoB,CAAC;EACpDM,2BAA2B,CAACN,oBAAoB,CAAC;EACjDO,2BAA2B,CAACP,oBAAoB,CAAC;EACjDQ,iBAAiB,CAACR,oBAAoB,CAAC;EACvCS,gBAAgB,CAACT,oBAAoB,CAAC;EACtCU,mBAAmB,CAACV,oBAAoB,CAAC;EACzCW,oBAAoB,CAACX,oBAAoB,CAAC;AAC5C,CAAC;AACD,IAAIC,cAAc,GAAIW,oBAAoB,IAAK;EAC7C,MAAMC,YAAY,GAAGD,oBAAoB,CAACE,SAAS;EACnDF,oBAAoB,CAACE,SAAS,GAAG,UAASC,IAAI,EAAE;IAC9C,MAAMC,OAAO,GAAG,IAAI;IACpB,MAAMC,WAAW,GAAGD,OAAO,CAAC1E,UAAU,IAAIhF,cAAc;IACxD,MAAM4J,UAAU,GAAGL,YAAY,CAACjR,IAAI,CAACoR,OAAO,EAAEC,WAAW,GAAGF,IAAI,GAAG,KAAK,CAAC;IACzE,IAAI,CAACE,WAAW,IAAIF,IAAI,EAAE;MACxB,IAAIrI,EAAE,GAAG,CAAC;MACV,IAAIyI,OAAO,EAAEC,cAAc;MAC3B,MAAMC,eAAe,GAAG,CACtB,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,CACR;MACD,MAAMzE,UAAU,GAAG,IAAI,CAAC0C,YAAY,IAAI,IAAI,CAAC1C,UAAU;MACvD,OAAOlE,EAAE,GAAGkE,UAAU,CAACtR,MAAM,EAAEoN,EAAE,EAAE,EAAE;QACnCyI,OAAO,GAAGvE,UAAU,CAAClE,EAAE,CAAC,CAAC,MAAM,CAAC;QAChC0I,cAAc,GAAGC,eAAe,CAACC,KAAK,CAAEC,YAAY,IAAK,CAAC3E,UAAU,CAAClE,EAAE,CAAC,CAAC6I,YAAY,CAAC,CAAC;QACvF,IAAIJ,OAAO,EAAE;UACX,IAAID,UAAU,CAACM,aAAa,EAAE;YAC5BN,UAAU,CAACM,aAAa,CAACL,OAAO,CAACL,SAAS,CAAC,IAAI,CAAC,CAAC;UACnD,CAAC,MAAM;YACLI,UAAU,CAACO,WAAW,CAACN,OAAO,CAACL,SAAS,CAAC,IAAI,CAAC,CAAC;UACjD;QACF;QACA,IAAIM,cAAc,EAAE;UAClBF,UAAU,CAACO,WAAW,CAAC7E,UAAU,CAAClE,EAAE,CAAC,CAACoI,SAAS,CAAC,IAAI,CAAC,CAAC;QACxD;MACF;IACF;IACA,OAAOI,UAAU;EACnB,CAAC;AACH,CAAC;AACD,IAAIhB,oBAAoB,GAAIU,oBAAoB,IAAK;EACnDA,oBAAoB,CAACY,aAAa,GAAGZ,oBAAoB,CAACa,WAAW;EACrEb,oBAAoB,CAACa,WAAW,GAAG,UAASvD,QAAQ,EAAE;IACpD,MAAM;MAAET,QAAQ;MAAEV;IAAS,CAAC,GAAG8C,uBAAuB,CAAC3B,QAAQ,EAAE,IAAI,CAAC;IACtE,IAAInB,QAAQ,EAAE;MACZkB,mBAAmB,CAACC,QAAQ,EAAEnB,QAAQ,CAAC;MACvC,MAAM2E,cAAc,GAAGzE,oBAAoB,CAACF,QAAQ,EAAEU,QAAQ,CAAC;MAC/D,MAAMkE,WAAW,GAAGD,cAAc,CAACA,cAAc,CAACpW,MAAM,GAAG,CAAC,CAAC;MAC7D,MAAMkT,MAAM,GAAG3B,YAAY,CAAC8E,WAAW,EAAE,YAAY,CAAC;MACtD,MAAMC,YAAY,GAAG/E,YAAY,CAAC2B,MAAM,EAAE,cAAc,CAAC,CAACN,QAAQ,EAAEyD,WAAW,CAAC7D,WAAW,CAAC;MAC5F0B,uBAAuB,CAACzC,QAAQ,CAAC;MACjCJ,4BAA4B,CAAC,IAAI,CAAC;MAClC,OAAOiF,YAAY;IACrB;IACA,OAAO,IAAI,CAACJ,aAAa,CAACtD,QAAQ,CAAC;EACrC,CAAC;AACH,CAAC;AACD,IAAIyC,oBAAoB,GAAIkB,gBAAgB,IAAK;EAC/CA,gBAAgB,CAACC,aAAa,GAAGD,gBAAgB,CAACE,WAAW;EAC7DF,gBAAgB,CAACE,WAAW,GAAG,UAASC,QAAQ,EAAE;IAChD,IAAIA,QAAQ,IAAI,OAAOA,QAAQ,CAAC,MAAM,CAAC,KAAK,WAAW,EAAE;MACvD,MAAMpF,UAAU,GAAG,IAAI,CAAC0C,YAAY,IAAI,IAAI,CAAC1C,UAAU;MACvD,MAAMG,QAAQ,GAAGD,gBAAgB,CAACF,UAAU,EAAE,IAAI,CAACnQ,OAAO,EAAEuV,QAAQ,CAAC,MAAM,CAAC,CAAC;MAC7E,IAAIjF,QAAQ,IAAIiF,QAAQ,CAACzE,WAAW,EAAE;QACpCyE,QAAQ,CAACC,MAAM,CAAC,CAAC;QACjBtF,4BAA4B,CAAC,IAAI,CAAC;QAClC;MACF;IACF;IACA,OAAO,IAAI,CAACmF,aAAa,CAACE,QAAQ,CAAC;EACrC,CAAC;AACH,CAAC;AACD,IAAI5B,gBAAgB,GAAIQ,oBAAoB,IAAK;EAC/CA,oBAAoB,CAACsB,SAAS,GAAGtB,oBAAoB,CAACzC,OAAO;EAC7DyC,oBAAoB,CAACzC,OAAO,GAAG,UAAS,GAAGgE,WAAW,EAAE;IACtDA,WAAW,CAAC5W,OAAO,CAAE2S,QAAQ,IAAK;MAChC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QAChCA,QAAQ,GAAG,IAAI,CAACkE,aAAa,CAAC9D,cAAc,CAACJ,QAAQ,CAAC;MACxD;MACA,MAAMT,QAAQ,GAAG,CAACS,QAAQ,CAAC,MAAM,CAAC,GAAGhB,WAAW,CAACgB,QAAQ,CAAC,KAAK,EAAE;MACjE,MAAMtB,UAAU,GAAGC,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC;MACnD,MAAME,QAAQ,GAAGD,gBAAgB,CAACF,UAAU,EAAE,IAAI,CAACnQ,OAAO,EAAEgR,QAAQ,CAAC,CAAC,CAAC,CAAC;MACxE,IAAIV,QAAQ,EAAE;QACZkB,mBAAmB,CAACC,QAAQ,EAAEnB,QAAQ,EAAE,IAAI,CAAC;QAC7C,MAAM2E,cAAc,GAAGzE,oBAAoB,CAACF,QAAQ,EAAEU,QAAQ,CAAC;QAC/D,MAAMkE,WAAW,GAAGD,cAAc,CAAC,CAAC,CAAC;QACrC,MAAMlD,MAAM,GAAG3B,YAAY,CAAC8E,WAAW,EAAE,YAAY,CAAC;QACtD,MAAMvC,QAAQ,GAAGvC,YAAY,CAAC2B,MAAM,EAAE,cAAc,CAAC,CAACN,QAAQ,EAAErB,YAAY,CAAC8E,WAAW,EAAE,aAAa,CAAC,CAAC;QACzGnC,uBAAuB,CAACzC,QAAQ,CAAC;QACjC,OAAOqC,QAAQ;MACjB;MACA,IAAIlB,QAAQ,CAAClB,QAAQ,KAAK,CAAC,IAAI,CAAC,CAACkB,QAAQ,CAACvM,YAAY,CAAC,MAAM,CAAC,EAAE;QAC9DuM,QAAQ,CAACf,MAAM,GAAG,IAAI;MACxB;MACA,OAAOyD,oBAAoB,CAACsB,SAAS,CAAChE,QAAQ,CAAC;IACjD,CAAC,CAAC;EACJ,CAAC;AACH,CAAC;AACD,IAAIiC,eAAe,GAAIS,oBAAoB,IAAK;EAC9CA,oBAAoB,CAACyB,QAAQ,GAAGzB,oBAAoB,CAAC0B,MAAM;EAC3D1B,oBAAoB,CAAC0B,MAAM,GAAG,UAAS,GAAGH,WAAW,EAAE;IACrDA,WAAW,CAAC5W,OAAO,CAAE2S,QAAQ,IAAK;MAChC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QAChCA,QAAQ,GAAG,IAAI,CAACkE,aAAa,CAAC9D,cAAc,CAACJ,QAAQ,CAAC;MACxD;MACA,IAAI,CAACuD,WAAW,CAACvD,QAAQ,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC;AACH,CAAC;AACD,IAAIoC,2BAA2B,GAAIM,oBAAoB,IAAK;EAC1D,MAAM2B,0BAA0B,GAAG3B,oBAAoB,CAAC4B,kBAAkB;EAC1E5B,oBAAoB,CAAC4B,kBAAkB,GAAG,UAASpE,QAAQ,EAAErE,IAAI,EAAE;IACjE,IAAIqE,QAAQ,KAAK,YAAY,IAAIA,QAAQ,KAAK,WAAW,EAAE;MACzD,OAAOmE,0BAA0B,CAAC3S,IAAI,CAAC,IAAI,EAAEwO,QAAQ,EAAErE,IAAI,CAAC;IAC9D;IACA,MAAM0I,SAAS,GAAG,IAAI,CAACL,aAAa,CAACM,aAAa,CAAC,GAAG,CAAC;IACvD,IAAI7E,IAAI;IACR4E,SAAS,CAACE,SAAS,GAAG5I,IAAI;IAC1B,IAAIqE,QAAQ,KAAK,YAAY,EAAE;MAC7B,OAAOP,IAAI,GAAG4E,SAAS,CAACG,UAAU,EAAE;QAClC,IAAI,CAACzE,OAAO,CAACN,IAAI,CAAC;MACpB;IACF,CAAC,MAAM,IAAIO,QAAQ,KAAK,WAAW,EAAE;MACnC,OAAOP,IAAI,GAAG4E,SAAS,CAACG,UAAU,EAAE;QAClC,IAAI,CAACN,MAAM,CAACzE,IAAI,CAAC;MACnB;IACF;EACF,CAAC;AACH,CAAC;AACD,IAAI0C,2BAA2B,GAAIK,oBAAoB,IAAK;EAC1DA,oBAAoB,CAACiC,kBAAkB,GAAG,UAASzE,QAAQ,EAAErE,IAAI,EAAE;IACjE,IAAI,CAACyI,kBAAkB,CAACpE,QAAQ,EAAErE,IAAI,CAAC;EACzC,CAAC;AACH,CAAC;AACD,IAAIyG,iBAAiB,GAAII,oBAAoB,IAAK;EAChD,MAAMkC,QAAQ,GAAGlC,oBAAoB;EACrC,IAAIkC,QAAQ,CAACC,cAAc,EAAE;EAC7BD,QAAQ,CAACC,cAAc,GAAGnC,oBAAoB,CAACoC,YAAY;EAC3DpC,oBAAoB,CAACoC,YAAY,GAAG,UAAS9E,QAAQ,EAAE+E,YAAY,EAAE;IACnE,MAAM;MAAExF,QAAQ;MAAEV;IAAS,CAAC,GAAG8C,uBAAuB,CAAC3B,QAAQ,EAAE,IAAI,CAAC;IACtE,MAAMR,YAAY,GAAG,IAAI,CAAC4B,YAAY,GAAG,IAAI,CAAC1C,UAAU,GAAGS,oBAAoB,CAAC,IAAI,CAACT,UAAU,CAAC;IAChG,IAAIG,QAAQ,EAAE;MACZ,IAAImG,KAAK,GAAG,KAAK;MACjBxF,YAAY,CAACnS,OAAO,CAAE6R,SAAS,IAAK;QAClC,IAAIA,SAAS,KAAK6F,YAAY,IAAIA,YAAY,KAAK,IAAI,EAAE;UACvDC,KAAK,GAAG,IAAI;UACZ,IAAID,YAAY,KAAK,IAAI,IAAIxF,QAAQ,KAAKwF,YAAY,CAAC,MAAM,CAAC,EAAE;YAC9D,IAAI,CAACxB,WAAW,CAACvD,QAAQ,CAAC;YAC1B;UACF;UACA,IAAIT,QAAQ,KAAKwF,YAAY,CAAC,MAAM,CAAC,EAAE;YACrChF,mBAAmB,CAACC,QAAQ,EAAEnB,QAAQ,CAAC;YACvC,MAAMyB,MAAM,GAAG3B,YAAY,CAACoG,YAAY,EAAE,YAAY,CAAC;YACvDpG,YAAY,CAAC2B,MAAM,EAAE,cAAc,CAAC,CAACN,QAAQ,EAAE+E,YAAY,CAAC;YAC5DzD,uBAAuB,CAACzC,QAAQ,CAAC;UACnC;UACA;QACF;MACF,CAAC,CAAC;MACF,IAAImG,KAAK,EAAE,OAAOhF,QAAQ;IAC5B;IACA,MAAMK,UAAU,GAAG0E,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACE,YAAY;IAC5E,IAAI5E,UAAU,IAAI,CAAC,IAAI,CAAC6E,UAAU,CAAC7E,UAAU,CAAC,EAAE;MAC9C,OAAO,IAAI,CAACkD,WAAW,CAACvD,QAAQ,CAAC;IACnC;IACA,OAAO,IAAI,CAAC6E,cAAc,CAAC7E,QAAQ,EAAE+E,YAAY,CAAC;EACpD,CAAC;AACH,CAAC;AACD,IAAI5C,8BAA8B,GAAIO,oBAAoB,IAAK;EAC7D,MAAMyC,6BAA6B,GAAGzC,oBAAoB,CAAC0C,qBAAqB;EAChF1C,oBAAoB,CAAC0C,qBAAqB,GAAG,UAASlF,QAAQ,EAAEmF,OAAO,EAAE;IACvE,IAAInF,QAAQ,KAAK,YAAY,IAAIA,QAAQ,KAAK,WAAW,EAAE;MACzD,OAAOiF,6BAA6B,CAACzT,IAAI,CAAC,IAAI,EAAEwO,QAAQ,EAAEmF,OAAO,CAAC;IACpE;IACA,IAAInF,QAAQ,KAAK,YAAY,EAAE;MAC7B,IAAI,CAACD,OAAO,CAACoF,OAAO,CAAC;MACrB,OAAOA,OAAO;IAChB,CAAC,MAAM,IAAInF,QAAQ,KAAK,WAAW,EAAE;MACnC,IAAI,CAACkE,MAAM,CAACiB,OAAO,CAAC;MACpB,OAAOA,OAAO;IAChB;IACA,OAAOA,OAAO;EAChB,CAAC;AACH,CAAC;AACD,IAAI9C,gBAAgB,GAAIT,oBAAoB,IAAK;EAC/CwD,yBAAyB,CAAC,aAAa,EAAExD,oBAAoB,CAAC;EAC9DjX,MAAM,CAACuJ,cAAc,CAAC0N,oBAAoB,EAAE,aAAa,EAAE;IACzD/W,GAAG,EAAE,SAAAA,CAAA,EAAW;MACd,IAAI8Q,IAAI,GAAG,EAAE;MACb,MAAM6C,UAAU,GAAG,IAAI,CAAC0C,YAAY,GAAG,IAAI,CAAC1C,UAAU,GAAGS,oBAAoB,CAAC,IAAI,CAACT,UAAU,CAAC;MAC9FA,UAAU,CAACrR,OAAO,CAAEsS,IAAI,IAAK9D,IAAI,IAAI8D,IAAI,CAAC4F,WAAW,IAAI,EAAE,CAAC;MAC5D,OAAO1J,IAAI;IACb,CAAC;IACDnQ,GAAG,EAAE,SAAAA,CAASR,KAAK,EAAE;MACnB,MAAMwT,UAAU,GAAG,IAAI,CAAC0C,YAAY,GAAG,IAAI,CAAC1C,UAAU,GAAGS,oBAAoB,CAAC,IAAI,CAACT,UAAU,CAAC;MAC9FA,UAAU,CAACrR,OAAO,CAAEsS,IAAI,IAAK;QAC3B,IAAIA,IAAI,CAAC,MAAM,CAAC,EAAEA,IAAI,CAAC,MAAM,CAAC,CAACoE,MAAM,CAAC,CAAC;QACvCpE,IAAI,CAACoE,MAAM,CAAC,CAAC;MACf,CAAC,CAAC;MACF,IAAI,CAACO,kBAAkB,CAAC,WAAW,EAAEpZ,KAAK,CAAC;IAC7C;EACF,CAAC,CAAC;AACJ,CAAC;AACD,IAAIsX,mBAAmB,GAAI5O,GAAG,IAAK;EACjC,MAAM4R,YAAY,SAASC,KAAK,CAAC;IAC/BpJ,IAAIA,CAACoE,CAAC,EAAE;MACN,OAAO,IAAI,CAACA,CAAC,CAAC;IAChB;EACF;EACA6E,yBAAyB,CAAC,UAAU,EAAE1R,GAAG,CAAC;EAC1C/I,MAAM,CAACuJ,cAAc,CAACR,GAAG,EAAE,UAAU,EAAE;IACrC7I,GAAGA,CAAA,EAAG;MACJ,OAAO,IAAI,CAAC2T,UAAU,CAACzR,MAAM,CAAEwT,CAAC,IAAKA,CAAC,CAAC3B,QAAQ,KAAK,CAAC,CAAC;IACxD;EACF,CAAC,CAAC;EACFjU,MAAM,CAACuJ,cAAc,CAACR,GAAG,EAAE,mBAAmB,EAAE;IAC9C7I,GAAGA,CAAA,EAAG;MACJ,OAAO,IAAI,CAAC2a,QAAQ,CAACtY,MAAM;IAC7B;EACF,CAAC,CAAC;EACFkY,yBAAyB,CAAC,YAAY,EAAE1R,GAAG,CAAC;EAC5C/I,MAAM,CAACuJ,cAAc,CAACR,GAAG,EAAE,YAAY,EAAE;IACvC7I,GAAGA,CAAA,EAAG;MACJ,OAAO,IAAI,CAAC2T,UAAU,CAAC,CAAC,CAAC;IAC3B;EACF,CAAC,CAAC;EACF4G,yBAAyB,CAAC,WAAW,EAAE1R,GAAG,CAAC;EAC3C/I,MAAM,CAACuJ,cAAc,CAACR,GAAG,EAAE,WAAW,EAAE;IACtC7I,GAAGA,CAAA,EAAG;MACJ,OAAO,IAAI,CAAC2T,UAAU,CAAC,IAAI,CAACA,UAAU,CAACtR,MAAM,GAAG,CAAC,CAAC;IACpD;EACF,CAAC,CAAC;EACFkY,yBAAyB,CAAC,YAAY,EAAE1R,GAAG,CAAC;EAC5C/I,MAAM,CAACuJ,cAAc,CAACR,GAAG,EAAE,YAAY,EAAE;IACvC7I,GAAGA,CAAA,EAAG;MACJ,MAAM+S,MAAM,GAAG,IAAI0H,YAAY,CAAC,CAAC;MACjC1H,MAAM,CAAC1D,IAAI,CAAC,GAAG+E,oBAAoB,CAAC,IAAI,CAACiC,YAAY,CAAC,CAAC;MACvD,OAAOtD,MAAM;IACf;EACF,CAAC,CAAC;AACJ,CAAC;AACD,IAAI6H,gBAAgB,GAAIhG,IAAI,IAAK;EAC/B,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACiG,aAAa,KAAK,KAAK,CAAC,IAAI,CAACC,UAAU,CAACC,IAAI,EAAE;EAChEC,gBAAgB,CAACpG,IAAI,CAAC;EACtBqG,oBAAoB,CAACrG,IAAI,CAAC;EAC1BsG,eAAe,CAACtG,IAAI,CAAC;EACrB,IAAIA,IAAI,CAACb,QAAQ,KAAKgH,IAAI,CAACI,YAAY,EAAE;IACvCC,uBAAuB,CAACxG,IAAI,CAAC;IAC7ByG,2BAA2B,CAACzG,IAAI,CAAC;EACnC;AACF,CAAC;AACD,IAAIoG,gBAAgB,GAAIpG,IAAI,IAAK;EAC/B,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACiG,aAAa,EAAE;EACjCN,yBAAyB,CAAC,aAAa,EAAE3F,IAAI,CAAC;EAC9C9U,MAAM,CAACuJ,cAAc,CAACuL,IAAI,EAAE,aAAa,EAAE;IACzC5U,GAAG,EAAE,SAAAA,CAAA,EAAW;MACd,IAAI0G,EAAE;MACN,MAAM4U,WAAW,GAAG,CAAC5U,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4O,UAAU,CAAC3B,UAAU;MACnF,MAAM4H,KAAK,GAAGD,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACE,OAAO,CAAC,IAAI,CAAC;MACtE,IAAIF,WAAW,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE;QAC7B,OAAOD,WAAW,CAACC,KAAK,GAAG,CAAC,CAAC;MAC/B;MACA,OAAO,IAAI,CAACV,aAAa;IAC3B;EACF,CAAC,CAAC;AACJ,CAAC;AACD,IAAIO,uBAAuB,GAAId,OAAO,IAAK;EACzC,IAAI,CAACA,OAAO,IAAIA,OAAO,CAACmB,oBAAoB,EAAE;EAC9ClB,yBAAyB,CAAC,oBAAoB,EAAED,OAAO,CAAC;EACxDxa,MAAM,CAACuJ,cAAc,CAACiR,OAAO,EAAE,oBAAoB,EAAE;IACnDta,GAAG,EAAE,SAAAA,CAAA,EAAW;MACd,IAAI0G,EAAE;MACN,MAAMgV,UAAU,GAAG,CAAChV,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4O,UAAU,CAACqF,QAAQ;MAChF,MAAMY,KAAK,GAAGG,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACF,OAAO,CAAC,IAAI,CAAC;MACpE,IAAIE,UAAU,IAAIH,KAAK,GAAG,CAAC,CAAC,EAAE;QAC5B,OAAOG,UAAU,CAACH,KAAK,GAAG,CAAC,CAAC;MAC9B;MACA,OAAO,IAAI,CAACE,oBAAoB;IAClC;EACF,CAAC,CAAC;AACJ,CAAC;AACD,IAAIR,oBAAoB,GAAIrG,IAAI,IAAK;EACnC,IAAI,CAACA,IAAI,IAAIA,IAAI,CAAC+G,iBAAiB,EAAE;EACrCpB,yBAAyB,CAAC,iBAAiB,EAAE3F,IAAI,CAAC;EAClD9U,MAAM,CAACuJ,cAAc,CAACuL,IAAI,EAAE,iBAAiB,EAAE;IAC7C5U,GAAG,EAAE,SAAAA,CAAA,EAAW;MACd,IAAI0G,EAAE;MACN,MAAM4U,WAAW,GAAG,CAAC5U,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4O,UAAU,CAAC3B,UAAU;MACnF,MAAM4H,KAAK,GAAGD,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACE,OAAO,CAAC,IAAI,CAAC;MACtE,IAAIF,WAAW,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE;QAC7B,OAAOD,WAAW,CAACC,KAAK,GAAG,CAAC,CAAC;MAC/B;MACA,OAAO,IAAI,CAACI,iBAAiB;IAC/B;EACF,CAAC,CAAC;AACJ,CAAC;AACD,IAAIN,2BAA2B,GAAIf,OAAO,IAAK;EAC7C,IAAI,CAACA,OAAO,IAAIA,OAAO,CAACsB,wBAAwB,EAAE;EAClDrB,yBAAyB,CAAC,wBAAwB,EAAED,OAAO,CAAC;EAC5Dxa,MAAM,CAACuJ,cAAc,CAACiR,OAAO,EAAE,wBAAwB,EAAE;IACvDta,GAAG,EAAE,SAAAA,CAAA,EAAW;MACd,IAAI0G,EAAE;MACN,MAAM4U,WAAW,GAAG,CAAC5U,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4O,UAAU,CAACqF,QAAQ;MACjF,MAAMY,KAAK,GAAGD,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACE,OAAO,CAAC,IAAI,CAAC;MACtE,IAAIF,WAAW,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE;QAC7B,OAAOD,WAAW,CAACC,KAAK,GAAG,CAAC,CAAC;MAC/B;MACA,OAAO,IAAI,CAACK,wBAAwB;IACtC;EACF,CAAC,CAAC;AACJ,CAAC;AACD,IAAIV,eAAe,GAAItG,IAAI,IAAK;EAC9B,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACsF,YAAY,EAAE;EAChCK,yBAAyB,CAAC,YAAY,EAAE3F,IAAI,CAAC;EAC7C9U,MAAM,CAACuJ,cAAc,CAACuL,IAAI,EAAE,YAAY,EAAE;IACxC5U,GAAG,EAAE,SAAAA,CAAA,EAAW;MACd,IAAI0G,EAAE;MACN,OAAO,CAAC,CAACA,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4O,UAAU,KAAK,IAAI,CAAC4E,YAAY;IACpF,CAAC;IACDvZ,GAAG,EAAE,SAAAA,CAASR,KAAK,EAAE;MACnB,IAAI,CAAC+Z,YAAY,GAAG/Z,KAAK;IAC3B;EACF,CAAC,CAAC;AACJ,CAAC;AACD,IAAI0b,mBAAmB,GAAG,CAAC,UAAU,EAAE,oBAAoB,EAAE,wBAAwB,CAAC;AACtF,IAAIC,iBAAiB,GAAG,CACtB,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,aAAa,EACb,iBAAiB,EACjB,aAAa,EACb,YAAY,CACb;AACD,SAASvB,yBAAyBA,CAACwB,YAAY,EAAEnH,IAAI,EAAE;EACrD,IAAIoH,QAAQ;EACZ,IAAIH,mBAAmB,CAAC9Y,QAAQ,CAACgZ,YAAY,CAAC,EAAE;IAC9CC,QAAQ,GAAGlc,MAAM,CAACmc,wBAAwB,CAACC,OAAO,CAACC,SAAS,EAAEJ,YAAY,CAAC;EAC7E,CAAC,MAAM,IAAID,iBAAiB,CAAC/Y,QAAQ,CAACgZ,YAAY,CAAC,EAAE;IACnDC,QAAQ,GAAGlc,MAAM,CAACmc,wBAAwB,CAAClB,IAAI,CAACoB,SAAS,EAAEJ,YAAY,CAAC;EAC1E;EACA,IAAI,CAACC,QAAQ,EAAE;IACbA,QAAQ,GAAGlc,MAAM,CAACmc,wBAAwB,CAACrH,IAAI,EAAEmH,YAAY,CAAC;EAChE;EACA,IAAIC,QAAQ,EAAElc,MAAM,CAACuJ,cAAc,CAACuL,IAAI,EAAE,IAAI,GAAGmH,YAAY,EAAEC,QAAQ,CAAC;AAC1E;AACA,SAASpI,YAAYA,CAACgB,IAAI,EAAEwH,MAAM,EAAE;EAClC,IAAI,IAAI,GAAGA,MAAM,IAAIxH,IAAI,EAAE;IACzB,MAAMuB,QAAQ,GAAGvB,IAAI,CAAC,IAAI,GAAGwH,MAAM,CAAC;IACpC,IAAI,OAAOjG,QAAQ,KAAK,UAAU,EAAE,OAAOA,QAAQ;IACnD,OAAOA,QAAQ,CAACG,IAAI,CAAC1B,IAAI,CAAC;EAC5B,CAAC,MAAM;IACL,IAAI,OAAOA,IAAI,CAACwH,MAAM,CAAC,KAAK,UAAU,EAAE,OAAOxH,IAAI,CAACwH,MAAM,CAAC;IAC3D,OAAOxH,IAAI,CAACwH,MAAM,CAAC,CAAC9F,IAAI,CAAC1B,IAAI,CAAC;EAChC;AACF;AACA,IAAIyH,UAAU,GAAGA,CAACC,MAAM,EAAE9Y,OAAO,GAAG,EAAE,KAAK;EACzC;IACE,OAAO,MAAM;MACX;IACF,CAAC;EACH;AACF,CAAC;AACD,IAAI+Y,UAAU,GAAGA,CAACtc,GAAG,EAAEuc,WAAW,KAAK;EACrC;IACE,OAAO,MAAM;MACX;IACF,CAAC;EACH;AACF,CAAC;AACD,IAAIC,CAAC,GAAGA,CAACC,QAAQ,EAAEC,SAAS,EAAE,GAAGhC,QAAQ,KAAK;EAC5C,IAAIiC,KAAK,GAAG,IAAI;EAChB,IAAI3c,GAAG,GAAG,IAAI;EACd,IAAIuU,QAAQ,GAAG,IAAI;EACnB,IAAIqI,MAAM,GAAG,KAAK;EAClB,IAAIC,UAAU,GAAG,KAAK;EACtB,MAAMC,aAAa,GAAG,EAAE;EACxB,MAAMC,IAAI,GAAIzb,CAAC,IAAK;IAClB,KAAK,IAAIkO,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGlO,CAAC,CAACc,MAAM,EAAEoN,EAAE,EAAE,EAAE;MACpCmN,KAAK,GAAGrb,CAAC,CAACkO,EAAE,CAAC;MACb,IAAIiL,KAAK,CAACuC,OAAO,CAACL,KAAK,CAAC,EAAE;QACxBI,IAAI,CAACJ,KAAK,CAAC;MACb,CAAC,MAAM,IAAIA,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE;QACtD,IAAIC,MAAM,GAAG,OAAOH,QAAQ,KAAK,UAAU,IAAI,CAACpM,aAAa,CAACsM,KAAK,CAAC,EAAE;UACpEA,KAAK,GAAGM,MAAM,CAACN,KAAK,CAAC;QACvB;QACA,IAAIC,MAAM,IAAIC,UAAU,EAAE;UACxBC,aAAa,CAACA,aAAa,CAAC1a,MAAM,GAAG,CAAC,CAAC,CAAC8a,MAAM,IAAIP,KAAK;QACzD,CAAC,MAAM;UACLG,aAAa,CAAC1N,IAAI,CAACwN,MAAM,GAAGO,QAAQ,CAAC,IAAI,EAAER,KAAK,CAAC,GAAGA,KAAK,CAAC;QAC5D;QACAE,UAAU,GAAGD,MAAM;MACrB;IACF;EACF,CAAC;EACDG,IAAI,CAACrC,QAAQ,CAAC;EACd,IAAIgC,SAAS,EAAE;IACb,IAAIA,SAAS,CAAC1c,GAAG,EAAE;MACjBA,GAAG,GAAG0c,SAAS,CAAC1c,GAAG;IACrB;IACA,IAAI0c,SAAS,CAAClT,IAAI,EAAE;MAClB+K,QAAQ,GAAGmI,SAAS,CAAClT,IAAI;IAC3B;IACA;MACE,MAAM4T,SAAS,GAAGV,SAAS,CAACW,SAAS,IAAIX,SAAS,CAACY,KAAK;MACxD,IAAIF,SAAS,EAAE;QACbV,SAAS,CAACY,KAAK,GAAG,OAAOF,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAGvd,MAAM,CAAC4E,IAAI,CAAC2Y,SAAS,CAAC,CAACnb,MAAM,CAAEsb,CAAC,IAAKH,SAAS,CAACG,CAAC,CAAC,CAAC,CAAC9Z,IAAI,CAAC,GAAG,CAAC;MAC5H;IACF;EACF;EACA,IAAI,OAAOgZ,QAAQ,KAAK,UAAU,EAAE;IAClC,OAAOA,QAAQ,CACbC,SAAS,KAAK,IAAI,GAAG,CAAC,CAAC,GAAGA,SAAS,EACnCI,aAAa,EACbU,WACF,CAAC;EACH;EACA,MAAMC,KAAK,GAAGN,QAAQ,CAACV,QAAQ,EAAE,IAAI,CAAC;EACtCgB,KAAK,CAACC,OAAO,GAAGhB,SAAS;EACzB,IAAII,aAAa,CAAC1a,MAAM,GAAG,CAAC,EAAE;IAC5Bqb,KAAK,CAACE,UAAU,GAAGb,aAAa;EAClC;EACA;IACEW,KAAK,CAACG,KAAK,GAAG5d,GAAG;EACnB;EACA;IACEyd,KAAK,CAACI,MAAM,GAAGtJ,QAAQ;EACzB;EACA,OAAOkJ,KAAK;AACd,CAAC;AACD,IAAIN,QAAQ,GAAGA,CAACW,GAAG,EAAEjN,IAAI,KAAK;EAC5B,MAAM4M,KAAK,GAAG;IACZ3S,OAAO,EAAE,CAAC;IACViT,KAAK,EAAED,GAAG;IACVZ,MAAM,EAAErM,IAAI;IACZmN,KAAK,EAAE,IAAI;IACXL,UAAU,EAAE;EACd,CAAC;EACD;IACEF,KAAK,CAACC,OAAO,GAAG,IAAI;EACtB;EACA;IACED,KAAK,CAACG,KAAK,GAAG,IAAI;EACpB;EACA;IACEH,KAAK,CAACI,MAAM,GAAG,IAAI;EACrB;EACA,OAAOJ,KAAK;AACd,CAAC;AACD,IAAIQ,IAAI,GAAG,CAAC,CAAC;AACb,IAAIC,MAAM,GAAIvJ,IAAI,IAAKA,IAAI,IAAIA,IAAI,CAACoJ,KAAK,KAAKE,IAAI;AAClD,IAAIT,WAAW,GAAG;EAChBnb,OAAO,EAAEA,CAACqY,QAAQ,EAAEvL,EAAE,KAAKuL,QAAQ,CAAC5Y,GAAG,CAACqc,eAAe,CAAC,CAAC9b,OAAO,CAAC8M,EAAE,CAAC;EACpErN,GAAG,EAAEA,CAAC4Y,QAAQ,EAAEvL,EAAE,KAAKuL,QAAQ,CAAC5Y,GAAG,CAACqc,eAAe,CAAC,CAACrc,GAAG,CAACqN,EAAE,CAAC,CAACrN,GAAG,CAACsc,gBAAgB;AACnF,CAAC;AACD,IAAID,eAAe,GAAIxJ,IAAI,KAAM;EAC/B0J,MAAM,EAAE1J,IAAI,CAAC+I,OAAO;EACpBY,SAAS,EAAE3J,IAAI,CAACgJ,UAAU;EAC1BY,IAAI,EAAE5J,IAAI,CAACiJ,KAAK;EAChBY,KAAK,EAAE7J,IAAI,CAACkJ,MAAM;EAClBY,IAAI,EAAE9J,IAAI,CAACoJ,KAAK;EAChBW,KAAK,EAAE/J,IAAI,CAACuI;AACd,CAAC,CAAC;AACF,IAAIkB,gBAAgB,GAAIzJ,IAAI,IAAK;EAC/B,IAAI,OAAOA,IAAI,CAAC8J,IAAI,KAAK,UAAU,EAAE;IACnC,MAAM/B,SAAS,GAAG;MAAE,GAAG/H,IAAI,CAAC0J;IAAO,CAAC;IACpC,IAAI1J,IAAI,CAAC4J,IAAI,EAAE;MACb7B,SAAS,CAAC1c,GAAG,GAAG2U,IAAI,CAAC4J,IAAI;IAC3B;IACA,IAAI5J,IAAI,CAAC6J,KAAK,EAAE;MACd9B,SAAS,CAAClT,IAAI,GAAGmL,IAAI,CAAC6J,KAAK;IAC7B;IACA,OAAOhC,CAAC,CAAC7H,IAAI,CAAC8J,IAAI,EAAE/B,SAAS,EAAE,IAAG/H,IAAI,CAAC2J,SAAS,IAAI,EAAE,EAAC;EACzD;EACA,MAAMb,KAAK,GAAGN,QAAQ,CAACxI,IAAI,CAAC8J,IAAI,EAAE9J,IAAI,CAAC+J,KAAK,CAAC;EAC7CjB,KAAK,CAACC,OAAO,GAAG/I,IAAI,CAAC0J,MAAM;EAC3BZ,KAAK,CAACE,UAAU,GAAGhJ,IAAI,CAAC2J,SAAS;EACjCb,KAAK,CAACG,KAAK,GAAGjJ,IAAI,CAAC4J,IAAI;EACvBd,KAAK,CAACI,MAAM,GAAGlJ,IAAI,CAAC6J,KAAK;EACzB,OAAOf,KAAK;AACd,CAAC;;AAED;AACA,IAAIkB,uBAAuB,GAAGA,CAACC,OAAO,EAAErb,OAAO,EAAEsb,MAAM,EAAEpU,OAAO,KAAK;EACnE,IAAIhE,EAAE;EACN,MAAMqY,UAAU,GAAG1C,UAAU,CAAC,eAAe,EAAE7Y,OAAO,CAAC;EACvD,MAAM6P,UAAU,GAAGwL,OAAO,CAACxL,UAAU;EACrC,MAAM2L,gBAAgB,GAAG,EAAE;EAC3B,MAAMC,SAAS,GAAG,EAAE;EACpB,MAAMxK,YAAY,GAAG,EAAE;EACvB,MAAMyK,eAAe,GAAG7L,UAAU,GAAG,EAAE,GAAG,IAAI;EAC9C,MAAMqK,KAAK,GAAGN,QAAQ,CAAC5Z,OAAO,EAAE,IAAI,CAAC;EACrCka,KAAK,CAACO,KAAK,GAAGY,OAAO;EACrB,MAAMM,OAAO,GAAGrf,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC2G,EAAE,GAAGgE,OAAO,CAACO,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGvE,EAAE,CAAC0Y,SAAS,KAAK,CAAC,CAAC,CAAC;EAChGD,OAAO,CAAC7c,OAAO,CAAC,CAAC,CAACoJ,UAAU,EAAE,CAAC2T,WAAW,EAAEC,iBAAiB,CAAC,CAAC,KAAK;IAClE,IAAIC,GAAG;IACP,IAAI,EAAEF,WAAW,GAAG,EAAE,CAAC,WAAW,EAAE;MAClC;IACF;IACA,MAAMG,aAAa,GAAGF,iBAAiB,IAAI5T,UAAU;IACrD,MAAM+T,OAAO,GAAGZ,OAAO,CAACnW,YAAY,CAAC8W,aAAa,CAAC;IACnD,IAAIC,OAAO,KAAK,IAAI,EAAE;MACpB,MAAMC,WAAW,GAAGC,kBAAkB,CAACF,OAAO,EAAEJ,WAAW,CAAC;MAC5D,CAACE,GAAG,GAAG7U,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACQ,gBAAgB,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGqU,GAAG,CAAC5e,GAAG,CAAC+K,UAAU,EAAEgU,WAAW,CAAC;IACjH;EACF,CAAC,CAAC;EACF,IAAIE,QAAQ;EACZ;IACE,MAAM9U,OAAO,GAAGJ,OAAO,CAACO,SAAS;IACjC,IAAIH,OAAO,IAAIA,OAAO,CAACC,OAAO,GAAG,EAAE,CAAC,kCAAkC8T,OAAO,CAAC,MAAM,CAAC,EAAE;MACrFe,QAAQ,GAAGf,OAAO,CAAC,MAAM,CAAC;MAC1BA,OAAO,CAACta,SAAS,CAACC,GAAG,CAACob,QAAQ,GAAG,IAAI,CAAC;IACxC,CAAC,MAAM,IAAIf,OAAO,CAAC,MAAM,CAAC,EAAE;MAC1B,OAAOA,OAAO,CAAC,MAAM,CAAC;IACxB;EACF;EACA,IAAI/d,GAAG,CAACuD,QAAQ,KAAK,CAACiJ,GAAG,CAACuS,aAAa,IAAI,CAACvS,GAAG,CAACuS,aAAa,CAACC,IAAI,CAAC,EAAE;IACnEC,yBAAyB,CAACjf,GAAG,CAACuD,QAAQ,CAAC2b,IAAI,EAAE1S,GAAG,CAACuS,aAAa,GAAG,eAAgB,IAAIlgB,GAAG,CAAC,CAAC,CAAC;EAC7F;EACAkf,OAAO,CAAC/R,UAAU,CAAC,GAAGgS,MAAM;EAC5BD,OAAO,CAACoB,eAAe,CAACnT,UAAU,CAAC;EACnCpC,OAAO,CAACwV,OAAO,GAAGC,aAAa,CAC7BzC,KAAK,EACLsB,gBAAgB,EAChBC,SAAS,EACTC,eAAe,EACfL,OAAO,EACPA,OAAO,EACPC,MAAM,EACNrK,YACF,CAAC;EACD,IAAI2L,OAAO,GAAG,CAAC;EACf,MAAMC,QAAQ,GAAGrB,gBAAgB,CAAC3c,MAAM;EACxC,IAAIie,eAAe;EACnB,KAAKF,OAAO,EAAEA,OAAO,GAAGC,QAAQ,EAAED,OAAO,EAAE,EAAE;IAC3CE,eAAe,GAAGtB,gBAAgB,CAACoB,OAAO,CAAC;IAC3C,MAAMG,aAAa,GAAGD,eAAe,CAACE,QAAQ,GAAG,GAAG,GAAGF,eAAe,CAACG,QAAQ;IAC/E,MAAMC,eAAe,GAAGpT,GAAG,CAACuS,aAAa,CAAC7f,GAAG,CAACugB,aAAa,CAAC;IAC5D,MAAM3L,IAAI,GAAG0L,eAAe,CAACrC,KAAK;IAClC,IAAI,CAAC5K,UAAU,EAAE;MACfuB,IAAI,CAAC,MAAM,CAAC,GAAGpR,OAAO,CAACmd,WAAW,CAAC,CAAC;MACpC,IAAIL,eAAe,CAACtC,KAAK,KAAK,MAAM,EAAE;QACpCpJ,IAAI,CAAC,MAAM,CAAC,GAAGiK,OAAO,CAAC,MAAM,CAAC;MAChC;IACF;IACA,IAAIyB,eAAe,CAACtC,KAAK,KAAK,MAAM,EAAE;MACpCsC,eAAe,CAACxC,MAAM,GAAGwC,eAAe,CAACrC,KAAK,CAAC,MAAM,CAAC,IAAIqC,eAAe,CAACrC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI;MAC/F,IAAIqC,eAAe,CAAC1C,UAAU,EAAE;QAC9B0C,eAAe,CAACvV,OAAO,IAAI,CAAC,CAAC;QAC7B,IAAI,CAACuV,eAAe,CAACrC,KAAK,CAACtK,UAAU,CAACtR,MAAM,EAAE;UAC5Cie,eAAe,CAAC1C,UAAU,CAACtb,OAAO,CAAEf,CAAC,IAAK;YACxC+e,eAAe,CAACrC,KAAK,CAACzF,WAAW,CAACjX,CAAC,CAAC0c,KAAK,CAAC;UAC5C,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACLqC,eAAe,CAACvV,OAAO,IAAI,CAAC,CAAC;MAC/B;IACF;IACA,IAAI2V,eAAe,IAAIA,eAAe,CAACpM,WAAW,EAAE;MAClD,IAAIjB,UAAU,IAAIqN,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE;QAChDA,eAAe,CAACpL,UAAU,CAACyE,YAAY,CAACnF,IAAI,EAAE8L,eAAe,CAAC7L,WAAW,CAAC;MAC5E;MACA6L,eAAe,CAACpL,UAAU,CAACwD,WAAW,CAAC4H,eAAe,CAAC;MACvD,IAAI,CAACrN,UAAU,EAAE;QACfuB,IAAI,CAAC,MAAM,CAAC,GAAGgM,QAAQ,CAACN,eAAe,CAACG,QAAQ,CAAC;MACnD;IACF;IACAnT,GAAG,CAACuS,aAAa,CAACgB,MAAM,CAACN,aAAa,CAAC;EACzC;EACA,MAAMO,KAAK,GAAG,EAAE;EAChB,MAAMC,KAAK,GAAGtM,YAAY,CAACpS,MAAM;EACjC,IAAI2e,OAAO,GAAG,CAAC;EACf,IAAIC,SAAS;EACb,IAAIC,UAAU;EACd,IAAIC,UAAU;EACd,IAAIC,WAAW;EACf,KAAKJ,OAAO,EAAEA,OAAO,GAAGD,KAAK,EAAEC,OAAO,EAAE,EAAE;IACxCC,SAAS,GAAGxM,YAAY,CAACuM,OAAO,CAAC;IACjC,IAAI,CAACC,SAAS,IAAI,CAACA,SAAS,CAAC5e,MAAM,EAAE;IACrC8e,UAAU,GAAGF,SAAS,CAAC5e,MAAM;IAC7B6e,UAAU,GAAG,CAAC;IACd,KAAKA,UAAU,EAAEA,UAAU,GAAGC,UAAU,EAAED,UAAU,EAAE,EAAE;MACtDE,WAAW,GAAGH,SAAS,CAACC,UAAU,CAAC;MACnC,IAAI,CAACJ,KAAK,CAACM,WAAW,CAACtC,MAAM,CAAC,EAAE;QAC9BgC,KAAK,CAACM,WAAW,CAACtC,MAAM,CAAC,GAAGxR,GAAG,CAACuS,aAAa,CAAC7f,GAAG,CAACohB,WAAW,CAACtC,MAAM,CAAC;MACvE;MACA,IAAI,CAACgC,KAAK,CAACM,WAAW,CAACtC,MAAM,CAAC,EAAE;MAChC,MAAMuC,OAAO,GAAGP,KAAK,CAACM,WAAW,CAACtC,MAAM,CAAC;MACzC,IAAI,CAACuC,OAAO,CAAChO,UAAU,IAAI,CAACA,UAAU,EAAE;QACtC+N,WAAW,CAAC1M,IAAI,CAAC,MAAM,CAAC,GAAG2M,OAAO,CAAC,MAAM,CAAC;QAC1C,IAAI,CAACD,WAAW,CAAC1M,IAAI,CAAC,MAAM,CAAC,IAAI2M,OAAO,CAAChO,UAAU,EAAE;UACnD+N,WAAW,CAAC1M,IAAI,CAAC,MAAM,CAAC,GAAG2M,OAAO;QACpC,CAAC,MAAM;UACLD,WAAW,CAAC1M,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC2M,OAAO,CAAChL,YAAY,IAAIgL,OAAO,CAAC1N,UAAU,EAAE,CAAC,CAAC;QAC5E;QACAqB,mBAAmB,CAACoM,WAAW,CAACxM,IAAI,EAAEwM,WAAW,CAAC1M,IAAI,EAAE,KAAK,EAAE0M,WAAW,CAACxM,IAAI,CAAC,MAAM,CAAC,CAAC;QACxF;UACEgG,gBAAgB,CAACwG,WAAW,CAACxM,IAAI,CAAC;QACpC;MACF;MACA,IAAIyM,OAAO,CAAChO,UAAU,IAAI+N,WAAW,CAACxM,IAAI,CAAC3L,aAAa,KAAKoY,OAAO,EAAE;QACpEA,OAAO,CAAC7I,WAAW,CAAC4I,WAAW,CAACxM,IAAI,CAAC;MACvC;IACF;EACF;EACA,IAAIgL,QAAQ,IAAIX,SAAS,CAAC5c,MAAM,EAAE;IAChC4c,SAAS,CAAC3c,OAAO,CAAEoS,IAAI,IAAK;MAC1BA,IAAI,CAACuJ,KAAK,CAAChV,aAAa,CAAC1E,SAAS,CAACC,GAAG,CAACob,QAAQ,GAAG,IAAI,CAAC;IACzD,CAAC,CAAC;EACJ;EACA,IAAIvM,UAAU,IAAI,CAACA,UAAU,CAACM,UAAU,CAACtR,MAAM,EAAE;IAC/C,IAAIif,MAAM,GAAG,CAAC;IACd,MAAMC,KAAK,GAAGrC,eAAe,CAAC7c,MAAM;IACpC,IAAIkf,KAAK,EAAE;MACT,KAAKD,MAAM,EAAEA,MAAM,GAAGC,KAAK,EAAED,MAAM,EAAE,EAAE;QACrCjO,UAAU,CAACmF,WAAW,CAAC0G,eAAe,CAACoC,MAAM,CAAC,CAAC;MACjD;MACA5G,KAAK,CAAC8G,IAAI,CAAC3C,OAAO,CAAClL,UAAU,CAAC,CAACrR,OAAO,CAAEsS,IAAI,IAAK;QAC/C,IAAI,OAAOA,IAAI,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;UACpC,IAAIA,IAAI,CAACb,QAAQ,KAAK,CAAC,CAAC,qBAAqBa,IAAI,CAACF,IAAI,IAAIE,IAAI,CAACV,MAAM,EAAE;YACrEU,IAAI,CAACqL,eAAe,CAAC,QAAQ,CAAC;UAChC,CAAC,MAAM,IAAIrL,IAAI,CAACb,QAAQ,KAAK,CAAC,CAAC,qBAAqBa,IAAI,CAACb,QAAQ,KAAK,CAAC,CAAC,kBAAkB,CAACa,IAAI,CAAC6M,SAAS,CAACC,IAAI,CAAC,CAAC,EAAE;YAChH9M,IAAI,CAACU,UAAU,CAACwD,WAAW,CAAClE,IAAI,CAAC;UACnC;QACF;MACF,CAAC,CAAC;IACJ;EACF;EACAtH,GAAG,CAACuS,aAAa,CAACgB,MAAM,CAAChC,OAAO,CAAC,MAAM,CAAC,CAAC;EACzCnU,OAAO,CAACM,aAAa,GAAG6T,OAAO;EAC/BE,UAAU,CAAC,CAAC;AACd,CAAC;AACD,IAAIoB,aAAa,GAAGA,CAACwB,WAAW,EAAE3C,gBAAgB,EAAEC,SAAS,EAAEC,eAAe,EAAEL,OAAO,EAAEjK,IAAI,EAAEkK,MAAM,EAAErK,YAAY,GAAG,EAAE,KAAK;EAC3H,IAAImN,aAAa;EACjB,IAAIC,WAAW;EACf,IAAIC,UAAU;EACd,IAAIrS,EAAE;EACN,MAAMmQ,QAAQ,GAAGf,OAAO,CAAC,MAAM,CAAC;EAChC,IAAIjK,IAAI,CAACb,QAAQ,KAAK,CAAC,CAAC,mBAAmB;IACzC6N,aAAa,GAAGhN,IAAI,CAAClM,YAAY,CAACsE,gBAAgB,CAAC;IACnD,IAAI4U,aAAa,EAAE;MACjBC,WAAW,GAAGD,aAAa,CAAC9f,KAAK,CAAC,GAAG,CAAC;MACtC,IAAI+f,WAAW,CAAC,CAAC,CAAC,KAAK/C,MAAM,IAAI+C,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACvDC,UAAU,GAAGC,iBAAiB,CAAC;UAC7BhX,OAAO,EAAE,CAAC;UACVyV,QAAQ,EAAEqB,WAAW,CAAC,CAAC,CAAC;UACxBpB,QAAQ,EAAEoB,WAAW,CAAC,CAAC,CAAC;UACxBG,OAAO,EAAEH,WAAW,CAAC,CAAC,CAAC;UACvBI,OAAO,EAAEJ,WAAW,CAAC,CAAC,CAAC;UACvB7D,KAAK,EAAEpJ,IAAI,CAACpR,OAAO,CAACC,WAAW,CAAC,CAAC;UACjCwa,KAAK,EAAErJ,IAAI;UACX;UACA;UACA+I,OAAO,EAAE;YAAEJ,KAAK,EAAE3I,IAAI,CAAC0I,SAAS,IAAI;UAAG;QACzC,CAAC,CAAC;QACF0B,gBAAgB,CAAC3P,IAAI,CAACyS,UAAU,CAAC;QACjClN,IAAI,CAACqL,eAAe,CAACjT,gBAAgB,CAAC;QACtC,IAAI,CAAC2U,WAAW,CAAC/D,UAAU,EAAE;UAC3B+D,WAAW,CAAC/D,UAAU,GAAG,EAAE;QAC7B;QACA,IAAIgC,QAAQ,EAAE;UACZhL,IAAI,CAAC,MAAM,CAAC,GAAGgL,QAAQ;UACvBkC,UAAU,CAACnE,OAAO,CAACJ,KAAK,IAAI,GAAG,GAAGqC,QAAQ;QAC5C;QACA,MAAMpL,QAAQ,GAAGsN,UAAU,CAAC7D,KAAK,CAACvV,YAAY,CAAC,MAAM,CAAC;QACtD,IAAI,OAAO8L,QAAQ,KAAK,QAAQ,EAAE;UAChC,IAAIsN,UAAU,CAAC9D,KAAK,KAAK,SAAS,EAAE;YAClCkE,OAAO,CACL1N,QAAQ,EACRqN,WAAW,CAAC,CAAC,CAAC,EACdC,UAAU,EACVlN,IAAI,EACJ+M,WAAW,EACX3C,gBAAgB,EAChBC,SAAS,EACTC,eAAe,EACfzK,YACF,CAAC;YACD,IAAImL,QAAQ,EAAE;cACZhL,IAAI,CAACrQ,SAAS,CAACC,GAAG,CAACob,QAAQ,CAAC;YAC9B;UACF;UACAkC,UAAU,CAAC7D,KAAK,CAAC,MAAM,CAAC,GAAGzJ,QAAQ;UACnCsN,UAAU,CAAC7D,KAAK,CAACgC,eAAe,CAAC,MAAM,CAAC;QAC1C;QACA,IAAI6B,UAAU,CAACG,OAAO,KAAK,KAAK,CAAC,EAAE;UACjCN,WAAW,CAAC/D,UAAU,CAACkE,UAAU,CAACG,OAAO,CAAC,GAAGH,UAAU;QACzD;QACAH,WAAW,GAAGG,UAAU;QACxB,IAAI5C,eAAe,IAAI4C,UAAU,CAACE,OAAO,KAAK,GAAG,EAAE;UACjD9C,eAAe,CAAC4C,UAAU,CAACG,OAAO,CAAC,GAAGH,UAAU,CAAC7D,KAAK;QACxD;MACF;IACF;IACA,IAAIrJ,IAAI,CAACvB,UAAU,EAAE;MACnB,KAAK5D,EAAE,GAAGmF,IAAI,CAACvB,UAAU,CAACM,UAAU,CAACtR,MAAM,GAAG,CAAC,EAAEoN,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;QAC9D0Q,aAAa,CACXwB,WAAW,EACX3C,gBAAgB,EAChBC,SAAS,EACTC,eAAe,EACfL,OAAO,EACPjK,IAAI,CAACvB,UAAU,CAACM,UAAU,CAAClE,EAAE,CAAC,EAC9BqP,MAAM,EACNrK,YACF,CAAC;MACH;IACF;IACA,MAAM0N,cAAc,GAAGvN,IAAI,CAACyB,YAAY,IAAIzB,IAAI,CAACjB,UAAU;IAC3D,KAAKlE,EAAE,GAAG0S,cAAc,CAAC9f,MAAM,GAAG,CAAC,EAAEoN,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;MAClD0Q,aAAa,CACXwB,WAAW,EACX3C,gBAAgB,EAChBC,SAAS,EACTC,eAAe,EACfL,OAAO,EACPsD,cAAc,CAAC1S,EAAE,CAAC,EAClBqP,MAAM,EACNrK,YACF,CAAC;IACH;EACF,CAAC,MAAM,IAAIG,IAAI,CAACb,QAAQ,KAAK,CAAC,CAAC,mBAAmB;IAChD8N,WAAW,GAAGjN,IAAI,CAACwN,SAAS,CAACtgB,KAAK,CAAC,GAAG,CAAC;IACvC,IAAI+f,WAAW,CAAC,CAAC,CAAC,KAAK/C,MAAM,IAAI+C,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACvDD,aAAa,GAAGC,WAAW,CAAC,CAAC,CAAC;MAC9BC,UAAU,GAAGC,iBAAiB,CAAC;QAC7BvB,QAAQ,EAAEqB,WAAW,CAAC,CAAC,CAAC;QACxBpB,QAAQ,EAAEoB,WAAW,CAAC,CAAC,CAAC;QACxBG,OAAO,EAAEH,WAAW,CAAC,CAAC,CAAC;QACvBI,OAAO,EAAEJ,WAAW,CAAC,CAAC,CAAC,IAAI,GAAG;QAC9B5D,KAAK,EAAErJ,IAAI;QACX+I,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZE,KAAK,EAAE,IAAI;QACXb,MAAM,EAAE;MACV,CAAC,CAAC;MACF,IAAIyE,aAAa,KAAKhV,YAAY,EAAE;QAClCkV,UAAU,CAAC7D,KAAK,GAAGoE,qBAAqB,CAACzN,IAAI,EAAE,CAAC,CAAC,cAAc,CAAC;QAChE,IAAIkN,UAAU,CAAC7D,KAAK,IAAI6D,UAAU,CAAC7D,KAAK,CAAClK,QAAQ,KAAK,CAAC,CAAC,gBAAgB;UACtE+N,UAAU,CAAC3E,MAAM,GAAG2E,UAAU,CAAC7D,KAAK,CAACzD,WAAW;UAChDwE,gBAAgB,CAAC3P,IAAI,CAACyS,UAAU,CAAC;UACjClN,IAAI,CAACoE,MAAM,CAAC,CAAC;UACb,IAAI8F,MAAM,KAAKgD,UAAU,CAACtB,QAAQ,EAAE;YAClC,IAAI,CAACmB,WAAW,CAAC/D,UAAU,EAAE;cAC3B+D,WAAW,CAAC/D,UAAU,GAAG,EAAE;YAC7B;YACA+D,WAAW,CAAC/D,UAAU,CAACkE,UAAU,CAACG,OAAO,CAAC,GAAGH,UAAU;UACzD;UACA,IAAI5C,eAAe,IAAI4C,UAAU,CAACE,OAAO,KAAK,GAAG,EAAE;YACjD9C,eAAe,CAAC4C,UAAU,CAACG,OAAO,CAAC,GAAGH,UAAU,CAAC7D,KAAK;UACxD;QACF;MACF,CAAC,MAAM,IAAI2D,aAAa,KAAK/U,eAAe,EAAE;QAC5CiV,UAAU,CAAC7D,KAAK,GAAGoE,qBAAqB,CAACzN,IAAI,EAAE,CAAC,CAAC,iBAAiB,CAAC;QACnE,IAAIkN,UAAU,CAAC7D,KAAK,IAAI6D,UAAU,CAAC7D,KAAK,CAAClK,QAAQ,KAAK,CAAC,CAAC,mBAAmB;UACzEiL,gBAAgB,CAAC3P,IAAI,CAACyS,UAAU,CAAC;UACjClN,IAAI,CAACoE,MAAM,CAAC,CAAC;QACf;MACF,CAAC,MAAM,IAAI8I,UAAU,CAACtB,QAAQ,KAAK1B,MAAM,EAAE;QACzC,IAAI8C,aAAa,KAAKjV,YAAY,EAAE;UAClC,MAAM6H,QAAQ,GAAGI,IAAI,CAAC,MAAM,CAAC,GAAGiN,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE;UACpDK,OAAO,CACL1N,QAAQ,EACRqN,WAAW,CAAC,CAAC,CAAC,EACdC,UAAU,EACVlN,IAAI,EACJ+M,WAAW,EACX3C,gBAAgB,EAChBC,SAAS,EACTC,eAAe,EACfzK,YACF,CAAC;QACH,CAAC,MAAM,IAAImN,aAAa,KAAKnV,cAAc,EAAE;UAC3C,IAAIyS,eAAe,EAAE;YACnBtK,IAAI,CAACoE,MAAM,CAAC,CAAC;UACf,CAAC,MAAM;YACL6F,OAAO,CAAC,MAAM,CAAC,GAAGjK,IAAI;YACtBA,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI;UACrB;QACF;MACF;IACF;EACF,CAAC,MAAM,IAAI+M,WAAW,IAAIA,WAAW,CAAC3D,KAAK,KAAK,OAAO,EAAE;IACvD,MAAMN,KAAK,GAAGN,QAAQ,CAAC,IAAI,EAAExI,IAAI,CAAC4F,WAAW,CAAC;IAC9CkD,KAAK,CAACO,KAAK,GAAGrJ,IAAI;IAClB8I,KAAK,CAACuE,OAAO,GAAG,GAAG;IACnBN,WAAW,CAAC/D,UAAU,GAAG,CAACF,KAAK,CAAC;EAClC,CAAC,MAAM;IACL,IAAI9I,IAAI,CAACb,QAAQ,KAAK,CAAC,CAAC,kBAAkB,CAACa,IAAI,CAAC6M,SAAS,CAACC,IAAI,CAAC,CAAC,EAAE;MAChE9M,IAAI,CAACoE,MAAM,CAAC,CAAC;IACf;EACF;EACA,OAAO2I,WAAW;AACpB,CAAC;AACD,IAAI5B,yBAAyB,GAAGA,CAACnL,IAAI,EAAE0N,WAAW,KAAK;EACrD,IAAI1N,IAAI,CAACb,QAAQ,KAAK,CAAC,CAAC,mBAAmB;IACzC,MAAMwO,WAAW,GAAG3N,IAAI,CAAC9H,UAAU,CAAC,IAAI8H,IAAI,CAAClM,YAAY,CAACoE,UAAU,CAAC;IACrE,IAAIyV,WAAW,EAAE;MACfD,WAAW,CAAC3hB,GAAG,CAAC4hB,WAAW,EAAE3N,IAAI,CAAC;IACpC;IACA,IAAInF,EAAE,GAAG,CAAC;IACV,IAAImF,IAAI,CAACvB,UAAU,EAAE;MACnB,OAAO5D,EAAE,GAAGmF,IAAI,CAACvB,UAAU,CAACM,UAAU,CAACtR,MAAM,EAAEoN,EAAE,EAAE,EAAE;QACnDsQ,yBAAyB,CAACnL,IAAI,CAACvB,UAAU,CAACM,UAAU,CAAClE,EAAE,CAAC,EAAE6S,WAAW,CAAC;MACxE;IACF;IACA,MAAMH,cAAc,GAAGvN,IAAI,CAACyB,YAAY,IAAIzB,IAAI,CAACjB,UAAU;IAC3D,KAAKlE,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG0S,cAAc,CAAC9f,MAAM,EAAEoN,EAAE,EAAE,EAAE;MAC7CsQ,yBAAyB,CAACoC,cAAc,CAAC1S,EAAE,CAAC,EAAE6S,WAAW,CAAC;IAC5D;EACF,CAAC,MAAM,IAAI1N,IAAI,CAACb,QAAQ,KAAK,CAAC,CAAC,mBAAmB;IAChD,MAAM8N,WAAW,GAAGjN,IAAI,CAACwN,SAAS,CAACtgB,KAAK,CAAC,GAAG,CAAC;IAC7C,IAAI+f,WAAW,CAAC,CAAC,CAAC,KAAKnV,eAAe,EAAE;MACtC4V,WAAW,CAAC3hB,GAAG,CAACkhB,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,WAAW,CAAC,CAAC,CAAC,EAAEjN,IAAI,CAAC;MAC5DA,IAAI,CAACwN,SAAS,GAAG,EAAE;MACnBxN,IAAI,CAAC,MAAM,CAAC,GAAGiN,WAAW,CAAC,CAAC,CAAC;IAC/B;EACF;AACF,CAAC;AACD,IAAIE,iBAAiB,GAAIrE,KAAK,IAAK;EACjC,MAAM8E,YAAY,GAAG;IACnBzX,OAAO,EAAE,CAAC;IACVyV,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,IAAI;IACduB,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,GAAG;IACZhE,KAAK,EAAE,IAAI;IACXN,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZE,KAAK,EAAE,IAAI;IACXb,MAAM,EAAE;EACV,CAAC;EACD,OAAO;IAAE,GAAGqF,YAAY;IAAE,GAAG9E;EAAM,CAAC;AACtC,CAAC;AACD,SAASwE,OAAOA,CAAC1N,QAAQ,EAAEiO,MAAM,EAAEX,UAAU,EAAElN,IAAI,EAAE+M,WAAW,EAAE3C,gBAAgB,EAAEC,SAAS,EAAEC,eAAe,EAAEzK,YAAY,EAAE;EAC5HG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI;EACnBkN,UAAU,CAAChE,MAAM,GAAGtJ,QAAQ,IAAI,IAAI;EACpCsN,UAAU,CAAC9D,KAAK,GAAG,MAAM;EACzB,MAAM0E,YAAY,GAAG,CAACf,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC1D,KAAK,IAAI0D,WAAW,CAAC1D,KAAK,CAAC,MAAM,CAAC,IAAI0D,WAAW,CAAC1D,KAAK,CAACvV,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE;EAClJ,IAAIwW,eAAe,IAAIpe,GAAG,CAACuD,QAAQ,EAAE;IACnC,MAAMqQ,IAAI,GAAGoN,UAAU,CAAC7D,KAAK,GAAGnd,GAAG,CAACuD,QAAQ,CAACoV,aAAa,CAACqI,UAAU,CAAC9D,KAAK,CAAC;IAC5E,IAAI8D,UAAU,CAAChE,MAAM,EAAE;MACrBgE,UAAU,CAAC7D,KAAK,CAACtV,YAAY,CAAC,MAAM,EAAE6L,QAAQ,CAAC;IACjD;IACA,IAAIkO,YAAY,IAAIA,YAAY,KAAKZ,UAAU,CAACtB,QAAQ,EAAE;MACxDmB,WAAW,CAAC1D,KAAK,CAAClE,YAAY,CAACrF,IAAI,EAAEiN,WAAW,CAAC1D,KAAK,CAACtD,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrE,CAAC,MAAM;MACL/F,IAAI,CAACU,UAAU,CAACyE,YAAY,CAAC+H,UAAU,CAAC7D,KAAK,EAAErJ,IAAI,CAAC;IACtD;IACA+N,eAAe,CAAClO,YAAY,EAAEgO,MAAM,EAAEjO,QAAQ,EAAEI,IAAI,EAAEkN,UAAU,CAACtB,QAAQ,CAAC;IAC1E5L,IAAI,CAACoE,MAAM,CAAC,CAAC;IACb,IAAI8I,UAAU,CAACE,OAAO,KAAK,GAAG,EAAE;MAC9B9C,eAAe,CAAC4C,UAAU,CAACG,OAAO,CAAC,GAAGH,UAAU,CAAC7D,KAAK;IACxD;EACF,CAAC,MAAM;IACL,MAAMvJ,IAAI,GAAGoN,UAAU,CAAC7D,KAAK;IAC7B,MAAM2E,UAAU,GAAGF,YAAY,IAAIA,YAAY,KAAKZ,UAAU,CAACtB,QAAQ,IAAImB,WAAW,CAAC1D,KAAK,CAAC5K,UAAU;IACvGsP,eAAe,CAAClO,YAAY,EAAEgO,MAAM,EAAEjO,QAAQ,EAAEI,IAAI,EAAEgO,UAAU,GAAGF,YAAY,GAAGZ,UAAU,CAACtB,QAAQ,CAAC;IACtG1K,aAAa,CAAClB,IAAI,CAAC;IACnB,IAAIgO,UAAU,EAAE;MACdjB,WAAW,CAAC1D,KAAK,CAAClE,YAAY,CAACrF,IAAI,EAAEiN,WAAW,CAAC1D,KAAK,CAACtD,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrE;IACAqE,gBAAgB,CAAC3P,IAAI,CAACyS,UAAU,CAAC;EACnC;EACA7C,SAAS,CAAC5P,IAAI,CAACyS,UAAU,CAAC;EAC1B,IAAI,CAACH,WAAW,CAAC/D,UAAU,EAAE;IAC3B+D,WAAW,CAAC/D,UAAU,GAAG,EAAE;EAC7B;EACA+D,WAAW,CAAC/D,UAAU,CAACkE,UAAU,CAACG,OAAO,CAAC,GAAGH,UAAU;AACzD;AACA,IAAIa,eAAe,GAAGA,CAAClO,YAAY,EAAEoO,UAAU,EAAErO,QAAQ,EAAEV,QAAQ,EAAEgL,MAAM,KAAK;EAC9E,IAAIzK,WAAW,GAAGP,QAAQ,CAACe,WAAW;EACtCJ,YAAY,CAACoO,UAAU,CAAC,GAAGpO,YAAY,CAACoO,UAAU,CAAC,IAAI,EAAE;EACzD,OAAOxO,WAAW,KAAK,CAACA,WAAW,CAAC,cAAc,CAAC,IAAIA,WAAW,CAAC3L,YAAY,CAAC,MAAM,CAAC,IAAI2L,WAAW,CAAC,MAAM,CAAC,MAAMG,QAAQ,IAAIA,QAAQ,KAAK,EAAE,IAAI,CAACH,WAAW,CAAC,MAAM,CAAC,KAAKA,WAAW,CAACN,QAAQ,KAAK,CAAC,CAAC,qBAAqBM,WAAW,CAAC+N,SAAS,CAAC5G,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAInH,WAAW,CAACN,QAAQ,KAAK,CAAC,CAAC,eAAe,CAAC,EAAE;IACnTM,WAAW,CAAC,MAAM,CAAC,GAAGG,QAAQ;IAC9BC,YAAY,CAACoO,UAAU,CAAC,CAACxT,IAAI,CAAC;MAAEqF,IAAI,EAAEZ,QAAQ;MAAEc,IAAI,EAAEP,WAAW;MAAEyK;IAAO,CAAC,CAAC;IAC5EzK,WAAW,GAAGA,WAAW,CAACQ,WAAW;EACvC;AACF,CAAC;AACD,IAAIwN,qBAAqB,GAAGA,CAACzN,IAAI,EAAEzD,IAAI,KAAK;EAC1C,IAAI2R,OAAO,GAAGlO,IAAI;EAClB,GAAG;IACDkO,OAAO,GAAGA,OAAO,CAACjO,WAAW;EAC/B,CAAC,QAAQiO,OAAO,KAAKA,OAAO,CAAC/O,QAAQ,KAAK5C,IAAI,IAAI,CAAC2R,OAAO,CAACV,SAAS,CAAC;EACrE,OAAOU,OAAO;AAChB,CAAC;AACD,IAAIC,oBAAoB,GAAIC,QAAQ,IAAK;EACvC,MAAMC,aAAa,GAAGpS,6BAA6B,CAACmS,QAAQ,CAAC;EAC7D,OAAO,IAAIlR,MAAM;EACf;EACA;EACA,gDAAgDmR,aAAa,MAAMA,aAAa,MAAM,EACtF,GACF,CAAC;AACH,CAAC;AACDF,oBAAoB,CAAC,WAAW,CAAC;AACjCA,oBAAoB,CAAC,OAAO,CAAC;AAC7BA,oBAAoB,CAAC,eAAe,CAAC;;AAErC;AACA,IAAIG,WAAW,GAAIra,GAAG,IAAK2D,mBAAmB,CAACzK,GAAG,CAAE0L,EAAE,IAAKA,EAAE,CAAC5E,GAAG,CAAC,CAAC,CAACsa,IAAI,CAAEzjB,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC;AACpF,IAAIsJ,OAAO,GAAIoa,OAAO,IAAK5W,mBAAmB,CAAC6C,IAAI,CAAC+T,OAAO,CAAC;AAC5D,IAAIjb,OAAO,GAAID,GAAG,IAAKoC,UAAU,CAACpC,GAAG,CAAC,CAACmb,UAAU;AACjD,IAAI1D,kBAAkB,GAAGA,CAAC2D,SAAS,EAAEC,QAAQ,KAAK;EAChD,IAAI,OAAOD,SAAS,KAAK,QAAQ,KAAKA,SAAS,CAACnhB,UAAU,CAAC,GAAG,CAAC,IAAImhB,SAAS,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,SAAS,CAACnhB,UAAU,CAAC,GAAG,CAAC,IAAImhB,SAAS,CAACE,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;IACnJ,IAAI;MACFF,SAAS,GAAGniB,IAAI,CAACC,KAAK,CAACkiB,SAAS,CAAC;MACjC,OAAOA,SAAS;IAClB,CAAC,CAAC,OAAOjiB,CAAC,EAAE,CACZ;EACF;EACA,IAAI,OAAOiiB,SAAS,KAAK,QAAQ,IAAIA,SAAS,CAACnhB,UAAU,CAACkI,iBAAiB,CAAC,EAAE;IAC5EiZ,SAAS,GAAGpQ,mBAAmB,CAACoQ,SAAS,CAAC;IAC1C,OAAOA,SAAS;EAClB;EACA,IAAIA,SAAS,IAAI,IAAI,IAAI,CAAChT,aAAa,CAACgT,SAAS,CAAC,EAAE;IAClD,IAAIC,QAAQ,GAAG,CAAC,CAAC,eAAe;MAC9B,OAAOD,SAAS,KAAK,OAAO,GAAG,KAAK,GAAGA,SAAS,KAAK,EAAE,IAAI,CAAC,CAACA,SAAS;IACxE;IACA,IAAIC,QAAQ,GAAG,CAAC,CAAC,cAAc;MAC7B,OAAO,OAAOD,SAAS,KAAK,QAAQ,GAAG9iB,UAAU,CAAC8iB,SAAS,CAAC,GAAG,OAAOA,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAG5iB,GAAG;IAChH;IACA,IAAI6iB,QAAQ,GAAG,CAAC,CAAC,cAAc;MAC7B,OAAOrG,MAAM,CAACoG,SAAS,CAAC;IAC1B;IACA,OAAOA,SAAS;EAClB;EACA,OAAOA,SAAS;AAClB,CAAC;AACD,IAAIG,UAAU,GAAIvb,GAAG,IAAKoC,UAAU,CAACpC,GAAG,CAAC,CAAC8C,aAAa;;AAEvD;AACA,IAAI0Y,WAAW,GAAGA,CAACxb,GAAG,EAAEuB,IAAI,EAAEoI,KAAK,KAAK;EACtC,MAAMhJ,GAAG,GAAG4a,UAAU,CAACvb,GAAG,CAAC;EAC3B,OAAO;IACLyb,IAAI,EAAGC,MAAM,IAAK;MAChB,OAAOC,SAAS,CAAChb,GAAG,EAAEY,IAAI,EAAE;QAC1BgN,OAAO,EAAE,CAAC,EAAE5E,KAAK,GAAG,CAAC,CAAC,cAAc;QACpC8E,QAAQ,EAAE,CAAC,EAAE9E,KAAK,GAAG,CAAC,CAAC,eAAe;QACtC6E,UAAU,EAAE,CAAC,EAAE7E,KAAK,GAAG,CAAC,CAAC,kBAAkB;QAC3C+R;MACF,CAAC,CAAC;IACJ;EACF,CAAC;AACH,CAAC;AACD,IAAIC,SAAS,GAAGA,CAAChb,GAAG,EAAEY,IAAI,EAAEsE,IAAI,KAAK;EACnC,MAAM+V,EAAE,GAAGxW,GAAG,CAACa,EAAE,CAAC1E,IAAI,EAAEsE,IAAI,CAAC;EAC7BlF,GAAG,CAAC2N,aAAa,CAACsN,EAAE,CAAC;EACrB,OAAOA,EAAE;AACX,CAAC;AACD,IAAIC,iBAAiB,GAAG,eAAgB,IAAIC,OAAO,CAAC,CAAC;AACrD,IAAIC,aAAa,GAAGA,CAACrE,QAAQ,EAAEsE,OAAO,EAAEC,OAAO,KAAK;EAClD,IAAIC,KAAK,GAAG7X,MAAM,CAACvM,GAAG,CAAC4f,QAAQ,CAAC;EAChC,IAAIjR,gCAAgC,IAAIwV,OAAO,EAAE;IAC/CC,KAAK,GAAGA,KAAK,IAAI,IAAIxV,aAAa,CAAC,CAAC;IACpC,IAAI,OAAOwV,KAAK,KAAK,QAAQ,EAAE;MAC7BA,KAAK,GAAGF,OAAO;IACjB,CAAC,MAAM;MACLE,KAAK,CAACvV,WAAW,CAACqV,OAAO,CAAC;IAC5B;EACF,CAAC,MAAM;IACLE,KAAK,GAAGF,OAAO;EACjB;EACA3X,MAAM,CAAC5L,GAAG,CAACif,QAAQ,EAAEwE,KAAK,CAAC;AAC7B,CAAC;AACD,IAAIC,QAAQ,GAAGA,CAACC,kBAAkB,EAAExZ,OAAO,EAAErC,IAAI,KAAK;EACpD,IAAI/B,EAAE;EACN,MAAMkZ,QAAQ,GAAG2E,UAAU,CAACzZ,OAAO,EAAErC,IAAI,CAAC;EAC1C,MAAM2b,KAAK,GAAG7X,MAAM,CAACvM,GAAG,CAAC4f,QAAQ,CAAC;EAClC,IAAI,CAAC9e,GAAG,CAACuD,QAAQ,EAAE;IACjB,OAAOub,QAAQ;EACjB;EACA0E,kBAAkB,GAAGA,kBAAkB,CAACvQ,QAAQ,KAAK,EAAE,CAAC,yBAAyBuQ,kBAAkB,GAAGxjB,GAAG,CAACuD,QAAQ;EAClH,IAAI+f,KAAK,EAAE;IACT,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7BE,kBAAkB,GAAGA,kBAAkB,CAAC3T,IAAI,IAAI2T,kBAAkB;MAClE,IAAIE,aAAa,GAAGT,iBAAiB,CAAC/jB,GAAG,CAACskB,kBAAkB,CAAC;MAC7D,IAAIG,QAAQ;MACZ,IAAI,CAACD,aAAa,EAAE;QAClBT,iBAAiB,CAACpjB,GAAG,CAAC2jB,kBAAkB,EAAEE,aAAa,GAAG,eAAgB,IAAIzS,GAAG,CAAC,CAAC,CAAC;MACtF;MACA,IAAI,CAACyS,aAAa,CAACE,GAAG,CAAC9E,QAAQ,CAAC,EAAE;QAChC,IAAI0E,kBAAkB,CAACK,IAAI,KAAKF,QAAQ,GAAGH,kBAAkB,CAAC1T,aAAa,CAAC,IAAI7D,iBAAiB,KAAK6S,QAAQ,IAAI,CAAC,CAAC,EAAE;UACpH6E,QAAQ,CAAC/K,SAAS,GAAG0K,KAAK;QAC5B,CAAC,MAAM;UACLK,QAAQ,GAAGpgB,QAAQ,CAACuM,aAAa,CAAC,IAAI7D,iBAAiB,KAAK6S,QAAQ,IAAI,CAAC,IAAI9e,GAAG,CAACuD,QAAQ,CAACoV,aAAa,CAAC,OAAO,CAAC;UAChHgL,QAAQ,CAAC/K,SAAS,GAAG0K,KAAK;UAC1B,MAAMQ,KAAK,GAAG,CAACle,EAAE,GAAG4G,GAAG,CAACuX,OAAO,KAAK,IAAI,GAAGne,EAAE,GAAG8J,wBAAwB,CAAC1P,GAAG,CAACuD,QAAQ,CAAC;UACtF,IAAIugB,KAAK,IAAI,IAAI,EAAE;YACjBH,QAAQ,CAAC9b,YAAY,CAAC,OAAO,EAAEic,KAAK,CAAC;UACvC;UACA,IAAI,EAAE9Z,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,6BAA6B,EAAE;YACvD,IAAIuZ,kBAAkB,CAAC5H,QAAQ,KAAK,MAAM,EAAE;cAC1C,MAAMoI,eAAe,GAAGR,kBAAkB,CAACS,gBAAgB,CAAC,sBAAsB,CAAC;cACnF,MAAMC,cAAc,GAAGF,eAAe,CAACziB,MAAM,GAAG,CAAC,GAAGyiB,eAAe,CAACA,eAAe,CAACziB,MAAM,GAAG,CAAC,CAAC,CAACwS,WAAW,GAAGyP,kBAAkB,CAAC1T,aAAa,CAAC,OAAO,CAAC;cACvJ0T,kBAAkB,CAACvK,YAAY,CAC7B0K,QAAQ,EACR,CAACO,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAAC1P,UAAU,MAAMgP,kBAAkB,GAAGU,cAAc,GAAG,IAC1G,CAAC;YACH,CAAC,MAAM,IAAI,MAAM,IAAIV,kBAAkB,EAAE;cACvC,IAAI3V,gCAAgC,EAAE;gBACpC,MAAMsW,UAAU,GAAG,IAAIrW,aAAa,CAAC,CAAC;gBACtCqW,UAAU,CAACpW,WAAW,CAACuV,KAAK,CAAC;gBAC7BE,kBAAkB,CAAC7Q,kBAAkB,GAAG,CAACwR,UAAU,EAAE,GAAGX,kBAAkB,CAAC7Q,kBAAkB,CAAC;cAChG,CAAC,MAAM;gBACL,MAAMyR,sBAAsB,GAAGZ,kBAAkB,CAAC1T,aAAa,CAAC,OAAO,CAAC;gBACxE,IAAIsU,sBAAsB,EAAE;kBAC1BA,sBAAsB,CAACxL,SAAS,GAAG0K,KAAK,GAAGc,sBAAsB,CAACxL,SAAS;gBAC7E,CAAC,MAAM;kBACL4K,kBAAkB,CAACpP,OAAO,CAACuP,QAAQ,CAAC;gBACtC;cACF;YACF,CAAC,MAAM;cACLH,kBAAkB,CAACjL,MAAM,CAACoL,QAAQ,CAAC;YACrC;UACF;UACA,IAAI3Z,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,8BAA8B;YACpDuZ,kBAAkB,CAACvK,YAAY,CAAC0K,QAAQ,EAAE,IAAI,CAAC;UACjD;QACF;QACA,IAAI3Z,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,yBAAyB;UAC/C0Z,QAAQ,CAAC/K,SAAS,IAAIxM,WAAW;QACnC;QACA,IAAIsX,aAAa,EAAE;UACjBA,aAAa,CAAChgB,GAAG,CAACob,QAAQ,CAAC;QAC7B;MACF;IACF,CAAC,MAAM,IAAI,CAAC0E,kBAAkB,CAAC7Q,kBAAkB,CAAC1Q,QAAQ,CAACqhB,KAAK,CAAC,EAAE;MACjEE,kBAAkB,CAAC7Q,kBAAkB,GAAG,CAAC,GAAG6Q,kBAAkB,CAAC7Q,kBAAkB,EAAE2Q,KAAK,CAAC;IAC3F;EACF;EACA,OAAOxE,QAAQ;AACjB,CAAC;AACD,IAAIuF,YAAY,GAAIza,OAAO,IAAK;EAC9B,MAAMI,OAAO,GAAGJ,OAAO,CAACO,SAAS;EACjC,MAAMpC,GAAG,GAAG6B,OAAO,CAACM,aAAa;EACjC,MAAM6G,KAAK,GAAG/G,OAAO,CAACC,OAAO;EAC7B,MAAMqa,eAAe,GAAG/I,UAAU,CAAC,cAAc,EAAEvR,OAAO,CAACkB,SAAS,CAAC;EACrE,MAAM4T,QAAQ,GAAGyE,QAAQ,CACvBxb,GAAG,CAACwK,UAAU,GAAGxK,GAAG,CAACwK,UAAU,GAAGxK,GAAG,CAACwc,WAAW,CAAC,CAAC,EACnDva,OAAO,EACPJ,OAAO,CAAC2Y,UACV,CAAC;EACD,IAAIxR,KAAK,GAAG,EAAE,CAAC,gCAAgC;IAC7ChJ,GAAG,CAAC,MAAM,CAAC,GAAG+W,QAAQ;IACtB/W,GAAG,CAACtE,SAAS,CAACC,GAAG,CAACob,QAAQ,GAAG,IAAI,CAAC;EACpC;EACAwF,eAAe,CAAC,CAAC;AACnB,CAAC;AACD,IAAIb,UAAU,GAAGA,CAACe,GAAG,EAAE7c,IAAI,KAAK,KAAK,IAAIA,IAAI,IAAI6c,GAAG,CAACva,OAAO,GAAG,EAAE,CAAC,gBAAgBua,GAAG,CAACtZ,SAAS,GAAG,GAAG,GAAGvD,IAAI,GAAG6c,GAAG,CAACtZ,SAAS,CAAC;AAC7H,IAAIuZ,qBAAqB,GAAIC,GAAG,IAAKA,GAAG,CAACvZ,OAAO,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACtF,IAAIwZ,qBAAqB,GAAGA,CAAA,KAAM;EAChC,IAAI,CAAC3kB,GAAG,CAACuD,QAAQ,EAAE;IACjB;EACF;EACA,MAAMqhB,OAAO,GAAG5kB,GAAG,CAACuD,QAAQ,CAAC0gB,gBAAgB,CAAC,IAAIhY,iBAAiB,GAAG,CAAC;EACvE,IAAI0C,EAAE,GAAG,CAAC;EACV,OAAOA,EAAE,GAAGiW,OAAO,CAACrjB,MAAM,EAAEoN,EAAE,EAAE,EAAE;IAChCwU,aAAa,CAACyB,OAAO,CAACjW,EAAE,CAAC,CAAC/G,YAAY,CAACqE,iBAAiB,CAAC,EAAEwY,qBAAqB,CAACG,OAAO,CAACjW,EAAE,CAAC,CAACiK,SAAS,CAAC,EAAE,IAAI,CAAC;EAChH;AACF,CAAC;AACD,IAAIiM,WAAW,GAAGA,CAAC9c,GAAG,EAAE6C,UAAU,EAAEka,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,EAAEjU,KAAK,EAAEkU,aAAa,KAAK;EACtF,IAAIH,QAAQ,KAAKC,QAAQ,EAAE;IACzB;EACF;EACA,IAAIG,MAAM,GAAGva,iBAAiB,CAAC5C,GAAG,EAAE6C,UAAU,CAAC;EAC/C,IAAIua,EAAE,GAAGva,UAAU,CAACjI,WAAW,CAAC,CAAC;EACjC,IAAIiI,UAAU,KAAK,OAAO,EAAE;IAC1B,MAAMnH,SAAS,GAAGsE,GAAG,CAACtE,SAAS;IAC/B,MAAM2hB,UAAU,GAAGC,cAAc,CAACP,QAAQ,CAAC;IAC3C,IAAIQ,UAAU,GAAGD,cAAc,CAACN,QAAQ,CAAC;IACzC,IAAIhd,GAAG,CAAC,MAAM,CAAC,IAAIkd,aAAa,EAAE;MAChCK,UAAU,CAAC/W,IAAI,CAACxG,GAAG,CAAC,MAAM,CAAC,CAAC;MAC5Bqd,UAAU,CAAC5jB,OAAO,CAAEf,CAAC,IAAK;QACxB,IAAIA,CAAC,CAACY,UAAU,CAAC0G,GAAG,CAAC,MAAM,CAAC,CAAC,EAAEud,UAAU,CAAC/W,IAAI,CAAC9N,CAAC,CAAC;MACnD,CAAC,CAAC;MACF6kB,UAAU,GAAG,CAAC,GAAG,IAAIrU,GAAG,CAACqU,UAAU,CAAC,CAAC;MACrC7hB,SAAS,CAACC,GAAG,CAAC,GAAG4hB,UAAU,CAAC;IAC9B,CAAC,MAAM;MACL7hB,SAAS,CAACyU,MAAM,CAAC,GAAGkN,UAAU,CAAChkB,MAAM,CAAEX,CAAC,IAAKA,CAAC,IAAI,CAAC6kB,UAAU,CAACrjB,QAAQ,CAACxB,CAAC,CAAC,CAAC,CAAC;MAC3EgD,SAAS,CAACC,GAAG,CAAC,GAAG4hB,UAAU,CAAClkB,MAAM,CAAEX,CAAC,IAAKA,CAAC,IAAI,CAAC2kB,UAAU,CAACnjB,QAAQ,CAACxB,CAAC,CAAC,CAAC,CAAC;IAC1E;EACF,CAAC,MAAM,IAAImK,UAAU,KAAK,OAAO,EAAE;IACjC;MACE,KAAK,MAAM2a,IAAI,IAAIT,QAAQ,EAAE;QAC3B,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACQ,IAAI,CAAC,IAAI,IAAI,EAAE;UACvC,IAAIA,IAAI,CAACtjB,QAAQ,CAAC,GAAG,CAAC,EAAE;YACtB8F,GAAG,CAACub,KAAK,CAACkC,cAAc,CAACD,IAAI,CAAC;UAChC,CAAC,MAAM;YACLxd,GAAG,CAACub,KAAK,CAACiC,IAAI,CAAC,GAAG,EAAE;UACtB;QACF;MACF;IACF;IACA,KAAK,MAAMA,IAAI,IAAIR,QAAQ,EAAE;MAC3B,IAAI,CAACD,QAAQ,IAAIC,QAAQ,CAACQ,IAAI,CAAC,KAAKT,QAAQ,CAACS,IAAI,CAAC,EAAE;QAClD,IAAIA,IAAI,CAACtjB,QAAQ,CAAC,GAAG,CAAC,EAAE;UACtB8F,GAAG,CAACub,KAAK,CAACmC,WAAW,CAACF,IAAI,EAAER,QAAQ,CAACQ,IAAI,CAAC,CAAC;QAC7C,CAAC,MAAM;UACLxd,GAAG,CAACub,KAAK,CAACiC,IAAI,CAAC,GAAGR,QAAQ,CAACQ,IAAI,CAAC;QAClC;MACF;IACF;EACF,CAAC,MAAM,IAAI3a,UAAU,KAAK,KAAK,EAAE,CAAC,KAAM,IAAIA,UAAU,KAAK,KAAK,EAAE;IAChE,IAAIma,QAAQ,EAAE;MACZA,QAAQ,CAAChd,GAAG,CAAC;IACf;EACF,CAAC,MAAM,IAAK,CAACmd,MAAM,IAAMta,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACvE,IAAIA,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACzBA,UAAU,GAAGA,UAAU,CAAC7J,KAAK,CAAC,CAAC,CAAC;IAClC,CAAC,MAAM,IAAI4J,iBAAiB,CAAC3K,GAAG,EAAEmlB,EAAE,CAAC,EAAE;MACrCva,UAAU,GAAGua,EAAE,CAACpkB,KAAK,CAAC,CAAC,CAAC;IAC1B,CAAC,MAAM;MACL6J,UAAU,GAAGua,EAAE,CAAC,CAAC,CAAC,GAAGva,UAAU,CAAC7J,KAAK,CAAC,CAAC,CAAC;IAC1C;IACA,IAAI+jB,QAAQ,IAAIC,QAAQ,EAAE;MACxB,MAAMW,OAAO,GAAG9a,UAAU,CAAC8X,QAAQ,CAACiD,oBAAoB,CAAC;MACzD/a,UAAU,GAAGA,UAAU,CAACO,OAAO,CAACya,mBAAmB,EAAE,EAAE,CAAC;MACxD,IAAId,QAAQ,EAAE;QACZtY,GAAG,CAACW,GAAG,CAACpF,GAAG,EAAE6C,UAAU,EAAEka,QAAQ,EAAEY,OAAO,CAAC;MAC7C;MACA,IAAIX,QAAQ,EAAE;QACZvY,GAAG,CAACM,GAAG,CAAC/E,GAAG,EAAE6C,UAAU,EAAEma,QAAQ,EAAEW,OAAO,CAAC;MAC7C;IACF;EACF,CAAC,MAAM;IACL,MAAMG,SAAS,GAAGrW,aAAa,CAACuV,QAAQ,CAAC;IACzC,IAAI,CAACG,MAAM,IAAIW,SAAS,IAAId,QAAQ,KAAK,IAAI,KAAK,CAACC,KAAK,EAAE;MACxD,IAAI;QACF,IAAI,CAACjd,GAAG,CAACrF,OAAO,CAACT,QAAQ,CAAC,GAAG,CAAC,EAAE;UAC9B,MAAM2S,CAAC,GAAGmQ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAGA,QAAQ;UAC1C,IAAIna,UAAU,KAAK,MAAM,EAAE;YACzBsa,MAAM,GAAG,KAAK;UAChB,CAAC,MAAM,IAAIJ,QAAQ,IAAI,IAAI,IAAI/c,GAAG,CAAC6C,UAAU,CAAC,IAAIgK,CAAC,EAAE;YACnD,IAAI,OAAO7M,GAAG,CAAC+d,gBAAgB,CAAClb,UAAU,CAAC,KAAK,UAAU,EAAE;cAC1D7C,GAAG,CAAC6C,UAAU,CAAC,GAAGgK,CAAC;YACrB,CAAC,MAAM;cACL7M,GAAG,CAACF,YAAY,CAAC+C,UAAU,EAAEgK,CAAC,CAAC;YACjC;UACF;QACF,CAAC,MAAM,IAAI7M,GAAG,CAAC6C,UAAU,CAAC,KAAKma,QAAQ,EAAE;UACvChd,GAAG,CAAC6C,UAAU,CAAC,GAAGma,QAAQ;QAC5B;MACF,CAAC,CAAC,OAAOxkB,CAAC,EAAE,CACZ;IACF;IACA,IAAIwlB,KAAK,GAAG,KAAK;IACjB;MACE,IAAIZ,EAAE,MAAMA,EAAE,GAAGA,EAAE,CAACha,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,EAAE;QAC7CP,UAAU,GAAGua,EAAE;QACfY,KAAK,GAAG,IAAI;MACd;IACF;IACA,IAAIhB,QAAQ,IAAI,IAAI,IAAIA,QAAQ,KAAK,KAAK,EAAE;MAC1C,IAAIA,QAAQ,KAAK,KAAK,IAAIhd,GAAG,CAACH,YAAY,CAACgD,UAAU,CAAC,KAAK,EAAE,EAAE;QAC7D,IAAImb,KAAK,EAAE;UACThe,GAAG,CAACie,iBAAiB,CAAC3Z,QAAQ,EAAEzB,UAAU,CAAC;QAC7C,CAAC,MAAM;UACL7C,GAAG,CAACoX,eAAe,CAACvU,UAAU,CAAC;QACjC;MACF;IACF,CAAC,MAAM,IAAI,CAAC,CAACsa,MAAM,IAAInU,KAAK,GAAG,CAAC,CAAC,gBAAgBiU,KAAK,KAAK,CAACa,SAAS,IAAI9d,GAAG,CAACkL,QAAQ,KAAK,CAAC,CAAC,mBAAmB;MAC7G8R,QAAQ,GAAGA,QAAQ,KAAK,IAAI,GAAG,EAAE,GAAGA,QAAQ;MAC5C,IAAIgB,KAAK,EAAE;QACThe,GAAG,CAACke,cAAc,CAAC5Z,QAAQ,EAAEzB,UAAU,EAAEma,QAAQ,CAAC;MACpD,CAAC,MAAM;QACLhd,GAAG,CAACF,YAAY,CAAC+C,UAAU,EAAEma,QAAQ,CAAC;MACxC;IACF;EACF;AACF,CAAC;AACD,IAAImB,mBAAmB,GAAG,IAAI;AAC9B,IAAIb,cAAc,GAAIhmB,KAAK,IAAK;EAC9B,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,IAAI,SAAS,IAAIA,KAAK,EAAE;IAC5DA,KAAK,GAAGA,KAAK,CAAC8mB,OAAO;EACvB;EACA,IAAI,CAAC9mB,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACvC,OAAO,EAAE;EACX;EACA,OAAOA,KAAK,CAAC2B,KAAK,CAACklB,mBAAmB,CAAC;AACzC,CAAC;AACD,IAAIP,oBAAoB,GAAG,SAAS;AACpC,IAAIC,mBAAmB,GAAG,IAAI5U,MAAM,CAAC2U,oBAAoB,GAAG,GAAG,CAAC;;AAEhE;AACA,IAAIS,aAAa,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,eAAe,KAAK;EACvE,MAAMze,GAAG,GAAGue,QAAQ,CAACnJ,KAAK,CAAClK,QAAQ,KAAK,EAAE,CAAC,0BAA0BqT,QAAQ,CAACnJ,KAAK,CAAC0G,IAAI,GAAGyC,QAAQ,CAACnJ,KAAK,CAAC0G,IAAI,GAAGyC,QAAQ,CAACnJ,KAAK;EAC/H,MAAMsJ,aAAa,GAAGJ,QAAQ,IAAIA,QAAQ,CAACxJ,OAAO,IAAI,CAAC,CAAC;EACxD,MAAM6J,aAAa,GAAGJ,QAAQ,CAACzJ,OAAO,IAAI,CAAC,CAAC;EAC5C;IACE,KAAK,MAAMjS,UAAU,IAAI+b,eAAe,CAAC3nB,MAAM,CAAC4E,IAAI,CAAC6iB,aAAa,CAAC,CAAC,EAAE;MACpE,IAAI,EAAE7b,UAAU,IAAI8b,aAAa,CAAC,EAAE;QAClC7B,WAAW,CACT9c,GAAG,EACH6C,UAAU,EACV6b,aAAa,CAAC7b,UAAU,CAAC,EACzB,KAAK,CAAC,EACN2b,UAAU,EACVD,QAAQ,CAACrc,OAAO,EAChBuc,eACF,CAAC;MACH;IACF;EACF;EACA,KAAK,MAAM5b,UAAU,IAAI+b,eAAe,CAAC3nB,MAAM,CAAC4E,IAAI,CAAC8iB,aAAa,CAAC,CAAC,EAAE;IACpE7B,WAAW,CACT9c,GAAG,EACH6C,UAAU,EACV6b,aAAa,CAAC7b,UAAU,CAAC,EACzB8b,aAAa,CAAC9b,UAAU,CAAC,EACzB2b,UAAU,EACVD,QAAQ,CAACrc,OAAO,EAChBuc,eACF,CAAC;EACH;AACF,CAAC;AACD,SAASG,eAAeA,CAACC,SAAS,EAAE;EAClC,OAAOA,SAAS,CAAC3kB,QAAQ,CAAC,KAAK,CAAC;EAC9B;EACA,CAAC,GAAG2kB,SAAS,CAACxlB,MAAM,CAAEylB,IAAI,IAAKA,IAAI,KAAK,KAAK,CAAC,EAAE,KAAK,CAAC;EAEtD;EACAD,SACD;AACH;;AAEA;AACA,IAAIE,OAAO;AACX,IAAIC,UAAU;AACd,IAAIC,WAAW;AACf,IAAIC,kBAAkB,GAAG,KAAK;AAC9B,IAAIC,2BAA2B,GAAG,KAAK;AACvC,IAAIC,iBAAiB,GAAG,KAAK;AAC7B,IAAIC,SAAS,GAAG,KAAK;AACrB,IAAIC,SAAS,GAAGA,CAACC,cAAc,EAAEC,cAAc,EAAEC,UAAU,KAAK;EAC9D,IAAI5hB,EAAE;EACN,MAAM6hB,SAAS,GAAGF,cAAc,CAACzK,UAAU,CAAC0K,UAAU,CAAC;EACvD,IAAI7Y,EAAE,GAAG,CAAC;EACV,IAAI5G,GAAG;EACP,IAAIsL,SAAS;EACb,IAAIqU,QAAQ;EACZ,IAAI,CAACT,kBAAkB,EAAE;IACvBE,iBAAiB,GAAG,IAAI;IACxB,IAAIM,SAAS,CAACvK,KAAK,KAAK,MAAM,EAAE;MAC9BuK,SAAS,CAACxd,OAAO,IAAIwd,SAAS,CAAC3K,UAAU;MACvC;MACA;MACA,CAAC,CAAC;MAEF;MACA;MACA;MACA,CAAC,CAAC,qBACH;IACH;EACF;EACA,IAAI2K,SAAS,CAACpL,MAAM,KAAK,IAAI,EAAE;IAC7BtU,GAAG,GAAG0f,SAAS,CAACtK,KAAK,GAAGnd,GAAG,CAACuD,QAAQ,CAACgR,cAAc,CAACkT,SAAS,CAACpL,MAAM,CAAC;EACvE,CAAC,MAAM,IAAIoL,SAAS,CAACxd,OAAO,GAAG,CAAC,CAAC,uBAAuB;IACtDlC,GAAG,GAAG0f,SAAS,CAACtK,KAAK,GAAGnd,GAAG,CAACuD,QAAQ,CAACgR,cAAc,CAAC,EAAE,CAAC;IACvD;MACE6R,aAAa,CAAC,IAAI,EAAEqB,SAAS,EAAEL,SAAS,CAAC;IAC3C;EACF,CAAC,MAAM;IACL,IAAI,CAACA,SAAS,EAAE;MACdA,SAAS,GAAGK,SAAS,CAACvK,KAAK,KAAK,KAAK;IACvC;IACA,IAAI,CAACld,GAAG,CAACuD,QAAQ,EAAE;MACjB,MAAM,IAAI4N,KAAK,CACb,wOACF,CAAC;IACH;IACApJ,GAAG,GAAG0f,SAAS,CAACtK,KAAK,GAAGnd,GAAG,CAACuD,QAAQ,CAACokB,eAAe,CAClDP,SAAS,GAAGre,MAAM,GAAGC,OAAO,EAC5B,CAACie,kBAAkB,IAAI9oB,KAAK,CAACK,cAAc,IAAIipB,SAAS,CAACxd,OAAO,GAAG,CAAC,CAAC,uBAAuB,SAAS,GAAGwd,SAAS,CAACvK,KACpH,CAAC;IACD,IAAIkK,SAAS,IAAIK,SAAS,CAACvK,KAAK,KAAK,eAAe,EAAE;MACpDkK,SAAS,GAAG,KAAK;IACnB;IACA;MACEhB,aAAa,CAAC,IAAI,EAAEqB,SAAS,EAAEL,SAAS,CAAC;IAC3C;IACA,IAAI7X,KAAK,CAACuX,OAAO,CAAC,IAAI/e,GAAG,CAAC,MAAM,CAAC,KAAK+e,OAAO,EAAE;MAC7C/e,GAAG,CAACtE,SAAS,CAACC,GAAG,CAACqE,GAAG,CAAC,MAAM,CAAC,GAAG+e,OAAO,CAAC;IAC1C;IACA,IAAIW,SAAS,CAAC3K,UAAU,EAAE;MACxB,KAAKnO,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG8Y,SAAS,CAAC3K,UAAU,CAACvb,MAAM,EAAE,EAAEoN,EAAE,EAAE;QACnD0E,SAAS,GAAGgU,SAAS,CAACC,cAAc,EAAEG,SAAS,EAAE9Y,EAAE,CAAC;QACpD,IAAI0E,SAAS,EAAE;UACbtL,GAAG,CAAC2P,WAAW,CAACrE,SAAS,CAAC;QAC5B;MACF;IACF;IACA;MACE,IAAIoU,SAAS,CAACvK,KAAK,KAAK,KAAK,EAAE;QAC7BkK,SAAS,GAAG,KAAK;MACnB,CAAC,MAAM,IAAIrf,GAAG,CAACrF,OAAO,KAAK,eAAe,EAAE;QAC1C0kB,SAAS,GAAG,IAAI;MAClB;IACF;EACF;EACArf,GAAG,CAAC,MAAM,CAAC,GAAGif,WAAW;EACzB;IACE,IAAIS,SAAS,CAACxd,OAAO,IAAI,CAAC,CAAC,uBAAuB,CAAC,CAAC,sBAAsB,EAAE;MAC1ElC,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI;MAClBA,GAAG,CAAC,MAAM,CAAC,GAAGgf,UAAU;MACxBhf,GAAG,CAAC,MAAM,CAAC,GAAG0f,SAAS,CAACzK,MAAM,IAAI,EAAE;MACpCjV,GAAG,CAAC,MAAM,CAAC,GAAG,CAACnC,EAAE,GAAG6hB,SAAS,CAAC5K,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGjX,EAAE,CAACwB,GAAG;MAChE4N,aAAa,CAACjN,GAAG,CAAC;MAClB2f,QAAQ,GAAGJ,cAAc,IAAIA,cAAc,CAACxK,UAAU,IAAIwK,cAAc,CAACxK,UAAU,CAAC0K,UAAU,CAAC;MAC/F,IAAIE,QAAQ,IAAIA,QAAQ,CAACxK,KAAK,KAAKuK,SAAS,CAACvK,KAAK,IAAIoK,cAAc,CAACnK,KAAK,EAAE;QAC1E;UACEyK,kBAAkB,CAACN,cAAc,CAACnK,KAAK,CAAC;QAC1C;MACF;MACA;QACE0K,wBAAwB,CAACd,UAAU,EAAEhf,GAAG,EAAEwf,cAAc,CAACpK,KAAK,EAAEmK,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACnK,KAAK,CAAC;MACzH;IACF;EACF;EACA,OAAOpV,GAAG;AACZ,CAAC;AACD,IAAI6f,kBAAkB,GAAIE,SAAS,IAAK;EACtCtb,GAAG,CAACvC,OAAO,IAAI,CAAC,CAAC;EACjB,MAAM4Z,IAAI,GAAGiE,SAAS,CAACC,OAAO,CAACf,WAAW,CAACrkB,WAAW,CAAC,CAAC,CAAC;EACzD,IAAIkhB,IAAI,IAAI,IAAI,EAAE;IAChB,MAAMmE,cAAc,GAAGpO,KAAK,CAAC8G,IAAI,CAACmD,IAAI,CAACtO,YAAY,IAAIsO,IAAI,CAAChR,UAAU,CAAC,CAACwP,IAAI,CACzEjb,GAAG,IAAKA,GAAG,CAAC,MAAM,CACrB,CAAC;IACD,MAAM6gB,cAAc,GAAGrO,KAAK,CAAC8G,IAAI,CAC/BoH,SAAS,CAACvS,YAAY,IAAIuS,SAAS,CAACjV,UACtC,CAAC;IACD,KAAK,MAAMQ,SAAS,IAAI2U,cAAc,GAAGC,cAAc,CAACC,OAAO,CAAC,CAAC,GAAGD,cAAc,EAAE;MAClF,IAAI5U,SAAS,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;QAC7B4F,YAAY,CAAC4K,IAAI,EAAExQ,SAAS,EAAE2U,cAAc,IAAI,IAAI,GAAGA,cAAc,GAAG,IAAI,CAAC;QAC7E3U,SAAS,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;QAC1B8T,iBAAiB,GAAG,IAAI;MAC1B;IACF;EACF;EACA3a,GAAG,CAACvC,OAAO,IAAI,CAAC,CAAC,CAAC;AACpB,CAAC;AACD,IAAIke,yBAAyB,GAAGA,CAACL,SAAS,EAAEM,SAAS,KAAK;EACxD5b,GAAG,CAACvC,OAAO,IAAI,CAAC,CAAC;EACjB,MAAMoe,iBAAiB,GAAGzO,KAAK,CAAC8G,IAAI,CAACoH,SAAS,CAACvS,YAAY,IAAIuS,SAAS,CAACjV,UAAU,CAAC;EACpF,IAAIiV,SAAS,CAAC,MAAM,CAAC,IAAI3pB,KAAK,CAACC,qBAAqB,EAAE;IACpD,IAAI0V,IAAI,GAAGgU,SAAS;IACpB,OAAOhU,IAAI,GAAGA,IAAI,CAACC,WAAW,EAAE;MAC9B,IAAID,IAAI,IAAIA,IAAI,CAAC,MAAM,CAAC,KAAKgU,SAAS,CAAC,MAAM,CAAC,IAAIhU,IAAI,CAAC,MAAM,CAAC,KAAKkT,WAAW,EAAE;QAC9EqB,iBAAiB,CAAC9Z,IAAI,CAACuF,IAAI,CAAC;MAC9B;IACF;EACF;EACA,KAAK,IAAInF,EAAE,GAAG0Z,iBAAiB,CAAC9mB,MAAM,GAAG,CAAC,EAAEoN,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;IACzD,MAAM0E,SAAS,GAAGgV,iBAAiB,CAAC1Z,EAAE,CAAC;IACvC,IAAI0E,SAAS,CAAC,MAAM,CAAC,KAAK2T,WAAW,IAAI3T,SAAS,CAAC,MAAM,CAAC,EAAE;MAC1D4F,YAAY,CAACqP,aAAa,CAACjV,SAAS,CAAC,CAACmB,UAAU,EAAEnB,SAAS,EAAEiV,aAAa,CAACjV,SAAS,CAAC,CAAC;MACtFA,SAAS,CAAC,MAAM,CAAC,CAAC6E,MAAM,CAAC,CAAC;MAC1B7E,SAAS,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;MAC1BA,SAAS,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;MAC1B8T,iBAAiB,GAAG,IAAI;IAC1B;IACA,IAAIiB,SAAS,EAAE;MACbD,yBAAyB,CAAC9U,SAAS,EAAE+U,SAAS,CAAC;IACjD;EACF;EACA5b,GAAG,CAACvC,OAAO,IAAI,CAAC,CAAC,CAAC;AACpB,CAAC;AACD,IAAIse,SAAS,GAAGA,CAACT,SAAS,EAAEU,MAAM,EAAE3H,WAAW,EAAE4H,MAAM,EAAEC,QAAQ,EAAEC,MAAM,KAAK;EAC5E,IAAIC,YAAY,GAAGd,SAAS,CAAC,MAAM,CAAC,IAAIA,SAAS,CAAC,MAAM,CAAC,CAACtT,UAAU,IAAIsT,SAAS;EACjF,IAAIzU,SAAS;EACb,IAAIuV,YAAY,CAACrW,UAAU,IAAIqW,YAAY,CAAClmB,OAAO,KAAKskB,WAAW,EAAE;IACnE4B,YAAY,GAAGA,YAAY,CAACrW,UAAU;EACxC;EACA,OAAOmW,QAAQ,IAAIC,MAAM,EAAE,EAAED,QAAQ,EAAE;IACrC,IAAID,MAAM,CAACC,QAAQ,CAAC,EAAE;MACpBrV,SAAS,GAAGgU,SAAS,CAAC,IAAI,EAAExG,WAAW,EAAE6H,QAAQ,CAAC;MAClD,IAAIrV,SAAS,EAAE;QACboV,MAAM,CAACC,QAAQ,CAAC,CAACvL,KAAK,GAAG9J,SAAS;QAClC4F,YAAY,CAAC2P,YAAY,EAAEvV,SAAS,EAAEiV,aAAa,CAACE,MAAM,CAAE,CAAC;MAC/D;IACF;EACF;AACF,CAAC;AACD,IAAIK,YAAY,GAAGA,CAACJ,MAAM,EAAEC,QAAQ,EAAEC,MAAM,KAAK;EAC/C,KAAK,IAAIlO,KAAK,GAAGiO,QAAQ,EAAEjO,KAAK,IAAIkO,MAAM,EAAE,EAAElO,KAAK,EAAE;IACnD,MAAMmC,KAAK,GAAG6L,MAAM,CAAChO,KAAK,CAAC;IAC3B,IAAImC,KAAK,EAAE;MACT,MAAM7U,GAAG,GAAG6U,KAAK,CAACO,KAAK;MACvB2L,gBAAgB,CAAClM,KAAK,CAAC;MACvB,IAAI7U,GAAG,EAAE;QACP;UACEmf,2BAA2B,GAAG,IAAI;UAClC,IAAInf,GAAG,CAAC,MAAM,CAAC,EAAE;YACfA,GAAG,CAAC,MAAM,CAAC,CAACmQ,MAAM,CAAC,CAAC;UACtB,CAAC,MAAM;YACLiQ,yBAAyB,CAACpgB,GAAG,EAAE,IAAI,CAAC;UACtC;QACF;QACAA,GAAG,CAACmQ,MAAM,CAAC,CAAC;MACd;IACF;EACF;AACF,CAAC;AACD,IAAI6Q,cAAc,GAAGA,CAACjB,SAAS,EAAEkB,KAAK,EAAEvB,SAAS,EAAEwB,KAAK,EAAEzC,eAAe,GAAG,KAAK,KAAK;EACpF,IAAI0C,WAAW,GAAG,CAAC;EACnB,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAIza,EAAE,GAAG,CAAC;EACV,IAAI0a,SAAS,GAAGL,KAAK,CAACznB,MAAM,GAAG,CAAC;EAChC,IAAI+nB,aAAa,GAAGN,KAAK,CAAC,CAAC,CAAC;EAC5B,IAAIO,WAAW,GAAGP,KAAK,CAACK,SAAS,CAAC;EAClC,IAAIG,SAAS,GAAGP,KAAK,CAAC1nB,MAAM,GAAG,CAAC;EAChC,IAAIkoB,aAAa,GAAGR,KAAK,CAAC,CAAC,CAAC;EAC5B,IAAIS,WAAW,GAAGT,KAAK,CAACO,SAAS,CAAC;EAClC,IAAI1V,IAAI;EACR,IAAI6V,SAAS;EACb,OAAOT,WAAW,IAAIG,SAAS,IAAIF,WAAW,IAAIK,SAAS,EAAE;IAC3D,IAAIF,aAAa,IAAI,IAAI,EAAE;MACzBA,aAAa,GAAGN,KAAK,CAAC,EAAEE,WAAW,CAAC;IACtC,CAAC,MAAM,IAAIK,WAAW,IAAI,IAAI,EAAE;MAC9BA,WAAW,GAAGP,KAAK,CAAC,EAAEK,SAAS,CAAC;IAClC,CAAC,MAAM,IAAII,aAAa,IAAI,IAAI,EAAE;MAChCA,aAAa,GAAGR,KAAK,CAAC,EAAEE,WAAW,CAAC;IACtC,CAAC,MAAM,IAAIO,WAAW,IAAI,IAAI,EAAE;MAC9BA,WAAW,GAAGT,KAAK,CAAC,EAAEO,SAAS,CAAC;IAClC,CAAC,MAAM,IAAII,WAAW,CAACN,aAAa,EAAEG,aAAa,EAAEjD,eAAe,CAAC,EAAE;MACrEqD,KAAK,CAACP,aAAa,EAAEG,aAAa,EAAEjD,eAAe,CAAC;MACpD8C,aAAa,GAAGN,KAAK,CAAC,EAAEE,WAAW,CAAC;MACpCO,aAAa,GAAGR,KAAK,CAAC,EAAEE,WAAW,CAAC;IACtC,CAAC,MAAM,IAAIS,WAAW,CAACL,WAAW,EAAEG,WAAW,EAAElD,eAAe,CAAC,EAAE;MACjEqD,KAAK,CAACN,WAAW,EAAEG,WAAW,EAAElD,eAAe,CAAC;MAChD+C,WAAW,GAAGP,KAAK,CAAC,EAAEK,SAAS,CAAC;MAChCK,WAAW,GAAGT,KAAK,CAAC,EAAEO,SAAS,CAAC;IAClC,CAAC,MAAM,IAAII,WAAW,CAACN,aAAa,EAAEI,WAAW,EAAElD,eAAe,CAAC,EAAE;MACnE,IAAK8C,aAAa,CAACpM,KAAK,KAAK,MAAM,IAAIwM,WAAW,CAACxM,KAAK,KAAK,MAAM,EAAG;QACpEiL,yBAAyB,CAACmB,aAAa,CAACnM,KAAK,CAAC3I,UAAU,EAAE,KAAK,CAAC;MAClE;MACAqV,KAAK,CAACP,aAAa,EAAEI,WAAW,EAAElD,eAAe,CAAC;MAClDvN,YAAY,CAAC6O,SAAS,EAAEwB,aAAa,CAACnM,KAAK,EAAEoM,WAAW,CAACpM,KAAK,CAACpJ,WAAW,CAAC;MAC3EuV,aAAa,GAAGN,KAAK,CAAC,EAAEE,WAAW,CAAC;MACpCQ,WAAW,GAAGT,KAAK,CAAC,EAAEO,SAAS,CAAC;IAClC,CAAC,MAAM,IAAII,WAAW,CAACL,WAAW,EAAEE,aAAa,EAAEjD,eAAe,CAAC,EAAE;MACnE,IAAK8C,aAAa,CAACpM,KAAK,KAAK,MAAM,IAAIwM,WAAW,CAACxM,KAAK,KAAK,MAAM,EAAG;QACpEiL,yBAAyB,CAACoB,WAAW,CAACpM,KAAK,CAAC3I,UAAU,EAAE,KAAK,CAAC;MAChE;MACAqV,KAAK,CAACN,WAAW,EAAEE,aAAa,EAAEjD,eAAe,CAAC;MAClDvN,YAAY,CAAC6O,SAAS,EAAEyB,WAAW,CAACpM,KAAK,EAAEmM,aAAa,CAACnM,KAAK,CAAC;MAC/DoM,WAAW,GAAGP,KAAK,CAAC,EAAEK,SAAS,CAAC;MAChCI,aAAa,GAAGR,KAAK,CAAC,EAAEE,WAAW,CAAC;IACtC,CAAC,MAAM;MACLC,QAAQ,GAAG,CAAC,CAAC;MACb;QACE,KAAKza,EAAE,GAAGua,WAAW,EAAEva,EAAE,IAAI0a,SAAS,EAAE,EAAE1a,EAAE,EAAE;UAC5C,IAAIqa,KAAK,CAACra,EAAE,CAAC,IAAIqa,KAAK,CAACra,EAAE,CAAC,CAACoO,KAAK,KAAK,IAAI,IAAIiM,KAAK,CAACra,EAAE,CAAC,CAACoO,KAAK,KAAK0M,aAAa,CAAC1M,KAAK,EAAE;YACpFqM,QAAQ,GAAGza,EAAE;YACb;UACF;QACF;MACF;MACA,IAAIya,QAAQ,IAAI,CAAC,EAAE;QACjBO,SAAS,GAAGX,KAAK,CAACI,QAAQ,CAAC;QAC3B,IAAIO,SAAS,CAACzM,KAAK,KAAKuM,aAAa,CAACvM,KAAK,EAAE;UAC3CpJ,IAAI,GAAGuT,SAAS,CAAC2B,KAAK,IAAIA,KAAK,CAACG,WAAW,CAAC,EAAE1B,SAAS,EAAE2B,QAAQ,CAAC;QACpE,CAAC,MAAM;UACLS,KAAK,CAACF,SAAS,EAAEF,aAAa,EAAEjD,eAAe,CAAC;UAChDwC,KAAK,CAACI,QAAQ,CAAC,GAAG,KAAK,CAAC;UACxBtV,IAAI,GAAG6V,SAAS,CAACxM,KAAK;QACxB;QACAsM,aAAa,GAAGR,KAAK,CAAC,EAAEE,WAAW,CAAC;MACtC,CAAC,MAAM;QACLrV,IAAI,GAAGuT,SAAS,CAAC2B,KAAK,IAAIA,KAAK,CAACG,WAAW,CAAC,EAAE1B,SAAS,EAAE0B,WAAW,CAAC;QACrEM,aAAa,GAAGR,KAAK,CAAC,EAAEE,WAAW,CAAC;MACtC;MACA,IAAIrV,IAAI,EAAE;QACR;UACEmF,YAAY,CACVqP,aAAa,CAACgB,aAAa,CAACnM,KAAK,CAAC,CAAC3I,UAAU,EAC7CV,IAAI,EACJwU,aAAa,CAACgB,aAAa,CAACnM,KAAK,CACnC,CAAC;QACH;MACF;IACF;EACF;EACA,IAAI+L,WAAW,GAAGG,SAAS,EAAE;IAC3Bd,SAAS,CACPT,SAAS,EACTmB,KAAK,CAACO,SAAS,GAAG,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAGP,KAAK,CAACO,SAAS,GAAG,CAAC,CAAC,CAACrM,KAAK,EAChEsK,SAAS,EACTwB,KAAK,EACLE,WAAW,EACXK,SACF,CAAC;EACH,CAAC,MAAM,IAAIL,WAAW,GAAGK,SAAS,EAAE;IAClCX,YAAY,CAACG,KAAK,EAAEE,WAAW,EAAEG,SAAS,CAAC;EAC7C;AACF,CAAC;AACD,IAAIO,WAAW,GAAGA,CAACE,SAAS,EAAEC,UAAU,EAAEvD,eAAe,GAAG,KAAK,KAAK;EACpE,IAAIsD,SAAS,CAAC5M,KAAK,KAAK6M,UAAU,CAAC7M,KAAK,EAAE;IACxC,IAAI4M,SAAS,CAAC5M,KAAK,KAAK,MAAM,EAAE;MAC9B,OAAO4M,SAAS,CAAC9M,MAAM,KAAK+M,UAAU,CAAC/M,MAAM;IAC/C;IACA,IAAI,CAACwJ,eAAe,EAAE;MACpB,OAAOsD,SAAS,CAAC/M,KAAK,KAAKgN,UAAU,CAAChN,KAAK;IAC7C;IACA,IAAIyJ,eAAe,IAAI,CAACsD,SAAS,CAAC/M,KAAK,IAAIgN,UAAU,CAAChN,KAAK,EAAE;MAC3D+M,SAAS,CAAC/M,KAAK,GAAGgN,UAAU,CAAChN,KAAK;IACpC;IACA,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;AACD,IAAIuL,aAAa,GAAIxU,IAAI,IAAKA,IAAI,IAAIA,IAAI,CAAC,MAAM,CAAC,IAAIA,IAAI;AAC1D,IAAI+V,KAAK,GAAGA,CAACnC,QAAQ,EAAED,SAAS,EAAEjB,eAAe,GAAG,KAAK,KAAK;EAC5D,MAAMze,GAAG,GAAG0f,SAAS,CAACtK,KAAK,GAAGuK,QAAQ,CAACvK,KAAK;EAC5C,MAAM6M,WAAW,GAAGtC,QAAQ,CAAC5K,UAAU;EACvC,MAAM1E,WAAW,GAAGqP,SAAS,CAAC3K,UAAU;EACxC,MAAMG,GAAG,GAAGwK,SAAS,CAACvK,KAAK;EAC3B,MAAMlN,IAAI,GAAGyX,SAAS,CAACpL,MAAM;EAC7B,IAAI4N,aAAa;EACjB,IAAIja,IAAI,KAAK,IAAI,EAAE;IACjB;MACEoX,SAAS,GAAGnK,GAAG,KAAK,KAAK,GAAG,IAAI,GAAGA,GAAG,KAAK,eAAe,GAAG,KAAK,GAAGmK,SAAS;IAChF;IACA;MACE,IAAInK,GAAG,KAAK,MAAM,IAAI,CAACgK,kBAAkB,EAAE;QACzC,IAAIS,QAAQ,CAAC1K,MAAM,KAAKyK,SAAS,CAACzK,MAAM,EAAE;UACxCyK,SAAS,CAACtK,KAAK,CAAC,MAAM,CAAC,GAAGsK,SAAS,CAACzK,MAAM,IAAI,EAAE;UAChD4K,kBAAkB,CAACH,SAAS,CAACtK,KAAK,CAAChV,aAAa,CAAC;QACnD;MACF;MACAie,aAAa,CAACsB,QAAQ,EAAED,SAAS,EAAEL,SAAS,EAAEZ,eAAe,CAAC;IAChE;IACA,IAAIwD,WAAW,KAAK,IAAI,IAAI5R,WAAW,KAAK,IAAI,EAAE;MAChD2Q,cAAc,CAAChhB,GAAG,EAAEiiB,WAAW,EAAEvC,SAAS,EAAErP,WAAW,EAAEoO,eAAe,CAAC;IAC3E,CAAC,MAAM,IAAIpO,WAAW,KAAK,IAAI,EAAE;MAC/B,IAAIsP,QAAQ,CAACrL,MAAM,KAAK,IAAI,EAAE;QAC5BtU,GAAG,CAAC2R,WAAW,GAAG,EAAE;MACtB;MACA6O,SAAS,CAACxgB,GAAG,EAAE,IAAI,EAAE0f,SAAS,EAAErP,WAAW,EAAE,CAAC,EAAEA,WAAW,CAAC7W,MAAM,GAAG,CAAC,CAAC;IACzE,CAAC,MAAM;IACL;IACA,CAACilB,eAAe,IAAIroB,KAAK,CAACM,SAAS,IAAIurB,WAAW,KAAK,IAAI,EAC3D;MACAnB,YAAY,CAACmB,WAAW,EAAE,CAAC,EAAEA,WAAW,CAACzoB,MAAM,GAAG,CAAC,CAAC;IACtD;IACA,IAAI6lB,SAAS,IAAInK,GAAG,KAAK,KAAK,EAAE;MAC9BmK,SAAS,GAAG,KAAK;IACnB;EACF,CAAC,MAAM,IAAK6C,aAAa,GAAGliB,GAAG,CAAC,MAAM,CAAC,EAAG;IACxCkiB,aAAa,CAACzV,UAAU,CAACkF,WAAW,GAAG1J,IAAI;EAC7C,CAAC,MAAM,IAAI0X,QAAQ,CAACrL,MAAM,KAAKrM,IAAI,EAAE;IACnCjI,GAAG,CAACmiB,IAAI,GAAGla,IAAI;EACjB;AACF,CAAC;AACD,IAAIma,aAAa,GAAG,EAAE;AACtB,IAAIC,4BAA4B,GAAIriB,GAAG,IAAK;EAC1C,IAAI+L,IAAI;EACR,IAAIuW,gBAAgB;EACpB,IAAIC,CAAC;EACL,MAAMzQ,QAAQ,GAAG9R,GAAG,CAACwN,YAAY,IAAIxN,GAAG,CAAC8K,UAAU;EACnD,KAAK,MAAMQ,SAAS,IAAIwG,QAAQ,EAAE;IAChC,IAAIxG,SAAS,CAAC,MAAM,CAAC,KAAKS,IAAI,GAAGT,SAAS,CAAC,MAAM,CAAC,CAAC,IAAIS,IAAI,CAACU,UAAU,EAAE;MACtE6V,gBAAgB,GAAGvW,IAAI,CAACU,UAAU,CAACe,YAAY,IAAIzB,IAAI,CAACU,UAAU,CAAC3B,UAAU;MAC7E,MAAMa,QAAQ,GAAGL,SAAS,CAAC,MAAM,CAAC;MAClC,KAAKiX,CAAC,GAAGD,gBAAgB,CAAC9oB,MAAM,GAAG,CAAC,EAAE+oB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACjDxW,IAAI,GAAGuW,gBAAgB,CAACC,CAAC,CAAC;QAC1B,IAAI,CAACxW,IAAI,CAAC,MAAM,CAAC,IAAI,CAACA,IAAI,CAAC,MAAM,CAAC,IAAIA,IAAI,CAAC,MAAM,CAAC,KAAKT,SAAS,CAAC,MAAM,CAAC,KAAK,CAACS,IAAI,CAAC,MAAM,CAAC,IAAIA,IAAI,CAAC,MAAM,CAAC,KAAKT,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE;UACjI,IAAIW,mBAAmB,CAACF,IAAI,EAAEJ,QAAQ,CAAC,EAAE;YACvC,IAAI6W,gBAAgB,GAAGJ,aAAa,CAAC9H,IAAI,CAAE9X,CAAC,IAAKA,CAAC,CAACigB,gBAAgB,KAAK1W,IAAI,CAAC;YAC7EoT,2BAA2B,GAAG,IAAI;YAClCpT,IAAI,CAAC,MAAM,CAAC,GAAGA,IAAI,CAAC,MAAM,CAAC,IAAIJ,QAAQ;YACvC,IAAI6W,gBAAgB,EAAE;cACpBA,gBAAgB,CAACC,gBAAgB,CAAC,MAAM,CAAC,GAAGnX,SAAS,CAAC,MAAM,CAAC;cAC7DkX,gBAAgB,CAACE,aAAa,GAAGpX,SAAS;YAC5C,CAAC,MAAM;cACLS,IAAI,CAAC,MAAM,CAAC,GAAGT,SAAS,CAAC,MAAM,CAAC;cAChC8W,aAAa,CAAC5b,IAAI,CAAC;gBACjBkc,aAAa,EAAEpX,SAAS;gBACxBmX,gBAAgB,EAAE1W;cACpB,CAAC,CAAC;YACJ;YACA,IAAIA,IAAI,CAAC,MAAM,CAAC,EAAE;cAChBqW,aAAa,CAAClpB,GAAG,CAAEypB,YAAY,IAAK;gBAClC,IAAI1W,mBAAmB,CAAC0W,YAAY,CAACF,gBAAgB,EAAE1W,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;kBACpEyW,gBAAgB,GAAGJ,aAAa,CAAC9H,IAAI,CAAE9X,CAAC,IAAKA,CAAC,CAACigB,gBAAgB,KAAK1W,IAAI,CAAC;kBACzE,IAAIyW,gBAAgB,IAAI,CAACG,YAAY,CAACD,aAAa,EAAE;oBACnDC,YAAY,CAACD,aAAa,GAAGF,gBAAgB,CAACE,aAAa;kBAC7D;gBACF;cACF,CAAC,CAAC;YACJ;UACF,CAAC,MAAM,IAAI,CAACN,aAAa,CAACQ,IAAI,CAAEpgB,CAAC,IAAKA,CAAC,CAACigB,gBAAgB,KAAK1W,IAAI,CAAC,EAAE;YAClEqW,aAAa,CAAC5b,IAAI,CAAC;cACjBic,gBAAgB,EAAE1W;YACpB,CAAC,CAAC;UACJ;QACF;MACF;IACF;IACA,IAAIT,SAAS,CAACJ,QAAQ,KAAK,CAAC,CAAC,mBAAmB;MAC9CmX,4BAA4B,CAAC/W,SAAS,CAAC;IACzC;EACF;AACF,CAAC;AACD,IAAIyV,gBAAgB,GAAI8B,KAAK,IAAK;EAChC;IACEA,KAAK,CAAC/N,OAAO,IAAI+N,KAAK,CAAC/N,OAAO,CAACzV,GAAG,IAAIwjB,KAAK,CAAC/N,OAAO,CAACzV,GAAG,CAAC,IAAI,CAAC;IAC7DwjB,KAAK,CAAC9N,UAAU,IAAI8N,KAAK,CAAC9N,UAAU,CAAC7b,GAAG,CAAC6nB,gBAAgB,CAAC;EAC5D;AACF,CAAC;AACD,IAAI7P,YAAY,GAAGA,CAACxE,MAAM,EAAEoW,OAAO,EAAEC,SAAS,KAAK;EACjD,IAAI,OAAOD,OAAO,CAAC,MAAM,CAAC,KAAK,QAAQ,IAAI,CAAC,CAACA,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAACA,OAAO,CAAC,MAAM,CAAC,EAAE;IACjFhD,wBAAwB,CAACgD,OAAO,CAAC,MAAM,CAAC,EAAEA,OAAO,EAAEpW,MAAM,EAAEoW,OAAO,CAAC1iB,aAAa,CAAC;EACnF,CAAC,MAAM,IAAI,OAAO0iB,OAAO,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;IAC9C,IAAIpW,MAAM,CAAC8P,WAAW,CAAC,CAAC,CAACtR,QAAQ,KAAK,EAAE,CAAC,8BAA8B;MACrEmH,eAAe,CAACyQ,OAAO,CAAC;IAC1B;IACApW,MAAM,CAACwE,YAAY,CAAC4R,OAAO,EAAEC,SAAS,CAAC;IACvC,MAAM;MAAE9X;IAAS,CAAC,GAAG8C,uBAAuB,CAAC+U,OAAO,CAAC;IACrD,IAAI7X,QAAQ,EAAEyC,uBAAuB,CAACzC,QAAQ,CAAC;IAC/C,OAAO6X,OAAO;EAChB;EACA,IAAIpW,MAAM,CAACuE,cAAc,EAAE;IACzB,OAAOvE,MAAM,CAACuE,cAAc,CAAC6R,OAAO,EAAEC,SAAS,CAAC;EAClD,CAAC,MAAM;IACL,OAAOrW,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACwE,YAAY,CAAC4R,OAAO,EAAEC,SAAS,CAAC;EAC1E;AACF,CAAC;AACD,SAASjD,wBAAwBA,CAACiD,SAAS,EAAE9X,QAAQ,EAAE+X,SAAS,EAAEC,SAAS,EAAE;EAC3E,IAAIplB,EAAE,EAAE+J,EAAE;EACV,IAAImP,QAAQ;EACZ,IAAIgM,SAAS,IAAI,OAAO9X,QAAQ,CAAC,MAAM,CAAC,KAAK,QAAQ,IAAI,CAAC,CAACA,QAAQ,CAAC,MAAM,CAAC,IAAI8X,SAAS,CAACtW,UAAU,IAAIsW,SAAS,CAACtW,UAAU,CAAC,MAAM,CAAC,KAAKsK,QAAQ,GAAG9L,QAAQ,CAAC,MAAM,CAAC,IAAI8X,SAAS,CAACtW,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE;IACpM,MAAMyW,SAAS,GAAGjY,QAAQ,CAAC,MAAM,CAAC;IAClC,MAAMS,QAAQ,GAAGT,QAAQ,CAAC,MAAM,CAAC;IACjC,CAACpN,EAAE,GAAGmlB,SAAS,CAACtnB,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmC,EAAE,CAAClC,GAAG,CAACob,QAAQ,GAAG,IAAI,CAAC;IACrE,IAAIkM,SAAS,KAAK,CAACrb,EAAE,GAAGqb,SAAS,CAACvnB,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGkM,EAAE,CAACub,QAAQ,CAACpM,QAAQ,GAAG,IAAI,CAAC,CAAC,EAAE;MAC7F,IAAIhD,KAAK,GAAG,CAACkP,SAAS,CAACzV,YAAY,IAAIyV,SAAS,CAACnY,UAAU,EAAE,CAAC,CAAC;MAC/D,IAAIsG,KAAK,GAAG,KAAK;MACjB,OAAO2C,KAAK,EAAE;QACZ,IAAIA,KAAK,CAAC,MAAM,CAAC,KAAKmP,SAAS,IAAInP,KAAK,CAAC,MAAM,CAAC,KAAKrI,QAAQ,IAAI,CAAC,CAACqI,KAAK,CAAC,MAAM,CAAC,EAAE;UAChF3C,KAAK,GAAG,IAAI;UACZ;QACF;QACA2C,KAAK,GAAGA,KAAK,CAAC/H,WAAW;MAC3B;MACA,IAAI,CAACoF,KAAK,EAAE6R,SAAS,CAACvnB,SAAS,CAACyU,MAAM,CAAC4G,QAAQ,GAAG,IAAI,CAAC;IACzD;EACF;AACF;AACA,IAAIqM,UAAU,GAAGA,CAACvhB,OAAO,EAAEwhB,eAAe,EAAEC,aAAa,GAAG,KAAK,KAAK;EACpE,IAAIzlB,EAAE,EAAE+J,EAAE,EAAEC,EAAE,EAAE0b,EAAE,EAAEC,EAAE;EACtB,MAAMxN,OAAO,GAAGnU,OAAO,CAACM,aAAa;EACrC,MAAMF,OAAO,GAAGJ,OAAO,CAACO,SAAS;EACjC,MAAMud,QAAQ,GAAG9d,OAAO,CAACwV,OAAO,IAAI9C,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC;EACxD,MAAMkP,aAAa,GAAGnO,MAAM,CAAC+N,eAAe,CAAC;EAC7C,MAAMK,SAAS,GAAGD,aAAa,GAAGJ,eAAe,GAAGzP,CAAC,CAAC,IAAI,EAAE,IAAI,EAAEyP,eAAe,CAAC;EAClFpE,WAAW,GAAGjJ,OAAO,CAACrb,OAAO;EAC7B,IAAIsH,OAAO,CAAC0hB,gBAAgB,EAAE;IAC5BD,SAAS,CAAC5O,OAAO,GAAG4O,SAAS,CAAC5O,OAAO,IAAI,CAAC,CAAC;IAC3C7S,OAAO,CAAC0hB,gBAAgB,CAACzqB,GAAG,CAC1B,CAAC,CAAC0qB,QAAQ,EAAEC,SAAS,CAAC,KAAKH,SAAS,CAAC5O,OAAO,CAAC+O,SAAS,CAAC,GAAG7N,OAAO,CAAC4N,QAAQ,CAC5E,CAAC;EACH;EACA,IAAIN,aAAa,IAAII,SAAS,CAAC5O,OAAO,EAAE;IACtC,KAAK,MAAM1d,GAAG,IAAIH,MAAM,CAAC4E,IAAI,CAAC6nB,SAAS,CAAC5O,OAAO,CAAC,EAAE;MAChD,IAAIkB,OAAO,CAAC8N,YAAY,CAAC1sB,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC8C,QAAQ,CAAC9C,GAAG,CAAC,EAAE;QAChFssB,SAAS,CAAC5O,OAAO,CAAC1d,GAAG,CAAC,GAAG4e,OAAO,CAAC5e,GAAG,CAAC;MACvC;IACF;EACF;EACAssB,SAAS,CAACvO,KAAK,GAAG,IAAI;EACtBuO,SAAS,CAACxhB,OAAO,IAAI,CAAC,CAAC;EACvBL,OAAO,CAACwV,OAAO,GAAGqM,SAAS;EAC3BA,SAAS,CAACtO,KAAK,GAAGuK,QAAQ,CAACvK,KAAK,GAAGY,OAAO,CAACxL,UAAU,IAAIwL,OAAO;EAChE;IACE+I,OAAO,GAAG/I,OAAO,CAAC,MAAM,CAAC;EAC3B;EACAkJ,kBAAkB,GAAG,CAAC,EAAEjd,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,6BAA6B,IAAI,EAAED,OAAO,CAACC,OAAO,GAAG,GAAG,CAAC,2BAA2B;EAChI;IACE8c,UAAU,GAAGhJ,OAAO,CAAC,MAAM,CAAC;IAC5BmJ,2BAA2B,GAAG,KAAK;EACrC;EACA2C,KAAK,CAACnC,QAAQ,EAAE+D,SAAS,EAAEJ,aAAa,CAAC;EACzC;IACE7e,GAAG,CAACvC,OAAO,IAAI,CAAC,CAAC;IACjB,IAAIkd,iBAAiB,EAAE;MACrBiD,4BAA4B,CAACqB,SAAS,CAACtO,KAAK,CAAC;MAC7C,KAAK,MAAM2O,YAAY,IAAI3B,aAAa,EAAE;QACxC,MAAMlW,cAAc,GAAG6X,YAAY,CAACtB,gBAAgB;QACpD,IAAI,CAACvW,cAAc,CAAC,MAAM,CAAC,IAAIjU,GAAG,CAACuD,QAAQ,EAAE;UAC3C,MAAMqc,eAAe,GAAG5f,GAAG,CAACuD,QAAQ,CAACgR,cAAc,CAAC,EAAE,CAAC;UACvDqL,eAAe,CAAC,MAAM,CAAC,GAAG3L,cAAc;UACxCgF,YAAY,CAAChF,cAAc,CAACO,UAAU,EAAEP,cAAc,CAAC,MAAM,CAAC,GAAG2L,eAAe,EAAE3L,cAAc,CAAC;QACnG;MACF;MACA,KAAK,MAAM6X,YAAY,IAAI3B,aAAa,EAAE;QACxC,MAAMlW,cAAc,GAAG6X,YAAY,CAACtB,gBAAgB;QACpD,MAAMuB,WAAW,GAAGD,YAAY,CAACrB,aAAa;QAC9C,IAAIsB,WAAW,EAAE;UACf,MAAMC,aAAa,GAAGD,WAAW,CAACvX,UAAU;UAC5C,IAAIyX,gBAAgB,GAAGF,WAAW,CAAChY,WAAW;UAC9C,IAAKkY,gBAAgB,IAAIA,gBAAgB,CAAChZ,QAAQ,KAAK,CAAC,CAAC,mBAAoB;YAC3E,IAAI2M,eAAe,GAAG,CAACha,EAAE,GAAGqO,cAAc,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGrO,EAAE,CAACsmB,eAAe;YACzF,OAAOtM,eAAe,EAAE;cACtB,IAAIuM,OAAO,GAAG,CAACxc,EAAE,GAAGiQ,eAAe,CAAC,MAAM,CAAC,KAAK,IAAI,GAAGjQ,EAAE,GAAG,IAAI;cAChE,IAAIwc,OAAO,IAAIA,OAAO,CAAC,MAAM,CAAC,KAAKlY,cAAc,CAAC,MAAM,CAAC,IAAI+X,aAAa,MAAMG,OAAO,CAAC/S,YAAY,IAAI+S,OAAO,CAAC3X,UAAU,CAAC,EAAE;gBAC3H2X,OAAO,GAAGA,OAAO,CAACpY,WAAW;gBAC7B,OAAOoY,OAAO,KAAKlY,cAAc,KAAKkY,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;kBACjFA,OAAO,GAAGA,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACpY,WAAW;gBAC1D;gBACA,IAAI,CAACoY,OAAO,IAAI,CAACA,OAAO,CAAC,MAAM,CAAC,EAAE;kBAChCF,gBAAgB,GAAGE,OAAO;kBAC1B;gBACF;cACF;cACAvM,eAAe,GAAGA,eAAe,CAACsM,eAAe;YACnD;UACF;UACA,MAAMzX,MAAM,GAAGR,cAAc,CAACmF,YAAY,IAAInF,cAAc,CAACO,UAAU;UACvE,MAAMT,WAAW,GAAGE,cAAc,CAAC8F,aAAa,IAAI9F,cAAc,CAACF,WAAW;UAC9E,IAAI,CAACkY,gBAAgB,IAAID,aAAa,KAAKvX,MAAM,IAAIV,WAAW,KAAKkY,gBAAgB,EAAE;YACrF,IAAIhY,cAAc,KAAKgY,gBAAgB,EAAE;cACvChT,YAAY,CAAC+S,aAAa,EAAE/X,cAAc,EAAEgY,gBAAgB,CAAC;cAC7D,IAAIhY,cAAc,CAAChB,QAAQ,KAAK,CAAC,CAAC,qBAAqBgB,cAAc,CAACvR,OAAO,KAAK,SAAS,EAAE;gBAC3FuR,cAAc,CAACb,MAAM,GAAG,CAACxD,EAAE,GAAGqE,cAAc,CAAC,MAAM,CAAC,KAAK,IAAI,GAAGrE,EAAE,GAAG,KAAK;cAC5E;YACF;UACF;UACAqE,cAAc,IAAI,OAAO8X,WAAW,CAAC,MAAM,CAAC,KAAK,UAAU,IAAIA,WAAW,CAAC,MAAM,CAAC,CAACA,WAAW,CAAC;QACjG,CAAC,MAAM;UACL,IAAI9X,cAAc,CAAChB,QAAQ,KAAK,CAAC,CAAC,mBAAmB;YACnD,IAAIoY,aAAa,EAAE;cACjBpX,cAAc,CAAC,MAAM,CAAC,GAAG,CAACqX,EAAE,GAAGrX,cAAc,CAACb,MAAM,KAAK,IAAI,GAAGkY,EAAE,GAAG,KAAK;YAC5E;YACArX,cAAc,CAACb,MAAM,GAAG,IAAI;UAC9B;QACF;MACF;IACF;IACA,IAAI8T,2BAA2B,EAAE;MAC/BtU,4BAA4B,CAAC6Y,SAAS,CAACtO,KAAK,CAAC;IAC/C;IACA3Q,GAAG,CAACvC,OAAO,IAAI,CAAC,CAAC,CAAC;IAClBkgB,aAAa,CAAC5oB,MAAM,GAAG,CAAC;EAC1B;EACA,IAAIyI,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,8BAA8B;IACpD,MAAM4P,QAAQ,GAAG4R,SAAS,CAACtO,KAAK,CAAC5H,YAAY,IAAIkW,SAAS,CAACtO,KAAK,CAACtK,UAAU;IAC3E,KAAK,MAAMQ,SAAS,IAAIwG,QAAQ,EAAE;MAChC,IAAIxG,SAAS,CAAC,MAAM,CAAC,KAAK2T,WAAW,IAAI,CAAC3T,SAAS,CAAC,MAAM,CAAC,EAAE;QAC3D,IAAIgY,aAAa,IAAIhY,SAAS,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;UAC9CA,SAAS,CAAC,MAAM,CAAC,GAAG,CAACkY,EAAE,GAAGlY,SAAS,CAACD,MAAM,KAAK,IAAI,GAAGmY,EAAE,GAAG,KAAK;QAClE;QACAlY,SAAS,CAACD,MAAM,GAAG,IAAI;MACzB;IACF;EACF;EACA2T,UAAU,GAAG,KAAK,CAAC;AACrB,CAAC;;AAED;AACA,IAAIqF,gBAAgB,GAAGA,CAACxiB,OAAO,EAAEyiB,iBAAiB,KAAK;EACrD,IAAIA,iBAAiB,IAAI,CAACziB,OAAO,CAAC0iB,iBAAiB,IAAID,iBAAiB,CAAC,KAAK,CAAC,EAAE;IAC/E,MAAM5R,KAAK,GAAG4R,iBAAiB,CAAC,KAAK,CAAC,CAAC9d,IAAI,CACzC,IAAIjE,OAAO,CACRC,CAAC,IAAKX,OAAO,CAAC0iB,iBAAiB,GAAG,MAAM;MACvCD,iBAAiB,CAAC,KAAK,CAAC,CAACE,MAAM,CAAC9R,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;MAC7ClQ,CAAC,CAAC,CAAC;IACL,CACF,CACF,CAAC;EACH;AACF,CAAC;AACD,IAAIiiB,cAAc,GAAGA,CAAC5iB,OAAO,EAAEyhB,aAAa,KAAK;EAC/C;IACEzhB,OAAO,CAACK,OAAO,IAAI,EAAE,CAAC;EACxB;EACA,IAAIL,OAAO,CAACK,OAAO,GAAG,CAAC,CAAC,4BAA4B;IAClDL,OAAO,CAACK,OAAO,IAAI,GAAG,CAAC;IACvB;EACF;EACAmiB,gBAAgB,CAACxiB,OAAO,EAAEA,OAAO,CAAC6iB,mBAAmB,CAAC;EACtD,MAAMC,QAAQ,GAAGA,CAAA,KAAMC,aAAa,CAAC/iB,OAAO,EAAEyhB,aAAa,CAAC;EAC5D,OAAOtc,SAAS,CAAC2d,QAAQ,CAAC;AAC5B,CAAC;AACD,IAAIC,aAAa,GAAGA,CAAC/iB,OAAO,EAAEyhB,aAAa,KAAK;EAC9C,MAAMtjB,GAAG,GAAG6B,OAAO,CAACM,aAAa;EACjC,MAAM0iB,WAAW,GAAGrR,UAAU,CAAC,gBAAgB,EAAE3R,OAAO,CAACO,SAAS,CAACe,SAAS,CAAC;EAC7E,MAAM2hB,QAAQ,GAAGjjB,OAAO,CAACC,cAAc;EACvC,IAAI,CAACgjB,QAAQ,EAAE;IACb,MAAM,IAAI1b,KAAK,CACb,2BAA2BpJ,GAAG,CAACrF,OAAO,CAACC,WAAW,CAAC,CAAC,yNACtD,CAAC;EACH;EACA,IAAImqB,YAAY;EAChB,IAAIzB,aAAa,EAAE;IACjB;MACEzhB,OAAO,CAACK,OAAO,IAAI,GAAG,CAAC;MACvB,IAAIL,OAAO,CAACmjB,iBAAiB,EAAE;QAC7BnjB,OAAO,CAACmjB,iBAAiB,CAAC9rB,GAAG,CAAC,CAAC,CAAC+rB,UAAU,EAAEC,KAAK,CAAC,KAAKC,QAAQ,CAACL,QAAQ,EAAEG,UAAU,EAAEC,KAAK,EAAEllB,GAAG,CAAC,CAAC;QAClG6B,OAAO,CAACmjB,iBAAiB,GAAG,KAAK,CAAC;MACpC;IACF;IACAD,YAAY,GAAGI,QAAQ,CAACL,QAAQ,EAAE,mBAAmB,EAAE,KAAK,CAAC,EAAE9kB,GAAG,CAAC;EACrE,CAAC,MAAM;IACL+kB,YAAY,GAAGI,QAAQ,CAACL,QAAQ,EAAE,qBAAqB,EAAE,KAAK,CAAC,EAAE9kB,GAAG,CAAC;EACvE;EACA+kB,YAAY,GAAGK,OAAO,CAACL,YAAY,EAAE,MAAMI,QAAQ,CAACL,QAAQ,EAAE,qBAAqB,EAAE,KAAK,CAAC,EAAE9kB,GAAG,CAAC,CAAC;EAClG6kB,WAAW,CAAC,CAAC;EACb,OAAOO,OAAO,CAACL,YAAY,EAAE,MAAMM,eAAe,CAACxjB,OAAO,EAAEijB,QAAQ,EAAExB,aAAa,CAAC,CAAC;AACvF,CAAC;AACD,IAAI8B,OAAO,GAAGA,CAACL,YAAY,EAAE5a,EAAE,KAAKmb,UAAU,CAACP,YAAY,CAAC,GAAGA,YAAY,CAACvhB,IAAI,CAAC2G,EAAE,CAAC,CAACob,KAAK,CAAEC,IAAI,IAAK;EACnGrrB,OAAO,CAACI,KAAK,CAACirB,IAAI,CAAC;EACnBrb,EAAE,CAAC,CAAC;AACN,CAAC,CAAC,GAAGA,EAAE,CAAC,CAAC;AACT,IAAImb,UAAU,GAAIP,YAAY,IAAKA,YAAY,YAAYxiB,OAAO,IAAIwiB,YAAY,IAAIA,YAAY,CAACvhB,IAAI,IAAI,OAAOuhB,YAAY,CAACvhB,IAAI,KAAK,UAAU;AAClJ,IAAI6hB,eAAe;EAAA,IAAAI,IAAA,GAAAC,iBAAA,CAAG,WAAO7jB,OAAO,EAAEijB,QAAQ,EAAExB,aAAa,EAAK;IAChE,IAAIzlB,EAAE;IACN,MAAMmC,GAAG,GAAG6B,OAAO,CAACM,aAAa;IACjC,MAAMwjB,SAAS,GAAGnS,UAAU,CAAC,QAAQ,EAAE3R,OAAO,CAACO,SAAS,CAACe,SAAS,CAAC;IACnE,MAAMyiB,EAAE,GAAG5lB,GAAG,CAAC,MAAM,CAAC;IACtB,IAAIsjB,aAAa,EAAE;MACjBhH,YAAY,CAACza,OAAO,CAAC;IACvB;IACA,MAAMgkB,SAAS,GAAGrS,UAAU,CAAC,QAAQ,EAAE3R,OAAO,CAACO,SAAS,CAACe,SAAS,CAAC;IACnE;MACE2iB,UAAU,CAACjkB,OAAO,EAAEijB,QAAQ,EAAE9kB,GAAG,EAAEsjB,aAAa,CAAC;IACnD;IACA,IAAIsC,EAAE,EAAE;MACNA,EAAE,CAAC1sB,GAAG,CAAEqN,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;MACpBvG,GAAG,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;IACtB;IACA6lB,SAAS,CAAC,CAAC;IACXF,SAAS,CAAC,CAAC;IACX;MACE,MAAMI,gBAAgB,GAAG,CAACloB,EAAE,GAAGmC,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,GAAGnC,EAAE,GAAG,EAAE;MAC5D,MAAMmoB,UAAU,GAAGA,CAAA,KAAMC,mBAAmB,CAACpkB,OAAO,CAAC;MACrD,IAAIkkB,gBAAgB,CAACvsB,MAAM,KAAK,CAAC,EAAE;QACjCwsB,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACLzjB,OAAO,CAAC5B,GAAG,CAAColB,gBAAgB,CAAC,CAACviB,IAAI,CAACwiB,UAAU,CAAC;QAC9CnkB,OAAO,CAACK,OAAO,IAAI,CAAC,CAAC;QACrB6jB,gBAAgB,CAACvsB,MAAM,GAAG,CAAC;MAC7B;IACF;EACF,CAAC;EAAA,gBA7BG6rB,eAAeA,CAAAa,EAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAX,IAAA,CAAAY,KAAA,OAAAC,SAAA;EAAA;AAAA,GA6BlB;AACD,IAAIR,UAAU,GAAGA,CAACjkB,OAAO,EAAEijB,QAAQ,EAAE9kB,GAAG,EAAEsjB,aAAa,KAAK;EAC1D,IAAI;IACFwB,QAAQ,GAAGA,QAAQ,CAACyB,MAAM,IAAIzB,QAAQ,CAACyB,MAAM,CAAC,CAAC;IAC/C;MACE1kB,OAAO,CAACK,OAAO,IAAI,CAAC,EAAE,CAAC;IACzB;IACA;MACEL,OAAO,CAACK,OAAO,IAAI,CAAC,CAAC;IACvB;IACA;MACE;QACE;UACEkhB,UAAU,CAACvhB,OAAO,EAAEijB,QAAQ,EAAExB,aAAa,CAAC;QAC9C;MACF;IACF;EACF,CAAC,CAAC,OAAO9qB,CAAC,EAAE;IACVsK,YAAY,CAACtK,CAAC,EAAEqJ,OAAO,CAACM,aAAa,CAAC;EACxC;EACA,OAAO,IAAI;AACb,CAAC;AACD,IAAI8jB,mBAAmB,GAAIpkB,OAAO,IAAK;EACrC,MAAMlH,OAAO,GAAGkH,OAAO,CAACO,SAAS,CAACe,SAAS;EAC3C,MAAMnD,GAAG,GAAG6B,OAAO,CAACM,aAAa;EACjC,MAAMqkB,aAAa,GAAGhT,UAAU,CAAC,YAAY,EAAE7Y,OAAO,CAAC;EACvD,MAAMmqB,QAAQ,GAAGjjB,OAAO,CAACC,cAAc;EACvC,MAAMwiB,iBAAiB,GAAGziB,OAAO,CAAC6iB,mBAAmB;EACrDS,QAAQ,CAACL,QAAQ,EAAE,oBAAoB,EAAE,KAAK,CAAC,EAAE9kB,GAAG,CAAC;EACrD,IAAI,EAAE6B,OAAO,CAACK,OAAO,GAAG,EAAE,CAAC,yBAAyB,EAAE;IACpDL,OAAO,CAACK,OAAO,IAAI,EAAE,CAAC;IACtB;MACEukB,eAAe,CAACzmB,GAAG,CAAC;IACtB;IACAmlB,QAAQ,CAACL,QAAQ,EAAE,kBAAkB,EAAE,KAAK,CAAC,EAAE9kB,GAAG,CAAC;IACnDwmB,aAAa,CAAC,CAAC;IACf;MACE3kB,OAAO,CAACc,gBAAgB,CAAC3C,GAAG,CAAC;MAC7B,IAAI,CAACskB,iBAAiB,EAAE;QACtBoC,UAAU,CAAC,CAAC;MACd;IACF;EACF,CAAC,MAAM;IACLvB,QAAQ,CAACL,QAAQ,EAAE,oBAAoB,EAAE,KAAK,CAAC,EAAE9kB,GAAG,CAAC;IACrDwmB,aAAa,CAAC,CAAC;EACjB;EACA;IACE3kB,OAAO,CAACY,mBAAmB,CAACzC,GAAG,CAAC;EAClC;EACA;IACE,IAAI6B,OAAO,CAAC0iB,iBAAiB,EAAE;MAC7B1iB,OAAO,CAAC0iB,iBAAiB,CAAC,CAAC;MAC3B1iB,OAAO,CAAC0iB,iBAAiB,GAAG,KAAK,CAAC;IACpC;IACA,IAAI1iB,OAAO,CAACK,OAAO,GAAG,GAAG,CAAC,qBAAqB;MAC7CuE,QAAQ,CAAC,MAAMge,cAAc,CAAC5iB,OAAO,EAAE,KAAK,CAAC,CAAC;IAChD;IACAA,OAAO,CAACK,OAAO,IAAI,CAAC,GAAG;EACzB;AACF,CAAC;AACD,IAAIykB,WAAW,GAAItnB,GAAG,IAAK;EACzB;IACE,MAAMwC,OAAO,GAAGJ,UAAU,CAACpC,GAAG,CAAC;IAC/B,MAAMoM,WAAW,GAAG5J,OAAO,CAACM,aAAa,CAACsJ,WAAW;IACrD,IAAIA,WAAW,IAAI,CAAC5J,OAAO,CAACK,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,CAAC,wBAAwB,MAAM,CAAC,CAAC,mBAAmB;MACjHuiB,cAAc,CAAC5iB,OAAO,EAAE,KAAK,CAAC;IAChC;IACA,OAAO4J,WAAW;EACpB;AACF,CAAC;AACD,IAAIib,UAAU,GAAIE,GAAG,IAAK;EACxBngB,QAAQ,CAAC,MAAMuU,SAAS,CAAC/iB,GAAG,EAAE,SAAS,EAAE;IAAE8iB,MAAM,EAAE;MAAE8L,SAAS,EAAE1wB;IAAU;EAAE,CAAC,CAAC,CAAC;AACjF,CAAC;AACD,IAAIgvB,QAAQ,GAAGA,CAACL,QAAQ,EAAEvR,MAAM,EAAEuT,GAAG,EAAE9mB,GAAG,KAAK;EAC7C,IAAI8kB,QAAQ,IAAIA,QAAQ,CAACvR,MAAM,CAAC,EAAE;IAChC,IAAI;MACF,OAAOuR,QAAQ,CAACvR,MAAM,CAAC,CAACuT,GAAG,CAAC;IAC9B,CAAC,CAAC,OAAOtuB,CAAC,EAAE;MACVsK,YAAY,CAACtK,CAAC,EAAEwH,GAAG,CAAC;IACtB;EACF;EACA,OAAO,KAAK,CAAC;AACf,CAAC;AACD,IAAIymB,eAAe,GAAIzmB,GAAG,IAAK;EAC7B,IAAInC,EAAE;EACN,OAAOmC,GAAG,CAACtE,SAAS,CAACC,GAAG,CAAC,CAACkC,EAAE,GAAGzH,KAAK,CAACE,oBAAoB,KAAK,IAAI,GAAGuH,EAAE,GAAG,UAAU,CAAC;AACvF,CAAC;;AAED;AACA,IAAIkpB,QAAQ,GAAGA,CAAC1nB,GAAG,EAAEukB,QAAQ,KAAKniB,UAAU,CAACpC,GAAG,CAAC,CAACgD,gBAAgB,CAAClL,GAAG,CAACysB,QAAQ,CAAC;AAChF,IAAIoD,QAAQ,GAAGA,CAAC3nB,GAAG,EAAEukB,QAAQ,EAAExZ,MAAM,EAAEnI,OAAO,KAAK;EACjD,MAAMJ,OAAO,GAAGJ,UAAU,CAACpC,GAAG,CAAC;EAC/B,IAAI,CAACwC,OAAO,EAAE;IACZ,MAAM,IAAIuH,KAAK,CACb,mCAAmCnH,OAAO,CAACkB,SAAS,2YACtD,CAAC;EACH;EACA,MAAMnD,GAAG,GAAG6B,OAAO,CAACM,aAAa;EACjC,MAAM8kB,MAAM,GAAGplB,OAAO,CAACQ,gBAAgB,CAAClL,GAAG,CAACysB,QAAQ,CAAC;EACrD,MAAM5a,KAAK,GAAGnH,OAAO,CAACK,OAAO;EAC7B,MAAM4iB,QAAQ,GAAGjjB,OAAO,CAACC,cAAc;EACvCsI,MAAM,GAAG0M,kBAAkB,CAAC1M,MAAM,EAAEnI,OAAO,CAACsU,SAAS,CAACqN,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACnE,MAAMsD,UAAU,GAAGC,MAAM,CAACvvB,KAAK,CAACqvB,MAAM,CAAC,IAAIE,MAAM,CAACvvB,KAAK,CAACwS,MAAM,CAAC;EAC/D,MAAMgd,cAAc,GAAGhd,MAAM,KAAK6c,MAAM,IAAI,CAACC,UAAU;EACvD,IAAI,CAAC,EAAEle,KAAK,GAAG,CAAC,CAAC,6BAA6B,IAAIie,MAAM,KAAK,KAAK,CAAC,KAAKG,cAAc,EAAE;IACtFvlB,OAAO,CAACQ,gBAAgB,CAACvK,GAAG,CAAC8rB,QAAQ,EAAExZ,MAAM,CAAC;IAC9C,IAAI0a,QAAQ,EAAE;MACZ,IAAI7iB,OAAO,CAAColB,UAAU,IAAIre,KAAK,GAAG,GAAG,CAAC,oBAAoB;QACxD,MAAMse,YAAY,GAAGrlB,OAAO,CAAColB,UAAU,CAACzD,QAAQ,CAAC;QACjD,IAAI0D,YAAY,EAAE;UAChBA,YAAY,CAACpuB,GAAG,CAAEquB,eAAe,IAAK;YACpC,IAAI;cACFzC,QAAQ,CAACyC,eAAe,CAAC,CAACnd,MAAM,EAAE6c,MAAM,EAAErD,QAAQ,CAAC;YACrD,CAAC,CAAC,OAAOprB,CAAC,EAAE;cACVsK,YAAY,CAACtK,CAAC,EAAEwH,GAAG,CAAC;YACtB;UACF,CAAC,CAAC;QACJ;MACF;MACA,IAAI,CAACgJ,KAAK,IAAI,CAAC,CAAC,oBAAoB,EAAE,CAAC,wBAAwB,MAAM,CAAC,CAAC,mBAAmB;QACxF,IAAI8b,QAAQ,CAAC0C,qBAAqB,EAAE;UAClC,IAAI1C,QAAQ,CAAC0C,qBAAqB,CAACpd,MAAM,EAAE6c,MAAM,EAAErD,QAAQ,CAAC,KAAK,KAAK,EAAE;YACtE;UACF;QACF;QACAa,cAAc,CAAC5iB,OAAO,EAAE,KAAK,CAAC;MAChC;IACF;EACF;AACF,CAAC;;AAED;AACA,IAAI4lB,cAAc,GAAGA,CAACC,IAAI,EAAEzlB,OAAO,EAAE+G,KAAK,KAAK;EAC7C,IAAInL,EAAE,EAAE+J,EAAE;EACV,MAAM0L,SAAS,GAAGoU,IAAI,CAACpU,SAAS;EAChC,IAAIrR,OAAO,CAACsU,SAAS,IAAKtU,OAAO,CAAColB,UAAU,IAAIK,IAAI,CAACC,QAAS,EAAE;IAC9D,IAAID,IAAI,CAACC,QAAQ,IAAI,CAAC1lB,OAAO,CAAColB,UAAU,EAAE;MACxCplB,OAAO,CAAColB,UAAU,GAAGK,IAAI,CAACC,QAAQ;IACpC;IACA,MAAMrR,OAAO,GAAGrf,MAAM,CAACC,OAAO,CAAC,CAAC2G,EAAE,GAAGoE,OAAO,CAACsU,SAAS,KAAK,IAAI,GAAG1Y,EAAE,GAAG,CAAC,CAAC,CAAC;IAC1EyY,OAAO,CAACpd,GAAG,CAAC,CAAC,CAAC2J,UAAU,EAAE,CAAC2T,WAAW,CAAC,CAAC,KAAK;MAC3C,IAAKA,WAAW,GAAG,EAAE,CAAC,cAAexN,KAAK,GAAG,CAAC,CAAC,oBAAqBwN,WAAW,GAAG,EAAE,CAAC,aAAc;QACjG,MAAM;UAAErf,GAAG,EAAEywB,UAAU;UAAE9vB,GAAG,EAAE+vB;QAAW,CAAC,GAAG5wB,MAAM,CAACmc,wBAAwB,CAACE,SAAS,EAAEzQ,UAAU,CAAC,IAAI,CAAC,CAAC;QACzG,IAAI+kB,UAAU,EAAE3lB,OAAO,CAACsU,SAAS,CAAC1T,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;QACzD,IAAIglB,UAAU,EAAE5lB,OAAO,CAACsU,SAAS,CAAC1T,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;QACzD,IAAImG,KAAK,GAAG,CAAC,CAAC,8BAA8B,CAAC4e,UAAU,EAAE;UACvD3wB,MAAM,CAACuJ,cAAc,CAAC8S,SAAS,EAAEzQ,UAAU,EAAE;YAC3C1L,GAAGA,CAAA,EAAG;cACJ;gBACE,IAAI,CAAC8K,OAAO,CAACsU,SAAS,CAAC1T,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,EAAE;kBAChE,OAAOkkB,QAAQ,CAAC,IAAI,EAAElkB,UAAU,CAAC;gBACnC;gBACA,MAAMxD,GAAG,GAAGoC,UAAU,CAAC,IAAI,CAAC;gBAC5B,MAAMqjB,QAAQ,GAAGzlB,GAAG,GAAGA,GAAG,CAACyC,cAAc,GAAGwR,SAAS;gBACrD,IAAI,CAACwR,QAAQ,EAAE;gBACf,OAAOA,QAAQ,CAACjiB,UAAU,CAAC;cAC7B;YACF,CAAC;YACDilB,YAAY,EAAE,IAAI;YAClBjnB,UAAU,EAAE;UACd,CAAC,CAAC;QACJ;QACA5J,MAAM,CAACuJ,cAAc,CAAC8S,SAAS,EAAEzQ,UAAU,EAAE;UAC3C/K,GAAGA,CAACklB,QAAQ,EAAE;YACZ,MAAM3d,GAAG,GAAGoC,UAAU,CAAC,IAAI,CAAC;YAC5B,IAAIomB,UAAU,EAAE;cACd,MAAME,YAAY,GAAGvR,WAAW,GAAG,EAAE,CAAC,cAAc,IAAI,CAAC3T,UAAU,CAAC,GAAGxD,GAAG,CAAC8C,aAAa,CAACU,UAAU,CAAC;cACpG,IAAI,OAAOklB,YAAY,KAAK,WAAW,IAAI1oB,GAAG,CAACgD,gBAAgB,CAAClL,GAAG,CAAC0L,UAAU,CAAC,EAAE;gBAC/Ema,QAAQ,GAAG3d,GAAG,CAACgD,gBAAgB,CAAClL,GAAG,CAAC0L,UAAU,CAAC;cACjD,CAAC,MAAM,IAAI,CAACxD,GAAG,CAACgD,gBAAgB,CAAClL,GAAG,CAAC0L,UAAU,CAAC,IAAIklB,YAAY,EAAE;gBAChE1oB,GAAG,CAACgD,gBAAgB,CAACvK,GAAG,CAAC+K,UAAU,EAAEklB,YAAY,CAAC;cACpD;cACAF,UAAU,CAACxB,KAAK,CAAC,IAAI,EAAE,CAACvP,kBAAkB,CAACkG,QAAQ,EAAExG,WAAW,CAAC,CAAC,CAAC;cACnEwG,QAAQ,GAAGxG,WAAW,GAAG,EAAE,CAAC,cAAc,IAAI,CAAC3T,UAAU,CAAC,GAAGxD,GAAG,CAAC8C,aAAa,CAACU,UAAU,CAAC;cAC1FmkB,QAAQ,CAAC,IAAI,EAAEnkB,UAAU,EAAEma,QAAQ,EAAE/a,OAAO,CAAC;cAC7C;YACF;YACA;cACE,IAAI,CAAC+G,KAAK,GAAG,CAAC,CAAC,gCAAgC,CAAC,IAAI,CAAC/G,OAAO,CAACsU,SAAS,CAAC1T,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,EAAE;gBAChHmkB,QAAQ,CAAC,IAAI,EAAEnkB,UAAU,EAAEma,QAAQ,EAAE/a,OAAO,CAAC;gBAC7C,IAAI+G,KAAK,GAAG,CAAC,CAAC,8BAA8B,CAAC3J,GAAG,CAACyC,cAAc,EAAE;kBAC/DzC,GAAG,CAACqD,gBAAgB,CAACc,IAAI,CAAC,MAAM;oBAC9B,IAAIvB,OAAO,CAACsU,SAAS,CAAC1T,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgBxD,GAAG,CAACyC,cAAc,CAACe,UAAU,CAAC,KAAKxD,GAAG,CAACgD,gBAAgB,CAAClL,GAAG,CAAC0L,UAAU,CAAC,EAAE;sBACnIxD,GAAG,CAACyC,cAAc,CAACe,UAAU,CAAC,GAAGma,QAAQ;oBAC3C;kBACF,CAAC,CAAC;gBACJ;gBACA;cACF;cACA,MAAMgL,YAAY,GAAGA,CAAA,KAAM;gBACzB,MAAMD,YAAY,GAAG1oB,GAAG,CAACyC,cAAc,CAACe,UAAU,CAAC;gBACnD,IAAI,CAACxD,GAAG,CAACgD,gBAAgB,CAAClL,GAAG,CAAC0L,UAAU,CAAC,IAAIklB,YAAY,EAAE;kBACzD1oB,GAAG,CAACgD,gBAAgB,CAACvK,GAAG,CAAC+K,UAAU,EAAEklB,YAAY,CAAC;gBACpD;gBACA1oB,GAAG,CAACyC,cAAc,CAACe,UAAU,CAAC,GAAGiU,kBAAkB,CAACkG,QAAQ,EAAExG,WAAW,CAAC;gBAC1EwQ,QAAQ,CAAC,IAAI,EAAEnkB,UAAU,EAAExD,GAAG,CAACyC,cAAc,CAACe,UAAU,CAAC,EAAEZ,OAAO,CAAC;cACrE,CAAC;cACD,IAAI5C,GAAG,CAACyC,cAAc,EAAE;gBACtBkmB,YAAY,CAAC,CAAC;cAChB,CAAC,MAAM;gBACL3oB,GAAG,CAACqD,gBAAgB,CAACc,IAAI,CAAC,MAAMwkB,YAAY,CAAC,CAAC,CAAC;cACjD;YACF;UACF;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIhf,KAAK,GAAG,CAAC,CAAC,8BAA8BwN,WAAW,GAAG,EAAE,CAAC,cAAc;QAChFvf,MAAM,CAACuJ,cAAc,CAAC8S,SAAS,EAAEzQ,UAAU,EAAE;UAC3CvL,KAAKA,CAAC,GAAG2wB,IAAI,EAAE;YACb,IAAIvR,GAAG;YACP,MAAMrX,GAAG,GAAGoC,UAAU,CAAC,IAAI,CAAC;YAC5B,OAAO,CAACiV,GAAG,GAAGrX,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACiD,mBAAmB,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoU,GAAG,CAAClT,IAAI,CAAC,MAAM;cAC9F,IAAI0kB,GAAG;cACP,OAAO,CAACA,GAAG,GAAG7oB,GAAG,CAACyC,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGomB,GAAG,CAACrlB,UAAU,CAAC,CAAC,GAAGolB,IAAI,CAAC;YAC/E,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF,IAAKjf,KAAK,GAAG,CAAC,CAAC,4BAA6B;MAC1C,MAAMmf,kBAAkB,GAAG,eAAgB,IAAIrxB,GAAG,CAAC,CAAC;MACpDwc,SAAS,CAAC8U,wBAAwB,GAAG,UAASC,QAAQ,EAAEtL,QAAQ,EAAEC,QAAQ,EAAE;QAC1EvY,GAAG,CAACE,GAAG,CAAC,MAAM;UACZ,IAAI+R,GAAG;UACP,MAAMkN,QAAQ,GAAGuE,kBAAkB,CAAChxB,GAAG,CAACkxB,QAAQ,CAAC;UACjD,IAAI,IAAI,CAAC7e,cAAc,CAACoa,QAAQ,CAAC,IAAIxtB,KAAK,CAACG,QAAQ,EAAE;YACnDymB,QAAQ,GAAG,IAAI,CAAC4G,QAAQ,CAAC;YACzB,OAAO,IAAI,CAACA,QAAQ,CAAC;UACvB,CAAC,MAAM,IAAItQ,SAAS,CAAC9J,cAAc,CAACoa,QAAQ,CAAC,IAAI,OAAO,IAAI,CAACA,QAAQ,CAAC,KAAK,QAAQ;UAAI;UACvF,IAAI,CAACA,QAAQ,CAAC,IAAI5G,QAAQ,EAAE;YAC1B;UACF,CAAC,MAAM,IAAI4G,QAAQ,IAAI,IAAI,EAAE;YAC3B,MAAM/hB,OAAO,GAAGJ,UAAU,CAAC,IAAI,CAAC;YAChC,MAAM6mB,MAAM,GAAGzmB,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACK,OAAO;YACzD,IAAIomB,MAAM,IAAI,EAAEA,MAAM,GAAG,CAAC,CAAC,6BAA6B,IAAIA,MAAM,GAAG,GAAG,CAAC,sBAAsBtL,QAAQ,KAAKD,QAAQ,EAAE;cACpH,MAAM+H,QAAQ,GAAGjjB,OAAO,CAACC,cAAc;cACvC,MAAM3I,KAAK,GAAG,CAACud,GAAG,GAAGzU,OAAO,CAAColB,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG3Q,GAAG,CAAC2R,QAAQ,CAAC;cACzElvB,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACM,OAAO,CAAE8uB,YAAY,IAAK;gBACvD,IAAIzD,QAAQ,CAACyD,YAAY,CAAC,IAAI,IAAI,EAAE;kBAClCzD,QAAQ,CAACyD,YAAY,CAAC,CAACzqB,IAAI,CAACgnB,QAAQ,EAAE9H,QAAQ,EAAED,QAAQ,EAAEsL,QAAQ,CAAC;gBACrE;cACF,CAAC,CAAC;YACJ;YACA;UACF;UACA,MAAMG,QAAQ,GAAGvxB,MAAM,CAACmc,wBAAwB,CAACE,SAAS,EAAEsQ,QAAQ,CAAC;UACrE5G,QAAQ,GAAGA,QAAQ,KAAK,IAAI,IAAI,OAAO,IAAI,CAAC4G,QAAQ,CAAC,KAAK,SAAS,GAAG,KAAK,GAAG5G,QAAQ;UACtF,IAAIA,QAAQ,KAAK,IAAI,CAAC4G,QAAQ,CAAC,KAAK,CAAC4E,QAAQ,CAACrxB,GAAG,IAAI,CAAC,CAACqxB,QAAQ,CAAC1wB,GAAG,CAAC,EAAE;YACpE,IAAI,CAAC8rB,QAAQ,CAAC,GAAG5G,QAAQ;UAC3B;QACF,CAAC,CAAC;MACJ,CAAC;MACD0K,IAAI,CAACe,kBAAkB,GAAG5W,KAAK,CAAC8G,IAAI,CAClC,eAAgB,IAAIzP,GAAG,CAAC,CACtB,GAAGjS,MAAM,CAAC4E,IAAI,CAAC,CAAC+L,EAAE,GAAG3F,OAAO,CAAColB,UAAU,KAAK,IAAI,GAAGzf,EAAE,GAAG,CAAC,CAAC,CAAC,EAC3D,GAAG0O,OAAO,CAACjd,MAAM,CAAC,CAAC,CAACqvB,CAAC,EAAE7xB,CAAC,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC,CAACqC,GAAG,CAAC,CAAC,CAAC0qB,QAAQ,EAAE/sB,CAAC,CAAC,KAAK;QACjF,IAAI6f,GAAG;QACP,MAAM2R,QAAQ,GAAGxxB,CAAC,CAAC,CAAC,CAAC,IAAI+sB,QAAQ;QACjCuE,kBAAkB,CAACrwB,GAAG,CAACuwB,QAAQ,EAAEzE,QAAQ,CAAC;QAC1C,IAAI/sB,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,mBAAmB;UAChC,CAAC6f,GAAG,GAAGzU,OAAO,CAAC0hB,gBAAgB,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGjN,GAAG,CAAClQ,IAAI,CAAC,CAACod,QAAQ,EAAEyE,QAAQ,CAAC,CAAC;QACpF;QACA,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACH,CACH,CAAC;IACH;EACF;EACA,OAAOX,IAAI;AACb,CAAC;;AAED;AACA,IAAIiB,mBAAmB;EAAA,IAAAC,KAAA,GAAAlD,iBAAA,CAAG,WAAO1lB,GAAG,EAAE6B,OAAO,EAAEI,OAAO,EAAEgB,YAAY,EAAK;IACvE,IAAIykB,IAAI;IACR,IAAI,CAAC7lB,OAAO,CAACK,OAAO,GAAG,EAAE,CAAC,mCAAmC,CAAC,EAAE;MAC9DL,OAAO,CAACK,OAAO,IAAI,EAAE,CAAC;MACtB,MAAMmB,QAAQ,GAAGpB,OAAO,CAACqB,cAAc;MACvC,IAAID,QAAQ,EAAE;QACZ,MAAMwlB,UAAU,GAAG7lB,UAAU,CAACf,OAAO,EAAEJ,OAAO,CAAC;QAC/C,IAAIgnB,UAAU,IAAI,MAAM,IAAIA,UAAU,EAAE;UACtC,MAAMC,OAAO,GAAGpV,UAAU,CAAC,CAAC;UAC5BgU,IAAI,SAASmB,UAAU;UACvBC,OAAO,CAAC,CAAC;QACX,CAAC,MAAM;UACLpB,IAAI,GAAGmB,UAAU;QACnB;QACA,IAAI,CAACnB,IAAI,EAAE;UACT,MAAM,IAAIte,KAAK,CAAC,oBAAoBnH,OAAO,CAACkB,SAAS,IAAItB,OAAO,CAAC2Y,UAAU,iBAAiB,CAAC;QAC/F;QACA,IAAI,CAACkN,IAAI,CAACqB,SAAS,EAAE;UACnB;YACE9mB,OAAO,CAAColB,UAAU,GAAGK,IAAI,CAACC,QAAQ;UACpC;UACAF,cAAc,CAACC,IAAI,EAAEzlB,OAAO,EAAE,CAAC,CAAC,gBAAgB,CAAC;UACjDylB,IAAI,CAACqB,SAAS,GAAG,IAAI;QACvB;QACA,MAAMC,cAAc,GAAGxV,UAAU,CAAC,gBAAgB,EAAEvR,OAAO,CAACkB,SAAS,CAAC;QACtE;UACEtB,OAAO,CAACK,OAAO,IAAI,CAAC,CAAC;QACvB;QACA,IAAI;UACF,IAAIwlB,IAAI,CAAC7lB,OAAO,CAAC;QACnB,CAAC,CAAC,OAAOrJ,CAAC,EAAE;UACVsK,YAAY,CAACtK,CAAC,EAAEwH,GAAG,CAAC;QACtB;QACA;UACE6B,OAAO,CAACK,OAAO,IAAI,CAAC,CAAC,CAAC;QACxB;QACA;UACEL,OAAO,CAACK,OAAO,IAAI,GAAG,CAAC;QACzB;QACA8mB,cAAc,CAAC,CAAC;QAChBC,qBAAqB,CAACpnB,OAAO,CAACC,cAAc,EAAE9B,GAAG,CAAC;MACpD,CAAC,MAAM;QACL0nB,IAAI,GAAG1nB,GAAG,CAACpJ,WAAW;QACtB,MAAMsyB,MAAM,GAAGlpB,GAAG,CAACmpB,SAAS;QAC5BC,cAAc,CAACC,WAAW,CAACH,MAAM,CAAC,CAAC1lB,IAAI,CAAC,MAAM3B,OAAO,CAACK,OAAO,IAAI,GAAG,CAAC,kBAAkB,CAAC;MAC1F;MACA,IAAIwlB,IAAI,IAAIA,IAAI,CAACnM,KAAK,EAAE;QACtB,IAAIA,KAAK;QACT,IAAI,OAAOmM,IAAI,CAACnM,KAAK,KAAK,QAAQ,EAAE;UAClCA,KAAK,GAAGmM,IAAI,CAACnM,KAAK;QACpB,CAAC,MAAM,IAAI,OAAOmM,IAAI,CAACnM,KAAK,KAAK,QAAQ,EAAE;UACzC1Z,OAAO,CAAC2Y,UAAU,GAAGH,WAAW,CAACra,GAAG,CAAC;UACrC,IAAI6B,OAAO,CAAC2Y,UAAU,EAAE;YACtBe,KAAK,GAAGmM,IAAI,CAACnM,KAAK,CAAC1Z,OAAO,CAAC2Y,UAAU,CAAC;UACxC;QACF;QACA,MAAMzD,QAAQ,GAAG2E,UAAU,CAACzZ,OAAO,EAAEJ,OAAO,CAAC2Y,UAAU,CAAC;QACxD,IAAI,CAAC9W,MAAM,CAACmY,GAAG,CAAC9E,QAAQ,CAAC,EAAE;UACzB,MAAMuS,iBAAiB,GAAG9V,UAAU,CAAC,gBAAgB,EAAEvR,OAAO,CAACkB,SAAS,CAAC;UACzEiY,aAAa,CAACrE,QAAQ,EAAEwE,KAAK,EAAE,CAAC,EAAEtZ,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,6BAA6B,CAAC;UACpFonB,iBAAiB,CAAC,CAAC;QACrB;MACF;IACF;IACA,MAAMhF,iBAAiB,GAAGziB,OAAO,CAAC6iB,mBAAmB;IACrD,MAAM6E,QAAQ,GAAGA,CAAA,KAAM9E,cAAc,CAAC5iB,OAAO,EAAE,IAAI,CAAC;IACpD,IAAIyiB,iBAAiB,IAAIA,iBAAiB,CAAC,MAAM,CAAC,EAAE;MAClDA,iBAAiB,CAAC,MAAM,CAAC,CAAC9d,IAAI,CAAC+iB,QAAQ,CAAC;IAC1C,CAAC,MAAM;MACLA,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EAAA,gBAvEGZ,mBAAmBA,CAAAa,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAf,KAAA,CAAAvC,KAAA,OAAAC,SAAA;EAAA;AAAA,GAuEtB;AACD,IAAI2C,qBAAqB,GAAGA,CAACnE,QAAQ,EAAE9kB,GAAG,KAAK;EAC7C;IACEmlB,QAAQ,CAACL,QAAQ,EAAE,mBAAmB,EAAE,KAAK,CAAC,EAAE9kB,GAAG,CAAC;EACtD;AACF,CAAC;;AAED;AACA,IAAI4pB,iBAAiB,GAAI5pB,GAAG,IAAK;EAC/B,IAAI,CAACyE,GAAG,CAACvC,OAAO,GAAG,CAAC,CAAC,6BAA6B,CAAC,EAAE;IACnD,MAAML,OAAO,GAAGJ,UAAU,CAACzB,GAAG,CAAC;IAC/B,MAAMiC,OAAO,GAAGJ,OAAO,CAACO,SAAS;IACjC,MAAMynB,YAAY,GAAGrW,UAAU,CAAC,mBAAmB,EAAEvR,OAAO,CAACkB,SAAS,CAAC;IACvE,IAAI,EAAEtB,OAAO,CAACK,OAAO,GAAG,CAAC,CAAC,mBAAmB,EAAE;MAC7CL,OAAO,CAACK,OAAO,IAAI,CAAC,CAAC;MACrB,IAAI+T,MAAM;MACV;QACEA,MAAM,GAAGjW,GAAG,CAACH,YAAY,CAACoE,UAAU,CAAC;QACrC,IAAIgS,MAAM,EAAE;UACV,IAAIhU,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,8BAA8B;YACpD,MAAM6U,QAAQ,GAAGyE,QAAQ,CAACxb,GAAG,CAACwK,UAAU,EAAEvI,OAAO,EAAEjC,GAAG,CAACH,YAAY,CAAC,QAAQ,CAAC,CAAC;YAC9EG,GAAG,CAACtE,SAAS,CAACyU,MAAM,CAAC4G,QAAQ,GAAG,IAAI,EAAEA,QAAQ,GAAG,IAAI,CAAC;UACxD,CAAC,MAAM,IAAI9U,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,8BAA8B;YAC3D,MAAM6U,QAAQ,GAAG2E,UAAU,CAACzZ,OAAO,EAAEjC,GAAG,CAACH,YAAY,CAAC,QAAQ,CAAE,CAAC;YACjEG,GAAG,CAAC,MAAM,CAAC,GAAG+W,QAAQ;UACxB;UACAhB,uBAAuB,CAAC/V,GAAG,EAAEiC,OAAO,CAACkB,SAAS,EAAE8S,MAAM,EAAEpU,OAAO,CAAC;QAClE;MACF;MACA,IAAI,CAACoU,MAAM,EAAE;QACX;QAAI;QACJhU,OAAO,CAACC,OAAO,IAAI,CAAC,CAAC,0BAA0B,CAAC,CAAC,yBAAyB,EAAE;UAC1E4nB,mBAAmB,CAAC9pB,GAAG,CAAC;QAC1B;MACF;MACA;QACE,IAAIskB,iBAAiB,GAAGtkB,GAAG;QAC3B,OAAOskB,iBAAiB,GAAGA,iBAAiB,CAAC7X,UAAU,IAAI6X,iBAAiB,CAACxI,IAAI,EAAE;UACjF,IAAIwI,iBAAiB,CAACpZ,QAAQ,KAAK,CAAC,CAAC,qBAAqBoZ,iBAAiB,CAACR,YAAY,CAAC,MAAM,CAAC,IAAIQ,iBAAiB,CAAC,KAAK,CAAC,IAAIA,iBAAiB,CAAC,KAAK,CAAC,EAAE;YACxJD,gBAAgB,CAACxiB,OAAO,EAAEA,OAAO,CAAC6iB,mBAAmB,GAAGJ,iBAAiB,CAAC;YAC1E;UACF;QACF;MACF;MACA,IAAIriB,OAAO,CAACsU,SAAS,EAAE;QACrBtf,MAAM,CAACC,OAAO,CAAC+K,OAAO,CAACsU,SAAS,CAAC,CAACrd,GAAG,CAAC,CAAC,CAAC2J,UAAU,EAAE,CAAC2T,WAAW,CAAC,CAAC,KAAK;UACrE,IAAIA,WAAW,GAAG,EAAE,CAAC,cAAcxW,GAAG,CAACwJ,cAAc,CAAC3G,UAAU,CAAC,EAAE;YACjE,MAAMvL,KAAK,GAAG0I,GAAG,CAAC6C,UAAU,CAAC;YAC7B,OAAO7C,GAAG,CAAC6C,UAAU,CAAC;YACtB7C,GAAG,CAAC6C,UAAU,CAAC,GAAGvL,KAAK;UACzB;QACF,CAAC,CAAC;MACJ;MACA;QACEqxB,mBAAmB,CAAC3oB,GAAG,EAAE6B,OAAO,EAAEI,OAAO,CAAC;MAC5C;IACF,CAAC,MAAM;MACL8nB,qBAAqB,CAAC/pB,GAAG,EAAE6B,OAAO,EAAEI,OAAO,CAAC+nB,WAAW,CAAC;MACxD,IAAInoB,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,cAAc,EAAE;QACrDmnB,qBAAqB,CAACpnB,OAAO,CAACC,cAAc,EAAE9B,GAAG,CAAC;MACpD,CAAC,MAAM,IAAI6B,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACa,gBAAgB,EAAE;QAC9Db,OAAO,CAACa,gBAAgB,CAACc,IAAI,CAAC,MAAMylB,qBAAqB,CAACpnB,OAAO,CAACC,cAAc,EAAE9B,GAAG,CAAC,CAAC;MACzF;IACF;IACA6pB,YAAY,CAAC,CAAC;EAChB;AACF,CAAC;AACD,IAAIC,mBAAmB,GAAI9pB,GAAG,IAAK;EACjC,IAAI,CAAC/H,GAAG,CAACuD,QAAQ,EAAE;IACjB;EACF;EACA,MAAMyuB,aAAa,GAAGjqB,GAAG,CAAC,MAAM,CAAC,GAAG/H,GAAG,CAACuD,QAAQ,CAAC0uB,aAAa,CAC5D,EACF,CAAC;EACDD,aAAa,CAAC,MAAM,CAAC,GAAG,IAAI;EAC5B/Y,YAAY,CAAClR,GAAG,EAAEiqB,aAAa,EAAEjqB,GAAG,CAAC8Q,UAAU,CAAC;AAClD,CAAC;AACD,IAAIqZ,kBAAkB,GAAGA,CAACrF,QAAQ,EAAE9kB,GAAG,KAAK;EAC1C;IACEmlB,QAAQ,CAACL,QAAQ,EAAE,sBAAsB,EAAE,KAAK,CAAC,EAAE9kB,GAAG,IAAI8kB,QAAQ,CAAC;EACrE;AACF,CAAC;AACD,IAAIsF,oBAAoB;EAAA,IAAAC,KAAA,GAAA3E,iBAAA,CAAG,WAAO1lB,GAAG,EAAK;IACxC,IAAI,CAACyE,GAAG,CAACvC,OAAO,GAAG,CAAC,CAAC,6BAA6B,CAAC,EAAE;MACnD,MAAML,OAAO,GAAGJ,UAAU,CAACzB,GAAG,CAAC;MAC/B;QACE,IAAI6B,OAAO,CAACyoB,aAAa,EAAE;UACzBzoB,OAAO,CAACyoB,aAAa,CAACpxB,GAAG,CAAEqxB,UAAU,IAAKA,UAAU,CAAC,CAAC,CAAC;UACvD1oB,OAAO,CAACyoB,aAAa,GAAG,KAAK,CAAC;QAChC;MACF;MACA,IAAIzoB,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,cAAc,EAAE;QACrDqoB,kBAAkB,CAACtoB,OAAO,CAACC,cAAc,EAAE9B,GAAG,CAAC;MACjD,CAAC,MAAM,IAAI6B,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACa,gBAAgB,EAAE;QAC9Db,OAAO,CAACa,gBAAgB,CAACc,IAAI,CAAC,MAAM2mB,kBAAkB,CAACtoB,OAAO,CAACC,cAAc,EAAE9B,GAAG,CAAC,CAAC;MACtF;IACF;IACA,IAAIkb,iBAAiB,CAACW,GAAG,CAAC7b,GAAG,CAAC,EAAE;MAC9Bkb,iBAAiB,CAAClD,MAAM,CAAChY,GAAG,CAAC;IAC/B;IACA,IAAIA,GAAG,CAACwK,UAAU,IAAI0Q,iBAAiB,CAACW,GAAG,CAAC7b,GAAG,CAACwK,UAAU,CAAC,EAAE;MAC3D0Q,iBAAiB,CAAClD,MAAM,CAAChY,GAAG,CAACwK,UAAU,CAAC;IAC1C;EACF,CAAC;EAAA,gBArBG4f,oBAAoBA,CAAAI,GAAA;IAAA,OAAAH,KAAA,CAAAhE,KAAA,OAAAC,SAAA;EAAA;AAAA,GAqBvB;;AAED;AACA,IAAImE,aAAa,GAAGA,CAACC,WAAW,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;EACjD,IAAI9sB,EAAE;EACN,IAAI,CAAC5F,GAAG,CAACuD,QAAQ,EAAE;IACjBrB,OAAO,CAACC,IAAI,CAAC,qEAAqE,CAAC;IACnF;EACF;EACA,MAAMwwB,YAAY,GAAGpX,UAAU,CAAC,CAAC;EACjC,MAAMqX,OAAO,GAAG,EAAE;EAClB,MAAMC,OAAO,GAAGH,OAAO,CAACG,OAAO,IAAI,EAAE;EACrC,MAAMC,eAAe,GAAG9yB,GAAG,CAACmxB,cAAc;EAC1C,MAAMthB,IAAI,GAAG7P,GAAG,CAACuD,QAAQ,CAACsM,IAAI;EAC9B,MAAMkjB,WAAW,GAAG,eAAgBljB,IAAI,CAACC,aAAa,CAAC,eAAe,CAAC;EACvE,MAAMkjB,UAAU,GAAG,eAAgBhzB,GAAG,CAACuD,QAAQ,CAACoV,aAAa,CAAC,OAAO,CAAC;EACtE,MAAMsa,0BAA0B,GAAG,EAAE;EACrC,IAAIC,eAAe;EACnB,IAAIC,eAAe,GAAG,IAAI;EAC1Bn0B,MAAM,CAACyI,MAAM,CAAC+E,GAAG,EAAEkmB,OAAO,CAAC;EAC3BlmB,GAAG,CAACC,cAAc,GAAG,IAAI0C,GAAG,CAACujB,OAAO,CAACU,YAAY,IAAI,IAAI,EAAEpzB,GAAG,CAACuD,QAAQ,CAAC8vB,OAAO,CAAC,CAAChkB,IAAI;EACrF;IACE7C,GAAG,CAACvC,OAAO,IAAI,CAAC,CAAC;EACnB;EACA;IACE0a,qBAAqB,CAAC,CAAC;EACzB;EACA,IAAI2O,iBAAiB,GAAG,KAAK;EAC7Bb,WAAW,CAACxxB,GAAG,CAAEsyB,UAAU,IAAK;IAC9BA,UAAU,CAAC,CAAC,CAAC,CAACtyB,GAAG,CAAEuyB,WAAW,IAAK;MACjC,IAAI/U,GAAG;MACP,MAAMzU,OAAO,GAAG;QACdC,OAAO,EAAEupB,WAAW,CAAC,CAAC,CAAC;QACvBtoB,SAAS,EAAEsoB,WAAW,CAAC,CAAC,CAAC;QACzBlV,SAAS,EAAEkV,WAAW,CAAC,CAAC,CAAC;QACzBzB,WAAW,EAAEyB,WAAW,CAAC,CAAC;MAC5B,CAAC;MACD,IAAIxpB,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,yBAAyB;QAC/CqpB,iBAAiB,GAAG,IAAI;MAC1B;MACA;QACEtpB,OAAO,CAACsU,SAAS,GAAGkV,WAAW,CAAC,CAAC,CAAC;MACpC;MACA;QACExpB,OAAO,CAAC+nB,WAAW,GAAGyB,WAAW,CAAC,CAAC,CAAC;MACtC;MACA;QACExpB,OAAO,CAAC0hB,gBAAgB,GAAG,EAAE;MAC/B;MACA;QACE1hB,OAAO,CAAColB,UAAU,GAAG,CAAC3Q,GAAG,GAAG+U,WAAW,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG/U,GAAG,GAAG,CAAC,CAAC;MAChE;MACA,MAAM/b,OAAO,GAAGsH,OAAO,CAACkB,SAAS;MACjC,MAAMuoB,WAAW,GAAG,cAAclnB,WAAW,CAAC;QAC5C;QACA5N,WAAWA,CAAC+0B,IAAI,EAAE;UAChB,KAAK,CAACA,IAAI,CAAC;UACX,IAAI,CAACC,2BAA2B,GAAG,KAAK;UACxCD,IAAI,GAAG,IAAI;UACX5pB,YAAY,CAAC4pB,IAAI,EAAE1pB,OAAO,CAAC;UAC3B,IAAIA,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,8BAA8B;YACpD;cACE,IAAI,CAACypB,IAAI,CAACnhB,UAAU,EAAE;gBACpBD,gBAAgB,CAACzM,IAAI,CAAC6tB,IAAI,EAAE1pB,OAAO,CAAC;cACtC,CAAC,MAAM;gBACL,IAAI0pB,IAAI,CAACnhB,UAAU,CAAC5K,IAAI,KAAK,MAAM,EAAE;kBACnC,MAAM,IAAIwJ,KAAK,CACb,6CAA6CnH,OAAO,CAACkB,SAAS,oBAAoBwoB,IAAI,CAACnhB,UAAU,CAAC5K,IAAI,+CACxG,CAAC;gBACH;cACF;YACF;UACF;QACF;QACAgqB,iBAAiBA,CAAA,EAAG;UAClB,MAAM/nB,OAAO,GAAGJ,UAAU,CAAC,IAAI,CAAC;UAChC,IAAI,CAAC,IAAI,CAACmqB,2BAA2B,EAAE;YACrC,IAAI,CAACA,2BAA2B,GAAG,IAAI;YACvC7B,qBAAqB,CAAC,IAAI,EAAEloB,OAAO,EAAEI,OAAO,CAAC+nB,WAAW,CAAC;UAC3D;UACA,IAAImB,eAAe,EAAE;YACnBU,YAAY,CAACV,eAAe,CAAC;YAC7BA,eAAe,GAAG,IAAI;UACxB;UACA,IAAIC,eAAe,EAAE;YACnBF,0BAA0B,CAAC1kB,IAAI,CAAC,IAAI,CAAC;UACvC,CAAC,MAAM;YACL/B,GAAG,CAACE,GAAG,CAAC,MAAMilB,iBAAiB,CAAC,IAAI,CAAC,CAAC;UACxC;QACF;QACAQ,oBAAoBA,CAAA,EAAG;UACrB3lB,GAAG,CAACE,GAAG,CAAC,MAAMylB,oBAAoB,CAAC,IAAI,CAAC,CAAC;UACzC3lB,GAAG,CAACI,GAAG,CAAC,MAAM;YACZ,IAAIqjB,GAAG;YACP,MAAMrmB,OAAO,GAAGJ,UAAU,CAAC,IAAI,CAAC;YAChC,MAAMmF,EAAE,GAAGskB,0BAA0B,CAACY,SAAS,CAAEhQ,IAAI,IAAKA,IAAI,KAAK,IAAI,CAAC;YACxE,IAAIlV,EAAE,GAAG,CAAC,CAAC,EAAE;cACXskB,0BAA0B,CAAC1G,MAAM,CAAC5d,EAAE,EAAE,CAAC,CAAC;YAC1C;YACA,IAAI,CAAC,CAACshB,GAAG,GAAGrmB,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACwV,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG6Q,GAAG,CAAC9S,KAAK,aAAalD,IAAI,IAAI,CAACrQ,OAAO,CAACwV,OAAO,CAACjC,KAAK,CAAC3J,WAAW,EAAE;cAC3I,OAAO5J,OAAO,CAACwV,OAAO,CAACjC,KAAK;YAC9B;UACF,CAAC,CAAC;QACJ;QACA2W,gBAAgBA,CAAA,EAAG;UACjB,OAAOtqB,UAAU,CAAC,IAAI,CAAC,CAACiB,gBAAgB;QAC1C;MACF,CAAC;MACD;QACE,IAAIT,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,8BAA8B;UACpD+L,oBAAoB,CAACyd,WAAW,CAACpY,SAAS,CAAC;QAC7C;MACF;MACArR,OAAO,CAACqB,cAAc,GAAGkoB,UAAU,CAAC,CAAC,CAAC;MACtC,IAAI,CAACV,OAAO,CAAC5wB,QAAQ,CAACS,OAAO,CAAC,IAAI,CAACowB,eAAe,CAAC5zB,GAAG,CAACwD,OAAO,CAAC,EAAE;QAC/DkwB,OAAO,CAACrkB,IAAI,CAAC7L,OAAO,CAAC;QACrBowB,eAAe,CAACiB,MAAM,CACpBrxB,OAAO,EACP8sB,cAAc,CAACiE,WAAW,EAAEzpB,OAAO,EAAE,CAAC,CAAC,0BAA0B,CACnE,CAAC;MACH;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAI4oB,OAAO,CAACrxB,MAAM,GAAG,CAAC,EAAE;IACtB,IAAI+xB,iBAAiB,EAAE;MACrBN,UAAU,CAACtZ,WAAW,IAAItN,WAAW;IACvC;IACA;MACE4mB,UAAU,CAACtZ,WAAW,IAAIkZ,OAAO,CAAC/d,IAAI,CAAC,CAAC,GAAG1I,YAAY;IACzD;IACA,IAAI6mB,UAAU,CAACpa,SAAS,CAACrX,MAAM,EAAE;MAC/ByxB,UAAU,CAACnrB,YAAY,CAAC,aAAa,EAAE,EAAE,CAAC;MAC1C,MAAMic,KAAK,GAAG,CAACle,EAAE,GAAG4G,GAAG,CAACuX,OAAO,KAAK,IAAI,GAAGne,EAAE,GAAG8J,wBAAwB,CAAC1P,GAAG,CAACuD,QAAQ,CAAC;MACtF,IAAIugB,KAAK,IAAI,IAAI,EAAE;QACjBkP,UAAU,CAACnrB,YAAY,CAAC,OAAO,EAAEic,KAAK,CAAC;MACzC;MACAjU,IAAI,CAACoJ,YAAY,CAAC+Z,UAAU,EAAED,WAAW,GAAGA,WAAW,CAAChf,WAAW,GAAGlE,IAAI,CAACgJ,UAAU,CAAC;IACxF;EACF;EACAsa,eAAe,GAAG,KAAK;EACvB,IAAIF,0BAA0B,CAAC1xB,MAAM,EAAE;IACrC0xB,0BAA0B,CAAChyB,GAAG,CAAE4iB,IAAI,IAAKA,IAAI,CAAC8N,iBAAiB,CAAC,CAAC,CAAC;EACpE,CAAC,MAAM;IACL;MACEnlB,GAAG,CAACE,GAAG,CAAC,MAAMwmB,eAAe,GAAGc,UAAU,CAACvF,UAAU,EAAE,EAAE,CAAC,CAAC;IAC7D;EACF;EACAkE,YAAY,CAAC,CAAC;AAChB,CAAC;;AAED;AACA,IAAIsB,QAAQ,GAAGA,CAACxD,CAAC,EAAE5W,QAAQ,KAAKA,QAAQ;AACxC,IAAIiY,qBAAqB,GAAGA,CAAC/pB,GAAG,EAAE6B,OAAO,EAAEsqB,SAAS,EAAEC,qBAAqB,KAAK;EAC9E,IAAID,SAAS,IAAIl0B,GAAG,CAACuD,QAAQ,EAAE;IAC7B2wB,SAAS,CAACjzB,GAAG,CAAC,CAAC,CAAC8P,KAAK,EAAEpI,IAAI,EAAE2S,MAAM,CAAC,KAAK;MACvC,MAAM7S,MAAM,GAAG2rB,qBAAqB,CAACp0B,GAAG,CAACuD,QAAQ,EAAEwE,GAAG,EAAEgJ,KAAK,CAAC;MAC9D,MAAMuR,OAAO,GAAG+R,iBAAiB,CAACzqB,OAAO,EAAE0R,MAAM,CAAC;MAClD,MAAMrO,IAAI,GAAGqnB,gBAAgB,CAACvjB,KAAK,CAAC;MACpCvE,GAAG,CAACM,GAAG,CAACrE,MAAM,EAAEE,IAAI,EAAE2Z,OAAO,EAAErV,IAAI,CAAC;MACpC,CAACrD,OAAO,CAACyoB,aAAa,GAAGzoB,OAAO,CAACyoB,aAAa,IAAI,EAAE,EAAE9jB,IAAI,CAAC,MAAM/B,GAAG,CAACW,GAAG,CAAC1E,MAAM,EAAEE,IAAI,EAAE2Z,OAAO,EAAErV,IAAI,CAAC,CAAC;IACxG,CAAC,CAAC;EACJ;AACF,CAAC;AACD,IAAIonB,iBAAiB,GAAGA,CAACzqB,OAAO,EAAEojB,UAAU,KAAMhK,EAAE,IAAK;EACvD,IAAIpd,EAAE;EACN,IAAI;IACF;MACE,IAAIgE,OAAO,CAACK,OAAO,GAAG,GAAG,CAAC,qBAAqB;QAC7C,CAACrE,EAAE,GAAGgE,OAAO,CAACC,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGjE,EAAE,CAAConB,UAAU,CAAC,CAAChK,EAAE,CAAC;MACrE,CAAC,MAAM;QACL,CAACpZ,OAAO,CAACmjB,iBAAiB,GAAGnjB,OAAO,CAACmjB,iBAAiB,IAAI,EAAE,EAAExe,IAAI,CAAC,CAACye,UAAU,EAAEhK,EAAE,CAAC,CAAC;MACtF;IACF;EACF,CAAC,CAAC,OAAOziB,CAAC,EAAE;IACVsK,YAAY,CAACtK,CAAC,EAAEqJ,OAAO,CAACM,aAAa,CAAC;EACxC;AACF,CAAC;AACD,IAAIkqB,qBAAqB,GAAGA,CAAC5sB,GAAG,EAAEO,GAAG,EAAEgJ,KAAK,KAAK;EAC/C,IAAIA,KAAK,GAAG,CAAC,CAAC,sBAAsB;IAClC,OAAOvJ,GAAG;EACZ;EACA,IAAIuJ,KAAK,GAAG,CAAC,CAAC,oBAAoB;IAChC,OAAO/Q,GAAG;EACZ;EACA,IAAI+Q,KAAK,GAAG,EAAE,CAAC,kBAAkB;IAC/B,OAAOvJ,GAAG,CAAC0X,IAAI;EACjB;EACA,OAAOnX,GAAG;AACZ,CAAC;AACD,IAAIusB,gBAAgB,GAAIvjB,KAAK,IAAKvD,uBAAuB,GAAG;EAC1D+mB,OAAO,EAAE,CAACxjB,KAAK,GAAG,CAAC,CAAC,mBAAmB,CAAC;EACxC2U,OAAO,EAAE,CAAC3U,KAAK,GAAG,CAAC,CAAC,mBAAmB;AACzC,CAAC,GAAG,CAACA,KAAK,GAAG,CAAC,CAAC,mBAAmB,CAAC;;AAEnC;AACA,IAAIyjB,QAAQ,GAAI1Q,KAAK,IAAKtX,GAAG,CAACuX,OAAO,GAAGD,KAAK;AAE7C,SAASjb,KAAK,IAAI4rB,CAAC,EAAER,QAAQ,IAAIS,CAAC,EAAEpoB,CAAC,EAAE3K,QAAQ,IAAIgzB,CAAC,EAAE5xB,UAAU,IAAI+R,CAAC,EAAE0d,aAAa,IAAIzd,CAAC,EAAElS,YAAY,IAAIpC,CAAC,EAAEmiB,WAAW,IAAIgS,CAAC,EAAEztB,UAAU,IAAI5G,CAAC,EAAEuO,QAAQ,IAAI+lB,CAAC,EAAEzsB,aAAa,IAAI0sB,CAAC,EAAEnZ,CAAC,EAAErU,UAAU,IAAIytB,CAAC,EAAE3X,IAAI,IAAIkN,CAAC,EAAE3H,UAAU,IAAIjG,CAAC,EAAE5c,MAAM,IAAIk1B,CAAC,EAAEpzB,eAAe,IAAIhD,CAAC,EAAE8vB,WAAW,IAAI9Z,CAAC,EAAExS,aAAa,IAAIqN,CAAC,EAAE/B,cAAc,IAAIpK,CAAC,EAAE0L,YAAY,IAAIimB,CAAC,EAAEvrB,gBAAgB,IAAIa,CAAC,EAAEiqB,QAAQ,IAAIU,CAAC,EAAE3yB,yBAAyB,IAAI4yB,CAAC,EAAEpmB,SAAS,IAAIqmB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}