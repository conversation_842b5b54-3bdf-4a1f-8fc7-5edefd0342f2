{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, e as getIonMode, h, j as Host } from './index-B_U9CtaY.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\nconst textCss = \":host(.ion-color){color:var(--ion-color-base)}\";\nconst Text = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '361035eae7b92dc109794348d39bad2f596eb6be',\n      class: createColorClasses(this.color, {\n        [mode]: true\n      })\n    }, h(\"slot\", {\n      key: 'c7b8835cf485ba9ecd73298f0529276ce1ea0852'\n    }));\n  }\n};\nText.style = textCss;\nexport { Text as ion_text };", "map": {"version": 3, "names": ["r", "registerInstance", "e", "getIonMode", "h", "j", "Host", "c", "createColorClasses", "textCss", "Text", "constructor", "hostRef", "render", "mode", "key", "class", "color", "style", "ion_text"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@ionic/core/dist/esm/ion-text.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, e as getIonMode, h, j as Host } from './index-B_U9CtaY.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\n\nconst textCss = \":host(.ion-color){color:var(--ion-color-base)}\";\n\nconst Text = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '361035eae7b92dc109794348d39bad2f596eb6be', class: createColorClasses(this.color, {\n                [mode]: true,\n            }) }, h(\"slot\", { key: 'c7b8835cf485ba9ecd73298f0529276ce1ea0852' })));\n    }\n};\nText.style = textCss;\n\nexport { Text as ion_text };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,QAAQ,qBAAqB;AAC1F,SAASC,CAAC,IAAIC,kBAAkB,QAAQ,qBAAqB;AAE7D,MAAMC,OAAO,GAAG,gDAAgD;AAEhE,MAAMC,IAAI,GAAG,MAAM;EACfC,WAAWA,CAACC,OAAO,EAAE;IACjBX,gBAAgB,CAAC,IAAI,EAAEW,OAAO,CAAC;EACnC;EACAC,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGX,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,CAAC,CAACE,IAAI,EAAE;MAAES,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAER,kBAAkB,CAAC,IAAI,CAACS,KAAK,EAAE;QACjG,CAACH,IAAI,GAAG;MACZ,CAAC;IAAE,CAAC,EAAEV,CAAC,CAAC,MAAM,EAAE;MAAEW,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC7E;AACJ,CAAC;AACDL,IAAI,CAACQ,KAAK,GAAGT,OAAO;AAEpB,SAASC,IAAI,IAAIS,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}