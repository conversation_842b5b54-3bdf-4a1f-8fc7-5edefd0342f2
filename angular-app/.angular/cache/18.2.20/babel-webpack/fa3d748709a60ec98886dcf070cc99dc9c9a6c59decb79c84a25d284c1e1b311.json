{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, e as getIonMode, h, j as Host } from './index-B_U9CtaY.js';\nconst backdropIosCss = \":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}\";\nconst backdropMdCss = \":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}\";\nconst Backdrop = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionBackdropTap = createEvent(this, \"ionBackdropTap\", 7);\n    /**\n     * If `true`, the backdrop will be visible.\n     */\n    this.visible = true;\n    /**\n     * If `true`, the backdrop will can be clicked and will emit the `ionBackdropTap` event.\n     */\n    this.tappable = true;\n    /**\n     * If `true`, the backdrop will stop propagation on tap.\n     */\n    this.stopPropagation = true;\n  }\n  onMouseDown(ev) {\n    this.emitTap(ev);\n  }\n  emitTap(ev) {\n    if (this.stopPropagation) {\n      ev.preventDefault();\n      ev.stopPropagation();\n    }\n    if (this.tappable) {\n      this.ionBackdropTap.emit();\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '7abaf2c310aa399607451b14063265e8a5846938',\n      \"aria-hidden\": \"true\",\n      class: {\n        [mode]: true,\n        'backdrop-hide': !this.visible,\n        'backdrop-no-tappable': !this.tappable\n      }\n    });\n  }\n};\nBackdrop.style = {\n  ios: backdropIosCss,\n  md: backdropMdCss\n};\nexport { Backdrop as ion_backdrop };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "e", "getIonMode", "h", "j", "Host", "backdropIosCss", "backdropMdCss", "Backdrop", "constructor", "hostRef", "ionBackdropTap", "visible", "tappable", "stopPropagation", "onMouseDown", "ev", "emitTap", "preventDefault", "emit", "render", "mode", "key", "class", "style", "ios", "md", "ion_backdrop"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@ionic/core/dist/esm/ion-backdrop.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, e as getIonMode, h, j as Host } from './index-B_U9CtaY.js';\n\nconst backdropIosCss = \":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}\";\n\nconst backdropMdCss = \":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}\";\n\nconst Backdrop = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionBackdropTap = createEvent(this, \"ionBackdropTap\", 7);\n        /**\n         * If `true`, the backdrop will be visible.\n         */\n        this.visible = true;\n        /**\n         * If `true`, the backdrop will can be clicked and will emit the `ionBackdropTap` event.\n         */\n        this.tappable = true;\n        /**\n         * If `true`, the backdrop will stop propagation on tap.\n         */\n        this.stopPropagation = true;\n    }\n    onMouseDown(ev) {\n        this.emitTap(ev);\n    }\n    emitTap(ev) {\n        if (this.stopPropagation) {\n            ev.preventDefault();\n            ev.stopPropagation();\n        }\n        if (this.tappable) {\n            this.ionBackdropTap.emit();\n        }\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '7abaf2c310aa399607451b14063265e8a5846938', \"aria-hidden\": \"true\", class: {\n                [mode]: true,\n                'backdrop-hide': !this.visible,\n                'backdrop-no-tappable': !this.tappable,\n            } }));\n    }\n};\nBackdrop.style = {\n    ios: backdropIosCss,\n    md: backdropMdCss\n};\n\nexport { Backdrop as ion_backdrop };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,QAAQ,qBAAqB;AAE5G,MAAMC,cAAc,GAAG,wWAAwW;AAE/X,MAAMC,aAAa,GAAG,wWAAwW;AAE9X,MAAMC,QAAQ,GAAG,MAAM;EACnBC,WAAWA,CAACC,OAAO,EAAE;IACjBZ,gBAAgB,CAAC,IAAI,EAAEY,OAAO,CAAC;IAC/B,IAAI,CAACC,cAAc,GAAGX,WAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D;AACR;AACA;IACQ,IAAI,CAACY,OAAO,GAAG,IAAI;IACnB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,IAAI;EAC/B;EACAC,WAAWA,CAACC,EAAE,EAAE;IACZ,IAAI,CAACC,OAAO,CAACD,EAAE,CAAC;EACpB;EACAC,OAAOA,CAACD,EAAE,EAAE;IACR,IAAI,IAAI,CAACF,eAAe,EAAE;MACtBE,EAAE,CAACE,cAAc,CAAC,CAAC;MACnBF,EAAE,CAACF,eAAe,CAAC,CAAC;IACxB;IACA,IAAI,IAAI,CAACD,QAAQ,EAAE;MACf,IAAI,CAACF,cAAc,CAACQ,IAAI,CAAC,CAAC;IAC9B;EACJ;EACAC,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGnB,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,CAAC,CAACE,IAAI,EAAE;MAAEiB,GAAG,EAAE,0CAA0C;MAAE,aAAa,EAAE,MAAM;MAAEC,KAAK,EAAE;QACzF,CAACF,IAAI,GAAG,IAAI;QACZ,eAAe,EAAE,CAAC,IAAI,CAACT,OAAO;QAC9B,sBAAsB,EAAE,CAAC,IAAI,CAACC;MAClC;IAAE,CAAC,CAAC;EACZ;AACJ,CAAC;AACDL,QAAQ,CAACgB,KAAK,GAAG;EACbC,GAAG,EAAEnB,cAAc;EACnBoB,EAAE,EAAEnB;AACR,CAAC;AAED,SAASC,QAAQ,IAAImB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}