{"ast": null, "code": "import { EvaluationComponent } from './components/evaluation/evaluation.component';\nexport const routes = [{\n  path: '',\n  component: EvaluationComponent\n}, {\n  path: '**',\n  redirectTo: ''\n}];", "map": {"version": 3, "names": ["EvaluationComponent", "routes", "path", "component", "redirectTo"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/src/app/app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { EvaluationComponent } from './components/evaluation/evaluation.component';\n\nexport const routes: Routes = [\n  { path: '', component: EvaluationComponent },\n  { path: '**', redirectTo: '' }\n];\n"], "mappings": "AACA,SAASA,mBAAmB,QAAQ,8CAA8C;AAElF,OAAO,MAAMC,MAAM,GAAW,CAC5B;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH;AAAmB,CAAE,EAC5C;EAAEE,IAAI,EAAE,IAAI;EAAEE,UAAU,EAAE;AAAE,CAAE,CAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}