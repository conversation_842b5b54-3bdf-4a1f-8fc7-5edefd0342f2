{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as writeTask } from './index-B_U9CtaY.js';\nimport { h as hapticSelectionEnd, a as hapticSelectionChanged, b as hapticSelectionStart } from './haptic-DzAMWJuk.js';\nimport { createGesture } from './index-CfgBF1SE.js';\nconst createButtonActiveGesture = (el, isButton) => {\n  let currentTouchedButton;\n  let initialTouchedButton;\n  const activateButtonAtPoint = (x, y, hapticFeedbackFn) => {\n    if (typeof document === 'undefined') {\n      return;\n    }\n    const target = document.elementFromPoint(x, y);\n    if (!target || !isButton(target) || target.disabled) {\n      clearActiveButton();\n      return;\n    }\n    if (target !== currentTouchedButton) {\n      clearActiveButton();\n      setActiveButton(target, hapticFeedbackFn);\n    }\n  };\n  const setActiveButton = (button, hapticFeedbackFn) => {\n    currentTouchedButton = button;\n    if (!initialTouchedButton) {\n      initialTouchedButton = currentTouchedButton;\n    }\n    const buttonToModify = currentTouchedButton;\n    writeTask(() => buttonToModify.classList.add('ion-activated'));\n    hapticFeedbackFn();\n  };\n  const clearActiveButton = (dispatchClick = false) => {\n    if (!currentTouchedButton) {\n      return;\n    }\n    const buttonToModify = currentTouchedButton;\n    writeTask(() => buttonToModify.classList.remove('ion-activated'));\n    /**\n     * Clicking on one button, but releasing on another button\n     * does not dispatch a click event in browsers, so we\n     * need to do it manually here. Some browsers will\n     * dispatch a click if clicking on one button, dragging over\n     * another button, and releasing on the original button. In that\n     * case, we need to make sure we do not cause a double click there.\n     */\n    if (dispatchClick && initialTouchedButton !== currentTouchedButton) {\n      currentTouchedButton.click();\n    }\n    currentTouchedButton = undefined;\n  };\n  return createGesture({\n    el,\n    gestureName: 'buttonActiveDrag',\n    threshold: 0,\n    onStart: ev => activateButtonAtPoint(ev.currentX, ev.currentY, hapticSelectionStart),\n    onMove: ev => activateButtonAtPoint(ev.currentX, ev.currentY, hapticSelectionChanged),\n    onEnd: () => {\n      clearActiveButton(true);\n      hapticSelectionEnd();\n      initialTouchedButton = undefined;\n    }\n  });\n};\nexport { createButtonActiveGesture as c };", "map": {"version": 3, "names": ["w", "writeTask", "h", "hapticSelectionEnd", "a", "hapticSelectionChanged", "b", "hapticSelectionStart", "createGesture", "createButtonActiveGesture", "el", "isButton", "currentTouchedButton", "initialTouchedButton", "activateButtonAtPoint", "x", "y", "hapticFeedbackFn", "document", "target", "elementFromPoint", "disabled", "clearActiveButton", "setActiveButton", "button", "buttonToModify", "classList", "add", "dispatchClick", "remove", "click", "undefined", "<PERSON><PERSON><PERSON>", "threshold", "onStart", "ev", "currentX", "currentY", "onMove", "onEnd", "c"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@ionic/core/dist/esm/button-active-Bxcnevju.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as writeTask } from './index-B_U9CtaY.js';\nimport { h as hapticSelectionEnd, a as hapticSelectionChanged, b as hapticSelectionStart } from './haptic-DzAMWJuk.js';\nimport { createGesture } from './index-CfgBF1SE.js';\n\nconst createButtonActiveGesture = (el, isButton) => {\n    let currentTouchedButton;\n    let initialTouchedButton;\n    const activateButtonAtPoint = (x, y, hapticFeedbackFn) => {\n        if (typeof document === 'undefined') {\n            return;\n        }\n        const target = document.elementFromPoint(x, y);\n        if (!target || !isButton(target) || target.disabled) {\n            clearActiveButton();\n            return;\n        }\n        if (target !== currentTouchedButton) {\n            clearActiveButton();\n            setActiveButton(target, hapticFeedbackFn);\n        }\n    };\n    const setActiveButton = (button, hapticFeedbackFn) => {\n        currentTouchedButton = button;\n        if (!initialTouchedButton) {\n            initialTouchedButton = currentTouchedButton;\n        }\n        const buttonToModify = currentTouchedButton;\n        writeTask(() => buttonToModify.classList.add('ion-activated'));\n        hapticFeedbackFn();\n    };\n    const clearActiveButton = (dispatchClick = false) => {\n        if (!currentTouchedButton) {\n            return;\n        }\n        const buttonToModify = currentTouchedButton;\n        writeTask(() => buttonToModify.classList.remove('ion-activated'));\n        /**\n         * Clicking on one button, but releasing on another button\n         * does not dispatch a click event in browsers, so we\n         * need to do it manually here. Some browsers will\n         * dispatch a click if clicking on one button, dragging over\n         * another button, and releasing on the original button. In that\n         * case, we need to make sure we do not cause a double click there.\n         */\n        if (dispatchClick && initialTouchedButton !== currentTouchedButton) {\n            currentTouchedButton.click();\n        }\n        currentTouchedButton = undefined;\n    };\n    return createGesture({\n        el,\n        gestureName: 'buttonActiveDrag',\n        threshold: 0,\n        onStart: (ev) => activateButtonAtPoint(ev.currentX, ev.currentY, hapticSelectionStart),\n        onMove: (ev) => activateButtonAtPoint(ev.currentX, ev.currentY, hapticSelectionChanged),\n        onEnd: () => {\n            clearActiveButton(true);\n            hapticSelectionEnd();\n            initialTouchedButton = undefined;\n        },\n    });\n};\n\nexport { createButtonActiveGesture as c };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,SAAS,QAAQ,qBAAqB;AACpD,SAASC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,sBAAsB,EAAEC,CAAC,IAAIC,oBAAoB,QAAQ,sBAAsB;AACtH,SAASC,aAAa,QAAQ,qBAAqB;AAEnD,MAAMC,yBAAyB,GAAGA,CAACC,EAAE,EAAEC,QAAQ,KAAK;EAChD,IAAIC,oBAAoB;EACxB,IAAIC,oBAAoB;EACxB,MAAMC,qBAAqB,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAEC,gBAAgB,KAAK;IACtD,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;MACjC;IACJ;IACA,MAAMC,MAAM,GAAGD,QAAQ,CAACE,gBAAgB,CAACL,CAAC,EAAEC,CAAC,CAAC;IAC9C,IAAI,CAACG,MAAM,IAAI,CAACR,QAAQ,CAACQ,MAAM,CAAC,IAAIA,MAAM,CAACE,QAAQ,EAAE;MACjDC,iBAAiB,CAAC,CAAC;MACnB;IACJ;IACA,IAAIH,MAAM,KAAKP,oBAAoB,EAAE;MACjCU,iBAAiB,CAAC,CAAC;MACnBC,eAAe,CAACJ,MAAM,EAAEF,gBAAgB,CAAC;IAC7C;EACJ,CAAC;EACD,MAAMM,eAAe,GAAGA,CAACC,MAAM,EAAEP,gBAAgB,KAAK;IAClDL,oBAAoB,GAAGY,MAAM;IAC7B,IAAI,CAACX,oBAAoB,EAAE;MACvBA,oBAAoB,GAAGD,oBAAoB;IAC/C;IACA,MAAMa,cAAc,GAAGb,oBAAoB;IAC3CX,SAAS,CAAC,MAAMwB,cAAc,CAACC,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC,CAAC;IAC9DV,gBAAgB,CAAC,CAAC;EACtB,CAAC;EACD,MAAMK,iBAAiB,GAAGA,CAACM,aAAa,GAAG,KAAK,KAAK;IACjD,IAAI,CAAChB,oBAAoB,EAAE;MACvB;IACJ;IACA,MAAMa,cAAc,GAAGb,oBAAoB;IAC3CX,SAAS,CAAC,MAAMwB,cAAc,CAACC,SAAS,CAACG,MAAM,CAAC,eAAe,CAAC,CAAC;IACjE;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAID,aAAa,IAAIf,oBAAoB,KAAKD,oBAAoB,EAAE;MAChEA,oBAAoB,CAACkB,KAAK,CAAC,CAAC;IAChC;IACAlB,oBAAoB,GAAGmB,SAAS;EACpC,CAAC;EACD,OAAOvB,aAAa,CAAC;IACjBE,EAAE;IACFsB,WAAW,EAAE,kBAAkB;IAC/BC,SAAS,EAAE,CAAC;IACZC,OAAO,EAAGC,EAAE,IAAKrB,qBAAqB,CAACqB,EAAE,CAACC,QAAQ,EAAED,EAAE,CAACE,QAAQ,EAAE9B,oBAAoB,CAAC;IACtF+B,MAAM,EAAGH,EAAE,IAAKrB,qBAAqB,CAACqB,EAAE,CAACC,QAAQ,EAAED,EAAE,CAACE,QAAQ,EAAEhC,sBAAsB,CAAC;IACvFkC,KAAK,EAAEA,CAAA,KAAM;MACTjB,iBAAiB,CAAC,IAAI,CAAC;MACvBnB,kBAAkB,CAAC,CAAC;MACpBU,oBAAoB,GAAGkB,SAAS;IACpC;EACJ,CAAC,CAAC;AACN,CAAC;AAED,SAAStB,yBAAyB,IAAI+B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}