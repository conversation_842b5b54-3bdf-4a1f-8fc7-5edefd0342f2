{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function sequenceEqual(compareTo, comparator = (a, b) => a === b) {\n  return operate((source, subscriber) => {\n    const aState = createState();\n    const bState = createState();\n    const emit = isEqual => {\n      subscriber.next(isEqual);\n      subscriber.complete();\n    };\n    const createSubscriber = (selfState, otherState) => {\n      const sequenceEqualSubscriber = createOperatorSubscriber(subscriber, a => {\n        const {\n          buffer,\n          complete\n        } = otherState;\n        if (buffer.length === 0) {\n          complete ? emit(false) : selfState.buffer.push(a);\n        } else {\n          !comparator(a, buffer.shift()) && emit(false);\n        }\n      }, () => {\n        selfState.complete = true;\n        const {\n          complete,\n          buffer\n        } = otherState;\n        complete && emit(buffer.length === 0);\n        sequenceEqualSubscriber === null || sequenceEqualSubscriber === void 0 ? void 0 : sequenceEqualSubscriber.unsubscribe();\n      });\n      return sequenceEqualSubscriber;\n    };\n    source.subscribe(createSubscriber(aState, bState));\n    innerFrom(compareTo).subscribe(createSubscriber(bState, aState));\n  });\n}\nfunction createState() {\n  return {\n    buffer: [],\n    complete: false\n  };\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "innerFrom", "sequenceEqual", "compareTo", "comparator", "a", "b", "source", "subscriber", "aState", "createState", "bState", "emit", "isEqual", "next", "complete", "createSubscriber", "selfState", "otherState", "sequenceEqualSubscriber", "buffer", "length", "push", "shift", "unsubscribe", "subscribe"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/rxjs/dist/esm/internal/operators/sequenceEqual.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function sequenceEqual(compareTo, comparator = (a, b) => a === b) {\n    return operate((source, subscriber) => {\n        const aState = createState();\n        const bState = createState();\n        const emit = (isEqual) => {\n            subscriber.next(isEqual);\n            subscriber.complete();\n        };\n        const createSubscriber = (selfState, otherState) => {\n            const sequenceEqualSubscriber = createOperatorSubscriber(subscriber, (a) => {\n                const { buffer, complete } = otherState;\n                if (buffer.length === 0) {\n                    complete ? emit(false) : selfState.buffer.push(a);\n                }\n                else {\n                    !comparator(a, buffer.shift()) && emit(false);\n                }\n            }, () => {\n                selfState.complete = true;\n                const { complete, buffer } = otherState;\n                complete && emit(buffer.length === 0);\n                sequenceEqualSubscriber === null || sequenceEqualSubscriber === void 0 ? void 0 : sequenceEqualSubscriber.unsubscribe();\n            });\n            return sequenceEqualSubscriber;\n        };\n        source.subscribe(createSubscriber(aState, bState));\n        innerFrom(compareTo).subscribe(createSubscriber(bState, aState));\n    });\n}\nfunction createState() {\n    return {\n        buffer: [],\n        complete: false,\n    };\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAO,SAASC,aAAaA,CAACC,SAAS,EAAEC,UAAU,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAKC,CAAC,EAAE;EACrE,OAAOP,OAAO,CAAC,CAACQ,MAAM,EAAEC,UAAU,KAAK;IACnC,MAAMC,MAAM,GAAGC,WAAW,CAAC,CAAC;IAC5B,MAAMC,MAAM,GAAGD,WAAW,CAAC,CAAC;IAC5B,MAAME,IAAI,GAAIC,OAAO,IAAK;MACtBL,UAAU,CAACM,IAAI,CAACD,OAAO,CAAC;MACxBL,UAAU,CAACO,QAAQ,CAAC,CAAC;IACzB,CAAC;IACD,MAAMC,gBAAgB,GAAGA,CAACC,SAAS,EAAEC,UAAU,KAAK;MAChD,MAAMC,uBAAuB,GAAGnB,wBAAwB,CAACQ,UAAU,EAAGH,CAAC,IAAK;QACxE,MAAM;UAAEe,MAAM;UAAEL;QAAS,CAAC,GAAGG,UAAU;QACvC,IAAIE,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;UACrBN,QAAQ,GAAGH,IAAI,CAAC,KAAK,CAAC,GAAGK,SAAS,CAACG,MAAM,CAACE,IAAI,CAACjB,CAAC,CAAC;QACrD,CAAC,MACI;UACD,CAACD,UAAU,CAACC,CAAC,EAAEe,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC,IAAIX,IAAI,CAAC,KAAK,CAAC;QACjD;MACJ,CAAC,EAAE,MAAM;QACLK,SAAS,CAACF,QAAQ,GAAG,IAAI;QACzB,MAAM;UAAEA,QAAQ;UAAEK;QAAO,CAAC,GAAGF,UAAU;QACvCH,QAAQ,IAAIH,IAAI,CAACQ,MAAM,CAACC,MAAM,KAAK,CAAC,CAAC;QACrCF,uBAAuB,KAAK,IAAI,IAAIA,uBAAuB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,uBAAuB,CAACK,WAAW,CAAC,CAAC;MAC3H,CAAC,CAAC;MACF,OAAOL,uBAAuB;IAClC,CAAC;IACDZ,MAAM,CAACkB,SAAS,CAACT,gBAAgB,CAACP,MAAM,EAAEE,MAAM,CAAC,CAAC;IAClDV,SAAS,CAACE,SAAS,CAAC,CAACsB,SAAS,CAACT,gBAAgB,CAACL,MAAM,EAAEF,MAAM,CAAC,CAAC;EACpE,CAAC,CAAC;AACN;AACA,SAASC,WAAWA,CAAA,EAAG;EACnB,OAAO;IACHU,MAAM,EAAE,EAAE;IACVL,QAAQ,EAAE;EACd,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}