{"ast": null, "code": "class TranslateHttpLoader {\n  http;\n  prefix;\n  suffix;\n  constructor(http, prefix = \"/assets/i18n/\", suffix = \".json\") {\n    this.http = http;\n    this.prefix = prefix;\n    this.suffix = suffix;\n  }\n  /**\n   * Gets the translations from the server\n   */\n  getTranslation(lang) {\n    return this.http.get(`${this.prefix}${lang}${this.suffix}`);\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TranslateHttpLoader };", "map": {"version": 3, "names": ["TranslateHttpLoader", "http", "prefix", "suffix", "constructor", "getTranslation", "lang", "get"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@ngx-translate/http-loader/dist/fesm2022/ngx-translate-http-loader.mjs"], "sourcesContent": ["class TranslateHttpLoader {\n    http;\n    prefix;\n    suffix;\n    constructor(http, prefix = \"/assets/i18n/\", suffix = \".json\") {\n        this.http = http;\n        this.prefix = prefix;\n        this.suffix = suffix;\n    }\n    /**\n     * Gets the translations from the server\n     */\n    getTranslation(lang) {\n        return this.http.get(`${this.prefix}${lang}${this.suffix}`);\n    }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TranslateHttpLoader };\n"], "mappings": "AAAA,MAAMA,mBAAmB,CAAC;EACtBC,IAAI;EACJC,MAAM;EACNC,MAAM;EACNC,WAAWA,CAACH,IAAI,EAAEC,MAAM,GAAG,eAAe,EAAEC,MAAM,GAAG,OAAO,EAAE;IAC1D,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACA;AACJ;AACA;EACIE,cAAcA,CAACC,IAAI,EAAE;IACjB,OAAO,IAAI,CAACL,IAAI,CAACM,GAAG,CAAC,GAAG,IAAI,CAACL,MAAM,GAAGI,IAAI,GAAG,IAAI,CAACH,MAAM,EAAE,CAAC;EAC/D;AACJ;;AAEA;AACA;AACA;;AAEA,SAASH,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}