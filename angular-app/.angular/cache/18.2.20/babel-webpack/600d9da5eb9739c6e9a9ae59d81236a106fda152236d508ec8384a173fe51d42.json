{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { g as getCapacitor } from './capacitor-CFERIeaU.js';\nvar ImpactStyle;\n(function (ImpactStyle) {\n  /**\n   * A collision between large, heavy user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Heavy\"] = \"HEAVY\";\n  /**\n   * A collision between moderately sized user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Medium\"] = \"MEDIUM\";\n  /**\n   * A collision between small, light user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Light\"] = \"LIGHT\";\n})(ImpactStyle || (ImpactStyle = {}));\nvar NotificationType;\n(function (NotificationType) {\n  /**\n   * A notification feedback type indicating that a task has completed successfully\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Success\"] = \"SUCCESS\";\n  /**\n   * A notification feedback type indicating that a task has produced a warning\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Warning\"] = \"WARNING\";\n  /**\n   * A notification feedback type indicating that a task has failed\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Error\"] = \"ERROR\";\n})(NotificationType || (NotificationType = {}));\nconst HapticEngine = {\n  getEngine() {\n    const capacitor = getCapacitor();\n    if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('Haptics')) {\n      // Capacitor\n      return capacitor.Plugins.Haptics;\n    }\n    return undefined;\n  },\n  available() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return false;\n    }\n    const capacitor = getCapacitor();\n    /**\n     * Developers can manually import the\n     * Haptics plugin in their app which will cause\n     * getEngine to return the Haptics engine. However,\n     * the Haptics engine will throw an error if\n     * used in a web browser that does not support\n     * the Vibrate API. This check avoids that error\n     * if the browser does not support the Vibrate API.\n     */\n    if ((capacitor === null || capacitor === void 0 ? void 0 : capacitor.getPlatform()) === 'web') {\n      // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n      return typeof navigator !== 'undefined' && navigator.vibrate !== undefined;\n    }\n    return true;\n  },\n  impact(options) {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    engine.impact({\n      style: options.style\n    });\n  },\n  notification(options) {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    engine.notification({\n      type: options.type\n    });\n  },\n  selection() {\n    this.impact({\n      style: ImpactStyle.Light\n    });\n  },\n  selectionStart() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    engine.selectionStart();\n  },\n  selectionChanged() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    engine.selectionChanged();\n  },\n  selectionEnd() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    engine.selectionEnd();\n  }\n};\n/**\n * Check to see if the Haptic Plugin is available\n * @return Returns `true` or false if the plugin is available\n */\nconst hapticAvailable = () => {\n  return HapticEngine.available();\n};\n/**\n * Trigger a selection changed haptic event. Good for one-time events\n * (not for gestures)\n */\nconst hapticSelection = () => {\n  hapticAvailable() && HapticEngine.selection();\n};\n/**\n * Tell the haptic engine that a gesture for a selection change is starting.\n */\nconst hapticSelectionStart = () => {\n  hapticAvailable() && HapticEngine.selectionStart();\n};\n/**\n * Tell the haptic engine that a selection changed during a gesture.\n */\nconst hapticSelectionChanged = () => {\n  hapticAvailable() && HapticEngine.selectionChanged();\n};\n/**\n * Tell the haptic engine we are done with a gesture. This needs to be\n * called lest resources are not properly recycled.\n */\nconst hapticSelectionEnd = () => {\n  hapticAvailable() && HapticEngine.selectionEnd();\n};\n/**\n * Use this to indicate success/failure/warning to the user.\n * options should be of the type `{ style: ImpactStyle.LIGHT }` (or `MEDIUM`/`HEAVY`)\n */\nconst hapticImpact = options => {\n  hapticAvailable() && HapticEngine.impact(options);\n};\nexport { ImpactStyle as I, hapticSelectionChanged as a, hapticSelectionStart as b, hapticSelection as c, hapticImpact as d, hapticSelectionEnd as h };", "map": {"version": 3, "names": ["g", "getCapacitor", "ImpactStyle", "NotificationType", "HapticEngine", "getEngine", "capacitor", "isPluginAvailable", "Plugins", "Haptics", "undefined", "available", "engine", "getPlatform", "navigator", "vibrate", "impact", "options", "style", "notification", "type", "selection", "Light", "selectionStart", "selectionChanged", "selectionEnd", "hapticAvailable", "hapticSelection", "hapticSelectionStart", "hapticSelectionChanged", "hapticSelectionEnd", "hapticImpact", "I", "a", "b", "c", "d", "h"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@ionic/core/dist/esm/haptic-DzAMWJuk.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { g as getCapacitor } from './capacitor-CFERIeaU.js';\n\nvar ImpactStyle;\n(function (ImpactStyle) {\n    /**\n     * A collision between large, heavy user interface elements\n     *\n     * @since 1.0.0\n     */\n    ImpactStyle[\"Heavy\"] = \"HEAVY\";\n    /**\n     * A collision between moderately sized user interface elements\n     *\n     * @since 1.0.0\n     */\n    ImpactStyle[\"Medium\"] = \"MEDIUM\";\n    /**\n     * A collision between small, light user interface elements\n     *\n     * @since 1.0.0\n     */\n    ImpactStyle[\"Light\"] = \"LIGHT\";\n})(ImpactStyle || (ImpactStyle = {}));\nvar NotificationType;\n(function (NotificationType) {\n    /**\n     * A notification feedback type indicating that a task has completed successfully\n     *\n     * @since 1.0.0\n     */\n    NotificationType[\"Success\"] = \"SUCCESS\";\n    /**\n     * A notification feedback type indicating that a task has produced a warning\n     *\n     * @since 1.0.0\n     */\n    NotificationType[\"Warning\"] = \"WARNING\";\n    /**\n     * A notification feedback type indicating that a task has failed\n     *\n     * @since 1.0.0\n     */\n    NotificationType[\"Error\"] = \"ERROR\";\n})(NotificationType || (NotificationType = {}));\nconst HapticEngine = {\n    getEngine() {\n        const capacitor = getCapacitor();\n        if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('Haptics')) {\n            // Capacitor\n            return capacitor.Plugins.Haptics;\n        }\n        return undefined;\n    },\n    available() {\n        const engine = this.getEngine();\n        if (!engine) {\n            return false;\n        }\n        const capacitor = getCapacitor();\n        /**\n         * Developers can manually import the\n         * Haptics plugin in their app which will cause\n         * getEngine to return the Haptics engine. However,\n         * the Haptics engine will throw an error if\n         * used in a web browser that does not support\n         * the Vibrate API. This check avoids that error\n         * if the browser does not support the Vibrate API.\n         */\n        if ((capacitor === null || capacitor === void 0 ? void 0 : capacitor.getPlatform()) === 'web') {\n            // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n            return typeof navigator !== 'undefined' && navigator.vibrate !== undefined;\n        }\n        return true;\n    },\n    impact(options) {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        engine.impact({ style: options.style });\n    },\n    notification(options) {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        engine.notification({ type: options.type });\n    },\n    selection() {\n        this.impact({ style: ImpactStyle.Light });\n    },\n    selectionStart() {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        engine.selectionStart();\n    },\n    selectionChanged() {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        engine.selectionChanged();\n    },\n    selectionEnd() {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        engine.selectionEnd();\n    },\n};\n/**\n * Check to see if the Haptic Plugin is available\n * @return Returns `true` or false if the plugin is available\n */\nconst hapticAvailable = () => {\n    return HapticEngine.available();\n};\n/**\n * Trigger a selection changed haptic event. Good for one-time events\n * (not for gestures)\n */\nconst hapticSelection = () => {\n    hapticAvailable() && HapticEngine.selection();\n};\n/**\n * Tell the haptic engine that a gesture for a selection change is starting.\n */\nconst hapticSelectionStart = () => {\n    hapticAvailable() && HapticEngine.selectionStart();\n};\n/**\n * Tell the haptic engine that a selection changed during a gesture.\n */\nconst hapticSelectionChanged = () => {\n    hapticAvailable() && HapticEngine.selectionChanged();\n};\n/**\n * Tell the haptic engine we are done with a gesture. This needs to be\n * called lest resources are not properly recycled.\n */\nconst hapticSelectionEnd = () => {\n    hapticAvailable() && HapticEngine.selectionEnd();\n};\n/**\n * Use this to indicate success/failure/warning to the user.\n * options should be of the type `{ style: ImpactStyle.LIGHT }` (or `MEDIUM`/`HEAVY`)\n */\nconst hapticImpact = (options) => {\n    hapticAvailable() && HapticEngine.impact(options);\n};\n\nexport { ImpactStyle as I, hapticSelectionChanged as a, hapticSelectionStart as b, hapticSelection as c, hapticImpact as d, hapticSelectionEnd as h };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,YAAY,QAAQ,yBAAyB;AAE3D,IAAIC,WAAW;AACf,CAAC,UAAUA,WAAW,EAAE;EACpB;AACJ;AACA;AACA;AACA;EACIA,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;EAC9B;AACJ;AACA;AACA;AACA;EACIA,WAAW,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAChC;AACJ;AACA;AACA;AACA;EACIA,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;AAClC,CAAC,EAAEA,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;AACrC,IAAIC,gBAAgB;AACpB,CAAC,UAAUA,gBAAgB,EAAE;EACzB;AACJ;AACA;AACA;AACA;EACIA,gBAAgB,CAAC,SAAS,CAAC,GAAG,SAAS;EACvC;AACJ;AACA;AACA;AACA;EACIA,gBAAgB,CAAC,SAAS,CAAC,GAAG,SAAS;EACvC;AACJ;AACA;AACA;AACA;EACIA,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO;AACvC,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,MAAMC,YAAY,GAAG;EACjBC,SAASA,CAAA,EAAG;IACR,MAAMC,SAAS,GAAGL,YAAY,CAAC,CAAC;IAChC,IAAIK,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,iBAAiB,CAAC,SAAS,CAAC,EAAE;MAC9F;MACA,OAAOD,SAAS,CAACE,OAAO,CAACC,OAAO;IACpC;IACA,OAAOC,SAAS;EACpB,CAAC;EACDC,SAASA,CAAA,EAAG;IACR,MAAMC,MAAM,GAAG,IAAI,CAACP,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACO,MAAM,EAAE;MACT,OAAO,KAAK;IAChB;IACA,MAAMN,SAAS,GAAGL,YAAY,CAAC,CAAC;IAChC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACK,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACO,WAAW,CAAC,CAAC,MAAM,KAAK,EAAE;MAC3F;MACA,OAAO,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,OAAO,KAAKL,SAAS;IAC9E;IACA,OAAO,IAAI;EACf,CAAC;EACDM,MAAMA,CAACC,OAAO,EAAE;IACZ,MAAML,MAAM,GAAG,IAAI,CAACP,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACO,MAAM,EAAE;MACT;IACJ;IACAA,MAAM,CAACI,MAAM,CAAC;MAAEE,KAAK,EAAED,OAAO,CAACC;IAAM,CAAC,CAAC;EAC3C,CAAC;EACDC,YAAYA,CAACF,OAAO,EAAE;IAClB,MAAML,MAAM,GAAG,IAAI,CAACP,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACO,MAAM,EAAE;MACT;IACJ;IACAA,MAAM,CAACO,YAAY,CAAC;MAAEC,IAAI,EAAEH,OAAO,CAACG;IAAK,CAAC,CAAC;EAC/C,CAAC;EACDC,SAASA,CAAA,EAAG;IACR,IAAI,CAACL,MAAM,CAAC;MAAEE,KAAK,EAAEhB,WAAW,CAACoB;IAAM,CAAC,CAAC;EAC7C,CAAC;EACDC,cAAcA,CAAA,EAAG;IACb,MAAMX,MAAM,GAAG,IAAI,CAACP,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACO,MAAM,EAAE;MACT;IACJ;IACAA,MAAM,CAACW,cAAc,CAAC,CAAC;EAC3B,CAAC;EACDC,gBAAgBA,CAAA,EAAG;IACf,MAAMZ,MAAM,GAAG,IAAI,CAACP,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACO,MAAM,EAAE;MACT;IACJ;IACAA,MAAM,CAACY,gBAAgB,CAAC,CAAC;EAC7B,CAAC;EACDC,YAAYA,CAAA,EAAG;IACX,MAAMb,MAAM,GAAG,IAAI,CAACP,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACO,MAAM,EAAE;MACT;IACJ;IACAA,MAAM,CAACa,YAAY,CAAC,CAAC;EACzB;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC1B,OAAOtB,YAAY,CAACO,SAAS,CAAC,CAAC;AACnC,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMgB,eAAe,GAAGA,CAAA,KAAM;EAC1BD,eAAe,CAAC,CAAC,IAAItB,YAAY,CAACiB,SAAS,CAAC,CAAC;AACjD,CAAC;AACD;AACA;AACA;AACA,MAAMO,oBAAoB,GAAGA,CAAA,KAAM;EAC/BF,eAAe,CAAC,CAAC,IAAItB,YAAY,CAACmB,cAAc,CAAC,CAAC;AACtD,CAAC;AACD;AACA;AACA;AACA,MAAMM,sBAAsB,GAAGA,CAAA,KAAM;EACjCH,eAAe,CAAC,CAAC,IAAItB,YAAY,CAACoB,gBAAgB,CAAC,CAAC;AACxD,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMM,kBAAkB,GAAGA,CAAA,KAAM;EAC7BJ,eAAe,CAAC,CAAC,IAAItB,YAAY,CAACqB,YAAY,CAAC,CAAC;AACpD,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMM,YAAY,GAAId,OAAO,IAAK;EAC9BS,eAAe,CAAC,CAAC,IAAItB,YAAY,CAACY,MAAM,CAACC,OAAO,CAAC;AACrD,CAAC;AAED,SAASf,WAAW,IAAI8B,CAAC,EAAEH,sBAAsB,IAAII,CAAC,EAAEL,oBAAoB,IAAIM,CAAC,EAAEP,eAAe,IAAIQ,CAAC,EAAEJ,YAAY,IAAIK,CAAC,EAAEN,kBAAkB,IAAIO,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}