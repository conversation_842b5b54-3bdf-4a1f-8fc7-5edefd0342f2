{"ast": null, "code": "import { RouterOutlet } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'driver-evaluation-app';\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 0,\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ion-app\")(1, \"ion-content\");\n          i0.ɵɵelement(2, \"router-outlet\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [RouterOutlet, IonicModule, i1.<PERSON>App, i1.IonContent],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["RouterOutlet", "IonicModule", "AppComponent", "constructor", "title", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "i1", "IonApp", "IonContent", "encapsulation"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/src/app/app.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { RouterOutlet } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [RouterOutlet, IonicModule],\n  template: `\n    <ion-app>\n      <ion-content>\n        <router-outlet></router-outlet>\n      </ion-content>\n    </ion-app>\n  `\n})\nexport class AppComponent {\n  title = 'driver-evaluation-app';\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;AAc5C,OAAM,MAAOC,YAAY;EAZzBC,YAAA;IAaE,KAAAC,KAAK,GAAG,uBAAuB;;;;uCADpBF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UANnBN,EADF,CAAAQ,cAAA,cAAS,kBACM;UACXR,EAAA,CAAAS,SAAA,oBAA+B;UAEnCT,EADE,CAAAU,YAAA,EAAc,EACN;;;qBANFlB,YAAY,EAAEC,WAAW,EAAAkB,EAAA,CAAAC,MAAA,EAAAD,EAAA,CAAAE,UAAA;MAAAC,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}