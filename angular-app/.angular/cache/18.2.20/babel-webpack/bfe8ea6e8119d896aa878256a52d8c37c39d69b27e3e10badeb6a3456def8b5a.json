{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { G as GESTURE_CONTROLLER } from './gesture-controller-BTEOs1at.js';\nconst addEventListener = (el,\n// TODO(FW-2832): type\neventName, callback, opts) => {\n  // use event listener options when supported\n  // otherwise it's just a boolean for the \"capture\" arg\n  const listenerOpts = supportsPassive(el) ? {\n    capture: false,\n    passive: !!opts.passive\n  } : false;\n  let add;\n  let remove;\n  if (el['__zone_symbol__addEventListener']) {\n    add = '__zone_symbol__addEventListener';\n    remove = '__zone_symbol__removeEventListener';\n  } else {\n    add = 'addEventListener';\n    remove = 'removeEventListener';\n  }\n  el[add](eventName, callback, listenerOpts);\n  return () => {\n    el[remove](eventName, callback, listenerOpts);\n  };\n};\nconst supportsPassive = node => {\n  if (_sPassive === undefined) {\n    try {\n      const opts = Object.defineProperty({}, 'passive', {\n        get: () => {\n          _sPassive = true;\n        }\n      });\n      node.addEventListener('optsTest', () => {\n        return;\n      }, opts);\n    } catch (e) {\n      _sPassive = false;\n    }\n  }\n  return !!_sPassive;\n};\nlet _sPassive;\nconst MOUSE_WAIT = 2000;\n// TODO(FW-2832): types\nconst createPointerEvents = (el, pointerDown, pointerMove, pointerUp, options) => {\n  let rmTouchStart;\n  let rmTouchMove;\n  let rmTouchEnd;\n  let rmTouchCancel;\n  let rmMouseStart;\n  let rmMouseMove;\n  let rmMouseUp;\n  let lastTouchEvent = 0;\n  const handleTouchStart = ev => {\n    lastTouchEvent = Date.now() + MOUSE_WAIT;\n    if (!pointerDown(ev)) {\n      return;\n    }\n    if (!rmTouchMove && pointerMove) {\n      rmTouchMove = addEventListener(el, 'touchmove', pointerMove, options);\n    }\n    /**\n     * Events are dispatched on the element that is tapped and bubble up to\n     * the reference element in the gesture. In the event that the element this\n     * event was first dispatched on is removed from the DOM, the event will no\n     * longer bubble up to our reference element. This leaves the gesture in an\n     * unusable state. To account for this, the touchend and touchcancel listeners\n     * should be added to the event target so that they still fire even if the target\n     * is removed from the DOM.\n     */\n    if (!rmTouchEnd) {\n      rmTouchEnd = addEventListener(ev.target, 'touchend', handleTouchEnd, options);\n    }\n    if (!rmTouchCancel) {\n      rmTouchCancel = addEventListener(ev.target, 'touchcancel', handleTouchEnd, options);\n    }\n  };\n  const handleMouseDown = ev => {\n    if (lastTouchEvent > Date.now()) {\n      return;\n    }\n    if (!pointerDown(ev)) {\n      return;\n    }\n    if (!rmMouseMove && pointerMove) {\n      rmMouseMove = addEventListener(getDocument(el), 'mousemove', pointerMove, options);\n    }\n    if (!rmMouseUp) {\n      rmMouseUp = addEventListener(getDocument(el), 'mouseup', handleMouseUp, options);\n    }\n  };\n  const handleTouchEnd = ev => {\n    stopTouch();\n    if (pointerUp) {\n      pointerUp(ev);\n    }\n  };\n  const handleMouseUp = ev => {\n    stopMouse();\n    if (pointerUp) {\n      pointerUp(ev);\n    }\n  };\n  const stopTouch = () => {\n    if (rmTouchMove) {\n      rmTouchMove();\n    }\n    if (rmTouchEnd) {\n      rmTouchEnd();\n    }\n    if (rmTouchCancel) {\n      rmTouchCancel();\n    }\n    rmTouchMove = rmTouchEnd = rmTouchCancel = undefined;\n  };\n  const stopMouse = () => {\n    if (rmMouseMove) {\n      rmMouseMove();\n    }\n    if (rmMouseUp) {\n      rmMouseUp();\n    }\n    rmMouseMove = rmMouseUp = undefined;\n  };\n  const stop = () => {\n    stopTouch();\n    stopMouse();\n  };\n  const enable = (isEnabled = true) => {\n    if (!isEnabled) {\n      if (rmTouchStart) {\n        rmTouchStart();\n      }\n      if (rmMouseStart) {\n        rmMouseStart();\n      }\n      rmTouchStart = rmMouseStart = undefined;\n      stop();\n    } else {\n      if (!rmTouchStart) {\n        rmTouchStart = addEventListener(el, 'touchstart', handleTouchStart, options);\n      }\n      if (!rmMouseStart) {\n        rmMouseStart = addEventListener(el, 'mousedown', handleMouseDown, options);\n      }\n    }\n  };\n  const destroy = () => {\n    enable(false);\n    pointerUp = pointerMove = pointerDown = undefined;\n  };\n  return {\n    enable,\n    stop,\n    destroy\n  };\n};\nconst getDocument = node => {\n  return node instanceof Document ? node : node.ownerDocument;\n};\nconst createPanRecognizer = (direction, thresh, maxAngle) => {\n  const radians = maxAngle * (Math.PI / 180);\n  const isDirX = direction === 'x';\n  const maxCosine = Math.cos(radians);\n  const threshold = thresh * thresh;\n  let startX = 0;\n  let startY = 0;\n  let dirty = false;\n  let isPan = 0;\n  return {\n    start(x, y) {\n      startX = x;\n      startY = y;\n      isPan = 0;\n      dirty = true;\n    },\n    detect(x, y) {\n      if (!dirty) {\n        return false;\n      }\n      const deltaX = x - startX;\n      const deltaY = y - startY;\n      const distance = deltaX * deltaX + deltaY * deltaY;\n      if (distance < threshold) {\n        return false;\n      }\n      const hypotenuse = Math.sqrt(distance);\n      const cosine = (isDirX ? deltaX : deltaY) / hypotenuse;\n      if (cosine > maxCosine) {\n        isPan = 1;\n      } else if (cosine < -maxCosine) {\n        isPan = -1;\n      } else {\n        isPan = 0;\n      }\n      dirty = false;\n      return true;\n    },\n    isGesture() {\n      return isPan !== 0;\n    },\n    getDirection() {\n      return isPan;\n    }\n  };\n};\n\n// TODO(FW-2832): types\nconst createGesture = config => {\n  let hasCapturedPan = false;\n  let hasStartedPan = false;\n  let hasFiredStart = true;\n  let isMoveQueued = false;\n  const finalConfig = Object.assign({\n    disableScroll: false,\n    direction: 'x',\n    gesturePriority: 0,\n    passive: true,\n    maxAngle: 40,\n    threshold: 10\n  }, config);\n  const canStart = finalConfig.canStart;\n  const onWillStart = finalConfig.onWillStart;\n  const onStart = finalConfig.onStart;\n  const onEnd = finalConfig.onEnd;\n  const notCaptured = finalConfig.notCaptured;\n  const onMove = finalConfig.onMove;\n  const threshold = finalConfig.threshold;\n  const passive = finalConfig.passive;\n  const blurOnStart = finalConfig.blurOnStart;\n  const detail = {\n    type: 'pan',\n    startX: 0,\n    startY: 0,\n    startTime: 0,\n    currentX: 0,\n    currentY: 0,\n    velocityX: 0,\n    velocityY: 0,\n    deltaX: 0,\n    deltaY: 0,\n    currentTime: 0,\n    event: undefined,\n    data: undefined\n  };\n  const pan = createPanRecognizer(finalConfig.direction, finalConfig.threshold, finalConfig.maxAngle);\n  const gesture = GESTURE_CONTROLLER.createGesture({\n    name: config.gestureName,\n    priority: config.gesturePriority,\n    disableScroll: config.disableScroll\n  });\n  const pointerDown = ev => {\n    const timeStamp = now(ev);\n    if (hasStartedPan || !hasFiredStart) {\n      return false;\n    }\n    updateDetail(ev, detail);\n    detail.startX = detail.currentX;\n    detail.startY = detail.currentY;\n    detail.startTime = detail.currentTime = timeStamp;\n    detail.velocityX = detail.velocityY = detail.deltaX = detail.deltaY = 0;\n    detail.event = ev;\n    // Check if gesture can start\n    if (canStart && canStart(detail) === false) {\n      return false;\n    }\n    // Release fallback\n    gesture.release();\n    // Start gesture\n    if (!gesture.start()) {\n      return false;\n    }\n    hasStartedPan = true;\n    if (threshold === 0) {\n      return tryToCapturePan();\n    }\n    pan.start(detail.startX, detail.startY);\n    return true;\n  };\n  const pointerMove = ev => {\n    // fast path, if gesture is currently captured\n    // do minimum job to get user-land even dispatched\n    if (hasCapturedPan) {\n      if (!isMoveQueued && hasFiredStart) {\n        isMoveQueued = true;\n        calcGestureData(detail, ev);\n        requestAnimationFrame(fireOnMove);\n      }\n      return;\n    }\n    // gesture is currently being detected\n    calcGestureData(detail, ev);\n    if (pan.detect(detail.currentX, detail.currentY)) {\n      if (!pan.isGesture() || !tryToCapturePan()) {\n        abortGesture();\n      }\n    }\n  };\n  const fireOnMove = () => {\n    // Since fireOnMove is called inside a RAF, onEnd() might be called,\n    // we must double check hasCapturedPan\n    if (!hasCapturedPan) {\n      return;\n    }\n    isMoveQueued = false;\n    if (onMove) {\n      onMove(detail);\n    }\n  };\n  const tryToCapturePan = () => {\n    if (!gesture.capture()) {\n      return false;\n    }\n    hasCapturedPan = true;\n    hasFiredStart = false;\n    // reset start position since the real user-land event starts here\n    // If the pan detector threshold is big, not resetting the start position\n    // will cause a jump in the animation equal to the detector threshold.\n    // the array of positions used to calculate the gesture velocity does not\n    // need to be cleaned, more points in the positions array always results in a\n    // more accurate value of the velocity.\n    detail.startX = detail.currentX;\n    detail.startY = detail.currentY;\n    detail.startTime = detail.currentTime;\n    if (onWillStart) {\n      onWillStart(detail).then(fireOnStart);\n    } else {\n      fireOnStart();\n    }\n    return true;\n  };\n  const blurActiveElement = () => {\n    if (typeof document !== 'undefined') {\n      const activeElement = document.activeElement;\n      if (activeElement === null || activeElement === void 0 ? void 0 : activeElement.blur) {\n        activeElement.blur();\n      }\n    }\n  };\n  const fireOnStart = () => {\n    if (blurOnStart) {\n      blurActiveElement();\n    }\n    if (onStart) {\n      onStart(detail);\n    }\n    hasFiredStart = true;\n  };\n  const reset = () => {\n    hasCapturedPan = false;\n    hasStartedPan = false;\n    isMoveQueued = false;\n    hasFiredStart = true;\n    gesture.release();\n  };\n  // END *************************\n  const pointerUp = ev => {\n    const tmpHasCaptured = hasCapturedPan;\n    const tmpHasFiredStart = hasFiredStart;\n    reset();\n    if (!tmpHasFiredStart) {\n      return;\n    }\n    calcGestureData(detail, ev);\n    // Try to capture press\n    if (tmpHasCaptured) {\n      if (onEnd) {\n        onEnd(detail);\n      }\n      return;\n    }\n    // Not captured any event\n    if (notCaptured) {\n      notCaptured(detail);\n    }\n  };\n  const pointerEvents = createPointerEvents(finalConfig.el, pointerDown, pointerMove, pointerUp, {\n    passive\n  });\n  const abortGesture = () => {\n    reset();\n    pointerEvents.stop();\n    if (notCaptured) {\n      notCaptured(detail);\n    }\n  };\n  return {\n    enable(enable = true) {\n      if (!enable) {\n        if (hasCapturedPan) {\n          pointerUp(undefined);\n        }\n        reset();\n      }\n      pointerEvents.enable(enable);\n    },\n    destroy() {\n      gesture.destroy();\n      pointerEvents.destroy();\n    }\n  };\n};\nconst calcGestureData = (detail, ev) => {\n  if (!ev) {\n    return;\n  }\n  const prevX = detail.currentX;\n  const prevY = detail.currentY;\n  const prevT = detail.currentTime;\n  updateDetail(ev, detail);\n  const currentX = detail.currentX;\n  const currentY = detail.currentY;\n  const timestamp = detail.currentTime = now(ev);\n  const timeDelta = timestamp - prevT;\n  if (timeDelta > 0 && timeDelta < 100) {\n    const velocityX = (currentX - prevX) / timeDelta;\n    const velocityY = (currentY - prevY) / timeDelta;\n    detail.velocityX = velocityX * 0.7 + detail.velocityX * 0.3;\n    detail.velocityY = velocityY * 0.7 + detail.velocityY * 0.3;\n  }\n  detail.deltaX = currentX - detail.startX;\n  detail.deltaY = currentY - detail.startY;\n  detail.event = ev;\n};\nconst updateDetail = (ev, detail) => {\n  // get X coordinates for either a mouse click\n  // or a touch depending on the given event\n  let x = 0;\n  let y = 0;\n  if (ev) {\n    const changedTouches = ev.changedTouches;\n    if (changedTouches && changedTouches.length > 0) {\n      const touch = changedTouches[0];\n      x = touch.clientX;\n      y = touch.clientY;\n    } else if (ev.pageX !== undefined) {\n      x = ev.pageX;\n      y = ev.pageY;\n    }\n  }\n  detail.currentX = x;\n  detail.currentY = y;\n};\nconst now = ev => {\n  return ev.timeStamp || Date.now();\n};\nexport { GESTURE_CONTROLLER, createGesture };", "map": {"version": 3, "names": ["G", "GESTURE_CONTROLLER", "addEventListener", "el", "eventName", "callback", "opts", "listenerOpts", "supportsPassive", "capture", "passive", "add", "remove", "node", "_sPassive", "undefined", "Object", "defineProperty", "get", "e", "MOUSE_WAIT", "createPointerEvents", "pointerDown", "pointer<PERSON><PERSON>", "pointerUp", "options", "rmTouchStart", "rmTouchMove", "rmTouchEnd", "rmTouchCancel", "rmMouseStart", "rmMouseMove", "rmMouseUp", "lastTouchEvent", "handleTouchStart", "ev", "Date", "now", "target", "handleTouchEnd", "handleMouseDown", "getDocument", "handleMouseUp", "stopTouch", "stopMouse", "stop", "enable", "isEnabled", "destroy", "Document", "ownerDocument", "createPanRecognizer", "direction", "thresh", "maxAngle", "radians", "Math", "PI", "isDirX", "maxCosine", "cos", "threshold", "startX", "startY", "dirty", "isPan", "start", "x", "y", "detect", "deltaX", "deltaY", "distance", "hypotenuse", "sqrt", "cosine", "isGesture", "getDirection", "createGesture", "config", "hasCapturedPan", "hasStartedPan", "hasFiredStart", "isMoveQueued", "finalConfig", "assign", "disableScroll", "gesturePriority", "canStart", "onWillStart", "onStart", "onEnd", "notCaptured", "onMove", "blurOnStart", "detail", "type", "startTime", "currentX", "currentY", "velocityX", "velocityY", "currentTime", "event", "data", "pan", "gesture", "name", "<PERSON><PERSON><PERSON>", "priority", "timeStamp", "updateDetail", "release", "tryToCapturePan", "calcGestureData", "requestAnimationFrame", "fireOnMove", "abortGesture", "then", "fireOnStart", "blurActiveElement", "document", "activeElement", "blur", "reset", "tmpHasCaptured", "tmpHasFiredStart", "pointerEvents", "prevX", "prevY", "prevT", "timestamp", "<PERSON><PERSON><PERSON><PERSON>", "changedTouches", "length", "touch", "clientX", "clientY", "pageX", "pageY"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@ionic/core/dist/esm/index-CfgBF1SE.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { G as GESTURE_CONTROLLER } from './gesture-controller-BTEOs1at.js';\n\nconst addEventListener = (el, // TODO(FW-2832): type\neventName, callback, opts) => {\n    // use event listener options when supported\n    // otherwise it's just a boolean for the \"capture\" arg\n    const listenerOpts = supportsPassive(el)\n        ? {\n            capture: false,\n            passive: !!opts.passive,\n        }\n        : false;\n    let add;\n    let remove;\n    if (el['__zone_symbol__addEventListener']) {\n        add = '__zone_symbol__addEventListener';\n        remove = '__zone_symbol__removeEventListener';\n    }\n    else {\n        add = 'addEventListener';\n        remove = 'removeEventListener';\n    }\n    el[add](eventName, callback, listenerOpts);\n    return () => {\n        el[remove](eventName, callback, listenerOpts);\n    };\n};\nconst supportsPassive = (node) => {\n    if (_sPassive === undefined) {\n        try {\n            const opts = Object.defineProperty({}, 'passive', {\n                get: () => {\n                    _sPassive = true;\n                },\n            });\n            node.addEventListener('optsTest', () => {\n                return;\n            }, opts);\n        }\n        catch (e) {\n            _sPassive = false;\n        }\n    }\n    return !!_sPassive;\n};\nlet _sPassive;\n\nconst MOUSE_WAIT = 2000;\n// TODO(FW-2832): types\nconst createPointerEvents = (el, pointerDown, pointerMove, pointerUp, options) => {\n    let rmTouchStart;\n    let rmTouchMove;\n    let rmTouchEnd;\n    let rmTouchCancel;\n    let rmMouseStart;\n    let rmMouseMove;\n    let rmMouseUp;\n    let lastTouchEvent = 0;\n    const handleTouchStart = (ev) => {\n        lastTouchEvent = Date.now() + MOUSE_WAIT;\n        if (!pointerDown(ev)) {\n            return;\n        }\n        if (!rmTouchMove && pointerMove) {\n            rmTouchMove = addEventListener(el, 'touchmove', pointerMove, options);\n        }\n        /**\n         * Events are dispatched on the element that is tapped and bubble up to\n         * the reference element in the gesture. In the event that the element this\n         * event was first dispatched on is removed from the DOM, the event will no\n         * longer bubble up to our reference element. This leaves the gesture in an\n         * unusable state. To account for this, the touchend and touchcancel listeners\n         * should be added to the event target so that they still fire even if the target\n         * is removed from the DOM.\n         */\n        if (!rmTouchEnd) {\n            rmTouchEnd = addEventListener(ev.target, 'touchend', handleTouchEnd, options);\n        }\n        if (!rmTouchCancel) {\n            rmTouchCancel = addEventListener(ev.target, 'touchcancel', handleTouchEnd, options);\n        }\n    };\n    const handleMouseDown = (ev) => {\n        if (lastTouchEvent > Date.now()) {\n            return;\n        }\n        if (!pointerDown(ev)) {\n            return;\n        }\n        if (!rmMouseMove && pointerMove) {\n            rmMouseMove = addEventListener(getDocument(el), 'mousemove', pointerMove, options);\n        }\n        if (!rmMouseUp) {\n            rmMouseUp = addEventListener(getDocument(el), 'mouseup', handleMouseUp, options);\n        }\n    };\n    const handleTouchEnd = (ev) => {\n        stopTouch();\n        if (pointerUp) {\n            pointerUp(ev);\n        }\n    };\n    const handleMouseUp = (ev) => {\n        stopMouse();\n        if (pointerUp) {\n            pointerUp(ev);\n        }\n    };\n    const stopTouch = () => {\n        if (rmTouchMove) {\n            rmTouchMove();\n        }\n        if (rmTouchEnd) {\n            rmTouchEnd();\n        }\n        if (rmTouchCancel) {\n            rmTouchCancel();\n        }\n        rmTouchMove = rmTouchEnd = rmTouchCancel = undefined;\n    };\n    const stopMouse = () => {\n        if (rmMouseMove) {\n            rmMouseMove();\n        }\n        if (rmMouseUp) {\n            rmMouseUp();\n        }\n        rmMouseMove = rmMouseUp = undefined;\n    };\n    const stop = () => {\n        stopTouch();\n        stopMouse();\n    };\n    const enable = (isEnabled = true) => {\n        if (!isEnabled) {\n            if (rmTouchStart) {\n                rmTouchStart();\n            }\n            if (rmMouseStart) {\n                rmMouseStart();\n            }\n            rmTouchStart = rmMouseStart = undefined;\n            stop();\n        }\n        else {\n            if (!rmTouchStart) {\n                rmTouchStart = addEventListener(el, 'touchstart', handleTouchStart, options);\n            }\n            if (!rmMouseStart) {\n                rmMouseStart = addEventListener(el, 'mousedown', handleMouseDown, options);\n            }\n        }\n    };\n    const destroy = () => {\n        enable(false);\n        pointerUp = pointerMove = pointerDown = undefined;\n    };\n    return {\n        enable,\n        stop,\n        destroy,\n    };\n};\nconst getDocument = (node) => {\n    return node instanceof Document ? node : node.ownerDocument;\n};\n\nconst createPanRecognizer = (direction, thresh, maxAngle) => {\n    const radians = maxAngle * (Math.PI / 180);\n    const isDirX = direction === 'x';\n    const maxCosine = Math.cos(radians);\n    const threshold = thresh * thresh;\n    let startX = 0;\n    let startY = 0;\n    let dirty = false;\n    let isPan = 0;\n    return {\n        start(x, y) {\n            startX = x;\n            startY = y;\n            isPan = 0;\n            dirty = true;\n        },\n        detect(x, y) {\n            if (!dirty) {\n                return false;\n            }\n            const deltaX = x - startX;\n            const deltaY = y - startY;\n            const distance = deltaX * deltaX + deltaY * deltaY;\n            if (distance < threshold) {\n                return false;\n            }\n            const hypotenuse = Math.sqrt(distance);\n            const cosine = (isDirX ? deltaX : deltaY) / hypotenuse;\n            if (cosine > maxCosine) {\n                isPan = 1;\n            }\n            else if (cosine < -maxCosine) {\n                isPan = -1;\n            }\n            else {\n                isPan = 0;\n            }\n            dirty = false;\n            return true;\n        },\n        isGesture() {\n            return isPan !== 0;\n        },\n        getDirection() {\n            return isPan;\n        },\n    };\n};\n\n// TODO(FW-2832): types\nconst createGesture = (config) => {\n    let hasCapturedPan = false;\n    let hasStartedPan = false;\n    let hasFiredStart = true;\n    let isMoveQueued = false;\n    const finalConfig = Object.assign({ disableScroll: false, direction: 'x', gesturePriority: 0, passive: true, maxAngle: 40, threshold: 10 }, config);\n    const canStart = finalConfig.canStart;\n    const onWillStart = finalConfig.onWillStart;\n    const onStart = finalConfig.onStart;\n    const onEnd = finalConfig.onEnd;\n    const notCaptured = finalConfig.notCaptured;\n    const onMove = finalConfig.onMove;\n    const threshold = finalConfig.threshold;\n    const passive = finalConfig.passive;\n    const blurOnStart = finalConfig.blurOnStart;\n    const detail = {\n        type: 'pan',\n        startX: 0,\n        startY: 0,\n        startTime: 0,\n        currentX: 0,\n        currentY: 0,\n        velocityX: 0,\n        velocityY: 0,\n        deltaX: 0,\n        deltaY: 0,\n        currentTime: 0,\n        event: undefined,\n        data: undefined,\n    };\n    const pan = createPanRecognizer(finalConfig.direction, finalConfig.threshold, finalConfig.maxAngle);\n    const gesture = GESTURE_CONTROLLER.createGesture({\n        name: config.gestureName,\n        priority: config.gesturePriority,\n        disableScroll: config.disableScroll,\n    });\n    const pointerDown = (ev) => {\n        const timeStamp = now(ev);\n        if (hasStartedPan || !hasFiredStart) {\n            return false;\n        }\n        updateDetail(ev, detail);\n        detail.startX = detail.currentX;\n        detail.startY = detail.currentY;\n        detail.startTime = detail.currentTime = timeStamp;\n        detail.velocityX = detail.velocityY = detail.deltaX = detail.deltaY = 0;\n        detail.event = ev;\n        // Check if gesture can start\n        if (canStart && canStart(detail) === false) {\n            return false;\n        }\n        // Release fallback\n        gesture.release();\n        // Start gesture\n        if (!gesture.start()) {\n            return false;\n        }\n        hasStartedPan = true;\n        if (threshold === 0) {\n            return tryToCapturePan();\n        }\n        pan.start(detail.startX, detail.startY);\n        return true;\n    };\n    const pointerMove = (ev) => {\n        // fast path, if gesture is currently captured\n        // do minimum job to get user-land even dispatched\n        if (hasCapturedPan) {\n            if (!isMoveQueued && hasFiredStart) {\n                isMoveQueued = true;\n                calcGestureData(detail, ev);\n                requestAnimationFrame(fireOnMove);\n            }\n            return;\n        }\n        // gesture is currently being detected\n        calcGestureData(detail, ev);\n        if (pan.detect(detail.currentX, detail.currentY)) {\n            if (!pan.isGesture() || !tryToCapturePan()) {\n                abortGesture();\n            }\n        }\n    };\n    const fireOnMove = () => {\n        // Since fireOnMove is called inside a RAF, onEnd() might be called,\n        // we must double check hasCapturedPan\n        if (!hasCapturedPan) {\n            return;\n        }\n        isMoveQueued = false;\n        if (onMove) {\n            onMove(detail);\n        }\n    };\n    const tryToCapturePan = () => {\n        if (!gesture.capture()) {\n            return false;\n        }\n        hasCapturedPan = true;\n        hasFiredStart = false;\n        // reset start position since the real user-land event starts here\n        // If the pan detector threshold is big, not resetting the start position\n        // will cause a jump in the animation equal to the detector threshold.\n        // the array of positions used to calculate the gesture velocity does not\n        // need to be cleaned, more points in the positions array always results in a\n        // more accurate value of the velocity.\n        detail.startX = detail.currentX;\n        detail.startY = detail.currentY;\n        detail.startTime = detail.currentTime;\n        if (onWillStart) {\n            onWillStart(detail).then(fireOnStart);\n        }\n        else {\n            fireOnStart();\n        }\n        return true;\n    };\n    const blurActiveElement = () => {\n        if (typeof document !== 'undefined') {\n            const activeElement = document.activeElement;\n            if (activeElement === null || activeElement === void 0 ? void 0 : activeElement.blur) {\n                activeElement.blur();\n            }\n        }\n    };\n    const fireOnStart = () => {\n        if (blurOnStart) {\n            blurActiveElement();\n        }\n        if (onStart) {\n            onStart(detail);\n        }\n        hasFiredStart = true;\n    };\n    const reset = () => {\n        hasCapturedPan = false;\n        hasStartedPan = false;\n        isMoveQueued = false;\n        hasFiredStart = true;\n        gesture.release();\n    };\n    // END *************************\n    const pointerUp = (ev) => {\n        const tmpHasCaptured = hasCapturedPan;\n        const tmpHasFiredStart = hasFiredStart;\n        reset();\n        if (!tmpHasFiredStart) {\n            return;\n        }\n        calcGestureData(detail, ev);\n        // Try to capture press\n        if (tmpHasCaptured) {\n            if (onEnd) {\n                onEnd(detail);\n            }\n            return;\n        }\n        // Not captured any event\n        if (notCaptured) {\n            notCaptured(detail);\n        }\n    };\n    const pointerEvents = createPointerEvents(finalConfig.el, pointerDown, pointerMove, pointerUp, {\n        passive,\n    });\n    const abortGesture = () => {\n        reset();\n        pointerEvents.stop();\n        if (notCaptured) {\n            notCaptured(detail);\n        }\n    };\n    return {\n        enable(enable = true) {\n            if (!enable) {\n                if (hasCapturedPan) {\n                    pointerUp(undefined);\n                }\n                reset();\n            }\n            pointerEvents.enable(enable);\n        },\n        destroy() {\n            gesture.destroy();\n            pointerEvents.destroy();\n        },\n    };\n};\nconst calcGestureData = (detail, ev) => {\n    if (!ev) {\n        return;\n    }\n    const prevX = detail.currentX;\n    const prevY = detail.currentY;\n    const prevT = detail.currentTime;\n    updateDetail(ev, detail);\n    const currentX = detail.currentX;\n    const currentY = detail.currentY;\n    const timestamp = (detail.currentTime = now(ev));\n    const timeDelta = timestamp - prevT;\n    if (timeDelta > 0 && timeDelta < 100) {\n        const velocityX = (currentX - prevX) / timeDelta;\n        const velocityY = (currentY - prevY) / timeDelta;\n        detail.velocityX = velocityX * 0.7 + detail.velocityX * 0.3;\n        detail.velocityY = velocityY * 0.7 + detail.velocityY * 0.3;\n    }\n    detail.deltaX = currentX - detail.startX;\n    detail.deltaY = currentY - detail.startY;\n    detail.event = ev;\n};\nconst updateDetail = (ev, detail) => {\n    // get X coordinates for either a mouse click\n    // or a touch depending on the given event\n    let x = 0;\n    let y = 0;\n    if (ev) {\n        const changedTouches = ev.changedTouches;\n        if (changedTouches && changedTouches.length > 0) {\n            const touch = changedTouches[0];\n            x = touch.clientX;\n            y = touch.clientY;\n        }\n        else if (ev.pageX !== undefined) {\n            x = ev.pageX;\n            y = ev.pageY;\n        }\n    }\n    detail.currentX = x;\n    detail.currentY = y;\n};\nconst now = (ev) => {\n    return ev.timeStamp || Date.now();\n};\n\nexport { GESTURE_CONTROLLER, createGesture };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,kBAAkB,QAAQ,kCAAkC;AAE1E,MAAMC,gBAAgB,GAAGA,CAACC,EAAE;AAAE;AAC9BC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,KAAK;EAC1B;EACA;EACA,MAAMC,YAAY,GAAGC,eAAe,CAACL,EAAE,CAAC,GAClC;IACEM,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,CAAC,CAACJ,IAAI,CAACI;EACpB,CAAC,GACC,KAAK;EACX,IAAIC,GAAG;EACP,IAAIC,MAAM;EACV,IAAIT,EAAE,CAAC,iCAAiC,CAAC,EAAE;IACvCQ,GAAG,GAAG,iCAAiC;IACvCC,MAAM,GAAG,oCAAoC;EACjD,CAAC,MACI;IACDD,GAAG,GAAG,kBAAkB;IACxBC,MAAM,GAAG,qBAAqB;EAClC;EACAT,EAAE,CAACQ,GAAG,CAAC,CAACP,SAAS,EAAEC,QAAQ,EAAEE,YAAY,CAAC;EAC1C,OAAO,MAAM;IACTJ,EAAE,CAACS,MAAM,CAAC,CAACR,SAAS,EAAEC,QAAQ,EAAEE,YAAY,CAAC;EACjD,CAAC;AACL,CAAC;AACD,MAAMC,eAAe,GAAIK,IAAI,IAAK;EAC9B,IAAIC,SAAS,KAAKC,SAAS,EAAE;IACzB,IAAI;MACA,MAAMT,IAAI,GAAGU,MAAM,CAACC,cAAc,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE;QAC9CC,GAAG,EAAEA,CAAA,KAAM;UACPJ,SAAS,GAAG,IAAI;QACpB;MACJ,CAAC,CAAC;MACFD,IAAI,CAACX,gBAAgB,CAAC,UAAU,EAAE,MAAM;QACpC;MACJ,CAAC,EAAEI,IAAI,CAAC;IACZ,CAAC,CACD,OAAOa,CAAC,EAAE;MACNL,SAAS,GAAG,KAAK;IACrB;EACJ;EACA,OAAO,CAAC,CAACA,SAAS;AACtB,CAAC;AACD,IAAIA,SAAS;AAEb,MAAMM,UAAU,GAAG,IAAI;AACvB;AACA,MAAMC,mBAAmB,GAAGA,CAAClB,EAAE,EAAEmB,WAAW,EAAEC,WAAW,EAAEC,SAAS,EAAEC,OAAO,KAAK;EAC9E,IAAIC,YAAY;EAChB,IAAIC,WAAW;EACf,IAAIC,UAAU;EACd,IAAIC,aAAa;EACjB,IAAIC,YAAY;EAChB,IAAIC,WAAW;EACf,IAAIC,SAAS;EACb,IAAIC,cAAc,GAAG,CAAC;EACtB,MAAMC,gBAAgB,GAAIC,EAAE,IAAK;IAC7BF,cAAc,GAAGG,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGjB,UAAU;IACxC,IAAI,CAACE,WAAW,CAACa,EAAE,CAAC,EAAE;MAClB;IACJ;IACA,IAAI,CAACR,WAAW,IAAIJ,WAAW,EAAE;MAC7BI,WAAW,GAAGzB,gBAAgB,CAACC,EAAE,EAAE,WAAW,EAAEoB,WAAW,EAAEE,OAAO,CAAC;IACzE;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACG,UAAU,EAAE;MACbA,UAAU,GAAG1B,gBAAgB,CAACiC,EAAE,CAACG,MAAM,EAAE,UAAU,EAAEC,cAAc,EAAEd,OAAO,CAAC;IACjF;IACA,IAAI,CAACI,aAAa,EAAE;MAChBA,aAAa,GAAG3B,gBAAgB,CAACiC,EAAE,CAACG,MAAM,EAAE,aAAa,EAAEC,cAAc,EAAEd,OAAO,CAAC;IACvF;EACJ,CAAC;EACD,MAAMe,eAAe,GAAIL,EAAE,IAAK;IAC5B,IAAIF,cAAc,GAAGG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MAC7B;IACJ;IACA,IAAI,CAACf,WAAW,CAACa,EAAE,CAAC,EAAE;MAClB;IACJ;IACA,IAAI,CAACJ,WAAW,IAAIR,WAAW,EAAE;MAC7BQ,WAAW,GAAG7B,gBAAgB,CAACuC,WAAW,CAACtC,EAAE,CAAC,EAAE,WAAW,EAAEoB,WAAW,EAAEE,OAAO,CAAC;IACtF;IACA,IAAI,CAACO,SAAS,EAAE;MACZA,SAAS,GAAG9B,gBAAgB,CAACuC,WAAW,CAACtC,EAAE,CAAC,EAAE,SAAS,EAAEuC,aAAa,EAAEjB,OAAO,CAAC;IACpF;EACJ,CAAC;EACD,MAAMc,cAAc,GAAIJ,EAAE,IAAK;IAC3BQ,SAAS,CAAC,CAAC;IACX,IAAInB,SAAS,EAAE;MACXA,SAAS,CAACW,EAAE,CAAC;IACjB;EACJ,CAAC;EACD,MAAMO,aAAa,GAAIP,EAAE,IAAK;IAC1BS,SAAS,CAAC,CAAC;IACX,IAAIpB,SAAS,EAAE;MACXA,SAAS,CAACW,EAAE,CAAC;IACjB;EACJ,CAAC;EACD,MAAMQ,SAAS,GAAGA,CAAA,KAAM;IACpB,IAAIhB,WAAW,EAAE;MACbA,WAAW,CAAC,CAAC;IACjB;IACA,IAAIC,UAAU,EAAE;MACZA,UAAU,CAAC,CAAC;IAChB;IACA,IAAIC,aAAa,EAAE;MACfA,aAAa,CAAC,CAAC;IACnB;IACAF,WAAW,GAAGC,UAAU,GAAGC,aAAa,GAAGd,SAAS;EACxD,CAAC;EACD,MAAM6B,SAAS,GAAGA,CAAA,KAAM;IACpB,IAAIb,WAAW,EAAE;MACbA,WAAW,CAAC,CAAC;IACjB;IACA,IAAIC,SAAS,EAAE;MACXA,SAAS,CAAC,CAAC;IACf;IACAD,WAAW,GAAGC,SAAS,GAAGjB,SAAS;EACvC,CAAC;EACD,MAAM8B,IAAI,GAAGA,CAAA,KAAM;IACfF,SAAS,CAAC,CAAC;IACXC,SAAS,CAAC,CAAC;EACf,CAAC;EACD,MAAME,MAAM,GAAGA,CAACC,SAAS,GAAG,IAAI,KAAK;IACjC,IAAI,CAACA,SAAS,EAAE;MACZ,IAAIrB,YAAY,EAAE;QACdA,YAAY,CAAC,CAAC;MAClB;MACA,IAAII,YAAY,EAAE;QACdA,YAAY,CAAC,CAAC;MAClB;MACAJ,YAAY,GAAGI,YAAY,GAAGf,SAAS;MACvC8B,IAAI,CAAC,CAAC;IACV,CAAC,MACI;MACD,IAAI,CAACnB,YAAY,EAAE;QACfA,YAAY,GAAGxB,gBAAgB,CAACC,EAAE,EAAE,YAAY,EAAE+B,gBAAgB,EAAET,OAAO,CAAC;MAChF;MACA,IAAI,CAACK,YAAY,EAAE;QACfA,YAAY,GAAG5B,gBAAgB,CAACC,EAAE,EAAE,WAAW,EAAEqC,eAAe,EAAEf,OAAO,CAAC;MAC9E;IACJ;EACJ,CAAC;EACD,MAAMuB,OAAO,GAAGA,CAAA,KAAM;IAClBF,MAAM,CAAC,KAAK,CAAC;IACbtB,SAAS,GAAGD,WAAW,GAAGD,WAAW,GAAGP,SAAS;EACrD,CAAC;EACD,OAAO;IACH+B,MAAM;IACND,IAAI;IACJG;EACJ,CAAC;AACL,CAAC;AACD,MAAMP,WAAW,GAAI5B,IAAI,IAAK;EAC1B,OAAOA,IAAI,YAAYoC,QAAQ,GAAGpC,IAAI,GAAGA,IAAI,CAACqC,aAAa;AAC/D,CAAC;AAED,MAAMC,mBAAmB,GAAGA,CAACC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,KAAK;EACzD,MAAMC,OAAO,GAAGD,QAAQ,IAAIE,IAAI,CAACC,EAAE,GAAG,GAAG,CAAC;EAC1C,MAAMC,MAAM,GAAGN,SAAS,KAAK,GAAG;EAChC,MAAMO,SAAS,GAAGH,IAAI,CAACI,GAAG,CAACL,OAAO,CAAC;EACnC,MAAMM,SAAS,GAAGR,MAAM,GAAGA,MAAM;EACjC,IAAIS,MAAM,GAAG,CAAC;EACd,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,KAAK,GAAG,KAAK;EACjB,IAAIC,KAAK,GAAG,CAAC;EACb,OAAO;IACHC,KAAKA,CAACC,CAAC,EAAEC,CAAC,EAAE;MACRN,MAAM,GAAGK,CAAC;MACVJ,MAAM,GAAGK,CAAC;MACVH,KAAK,GAAG,CAAC;MACTD,KAAK,GAAG,IAAI;IAChB,CAAC;IACDK,MAAMA,CAACF,CAAC,EAAEC,CAAC,EAAE;MACT,IAAI,CAACJ,KAAK,EAAE;QACR,OAAO,KAAK;MAChB;MACA,MAAMM,MAAM,GAAGH,CAAC,GAAGL,MAAM;MACzB,MAAMS,MAAM,GAAGH,CAAC,GAAGL,MAAM;MACzB,MAAMS,QAAQ,GAAGF,MAAM,GAAGA,MAAM,GAAGC,MAAM,GAAGA,MAAM;MAClD,IAAIC,QAAQ,GAAGX,SAAS,EAAE;QACtB,OAAO,KAAK;MAChB;MACA,MAAMY,UAAU,GAAGjB,IAAI,CAACkB,IAAI,CAACF,QAAQ,CAAC;MACtC,MAAMG,MAAM,GAAG,CAACjB,MAAM,GAAGY,MAAM,GAAGC,MAAM,IAAIE,UAAU;MACtD,IAAIE,MAAM,GAAGhB,SAAS,EAAE;QACpBM,KAAK,GAAG,CAAC;MACb,CAAC,MACI,IAAIU,MAAM,GAAG,CAAChB,SAAS,EAAE;QAC1BM,KAAK,GAAG,CAAC,CAAC;MACd,CAAC,MACI;QACDA,KAAK,GAAG,CAAC;MACb;MACAD,KAAK,GAAG,KAAK;MACb,OAAO,IAAI;IACf,CAAC;IACDY,SAASA,CAAA,EAAG;MACR,OAAOX,KAAK,KAAK,CAAC;IACtB,CAAC;IACDY,YAAYA,CAAA,EAAG;MACX,OAAOZ,KAAK;IAChB;EACJ,CAAC;AACL,CAAC;;AAED;AACA,MAAMa,aAAa,GAAIC,MAAM,IAAK;EAC9B,IAAIC,cAAc,GAAG,KAAK;EAC1B,IAAIC,aAAa,GAAG,KAAK;EACzB,IAAIC,aAAa,GAAG,IAAI;EACxB,IAAIC,YAAY,GAAG,KAAK;EACxB,MAAMC,WAAW,GAAGpE,MAAM,CAACqE,MAAM,CAAC;IAAEC,aAAa,EAAE,KAAK;IAAElC,SAAS,EAAE,GAAG;IAAEmC,eAAe,EAAE,CAAC;IAAE7E,OAAO,EAAE,IAAI;IAAE4C,QAAQ,EAAE,EAAE;IAAEO,SAAS,EAAE;EAAG,CAAC,EAAEkB,MAAM,CAAC;EACnJ,MAAMS,QAAQ,GAAGJ,WAAW,CAACI,QAAQ;EACrC,MAAMC,WAAW,GAAGL,WAAW,CAACK,WAAW;EAC3C,MAAMC,OAAO,GAAGN,WAAW,CAACM,OAAO;EACnC,MAAMC,KAAK,GAAGP,WAAW,CAACO,KAAK;EAC/B,MAAMC,WAAW,GAAGR,WAAW,CAACQ,WAAW;EAC3C,MAAMC,MAAM,GAAGT,WAAW,CAACS,MAAM;EACjC,MAAMhC,SAAS,GAAGuB,WAAW,CAACvB,SAAS;EACvC,MAAMnD,OAAO,GAAG0E,WAAW,CAAC1E,OAAO;EACnC,MAAMoF,WAAW,GAAGV,WAAW,CAACU,WAAW;EAC3C,MAAMC,MAAM,GAAG;IACXC,IAAI,EAAE,KAAK;IACXlC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,CAAC;IACTkC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZ/B,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,CAAC;IACT+B,WAAW,EAAE,CAAC;IACdC,KAAK,EAAExF,SAAS;IAChByF,IAAI,EAAEzF;EACV,CAAC;EACD,MAAM0F,GAAG,GAAGtD,mBAAmB,CAACiC,WAAW,CAAChC,SAAS,EAAEgC,WAAW,CAACvB,SAAS,EAAEuB,WAAW,CAAC9B,QAAQ,CAAC;EACnG,MAAMoD,OAAO,GAAGzG,kBAAkB,CAAC6E,aAAa,CAAC;IAC7C6B,IAAI,EAAE5B,MAAM,CAAC6B,WAAW;IACxBC,QAAQ,EAAE9B,MAAM,CAACQ,eAAe;IAChCD,aAAa,EAAEP,MAAM,CAACO;EAC1B,CAAC,CAAC;EACF,MAAMhE,WAAW,GAAIa,EAAE,IAAK;IACxB,MAAM2E,SAAS,GAAGzE,GAAG,CAACF,EAAE,CAAC;IACzB,IAAI8C,aAAa,IAAI,CAACC,aAAa,EAAE;MACjC,OAAO,KAAK;IAChB;IACA6B,YAAY,CAAC5E,EAAE,EAAE4D,MAAM,CAAC;IACxBA,MAAM,CAACjC,MAAM,GAAGiC,MAAM,CAACG,QAAQ;IAC/BH,MAAM,CAAChC,MAAM,GAAGgC,MAAM,CAACI,QAAQ;IAC/BJ,MAAM,CAACE,SAAS,GAAGF,MAAM,CAACO,WAAW,GAAGQ,SAAS;IACjDf,MAAM,CAACK,SAAS,GAAGL,MAAM,CAACM,SAAS,GAAGN,MAAM,CAACzB,MAAM,GAAGyB,MAAM,CAACxB,MAAM,GAAG,CAAC;IACvEwB,MAAM,CAACQ,KAAK,GAAGpE,EAAE;IACjB;IACA,IAAIqD,QAAQ,IAAIA,QAAQ,CAACO,MAAM,CAAC,KAAK,KAAK,EAAE;MACxC,OAAO,KAAK;IAChB;IACA;IACAW,OAAO,CAACM,OAAO,CAAC,CAAC;IACjB;IACA,IAAI,CAACN,OAAO,CAACxC,KAAK,CAAC,CAAC,EAAE;MAClB,OAAO,KAAK;IAChB;IACAe,aAAa,GAAG,IAAI;IACpB,IAAIpB,SAAS,KAAK,CAAC,EAAE;MACjB,OAAOoD,eAAe,CAAC,CAAC;IAC5B;IACAR,GAAG,CAACvC,KAAK,CAAC6B,MAAM,CAACjC,MAAM,EAAEiC,MAAM,CAAChC,MAAM,CAAC;IACvC,OAAO,IAAI;EACf,CAAC;EACD,MAAMxC,WAAW,GAAIY,EAAE,IAAK;IACxB;IACA;IACA,IAAI6C,cAAc,EAAE;MAChB,IAAI,CAACG,YAAY,IAAID,aAAa,EAAE;QAChCC,YAAY,GAAG,IAAI;QACnB+B,eAAe,CAACnB,MAAM,EAAE5D,EAAE,CAAC;QAC3BgF,qBAAqB,CAACC,UAAU,CAAC;MACrC;MACA;IACJ;IACA;IACAF,eAAe,CAACnB,MAAM,EAAE5D,EAAE,CAAC;IAC3B,IAAIsE,GAAG,CAACpC,MAAM,CAAC0B,MAAM,CAACG,QAAQ,EAAEH,MAAM,CAACI,QAAQ,CAAC,EAAE;MAC9C,IAAI,CAACM,GAAG,CAAC7B,SAAS,CAAC,CAAC,IAAI,CAACqC,eAAe,CAAC,CAAC,EAAE;QACxCI,YAAY,CAAC,CAAC;MAClB;IACJ;EACJ,CAAC;EACD,MAAMD,UAAU,GAAGA,CAAA,KAAM;IACrB;IACA;IACA,IAAI,CAACpC,cAAc,EAAE;MACjB;IACJ;IACAG,YAAY,GAAG,KAAK;IACpB,IAAIU,MAAM,EAAE;MACRA,MAAM,CAACE,MAAM,CAAC;IAClB;EACJ,CAAC;EACD,MAAMkB,eAAe,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACP,OAAO,CAACjG,OAAO,CAAC,CAAC,EAAE;MACpB,OAAO,KAAK;IAChB;IACAuE,cAAc,GAAG,IAAI;IACrBE,aAAa,GAAG,KAAK;IACrB;IACA;IACA;IACA;IACA;IACA;IACAa,MAAM,CAACjC,MAAM,GAAGiC,MAAM,CAACG,QAAQ;IAC/BH,MAAM,CAAChC,MAAM,GAAGgC,MAAM,CAACI,QAAQ;IAC/BJ,MAAM,CAACE,SAAS,GAAGF,MAAM,CAACO,WAAW;IACrC,IAAIb,WAAW,EAAE;MACbA,WAAW,CAACM,MAAM,CAAC,CAACuB,IAAI,CAACC,WAAW,CAAC;IACzC,CAAC,MACI;MACDA,WAAW,CAAC,CAAC;IACjB;IACA,OAAO,IAAI;EACf,CAAC;EACD,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC5B,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;MACjC,MAAMC,aAAa,GAAGD,QAAQ,CAACC,aAAa;MAC5C,IAAIA,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACC,IAAI,EAAE;QAClFD,aAAa,CAACC,IAAI,CAAC,CAAC;MACxB;IACJ;EACJ,CAAC;EACD,MAAMJ,WAAW,GAAGA,CAAA,KAAM;IACtB,IAAIzB,WAAW,EAAE;MACb0B,iBAAiB,CAAC,CAAC;IACvB;IACA,IAAI9B,OAAO,EAAE;MACTA,OAAO,CAACK,MAAM,CAAC;IACnB;IACAb,aAAa,GAAG,IAAI;EACxB,CAAC;EACD,MAAM0C,KAAK,GAAGA,CAAA,KAAM;IAChB5C,cAAc,GAAG,KAAK;IACtBC,aAAa,GAAG,KAAK;IACrBE,YAAY,GAAG,KAAK;IACpBD,aAAa,GAAG,IAAI;IACpBwB,OAAO,CAACM,OAAO,CAAC,CAAC;EACrB,CAAC;EACD;EACA,MAAMxF,SAAS,GAAIW,EAAE,IAAK;IACtB,MAAM0F,cAAc,GAAG7C,cAAc;IACrC,MAAM8C,gBAAgB,GAAG5C,aAAa;IACtC0C,KAAK,CAAC,CAAC;IACP,IAAI,CAACE,gBAAgB,EAAE;MACnB;IACJ;IACAZ,eAAe,CAACnB,MAAM,EAAE5D,EAAE,CAAC;IAC3B;IACA,IAAI0F,cAAc,EAAE;MAChB,IAAIlC,KAAK,EAAE;QACPA,KAAK,CAACI,MAAM,CAAC;MACjB;MACA;IACJ;IACA;IACA,IAAIH,WAAW,EAAE;MACbA,WAAW,CAACG,MAAM,CAAC;IACvB;EACJ,CAAC;EACD,MAAMgC,aAAa,GAAG1G,mBAAmB,CAAC+D,WAAW,CAACjF,EAAE,EAAEmB,WAAW,EAAEC,WAAW,EAAEC,SAAS,EAAE;IAC3Fd;EACJ,CAAC,CAAC;EACF,MAAM2G,YAAY,GAAGA,CAAA,KAAM;IACvBO,KAAK,CAAC,CAAC;IACPG,aAAa,CAAClF,IAAI,CAAC,CAAC;IACpB,IAAI+C,WAAW,EAAE;MACbA,WAAW,CAACG,MAAM,CAAC;IACvB;EACJ,CAAC;EACD,OAAO;IACHjD,MAAMA,CAACA,MAAM,GAAG,IAAI,EAAE;MAClB,IAAI,CAACA,MAAM,EAAE;QACT,IAAIkC,cAAc,EAAE;UAChBxD,SAAS,CAACT,SAAS,CAAC;QACxB;QACA6G,KAAK,CAAC,CAAC;MACX;MACAG,aAAa,CAACjF,MAAM,CAACA,MAAM,CAAC;IAChC,CAAC;IACDE,OAAOA,CAAA,EAAG;MACN0D,OAAO,CAAC1D,OAAO,CAAC,CAAC;MACjB+E,aAAa,CAAC/E,OAAO,CAAC,CAAC;IAC3B;EACJ,CAAC;AACL,CAAC;AACD,MAAMkE,eAAe,GAAGA,CAACnB,MAAM,EAAE5D,EAAE,KAAK;EACpC,IAAI,CAACA,EAAE,EAAE;IACL;EACJ;EACA,MAAM6F,KAAK,GAAGjC,MAAM,CAACG,QAAQ;EAC7B,MAAM+B,KAAK,GAAGlC,MAAM,CAACI,QAAQ;EAC7B,MAAM+B,KAAK,GAAGnC,MAAM,CAACO,WAAW;EAChCS,YAAY,CAAC5E,EAAE,EAAE4D,MAAM,CAAC;EACxB,MAAMG,QAAQ,GAAGH,MAAM,CAACG,QAAQ;EAChC,MAAMC,QAAQ,GAAGJ,MAAM,CAACI,QAAQ;EAChC,MAAMgC,SAAS,GAAIpC,MAAM,CAACO,WAAW,GAAGjE,GAAG,CAACF,EAAE,CAAE;EAChD,MAAMiG,SAAS,GAAGD,SAAS,GAAGD,KAAK;EACnC,IAAIE,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAG,GAAG,EAAE;IAClC,MAAMhC,SAAS,GAAG,CAACF,QAAQ,GAAG8B,KAAK,IAAII,SAAS;IAChD,MAAM/B,SAAS,GAAG,CAACF,QAAQ,GAAG8B,KAAK,IAAIG,SAAS;IAChDrC,MAAM,CAACK,SAAS,GAAGA,SAAS,GAAG,GAAG,GAAGL,MAAM,CAACK,SAAS,GAAG,GAAG;IAC3DL,MAAM,CAACM,SAAS,GAAGA,SAAS,GAAG,GAAG,GAAGN,MAAM,CAACM,SAAS,GAAG,GAAG;EAC/D;EACAN,MAAM,CAACzB,MAAM,GAAG4B,QAAQ,GAAGH,MAAM,CAACjC,MAAM;EACxCiC,MAAM,CAACxB,MAAM,GAAG4B,QAAQ,GAAGJ,MAAM,CAAChC,MAAM;EACxCgC,MAAM,CAACQ,KAAK,GAAGpE,EAAE;AACrB,CAAC;AACD,MAAM4E,YAAY,GAAGA,CAAC5E,EAAE,EAAE4D,MAAM,KAAK;EACjC;EACA;EACA,IAAI5B,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIjC,EAAE,EAAE;IACJ,MAAMkG,cAAc,GAAGlG,EAAE,CAACkG,cAAc;IACxC,IAAIA,cAAc,IAAIA,cAAc,CAACC,MAAM,GAAG,CAAC,EAAE;MAC7C,MAAMC,KAAK,GAAGF,cAAc,CAAC,CAAC,CAAC;MAC/BlE,CAAC,GAAGoE,KAAK,CAACC,OAAO;MACjBpE,CAAC,GAAGmE,KAAK,CAACE,OAAO;IACrB,CAAC,MACI,IAAItG,EAAE,CAACuG,KAAK,KAAK3H,SAAS,EAAE;MAC7BoD,CAAC,GAAGhC,EAAE,CAACuG,KAAK;MACZtE,CAAC,GAAGjC,EAAE,CAACwG,KAAK;IAChB;EACJ;EACA5C,MAAM,CAACG,QAAQ,GAAG/B,CAAC;EACnB4B,MAAM,CAACI,QAAQ,GAAG/B,CAAC;AACvB,CAAC;AACD,MAAM/B,GAAG,GAAIF,EAAE,IAAK;EAChB,OAAOA,EAAE,CAAC2E,SAAS,IAAI1E,IAAI,CAACC,GAAG,CAAC,CAAC;AACrC,CAAC;AAED,SAASpC,kBAAkB,EAAE6E,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}