{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, m as printIonWarning, e as getIonMode, h, F as Fragment, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { i as inheritAriaAttributes } from './helpers-1O4D2b7y.js';\nimport { i as isRTL } from './dir-C53feagD.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\nconst inputOtpIosCss = \".sc-ion-input-otp-ios-h{--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--padding-top:16px;--padding-end:0;--padding-bottom:16px;--padding-start:0;--color:initial;--min-width:40px;--separator-width:8px;--separator-height:var(--separator-width);--separator-border-radius:999px;--separator-color:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-size:0.875rem}.input-otp-group.sc-ion-input-otp-ios{-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.native-wrapper.sc-ion-input-otp-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:var(--min-width)}.native-input.sc-ion-input-otp-ios{border-radius:var(--border-radius);width:var(--width);min-width:inherit;height:var(--height);border-width:var(--border-width);border-style:solid;border-color:var(--border-color);background:var(--background);color:var(--color);font-size:inherit;text-align:center;-webkit-appearance:none;-moz-appearance:none;appearance:none}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{caret-color:var(--highlight-color)}.input-otp-description.sc-ion-input-otp-ios{color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));font-size:0.75rem;line-height:1.25rem;text-align:center}.input-otp-description-hidden.sc-ion-input-otp-ios{display:none}.input-otp-separator.sc-ion-input-otp-ios{border-radius:var(--separator-border-radius);-ms-flex-negative:0;flex-shrink:0;width:var(--separator-width);height:var(--separator-height);background:var(--separator-color)}.input-otp-size-small.sc-ion-input-otp-ios-h{--width:40px;--height:40px}.input-otp-size-small.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios{gap:8px}.input-otp-size-medium.sc-ion-input-otp-ios-h{--width:48px;--height:48px}.input-otp-size-large.sc-ion-input-otp-ios-h{--width:56px;--height:56px}.input-otp-size-medium.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios,.input-otp-size-large.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios{gap:12px}.input-otp-shape-round.sc-ion-input-otp-ios-h{--border-radius:16px}.input-otp-shape-soft.sc-ion-input-otp-ios-h{--border-radius:8px}.input-otp-shape-rectangular.sc-ion-input-otp-ios-h{--border-radius:0}.input-otp-fill-outline.sc-ion-input-otp-ios-h{--background:none}.input-otp-fill-solid.sc-ion-input-otp-ios-h{--border-color:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-disabled.sc-ion-input-otp-ios-h{--color:var(--ion-color-step-350, var(--ion-text-color-step-650, #a6a6a6))}.input-otp-fill-outline.input-otp-disabled.sc-ion-input-otp-ios-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.input-otp-disabled.sc-ion-input-otp-ios-h,.input-otp-disabled.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:disabled{cursor:not-allowed}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{--border-color:var(--highlight-color);outline:none}.input-otp-fill-outline.input-otp-readonly.sc-ion-input-otp-ios-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-fill-solid.input-otp-disabled.sc-ion-input-otp-ios-h,.input-otp-fill-solid.input-otp-readonly.sc-ion-input-otp-ios-h{--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.ion-touched.ion-invalid.sc-ion-input-otp-ios-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-otp-ios-h{--highlight-color:var(--highlight-color-valid)}.has-focus.ion-valid.sc-ion-input-otp-ios-h,.ion-touched.ion-invalid.sc-ion-input-otp-ios-h{--border-color:var(--highlight-color)}.ion-color.sc-ion-input-otp-ios-h{--highlight-color-focused:var(--ion-color-base)}.input-otp-fill-outline.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{border-color:rgba(var(--ion-color-base-rgb), 0.6)}.input-otp-fill-outline.ion-color.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-outline.ion-color.has-focus.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.has-focus.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:var(--ion-color-danger, #c5000f)}.input-otp-fill-outline.ion-color.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-outline.ion-color.has-focus.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.has-focus.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:var(--ion-color-success, #2dd55b)}.input-otp-fill-outline.input-otp-disabled.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:rgba(var(--ion-color-base-rgb), 0.3)}.sc-ion-input-otp-ios-h{--border-width:0.55px}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{--border-width:1px}.input-otp-fill-outline.sc-ion-input-otp-ios-h{--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))))}\";\nconst inputOtpMdCss = \".sc-ion-input-otp-md-h{--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--padding-top:16px;--padding-end:0;--padding-bottom:16px;--padding-start:0;--color:initial;--min-width:40px;--separator-width:8px;--separator-height:var(--separator-width);--separator-border-radius:999px;--separator-color:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-size:0.875rem}.input-otp-group.sc-ion-input-otp-md{-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.native-wrapper.sc-ion-input-otp-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:var(--min-width)}.native-input.sc-ion-input-otp-md{border-radius:var(--border-radius);width:var(--width);min-width:inherit;height:var(--height);border-width:var(--border-width);border-style:solid;border-color:var(--border-color);background:var(--background);color:var(--color);font-size:inherit;text-align:center;-webkit-appearance:none;-moz-appearance:none;appearance:none}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{caret-color:var(--highlight-color)}.input-otp-description.sc-ion-input-otp-md{color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));font-size:0.75rem;line-height:1.25rem;text-align:center}.input-otp-description-hidden.sc-ion-input-otp-md{display:none}.input-otp-separator.sc-ion-input-otp-md{border-radius:var(--separator-border-radius);-ms-flex-negative:0;flex-shrink:0;width:var(--separator-width);height:var(--separator-height);background:var(--separator-color)}.input-otp-size-small.sc-ion-input-otp-md-h{--width:40px;--height:40px}.input-otp-size-small.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md{gap:8px}.input-otp-size-medium.sc-ion-input-otp-md-h{--width:48px;--height:48px}.input-otp-size-large.sc-ion-input-otp-md-h{--width:56px;--height:56px}.input-otp-size-medium.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md,.input-otp-size-large.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md{gap:12px}.input-otp-shape-round.sc-ion-input-otp-md-h{--border-radius:16px}.input-otp-shape-soft.sc-ion-input-otp-md-h{--border-radius:8px}.input-otp-shape-rectangular.sc-ion-input-otp-md-h{--border-radius:0}.input-otp-fill-outline.sc-ion-input-otp-md-h{--background:none}.input-otp-fill-solid.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-disabled.sc-ion-input-otp-md-h{--color:var(--ion-color-step-350, var(--ion-text-color-step-650, #a6a6a6))}.input-otp-fill-outline.input-otp-disabled.sc-ion-input-otp-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.input-otp-disabled.sc-ion-input-otp-md-h,.input-otp-disabled.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:disabled{cursor:not-allowed}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{--border-color:var(--highlight-color);outline:none}.input-otp-fill-outline.input-otp-readonly.sc-ion-input-otp-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-fill-solid.input-otp-disabled.sc-ion-input-otp-md-h,.input-otp-fill-solid.input-otp-readonly.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.ion-touched.ion-invalid.sc-ion-input-otp-md-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-otp-md-h{--highlight-color:var(--highlight-color-valid)}.has-focus.ion-valid.sc-ion-input-otp-md-h,.ion-touched.ion-invalid.sc-ion-input-otp-md-h{--border-color:var(--highlight-color)}.ion-color.sc-ion-input-otp-md-h{--highlight-color-focused:var(--ion-color-base)}.input-otp-fill-outline.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{border-color:rgba(var(--ion-color-base-rgb), 0.6)}.input-otp-fill-outline.ion-color.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-outline.ion-color.has-focus.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.has-focus.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:var(--ion-color-danger, #c5000f)}.input-otp-fill-outline.ion-color.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-outline.ion-color.has-focus.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.has-focus.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:var(--ion-color-success, #2dd55b)}.input-otp-fill-outline.input-otp-disabled.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:rgba(var(--ion-color-base-rgb), 0.3)}.sc-ion-input-otp-md-h{--border-width:1px}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{--border-width:2px}.input-otp-fill-outline.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3))}\";\nconst InputOTP = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionInput = createEvent(this, \"ionInput\", 7);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionComplete = createEvent(this, \"ionComplete\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.inheritedAttributes = {};\n    this.inputRefs = [];\n    this.inputId = `ion-input-otp-${inputIds++}`;\n    this.parsedSeparators = [];\n    /**\n     * Tracks whether the user is navigating through input boxes using keyboard navigation\n     * (arrow keys, tab) versus mouse clicks. This is used to determine the appropriate\n     * focus behavior when an input box is focused.\n     */\n    this.isKeyboardNavigation = false;\n    this.inputValues = [];\n    this.hasFocus = false;\n    this.previousInputValues = [];\n    /**\n     * Indicates whether and how the text value should be automatically capitalized as it is entered/edited by the user.\n     * Available options: `\"off\"`, `\"none\"`, `\"on\"`, `\"sentences\"`, `\"words\"`, `\"characters\"`.\n     */\n    this.autocapitalize = 'off';\n    /**\n     * If `true`, the user cannot interact with the input.\n     */\n    this.disabled = false;\n    /**\n     * The fill for the input boxes. If `\"solid\"` the input boxes will have a background. If\n     * `\"outline\"` the input boxes will be transparent with a border.\n     */\n    this.fill = 'outline';\n    /**\n     * The number of input boxes to display.\n     */\n    this.length = 4;\n    /**\n     * If `true`, the user cannot modify the value.\n     */\n    this.readonly = false;\n    /**\n     * The shape of the input boxes.\n     * If \"round\" they will have an increased border radius.\n     * If \"rectangular\" they will have no border radius.\n     * If \"soft\" they will have a soft border radius.\n     */\n    this.shape = 'round';\n    /**\n     * The size of the input boxes.\n     */\n    this.size = 'medium';\n    /**\n     * The type of input allowed in the input boxes.\n     */\n    this.type = 'number';\n    /**\n     * The value of the input group.\n     */\n    this.value = '';\n    /**\n     * Handles the focus behavior for the input OTP component.\n     *\n     * Focus behavior:\n     * 1. Keyboard navigation: Allow normal focus movement\n     * 2. Mouse click:\n     *    - If clicked box has value: Focus that box\n     *    - If clicked box is empty: Focus first empty box\n     *\n     * Emits the `ionFocus` event when the input group gains focus.\n     */\n    this.onFocus = index => event => {\n      var _a;\n      const {\n        inputRefs\n      } = this;\n      // Only emit ionFocus and set the focusedValue when the\n      // component first gains focus\n      if (!this.hasFocus) {\n        this.ionFocus.emit(event);\n        this.focusedValue = this.value;\n      }\n      this.hasFocus = true;\n      let finalIndex = index;\n      if (!this.isKeyboardNavigation) {\n        // If the clicked box has a value, focus it\n        // Otherwise focus the first empty box\n        const targetIndex = this.inputValues[index] ? index : this.getFirstEmptyIndex();\n        finalIndex = targetIndex === -1 ? this.length - 1 : targetIndex;\n        // Focus the target box\n        (_a = this.inputRefs[finalIndex]) === null || _a === void 0 ? void 0 : _a.focus();\n      }\n      // Update tabIndexes to match the focused box\n      inputRefs.forEach((input, i) => {\n        input.tabIndex = i === finalIndex ? 0 : -1;\n      });\n      // Reset the keyboard navigation flag\n      this.isKeyboardNavigation = false;\n    };\n    /**\n     * Handles the blur behavior for the input OTP component.\n     * Emits the `ionBlur` event when the input group loses focus.\n     */\n    this.onBlur = event => {\n      const {\n        inputRefs\n      } = this;\n      const relatedTarget = event.relatedTarget;\n      // Do not emit blur if we're moving to another input box in the same component\n      const isInternalFocus = relatedTarget != null && inputRefs.includes(relatedTarget);\n      if (!isInternalFocus) {\n        this.hasFocus = false;\n        // Reset tabIndexes when focus leaves the component\n        this.updateTabIndexes();\n        // Always emit ionBlur when focus leaves the component\n        this.ionBlur.emit(event);\n        // Only emit ionChange if the value has actually changed\n        if (this.focusedValue !== this.value) {\n          this.emitIonChange(event);\n        }\n      }\n    };\n    /**\n     * Handles keyboard navigation for the OTP component.\n     *\n     * Navigation:\n     * - Backspace: Clears current input and moves to previous box if empty\n     * - Arrow Left/Right: Moves focus between input boxes\n     * - Tab: Allows normal tab navigation between components\n     */\n    this.onKeyDown = index => event => {\n      const {\n        length\n      } = this;\n      const rtl = isRTL(this.el);\n      const input = event.target;\n      // Meta shortcuts are used to copy, paste, and select text\n      // We don't want to handle these keys here\n      const metaShortcuts = ['a', 'c', 'v', 'x', 'r', 'z', 'y'];\n      const isTextSelection = input.selectionStart !== input.selectionEnd;\n      // Return if the key is a meta shortcut or the input value\n      // text is selected and let the onPaste / onInput handler manage it\n      if (isTextSelection || (event.metaKey || event.ctrlKey) && metaShortcuts.includes(event.key.toLowerCase())) {\n        return;\n      }\n      if (event.key === 'Backspace') {\n        if (this.inputValues[index]) {\n          // Shift all values to the right of the current index left by one\n          for (let i = index; i < length - 1; i++) {\n            this.inputValues[i] = this.inputValues[i + 1];\n          }\n          // Clear the last box\n          this.inputValues[length - 1] = '';\n          // Update all inputRefs to match inputValues\n          for (let i = 0; i < length; i++) {\n            this.inputRefs[i].value = this.inputValues[i] || '';\n          }\n          this.updateValue(event);\n          event.preventDefault();\n        } else if (!this.inputValues[index] && index > 0) {\n          // If current input is empty, move to previous input\n          this.focusPrevious(index);\n        }\n      } else if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {\n        this.isKeyboardNavigation = true;\n        event.preventDefault();\n        const isLeft = event.key === 'ArrowLeft';\n        const shouldMoveNext = isLeft && rtl || !isLeft && !rtl;\n        // Only allow moving to the next input if the current has a value\n        if (shouldMoveNext) {\n          if (this.inputValues[index] && index < length - 1) {\n            this.focusNext(index);\n          }\n        } else {\n          this.focusPrevious(index);\n        }\n      } else if (event.key === 'Tab') {\n        this.isKeyboardNavigation = true;\n        // Let all tab events proceed normally\n        return;\n      }\n    };\n    /**\n     * Processes all input scenarios for each input box.\n     *\n     * This function manages:\n     * 1. Autofill handling\n     * 2. Input validation\n     * 3. Full selection replacement or typing in an empty box\n     * 4. Inserting in the middle with available space (shifting)\n     * 5. Single character replacement\n     */\n    this.onInput = index => event => {\n      var _a, _b;\n      const {\n        length,\n        validKeyPattern\n      } = this;\n      const input = event.target;\n      const value = input.value;\n      const previousValue = this.previousInputValues[index] || '';\n      // 1. Autofill handling\n      // If the length of the value increases by more than 1 from the previous\n      // value, treat this as autofill. This is to prevent the case where the\n      // user is typing a single character into an input box containing a value\n      // as that will trigger this function with a value length of 2 characters.\n      const isAutofill = value.length - previousValue.length > 1;\n      if (isAutofill) {\n        // Distribute valid characters across input boxes\n        const validChars = value.split('').filter(char => validKeyPattern.test(char)).slice(0, length);\n        // If there are no valid characters coming from the\n        // autofill, all input refs have to be cleared after the\n        // browser has finished the autofill behavior\n        if (validChars.length === 0) {\n          requestAnimationFrame(() => {\n            this.inputRefs.forEach(input => {\n              input.value = '';\n            });\n          });\n        }\n        for (let i = 0; i < length; i++) {\n          this.inputValues[i] = validChars[i] || '';\n          this.inputRefs[i].value = validChars[i] || '';\n        }\n        this.updateValue(event);\n        // Focus the first empty input box or the last input box if all boxes\n        // are filled after a small delay to ensure the input boxes have been\n        // updated before moving the focus\n        setTimeout(() => {\n          var _a;\n          const nextIndex = validChars.length < length ? validChars.length : length - 1;\n          (_a = this.inputRefs[nextIndex]) === null || _a === void 0 ? void 0 : _a.focus();\n        }, 20);\n        this.previousInputValues = [...this.inputValues];\n        return;\n      }\n      // 2. Input validation\n      // If the character entered is invalid (does not match the pattern),\n      // restore the previous value and exit\n      if (value.length > 0 && !validKeyPattern.test(value[value.length - 1])) {\n        input.value = this.inputValues[index] || '';\n        this.previousInputValues = [...this.inputValues];\n        return;\n      }\n      // 3. Full selection replacement or typing in an empty box\n      // If the user selects all text in the input box and types, or if the\n      // input box is empty, replace only this input box. If the box is empty,\n      // move to the next box, otherwise stay focused on this box.\n      const isAllSelected = input.selectionStart === 0 && input.selectionEnd === value.length;\n      const isEmpty = !this.inputValues[index];\n      if (isAllSelected || isEmpty) {\n        this.inputValues[index] = value;\n        input.value = value;\n        this.updateValue(event);\n        this.focusNext(index);\n        this.previousInputValues = [...this.inputValues];\n        return;\n      }\n      // 4. Inserting in the middle with available space (shifting)\n      // If typing in a filled input box and there are empty boxes at the end,\n      // shift all values starting at the current box to the right, and insert\n      // the new character at the current box.\n      const hasAvailableBoxAtEnd = this.inputValues[this.inputValues.length - 1] === '';\n      if (this.inputValues[index] && hasAvailableBoxAtEnd && value.length === 2) {\n        // Get the inserted character (from event or by diffing value/previousValue)\n        let newChar = event.data;\n        if (!newChar) {\n          newChar = value.split('').find((c, i) => c !== previousValue[i]) || value[value.length - 1];\n        }\n        // Validate the new character before shifting\n        if (!validKeyPattern.test(newChar)) {\n          input.value = this.inputValues[index] || '';\n          this.previousInputValues = [...this.inputValues];\n          return;\n        }\n        // Shift values right from the end to the insertion point\n        for (let i = this.inputValues.length - 1; i > index; i--) {\n          this.inputValues[i] = this.inputValues[i - 1];\n          this.inputRefs[i].value = this.inputValues[i] || '';\n        }\n        this.inputValues[index] = newChar;\n        this.inputRefs[index].value = newChar;\n        this.updateValue(event);\n        this.previousInputValues = [...this.inputValues];\n        return;\n      }\n      // 5. Single character replacement\n      // Handles replacing a single character in a box containing a value based\n      // on the cursor position. We need the cursor position to determine which\n      // character was the last character typed. For example, if the user types \"2\"\n      // in an input box with the cursor at the beginning of the value of \"6\",\n      // the value will be \"26\", but we want to grab the \"2\" as the last character\n      // typed.\n      const cursorPos = (_a = input.selectionStart) !== null && _a !== void 0 ? _a : value.length;\n      const newCharIndex = cursorPos - 1;\n      const newChar = (_b = value[newCharIndex]) !== null && _b !== void 0 ? _b : value[0];\n      // Check if the new character is valid before updating the value\n      if (!validKeyPattern.test(newChar)) {\n        input.value = this.inputValues[index] || '';\n        this.previousInputValues = [...this.inputValues];\n        return;\n      }\n      this.inputValues[index] = newChar;\n      input.value = newChar;\n      this.updateValue(event);\n      this.previousInputValues = [...this.inputValues];\n    };\n    /**\n     * Handles pasting text into the input OTP component.\n     * This function prevents the default paste behavior and\n     * validates the pasted text against the allowed pattern.\n     * It then updates the value of the input group and focuses\n     * the next empty input after pasting.\n     */\n    this.onPaste = event => {\n      var _a, _b;\n      const {\n        inputRefs,\n        length,\n        validKeyPattern\n      } = this;\n      event.preventDefault();\n      const pastedText = (_a = event.clipboardData) === null || _a === void 0 ? void 0 : _a.getData('text');\n      // If there is no pasted text, still emit the input change event\n      // because this is how the native input element behaves\n      // but return early because there is nothing to paste.\n      if (!pastedText) {\n        this.emitIonInput(event);\n        return;\n      }\n      const validChars = pastedText.split('').filter(char => validKeyPattern.test(char)).slice(0, length);\n      // Always paste starting at the first box\n      validChars.forEach((char, index) => {\n        if (index < length) {\n          this.inputRefs[index].value = char;\n          this.inputValues[index] = char;\n        }\n      });\n      // Update the value so that all input boxes are updated\n      this.value = validChars.join('');\n      this.updateValue(event);\n      // Focus the next empty input after pasting\n      // If all boxes are filled, focus the last input\n      const nextEmptyIndex = validChars.length < length ? validChars.length : length - 1;\n      (_b = inputRefs[nextEmptyIndex]) === null || _b === void 0 ? void 0 : _b.focus();\n    };\n  }\n  /**\n   * Sets focus to an input box.\n   * @param index - The index of the input box to focus (0-based).\n   * If provided and the input box has a value, the input box at that index will be focused.\n   * Otherwise, the first empty input box or the last input if all are filled will be focused.\n   */\n  setFocus(index) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      var _a, _b;\n      if (typeof index === 'number') {\n        const validIndex = Math.max(0, Math.min(index, _this.length - 1));\n        (_a = _this.inputRefs[validIndex]) === null || _a === void 0 ? void 0 : _a.focus();\n      } else {\n        const tabbableIndex = _this.getTabbableIndex();\n        (_b = _this.inputRefs[tabbableIndex]) === null || _b === void 0 ? void 0 : _b.focus();\n      }\n    })();\n  }\n  valueChanged() {\n    this.initializeValues();\n    this.updateTabIndexes();\n  }\n  /**\n   * Processes the separators prop into an array of numbers.\n   *\n   * If the separators prop is not provided, returns an empty array.\n   * If the separators prop is 'all', returns an array of all valid positions (1 to length-1).\n   * If the separators prop is an array, returns it as is.\n   * If the separators prop is a string, splits it by commas and parses each part as a number.\n   *\n   * If the separators are greater than the input length, it will warn and ignore the separators.\n   */\n  processSeparators() {\n    const {\n      separators,\n      length\n    } = this;\n    if (separators === undefined) {\n      this.parsedSeparators = [];\n      return;\n    }\n    if (typeof separators === 'string' && separators !== 'all') {\n      const isValidFormat = /^(\\d+)(,\\d+)*$/.test(separators);\n      if (!isValidFormat) {\n        printIonWarning(`[ion-input-otp] - Invalid separators format. Expected a comma-separated list of numbers, an array of numbers, or \"all\". Received: ${separators}`, this.el);\n        this.parsedSeparators = [];\n        return;\n      }\n    }\n    let separatorValues;\n    if (separators === 'all') {\n      separatorValues = Array.from({\n        length: length - 1\n      }, (_, i) => i + 1);\n    } else if (Array.isArray(separators)) {\n      separatorValues = separators;\n    } else {\n      separatorValues = separators.split(',').map(pos => parseInt(pos, 10)).filter(pos => !isNaN(pos));\n    }\n    // Check for duplicate separator positions\n    const duplicates = separatorValues.filter((pos, index) => separatorValues.indexOf(pos) !== index);\n    if (duplicates.length > 0) {\n      printIonWarning(`[ion-input-otp] - Duplicate separator positions are not allowed. Received: ${separators}`, this.el);\n    }\n    const invalidSeparators = separatorValues.filter(pos => pos > length);\n    if (invalidSeparators.length > 0) {\n      printIonWarning(`[ion-input-otp] - The following separator positions are greater than the input length (${length}): ${invalidSeparators.join(', ')}. These separators will be ignored.`, this.el);\n    }\n    this.parsedSeparators = separatorValues.filter(pos => pos <= length);\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n    this.processSeparators();\n    this.initializeValues();\n  }\n  componentDidLoad() {\n    this.updateTabIndexes();\n  }\n  /**\n   * Get the regex pattern for allowed characters.\n   * If a pattern is provided, use it to create a regex pattern\n   * Otherwise, use the default regex pattern based on type\n   */\n  get validKeyPattern() {\n    return new RegExp(`^${this.getPattern()}$`, 'u');\n  }\n  /**\n   * Gets the string pattern to pass to the input element\n   * and use in the regex for allowed characters.\n   */\n  getPattern() {\n    const {\n      pattern,\n      type\n    } = this;\n    if (pattern) {\n      return pattern;\n    }\n    return type === 'number' ? '[\\\\p{N}]' : '[\\\\p{L}\\\\p{N}]';\n  }\n  /**\n   * Get the default value for inputmode.\n   * If inputmode is provided, use it.\n   * Otherwise, use the default inputmode based on type\n   */\n  getInputmode() {\n    const {\n      inputmode\n    } = this;\n    if (inputmode) {\n      return inputmode;\n    }\n    if (this.type == 'number') {\n      return 'numeric';\n    } else {\n      return 'text';\n    }\n  }\n  /**\n   * Initializes the input values array based on the current value prop.\n   * This splits the value into individual characters and validates them against\n   * the allowed pattern. The values are then used as the values in the native\n   * input boxes and the value of the input group is updated.\n   */\n  initializeValues() {\n    // Clear all input values\n    this.inputValues = Array(this.length).fill('');\n    // If the value is null, undefined, or an empty string, return\n    if (this.value == null || String(this.value).length === 0) {\n      return;\n    }\n    // Split the value into individual characters and validate\n    // them against the allowed pattern\n    const chars = String(this.value).split('').slice(0, this.length);\n    chars.forEach((char, index) => {\n      if (this.validKeyPattern.test(char)) {\n        this.inputValues[index] = char;\n      }\n    });\n    // Update the value without emitting events\n    this.value = this.inputValues.join('');\n    this.previousInputValues = [...this.inputValues];\n  }\n  /**\n   * Updates the value of the input group.\n   * This updates the value of the input group and emits an `ionChange` event.\n   * If all of the input boxes are filled, it emits an `ionComplete` event.\n   */\n  updateValue(event) {\n    const {\n      inputValues,\n      length\n    } = this;\n    const newValue = inputValues.join('');\n    this.value = newValue;\n    this.emitIonInput(event);\n    if (newValue.length === length) {\n      this.ionComplete.emit({\n        value: newValue\n      });\n    }\n  }\n  /**\n   * Emits an `ionChange` event.\n   * This API should be called for user committed changes.\n   * This API should not be used for external value changes.\n   */\n  emitIonChange(event) {\n    const {\n      value\n    } = this;\n    // Checks for both null and undefined values\n    const newValue = value == null ? value : value.toString();\n    this.ionChange.emit({\n      value: newValue,\n      event\n    });\n  }\n  /**\n   * Emits an `ionInput` event.\n   * This is used to emit the input value when the user types,\n   * backspaces, or pastes.\n   */\n  emitIonInput(event) {\n    const {\n      value\n    } = this;\n    // Checks for both null and undefined values\n    const newValue = value == null ? value : value.toString();\n    this.ionInput.emit({\n      value: newValue,\n      event\n    });\n  }\n  /**\n   * Focuses the next input box.\n   */\n  focusNext(currentIndex) {\n    var _a;\n    const {\n      inputRefs,\n      length\n    } = this;\n    if (currentIndex < length - 1) {\n      (_a = inputRefs[currentIndex + 1]) === null || _a === void 0 ? void 0 : _a.focus();\n    }\n  }\n  /**\n   * Focuses the previous input box.\n   */\n  focusPrevious(currentIndex) {\n    var _a;\n    const {\n      inputRefs\n    } = this;\n    if (currentIndex > 0) {\n      (_a = inputRefs[currentIndex - 1]) === null || _a === void 0 ? void 0 : _a.focus();\n    }\n  }\n  /**\n   * Searches through the input values and returns the index\n   * of the first empty input.\n   * Returns -1 if all inputs are filled.\n   */\n  getFirstEmptyIndex() {\n    var _a;\n    const {\n      inputValues,\n      length\n    } = this;\n    // Create an array of the same length as the input OTP\n    // and fill it with the input values\n    const values = Array.from({\n      length\n    }, (_, i) => inputValues[i] || '');\n    return (_a = values.findIndex(value => !value || value === '')) !== null && _a !== void 0 ? _a : -1;\n  }\n  /**\n   * Returns the index of the input that should be tabbed to.\n   * If all inputs are filled, returns the last input's index.\n   * Otherwise, returns the index of the first empty input.\n   */\n  getTabbableIndex() {\n    const {\n      length\n    } = this;\n    const firstEmptyIndex = this.getFirstEmptyIndex();\n    return firstEmptyIndex === -1 ? length - 1 : firstEmptyIndex;\n  }\n  /**\n   * Updates the tabIndexes for the input boxes.\n   * This is used to ensure that the correct input is\n   * focused when the user navigates using the tab key.\n   */\n  updateTabIndexes() {\n    const {\n      inputRefs,\n      inputValues,\n      length\n    } = this;\n    // Find first empty index after any filled boxes\n    let firstEmptyIndex = -1;\n    for (let i = 0; i < length; i++) {\n      if (!inputValues[i] || inputValues[i] === '') {\n        firstEmptyIndex = i;\n        break;\n      }\n    }\n    // Update tabIndex and aria-hidden for all inputs\n    inputRefs.forEach((input, index) => {\n      const shouldBeTabbable = firstEmptyIndex === -1 ? index === length - 1 : firstEmptyIndex === index;\n      input.tabIndex = shouldBeTabbable ? 0 : -1;\n      // If the input is empty and not the first empty input,\n      // it should be hidden from screen readers.\n      const isEmpty = !inputValues[index] || inputValues[index] === '';\n      input.setAttribute('aria-hidden', isEmpty && !shouldBeTabbable ? 'true' : 'false');\n    });\n  }\n  /**\n   * Determines if a separator should be shown for a given index by\n   * checking if the index is included in the parsed separators array.\n   */\n  showSeparator(index) {\n    const {\n      length\n    } = this;\n    return this.parsedSeparators.includes(index + 1) && index < length - 1;\n  }\n  render() {\n    var _a, _b;\n    const {\n      autocapitalize,\n      color,\n      disabled,\n      el,\n      fill,\n      hasFocus,\n      inheritedAttributes,\n      inputId,\n      inputRefs,\n      inputValues,\n      length,\n      readonly,\n      shape,\n      size\n    } = this;\n    const mode = getIonMode(this);\n    const inputmode = this.getInputmode();\n    const tabbableIndex = this.getTabbableIndex();\n    const pattern = this.getPattern();\n    const hasDescription = ((_b = (_a = el.querySelector('.input-otp-description')) === null || _a === void 0 ? void 0 : _a.textContent) === null || _b === void 0 ? void 0 : _b.trim()) !== '';\n    return h(Host, {\n      key: 'f15a29fb17b681ef55885ca36d3d5f899cbaca83',\n      class: createColorClasses(color, {\n        [mode]: true,\n        'has-focus': hasFocus,\n        [`input-otp-size-${size}`]: true,\n        [`input-otp-shape-${shape}`]: true,\n        [`input-otp-fill-${fill}`]: true,\n        'input-otp-disabled': disabled,\n        'input-otp-readonly': readonly\n      })\n    }, h(\"div\", Object.assign({\n      key: 'd7e1d4edd8aafcf2ed4313301287282e90fc7e82',\n      role: \"group\",\n      \"aria-label\": \"One-time password input\",\n      class: \"input-otp-group\"\n    }, inheritedAttributes), Array.from({\n      length\n    }).map((_, index) => h(Fragment, null, h(\"div\", {\n      class: \"native-wrapper\"\n    }, h(\"input\", {\n      class: \"native-input\",\n      id: `${inputId}-${index}`,\n      \"aria-label\": `Input ${index + 1} of ${length}`,\n      type: \"text\",\n      autoCapitalize: autocapitalize,\n      inputmode: inputmode,\n      pattern: pattern,\n      disabled: disabled,\n      readOnly: readonly,\n      tabIndex: index === tabbableIndex ? 0 : -1,\n      value: inputValues[index] || '',\n      autocomplete: \"one-time-code\",\n      ref: el => inputRefs[index] = el,\n      onInput: this.onInput(index),\n      onBlur: this.onBlur,\n      onFocus: this.onFocus(index),\n      onKeyDown: this.onKeyDown(index),\n      onPaste: this.onPaste\n    })), this.showSeparator(index) && h(\"div\", {\n      class: \"input-otp-separator\"\n    })))), h(\"div\", {\n      key: '3724a3159d02860971879a906092f9965f5a7c47',\n      class: {\n        'input-otp-description': true,\n        'input-otp-description-hidden': !hasDescription\n      }\n    }, h(\"slot\", {\n      key: '11baa2624926a08274508afe0833d9237a8dc35c'\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"value\": [\"valueChanged\"],\n      \"separators\": [\"processSeparators\"],\n      \"length\": [\"processSeparators\"]\n    };\n  }\n};\nlet inputIds = 0;\nInputOTP.style = {\n  ios: inputOtpIosCss,\n  md: inputOtpMdCss\n};\nexport { InputOTP as ion_input_otp };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "m", "printIonWarning", "e", "getIonMode", "h", "F", "Fragment", "j", "Host", "k", "getElement", "i", "inheritAriaAttributes", "isRTL", "c", "createColorClasses", "inputOtpIosCss", "inputOtpMdCss", "InputOTP", "constructor", "hostRef", "ionInput", "ionChange", "ionComplete", "ionBlur", "ionFocus", "inheritedAttributes", "inputRefs", "inputId", "inputIds", "parsedSeparators", "isKeyboardNavigation", "inputValues", "hasFocus", "previousInputValues", "autocapitalize", "disabled", "fill", "length", "readonly", "shape", "size", "type", "value", "onFocus", "index", "event", "_a", "emit", "focusedValue", "finalIndex", "targetIndex", "getFirstEmptyIndex", "focus", "for<PERSON>ach", "input", "tabIndex", "onBlur", "relatedTarget", "isInternalFocus", "includes", "updateTabIndexes", "emitIonChange", "onKeyDown", "rtl", "el", "target", "metaShortcuts", "isTextSelection", "selectionStart", "selectionEnd", "metaKey", "ctrl<PERSON>ey", "key", "toLowerCase", "updateValue", "preventDefault", "focusPrevious", "isLeft", "shouldMoveNext", "focusNext", "onInput", "_b", "validKeyPattern", "previousValue", "isAutofill", "validChars", "split", "filter", "char", "test", "slice", "requestAnimationFrame", "setTimeout", "nextIndex", "isAllSelected", "isEmpty", "hasAvailableBoxAtEnd", "newChar", "data", "find", "cursorPos", "newCharIndex", "onPaste", "pastedText", "clipboardData", "getData", "emitIonInput", "join", "nextEmptyIndex", "setFocus", "_this", "_asyncToGenerator", "validIndex", "Math", "max", "min", "tabbableIndex", "getTabbableIndex", "valueChanged", "initializeValues", "processSeparators", "separators", "undefined", "isValidFormat", "separatorV<PERSON>ues", "Array", "from", "_", "isArray", "map", "pos", "parseInt", "isNaN", "duplicates", "indexOf", "invalidSeparators", "componentWillLoad", "componentDidLoad", "RegExp", "getPattern", "pattern", "getInputmode", "inputmode", "String", "chars", "newValue", "toString", "currentIndex", "values", "findIndex", "firstEmptyIndex", "shouldBeTabbable", "setAttribute", "showSeparator", "render", "color", "mode", "hasDescription", "querySelector", "textContent", "trim", "class", "Object", "assign", "role", "id", "autoCapitalize", "readOnly", "autocomplete", "ref", "watchers", "style", "ios", "md", "ion_input_otp"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@ionic/core/dist/esm/ion-input-otp.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, m as printIonWarning, e as getIonMode, h, F as Fragment, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { i as inheritAriaAttributes } from './helpers-1O4D2b7y.js';\nimport { i as isRTL } from './dir-C53feagD.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\n\nconst inputOtpIosCss = \".sc-ion-input-otp-ios-h{--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--padding-top:16px;--padding-end:0;--padding-bottom:16px;--padding-start:0;--color:initial;--min-width:40px;--separator-width:8px;--separator-height:var(--separator-width);--separator-border-radius:999px;--separator-color:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-size:0.875rem}.input-otp-group.sc-ion-input-otp-ios{-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.native-wrapper.sc-ion-input-otp-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:var(--min-width)}.native-input.sc-ion-input-otp-ios{border-radius:var(--border-radius);width:var(--width);min-width:inherit;height:var(--height);border-width:var(--border-width);border-style:solid;border-color:var(--border-color);background:var(--background);color:var(--color);font-size:inherit;text-align:center;-webkit-appearance:none;-moz-appearance:none;appearance:none}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{caret-color:var(--highlight-color)}.input-otp-description.sc-ion-input-otp-ios{color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));font-size:0.75rem;line-height:1.25rem;text-align:center}.input-otp-description-hidden.sc-ion-input-otp-ios{display:none}.input-otp-separator.sc-ion-input-otp-ios{border-radius:var(--separator-border-radius);-ms-flex-negative:0;flex-shrink:0;width:var(--separator-width);height:var(--separator-height);background:var(--separator-color)}.input-otp-size-small.sc-ion-input-otp-ios-h{--width:40px;--height:40px}.input-otp-size-small.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios{gap:8px}.input-otp-size-medium.sc-ion-input-otp-ios-h{--width:48px;--height:48px}.input-otp-size-large.sc-ion-input-otp-ios-h{--width:56px;--height:56px}.input-otp-size-medium.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios,.input-otp-size-large.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios{gap:12px}.input-otp-shape-round.sc-ion-input-otp-ios-h{--border-radius:16px}.input-otp-shape-soft.sc-ion-input-otp-ios-h{--border-radius:8px}.input-otp-shape-rectangular.sc-ion-input-otp-ios-h{--border-radius:0}.input-otp-fill-outline.sc-ion-input-otp-ios-h{--background:none}.input-otp-fill-solid.sc-ion-input-otp-ios-h{--border-color:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-disabled.sc-ion-input-otp-ios-h{--color:var(--ion-color-step-350, var(--ion-text-color-step-650, #a6a6a6))}.input-otp-fill-outline.input-otp-disabled.sc-ion-input-otp-ios-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.input-otp-disabled.sc-ion-input-otp-ios-h,.input-otp-disabled.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:disabled{cursor:not-allowed}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{--border-color:var(--highlight-color);outline:none}.input-otp-fill-outline.input-otp-readonly.sc-ion-input-otp-ios-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-fill-solid.input-otp-disabled.sc-ion-input-otp-ios-h,.input-otp-fill-solid.input-otp-readonly.sc-ion-input-otp-ios-h{--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.ion-touched.ion-invalid.sc-ion-input-otp-ios-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-otp-ios-h{--highlight-color:var(--highlight-color-valid)}.has-focus.ion-valid.sc-ion-input-otp-ios-h,.ion-touched.ion-invalid.sc-ion-input-otp-ios-h{--border-color:var(--highlight-color)}.ion-color.sc-ion-input-otp-ios-h{--highlight-color-focused:var(--ion-color-base)}.input-otp-fill-outline.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{border-color:rgba(var(--ion-color-base-rgb), 0.6)}.input-otp-fill-outline.ion-color.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-outline.ion-color.has-focus.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.has-focus.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:var(--ion-color-danger, #c5000f)}.input-otp-fill-outline.ion-color.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-outline.ion-color.has-focus.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.has-focus.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:var(--ion-color-success, #2dd55b)}.input-otp-fill-outline.input-otp-disabled.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:rgba(var(--ion-color-base-rgb), 0.3)}.sc-ion-input-otp-ios-h{--border-width:0.55px}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{--border-width:1px}.input-otp-fill-outline.sc-ion-input-otp-ios-h{--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))))}\";\n\nconst inputOtpMdCss = \".sc-ion-input-otp-md-h{--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--padding-top:16px;--padding-end:0;--padding-bottom:16px;--padding-start:0;--color:initial;--min-width:40px;--separator-width:8px;--separator-height:var(--separator-width);--separator-border-radius:999px;--separator-color:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-size:0.875rem}.input-otp-group.sc-ion-input-otp-md{-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.native-wrapper.sc-ion-input-otp-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:var(--min-width)}.native-input.sc-ion-input-otp-md{border-radius:var(--border-radius);width:var(--width);min-width:inherit;height:var(--height);border-width:var(--border-width);border-style:solid;border-color:var(--border-color);background:var(--background);color:var(--color);font-size:inherit;text-align:center;-webkit-appearance:none;-moz-appearance:none;appearance:none}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{caret-color:var(--highlight-color)}.input-otp-description.sc-ion-input-otp-md{color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));font-size:0.75rem;line-height:1.25rem;text-align:center}.input-otp-description-hidden.sc-ion-input-otp-md{display:none}.input-otp-separator.sc-ion-input-otp-md{border-radius:var(--separator-border-radius);-ms-flex-negative:0;flex-shrink:0;width:var(--separator-width);height:var(--separator-height);background:var(--separator-color)}.input-otp-size-small.sc-ion-input-otp-md-h{--width:40px;--height:40px}.input-otp-size-small.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md{gap:8px}.input-otp-size-medium.sc-ion-input-otp-md-h{--width:48px;--height:48px}.input-otp-size-large.sc-ion-input-otp-md-h{--width:56px;--height:56px}.input-otp-size-medium.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md,.input-otp-size-large.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md{gap:12px}.input-otp-shape-round.sc-ion-input-otp-md-h{--border-radius:16px}.input-otp-shape-soft.sc-ion-input-otp-md-h{--border-radius:8px}.input-otp-shape-rectangular.sc-ion-input-otp-md-h{--border-radius:0}.input-otp-fill-outline.sc-ion-input-otp-md-h{--background:none}.input-otp-fill-solid.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-disabled.sc-ion-input-otp-md-h{--color:var(--ion-color-step-350, var(--ion-text-color-step-650, #a6a6a6))}.input-otp-fill-outline.input-otp-disabled.sc-ion-input-otp-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.input-otp-disabled.sc-ion-input-otp-md-h,.input-otp-disabled.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:disabled{cursor:not-allowed}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{--border-color:var(--highlight-color);outline:none}.input-otp-fill-outline.input-otp-readonly.sc-ion-input-otp-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-fill-solid.input-otp-disabled.sc-ion-input-otp-md-h,.input-otp-fill-solid.input-otp-readonly.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.ion-touched.ion-invalid.sc-ion-input-otp-md-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-otp-md-h{--highlight-color:var(--highlight-color-valid)}.has-focus.ion-valid.sc-ion-input-otp-md-h,.ion-touched.ion-invalid.sc-ion-input-otp-md-h{--border-color:var(--highlight-color)}.ion-color.sc-ion-input-otp-md-h{--highlight-color-focused:var(--ion-color-base)}.input-otp-fill-outline.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{border-color:rgba(var(--ion-color-base-rgb), 0.6)}.input-otp-fill-outline.ion-color.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-outline.ion-color.has-focus.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.has-focus.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:var(--ion-color-danger, #c5000f)}.input-otp-fill-outline.ion-color.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-outline.ion-color.has-focus.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.has-focus.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:var(--ion-color-success, #2dd55b)}.input-otp-fill-outline.input-otp-disabled.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:rgba(var(--ion-color-base-rgb), 0.3)}.sc-ion-input-otp-md-h{--border-width:1px}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{--border-width:2px}.input-otp-fill-outline.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3))}\";\n\nconst InputOTP = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionInput = createEvent(this, \"ionInput\", 7);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionComplete = createEvent(this, \"ionComplete\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.inheritedAttributes = {};\n        this.inputRefs = [];\n        this.inputId = `ion-input-otp-${inputIds++}`;\n        this.parsedSeparators = [];\n        /**\n         * Tracks whether the user is navigating through input boxes using keyboard navigation\n         * (arrow keys, tab) versus mouse clicks. This is used to determine the appropriate\n         * focus behavior when an input box is focused.\n         */\n        this.isKeyboardNavigation = false;\n        this.inputValues = [];\n        this.hasFocus = false;\n        this.previousInputValues = [];\n        /**\n         * Indicates whether and how the text value should be automatically capitalized as it is entered/edited by the user.\n         * Available options: `\"off\"`, `\"none\"`, `\"on\"`, `\"sentences\"`, `\"words\"`, `\"characters\"`.\n         */\n        this.autocapitalize = 'off';\n        /**\n         * If `true`, the user cannot interact with the input.\n         */\n        this.disabled = false;\n        /**\n         * The fill for the input boxes. If `\"solid\"` the input boxes will have a background. If\n         * `\"outline\"` the input boxes will be transparent with a border.\n         */\n        this.fill = 'outline';\n        /**\n         * The number of input boxes to display.\n         */\n        this.length = 4;\n        /**\n         * If `true`, the user cannot modify the value.\n         */\n        this.readonly = false;\n        /**\n         * The shape of the input boxes.\n         * If \"round\" they will have an increased border radius.\n         * If \"rectangular\" they will have no border radius.\n         * If \"soft\" they will have a soft border radius.\n         */\n        this.shape = 'round';\n        /**\n         * The size of the input boxes.\n         */\n        this.size = 'medium';\n        /**\n         * The type of input allowed in the input boxes.\n         */\n        this.type = 'number';\n        /**\n         * The value of the input group.\n         */\n        this.value = '';\n        /**\n         * Handles the focus behavior for the input OTP component.\n         *\n         * Focus behavior:\n         * 1. Keyboard navigation: Allow normal focus movement\n         * 2. Mouse click:\n         *    - If clicked box has value: Focus that box\n         *    - If clicked box is empty: Focus first empty box\n         *\n         * Emits the `ionFocus` event when the input group gains focus.\n         */\n        this.onFocus = (index) => (event) => {\n            var _a;\n            const { inputRefs } = this;\n            // Only emit ionFocus and set the focusedValue when the\n            // component first gains focus\n            if (!this.hasFocus) {\n                this.ionFocus.emit(event);\n                this.focusedValue = this.value;\n            }\n            this.hasFocus = true;\n            let finalIndex = index;\n            if (!this.isKeyboardNavigation) {\n                // If the clicked box has a value, focus it\n                // Otherwise focus the first empty box\n                const targetIndex = this.inputValues[index] ? index : this.getFirstEmptyIndex();\n                finalIndex = targetIndex === -1 ? this.length - 1 : targetIndex;\n                // Focus the target box\n                (_a = this.inputRefs[finalIndex]) === null || _a === void 0 ? void 0 : _a.focus();\n            }\n            // Update tabIndexes to match the focused box\n            inputRefs.forEach((input, i) => {\n                input.tabIndex = i === finalIndex ? 0 : -1;\n            });\n            // Reset the keyboard navigation flag\n            this.isKeyboardNavigation = false;\n        };\n        /**\n         * Handles the blur behavior for the input OTP component.\n         * Emits the `ionBlur` event when the input group loses focus.\n         */\n        this.onBlur = (event) => {\n            const { inputRefs } = this;\n            const relatedTarget = event.relatedTarget;\n            // Do not emit blur if we're moving to another input box in the same component\n            const isInternalFocus = relatedTarget != null && inputRefs.includes(relatedTarget);\n            if (!isInternalFocus) {\n                this.hasFocus = false;\n                // Reset tabIndexes when focus leaves the component\n                this.updateTabIndexes();\n                // Always emit ionBlur when focus leaves the component\n                this.ionBlur.emit(event);\n                // Only emit ionChange if the value has actually changed\n                if (this.focusedValue !== this.value) {\n                    this.emitIonChange(event);\n                }\n            }\n        };\n        /**\n         * Handles keyboard navigation for the OTP component.\n         *\n         * Navigation:\n         * - Backspace: Clears current input and moves to previous box if empty\n         * - Arrow Left/Right: Moves focus between input boxes\n         * - Tab: Allows normal tab navigation between components\n         */\n        this.onKeyDown = (index) => (event) => {\n            const { length } = this;\n            const rtl = isRTL(this.el);\n            const input = event.target;\n            // Meta shortcuts are used to copy, paste, and select text\n            // We don't want to handle these keys here\n            const metaShortcuts = ['a', 'c', 'v', 'x', 'r', 'z', 'y'];\n            const isTextSelection = input.selectionStart !== input.selectionEnd;\n            // Return if the key is a meta shortcut or the input value\n            // text is selected and let the onPaste / onInput handler manage it\n            if (isTextSelection || ((event.metaKey || event.ctrlKey) && metaShortcuts.includes(event.key.toLowerCase()))) {\n                return;\n            }\n            if (event.key === 'Backspace') {\n                if (this.inputValues[index]) {\n                    // Shift all values to the right of the current index left by one\n                    for (let i = index; i < length - 1; i++) {\n                        this.inputValues[i] = this.inputValues[i + 1];\n                    }\n                    // Clear the last box\n                    this.inputValues[length - 1] = '';\n                    // Update all inputRefs to match inputValues\n                    for (let i = 0; i < length; i++) {\n                        this.inputRefs[i].value = this.inputValues[i] || '';\n                    }\n                    this.updateValue(event);\n                    event.preventDefault();\n                }\n                else if (!this.inputValues[index] && index > 0) {\n                    // If current input is empty, move to previous input\n                    this.focusPrevious(index);\n                }\n            }\n            else if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {\n                this.isKeyboardNavigation = true;\n                event.preventDefault();\n                const isLeft = event.key === 'ArrowLeft';\n                const shouldMoveNext = (isLeft && rtl) || (!isLeft && !rtl);\n                // Only allow moving to the next input if the current has a value\n                if (shouldMoveNext) {\n                    if (this.inputValues[index] && index < length - 1) {\n                        this.focusNext(index);\n                    }\n                }\n                else {\n                    this.focusPrevious(index);\n                }\n            }\n            else if (event.key === 'Tab') {\n                this.isKeyboardNavigation = true;\n                // Let all tab events proceed normally\n                return;\n            }\n        };\n        /**\n         * Processes all input scenarios for each input box.\n         *\n         * This function manages:\n         * 1. Autofill handling\n         * 2. Input validation\n         * 3. Full selection replacement or typing in an empty box\n         * 4. Inserting in the middle with available space (shifting)\n         * 5. Single character replacement\n         */\n        this.onInput = (index) => (event) => {\n            var _a, _b;\n            const { length, validKeyPattern } = this;\n            const input = event.target;\n            const value = input.value;\n            const previousValue = this.previousInputValues[index] || '';\n            // 1. Autofill handling\n            // If the length of the value increases by more than 1 from the previous\n            // value, treat this as autofill. This is to prevent the case where the\n            // user is typing a single character into an input box containing a value\n            // as that will trigger this function with a value length of 2 characters.\n            const isAutofill = value.length - previousValue.length > 1;\n            if (isAutofill) {\n                // Distribute valid characters across input boxes\n                const validChars = value\n                    .split('')\n                    .filter((char) => validKeyPattern.test(char))\n                    .slice(0, length);\n                // If there are no valid characters coming from the\n                // autofill, all input refs have to be cleared after the\n                // browser has finished the autofill behavior\n                if (validChars.length === 0) {\n                    requestAnimationFrame(() => {\n                        this.inputRefs.forEach((input) => {\n                            input.value = '';\n                        });\n                    });\n                }\n                for (let i = 0; i < length; i++) {\n                    this.inputValues[i] = validChars[i] || '';\n                    this.inputRefs[i].value = validChars[i] || '';\n                }\n                this.updateValue(event);\n                // Focus the first empty input box or the last input box if all boxes\n                // are filled after a small delay to ensure the input boxes have been\n                // updated before moving the focus\n                setTimeout(() => {\n                    var _a;\n                    const nextIndex = validChars.length < length ? validChars.length : length - 1;\n                    (_a = this.inputRefs[nextIndex]) === null || _a === void 0 ? void 0 : _a.focus();\n                }, 20);\n                this.previousInputValues = [...this.inputValues];\n                return;\n            }\n            // 2. Input validation\n            // If the character entered is invalid (does not match the pattern),\n            // restore the previous value and exit\n            if (value.length > 0 && !validKeyPattern.test(value[value.length - 1])) {\n                input.value = this.inputValues[index] || '';\n                this.previousInputValues = [...this.inputValues];\n                return;\n            }\n            // 3. Full selection replacement or typing in an empty box\n            // If the user selects all text in the input box and types, or if the\n            // input box is empty, replace only this input box. If the box is empty,\n            // move to the next box, otherwise stay focused on this box.\n            const isAllSelected = input.selectionStart === 0 && input.selectionEnd === value.length;\n            const isEmpty = !this.inputValues[index];\n            if (isAllSelected || isEmpty) {\n                this.inputValues[index] = value;\n                input.value = value;\n                this.updateValue(event);\n                this.focusNext(index);\n                this.previousInputValues = [...this.inputValues];\n                return;\n            }\n            // 4. Inserting in the middle with available space (shifting)\n            // If typing in a filled input box and there are empty boxes at the end,\n            // shift all values starting at the current box to the right, and insert\n            // the new character at the current box.\n            const hasAvailableBoxAtEnd = this.inputValues[this.inputValues.length - 1] === '';\n            if (this.inputValues[index] && hasAvailableBoxAtEnd && value.length === 2) {\n                // Get the inserted character (from event or by diffing value/previousValue)\n                let newChar = event.data;\n                if (!newChar) {\n                    newChar = value.split('').find((c, i) => c !== previousValue[i]) || value[value.length - 1];\n                }\n                // Validate the new character before shifting\n                if (!validKeyPattern.test(newChar)) {\n                    input.value = this.inputValues[index] || '';\n                    this.previousInputValues = [...this.inputValues];\n                    return;\n                }\n                // Shift values right from the end to the insertion point\n                for (let i = this.inputValues.length - 1; i > index; i--) {\n                    this.inputValues[i] = this.inputValues[i - 1];\n                    this.inputRefs[i].value = this.inputValues[i] || '';\n                }\n                this.inputValues[index] = newChar;\n                this.inputRefs[index].value = newChar;\n                this.updateValue(event);\n                this.previousInputValues = [...this.inputValues];\n                return;\n            }\n            // 5. Single character replacement\n            // Handles replacing a single character in a box containing a value based\n            // on the cursor position. We need the cursor position to determine which\n            // character was the last character typed. For example, if the user types \"2\"\n            // in an input box with the cursor at the beginning of the value of \"6\",\n            // the value will be \"26\", but we want to grab the \"2\" as the last character\n            // typed.\n            const cursorPos = (_a = input.selectionStart) !== null && _a !== void 0 ? _a : value.length;\n            const newCharIndex = cursorPos - 1;\n            const newChar = (_b = value[newCharIndex]) !== null && _b !== void 0 ? _b : value[0];\n            // Check if the new character is valid before updating the value\n            if (!validKeyPattern.test(newChar)) {\n                input.value = this.inputValues[index] || '';\n                this.previousInputValues = [...this.inputValues];\n                return;\n            }\n            this.inputValues[index] = newChar;\n            input.value = newChar;\n            this.updateValue(event);\n            this.previousInputValues = [...this.inputValues];\n        };\n        /**\n         * Handles pasting text into the input OTP component.\n         * This function prevents the default paste behavior and\n         * validates the pasted text against the allowed pattern.\n         * It then updates the value of the input group and focuses\n         * the next empty input after pasting.\n         */\n        this.onPaste = (event) => {\n            var _a, _b;\n            const { inputRefs, length, validKeyPattern } = this;\n            event.preventDefault();\n            const pastedText = (_a = event.clipboardData) === null || _a === void 0 ? void 0 : _a.getData('text');\n            // If there is no pasted text, still emit the input change event\n            // because this is how the native input element behaves\n            // but return early because there is nothing to paste.\n            if (!pastedText) {\n                this.emitIonInput(event);\n                return;\n            }\n            const validChars = pastedText\n                .split('')\n                .filter((char) => validKeyPattern.test(char))\n                .slice(0, length);\n            // Always paste starting at the first box\n            validChars.forEach((char, index) => {\n                if (index < length) {\n                    this.inputRefs[index].value = char;\n                    this.inputValues[index] = char;\n                }\n            });\n            // Update the value so that all input boxes are updated\n            this.value = validChars.join('');\n            this.updateValue(event);\n            // Focus the next empty input after pasting\n            // If all boxes are filled, focus the last input\n            const nextEmptyIndex = validChars.length < length ? validChars.length : length - 1;\n            (_b = inputRefs[nextEmptyIndex]) === null || _b === void 0 ? void 0 : _b.focus();\n        };\n    }\n    /**\n     * Sets focus to an input box.\n     * @param index - The index of the input box to focus (0-based).\n     * If provided and the input box has a value, the input box at that index will be focused.\n     * Otherwise, the first empty input box or the last input if all are filled will be focused.\n     */\n    async setFocus(index) {\n        var _a, _b;\n        if (typeof index === 'number') {\n            const validIndex = Math.max(0, Math.min(index, this.length - 1));\n            (_a = this.inputRefs[validIndex]) === null || _a === void 0 ? void 0 : _a.focus();\n        }\n        else {\n            const tabbableIndex = this.getTabbableIndex();\n            (_b = this.inputRefs[tabbableIndex]) === null || _b === void 0 ? void 0 : _b.focus();\n        }\n    }\n    valueChanged() {\n        this.initializeValues();\n        this.updateTabIndexes();\n    }\n    /**\n     * Processes the separators prop into an array of numbers.\n     *\n     * If the separators prop is not provided, returns an empty array.\n     * If the separators prop is 'all', returns an array of all valid positions (1 to length-1).\n     * If the separators prop is an array, returns it as is.\n     * If the separators prop is a string, splits it by commas and parses each part as a number.\n     *\n     * If the separators are greater than the input length, it will warn and ignore the separators.\n     */\n    processSeparators() {\n        const { separators, length } = this;\n        if (separators === undefined) {\n            this.parsedSeparators = [];\n            return;\n        }\n        if (typeof separators === 'string' && separators !== 'all') {\n            const isValidFormat = /^(\\d+)(,\\d+)*$/.test(separators);\n            if (!isValidFormat) {\n                printIonWarning(`[ion-input-otp] - Invalid separators format. Expected a comma-separated list of numbers, an array of numbers, or \"all\". Received: ${separators}`, this.el);\n                this.parsedSeparators = [];\n                return;\n            }\n        }\n        let separatorValues;\n        if (separators === 'all') {\n            separatorValues = Array.from({ length: length - 1 }, (_, i) => i + 1);\n        }\n        else if (Array.isArray(separators)) {\n            separatorValues = separators;\n        }\n        else {\n            separatorValues = separators\n                .split(',')\n                .map((pos) => parseInt(pos, 10))\n                .filter((pos) => !isNaN(pos));\n        }\n        // Check for duplicate separator positions\n        const duplicates = separatorValues.filter((pos, index) => separatorValues.indexOf(pos) !== index);\n        if (duplicates.length > 0) {\n            printIonWarning(`[ion-input-otp] - Duplicate separator positions are not allowed. Received: ${separators}`, this.el);\n        }\n        const invalidSeparators = separatorValues.filter((pos) => pos > length);\n        if (invalidSeparators.length > 0) {\n            printIonWarning(`[ion-input-otp] - The following separator positions are greater than the input length (${length}): ${invalidSeparators.join(', ')}. These separators will be ignored.`, this.el);\n        }\n        this.parsedSeparators = separatorValues.filter((pos) => pos <= length);\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n        this.processSeparators();\n        this.initializeValues();\n    }\n    componentDidLoad() {\n        this.updateTabIndexes();\n    }\n    /**\n     * Get the regex pattern for allowed characters.\n     * If a pattern is provided, use it to create a regex pattern\n     * Otherwise, use the default regex pattern based on type\n     */\n    get validKeyPattern() {\n        return new RegExp(`^${this.getPattern()}$`, 'u');\n    }\n    /**\n     * Gets the string pattern to pass to the input element\n     * and use in the regex for allowed characters.\n     */\n    getPattern() {\n        const { pattern, type } = this;\n        if (pattern) {\n            return pattern;\n        }\n        return type === 'number' ? '[\\\\p{N}]' : '[\\\\p{L}\\\\p{N}]';\n    }\n    /**\n     * Get the default value for inputmode.\n     * If inputmode is provided, use it.\n     * Otherwise, use the default inputmode based on type\n     */\n    getInputmode() {\n        const { inputmode } = this;\n        if (inputmode) {\n            return inputmode;\n        }\n        if (this.type == 'number') {\n            return 'numeric';\n        }\n        else {\n            return 'text';\n        }\n    }\n    /**\n     * Initializes the input values array based on the current value prop.\n     * This splits the value into individual characters and validates them against\n     * the allowed pattern. The values are then used as the values in the native\n     * input boxes and the value of the input group is updated.\n     */\n    initializeValues() {\n        // Clear all input values\n        this.inputValues = Array(this.length).fill('');\n        // If the value is null, undefined, or an empty string, return\n        if (this.value == null || String(this.value).length === 0) {\n            return;\n        }\n        // Split the value into individual characters and validate\n        // them against the allowed pattern\n        const chars = String(this.value).split('').slice(0, this.length);\n        chars.forEach((char, index) => {\n            if (this.validKeyPattern.test(char)) {\n                this.inputValues[index] = char;\n            }\n        });\n        // Update the value without emitting events\n        this.value = this.inputValues.join('');\n        this.previousInputValues = [...this.inputValues];\n    }\n    /**\n     * Updates the value of the input group.\n     * This updates the value of the input group and emits an `ionChange` event.\n     * If all of the input boxes are filled, it emits an `ionComplete` event.\n     */\n    updateValue(event) {\n        const { inputValues, length } = this;\n        const newValue = inputValues.join('');\n        this.value = newValue;\n        this.emitIonInput(event);\n        if (newValue.length === length) {\n            this.ionComplete.emit({ value: newValue });\n        }\n    }\n    /**\n     * Emits an `ionChange` event.\n     * This API should be called for user committed changes.\n     * This API should not be used for external value changes.\n     */\n    emitIonChange(event) {\n        const { value } = this;\n        // Checks for both null and undefined values\n        const newValue = value == null ? value : value.toString();\n        this.ionChange.emit({ value: newValue, event });\n    }\n    /**\n     * Emits an `ionInput` event.\n     * This is used to emit the input value when the user types,\n     * backspaces, or pastes.\n     */\n    emitIonInput(event) {\n        const { value } = this;\n        // Checks for both null and undefined values\n        const newValue = value == null ? value : value.toString();\n        this.ionInput.emit({ value: newValue, event });\n    }\n    /**\n     * Focuses the next input box.\n     */\n    focusNext(currentIndex) {\n        var _a;\n        const { inputRefs, length } = this;\n        if (currentIndex < length - 1) {\n            (_a = inputRefs[currentIndex + 1]) === null || _a === void 0 ? void 0 : _a.focus();\n        }\n    }\n    /**\n     * Focuses the previous input box.\n     */\n    focusPrevious(currentIndex) {\n        var _a;\n        const { inputRefs } = this;\n        if (currentIndex > 0) {\n            (_a = inputRefs[currentIndex - 1]) === null || _a === void 0 ? void 0 : _a.focus();\n        }\n    }\n    /**\n     * Searches through the input values and returns the index\n     * of the first empty input.\n     * Returns -1 if all inputs are filled.\n     */\n    getFirstEmptyIndex() {\n        var _a;\n        const { inputValues, length } = this;\n        // Create an array of the same length as the input OTP\n        // and fill it with the input values\n        const values = Array.from({ length }, (_, i) => inputValues[i] || '');\n        return (_a = values.findIndex((value) => !value || value === '')) !== null && _a !== void 0 ? _a : -1;\n    }\n    /**\n     * Returns the index of the input that should be tabbed to.\n     * If all inputs are filled, returns the last input's index.\n     * Otherwise, returns the index of the first empty input.\n     */\n    getTabbableIndex() {\n        const { length } = this;\n        const firstEmptyIndex = this.getFirstEmptyIndex();\n        return firstEmptyIndex === -1 ? length - 1 : firstEmptyIndex;\n    }\n    /**\n     * Updates the tabIndexes for the input boxes.\n     * This is used to ensure that the correct input is\n     * focused when the user navigates using the tab key.\n     */\n    updateTabIndexes() {\n        const { inputRefs, inputValues, length } = this;\n        // Find first empty index after any filled boxes\n        let firstEmptyIndex = -1;\n        for (let i = 0; i < length; i++) {\n            if (!inputValues[i] || inputValues[i] === '') {\n                firstEmptyIndex = i;\n                break;\n            }\n        }\n        // Update tabIndex and aria-hidden for all inputs\n        inputRefs.forEach((input, index) => {\n            const shouldBeTabbable = firstEmptyIndex === -1 ? index === length - 1 : firstEmptyIndex === index;\n            input.tabIndex = shouldBeTabbable ? 0 : -1;\n            // If the input is empty and not the first empty input,\n            // it should be hidden from screen readers.\n            const isEmpty = !inputValues[index] || inputValues[index] === '';\n            input.setAttribute('aria-hidden', isEmpty && !shouldBeTabbable ? 'true' : 'false');\n        });\n    }\n    /**\n     * Determines if a separator should be shown for a given index by\n     * checking if the index is included in the parsed separators array.\n     */\n    showSeparator(index) {\n        const { length } = this;\n        return this.parsedSeparators.includes(index + 1) && index < length - 1;\n    }\n    render() {\n        var _a, _b;\n        const { autocapitalize, color, disabled, el, fill, hasFocus, inheritedAttributes, inputId, inputRefs, inputValues, length, readonly, shape, size, } = this;\n        const mode = getIonMode(this);\n        const inputmode = this.getInputmode();\n        const tabbableIndex = this.getTabbableIndex();\n        const pattern = this.getPattern();\n        const hasDescription = ((_b = (_a = el.querySelector('.input-otp-description')) === null || _a === void 0 ? void 0 : _a.textContent) === null || _b === void 0 ? void 0 : _b.trim()) !== '';\n        return (h(Host, { key: 'f15a29fb17b681ef55885ca36d3d5f899cbaca83', class: createColorClasses(color, {\n                [mode]: true,\n                'has-focus': hasFocus,\n                [`input-otp-size-${size}`]: true,\n                [`input-otp-shape-${shape}`]: true,\n                [`input-otp-fill-${fill}`]: true,\n                'input-otp-disabled': disabled,\n                'input-otp-readonly': readonly,\n            }) }, h(\"div\", Object.assign({ key: 'd7e1d4edd8aafcf2ed4313301287282e90fc7e82', role: \"group\", \"aria-label\": \"One-time password input\", class: \"input-otp-group\" }, inheritedAttributes), Array.from({ length }).map((_, index) => (h(Fragment, null, h(\"div\", { class: \"native-wrapper\" }, h(\"input\", { class: \"native-input\", id: `${inputId}-${index}`, \"aria-label\": `Input ${index + 1} of ${length}`, type: \"text\", autoCapitalize: autocapitalize, inputmode: inputmode, pattern: pattern, disabled: disabled, readOnly: readonly, tabIndex: index === tabbableIndex ? 0 : -1, value: inputValues[index] || '', autocomplete: \"one-time-code\", ref: (el) => (inputRefs[index] = el), onInput: this.onInput(index), onBlur: this.onBlur, onFocus: this.onFocus(index), onKeyDown: this.onKeyDown(index), onPaste: this.onPaste })), this.showSeparator(index) && h(\"div\", { class: \"input-otp-separator\" }))))), h(\"div\", { key: '3724a3159d02860971879a906092f9965f5a7c47', class: {\n                'input-otp-description': true,\n                'input-otp-description-hidden': !hasDescription,\n            } }, h(\"slot\", { key: '11baa2624926a08274508afe0833d9237a8dc35c' }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"value\": [\"valueChanged\"],\n        \"separators\": [\"processSeparators\"],\n        \"length\": [\"processSeparators\"]\n    }; }\n};\nlet inputIds = 0;\nInputOTP.style = {\n    ios: inputOtpIosCss,\n    md: inputOtpMdCss\n};\n\nexport { InputOTP as ion_input_otp };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAClK,SAASC,CAAC,IAAIC,qBAAqB,QAAQ,uBAAuB;AAClE,SAASD,CAAC,IAAIE,KAAK,QAAQ,mBAAmB;AAC9C,SAASC,CAAC,IAAIC,kBAAkB,QAAQ,qBAAqB;AAE7D,MAAMC,cAAc,GAAG,g1MAAg1M;AAEv2M,MAAMC,aAAa,GAAG,stMAAstM;AAE5uM,MAAMC,QAAQ,GAAG,MAAM;EACnBC,WAAWA,CAACC,OAAO,EAAE;IACjBvB,gBAAgB,CAAC,IAAI,EAAEuB,OAAO,CAAC;IAC/B,IAAI,CAACC,QAAQ,GAAGtB,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACuB,SAAS,GAAGvB,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACwB,WAAW,GAAGxB,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IACtD,IAAI,CAACyB,OAAO,GAAGzB,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC0B,QAAQ,GAAG1B,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC2B,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,iBAAiBC,QAAQ,EAAE,EAAE;IAC5C,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,SAAS;IACrB;AACR;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,CAAC;IACf;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,OAAO;IACpB;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB;AACR;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,EAAE;IACf;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,OAAO,GAAIC,KAAK,IAAMC,KAAK,IAAK;MACjC,IAAIC,EAAE;MACN,MAAM;QAAEpB;MAAU,CAAC,GAAG,IAAI;MAC1B;MACA;MACA,IAAI,CAAC,IAAI,CAACM,QAAQ,EAAE;QAChB,IAAI,CAACR,QAAQ,CAACuB,IAAI,CAACF,KAAK,CAAC;QACzB,IAAI,CAACG,YAAY,GAAG,IAAI,CAACN,KAAK;MAClC;MACA,IAAI,CAACV,QAAQ,GAAG,IAAI;MACpB,IAAIiB,UAAU,GAAGL,KAAK;MACtB,IAAI,CAAC,IAAI,CAACd,oBAAoB,EAAE;QAC5B;QACA;QACA,MAAMoB,WAAW,GAAG,IAAI,CAACnB,WAAW,CAACa,KAAK,CAAC,GAAGA,KAAK,GAAG,IAAI,CAACO,kBAAkB,CAAC,CAAC;QAC/EF,UAAU,GAAGC,WAAW,KAAK,CAAC,CAAC,GAAG,IAAI,CAACb,MAAM,GAAG,CAAC,GAAGa,WAAW;QAC/D;QACA,CAACJ,EAAE,GAAG,IAAI,CAACpB,SAAS,CAACuB,UAAU,CAAC,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,KAAK,CAAC,CAAC;MACrF;MACA;MACA1B,SAAS,CAAC2B,OAAO,CAAC,CAACC,KAAK,EAAE5C,CAAC,KAAK;QAC5B4C,KAAK,CAACC,QAAQ,GAAG7C,CAAC,KAAKuC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;MAC9C,CAAC,CAAC;MACF;MACA,IAAI,CAACnB,oBAAoB,GAAG,KAAK;IACrC,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAAC0B,MAAM,GAAIX,KAAK,IAAK;MACrB,MAAM;QAAEnB;MAAU,CAAC,GAAG,IAAI;MAC1B,MAAM+B,aAAa,GAAGZ,KAAK,CAACY,aAAa;MACzC;MACA,MAAMC,eAAe,GAAGD,aAAa,IAAI,IAAI,IAAI/B,SAAS,CAACiC,QAAQ,CAACF,aAAa,CAAC;MAClF,IAAI,CAACC,eAAe,EAAE;QAClB,IAAI,CAAC1B,QAAQ,GAAG,KAAK;QACrB;QACA,IAAI,CAAC4B,gBAAgB,CAAC,CAAC;QACvB;QACA,IAAI,CAACrC,OAAO,CAACwB,IAAI,CAACF,KAAK,CAAC;QACxB;QACA,IAAI,IAAI,CAACG,YAAY,KAAK,IAAI,CAACN,KAAK,EAAE;UAClC,IAAI,CAACmB,aAAa,CAAChB,KAAK,CAAC;QAC7B;MACJ;IACJ,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACiB,SAAS,GAAIlB,KAAK,IAAMC,KAAK,IAAK;MACnC,MAAM;QAAER;MAAO,CAAC,GAAG,IAAI;MACvB,MAAM0B,GAAG,GAAGnD,KAAK,CAAC,IAAI,CAACoD,EAAE,CAAC;MAC1B,MAAMV,KAAK,GAAGT,KAAK,CAACoB,MAAM;MAC1B;MACA;MACA,MAAMC,aAAa,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MACzD,MAAMC,eAAe,GAAGb,KAAK,CAACc,cAAc,KAAKd,KAAK,CAACe,YAAY;MACnE;MACA;MACA,IAAIF,eAAe,IAAK,CAACtB,KAAK,CAACyB,OAAO,IAAIzB,KAAK,CAAC0B,OAAO,KAAKL,aAAa,CAACP,QAAQ,CAACd,KAAK,CAAC2B,GAAG,CAACC,WAAW,CAAC,CAAC,CAAE,EAAE;QAC1G;MACJ;MACA,IAAI5B,KAAK,CAAC2B,GAAG,KAAK,WAAW,EAAE;QAC3B,IAAI,IAAI,CAACzC,WAAW,CAACa,KAAK,CAAC,EAAE;UACzB;UACA,KAAK,IAAIlC,CAAC,GAAGkC,KAAK,EAAElC,CAAC,GAAG2B,MAAM,GAAG,CAAC,EAAE3B,CAAC,EAAE,EAAE;YACrC,IAAI,CAACqB,WAAW,CAACrB,CAAC,CAAC,GAAG,IAAI,CAACqB,WAAW,CAACrB,CAAC,GAAG,CAAC,CAAC;UACjD;UACA;UACA,IAAI,CAACqB,WAAW,CAACM,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE;UACjC;UACA,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2B,MAAM,EAAE3B,CAAC,EAAE,EAAE;YAC7B,IAAI,CAACgB,SAAS,CAAChB,CAAC,CAAC,CAACgC,KAAK,GAAG,IAAI,CAACX,WAAW,CAACrB,CAAC,CAAC,IAAI,EAAE;UACvD;UACA,IAAI,CAACgE,WAAW,CAAC7B,KAAK,CAAC;UACvBA,KAAK,CAAC8B,cAAc,CAAC,CAAC;QAC1B,CAAC,MACI,IAAI,CAAC,IAAI,CAAC5C,WAAW,CAACa,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;UAC5C;UACA,IAAI,CAACgC,aAAa,CAAChC,KAAK,CAAC;QAC7B;MACJ,CAAC,MACI,IAAIC,KAAK,CAAC2B,GAAG,KAAK,WAAW,IAAI3B,KAAK,CAAC2B,GAAG,KAAK,YAAY,EAAE;QAC9D,IAAI,CAAC1C,oBAAoB,GAAG,IAAI;QAChCe,KAAK,CAAC8B,cAAc,CAAC,CAAC;QACtB,MAAME,MAAM,GAAGhC,KAAK,CAAC2B,GAAG,KAAK,WAAW;QACxC,MAAMM,cAAc,GAAID,MAAM,IAAId,GAAG,IAAM,CAACc,MAAM,IAAI,CAACd,GAAI;QAC3D;QACA,IAAIe,cAAc,EAAE;UAChB,IAAI,IAAI,CAAC/C,WAAW,CAACa,KAAK,CAAC,IAAIA,KAAK,GAAGP,MAAM,GAAG,CAAC,EAAE;YAC/C,IAAI,CAAC0C,SAAS,CAACnC,KAAK,CAAC;UACzB;QACJ,CAAC,MACI;UACD,IAAI,CAACgC,aAAa,CAAChC,KAAK,CAAC;QAC7B;MACJ,CAAC,MACI,IAAIC,KAAK,CAAC2B,GAAG,KAAK,KAAK,EAAE;QAC1B,IAAI,CAAC1C,oBAAoB,GAAG,IAAI;QAChC;QACA;MACJ;IACJ,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACkD,OAAO,GAAIpC,KAAK,IAAMC,KAAK,IAAK;MACjC,IAAIC,EAAE,EAAEmC,EAAE;MACV,MAAM;QAAE5C,MAAM;QAAE6C;MAAgB,CAAC,GAAG,IAAI;MACxC,MAAM5B,KAAK,GAAGT,KAAK,CAACoB,MAAM;MAC1B,MAAMvB,KAAK,GAAGY,KAAK,CAACZ,KAAK;MACzB,MAAMyC,aAAa,GAAG,IAAI,CAAClD,mBAAmB,CAACW,KAAK,CAAC,IAAI,EAAE;MAC3D;MACA;MACA;MACA;MACA;MACA,MAAMwC,UAAU,GAAG1C,KAAK,CAACL,MAAM,GAAG8C,aAAa,CAAC9C,MAAM,GAAG,CAAC;MAC1D,IAAI+C,UAAU,EAAE;QACZ;QACA,MAAMC,UAAU,GAAG3C,KAAK,CACnB4C,KAAK,CAAC,EAAE,CAAC,CACTC,MAAM,CAAEC,IAAI,IAAKN,eAAe,CAACO,IAAI,CAACD,IAAI,CAAC,CAAC,CAC5CE,KAAK,CAAC,CAAC,EAAErD,MAAM,CAAC;QACrB;QACA;QACA;QACA,IAAIgD,UAAU,CAAChD,MAAM,KAAK,CAAC,EAAE;UACzBsD,qBAAqB,CAAC,MAAM;YACxB,IAAI,CAACjE,SAAS,CAAC2B,OAAO,CAAEC,KAAK,IAAK;cAC9BA,KAAK,CAACZ,KAAK,GAAG,EAAE;YACpB,CAAC,CAAC;UACN,CAAC,CAAC;QACN;QACA,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2B,MAAM,EAAE3B,CAAC,EAAE,EAAE;UAC7B,IAAI,CAACqB,WAAW,CAACrB,CAAC,CAAC,GAAG2E,UAAU,CAAC3E,CAAC,CAAC,IAAI,EAAE;UACzC,IAAI,CAACgB,SAAS,CAAChB,CAAC,CAAC,CAACgC,KAAK,GAAG2C,UAAU,CAAC3E,CAAC,CAAC,IAAI,EAAE;QACjD;QACA,IAAI,CAACgE,WAAW,CAAC7B,KAAK,CAAC;QACvB;QACA;QACA;QACA+C,UAAU,CAAC,MAAM;UACb,IAAI9C,EAAE;UACN,MAAM+C,SAAS,GAAGR,UAAU,CAAChD,MAAM,GAAGA,MAAM,GAAGgD,UAAU,CAAChD,MAAM,GAAGA,MAAM,GAAG,CAAC;UAC7E,CAACS,EAAE,GAAG,IAAI,CAACpB,SAAS,CAACmE,SAAS,CAAC,MAAM,IAAI,IAAI/C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,KAAK,CAAC,CAAC;QACpF,CAAC,EAAE,EAAE,CAAC;QACN,IAAI,CAACnB,mBAAmB,GAAG,CAAC,GAAG,IAAI,CAACF,WAAW,CAAC;QAChD;MACJ;MACA;MACA;MACA;MACA,IAAIW,KAAK,CAACL,MAAM,GAAG,CAAC,IAAI,CAAC6C,eAAe,CAACO,IAAI,CAAC/C,KAAK,CAACA,KAAK,CAACL,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;QACpEiB,KAAK,CAACZ,KAAK,GAAG,IAAI,CAACX,WAAW,CAACa,KAAK,CAAC,IAAI,EAAE;QAC3C,IAAI,CAACX,mBAAmB,GAAG,CAAC,GAAG,IAAI,CAACF,WAAW,CAAC;QAChD;MACJ;MACA;MACA;MACA;MACA;MACA,MAAM+D,aAAa,GAAGxC,KAAK,CAACc,cAAc,KAAK,CAAC,IAAId,KAAK,CAACe,YAAY,KAAK3B,KAAK,CAACL,MAAM;MACvF,MAAM0D,OAAO,GAAG,CAAC,IAAI,CAAChE,WAAW,CAACa,KAAK,CAAC;MACxC,IAAIkD,aAAa,IAAIC,OAAO,EAAE;QAC1B,IAAI,CAAChE,WAAW,CAACa,KAAK,CAAC,GAAGF,KAAK;QAC/BY,KAAK,CAACZ,KAAK,GAAGA,KAAK;QACnB,IAAI,CAACgC,WAAW,CAAC7B,KAAK,CAAC;QACvB,IAAI,CAACkC,SAAS,CAACnC,KAAK,CAAC;QACrB,IAAI,CAACX,mBAAmB,GAAG,CAAC,GAAG,IAAI,CAACF,WAAW,CAAC;QAChD;MACJ;MACA;MACA;MACA;MACA;MACA,MAAMiE,oBAAoB,GAAG,IAAI,CAACjE,WAAW,CAAC,IAAI,CAACA,WAAW,CAACM,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE;MACjF,IAAI,IAAI,CAACN,WAAW,CAACa,KAAK,CAAC,IAAIoD,oBAAoB,IAAItD,KAAK,CAACL,MAAM,KAAK,CAAC,EAAE;QACvE;QACA,IAAI4D,OAAO,GAAGpD,KAAK,CAACqD,IAAI;QACxB,IAAI,CAACD,OAAO,EAAE;UACVA,OAAO,GAAGvD,KAAK,CAAC4C,KAAK,CAAC,EAAE,CAAC,CAACa,IAAI,CAAC,CAACtF,CAAC,EAAEH,CAAC,KAAKG,CAAC,KAAKsE,aAAa,CAACzE,CAAC,CAAC,CAAC,IAAIgC,KAAK,CAACA,KAAK,CAACL,MAAM,GAAG,CAAC,CAAC;QAC/F;QACA;QACA,IAAI,CAAC6C,eAAe,CAACO,IAAI,CAACQ,OAAO,CAAC,EAAE;UAChC3C,KAAK,CAACZ,KAAK,GAAG,IAAI,CAACX,WAAW,CAACa,KAAK,CAAC,IAAI,EAAE;UAC3C,IAAI,CAACX,mBAAmB,GAAG,CAAC,GAAG,IAAI,CAACF,WAAW,CAAC;UAChD;QACJ;QACA;QACA,KAAK,IAAIrB,CAAC,GAAG,IAAI,CAACqB,WAAW,CAACM,MAAM,GAAG,CAAC,EAAE3B,CAAC,GAAGkC,KAAK,EAAElC,CAAC,EAAE,EAAE;UACtD,IAAI,CAACqB,WAAW,CAACrB,CAAC,CAAC,GAAG,IAAI,CAACqB,WAAW,CAACrB,CAAC,GAAG,CAAC,CAAC;UAC7C,IAAI,CAACgB,SAAS,CAAChB,CAAC,CAAC,CAACgC,KAAK,GAAG,IAAI,CAACX,WAAW,CAACrB,CAAC,CAAC,IAAI,EAAE;QACvD;QACA,IAAI,CAACqB,WAAW,CAACa,KAAK,CAAC,GAAGqD,OAAO;QACjC,IAAI,CAACvE,SAAS,CAACkB,KAAK,CAAC,CAACF,KAAK,GAAGuD,OAAO;QACrC,IAAI,CAACvB,WAAW,CAAC7B,KAAK,CAAC;QACvB,IAAI,CAACZ,mBAAmB,GAAG,CAAC,GAAG,IAAI,CAACF,WAAW,CAAC;QAChD;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMqE,SAAS,GAAG,CAACtD,EAAE,GAAGQ,KAAK,CAACc,cAAc,MAAM,IAAI,IAAItB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGJ,KAAK,CAACL,MAAM;MAC3F,MAAMgE,YAAY,GAAGD,SAAS,GAAG,CAAC;MAClC,MAAMH,OAAO,GAAG,CAAChB,EAAE,GAAGvC,KAAK,CAAC2D,YAAY,CAAC,MAAM,IAAI,IAAIpB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGvC,KAAK,CAAC,CAAC,CAAC;MACpF;MACA,IAAI,CAACwC,eAAe,CAACO,IAAI,CAACQ,OAAO,CAAC,EAAE;QAChC3C,KAAK,CAACZ,KAAK,GAAG,IAAI,CAACX,WAAW,CAACa,KAAK,CAAC,IAAI,EAAE;QAC3C,IAAI,CAACX,mBAAmB,GAAG,CAAC,GAAG,IAAI,CAACF,WAAW,CAAC;QAChD;MACJ;MACA,IAAI,CAACA,WAAW,CAACa,KAAK,CAAC,GAAGqD,OAAO;MACjC3C,KAAK,CAACZ,KAAK,GAAGuD,OAAO;MACrB,IAAI,CAACvB,WAAW,CAAC7B,KAAK,CAAC;MACvB,IAAI,CAACZ,mBAAmB,GAAG,CAAC,GAAG,IAAI,CAACF,WAAW,CAAC;IACpD,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACuE,OAAO,GAAIzD,KAAK,IAAK;MACtB,IAAIC,EAAE,EAAEmC,EAAE;MACV,MAAM;QAAEvD,SAAS;QAAEW,MAAM;QAAE6C;MAAgB,CAAC,GAAG,IAAI;MACnDrC,KAAK,CAAC8B,cAAc,CAAC,CAAC;MACtB,MAAM4B,UAAU,GAAG,CAACzD,EAAE,GAAGD,KAAK,CAAC2D,aAAa,MAAM,IAAI,IAAI1D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2D,OAAO,CAAC,MAAM,CAAC;MACrG;MACA;MACA;MACA,IAAI,CAACF,UAAU,EAAE;QACb,IAAI,CAACG,YAAY,CAAC7D,KAAK,CAAC;QACxB;MACJ;MACA,MAAMwC,UAAU,GAAGkB,UAAU,CACxBjB,KAAK,CAAC,EAAE,CAAC,CACTC,MAAM,CAAEC,IAAI,IAAKN,eAAe,CAACO,IAAI,CAACD,IAAI,CAAC,CAAC,CAC5CE,KAAK,CAAC,CAAC,EAAErD,MAAM,CAAC;MACrB;MACAgD,UAAU,CAAChC,OAAO,CAAC,CAACmC,IAAI,EAAE5C,KAAK,KAAK;QAChC,IAAIA,KAAK,GAAGP,MAAM,EAAE;UAChB,IAAI,CAACX,SAAS,CAACkB,KAAK,CAAC,CAACF,KAAK,GAAG8C,IAAI;UAClC,IAAI,CAACzD,WAAW,CAACa,KAAK,CAAC,GAAG4C,IAAI;QAClC;MACJ,CAAC,CAAC;MACF;MACA,IAAI,CAAC9C,KAAK,GAAG2C,UAAU,CAACsB,IAAI,CAAC,EAAE,CAAC;MAChC,IAAI,CAACjC,WAAW,CAAC7B,KAAK,CAAC;MACvB;MACA;MACA,MAAM+D,cAAc,GAAGvB,UAAU,CAAChD,MAAM,GAAGA,MAAM,GAAGgD,UAAU,CAAChD,MAAM,GAAGA,MAAM,GAAG,CAAC;MAClF,CAAC4C,EAAE,GAAGvD,SAAS,CAACkF,cAAc,CAAC,MAAM,IAAI,IAAI3B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC7B,KAAK,CAAC,CAAC;IACpF,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;EACUyD,QAAQA,CAACjE,KAAK,EAAE;IAAA,IAAAkE,KAAA;IAAA,OAAAC,iBAAA;MAClB,IAAIjE,EAAE,EAAEmC,EAAE;MACV,IAAI,OAAOrC,KAAK,KAAK,QAAQ,EAAE;QAC3B,MAAMoE,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACvE,KAAK,EAAEkE,KAAI,CAACzE,MAAM,GAAG,CAAC,CAAC,CAAC;QAChE,CAACS,EAAE,GAAGgE,KAAI,CAACpF,SAAS,CAACsF,UAAU,CAAC,MAAM,IAAI,IAAIlE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,KAAK,CAAC,CAAC;MACrF,CAAC,MACI;QACD,MAAMgE,aAAa,GAAGN,KAAI,CAACO,gBAAgB,CAAC,CAAC;QAC7C,CAACpC,EAAE,GAAG6B,KAAI,CAACpF,SAAS,CAAC0F,aAAa,CAAC,MAAM,IAAI,IAAInC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC7B,KAAK,CAAC,CAAC;MACxF;IAAC;EACL;EACAkE,YAAYA,CAAA,EAAG;IACX,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAAC3D,gBAAgB,CAAC,CAAC;EAC3B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI4D,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAEC,UAAU;MAAEpF;IAAO,CAAC,GAAG,IAAI;IACnC,IAAIoF,UAAU,KAAKC,SAAS,EAAE;MAC1B,IAAI,CAAC7F,gBAAgB,GAAG,EAAE;MAC1B;IACJ;IACA,IAAI,OAAO4F,UAAU,KAAK,QAAQ,IAAIA,UAAU,KAAK,KAAK,EAAE;MACxD,MAAME,aAAa,GAAG,gBAAgB,CAAClC,IAAI,CAACgC,UAAU,CAAC;MACvD,IAAI,CAACE,aAAa,EAAE;QAChB3H,eAAe,CAAC,qIAAqIyH,UAAU,EAAE,EAAE,IAAI,CAACzD,EAAE,CAAC;QAC3K,IAAI,CAACnC,gBAAgB,GAAG,EAAE;QAC1B;MACJ;IACJ;IACA,IAAI+F,eAAe;IACnB,IAAIH,UAAU,KAAK,KAAK,EAAE;MACtBG,eAAe,GAAGC,KAAK,CAACC,IAAI,CAAC;QAAEzF,MAAM,EAAEA,MAAM,GAAG;MAAE,CAAC,EAAE,CAAC0F,CAAC,EAAErH,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;IACzE,CAAC,MACI,IAAImH,KAAK,CAACG,OAAO,CAACP,UAAU,CAAC,EAAE;MAChCG,eAAe,GAAGH,UAAU;IAChC,CAAC,MACI;MACDG,eAAe,GAAGH,UAAU,CACvBnC,KAAK,CAAC,GAAG,CAAC,CACV2C,GAAG,CAAEC,GAAG,IAAKC,QAAQ,CAACD,GAAG,EAAE,EAAE,CAAC,CAAC,CAC/B3C,MAAM,CAAE2C,GAAG,IAAK,CAACE,KAAK,CAACF,GAAG,CAAC,CAAC;IACrC;IACA;IACA,MAAMG,UAAU,GAAGT,eAAe,CAACrC,MAAM,CAAC,CAAC2C,GAAG,EAAEtF,KAAK,KAAKgF,eAAe,CAACU,OAAO,CAACJ,GAAG,CAAC,KAAKtF,KAAK,CAAC;IACjG,IAAIyF,UAAU,CAAChG,MAAM,GAAG,CAAC,EAAE;MACvBrC,eAAe,CAAC,8EAA8EyH,UAAU,EAAE,EAAE,IAAI,CAACzD,EAAE,CAAC;IACxH;IACA,MAAMuE,iBAAiB,GAAGX,eAAe,CAACrC,MAAM,CAAE2C,GAAG,IAAKA,GAAG,GAAG7F,MAAM,CAAC;IACvE,IAAIkG,iBAAiB,CAAClG,MAAM,GAAG,CAAC,EAAE;MAC9BrC,eAAe,CAAC,0FAA0FqC,MAAM,MAAMkG,iBAAiB,CAAC5B,IAAI,CAAC,IAAI,CAAC,qCAAqC,EAAE,IAAI,CAAC3C,EAAE,CAAC;IACrM;IACA,IAAI,CAACnC,gBAAgB,GAAG+F,eAAe,CAACrC,MAAM,CAAE2C,GAAG,IAAKA,GAAG,IAAI7F,MAAM,CAAC;EAC1E;EACAmG,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC/G,mBAAmB,GAAGd,qBAAqB,CAAC,IAAI,CAACqD,EAAE,CAAC;IACzD,IAAI,CAACwD,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACD,gBAAgB,CAAC,CAAC;EAC3B;EACAkB,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC7E,gBAAgB,CAAC,CAAC;EAC3B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIsB,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAIwD,MAAM,CAAC,IAAI,IAAI,CAACC,UAAU,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;EACpD;EACA;AACJ;AACA;AACA;EACIA,UAAUA,CAAA,EAAG;IACT,MAAM;MAAEC,OAAO;MAAEnG;IAAK,CAAC,GAAG,IAAI;IAC9B,IAAImG,OAAO,EAAE;MACT,OAAOA,OAAO;IAClB;IACA,OAAOnG,IAAI,KAAK,QAAQ,GAAG,UAAU,GAAG,gBAAgB;EAC5D;EACA;AACJ;AACA;AACA;AACA;EACIoG,YAAYA,CAAA,EAAG;IACX,MAAM;MAAEC;IAAU,CAAC,GAAG,IAAI;IAC1B,IAAIA,SAAS,EAAE;MACX,OAAOA,SAAS;IACpB;IACA,IAAI,IAAI,CAACrG,IAAI,IAAI,QAAQ,EAAE;MACvB,OAAO,SAAS;IACpB,CAAC,MACI;MACD,OAAO,MAAM;IACjB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI8E,gBAAgBA,CAAA,EAAG;IACf;IACA,IAAI,CAACxF,WAAW,GAAG8F,KAAK,CAAC,IAAI,CAACxF,MAAM,CAAC,CAACD,IAAI,CAAC,EAAE,CAAC;IAC9C;IACA,IAAI,IAAI,CAACM,KAAK,IAAI,IAAI,IAAIqG,MAAM,CAAC,IAAI,CAACrG,KAAK,CAAC,CAACL,MAAM,KAAK,CAAC,EAAE;MACvD;IACJ;IACA;IACA;IACA,MAAM2G,KAAK,GAAGD,MAAM,CAAC,IAAI,CAACrG,KAAK,CAAC,CAAC4C,KAAK,CAAC,EAAE,CAAC,CAACI,KAAK,CAAC,CAAC,EAAE,IAAI,CAACrD,MAAM,CAAC;IAChE2G,KAAK,CAAC3F,OAAO,CAAC,CAACmC,IAAI,EAAE5C,KAAK,KAAK;MAC3B,IAAI,IAAI,CAACsC,eAAe,CAACO,IAAI,CAACD,IAAI,CAAC,EAAE;QACjC,IAAI,CAACzD,WAAW,CAACa,KAAK,CAAC,GAAG4C,IAAI;MAClC;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAAC9C,KAAK,GAAG,IAAI,CAACX,WAAW,CAAC4E,IAAI,CAAC,EAAE,CAAC;IACtC,IAAI,CAAC1E,mBAAmB,GAAG,CAAC,GAAG,IAAI,CAACF,WAAW,CAAC;EACpD;EACA;AACJ;AACA;AACA;AACA;EACI2C,WAAWA,CAAC7B,KAAK,EAAE;IACf,MAAM;MAAEd,WAAW;MAAEM;IAAO,CAAC,GAAG,IAAI;IACpC,MAAM4G,QAAQ,GAAGlH,WAAW,CAAC4E,IAAI,CAAC,EAAE,CAAC;IACrC,IAAI,CAACjE,KAAK,GAAGuG,QAAQ;IACrB,IAAI,CAACvC,YAAY,CAAC7D,KAAK,CAAC;IACxB,IAAIoG,QAAQ,CAAC5G,MAAM,KAAKA,MAAM,EAAE;MAC5B,IAAI,CAACf,WAAW,CAACyB,IAAI,CAAC;QAAEL,KAAK,EAAEuG;MAAS,CAAC,CAAC;IAC9C;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIpF,aAAaA,CAAChB,KAAK,EAAE;IACjB,MAAM;MAAEH;IAAM,CAAC,GAAG,IAAI;IACtB;IACA,MAAMuG,QAAQ,GAAGvG,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGA,KAAK,CAACwG,QAAQ,CAAC,CAAC;IACzD,IAAI,CAAC7H,SAAS,CAAC0B,IAAI,CAAC;MAAEL,KAAK,EAAEuG,QAAQ;MAAEpG;IAAM,CAAC,CAAC;EACnD;EACA;AACJ;AACA;AACA;AACA;EACI6D,YAAYA,CAAC7D,KAAK,EAAE;IAChB,MAAM;MAAEH;IAAM,CAAC,GAAG,IAAI;IACtB;IACA,MAAMuG,QAAQ,GAAGvG,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGA,KAAK,CAACwG,QAAQ,CAAC,CAAC;IACzD,IAAI,CAAC9H,QAAQ,CAAC2B,IAAI,CAAC;MAAEL,KAAK,EAAEuG,QAAQ;MAAEpG;IAAM,CAAC,CAAC;EAClD;EACA;AACJ;AACA;EACIkC,SAASA,CAACoE,YAAY,EAAE;IACpB,IAAIrG,EAAE;IACN,MAAM;MAAEpB,SAAS;MAAEW;IAAO,CAAC,GAAG,IAAI;IAClC,IAAI8G,YAAY,GAAG9G,MAAM,GAAG,CAAC,EAAE;MAC3B,CAACS,EAAE,GAAGpB,SAAS,CAACyH,YAAY,GAAG,CAAC,CAAC,MAAM,IAAI,IAAIrG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,KAAK,CAAC,CAAC;IACtF;EACJ;EACA;AACJ;AACA;EACIwB,aAAaA,CAACuE,YAAY,EAAE;IACxB,IAAIrG,EAAE;IACN,MAAM;MAAEpB;IAAU,CAAC,GAAG,IAAI;IAC1B,IAAIyH,YAAY,GAAG,CAAC,EAAE;MAClB,CAACrG,EAAE,GAAGpB,SAAS,CAACyH,YAAY,GAAG,CAAC,CAAC,MAAM,IAAI,IAAIrG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,KAAK,CAAC,CAAC;IACtF;EACJ;EACA;AACJ;AACA;AACA;AACA;EACID,kBAAkBA,CAAA,EAAG;IACjB,IAAIL,EAAE;IACN,MAAM;MAAEf,WAAW;MAAEM;IAAO,CAAC,GAAG,IAAI;IACpC;IACA;IACA,MAAM+G,MAAM,GAAGvB,KAAK,CAACC,IAAI,CAAC;MAAEzF;IAAO,CAAC,EAAE,CAAC0F,CAAC,EAAErH,CAAC,KAAKqB,WAAW,CAACrB,CAAC,CAAC,IAAI,EAAE,CAAC;IACrE,OAAO,CAACoC,EAAE,GAAGsG,MAAM,CAACC,SAAS,CAAE3G,KAAK,IAAK,CAACA,KAAK,IAAIA,KAAK,KAAK,EAAE,CAAC,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;EACzG;EACA;AACJ;AACA;AACA;AACA;EACIuE,gBAAgBA,CAAA,EAAG;IACf,MAAM;MAAEhF;IAAO,CAAC,GAAG,IAAI;IACvB,MAAMiH,eAAe,GAAG,IAAI,CAACnG,kBAAkB,CAAC,CAAC;IACjD,OAAOmG,eAAe,KAAK,CAAC,CAAC,GAAGjH,MAAM,GAAG,CAAC,GAAGiH,eAAe;EAChE;EACA;AACJ;AACA;AACA;AACA;EACI1F,gBAAgBA,CAAA,EAAG;IACf,MAAM;MAAElC,SAAS;MAAEK,WAAW;MAAEM;IAAO,CAAC,GAAG,IAAI;IAC/C;IACA,IAAIiH,eAAe,GAAG,CAAC,CAAC;IACxB,KAAK,IAAI5I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2B,MAAM,EAAE3B,CAAC,EAAE,EAAE;MAC7B,IAAI,CAACqB,WAAW,CAACrB,CAAC,CAAC,IAAIqB,WAAW,CAACrB,CAAC,CAAC,KAAK,EAAE,EAAE;QAC1C4I,eAAe,GAAG5I,CAAC;QACnB;MACJ;IACJ;IACA;IACAgB,SAAS,CAAC2B,OAAO,CAAC,CAACC,KAAK,EAAEV,KAAK,KAAK;MAChC,MAAM2G,gBAAgB,GAAGD,eAAe,KAAK,CAAC,CAAC,GAAG1G,KAAK,KAAKP,MAAM,GAAG,CAAC,GAAGiH,eAAe,KAAK1G,KAAK;MAClGU,KAAK,CAACC,QAAQ,GAAGgG,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAAC;MAC1C;MACA;MACA,MAAMxD,OAAO,GAAG,CAAChE,WAAW,CAACa,KAAK,CAAC,IAAIb,WAAW,CAACa,KAAK,CAAC,KAAK,EAAE;MAChEU,KAAK,CAACkG,YAAY,CAAC,aAAa,EAAEzD,OAAO,IAAI,CAACwD,gBAAgB,GAAG,MAAM,GAAG,OAAO,CAAC;IACtF,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIE,aAAaA,CAAC7G,KAAK,EAAE;IACjB,MAAM;MAAEP;IAAO,CAAC,GAAG,IAAI;IACvB,OAAO,IAAI,CAACR,gBAAgB,CAAC8B,QAAQ,CAACf,KAAK,GAAG,CAAC,CAAC,IAAIA,KAAK,GAAGP,MAAM,GAAG,CAAC;EAC1E;EACAqH,MAAMA,CAAA,EAAG;IACL,IAAI5G,EAAE,EAAEmC,EAAE;IACV,MAAM;MAAE/C,cAAc;MAAEyH,KAAK;MAAExH,QAAQ;MAAE6B,EAAE;MAAE5B,IAAI;MAAEJ,QAAQ;MAAEP,mBAAmB;MAAEE,OAAO;MAAED,SAAS;MAAEK,WAAW;MAAEM,MAAM;MAAEC,QAAQ;MAAEC,KAAK;MAAEC;IAAM,CAAC,GAAG,IAAI;IAC1J,MAAMoH,IAAI,GAAG1J,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM4I,SAAS,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;IACrC,MAAMzB,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAMuB,OAAO,GAAG,IAAI,CAACD,UAAU,CAAC,CAAC;IACjC,MAAMkB,cAAc,GAAG,CAAC,CAAC5E,EAAE,GAAG,CAACnC,EAAE,GAAGkB,EAAE,CAAC8F,aAAa,CAAC,wBAAwB,CAAC,MAAM,IAAI,IAAIhH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiH,WAAW,MAAM,IAAI,IAAI9E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+E,IAAI,CAAC,CAAC,MAAM,EAAE;IAC3L,OAAQ7J,CAAC,CAACI,IAAI,EAAE;MAAEiE,GAAG,EAAE,0CAA0C;MAAEyF,KAAK,EAAEnJ,kBAAkB,CAAC6I,KAAK,EAAE;QAC5F,CAACC,IAAI,GAAG,IAAI;QACZ,WAAW,EAAE5H,QAAQ;QACrB,CAAC,kBAAkBQ,IAAI,EAAE,GAAG,IAAI;QAChC,CAAC,mBAAmBD,KAAK,EAAE,GAAG,IAAI;QAClC,CAAC,kBAAkBH,IAAI,EAAE,GAAG,IAAI;QAChC,oBAAoB,EAAED,QAAQ;QAC9B,oBAAoB,EAAEG;MAC1B,CAAC;IAAE,CAAC,EAAEnC,CAAC,CAAC,KAAK,EAAE+J,MAAM,CAACC,MAAM,CAAC;MAAE3F,GAAG,EAAE,0CAA0C;MAAE4F,IAAI,EAAE,OAAO;MAAE,YAAY,EAAE,yBAAyB;MAAEH,KAAK,EAAE;IAAkB,CAAC,EAAExI,mBAAmB,CAAC,EAAEoG,KAAK,CAACC,IAAI,CAAC;MAAEzF;IAAO,CAAC,CAAC,CAAC4F,GAAG,CAAC,CAACF,CAAC,EAAEnF,KAAK,KAAMzC,CAAC,CAACE,QAAQ,EAAE,IAAI,EAAEF,CAAC,CAAC,KAAK,EAAE;MAAE8J,KAAK,EAAE;IAAiB,CAAC,EAAE9J,CAAC,CAAC,OAAO,EAAE;MAAE8J,KAAK,EAAE,cAAc;MAAEI,EAAE,EAAE,GAAG1I,OAAO,IAAIiB,KAAK,EAAE;MAAE,YAAY,EAAE,SAASA,KAAK,GAAG,CAAC,OAAOP,MAAM,EAAE;MAAEI,IAAI,EAAE,MAAM;MAAE6H,cAAc,EAAEpI,cAAc;MAAE4G,SAAS,EAAEA,SAAS;MAAEF,OAAO,EAAEA,OAAO;MAAEzG,QAAQ,EAAEA,QAAQ;MAAEoI,QAAQ,EAAEjI,QAAQ;MAAEiB,QAAQ,EAAEX,KAAK,KAAKwE,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;MAAE1E,KAAK,EAAEX,WAAW,CAACa,KAAK,CAAC,IAAI,EAAE;MAAE4H,YAAY,EAAE,eAAe;MAAEC,GAAG,EAAGzG,EAAE,IAAMtC,SAAS,CAACkB,KAAK,CAAC,GAAGoB,EAAG;MAAEgB,OAAO,EAAE,IAAI,CAACA,OAAO,CAACpC,KAAK,CAAC;MAAEY,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEb,OAAO,EAAE,IAAI,CAACA,OAAO,CAACC,KAAK,CAAC;MAAEkB,SAAS,EAAE,IAAI,CAACA,SAAS,CAAClB,KAAK,CAAC;MAAE0D,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACmD,aAAa,CAAC7G,KAAK,CAAC,IAAIzC,CAAC,CAAC,KAAK,EAAE;MAAE8J,KAAK,EAAE;IAAsB,CAAC,CAAC,CAAE,CAAC,CAAC,EAAE9J,CAAC,CAAC,KAAK,EAAE;MAAEqE,GAAG,EAAE,0CAA0C;MAAEyF,KAAK,EAAE;QACt7B,uBAAuB,EAAE,IAAI;QAC7B,8BAA8B,EAAE,CAACJ;MACrC;IAAE,CAAC,EAAE1J,CAAC,CAAC,MAAM,EAAE;MAAEqE,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,CAAC;EAC7E;EACA,IAAIR,EAAEA,CAAA,EAAG;IAAE,OAAOvD,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWiK,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,OAAO,EAAE,CAAC,cAAc,CAAC;MACzB,YAAY,EAAE,CAAC,mBAAmB,CAAC;MACnC,QAAQ,EAAE,CAAC,mBAAmB;IAClC,CAAC;EAAE;AACP,CAAC;AACD,IAAI9I,QAAQ,GAAG,CAAC;AAChBX,QAAQ,CAAC0J,KAAK,GAAG;EACbC,GAAG,EAAE7J,cAAc;EACnB8J,EAAE,EAAE7J;AACR,CAAC;AAED,SAASC,QAAQ,IAAI6J,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}