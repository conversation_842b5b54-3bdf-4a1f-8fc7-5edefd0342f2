{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, e as getIonMode, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { b as inheritAttributes } from './helpers-1O4D2b7y.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\nconst pickerColumnOptionIosCss = \"button{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;overflow:hidden}:host(.option-disabled){opacity:0.4}:host(.option-disabled) button{cursor:default}\";\nconst pickerColumnOptionMdCss = \"button{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;overflow:hidden}:host(.option-disabled){opacity:0.4}:host(.option-disabled) button{cursor:default}:host(.option-active){color:var(--ion-color-base)}\";\nconst PickerColumnOption = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    /**\n     * We keep track of the parent picker column\n     * so we can update the value of it when\n     * clicking an enable option.\n     */\n    this.pickerColumn = null;\n    /**\n     * The aria-label of the option.\n     *\n     * If the value changes, then it will trigger a\n     * re-render of the picker since it's a @State variable.\n     * Otherwise, the `aria-label` attribute cannot be updated\n     * after the component is loaded.\n     */\n    this.ariaLabel = null;\n    /**\n     * If `true`, the user cannot interact with the picker column option.\n     */\n    this.disabled = false;\n    /**\n     * The color to use from your application's color palette.\n     * Default options are: `\"primary\"`, `\"secondary\"`, `\"tertiary\"`, `\"success\"`, `\"warning\"`, `\"danger\"`, `\"light\"`, `\"medium\"`, and `\"dark\"`.\n     * For more information on colors, see [theming](/docs/theming/basics).\n     */\n    this.color = 'primary';\n  }\n  /**\n   * The aria-label of the option has changed after the\n   * first render and needs to be updated within the component.\n   *\n   * @param ariaLbl The new aria-label value.\n   */\n  onAriaLabelChange(ariaLbl) {\n    this.ariaLabel = ariaLbl;\n  }\n  componentWillLoad() {\n    const inheritedAttributes = inheritAttributes(this.el, ['aria-label']);\n    /**\n     * The initial value of `aria-label` needs to be set for\n     * the first render.\n          */\n    this.ariaLabel = inheritedAttributes['aria-label'] || null;\n  }\n  connectedCallback() {\n    this.pickerColumn = this.el.closest('ion-picker-column');\n  }\n  disconnectedCallback() {\n    this.pickerColumn = null;\n  }\n  /**\n   * The column options can load at any time\n   * so the options needs to tell the\n   * parent picker column when it is loaded\n   * so the picker column can ensure it is\n   * centered in the view.\n   *\n   * We intentionally run this for every\n   * option. If we only ran this from\n   * the selected option then if the newly\n   * loaded options were not selected then\n   * scrollActiveItemIntoView would not be called.\n   */\n  componentDidLoad() {\n    const {\n      pickerColumn\n    } = this;\n    if (pickerColumn !== null) {\n      pickerColumn.scrollActiveItemIntoView();\n    }\n  }\n  /**\n   * When an option is clicked, update the\n   * parent picker column value. This\n   * component will handle centering the option\n   * in the column view.\n   */\n  onClick() {\n    const {\n      pickerColumn\n    } = this;\n    if (pickerColumn !== null) {\n      pickerColumn.setValue(this.value);\n    }\n  }\n  render() {\n    const {\n      color,\n      disabled,\n      ariaLabel\n    } = this;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: 'f816729941aabcb31ddfdce3ffe2e2139030d715',\n      class: createColorClasses(color, {\n        [mode]: true,\n        ['option-disabled']: disabled\n      })\n    }, h(\"button\", {\n      key: '48dff7833bb60fc8331cd353a0885e6affa683d1',\n      tabindex: \"-1\",\n      \"aria-label\": ariaLabel,\n      disabled: disabled,\n      onClick: () => this.onClick()\n    }, h(\"slot\", {\n      key: 'f9224d0e7b7aa6c05b29abfdcfe0f30ad6ee3141'\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"aria-label\": [\"onAriaLabelChange\"]\n    };\n  }\n};\nPickerColumnOption.style = {\n  ios: pickerColumnOptionIosCss,\n  md: pickerColumnOptionMdCss\n};\nexport { PickerColumnOption as ion_picker_column_option };", "map": {"version": 3, "names": ["r", "registerInstance", "e", "getIonMode", "h", "j", "Host", "k", "getElement", "b", "inheritAttributes", "c", "createColorClasses", "pickerColumnOptionIosCss", "pickerColumnOptionMdCss", "PickerColumnOption", "constructor", "hostRef", "pickerColumn", "aria<PERSON><PERSON><PERSON>", "disabled", "color", "onAriaLabelChange", "ariaLbl", "componentWillLoad", "inheritedAttributes", "el", "connectedCallback", "closest", "disconnectedCallback", "componentDidLoad", "scrollActiveItemIntoView", "onClick", "setValue", "value", "render", "mode", "key", "class", "tabindex", "watchers", "style", "ios", "md", "ion_picker_column_option"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@ionic/core/dist/esm/ion-picker-column-option.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, e as getIonMode, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { b as inheritAttributes } from './helpers-1O4D2b7y.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\n\nconst pickerColumnOptionIosCss = \"button{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;overflow:hidden}:host(.option-disabled){opacity:0.4}:host(.option-disabled) button{cursor:default}\";\n\nconst pickerColumnOptionMdCss = \"button{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;overflow:hidden}:host(.option-disabled){opacity:0.4}:host(.option-disabled) button{cursor:default}:host(.option-active){color:var(--ion-color-base)}\";\n\nconst PickerColumnOption = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        /**\n         * We keep track of the parent picker column\n         * so we can update the value of it when\n         * clicking an enable option.\n         */\n        this.pickerColumn = null;\n        /**\n         * The aria-label of the option.\n         *\n         * If the value changes, then it will trigger a\n         * re-render of the picker since it's a @State variable.\n         * Otherwise, the `aria-label` attribute cannot be updated\n         * after the component is loaded.\n         */\n        this.ariaLabel = null;\n        /**\n         * If `true`, the user cannot interact with the picker column option.\n         */\n        this.disabled = false;\n        /**\n         * The color to use from your application's color palette.\n         * Default options are: `\"primary\"`, `\"secondary\"`, `\"tertiary\"`, `\"success\"`, `\"warning\"`, `\"danger\"`, `\"light\"`, `\"medium\"`, and `\"dark\"`.\n         * For more information on colors, see [theming](/docs/theming/basics).\n         */\n        this.color = 'primary';\n    }\n    /**\n     * The aria-label of the option has changed after the\n     * first render and needs to be updated within the component.\n     *\n     * @param ariaLbl The new aria-label value.\n     */\n    onAriaLabelChange(ariaLbl) {\n        this.ariaLabel = ariaLbl;\n    }\n    componentWillLoad() {\n        const inheritedAttributes = inheritAttributes(this.el, ['aria-label']);\n        /**\n         * The initial value of `aria-label` needs to be set for\n         * the first render.\n    \n         */\n        this.ariaLabel = inheritedAttributes['aria-label'] || null;\n    }\n    connectedCallback() {\n        this.pickerColumn = this.el.closest('ion-picker-column');\n    }\n    disconnectedCallback() {\n        this.pickerColumn = null;\n    }\n    /**\n     * The column options can load at any time\n     * so the options needs to tell the\n     * parent picker column when it is loaded\n     * so the picker column can ensure it is\n     * centered in the view.\n     *\n     * We intentionally run this for every\n     * option. If we only ran this from\n     * the selected option then if the newly\n     * loaded options were not selected then\n     * scrollActiveItemIntoView would not be called.\n     */\n    componentDidLoad() {\n        const { pickerColumn } = this;\n        if (pickerColumn !== null) {\n            pickerColumn.scrollActiveItemIntoView();\n        }\n    }\n    /**\n     * When an option is clicked, update the\n     * parent picker column value. This\n     * component will handle centering the option\n     * in the column view.\n     */\n    onClick() {\n        const { pickerColumn } = this;\n        if (pickerColumn !== null) {\n            pickerColumn.setValue(this.value);\n        }\n    }\n    render() {\n        const { color, disabled, ariaLabel } = this;\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'f816729941aabcb31ddfdce3ffe2e2139030d715', class: createColorClasses(color, {\n                [mode]: true,\n                ['option-disabled']: disabled,\n            }) }, h(\"button\", { key: '48dff7833bb60fc8331cd353a0885e6affa683d1', tabindex: \"-1\", \"aria-label\": ariaLabel, disabled: disabled, onClick: () => this.onClick() }, h(\"slot\", { key: 'f9224d0e7b7aa6c05b29abfdcfe0f30ad6ee3141' }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"aria-label\": [\"onAriaLabelChange\"]\n    }; }\n};\nPickerColumnOption.style = {\n    ios: pickerColumnOptionIosCss,\n    md: pickerColumnOptionMdCss\n};\n\nexport { PickerColumnOption as ion_picker_column_option };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAC3G,SAASC,CAAC,IAAIC,iBAAiB,QAAQ,uBAAuB;AAC9D,SAASC,CAAC,IAAIC,kBAAkB,QAAQ,qBAAqB;AAE7D,MAAMC,wBAAwB,GAAG,kdAAkd;AAEnf,MAAMC,uBAAuB,GAAG,ogBAAogB;AAEpiB,MAAMC,kBAAkB,GAAG,MAAM;EAC7BC,WAAWA,CAACC,OAAO,EAAE;IACjBhB,gBAAgB,CAAC,IAAI,EAAEgB,OAAO,CAAC;IAC/B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,SAAS;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,iBAAiBA,CAACC,OAAO,EAAE;IACvB,IAAI,CAACJ,SAAS,GAAGI,OAAO;EAC5B;EACAC,iBAAiBA,CAAA,EAAG;IAChB,MAAMC,mBAAmB,GAAGf,iBAAiB,CAAC,IAAI,CAACgB,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC;IACtE;AACR;AACA;AACA;IAEQ,IAAI,CAACP,SAAS,GAAGM,mBAAmB,CAAC,YAAY,CAAC,IAAI,IAAI;EAC9D;EACAE,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACT,YAAY,GAAG,IAAI,CAACQ,EAAE,CAACE,OAAO,CAAC,mBAAmB,CAAC;EAC5D;EACAC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACX,YAAY,GAAG,IAAI;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIY,gBAAgBA,CAAA,EAAG;IACf,MAAM;MAAEZ;IAAa,CAAC,GAAG,IAAI;IAC7B,IAAIA,YAAY,KAAK,IAAI,EAAE;MACvBA,YAAY,CAACa,wBAAwB,CAAC,CAAC;IAC3C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAAA,EAAG;IACN,MAAM;MAAEd;IAAa,CAAC,GAAG,IAAI;IAC7B,IAAIA,YAAY,KAAK,IAAI,EAAE;MACvBA,YAAY,CAACe,QAAQ,CAAC,IAAI,CAACC,KAAK,CAAC;IACrC;EACJ;EACAC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEd,KAAK;MAAED,QAAQ;MAAED;IAAU,CAAC,GAAG,IAAI;IAC3C,MAAMiB,IAAI,GAAGjC,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,CAAC,CAACE,IAAI,EAAE;MAAE+B,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE1B,kBAAkB,CAACS,KAAK,EAAE;QAC5F,CAACe,IAAI,GAAG,IAAI;QACZ,CAAC,iBAAiB,GAAGhB;MACzB,CAAC;IAAE,CAAC,EAAEhB,CAAC,CAAC,QAAQ,EAAE;MAAEiC,GAAG,EAAE,0CAA0C;MAAEE,QAAQ,EAAE,IAAI;MAAE,YAAY,EAAEpB,SAAS;MAAEC,QAAQ,EAAEA,QAAQ;MAAEY,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACA,OAAO,CAAC;IAAE,CAAC,EAAE5B,CAAC,CAAC,MAAM,EAAE;MAAEiC,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,CAAC;EAC3O;EACA,IAAIX,EAAEA,CAAA,EAAG;IAAE,OAAOlB,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWgC,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,YAAY,EAAE,CAAC,mBAAmB;IACtC,CAAC;EAAE;AACP,CAAC;AACDzB,kBAAkB,CAAC0B,KAAK,GAAG;EACvBC,GAAG,EAAE7B,wBAAwB;EAC7B8B,EAAE,EAAE7B;AACR,CAAC;AAED,SAASC,kBAAkB,IAAI6B,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}