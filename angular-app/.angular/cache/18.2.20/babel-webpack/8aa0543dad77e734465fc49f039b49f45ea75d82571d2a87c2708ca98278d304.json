{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, m as printIonWarning, e as getIonMode, h, j as Host, k as getElement, n as forceUpdate } from './index-B_U9CtaY.js';\nimport { c as createNotchController } from './notch-controller-C5LPspO8.js';\nimport { i as isOptionSelected, c as compareOptions } from './compare-with-utils-sObYyvOy.js';\nimport { b as inheritAttributes, a as renderHiddenInput, n as focusVisibleElement } from './helpers-1O4D2b7y.js';\nimport { c as popoverController, b as actionSheetController, a as alertController, m as modalController, s as safeCall } from './overlays-8Y2rA-ps.js';\nimport { i as isRTL } from './dir-C53feagD.js';\nimport { h as hostContext, c as createColorClasses, g as getClassMap } from './theme-DiVJyqlX.js';\nimport { w as watchForOptions } from './watch-options-Dtdm8lKC.js';\nimport { w as chevronExpand, q as caretDownSharp } from './index-BLV6ykCk.js';\nimport './index-ZjP4CjeZ.js';\nimport './hardware-back-button-DcH0BbDp.js';\nimport './framework-delegate-DxcnWic_.js';\nimport './gesture-controller-BTEOs1at.js';\nconst selectIosCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0}:host(.select-disabled){pointer-events:none}:host(.has-focus) button{border:2px solid #5e9ed6}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.select-bottom{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}:host(.has-focus.ion-valid),:host(.select-expanded.ion-valid),:host(.ion-touched.ion-invalid),:host(.select-expanded.ion-touched.ion-invalid){--border-color:var(--highlight-color)}.select-bottom .error-text{display:none;color:var(--highlight-color-invalid)}.select-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .select-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .select-bottom .helper-text{display:none}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-focus.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]:last-of-type){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]:first-of-type){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host{--border-width:0.55px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--highlight-height:0px}.select-icon{width:1.125rem;height:1.125rem;color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959))}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 1.125rem - 4px)}:host(.select-disabled){opacity:0.3}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\nconst selectMdCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0}:host(.select-disabled){pointer-events:none}:host(.has-focus) button{border:2px solid #5e9ed6}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.select-bottom{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}:host(.has-focus.ion-valid),:host(.select-expanded.ion-valid),:host(.ion-touched.ion-invalid),:host(.select-expanded.ion-touched.ion-invalid){--border-color:var(--highlight-color)}.select-bottom .error-text{display:none;color:var(--highlight-color-invalid)}.select-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .select-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .select-bottom .helper-text{display:none}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-focus.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]:last-of-type){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]:first-of-type){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.select-fill-solid){--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-500, var(--ion-background-color-step-500, gray));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-solid) .select-wrapper{border-bottom:var(--border-width) var(--border-style) var(--border-color)}:host(.select-expanded.select-fill-solid.ion-valid),:host(.has-focus.select-fill-solid.ion-valid),:host(.select-fill-solid.ion-touched.ion-invalid){--border-color:var(--highlight-color)}:host(.select-fill-solid) .select-bottom{border-top:none}@media (any-hover: hover){:host(.select-fill-solid:hover){--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}:host(.select-fill-solid.select-expanded),:host(.select-fill-solid.has-focus){--background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--border-color:var(--highlight-color)}:host(.select-fill-solid) .select-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0px;border-end-start-radius:0px}:host(.label-floating.select-fill-solid) .label-text-wrapper{max-width:calc(100% / 0.75)}:host(.in-item.select-expanded.select-fill-solid) .select-wrapper .select-icon,:host(.in-item.has-focus.select-fill-solid) .select-wrapper .select-icon,:host(.in-item.has-focus.ion-valid.select-fill-solid) .select-wrapper .select-icon,:host(.in-item.ion-touched.ion-invalid.select-fill-solid) .select-wrapper .select-icon{color:var(--highlight-color)}:host(.select-fill-outline){--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-outline.select-shape-round){--border-radius:28px;--padding-start:32px;--padding-end:32px}:host(.has-focus.select-fill-outline.ion-valid),:host(.select-fill-outline.ion-touched.ion-invalid){--border-color:var(--highlight-color)}@media (any-hover: hover){:host(.select-fill-outline:hover){--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}:host(.select-fill-outline.select-expanded),:host(.select-fill-outline.has-focus){--border-width:var(--highlight-height);--border-color:var(--highlight-color)}:host(.select-fill-outline) .select-bottom{border-top:none}:host(.select-fill-outline) .select-wrapper{border-bottom:none}:host(.select-ltr.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-fill-outline.select-label-placement-floating) .label-text-wrapper{position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .label-text-wrapper{position:relative;z-index:1}:host(.label-floating.select-fill-outline) .label-text-wrapper{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}:host(.select-fill-outline.select-label-placement-stacked) select,:host(.select-fill-outline.select-label-placement-floating) select{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}:host(.select-fill-outline) .select-outline-container{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-end{pointer-events:none}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-notch,:host(.select-fill-outline) .select-outline-end{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.select-fill-outline) .select-outline-notch{max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .notch-spacer{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none}:host(.select-fill-outline) .select-outline-start{-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color)}:host(.select-fill-outline) .select-outline-start{border-start-start-radius:var(--border-radius);border-start-end-radius:0px;border-end-end-radius:0px;border-end-start-radius:var(--border-radius)}:host(.select-fill-outline) .select-outline-start{width:calc(var(--padding-start) - 4px)}:host(.select-fill-outline) .select-outline-end{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color)}:host(.select-fill-outline) .select-outline-end{border-start-start-radius:0px;border-start-end-radius:var(--border-radius);border-end-end-radius:var(--border-radius);border-end-start-radius:0px}:host(.select-fill-outline) .select-outline-end{-ms-flex-positive:1;flex-grow:1}:host(.label-floating.select-fill-outline) .select-outline-notch{border-top:none}:host(.in-item.select-expanded.select-fill-outline) .select-wrapper .select-icon,:host(.in-item.has-focus.select-fill-outline) .select-wrapper .select-icon,:host(.in-item.has-focus.ion-valid.select-fill-outline) .select-wrapper .select-icon,:host(.in-item.ion-touched.ion-invalid.select-fill-outline) .select-wrapper .select-icon{color:var(--highlight-color)}:host{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--highlight-height:2px}:host(.select-label-placement-floating.select-expanded) .label-text-wrapper,:host(.select-label-placement-floating.has-focus) .label-text-wrapper,:host(.select-label-placement-stacked.select-expanded) .label-text-wrapper,:host(.select-label-placement-stacked.has-focus) .label-text-wrapper{color:var(--highlight-color)}:host(.has-focus.select-label-placement-floating.ion-valid) .label-text-wrapper,:host(.select-label-placement-floating.ion-touched.ion-invalid) .label-text-wrapper,:host(.has-focus.select-label-placement-stacked.ion-valid) .label-text-wrapper,:host(.select-label-placement-stacked.ion-touched.ion-invalid) .label-text-wrapper{color:var(--highlight-color)}.select-highlight{bottom:-1px;position:absolute;width:100%;height:var(--highlight-height);-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}.select-highlight{inset-inline-start:0}:host(.select-expanded) .select-highlight,:host(.has-focus) .select-highlight{-webkit-transform:scale(1);transform:scale(1)}:host(.in-item) .select-highlight{bottom:0}:host(.in-item) .select-highlight{inset-inline-start:0}.select-icon{width:0.8125rem;-webkit-transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray))}:host(.select-expanded:not(.has-expanded-icon)) .select-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.in-item.select-expanded) .select-wrapper .select-icon,:host(.in-item.has-focus) .select-wrapper .select-icon,:host(.in-item.has-focus.ion-valid) .select-wrapper .select-icon,:host(.in-item.ion-touched.ion-invalid) .select-wrapper .select-icon{color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray))}:host(.select-expanded) .select-wrapper .select-icon,:host(.has-focus.ion-valid) .select-wrapper .select-icon,:host(.ion-touched.ion-invalid) .select-wrapper .select-icon,:host(.has-focus) .select-wrapper .select-icon{color:var(--highlight-color)}:host(.select-shape-round){--border-radius:16px}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 0.8125rem - 4px)}:host(.select-disabled){opacity:0.38}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\nconst Select = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionCancel = createEvent(this, \"ionCancel\", 7);\n    this.ionDismiss = createEvent(this, \"ionDismiss\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.inputId = `ion-sel-${selectIds++}`;\n    this.helperTextId = `${this.inputId}-helper-text`;\n    this.errorTextId = `${this.inputId}-error-text`;\n    this.inheritedAttributes = {};\n    this.isExpanded = false;\n    /**\n     * The `hasFocus` state ensures the focus class is\n     * added regardless of how the element is focused.\n     * The `ion-focused` class only applies when focused\n     * via tabbing, not by clicking.\n     * The `has-focus` logic was added to ensure the class\n     * is applied in both cases.\n     */\n    this.hasFocus = false;\n    /**\n     * The text to display on the cancel button.\n     */\n    this.cancelText = 'Cancel';\n    /**\n     * If `true`, the user cannot interact with the select.\n     */\n    this.disabled = false;\n    /**\n     * The interface the select should use: `action-sheet`, `popover`, `alert`, or `modal`.\n     */\n    this.interface = 'alert';\n    /**\n     * Any additional options that the `alert`, `action-sheet` or `popover` interface\n     * can take. See the [ion-alert docs](./alert), the\n     * [ion-action-sheet docs](./action-sheet), the\n     * [ion-popover docs](./popover), and the [ion-modal docs](./modal) for the\n     * create options for each interface.\n     *\n     * Note: `interfaceOptions` will not override `inputs` or `buttons` with the `alert` interface.\n     */\n    this.interfaceOptions = {};\n    /**\n     * Where to place the label relative to the select.\n     * `\"start\"`: The label will appear to the left of the select in LTR and to the right in RTL.\n     * `\"end\"`: The label will appear to the right of the select in LTR and to the left in RTL.\n     * `\"floating\"`: The label will appear smaller and above the select when the select is focused or it has a value. Otherwise it will appear on top of the select.\n     * `\"stacked\"`: The label will appear smaller and above the select regardless even when the select is blurred or has no value.\n     * `\"fixed\"`: The label has the same behavior as `\"start\"` except it also has a fixed width. Long text will be truncated with ellipses (\"...\").\n     * When using `\"floating\"` or `\"stacked\"` we recommend initializing the select with either a `value` or a `placeholder`.\n     */\n    this.labelPlacement = 'start';\n    /**\n     * If `true`, the select can accept multiple values.\n     */\n    this.multiple = false;\n    /**\n     * The name of the control, which is submitted with the form data.\n     */\n    this.name = this.inputId;\n    /**\n     * The text to display on the ok button.\n     */\n    this.okText = 'OK';\n    /**\n     * If true, screen readers will announce it as a required field. This property\n     * works only for accessibility purposes, it will not prevent the form from\n     * submitting if the value is invalid.\n     */\n    this.required = false;\n    this.onClick = ev => {\n      const target = ev.target;\n      const closestSlot = target.closest('[slot=\"start\"], [slot=\"end\"]');\n      if (target === this.el || closestSlot === null) {\n        this.setFocus();\n        this.open(ev);\n      } else {\n        /**\n         * Prevent clicks to the start/end slots from opening the select.\n         * We ensure the target isn't this element in case the select is slotted\n         * in, for example, an item. This would prevent the select from ever\n         * being opened since the element itself has slot=\"start\"/\"end\".\n         *\n         * Clicking a slotted element also causes a click\n         * on the <label> element (since it wraps the slots).\n         * Clicking <label> dispatches another click event on\n         * the native form control that then bubbles up to this\n         * listener. This additional event targets the host\n         * element, so the select overlay is opened.\n         *\n         * When the slotted elements are clicked (and therefore\n         * the ancestor <label> element) we want to prevent the label\n         * from dispatching another click event.\n         *\n         * Do not call stopPropagation() because this will cause\n         * click handlers on the slotted elements to never fire in React.\n         * When developers do onClick in React a native \"click\" listener\n         * is added on the root element, not the slotted element. When that\n         * native click listener fires, React then dispatches the synthetic\n         * click event on the slotted element. However, if stopPropagation\n         * is called then the native click event will never bubble up\n         * to the root element.\n         */\n        ev.preventDefault();\n      }\n    };\n    this.onFocus = () => {\n      this.hasFocus = true;\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.hasFocus = false;\n      this.ionBlur.emit();\n    };\n    /**\n     * Stops propagation when the label is clicked,\n     * otherwise, two clicks will be triggered.\n     */\n    this.onLabelClick = ev => {\n      // Only stop propagation if the click was directly on the label\n      // and not on the input or other child elements\n      if (ev.target === ev.currentTarget) {\n        ev.stopPropagation();\n      }\n    };\n  }\n  styleChanged() {\n    this.emitStyle();\n  }\n  setValue(value) {\n    this.value = value;\n    this.ionChange.emit({\n      value\n    });\n  }\n  connectedCallback() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const {\n        el\n      } = _this;\n      _this.notchController = createNotchController(el, () => _this.notchSpacerEl, () => _this.labelSlot);\n      _this.updateOverlayOptions();\n      _this.emitStyle();\n      _this.mutationO = watchForOptions(_this.el, 'ion-select-option', /*#__PURE__*/_asyncToGenerator(function* () {\n        _this.updateOverlayOptions();\n        /**\n         * We need to re-render the component\n         * because one of the new ion-select-option\n         * elements may match the value. In this case,\n         * the rendered selected text should be updated.\n         */\n        forceUpdate(_this);\n      }));\n    })();\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAttributes(this.el, ['aria-label']);\n  }\n  componentDidLoad() {\n    /**\n     * If any of the conditions that trigger the styleChanged callback\n     * are met on component load, it is possible the event emitted\n     * prior to a parent web component registering an event listener.\n     *\n     * To ensure the parent web component receives the event, we\n     * emit the style event again after the component has loaded.\n     *\n     * This is often seen in Angular with the `dist` output target.\n     */\n    this.emitStyle();\n  }\n  disconnectedCallback() {\n    if (this.mutationO) {\n      this.mutationO.disconnect();\n      this.mutationO = undefined;\n    }\n    if (this.notchController) {\n      this.notchController.destroy();\n      this.notchController = undefined;\n    }\n  }\n  /**\n   * Open the select overlay. The overlay is either an alert, action sheet, or popover,\n   * depending on the `interface` property on the `ion-select`.\n   *\n   * @param event The user interface event that called the open.\n   */\n  open(event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.disabled || _this2.isExpanded) {\n        return undefined;\n      }\n      _this2.isExpanded = true;\n      const overlay = _this2.overlay = yield _this2.createOverlay(event);\n      // Add logic to scroll selected item into view before presenting\n      const scrollSelectedIntoView = () => {\n        const indexOfSelected = _this2.childOpts.findIndex(o => o.value === _this2.value);\n        if (indexOfSelected > -1) {\n          const selectedItem = overlay.querySelector(`.select-interface-option:nth-of-type(${indexOfSelected + 1})`);\n          if (selectedItem) {\n            /**\n             * Browsers such as Firefox do not\n             * correctly delegate focus when manually\n             * focusing an element with delegatesFocus.\n             * We work around this by manually focusing\n             * the interactive element.\n             * ion-radio and ion-checkbox are the only\n             * elements that ion-select-popover uses, so\n             * we only need to worry about those two components\n             * when focusing.\n             */\n            const interactiveEl = selectedItem.querySelector('ion-radio, ion-checkbox');\n            if (interactiveEl) {\n              selectedItem.scrollIntoView({\n                block: 'nearest'\n              });\n              // Needs to be called before `focusVisibleElement` to prevent issue with focus event bubbling\n              // and removing `ion-focused` style\n              interactiveEl.setFocus();\n            }\n            focusVisibleElement(selectedItem);\n          }\n        } else {\n          /**\n           * If no value is set then focus the first enabled option.\n           */\n          const firstEnabledOption = overlay.querySelector('ion-radio:not(.radio-disabled), ion-checkbox:not(.checkbox-disabled)');\n          if (firstEnabledOption) {\n            /**\n             * Focus the option for the same reason as we do above.\n             *\n             * Needs to be called before `focusVisibleElement` to prevent issue with focus event bubbling\n             * and removing `ion-focused` style\n             */\n            firstEnabledOption.setFocus();\n            focusVisibleElement(firstEnabledOption.closest('ion-item'));\n          }\n        }\n      };\n      // For modals and popovers, we can scroll before they're visible\n      if (_this2.interface === 'modal') {\n        overlay.addEventListener('ionModalWillPresent', scrollSelectedIntoView, {\n          once: true\n        });\n      } else if (_this2.interface === 'popover') {\n        overlay.addEventListener('ionPopoverWillPresent', scrollSelectedIntoView, {\n          once: true\n        });\n      } else {\n        /**\n         * For alerts and action sheets, we need to wait a frame after willPresent\n         * because these overlays don't have their content in the DOM immediately\n         * when willPresent fires. By waiting a frame, we ensure the content is\n         * rendered and can be properly scrolled into view.\n         */\n        const scrollAfterRender = () => {\n          requestAnimationFrame(() => {\n            scrollSelectedIntoView();\n          });\n        };\n        if (_this2.interface === 'alert') {\n          overlay.addEventListener('ionAlertWillPresent', scrollAfterRender, {\n            once: true\n          });\n        } else if (_this2.interface === 'action-sheet') {\n          overlay.addEventListener('ionActionSheetWillPresent', scrollAfterRender, {\n            once: true\n          });\n        }\n      }\n      overlay.onDidDismiss().then(() => {\n        _this2.overlay = undefined;\n        _this2.isExpanded = false;\n        _this2.ionDismiss.emit();\n        _this2.setFocus();\n      });\n      yield overlay.present();\n      return overlay;\n    })();\n  }\n  createOverlay(ev) {\n    let selectInterface = this.interface;\n    if (selectInterface === 'action-sheet' && this.multiple) {\n      printIonWarning(`[ion-select] - Interface cannot be \"${selectInterface}\" with a multi-value select. Using the \"alert\" interface instead.`);\n      selectInterface = 'alert';\n    }\n    if (selectInterface === 'popover' && !ev) {\n      printIonWarning(`[ion-select] - Interface cannot be a \"${selectInterface}\" without passing an event. Using the \"alert\" interface instead.`);\n      selectInterface = 'alert';\n    }\n    if (selectInterface === 'action-sheet') {\n      return this.openActionSheet();\n    }\n    if (selectInterface === 'popover') {\n      return this.openPopover(ev);\n    }\n    if (selectInterface === 'modal') {\n      return this.openModal();\n    }\n    return this.openAlert();\n  }\n  updateOverlayOptions() {\n    const overlay = this.overlay;\n    if (!overlay) {\n      return;\n    }\n    const childOpts = this.childOpts;\n    const value = this.value;\n    switch (this.interface) {\n      case 'action-sheet':\n        overlay.buttons = this.createActionSheetButtons(childOpts, value);\n        break;\n      case 'popover':\n        const popover = overlay.querySelector('ion-select-popover');\n        if (popover) {\n          popover.options = this.createOverlaySelectOptions(childOpts, value);\n        }\n        break;\n      case 'modal':\n        const modal = overlay.querySelector('ion-select-modal');\n        if (modal) {\n          modal.options = this.createOverlaySelectOptions(childOpts, value);\n        }\n        break;\n      case 'alert':\n        const inputType = this.multiple ? 'checkbox' : 'radio';\n        overlay.inputs = this.createAlertInputs(childOpts, inputType, value);\n        break;\n    }\n  }\n  createActionSheetButtons(data, selectValue) {\n    const actionSheetButtons = data.map(option => {\n      const value = getOptionValue(option);\n      // Remove hydrated before copying over classes\n      const copyClasses = Array.from(option.classList).filter(cls => cls !== 'hydrated').join(' ');\n      const optClass = `${OPTION_CLASS} ${copyClasses}`;\n      return {\n        role: isOptionSelected(selectValue, value, this.compareWith) ? 'selected' : '',\n        text: option.textContent,\n        cssClass: optClass,\n        handler: () => {\n          this.setValue(value);\n        }\n      };\n    });\n    // Add \"cancel\" button\n    actionSheetButtons.push({\n      text: this.cancelText,\n      role: 'cancel',\n      handler: () => {\n        this.ionCancel.emit();\n      }\n    });\n    return actionSheetButtons;\n  }\n  createAlertInputs(data, inputType, selectValue) {\n    const alertInputs = data.map(option => {\n      const value = getOptionValue(option);\n      // Remove hydrated before copying over classes\n      const copyClasses = Array.from(option.classList).filter(cls => cls !== 'hydrated').join(' ');\n      const optClass = `${OPTION_CLASS} ${copyClasses}`;\n      return {\n        type: inputType,\n        cssClass: optClass,\n        label: option.textContent || '',\n        value,\n        checked: isOptionSelected(selectValue, value, this.compareWith),\n        disabled: option.disabled\n      };\n    });\n    return alertInputs;\n  }\n  createOverlaySelectOptions(data, selectValue) {\n    const popoverOptions = data.map(option => {\n      const value = getOptionValue(option);\n      // Remove hydrated before copying over classes\n      const copyClasses = Array.from(option.classList).filter(cls => cls !== 'hydrated').join(' ');\n      const optClass = `${OPTION_CLASS} ${copyClasses}`;\n      return {\n        text: option.textContent || '',\n        cssClass: optClass,\n        value,\n        checked: isOptionSelected(selectValue, value, this.compareWith),\n        disabled: option.disabled,\n        handler: selected => {\n          this.setValue(selected);\n          if (!this.multiple) {\n            this.close();\n          }\n        }\n      };\n    });\n    return popoverOptions;\n  }\n  openPopover(ev) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        fill,\n        labelPlacement\n      } = _this3;\n      const interfaceOptions = _this3.interfaceOptions;\n      const mode = getIonMode(_this3);\n      const showBackdrop = mode === 'md' ? false : true;\n      const multiple = _this3.multiple;\n      const value = _this3.value;\n      let event = ev;\n      let size = 'auto';\n      const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n      /**\n       * The popover should take up the full width\n       * when using a fill in MD mode or if the\n       * label is floating/stacked.\n       */\n      if (hasFloatingOrStackedLabel || mode === 'md' && fill !== undefined) {\n        size = 'cover';\n        /**\n         * Otherwise the popover\n         * should be positioned relative\n         * to the native element.\n         */\n      } else {\n        event = Object.assign(Object.assign({}, ev), {\n          detail: {\n            ionShadowTarget: _this3.nativeWrapperEl\n          }\n        });\n      }\n      const popoverOpts = Object.assign(Object.assign({\n        mode,\n        event,\n        alignment: 'center',\n        size,\n        showBackdrop\n      }, interfaceOptions), {\n        component: 'ion-select-popover',\n        cssClass: ['select-popover', interfaceOptions.cssClass],\n        componentProps: {\n          header: interfaceOptions.header,\n          subHeader: interfaceOptions.subHeader,\n          message: interfaceOptions.message,\n          multiple,\n          value,\n          options: _this3.createOverlaySelectOptions(_this3.childOpts, value)\n        }\n      });\n      return popoverController.create(popoverOpts);\n    })();\n  }\n  openActionSheet() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      const mode = getIonMode(_this4);\n      const interfaceOptions = _this4.interfaceOptions;\n      const actionSheetOpts = Object.assign(Object.assign({\n        mode\n      }, interfaceOptions), {\n        buttons: _this4.createActionSheetButtons(_this4.childOpts, _this4.value),\n        cssClass: ['select-action-sheet', interfaceOptions.cssClass]\n      });\n      return actionSheetController.create(actionSheetOpts);\n    })();\n  }\n  openAlert() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      const interfaceOptions = _this5.interfaceOptions;\n      const inputType = _this5.multiple ? 'checkbox' : 'radio';\n      const mode = getIonMode(_this5);\n      const alertOpts = Object.assign(Object.assign({\n        mode\n      }, interfaceOptions), {\n        header: interfaceOptions.header ? interfaceOptions.header : _this5.labelText,\n        inputs: _this5.createAlertInputs(_this5.childOpts, inputType, _this5.value),\n        buttons: [{\n          text: _this5.cancelText,\n          role: 'cancel',\n          handler: () => {\n            _this5.ionCancel.emit();\n          }\n        }, {\n          text: _this5.okText,\n          handler: selectedValues => {\n            _this5.setValue(selectedValues);\n          }\n        }],\n        cssClass: ['select-alert', interfaceOptions.cssClass, _this5.multiple ? 'multiple-select-alert' : 'single-select-alert']\n      });\n      return alertController.create(alertOpts);\n    })();\n  }\n  openModal() {\n    const {\n      multiple,\n      value,\n      interfaceOptions\n    } = this;\n    const mode = getIonMode(this);\n    const modalOpts = Object.assign(Object.assign({}, interfaceOptions), {\n      mode,\n      cssClass: ['select-modal', interfaceOptions.cssClass],\n      component: 'ion-select-modal',\n      componentProps: {\n        header: interfaceOptions.header,\n        multiple,\n        value,\n        options: this.createOverlaySelectOptions(this.childOpts, value)\n      }\n    });\n    return modalController.create(modalOpts);\n  }\n  /**\n   * Close the select interface.\n   */\n  close() {\n    if (!this.overlay) {\n      return Promise.resolve(false);\n    }\n    return this.overlay.dismiss();\n  }\n  hasValue() {\n    return this.getText() !== '';\n  }\n  get childOpts() {\n    return Array.from(this.el.querySelectorAll('ion-select-option'));\n  }\n  /**\n   * Returns any plaintext associated with\n   * the label (either prop or slot).\n   * Note: This will not return any custom\n   * HTML. Use the `hasLabel` getter if you\n   * want to know if any slotted label content\n   * was passed.\n   */\n  get labelText() {\n    const {\n      label\n    } = this;\n    if (label !== undefined) {\n      return label;\n    }\n    const {\n      labelSlot\n    } = this;\n    if (labelSlot !== null) {\n      return labelSlot.textContent;\n    }\n    return;\n  }\n  getText() {\n    const selectedText = this.selectedText;\n    if (selectedText != null && selectedText !== '') {\n      return selectedText;\n    }\n    return generateText(this.childOpts, this.value, this.compareWith);\n  }\n  setFocus() {\n    if (this.focusEl) {\n      this.focusEl.focus();\n    }\n  }\n  emitStyle() {\n    const {\n      disabled\n    } = this;\n    const style = {\n      'interactive-disabled': disabled\n    };\n    this.ionStyle.emit(style);\n  }\n  renderLabel() {\n    const {\n      label\n    } = this;\n    return h(\"div\", {\n      class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': !this.hasLabel\n      },\n      part: \"label\"\n    }, label === undefined ? h(\"slot\", {\n      name: \"label\"\n    }) : h(\"div\", {\n      class: \"label-text\"\n    }, label));\n  }\n  componentDidRender() {\n    var _a;\n    (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n  }\n  /**\n   * Gets any content passed into the `label` slot,\n   * not the <slot> definition.\n   */\n  get labelSlot() {\n    return this.el.querySelector('[slot=\"label\"]');\n  }\n  /**\n   * Returns `true` if label content is provided\n   * either by a prop or a content. If you want\n   * to get the plaintext value of the label use\n   * the `labelText` getter instead.\n   */\n  get hasLabel() {\n    return this.label !== undefined || this.labelSlot !== null;\n  }\n  /**\n   * Renders the border container\n   * when fill=\"outline\".\n   */\n  renderLabelContainer() {\n    const mode = getIonMode(this);\n    const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n    if (hasOutlineFill) {\n      /**\n       * The outline fill has a special outline\n       * that appears around the select and the label.\n       * Certain stacked and floating label placements cause the\n       * label to translate up and create a \"cut out\"\n       * inside of that border by using the notch-spacer element.\n       */\n      return [h(\"div\", {\n        class: \"select-outline-container\"\n      }, h(\"div\", {\n        class: \"select-outline-start\"\n      }), h(\"div\", {\n        class: {\n          'select-outline-notch': true,\n          'select-outline-notch-hidden': !this.hasLabel\n        }\n      }, h(\"div\", {\n        class: \"notch-spacer\",\n        \"aria-hidden\": \"true\",\n        ref: el => this.notchSpacerEl = el\n      }, this.label)), h(\"div\", {\n        class: \"select-outline-end\"\n      })), this.renderLabel()];\n    }\n    /**\n     * If not using the outline style,\n     * we can render just the label.\n     */\n    return this.renderLabel();\n  }\n  /**\n   * Renders either the placeholder\n   * or the selected values based on\n   * the state of the select.\n   */\n  renderSelectText() {\n    const {\n      placeholder\n    } = this;\n    const displayValue = this.getText();\n    let addPlaceholderClass = false;\n    let selectText = displayValue;\n    if (selectText === '' && placeholder !== undefined) {\n      selectText = placeholder;\n      addPlaceholderClass = true;\n    }\n    const selectTextClasses = {\n      'select-text': true,\n      'select-placeholder': addPlaceholderClass\n    };\n    const textPart = addPlaceholderClass ? 'placeholder' : 'text';\n    return h(\"div\", {\n      \"aria-hidden\": \"true\",\n      class: selectTextClasses,\n      part: textPart\n    }, selectText);\n  }\n  /**\n   * Renders the chevron icon\n   * next to the select text.\n   */\n  renderSelectIcon() {\n    const mode = getIonMode(this);\n    const {\n      isExpanded,\n      toggleIcon,\n      expandedIcon\n    } = this;\n    let icon;\n    if (isExpanded && expandedIcon !== undefined) {\n      icon = expandedIcon;\n    } else {\n      const defaultIcon = mode === 'ios' ? chevronExpand : caretDownSharp;\n      icon = toggleIcon !== null && toggleIcon !== void 0 ? toggleIcon : defaultIcon;\n    }\n    return h(\"ion-icon\", {\n      class: \"select-icon\",\n      part: \"icon\",\n      \"aria-hidden\": \"true\",\n      icon: icon\n    });\n  }\n  get ariaLabel() {\n    var _a;\n    const {\n      placeholder,\n      inheritedAttributes\n    } = this;\n    const displayValue = this.getText();\n    // The aria label should be preferred over visible text if both are specified\n    const definedLabel = (_a = inheritedAttributes['aria-label']) !== null && _a !== void 0 ? _a : this.labelText;\n    /**\n     * If developer has specified a placeholder\n     * and there is nothing selected, the selectText\n     * should have the placeholder value.\n     */\n    let renderedLabel = displayValue;\n    if (renderedLabel === '' && placeholder !== undefined) {\n      renderedLabel = placeholder;\n    }\n    /**\n     * If there is a developer-defined label,\n     * then we need to concatenate the developer label\n     * string with the current current value.\n     * The label for the control should be read\n     * before the values of the control.\n     */\n    if (definedLabel !== undefined) {\n      renderedLabel = renderedLabel === '' ? definedLabel : `${definedLabel}, ${renderedLabel}`;\n    }\n    return renderedLabel;\n  }\n  renderListbox() {\n    const {\n      disabled,\n      inputId,\n      isExpanded,\n      required\n    } = this;\n    return h(\"button\", {\n      disabled: disabled,\n      id: inputId,\n      \"aria-label\": this.ariaLabel,\n      \"aria-haspopup\": \"dialog\",\n      \"aria-expanded\": `${isExpanded}`,\n      \"aria-describedby\": this.getHintTextID(),\n      \"aria-invalid\": this.getHintTextID() === this.errorTextId,\n      \"aria-required\": `${required}`,\n      onFocus: this.onFocus,\n      onBlur: this.onBlur,\n      ref: focusEl => this.focusEl = focusEl\n    });\n  }\n  getHintTextID() {\n    const {\n      el,\n      helperText,\n      errorText,\n      helperTextId,\n      errorTextId\n    } = this;\n    if (el.classList.contains('ion-touched') && el.classList.contains('ion-invalid') && errorText) {\n      return errorTextId;\n    }\n    if (helperText) {\n      return helperTextId;\n    }\n    return undefined;\n  }\n  /**\n   * Renders the helper text or error text values\n   */\n  renderHintText() {\n    const {\n      helperText,\n      errorText,\n      helperTextId,\n      errorTextId\n    } = this;\n    return [h(\"div\", {\n      id: helperTextId,\n      class: \"helper-text\",\n      part: \"supporting-text helper-text\"\n    }, helperText), h(\"div\", {\n      id: errorTextId,\n      class: \"error-text\",\n      part: \"supporting-text error-text\"\n    }, errorText)];\n  }\n  /**\n   * Responsible for rendering helper text, and error text. This element\n   * should only be rendered if hint text is set.\n   */\n  renderBottomContent() {\n    const {\n      helperText,\n      errorText\n    } = this;\n    /**\n     * undefined and empty string values should\n     * be treated as not having helper/error text.\n     */\n    const hasHintText = !!helperText || !!errorText;\n    if (!hasHintText) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"select-bottom\"\n    }, this.renderHintText());\n  }\n  render() {\n    const {\n      disabled,\n      el,\n      isExpanded,\n      expandedIcon,\n      labelPlacement,\n      justify,\n      placeholder,\n      fill,\n      shape,\n      name,\n      value,\n      hasFocus\n    } = this;\n    const mode = getIonMode(this);\n    const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n    const justifyEnabled = !hasFloatingOrStackedLabel && justify !== undefined;\n    const rtl = isRTL(el) ? 'rtl' : 'ltr';\n    const inItem = hostContext('ion-item', this.el);\n    const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n    const hasValue = this.hasValue();\n    const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n    renderHiddenInput(true, el, name, parseValue(value), disabled);\n    /**\n     * If the label is stacked, it should always sit above the select.\n     * For floating labels, the label should move above the select if\n     * the select has a value, is open, or has anything in either\n     * the start or end slot.\n     *\n     * If there is content in the start slot, the label would overlap\n     * it if not forced to float. This is also applied to the end slot\n     * because with the default or solid fills, the select is not\n     * vertically centered in the container, but the label is. This\n     * causes the slots and label to appear vertically offset from each\n     * other when the label isn't floating above the input. This doesn't\n     * apply to the outline fill, but this was not accounted for to keep\n     * things consistent.\n     *\n     * TODO(FW-5592): Remove hasStartEndSlots condition\n     */\n    const labelShouldFloat = labelPlacement === 'stacked' || labelPlacement === 'floating' && (hasValue || isExpanded || hasStartEndSlots);\n    return h(Host, {\n      key: 'c03fb65e8fc9f9aab295e07b282377d57d910519',\n      onClick: this.onClick,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'in-item': inItem,\n        'in-item-color': hostContext('ion-item.ion-color', el),\n        'select-disabled': disabled,\n        'select-expanded': isExpanded,\n        'has-expanded-icon': expandedIcon !== undefined,\n        'has-value': hasValue,\n        'label-floating': labelShouldFloat,\n        'has-placeholder': placeholder !== undefined,\n        'has-focus': hasFocus,\n        // TODO(FW-6451): Remove `ion-focusable` class in favor of `has-focus`.\n        'ion-focusable': true,\n        [`select-${rtl}`]: true,\n        [`select-fill-${fill}`]: fill !== undefined,\n        [`select-justify-${justify}`]: justifyEnabled,\n        [`select-shape-${shape}`]: shape !== undefined,\n        [`select-label-placement-${labelPlacement}`]: true\n      })\n    }, h(\"label\", {\n      key: '0d0c8ec55269adcac625f2899a547f4e7f3e3741',\n      class: \"select-wrapper\",\n      id: \"select-label\",\n      onClick: this.onLabelClick\n    }, this.renderLabelContainer(), h(\"div\", {\n      key: 'f6dfc93c0e23cbe75a2947abde67d842db2dad78',\n      class: \"select-wrapper-inner\"\n    }, h(\"slot\", {\n      key: '957bfadf9f101f519091419a362d3abdc2be66f6',\n      name: \"start\"\n    }), h(\"div\", {\n      key: 'ca349202a484e7f2e884533fd330f0b136754f7d',\n      class: \"native-wrapper\",\n      ref: el => this.nativeWrapperEl = el,\n      part: \"container\"\n    }, this.renderSelectText(), this.renderListbox()), h(\"slot\", {\n      key: 'f0e62a6533ff1c8f62bd2d27f60b23385c4fa9ed',\n      name: \"end\"\n    }), !hasFloatingOrStackedLabel && this.renderSelectIcon()), hasFloatingOrStackedLabel && this.renderSelectIcon(), shouldRenderHighlight && h(\"div\", {\n      key: 'fb840d46bafafb09898ebeebbe8c181906a3d8a2',\n      class: \"select-highlight\"\n    })), this.renderBottomContent());\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"disabled\": [\"styleChanged\"],\n      \"isExpanded\": [\"styleChanged\"],\n      \"placeholder\": [\"styleChanged\"],\n      \"value\": [\"styleChanged\"]\n    };\n  }\n};\nconst getOptionValue = el => {\n  const value = el.value;\n  return value === undefined ? el.textContent || '' : value;\n};\nconst parseValue = value => {\n  if (value == null) {\n    return undefined;\n  }\n  if (Array.isArray(value)) {\n    return value.join(',');\n  }\n  return value.toString();\n};\nconst generateText = (opts, value, compareWith) => {\n  if (value === undefined) {\n    return '';\n  }\n  if (Array.isArray(value)) {\n    return value.map(v => textForValue(opts, v, compareWith)).filter(opt => opt !== null).join(', ');\n  } else {\n    return textForValue(opts, value, compareWith) || '';\n  }\n};\nconst textForValue = (opts, value, compareWith) => {\n  const selectOpt = opts.find(opt => {\n    return compareOptions(value, getOptionValue(opt), compareWith);\n  });\n  return selectOpt ? selectOpt.textContent : null;\n};\nlet selectIds = 0;\nconst OPTION_CLASS = 'select-interface-option';\nSelect.style = {\n  ios: selectIosCss,\n  md: selectMdCss\n};\nconst selectOptionCss = \":host{display:none}\";\nconst SelectOption = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.inputId = `ion-selopt-${selectOptionIds++}`;\n    /**\n     * If `true`, the user cannot interact with the select option. This property does not apply when `interface=\"action-sheet\"` as `ion-action-sheet` does not allow for disabled buttons.\n     */\n    this.disabled = false;\n  }\n  render() {\n    return h(Host, {\n      key: '3a70eea9fa03a9acba582180761d18347c72acee',\n      role: \"option\",\n      id: this.inputId,\n      class: getIonMode(this)\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nlet selectOptionIds = 0;\nSelectOption.style = selectOptionCss;\nconst selectPopoverIosCss = \".sc-ion-select-popover-ios-h ion-list.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-ios,ion-label.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-ios-h{overflow-y:auto}\";\nconst selectPopoverMdCss = \".sc-ion-select-popover-md-h ion-list.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-md,ion-label.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-md-h{overflow-y:auto}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(container){display:none}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(label){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-item.sc-ion-select-popover-md{--inner-border-width:0}.item-radio-checked.sc-ion-select-popover-md{--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08);--background-focused:var(--ion-color-primary, #0054e9);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #0054e9);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-popover-md{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #0054e9)}\";\nconst SelectPopover = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    /**\n     * An array of options for the popover\n     */\n    this.options = [];\n  }\n  findOptionFromEvent(ev) {\n    const {\n      options\n    } = this;\n    return options.find(o => o.value === ev.target.value);\n  }\n  /**\n   * When an option is selected we need to get the value(s)\n   * of the selected option(s) and return it in the option\n   * handler\n   */\n  callOptionHandler(ev) {\n    const option = this.findOptionFromEvent(ev);\n    const values = this.getValues(ev);\n    if (option === null || option === void 0 ? void 0 : option.handler) {\n      safeCall(option.handler, values);\n    }\n  }\n  /**\n   * Dismisses the host popover that the `ion-select-popover`\n   * is rendered within.\n   */\n  dismissParentPopover() {\n    const popover = this.el.closest('ion-popover');\n    if (popover) {\n      popover.dismiss();\n    }\n  }\n  setChecked(ev) {\n    const {\n      multiple\n    } = this;\n    const option = this.findOptionFromEvent(ev);\n    // this is a popover with checkboxes (multiple value select)\n    // we need to set the checked value for this option\n    if (multiple && option) {\n      option.checked = ev.detail.checked;\n    }\n  }\n  getValues(ev) {\n    const {\n      multiple,\n      options\n    } = this;\n    if (multiple) {\n      // this is a popover with checkboxes (multiple value select)\n      // return an array of all the checked values\n      return options.filter(o => o.checked).map(o => o.value);\n    }\n    // this is a popover with radio buttons (single value select)\n    // return the value that was clicked, otherwise undefined\n    const option = this.findOptionFromEvent(ev);\n    return option ? option.value : undefined;\n  }\n  renderOptions(options) {\n    const {\n      multiple\n    } = this;\n    switch (multiple) {\n      case true:\n        return this.renderCheckboxOptions(options);\n      default:\n        return this.renderRadioOptions(options);\n    }\n  }\n  renderCheckboxOptions(options) {\n    return options.map(option => h(\"ion-item\", {\n      class: Object.assign({\n        // TODO FW-4784\n        'item-checkbox-checked': option.checked\n      }, getClassMap(option.cssClass))\n    }, h(\"ion-checkbox\", {\n      value: option.value,\n      disabled: option.disabled,\n      checked: option.checked,\n      justify: \"start\",\n      labelPlacement: \"end\",\n      onIonChange: ev => {\n        this.setChecked(ev);\n        this.callOptionHandler(ev);\n        // TODO FW-4784\n        forceUpdate(this);\n      }\n    }, option.text)));\n  }\n  renderRadioOptions(options) {\n    const checked = options.filter(o => o.checked).map(o => o.value)[0];\n    return h(\"ion-radio-group\", {\n      value: checked,\n      onIonChange: ev => this.callOptionHandler(ev)\n    }, options.map(option => h(\"ion-item\", {\n      class: Object.assign({\n        // TODO FW-4784\n        'item-radio-checked': option.value === checked\n      }, getClassMap(option.cssClass))\n    }, h(\"ion-radio\", {\n      value: option.value,\n      disabled: option.disabled,\n      onClick: () => this.dismissParentPopover(),\n      onKeyUp: ev => {\n        if (ev.key === ' ') {\n          /**\n           * Selecting a radio option with keyboard navigation,\n           * either through the Enter or Space keys, should\n           * dismiss the popover.\n           */\n          this.dismissParentPopover();\n        }\n      }\n    }, option.text))));\n  }\n  render() {\n    const {\n      header,\n      message,\n      options,\n      subHeader\n    } = this;\n    const hasSubHeaderOrMessage = subHeader !== undefined || message !== undefined;\n    return h(Host, {\n      key: 'ab931b49b59283825bd2afa3f7f995b0e6e05bef',\n      class: getIonMode(this)\n    }, h(\"ion-list\", {\n      key: '3bd12b67832607596b912a73d5b3ae9b954b244d'\n    }, header !== undefined && h(\"ion-list-header\", {\n      key: '97da930246edf7423a039c030d40e3ff7a5148a3'\n    }, header), hasSubHeaderOrMessage && h(\"ion-item\", {\n      key: 'c579df6ea8fac07bb0c59d34c69b149656863224'\n    }, h(\"ion-label\", {\n      key: 'af699c5f465710ccb13b8cf8e7be66f0e8acfad1',\n      class: \"ion-text-wrap\"\n    }, subHeader !== undefined && h(\"h3\", {\n      key: 'df9a936d42064b134e843c7229f314a2a3ec7e80'\n    }, subHeader), message !== undefined && h(\"p\", {\n      key: '9c3ddad378df00f106afa94e9928cf68c17124dd'\n    }, message))), this.renderOptions(options)));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nSelectPopover.style = {\n  ios: selectPopoverIosCss,\n  md: selectPopoverMdCss\n};\nexport { Select as ion_select, SelectOption as ion_select_option, SelectPopover as ion_select_popover };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "m", "printIonWarning", "e", "getIonMode", "h", "j", "Host", "k", "getElement", "n", "forceUpdate", "c", "createNotchController", "i", "isOptionSelected", "compareOptions", "b", "inheritAttributes", "a", "renderHiddenInput", "focusVisibleElement", "popoverController", "actionSheetController", "alertController", "modalController", "s", "safeCall", "isRTL", "hostContext", "createColorClasses", "g", "getClassMap", "w", "watchForOptions", "chevronExpand", "q", "caretDownSharp", "selectIosCss", "selectMdCss", "Select", "constructor", "hostRef", "ionChange", "ionCancel", "ion<PERSON><PERSON><PERSON>", "ionFocus", "ionBlur", "ionStyle", "inputId", "selectIds", "helperTextId", "errorTextId", "inheritedAttributes", "isExpanded", "hasFocus", "cancelText", "disabled", "interface", "interfaceOptions", "labelPlacement", "multiple", "name", "okText", "required", "onClick", "ev", "target", "closestSlot", "closest", "el", "setFocus", "open", "preventDefault", "onFocus", "emit", "onBlur", "onLabelClick", "currentTarget", "stopPropagation", "styleChanged", "emitStyle", "setValue", "value", "connectedCallback", "_this", "_asyncToGenerator", "notchController", "notchSpacerEl", "labelSlot", "updateOverlayOptions", "mutationO", "componentWillLoad", "componentDidLoad", "disconnectedCallback", "disconnect", "undefined", "destroy", "event", "_this2", "overlay", "createOverlay", "scrollSelectedIntoView", "indexOfSelected", "childOpts", "findIndex", "o", "selectedItem", "querySelector", "interactiveEl", "scrollIntoView", "block", "firstEnabledOption", "addEventListener", "once", "scrollAfterRender", "requestAnimationFrame", "onDid<PERSON><PERSON><PERSON>", "then", "present", "selectInterface", "openActionSheet", "openPopover", "openModal", "openAlert", "buttons", "createActionSheetButtons", "popover", "options", "createOverlaySelectOptions", "modal", "inputType", "inputs", "createAlertInputs", "data", "selectValue", "actionSheetButtons", "map", "option", "getOptionValue", "copyClasses", "Array", "from", "classList", "filter", "cls", "join", "optClass", "OPTION_CLASS", "role", "compareWith", "text", "textContent", "cssClass", "handler", "push", "alertInputs", "type", "label", "checked", "popoverOptions", "selected", "close", "_this3", "fill", "mode", "showBackdrop", "size", "hasFloatingOrStackedLabel", "Object", "assign", "detail", "ionShadowTarget", "nativeWrapperEl", "popoverOpts", "alignment", "component", "componentProps", "header", "subHeader", "message", "create", "_this4", "actionSheetOpts", "_this5", "alertOpts", "labelText", "<PERSON><PERSON><PERSON><PERSON>", "modalOpts", "Promise", "resolve", "dismiss", "hasValue", "getText", "querySelectorAll", "selectedText", "generateText", "focusEl", "focus", "style", "renderLabel", "class", "<PERSON><PERSON><PERSON><PERSON>", "part", "componentDidRender", "_a", "calculateNotchWidth", "renderLabelContainer", "hasOutlineFill", "ref", "renderSelectText", "placeholder", "displayValue", "addPlaceholderClass", "selectText", "selectTextClasses", "textPart", "renderSelectIcon", "toggleIcon", "expandedIcon", "icon", "defaultIcon", "aria<PERSON><PERSON><PERSON>", "defined<PERSON>abel", "<PERSON><PERSON><PERSON><PERSON>", "renderListbox", "id", "getHintTextID", "helperText", "errorText", "contains", "renderHintText", "renderBottomContent", "hasHintText", "render", "justify", "shape", "justifyEnabled", "rtl", "inItem", "should<PERSON>ender<PERSON>ighlight", "hasStartEndSlots", "parseValue", "labelShouldFloat", "key", "color", "watchers", "isArray", "toString", "opts", "v", "textForValue", "opt", "selectOpt", "find", "ios", "md", "selectOptionCss", "SelectOption", "selectOptionIds", "selectPopoverIosCss", "selectPopoverMdCss", "SelectPopover", "findOptionFromEvent", "callOptionHandler", "values", "getV<PERSON>ues", "dismissParentPopover", "setChecked", "renderOptions", "renderCheckboxOptions", "renderRadioOptions", "onIonChange", "onKeyUp", "hasSubHeaderOrMessage", "ion_select", "ion_select_option", "ion_select_popover"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@ionic/core/dist/esm/ion-select_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, m as printIonWarning, e as getIonMode, h, j as Host, k as getElement, n as forceUpdate } from './index-B_U9CtaY.js';\nimport { c as createNotchController } from './notch-controller-C5LPspO8.js';\nimport { i as isOptionSelected, c as compareOptions } from './compare-with-utils-sObYyvOy.js';\nimport { b as inheritAttributes, a as renderHiddenInput, n as focusVisibleElement } from './helpers-1O4D2b7y.js';\nimport { c as popoverController, b as actionSheetController, a as alertController, m as modalController, s as safeCall } from './overlays-8Y2rA-ps.js';\nimport { i as isRTL } from './dir-C53feagD.js';\nimport { h as hostContext, c as createColorClasses, g as getClassMap } from './theme-DiVJyqlX.js';\nimport { w as watchForOptions } from './watch-options-Dtdm8lKC.js';\nimport { w as chevronExpand, q as caretDownSharp } from './index-BLV6ykCk.js';\nimport './index-ZjP4CjeZ.js';\nimport './hardware-back-button-DcH0BbDp.js';\nimport './framework-delegate-DxcnWic_.js';\nimport './gesture-controller-BTEOs1at.js';\n\nconst selectIosCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0}:host(.select-disabled){pointer-events:none}:host(.has-focus) button{border:2px solid #5e9ed6}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.select-bottom{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}:host(.has-focus.ion-valid),:host(.select-expanded.ion-valid),:host(.ion-touched.ion-invalid),:host(.select-expanded.ion-touched.ion-invalid){--border-color:var(--highlight-color)}.select-bottom .error-text{display:none;color:var(--highlight-color-invalid)}.select-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .select-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .select-bottom .helper-text{display:none}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-focus.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]:last-of-type){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]:first-of-type){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host{--border-width:0.55px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--highlight-height:0px}.select-icon{width:1.125rem;height:1.125rem;color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959))}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 1.125rem - 4px)}:host(.select-disabled){opacity:0.3}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\n\nconst selectMdCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0}:host(.select-disabled){pointer-events:none}:host(.has-focus) button{border:2px solid #5e9ed6}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.select-bottom{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}:host(.has-focus.ion-valid),:host(.select-expanded.ion-valid),:host(.ion-touched.ion-invalid),:host(.select-expanded.ion-touched.ion-invalid){--border-color:var(--highlight-color)}.select-bottom .error-text{display:none;color:var(--highlight-color-invalid)}.select-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .select-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .select-bottom .helper-text{display:none}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-focus.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]:last-of-type){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]:first-of-type){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.select-fill-solid){--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-500, var(--ion-background-color-step-500, gray));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-solid) .select-wrapper{border-bottom:var(--border-width) var(--border-style) var(--border-color)}:host(.select-expanded.select-fill-solid.ion-valid),:host(.has-focus.select-fill-solid.ion-valid),:host(.select-fill-solid.ion-touched.ion-invalid){--border-color:var(--highlight-color)}:host(.select-fill-solid) .select-bottom{border-top:none}@media (any-hover: hover){:host(.select-fill-solid:hover){--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}:host(.select-fill-solid.select-expanded),:host(.select-fill-solid.has-focus){--background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--border-color:var(--highlight-color)}:host(.select-fill-solid) .select-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0px;border-end-start-radius:0px}:host(.label-floating.select-fill-solid) .label-text-wrapper{max-width:calc(100% / 0.75)}:host(.in-item.select-expanded.select-fill-solid) .select-wrapper .select-icon,:host(.in-item.has-focus.select-fill-solid) .select-wrapper .select-icon,:host(.in-item.has-focus.ion-valid.select-fill-solid) .select-wrapper .select-icon,:host(.in-item.ion-touched.ion-invalid.select-fill-solid) .select-wrapper .select-icon{color:var(--highlight-color)}:host(.select-fill-outline){--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-outline.select-shape-round){--border-radius:28px;--padding-start:32px;--padding-end:32px}:host(.has-focus.select-fill-outline.ion-valid),:host(.select-fill-outline.ion-touched.ion-invalid){--border-color:var(--highlight-color)}@media (any-hover: hover){:host(.select-fill-outline:hover){--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}:host(.select-fill-outline.select-expanded),:host(.select-fill-outline.has-focus){--border-width:var(--highlight-height);--border-color:var(--highlight-color)}:host(.select-fill-outline) .select-bottom{border-top:none}:host(.select-fill-outline) .select-wrapper{border-bottom:none}:host(.select-ltr.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-fill-outline.select-label-placement-floating) .label-text-wrapper{position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .label-text-wrapper{position:relative;z-index:1}:host(.label-floating.select-fill-outline) .label-text-wrapper{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}:host(.select-fill-outline.select-label-placement-stacked) select,:host(.select-fill-outline.select-label-placement-floating) select{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}:host(.select-fill-outline) .select-outline-container{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-end{pointer-events:none}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-notch,:host(.select-fill-outline) .select-outline-end{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.select-fill-outline) .select-outline-notch{max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .notch-spacer{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none}:host(.select-fill-outline) .select-outline-start{-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color)}:host(.select-fill-outline) .select-outline-start{border-start-start-radius:var(--border-radius);border-start-end-radius:0px;border-end-end-radius:0px;border-end-start-radius:var(--border-radius)}:host(.select-fill-outline) .select-outline-start{width:calc(var(--padding-start) - 4px)}:host(.select-fill-outline) .select-outline-end{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color)}:host(.select-fill-outline) .select-outline-end{border-start-start-radius:0px;border-start-end-radius:var(--border-radius);border-end-end-radius:var(--border-radius);border-end-start-radius:0px}:host(.select-fill-outline) .select-outline-end{-ms-flex-positive:1;flex-grow:1}:host(.label-floating.select-fill-outline) .select-outline-notch{border-top:none}:host(.in-item.select-expanded.select-fill-outline) .select-wrapper .select-icon,:host(.in-item.has-focus.select-fill-outline) .select-wrapper .select-icon,:host(.in-item.has-focus.ion-valid.select-fill-outline) .select-wrapper .select-icon,:host(.in-item.ion-touched.ion-invalid.select-fill-outline) .select-wrapper .select-icon{color:var(--highlight-color)}:host{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--highlight-height:2px}:host(.select-label-placement-floating.select-expanded) .label-text-wrapper,:host(.select-label-placement-floating.has-focus) .label-text-wrapper,:host(.select-label-placement-stacked.select-expanded) .label-text-wrapper,:host(.select-label-placement-stacked.has-focus) .label-text-wrapper{color:var(--highlight-color)}:host(.has-focus.select-label-placement-floating.ion-valid) .label-text-wrapper,:host(.select-label-placement-floating.ion-touched.ion-invalid) .label-text-wrapper,:host(.has-focus.select-label-placement-stacked.ion-valid) .label-text-wrapper,:host(.select-label-placement-stacked.ion-touched.ion-invalid) .label-text-wrapper{color:var(--highlight-color)}.select-highlight{bottom:-1px;position:absolute;width:100%;height:var(--highlight-height);-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}.select-highlight{inset-inline-start:0}:host(.select-expanded) .select-highlight,:host(.has-focus) .select-highlight{-webkit-transform:scale(1);transform:scale(1)}:host(.in-item) .select-highlight{bottom:0}:host(.in-item) .select-highlight{inset-inline-start:0}.select-icon{width:0.8125rem;-webkit-transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray))}:host(.select-expanded:not(.has-expanded-icon)) .select-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.in-item.select-expanded) .select-wrapper .select-icon,:host(.in-item.has-focus) .select-wrapper .select-icon,:host(.in-item.has-focus.ion-valid) .select-wrapper .select-icon,:host(.in-item.ion-touched.ion-invalid) .select-wrapper .select-icon{color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray))}:host(.select-expanded) .select-wrapper .select-icon,:host(.has-focus.ion-valid) .select-wrapper .select-icon,:host(.ion-touched.ion-invalid) .select-wrapper .select-icon,:host(.has-focus) .select-wrapper .select-icon{color:var(--highlight-color)}:host(.select-shape-round){--border-radius:16px}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 0.8125rem - 4px)}:host(.select-disabled){opacity:0.38}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\n\nconst Select = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionCancel = createEvent(this, \"ionCancel\", 7);\n        this.ionDismiss = createEvent(this, \"ionDismiss\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.inputId = `ion-sel-${selectIds++}`;\n        this.helperTextId = `${this.inputId}-helper-text`;\n        this.errorTextId = `${this.inputId}-error-text`;\n        this.inheritedAttributes = {};\n        this.isExpanded = false;\n        /**\n         * The `hasFocus` state ensures the focus class is\n         * added regardless of how the element is focused.\n         * The `ion-focused` class only applies when focused\n         * via tabbing, not by clicking.\n         * The `has-focus` logic was added to ensure the class\n         * is applied in both cases.\n         */\n        this.hasFocus = false;\n        /**\n         * The text to display on the cancel button.\n         */\n        this.cancelText = 'Cancel';\n        /**\n         * If `true`, the user cannot interact with the select.\n         */\n        this.disabled = false;\n        /**\n         * The interface the select should use: `action-sheet`, `popover`, `alert`, or `modal`.\n         */\n        this.interface = 'alert';\n        /**\n         * Any additional options that the `alert`, `action-sheet` or `popover` interface\n         * can take. See the [ion-alert docs](./alert), the\n         * [ion-action-sheet docs](./action-sheet), the\n         * [ion-popover docs](./popover), and the [ion-modal docs](./modal) for the\n         * create options for each interface.\n         *\n         * Note: `interfaceOptions` will not override `inputs` or `buttons` with the `alert` interface.\n         */\n        this.interfaceOptions = {};\n        /**\n         * Where to place the label relative to the select.\n         * `\"start\"`: The label will appear to the left of the select in LTR and to the right in RTL.\n         * `\"end\"`: The label will appear to the right of the select in LTR and to the left in RTL.\n         * `\"floating\"`: The label will appear smaller and above the select when the select is focused or it has a value. Otherwise it will appear on top of the select.\n         * `\"stacked\"`: The label will appear smaller and above the select regardless even when the select is blurred or has no value.\n         * `\"fixed\"`: The label has the same behavior as `\"start\"` except it also has a fixed width. Long text will be truncated with ellipses (\"...\").\n         * When using `\"floating\"` or `\"stacked\"` we recommend initializing the select with either a `value` or a `placeholder`.\n         */\n        this.labelPlacement = 'start';\n        /**\n         * If `true`, the select can accept multiple values.\n         */\n        this.multiple = false;\n        /**\n         * The name of the control, which is submitted with the form data.\n         */\n        this.name = this.inputId;\n        /**\n         * The text to display on the ok button.\n         */\n        this.okText = 'OK';\n        /**\n         * If true, screen readers will announce it as a required field. This property\n         * works only for accessibility purposes, it will not prevent the form from\n         * submitting if the value is invalid.\n         */\n        this.required = false;\n        this.onClick = (ev) => {\n            const target = ev.target;\n            const closestSlot = target.closest('[slot=\"start\"], [slot=\"end\"]');\n            if (target === this.el || closestSlot === null) {\n                this.setFocus();\n                this.open(ev);\n            }\n            else {\n                /**\n                 * Prevent clicks to the start/end slots from opening the select.\n                 * We ensure the target isn't this element in case the select is slotted\n                 * in, for example, an item. This would prevent the select from ever\n                 * being opened since the element itself has slot=\"start\"/\"end\".\n                 *\n                 * Clicking a slotted element also causes a click\n                 * on the <label> element (since it wraps the slots).\n                 * Clicking <label> dispatches another click event on\n                 * the native form control that then bubbles up to this\n                 * listener. This additional event targets the host\n                 * element, so the select overlay is opened.\n                 *\n                 * When the slotted elements are clicked (and therefore\n                 * the ancestor <label> element) we want to prevent the label\n                 * from dispatching another click event.\n                 *\n                 * Do not call stopPropagation() because this will cause\n                 * click handlers on the slotted elements to never fire in React.\n                 * When developers do onClick in React a native \"click\" listener\n                 * is added on the root element, not the slotted element. When that\n                 * native click listener fires, React then dispatches the synthetic\n                 * click event on the slotted element. However, if stopPropagation\n                 * is called then the native click event will never bubble up\n                 * to the root element.\n                 */\n                ev.preventDefault();\n            }\n        };\n        this.onFocus = () => {\n            this.hasFocus = true;\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.hasFocus = false;\n            this.ionBlur.emit();\n        };\n        /**\n         * Stops propagation when the label is clicked,\n         * otherwise, two clicks will be triggered.\n         */\n        this.onLabelClick = (ev) => {\n            // Only stop propagation if the click was directly on the label\n            // and not on the input or other child elements\n            if (ev.target === ev.currentTarget) {\n                ev.stopPropagation();\n            }\n        };\n    }\n    styleChanged() {\n        this.emitStyle();\n    }\n    setValue(value) {\n        this.value = value;\n        this.ionChange.emit({ value });\n    }\n    async connectedCallback() {\n        const { el } = this;\n        this.notchController = createNotchController(el, () => this.notchSpacerEl, () => this.labelSlot);\n        this.updateOverlayOptions();\n        this.emitStyle();\n        this.mutationO = watchForOptions(this.el, 'ion-select-option', async () => {\n            this.updateOverlayOptions();\n            /**\n             * We need to re-render the component\n             * because one of the new ion-select-option\n             * elements may match the value. In this case,\n             * the rendered selected text should be updated.\n             */\n            forceUpdate(this);\n        });\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAttributes(this.el, ['aria-label']);\n    }\n    componentDidLoad() {\n        /**\n         * If any of the conditions that trigger the styleChanged callback\n         * are met on component load, it is possible the event emitted\n         * prior to a parent web component registering an event listener.\n         *\n         * To ensure the parent web component receives the event, we\n         * emit the style event again after the component has loaded.\n         *\n         * This is often seen in Angular with the `dist` output target.\n         */\n        this.emitStyle();\n    }\n    disconnectedCallback() {\n        if (this.mutationO) {\n            this.mutationO.disconnect();\n            this.mutationO = undefined;\n        }\n        if (this.notchController) {\n            this.notchController.destroy();\n            this.notchController = undefined;\n        }\n    }\n    /**\n     * Open the select overlay. The overlay is either an alert, action sheet, or popover,\n     * depending on the `interface` property on the `ion-select`.\n     *\n     * @param event The user interface event that called the open.\n     */\n    async open(event) {\n        if (this.disabled || this.isExpanded) {\n            return undefined;\n        }\n        this.isExpanded = true;\n        const overlay = (this.overlay = await this.createOverlay(event));\n        // Add logic to scroll selected item into view before presenting\n        const scrollSelectedIntoView = () => {\n            const indexOfSelected = this.childOpts.findIndex((o) => o.value === this.value);\n            if (indexOfSelected > -1) {\n                const selectedItem = overlay.querySelector(`.select-interface-option:nth-of-type(${indexOfSelected + 1})`);\n                if (selectedItem) {\n                    /**\n                     * Browsers such as Firefox do not\n                     * correctly delegate focus when manually\n                     * focusing an element with delegatesFocus.\n                     * We work around this by manually focusing\n                     * the interactive element.\n                     * ion-radio and ion-checkbox are the only\n                     * elements that ion-select-popover uses, so\n                     * we only need to worry about those two components\n                     * when focusing.\n                     */\n                    const interactiveEl = selectedItem.querySelector('ion-radio, ion-checkbox');\n                    if (interactiveEl) {\n                        selectedItem.scrollIntoView({ block: 'nearest' });\n                        // Needs to be called before `focusVisibleElement` to prevent issue with focus event bubbling\n                        // and removing `ion-focused` style\n                        interactiveEl.setFocus();\n                    }\n                    focusVisibleElement(selectedItem);\n                }\n            }\n            else {\n                /**\n                 * If no value is set then focus the first enabled option.\n                 */\n                const firstEnabledOption = overlay.querySelector('ion-radio:not(.radio-disabled), ion-checkbox:not(.checkbox-disabled)');\n                if (firstEnabledOption) {\n                    /**\n                     * Focus the option for the same reason as we do above.\n                     *\n                     * Needs to be called before `focusVisibleElement` to prevent issue with focus event bubbling\n                     * and removing `ion-focused` style\n                     */\n                    firstEnabledOption.setFocus();\n                    focusVisibleElement(firstEnabledOption.closest('ion-item'));\n                }\n            }\n        };\n        // For modals and popovers, we can scroll before they're visible\n        if (this.interface === 'modal') {\n            overlay.addEventListener('ionModalWillPresent', scrollSelectedIntoView, { once: true });\n        }\n        else if (this.interface === 'popover') {\n            overlay.addEventListener('ionPopoverWillPresent', scrollSelectedIntoView, { once: true });\n        }\n        else {\n            /**\n             * For alerts and action sheets, we need to wait a frame after willPresent\n             * because these overlays don't have their content in the DOM immediately\n             * when willPresent fires. By waiting a frame, we ensure the content is\n             * rendered and can be properly scrolled into view.\n             */\n            const scrollAfterRender = () => {\n                requestAnimationFrame(() => {\n                    scrollSelectedIntoView();\n                });\n            };\n            if (this.interface === 'alert') {\n                overlay.addEventListener('ionAlertWillPresent', scrollAfterRender, { once: true });\n            }\n            else if (this.interface === 'action-sheet') {\n                overlay.addEventListener('ionActionSheetWillPresent', scrollAfterRender, { once: true });\n            }\n        }\n        overlay.onDidDismiss().then(() => {\n            this.overlay = undefined;\n            this.isExpanded = false;\n            this.ionDismiss.emit();\n            this.setFocus();\n        });\n        await overlay.present();\n        return overlay;\n    }\n    createOverlay(ev) {\n        let selectInterface = this.interface;\n        if (selectInterface === 'action-sheet' && this.multiple) {\n            printIonWarning(`[ion-select] - Interface cannot be \"${selectInterface}\" with a multi-value select. Using the \"alert\" interface instead.`);\n            selectInterface = 'alert';\n        }\n        if (selectInterface === 'popover' && !ev) {\n            printIonWarning(`[ion-select] - Interface cannot be a \"${selectInterface}\" without passing an event. Using the \"alert\" interface instead.`);\n            selectInterface = 'alert';\n        }\n        if (selectInterface === 'action-sheet') {\n            return this.openActionSheet();\n        }\n        if (selectInterface === 'popover') {\n            return this.openPopover(ev);\n        }\n        if (selectInterface === 'modal') {\n            return this.openModal();\n        }\n        return this.openAlert();\n    }\n    updateOverlayOptions() {\n        const overlay = this.overlay;\n        if (!overlay) {\n            return;\n        }\n        const childOpts = this.childOpts;\n        const value = this.value;\n        switch (this.interface) {\n            case 'action-sheet':\n                overlay.buttons = this.createActionSheetButtons(childOpts, value);\n                break;\n            case 'popover':\n                const popover = overlay.querySelector('ion-select-popover');\n                if (popover) {\n                    popover.options = this.createOverlaySelectOptions(childOpts, value);\n                }\n                break;\n            case 'modal':\n                const modal = overlay.querySelector('ion-select-modal');\n                if (modal) {\n                    modal.options = this.createOverlaySelectOptions(childOpts, value);\n                }\n                break;\n            case 'alert':\n                const inputType = this.multiple ? 'checkbox' : 'radio';\n                overlay.inputs = this.createAlertInputs(childOpts, inputType, value);\n                break;\n        }\n    }\n    createActionSheetButtons(data, selectValue) {\n        const actionSheetButtons = data.map((option) => {\n            const value = getOptionValue(option);\n            // Remove hydrated before copying over classes\n            const copyClasses = Array.from(option.classList)\n                .filter((cls) => cls !== 'hydrated')\n                .join(' ');\n            const optClass = `${OPTION_CLASS} ${copyClasses}`;\n            return {\n                role: isOptionSelected(selectValue, value, this.compareWith) ? 'selected' : '',\n                text: option.textContent,\n                cssClass: optClass,\n                handler: () => {\n                    this.setValue(value);\n                },\n            };\n        });\n        // Add \"cancel\" button\n        actionSheetButtons.push({\n            text: this.cancelText,\n            role: 'cancel',\n            handler: () => {\n                this.ionCancel.emit();\n            },\n        });\n        return actionSheetButtons;\n    }\n    createAlertInputs(data, inputType, selectValue) {\n        const alertInputs = data.map((option) => {\n            const value = getOptionValue(option);\n            // Remove hydrated before copying over classes\n            const copyClasses = Array.from(option.classList)\n                .filter((cls) => cls !== 'hydrated')\n                .join(' ');\n            const optClass = `${OPTION_CLASS} ${copyClasses}`;\n            return {\n                type: inputType,\n                cssClass: optClass,\n                label: option.textContent || '',\n                value,\n                checked: isOptionSelected(selectValue, value, this.compareWith),\n                disabled: option.disabled,\n            };\n        });\n        return alertInputs;\n    }\n    createOverlaySelectOptions(data, selectValue) {\n        const popoverOptions = data.map((option) => {\n            const value = getOptionValue(option);\n            // Remove hydrated before copying over classes\n            const copyClasses = Array.from(option.classList)\n                .filter((cls) => cls !== 'hydrated')\n                .join(' ');\n            const optClass = `${OPTION_CLASS} ${copyClasses}`;\n            return {\n                text: option.textContent || '',\n                cssClass: optClass,\n                value,\n                checked: isOptionSelected(selectValue, value, this.compareWith),\n                disabled: option.disabled,\n                handler: (selected) => {\n                    this.setValue(selected);\n                    if (!this.multiple) {\n                        this.close();\n                    }\n                },\n            };\n        });\n        return popoverOptions;\n    }\n    async openPopover(ev) {\n        const { fill, labelPlacement } = this;\n        const interfaceOptions = this.interfaceOptions;\n        const mode = getIonMode(this);\n        const showBackdrop = mode === 'md' ? false : true;\n        const multiple = this.multiple;\n        const value = this.value;\n        let event = ev;\n        let size = 'auto';\n        const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n        /**\n         * The popover should take up the full width\n         * when using a fill in MD mode or if the\n         * label is floating/stacked.\n         */\n        if (hasFloatingOrStackedLabel || (mode === 'md' && fill !== undefined)) {\n            size = 'cover';\n            /**\n             * Otherwise the popover\n             * should be positioned relative\n             * to the native element.\n             */\n        }\n        else {\n            event = Object.assign(Object.assign({}, ev), { detail: {\n                    ionShadowTarget: this.nativeWrapperEl,\n                } });\n        }\n        const popoverOpts = Object.assign(Object.assign({ mode,\n            event, alignment: 'center', size,\n            showBackdrop }, interfaceOptions), { component: 'ion-select-popover', cssClass: ['select-popover', interfaceOptions.cssClass], componentProps: {\n                header: interfaceOptions.header,\n                subHeader: interfaceOptions.subHeader,\n                message: interfaceOptions.message,\n                multiple,\n                value,\n                options: this.createOverlaySelectOptions(this.childOpts, value),\n            } });\n        return popoverController.create(popoverOpts);\n    }\n    async openActionSheet() {\n        const mode = getIonMode(this);\n        const interfaceOptions = this.interfaceOptions;\n        const actionSheetOpts = Object.assign(Object.assign({ mode }, interfaceOptions), { buttons: this.createActionSheetButtons(this.childOpts, this.value), cssClass: ['select-action-sheet', interfaceOptions.cssClass] });\n        return actionSheetController.create(actionSheetOpts);\n    }\n    async openAlert() {\n        const interfaceOptions = this.interfaceOptions;\n        const inputType = this.multiple ? 'checkbox' : 'radio';\n        const mode = getIonMode(this);\n        const alertOpts = Object.assign(Object.assign({ mode }, interfaceOptions), { header: interfaceOptions.header ? interfaceOptions.header : this.labelText, inputs: this.createAlertInputs(this.childOpts, inputType, this.value), buttons: [\n                {\n                    text: this.cancelText,\n                    role: 'cancel',\n                    handler: () => {\n                        this.ionCancel.emit();\n                    },\n                },\n                {\n                    text: this.okText,\n                    handler: (selectedValues) => {\n                        this.setValue(selectedValues);\n                    },\n                },\n            ], cssClass: [\n                'select-alert',\n                interfaceOptions.cssClass,\n                this.multiple ? 'multiple-select-alert' : 'single-select-alert',\n            ] });\n        return alertController.create(alertOpts);\n    }\n    openModal() {\n        const { multiple, value, interfaceOptions } = this;\n        const mode = getIonMode(this);\n        const modalOpts = Object.assign(Object.assign({}, interfaceOptions), { mode, cssClass: ['select-modal', interfaceOptions.cssClass], component: 'ion-select-modal', componentProps: {\n                header: interfaceOptions.header,\n                multiple,\n                value,\n                options: this.createOverlaySelectOptions(this.childOpts, value),\n            } });\n        return modalController.create(modalOpts);\n    }\n    /**\n     * Close the select interface.\n     */\n    close() {\n        if (!this.overlay) {\n            return Promise.resolve(false);\n        }\n        return this.overlay.dismiss();\n    }\n    hasValue() {\n        return this.getText() !== '';\n    }\n    get childOpts() {\n        return Array.from(this.el.querySelectorAll('ion-select-option'));\n    }\n    /**\n     * Returns any plaintext associated with\n     * the label (either prop or slot).\n     * Note: This will not return any custom\n     * HTML. Use the `hasLabel` getter if you\n     * want to know if any slotted label content\n     * was passed.\n     */\n    get labelText() {\n        const { label } = this;\n        if (label !== undefined) {\n            return label;\n        }\n        const { labelSlot } = this;\n        if (labelSlot !== null) {\n            return labelSlot.textContent;\n        }\n        return;\n    }\n    getText() {\n        const selectedText = this.selectedText;\n        if (selectedText != null && selectedText !== '') {\n            return selectedText;\n        }\n        return generateText(this.childOpts, this.value, this.compareWith);\n    }\n    setFocus() {\n        if (this.focusEl) {\n            this.focusEl.focus();\n        }\n    }\n    emitStyle() {\n        const { disabled } = this;\n        const style = {\n            'interactive-disabled': disabled,\n        };\n        this.ionStyle.emit(style);\n    }\n    renderLabel() {\n        const { label } = this;\n        return (h(\"div\", { class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !this.hasLabel,\n            }, part: \"label\" }, label === undefined ? h(\"slot\", { name: \"label\" }) : h(\"div\", { class: \"label-text\" }, label)));\n    }\n    componentDidRender() {\n        var _a;\n        (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n    }\n    /**\n     * Gets any content passed into the `label` slot,\n     * not the <slot> definition.\n     */\n    get labelSlot() {\n        return this.el.querySelector('[slot=\"label\"]');\n    }\n    /**\n     * Returns `true` if label content is provided\n     * either by a prop or a content. If you want\n     * to get the plaintext value of the label use\n     * the `labelText` getter instead.\n     */\n    get hasLabel() {\n        return this.label !== undefined || this.labelSlot !== null;\n    }\n    /**\n     * Renders the border container\n     * when fill=\"outline\".\n     */\n    renderLabelContainer() {\n        const mode = getIonMode(this);\n        const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n        if (hasOutlineFill) {\n            /**\n             * The outline fill has a special outline\n             * that appears around the select and the label.\n             * Certain stacked and floating label placements cause the\n             * label to translate up and create a \"cut out\"\n             * inside of that border by using the notch-spacer element.\n             */\n            return [\n                h(\"div\", { class: \"select-outline-container\" }, h(\"div\", { class: \"select-outline-start\" }), h(\"div\", { class: {\n                        'select-outline-notch': true,\n                        'select-outline-notch-hidden': !this.hasLabel,\n                    } }, h(\"div\", { class: \"notch-spacer\", \"aria-hidden\": \"true\", ref: (el) => (this.notchSpacerEl = el) }, this.label)), h(\"div\", { class: \"select-outline-end\" })),\n                this.renderLabel(),\n            ];\n        }\n        /**\n         * If not using the outline style,\n         * we can render just the label.\n         */\n        return this.renderLabel();\n    }\n    /**\n     * Renders either the placeholder\n     * or the selected values based on\n     * the state of the select.\n     */\n    renderSelectText() {\n        const { placeholder } = this;\n        const displayValue = this.getText();\n        let addPlaceholderClass = false;\n        let selectText = displayValue;\n        if (selectText === '' && placeholder !== undefined) {\n            selectText = placeholder;\n            addPlaceholderClass = true;\n        }\n        const selectTextClasses = {\n            'select-text': true,\n            'select-placeholder': addPlaceholderClass,\n        };\n        const textPart = addPlaceholderClass ? 'placeholder' : 'text';\n        return (h(\"div\", { \"aria-hidden\": \"true\", class: selectTextClasses, part: textPart }, selectText));\n    }\n    /**\n     * Renders the chevron icon\n     * next to the select text.\n     */\n    renderSelectIcon() {\n        const mode = getIonMode(this);\n        const { isExpanded, toggleIcon, expandedIcon } = this;\n        let icon;\n        if (isExpanded && expandedIcon !== undefined) {\n            icon = expandedIcon;\n        }\n        else {\n            const defaultIcon = mode === 'ios' ? chevronExpand : caretDownSharp;\n            icon = toggleIcon !== null && toggleIcon !== void 0 ? toggleIcon : defaultIcon;\n        }\n        return h(\"ion-icon\", { class: \"select-icon\", part: \"icon\", \"aria-hidden\": \"true\", icon: icon });\n    }\n    get ariaLabel() {\n        var _a;\n        const { placeholder, inheritedAttributes } = this;\n        const displayValue = this.getText();\n        // The aria label should be preferred over visible text if both are specified\n        const definedLabel = (_a = inheritedAttributes['aria-label']) !== null && _a !== void 0 ? _a : this.labelText;\n        /**\n         * If developer has specified a placeholder\n         * and there is nothing selected, the selectText\n         * should have the placeholder value.\n         */\n        let renderedLabel = displayValue;\n        if (renderedLabel === '' && placeholder !== undefined) {\n            renderedLabel = placeholder;\n        }\n        /**\n         * If there is a developer-defined label,\n         * then we need to concatenate the developer label\n         * string with the current current value.\n         * The label for the control should be read\n         * before the values of the control.\n         */\n        if (definedLabel !== undefined) {\n            renderedLabel = renderedLabel === '' ? definedLabel : `${definedLabel}, ${renderedLabel}`;\n        }\n        return renderedLabel;\n    }\n    renderListbox() {\n        const { disabled, inputId, isExpanded, required } = this;\n        return (h(\"button\", { disabled: disabled, id: inputId, \"aria-label\": this.ariaLabel, \"aria-haspopup\": \"dialog\", \"aria-expanded\": `${isExpanded}`, \"aria-describedby\": this.getHintTextID(), \"aria-invalid\": this.getHintTextID() === this.errorTextId, \"aria-required\": `${required}`, onFocus: this.onFocus, onBlur: this.onBlur, ref: (focusEl) => (this.focusEl = focusEl) }));\n    }\n    getHintTextID() {\n        const { el, helperText, errorText, helperTextId, errorTextId } = this;\n        if (el.classList.contains('ion-touched') && el.classList.contains('ion-invalid') && errorText) {\n            return errorTextId;\n        }\n        if (helperText) {\n            return helperTextId;\n        }\n        return undefined;\n    }\n    /**\n     * Renders the helper text or error text values\n     */\n    renderHintText() {\n        const { helperText, errorText, helperTextId, errorTextId } = this;\n        return [\n            h(\"div\", { id: helperTextId, class: \"helper-text\", part: \"supporting-text helper-text\" }, helperText),\n            h(\"div\", { id: errorTextId, class: \"error-text\", part: \"supporting-text error-text\" }, errorText),\n        ];\n    }\n    /**\n     * Responsible for rendering helper text, and error text. This element\n     * should only be rendered if hint text is set.\n     */\n    renderBottomContent() {\n        const { helperText, errorText } = this;\n        /**\n         * undefined and empty string values should\n         * be treated as not having helper/error text.\n         */\n        const hasHintText = !!helperText || !!errorText;\n        if (!hasHintText) {\n            return;\n        }\n        return h(\"div\", { class: \"select-bottom\" }, this.renderHintText());\n    }\n    render() {\n        const { disabled, el, isExpanded, expandedIcon, labelPlacement, justify, placeholder, fill, shape, name, value, hasFocus, } = this;\n        const mode = getIonMode(this);\n        const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n        const justifyEnabled = !hasFloatingOrStackedLabel && justify !== undefined;\n        const rtl = isRTL(el) ? 'rtl' : 'ltr';\n        const inItem = hostContext('ion-item', this.el);\n        const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n        const hasValue = this.hasValue();\n        const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n        renderHiddenInput(true, el, name, parseValue(value), disabled);\n        /**\n         * If the label is stacked, it should always sit above the select.\n         * For floating labels, the label should move above the select if\n         * the select has a value, is open, or has anything in either\n         * the start or end slot.\n         *\n         * If there is content in the start slot, the label would overlap\n         * it if not forced to float. This is also applied to the end slot\n         * because with the default or solid fills, the select is not\n         * vertically centered in the container, but the label is. This\n         * causes the slots and label to appear vertically offset from each\n         * other when the label isn't floating above the input. This doesn't\n         * apply to the outline fill, but this was not accounted for to keep\n         * things consistent.\n         *\n         * TODO(FW-5592): Remove hasStartEndSlots condition\n         */\n        const labelShouldFloat = labelPlacement === 'stacked' || (labelPlacement === 'floating' && (hasValue || isExpanded || hasStartEndSlots));\n        return (h(Host, { key: 'c03fb65e8fc9f9aab295e07b282377d57d910519', onClick: this.onClick, class: createColorClasses(this.color, {\n                [mode]: true,\n                'in-item': inItem,\n                'in-item-color': hostContext('ion-item.ion-color', el),\n                'select-disabled': disabled,\n                'select-expanded': isExpanded,\n                'has-expanded-icon': expandedIcon !== undefined,\n                'has-value': hasValue,\n                'label-floating': labelShouldFloat,\n                'has-placeholder': placeholder !== undefined,\n                'has-focus': hasFocus,\n                // TODO(FW-6451): Remove `ion-focusable` class in favor of `has-focus`.\n                'ion-focusable': true,\n                [`select-${rtl}`]: true,\n                [`select-fill-${fill}`]: fill !== undefined,\n                [`select-justify-${justify}`]: justifyEnabled,\n                [`select-shape-${shape}`]: shape !== undefined,\n                [`select-label-placement-${labelPlacement}`]: true,\n            }) }, h(\"label\", { key: '0d0c8ec55269adcac625f2899a547f4e7f3e3741', class: \"select-wrapper\", id: \"select-label\", onClick: this.onLabelClick }, this.renderLabelContainer(), h(\"div\", { key: 'f6dfc93c0e23cbe75a2947abde67d842db2dad78', class: \"select-wrapper-inner\" }, h(\"slot\", { key: '957bfadf9f101f519091419a362d3abdc2be66f6', name: \"start\" }), h(\"div\", { key: 'ca349202a484e7f2e884533fd330f0b136754f7d', class: \"native-wrapper\", ref: (el) => (this.nativeWrapperEl = el), part: \"container\" }, this.renderSelectText(), this.renderListbox()), h(\"slot\", { key: 'f0e62a6533ff1c8f62bd2d27f60b23385c4fa9ed', name: \"end\" }), !hasFloatingOrStackedLabel && this.renderSelectIcon()), hasFloatingOrStackedLabel && this.renderSelectIcon(), shouldRenderHighlight && h(\"div\", { key: 'fb840d46bafafb09898ebeebbe8c181906a3d8a2', class: \"select-highlight\" })), this.renderBottomContent()));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"disabled\": [\"styleChanged\"],\n        \"isExpanded\": [\"styleChanged\"],\n        \"placeholder\": [\"styleChanged\"],\n        \"value\": [\"styleChanged\"]\n    }; }\n};\nconst getOptionValue = (el) => {\n    const value = el.value;\n    return value === undefined ? el.textContent || '' : value;\n};\nconst parseValue = (value) => {\n    if (value == null) {\n        return undefined;\n    }\n    if (Array.isArray(value)) {\n        return value.join(',');\n    }\n    return value.toString();\n};\nconst generateText = (opts, value, compareWith) => {\n    if (value === undefined) {\n        return '';\n    }\n    if (Array.isArray(value)) {\n        return value\n            .map((v) => textForValue(opts, v, compareWith))\n            .filter((opt) => opt !== null)\n            .join(', ');\n    }\n    else {\n        return textForValue(opts, value, compareWith) || '';\n    }\n};\nconst textForValue = (opts, value, compareWith) => {\n    const selectOpt = opts.find((opt) => {\n        return compareOptions(value, getOptionValue(opt), compareWith);\n    });\n    return selectOpt ? selectOpt.textContent : null;\n};\nlet selectIds = 0;\nconst OPTION_CLASS = 'select-interface-option';\nSelect.style = {\n    ios: selectIosCss,\n    md: selectMdCss\n};\n\nconst selectOptionCss = \":host{display:none}\";\n\nconst SelectOption = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.inputId = `ion-selopt-${selectOptionIds++}`;\n        /**\n         * If `true`, the user cannot interact with the select option. This property does not apply when `interface=\"action-sheet\"` as `ion-action-sheet` does not allow for disabled buttons.\n         */\n        this.disabled = false;\n    }\n    render() {\n        return h(Host, { key: '3a70eea9fa03a9acba582180761d18347c72acee', role: \"option\", id: this.inputId, class: getIonMode(this) });\n    }\n    get el() { return getElement(this); }\n};\nlet selectOptionIds = 0;\nSelectOption.style = selectOptionCss;\n\nconst selectPopoverIosCss = \".sc-ion-select-popover-ios-h ion-list.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-ios,ion-label.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-ios-h{overflow-y:auto}\";\n\nconst selectPopoverMdCss = \".sc-ion-select-popover-md-h ion-list.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-md,ion-label.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-md-h{overflow-y:auto}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(container){display:none}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(label){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-item.sc-ion-select-popover-md{--inner-border-width:0}.item-radio-checked.sc-ion-select-popover-md{--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08);--background-focused:var(--ion-color-primary, #0054e9);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #0054e9);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-popover-md{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #0054e9)}\";\n\nconst SelectPopover = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        /**\n         * An array of options for the popover\n         */\n        this.options = [];\n    }\n    findOptionFromEvent(ev) {\n        const { options } = this;\n        return options.find((o) => o.value === ev.target.value);\n    }\n    /**\n     * When an option is selected we need to get the value(s)\n     * of the selected option(s) and return it in the option\n     * handler\n     */\n    callOptionHandler(ev) {\n        const option = this.findOptionFromEvent(ev);\n        const values = this.getValues(ev);\n        if (option === null || option === void 0 ? void 0 : option.handler) {\n            safeCall(option.handler, values);\n        }\n    }\n    /**\n     * Dismisses the host popover that the `ion-select-popover`\n     * is rendered within.\n     */\n    dismissParentPopover() {\n        const popover = this.el.closest('ion-popover');\n        if (popover) {\n            popover.dismiss();\n        }\n    }\n    setChecked(ev) {\n        const { multiple } = this;\n        const option = this.findOptionFromEvent(ev);\n        // this is a popover with checkboxes (multiple value select)\n        // we need to set the checked value for this option\n        if (multiple && option) {\n            option.checked = ev.detail.checked;\n        }\n    }\n    getValues(ev) {\n        const { multiple, options } = this;\n        if (multiple) {\n            // this is a popover with checkboxes (multiple value select)\n            // return an array of all the checked values\n            return options.filter((o) => o.checked).map((o) => o.value);\n        }\n        // this is a popover with radio buttons (single value select)\n        // return the value that was clicked, otherwise undefined\n        const option = this.findOptionFromEvent(ev);\n        return option ? option.value : undefined;\n    }\n    renderOptions(options) {\n        const { multiple } = this;\n        switch (multiple) {\n            case true:\n                return this.renderCheckboxOptions(options);\n            default:\n                return this.renderRadioOptions(options);\n        }\n    }\n    renderCheckboxOptions(options) {\n        return options.map((option) => (h(\"ion-item\", { class: Object.assign({\n                // TODO FW-4784\n                'item-checkbox-checked': option.checked\n            }, getClassMap(option.cssClass)) }, h(\"ion-checkbox\", { value: option.value, disabled: option.disabled, checked: option.checked, justify: \"start\", labelPlacement: \"end\", onIonChange: (ev) => {\n                this.setChecked(ev);\n                this.callOptionHandler(ev);\n                // TODO FW-4784\n                forceUpdate(this);\n            } }, option.text))));\n    }\n    renderRadioOptions(options) {\n        const checked = options.filter((o) => o.checked).map((o) => o.value)[0];\n        return (h(\"ion-radio-group\", { value: checked, onIonChange: (ev) => this.callOptionHandler(ev) }, options.map((option) => (h(\"ion-item\", { class: Object.assign({\n                // TODO FW-4784\n                'item-radio-checked': option.value === checked\n            }, getClassMap(option.cssClass)) }, h(\"ion-radio\", { value: option.value, disabled: option.disabled, onClick: () => this.dismissParentPopover(), onKeyUp: (ev) => {\n                if (ev.key === ' ') {\n                    /**\n                     * Selecting a radio option with keyboard navigation,\n                     * either through the Enter or Space keys, should\n                     * dismiss the popover.\n                     */\n                    this.dismissParentPopover();\n                }\n            } }, option.text))))));\n    }\n    render() {\n        const { header, message, options, subHeader } = this;\n        const hasSubHeaderOrMessage = subHeader !== undefined || message !== undefined;\n        return (h(Host, { key: 'ab931b49b59283825bd2afa3f7f995b0e6e05bef', class: getIonMode(this) }, h(\"ion-list\", { key: '3bd12b67832607596b912a73d5b3ae9b954b244d' }, header !== undefined && h(\"ion-list-header\", { key: '97da930246edf7423a039c030d40e3ff7a5148a3' }, header), hasSubHeaderOrMessage && (h(\"ion-item\", { key: 'c579df6ea8fac07bb0c59d34c69b149656863224' }, h(\"ion-label\", { key: 'af699c5f465710ccb13b8cf8e7be66f0e8acfad1', class: \"ion-text-wrap\" }, subHeader !== undefined && h(\"h3\", { key: 'df9a936d42064b134e843c7229f314a2a3ec7e80' }, subHeader), message !== undefined && h(\"p\", { key: '9c3ddad378df00f106afa94e9928cf68c17124dd' }, message)))), this.renderOptions(options))));\n    }\n    get el() { return getElement(this); }\n};\nSelectPopover.style = {\n    ios: selectPopoverIosCss,\n    md: selectPopoverMdCss\n};\n\nexport { Select as ion_select, SelectOption as ion_select_option, SelectPopover as ion_select_popover };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,WAAW,QAAQ,qBAAqB;AACrK,SAASC,CAAC,IAAIC,qBAAqB,QAAQ,gCAAgC;AAC3E,SAASC,CAAC,IAAIC,gBAAgB,EAAEH,CAAC,IAAII,cAAc,QAAQ,kCAAkC;AAC7F,SAASC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,iBAAiB,EAAEV,CAAC,IAAIW,mBAAmB,QAAQ,uBAAuB;AAChH,SAAST,CAAC,IAAIU,iBAAiB,EAAEL,CAAC,IAAIM,qBAAqB,EAAEJ,CAAC,IAAIK,eAAe,EAAEvB,CAAC,IAAIwB,eAAe,EAAEC,CAAC,IAAIC,QAAQ,QAAQ,wBAAwB;AACtJ,SAASb,CAAC,IAAIc,KAAK,QAAQ,mBAAmB;AAC9C,SAASvB,CAAC,IAAIwB,WAAW,EAAEjB,CAAC,IAAIkB,kBAAkB,EAAEC,CAAC,IAAIC,WAAW,QAAQ,qBAAqB;AACjG,SAASC,CAAC,IAAIC,eAAe,QAAQ,6BAA6B;AAClE,SAASD,CAAC,IAAIE,aAAa,EAAEC,CAAC,IAAIC,cAAc,QAAQ,qBAAqB;AAC7E,OAAO,qBAAqB;AAC5B,OAAO,oCAAoC;AAC3C,OAAO,kCAAkC;AACzC,OAAO,kCAAkC;AAEzC,MAAMC,YAAY,GAAG,+1RAA+1R;AAEp3R,MAAMC,WAAW,GAAG,wmiBAAwmiB;AAE5niB,MAAMC,MAAM,GAAG,MAAM;EACjBC,WAAWA,CAACC,OAAO,EAAE;IACjB5C,gBAAgB,CAAC,IAAI,EAAE4C,OAAO,CAAC;IAC/B,IAAI,CAACC,SAAS,GAAG3C,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC4C,SAAS,GAAG5C,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC6C,UAAU,GAAG7C,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IACpD,IAAI,CAAC8C,QAAQ,GAAG9C,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC+C,OAAO,GAAG/C,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACgD,QAAQ,GAAGhD,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACiD,OAAO,GAAG,WAAWC,SAAS,EAAE,EAAE;IACvC,IAAI,CAACC,YAAY,GAAG,GAAG,IAAI,CAACF,OAAO,cAAc;IACjD,IAAI,CAACG,WAAW,GAAG,GAAG,IAAI,CAACH,OAAO,aAAa;IAC/C,IAAI,CAACI,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,QAAQ;IAC1B;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,OAAO;IACxB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,CAAC,CAAC;IAC1B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,OAAO;IAC7B;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,IAAI,CAACb,OAAO;IACxB;AACR;AACA;IACQ,IAAI,CAACc,MAAM,GAAG,IAAI;IAClB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,OAAO,GAAIC,EAAE,IAAK;MACnB,MAAMC,MAAM,GAAGD,EAAE,CAACC,MAAM;MACxB,MAAMC,WAAW,GAAGD,MAAM,CAACE,OAAO,CAAC,8BAA8B,CAAC;MAClE,IAAIF,MAAM,KAAK,IAAI,CAACG,EAAE,IAAIF,WAAW,KAAK,IAAI,EAAE;QAC5C,IAAI,CAACG,QAAQ,CAAC,CAAC;QACf,IAAI,CAACC,IAAI,CAACN,EAAE,CAAC;MACjB,CAAC,MACI;QACD;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACgBA,EAAE,CAACO,cAAc,CAAC,CAAC;MACvB;IACJ,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAACnB,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACT,QAAQ,CAAC6B,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAACrB,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACR,OAAO,CAAC4B,IAAI,CAAC,CAAC;IACvB,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACE,YAAY,GAAIX,EAAE,IAAK;MACxB;MACA;MACA,IAAIA,EAAE,CAACC,MAAM,KAAKD,EAAE,CAACY,aAAa,EAAE;QAChCZ,EAAE,CAACa,eAAe,CAAC,CAAC;MACxB;IACJ,CAAC;EACL;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACAC,QAAQA,CAACC,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACxC,SAAS,CAACgC,IAAI,CAAC;MAAEQ;IAAM,CAAC,CAAC;EAClC;EACMC,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB,MAAM;QAAEhB;MAAG,CAAC,GAAGe,KAAI;MACnBA,KAAI,CAACE,eAAe,GAAG1E,qBAAqB,CAACyD,EAAE,EAAE,MAAMe,KAAI,CAACG,aAAa,EAAE,MAAMH,KAAI,CAACI,SAAS,CAAC;MAChGJ,KAAI,CAACK,oBAAoB,CAAC,CAAC;MAC3BL,KAAI,CAACJ,SAAS,CAAC,CAAC;MAChBI,KAAI,CAACM,SAAS,GAAGzD,eAAe,CAACmD,KAAI,CAACf,EAAE,EAAE,mBAAmB,eAAAgB,iBAAA,CAAE,aAAY;QACvED,KAAI,CAACK,oBAAoB,CAAC,CAAC;QAC3B;AACZ;AACA;AACA;AACA;AACA;QACY/E,WAAW,CAAC0E,KAAI,CAAC;MACrB,CAAC,EAAC;IAAC;EACP;EACAO,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACvC,mBAAmB,GAAGnC,iBAAiB,CAAC,IAAI,CAACoD,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC;EACzE;EACAuB,gBAAgBA,CAAA,EAAG;IACf;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACZ,SAAS,CAAC,CAAC;EACpB;EACAa,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACH,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACI,UAAU,CAAC,CAAC;MAC3B,IAAI,CAACJ,SAAS,GAAGK,SAAS;IAC9B;IACA,IAAI,IAAI,CAACT,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACU,OAAO,CAAC,CAAC;MAC9B,IAAI,CAACV,eAAe,GAAGS,SAAS;IACpC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACUxB,IAAIA,CAAC0B,KAAK,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAb,iBAAA;MACd,IAAIa,MAAI,CAAC1C,QAAQ,IAAI0C,MAAI,CAAC7C,UAAU,EAAE;QAClC,OAAO0C,SAAS;MACpB;MACAG,MAAI,CAAC7C,UAAU,GAAG,IAAI;MACtB,MAAM8C,OAAO,GAAID,MAAI,CAACC,OAAO,SAASD,MAAI,CAACE,aAAa,CAACH,KAAK,CAAE;MAChE;MACA,MAAMI,sBAAsB,GAAGA,CAAA,KAAM;QACjC,MAAMC,eAAe,GAAGJ,MAAI,CAACK,SAAS,CAACC,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAACvB,KAAK,KAAKgB,MAAI,CAAChB,KAAK,CAAC;QAC/E,IAAIoB,eAAe,GAAG,CAAC,CAAC,EAAE;UACtB,MAAMI,YAAY,GAAGP,OAAO,CAACQ,aAAa,CAAC,wCAAwCL,eAAe,GAAG,CAAC,GAAG,CAAC;UAC1G,IAAII,YAAY,EAAE;YACd;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;YACoB,MAAME,aAAa,GAAGF,YAAY,CAACC,aAAa,CAAC,yBAAyB,CAAC;YAC3E,IAAIC,aAAa,EAAE;cACfF,YAAY,CAACG,cAAc,CAAC;gBAAEC,KAAK,EAAE;cAAU,CAAC,CAAC;cACjD;cACA;cACAF,aAAa,CAACtC,QAAQ,CAAC,CAAC;YAC5B;YACAlD,mBAAmB,CAACsF,YAAY,CAAC;UACrC;QACJ,CAAC,MACI;UACD;AAChB;AACA;UACgB,MAAMK,kBAAkB,GAAGZ,OAAO,CAACQ,aAAa,CAAC,sEAAsE,CAAC;UACxH,IAAII,kBAAkB,EAAE;YACpB;AACpB;AACA;AACA;AACA;AACA;YACoBA,kBAAkB,CAACzC,QAAQ,CAAC,CAAC;YAC7BlD,mBAAmB,CAAC2F,kBAAkB,CAAC3C,OAAO,CAAC,UAAU,CAAC,CAAC;UAC/D;QACJ;MACJ,CAAC;MACD;MACA,IAAI8B,MAAI,CAACzC,SAAS,KAAK,OAAO,EAAE;QAC5B0C,OAAO,CAACa,gBAAgB,CAAC,qBAAqB,EAAEX,sBAAsB,EAAE;UAAEY,IAAI,EAAE;QAAK,CAAC,CAAC;MAC3F,CAAC,MACI,IAAIf,MAAI,CAACzC,SAAS,KAAK,SAAS,EAAE;QACnC0C,OAAO,CAACa,gBAAgB,CAAC,uBAAuB,EAAEX,sBAAsB,EAAE;UAAEY,IAAI,EAAE;QAAK,CAAC,CAAC;MAC7F,CAAC,MACI;QACD;AACZ;AACA;AACA;AACA;AACA;QACY,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;UAC5BC,qBAAqB,CAAC,MAAM;YACxBd,sBAAsB,CAAC,CAAC;UAC5B,CAAC,CAAC;QACN,CAAC;QACD,IAAIH,MAAI,CAACzC,SAAS,KAAK,OAAO,EAAE;UAC5B0C,OAAO,CAACa,gBAAgB,CAAC,qBAAqB,EAAEE,iBAAiB,EAAE;YAAED,IAAI,EAAE;UAAK,CAAC,CAAC;QACtF,CAAC,MACI,IAAIf,MAAI,CAACzC,SAAS,KAAK,cAAc,EAAE;UACxC0C,OAAO,CAACa,gBAAgB,CAAC,2BAA2B,EAAEE,iBAAiB,EAAE;YAAED,IAAI,EAAE;UAAK,CAAC,CAAC;QAC5F;MACJ;MACAd,OAAO,CAACiB,YAAY,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QAC9BnB,MAAI,CAACC,OAAO,GAAGJ,SAAS;QACxBG,MAAI,CAAC7C,UAAU,GAAG,KAAK;QACvB6C,MAAI,CAACtD,UAAU,CAAC8B,IAAI,CAAC,CAAC;QACtBwB,MAAI,CAAC5B,QAAQ,CAAC,CAAC;MACnB,CAAC,CAAC;MACF,MAAM6B,OAAO,CAACmB,OAAO,CAAC,CAAC;MACvB,OAAOnB,OAAO;IAAC;EACnB;EACAC,aAAaA,CAACnC,EAAE,EAAE;IACd,IAAIsD,eAAe,GAAG,IAAI,CAAC9D,SAAS;IACpC,IAAI8D,eAAe,KAAK,cAAc,IAAI,IAAI,CAAC3D,QAAQ,EAAE;MACrD3D,eAAe,CAAC,uCAAuCsH,eAAe,mEAAmE,CAAC;MAC1IA,eAAe,GAAG,OAAO;IAC7B;IACA,IAAIA,eAAe,KAAK,SAAS,IAAI,CAACtD,EAAE,EAAE;MACtChE,eAAe,CAAC,yCAAyCsH,eAAe,kEAAkE,CAAC;MAC3IA,eAAe,GAAG,OAAO;IAC7B;IACA,IAAIA,eAAe,KAAK,cAAc,EAAE;MACpC,OAAO,IAAI,CAACC,eAAe,CAAC,CAAC;IACjC;IACA,IAAID,eAAe,KAAK,SAAS,EAAE;MAC/B,OAAO,IAAI,CAACE,WAAW,CAACxD,EAAE,CAAC;IAC/B;IACA,IAAIsD,eAAe,KAAK,OAAO,EAAE;MAC7B,OAAO,IAAI,CAACG,SAAS,CAAC,CAAC;IAC3B;IACA,OAAO,IAAI,CAACC,SAAS,CAAC,CAAC;EAC3B;EACAlC,oBAAoBA,CAAA,EAAG;IACnB,MAAMU,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAI,CAACA,OAAO,EAAE;MACV;IACJ;IACA,MAAMI,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMrB,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,QAAQ,IAAI,CAACzB,SAAS;MAClB,KAAK,cAAc;QACf0C,OAAO,CAACyB,OAAO,GAAG,IAAI,CAACC,wBAAwB,CAACtB,SAAS,EAAErB,KAAK,CAAC;QACjE;MACJ,KAAK,SAAS;QACV,MAAM4C,OAAO,GAAG3B,OAAO,CAACQ,aAAa,CAAC,oBAAoB,CAAC;QAC3D,IAAImB,OAAO,EAAE;UACTA,OAAO,CAACC,OAAO,GAAG,IAAI,CAACC,0BAA0B,CAACzB,SAAS,EAAErB,KAAK,CAAC;QACvE;QACA;MACJ,KAAK,OAAO;QACR,MAAM+C,KAAK,GAAG9B,OAAO,CAACQ,aAAa,CAAC,kBAAkB,CAAC;QACvD,IAAIsB,KAAK,EAAE;UACPA,KAAK,CAACF,OAAO,GAAG,IAAI,CAACC,0BAA0B,CAACzB,SAAS,EAAErB,KAAK,CAAC;QACrE;QACA;MACJ,KAAK,OAAO;QACR,MAAMgD,SAAS,GAAG,IAAI,CAACtE,QAAQ,GAAG,UAAU,GAAG,OAAO;QACtDuC,OAAO,CAACgC,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAAC7B,SAAS,EAAE2B,SAAS,EAAEhD,KAAK,CAAC;QACpE;IACR;EACJ;EACA2C,wBAAwBA,CAACQ,IAAI,EAAEC,WAAW,EAAE;IACxC,MAAMC,kBAAkB,GAAGF,IAAI,CAACG,GAAG,CAAEC,MAAM,IAAK;MAC5C,MAAMvD,KAAK,GAAGwD,cAAc,CAACD,MAAM,CAAC;MACpC;MACA,MAAME,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,CAC3CC,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAK,UAAU,CAAC,CACnCC,IAAI,CAAC,GAAG,CAAC;MACd,MAAMC,QAAQ,GAAG,GAAGC,YAAY,IAAIR,WAAW,EAAE;MACjD,OAAO;QACHS,IAAI,EAAEtI,gBAAgB,CAACwH,WAAW,EAAEpD,KAAK,EAAE,IAAI,CAACmE,WAAW,CAAC,GAAG,UAAU,GAAG,EAAE;QAC9EC,IAAI,EAAEb,MAAM,CAACc,WAAW;QACxBC,QAAQ,EAAEN,QAAQ;QAClBO,OAAO,EAAEA,CAAA,KAAM;UACX,IAAI,CAACxE,QAAQ,CAACC,KAAK,CAAC;QACxB;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAqD,kBAAkB,CAACmB,IAAI,CAAC;MACpBJ,IAAI,EAAE,IAAI,CAAC/F,UAAU;MACrB6F,IAAI,EAAE,QAAQ;MACdK,OAAO,EAAEA,CAAA,KAAM;QACX,IAAI,CAAC9G,SAAS,CAAC+B,IAAI,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;IACF,OAAO6D,kBAAkB;EAC7B;EACAH,iBAAiBA,CAACC,IAAI,EAAEH,SAAS,EAAEI,WAAW,EAAE;IAC5C,MAAMqB,WAAW,GAAGtB,IAAI,CAACG,GAAG,CAAEC,MAAM,IAAK;MACrC,MAAMvD,KAAK,GAAGwD,cAAc,CAACD,MAAM,CAAC;MACpC;MACA,MAAME,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,CAC3CC,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAK,UAAU,CAAC,CACnCC,IAAI,CAAC,GAAG,CAAC;MACd,MAAMC,QAAQ,GAAG,GAAGC,YAAY,IAAIR,WAAW,EAAE;MACjD,OAAO;QACHiB,IAAI,EAAE1B,SAAS;QACfsB,QAAQ,EAAEN,QAAQ;QAClBW,KAAK,EAAEpB,MAAM,CAACc,WAAW,IAAI,EAAE;QAC/BrE,KAAK;QACL4E,OAAO,EAAEhJ,gBAAgB,CAACwH,WAAW,EAAEpD,KAAK,EAAE,IAAI,CAACmE,WAAW,CAAC;QAC/D7F,QAAQ,EAAEiF,MAAM,CAACjF;MACrB,CAAC;IACL,CAAC,CAAC;IACF,OAAOmG,WAAW;EACtB;EACA3B,0BAA0BA,CAACK,IAAI,EAAEC,WAAW,EAAE;IAC1C,MAAMyB,cAAc,GAAG1B,IAAI,CAACG,GAAG,CAAEC,MAAM,IAAK;MACxC,MAAMvD,KAAK,GAAGwD,cAAc,CAACD,MAAM,CAAC;MACpC;MACA,MAAME,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,CAC3CC,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAK,UAAU,CAAC,CACnCC,IAAI,CAAC,GAAG,CAAC;MACd,MAAMC,QAAQ,GAAG,GAAGC,YAAY,IAAIR,WAAW,EAAE;MACjD,OAAO;QACHW,IAAI,EAAEb,MAAM,CAACc,WAAW,IAAI,EAAE;QAC9BC,QAAQ,EAAEN,QAAQ;QAClBhE,KAAK;QACL4E,OAAO,EAAEhJ,gBAAgB,CAACwH,WAAW,EAAEpD,KAAK,EAAE,IAAI,CAACmE,WAAW,CAAC;QAC/D7F,QAAQ,EAAEiF,MAAM,CAACjF,QAAQ;QACzBiG,OAAO,EAAGO,QAAQ,IAAK;UACnB,IAAI,CAAC/E,QAAQ,CAAC+E,QAAQ,CAAC;UACvB,IAAI,CAAC,IAAI,CAACpG,QAAQ,EAAE;YAChB,IAAI,CAACqG,KAAK,CAAC,CAAC;UAChB;QACJ;MACJ,CAAC;IACL,CAAC,CAAC;IACF,OAAOF,cAAc;EACzB;EACMtC,WAAWA,CAACxD,EAAE,EAAE;IAAA,IAAAiG,MAAA;IAAA,OAAA7E,iBAAA;MAClB,MAAM;QAAE8E,IAAI;QAAExG;MAAe,CAAC,GAAGuG,MAAI;MACrC,MAAMxG,gBAAgB,GAAGwG,MAAI,CAACxG,gBAAgB;MAC9C,MAAM0G,IAAI,GAAGjK,UAAU,CAAC+J,MAAI,CAAC;MAC7B,MAAMG,YAAY,GAAGD,IAAI,KAAK,IAAI,GAAG,KAAK,GAAG,IAAI;MACjD,MAAMxG,QAAQ,GAAGsG,MAAI,CAACtG,QAAQ;MAC9B,MAAMsB,KAAK,GAAGgF,MAAI,CAAChF,KAAK;MACxB,IAAIe,KAAK,GAAGhC,EAAE;MACd,IAAIqG,IAAI,GAAG,MAAM;MACjB,MAAMC,yBAAyB,GAAG5G,cAAc,KAAK,UAAU,IAAIA,cAAc,KAAK,SAAS;MAC/F;AACR;AACA;AACA;AACA;MACQ,IAAI4G,yBAAyB,IAAKH,IAAI,KAAK,IAAI,IAAID,IAAI,KAAKpE,SAAU,EAAE;QACpEuE,IAAI,GAAG,OAAO;QACd;AACZ;AACA;AACA;AACA;MACQ,CAAC,MACI;QACDrE,KAAK,GAAGuE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAExG,EAAE,CAAC,EAAE;UAAEyG,MAAM,EAAE;YAC/CC,eAAe,EAAET,MAAI,CAACU;UAC1B;QAAE,CAAC,CAAC;MACZ;MACA,MAAMC,WAAW,GAAGL,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAEL,IAAI;QAClDnE,KAAK;QAAE6E,SAAS,EAAE,QAAQ;QAAER,IAAI;QAChCD;MAAa,CAAC,EAAE3G,gBAAgB,CAAC,EAAE;QAAEqH,SAAS,EAAE,oBAAoB;QAAEvB,QAAQ,EAAE,CAAC,gBAAgB,EAAE9F,gBAAgB,CAAC8F,QAAQ,CAAC;QAAEwB,cAAc,EAAE;UAC3IC,MAAM,EAAEvH,gBAAgB,CAACuH,MAAM;UAC/BC,SAAS,EAAExH,gBAAgB,CAACwH,SAAS;UACrCC,OAAO,EAAEzH,gBAAgB,CAACyH,OAAO;UACjCvH,QAAQ;UACRsB,KAAK;UACL6C,OAAO,EAAEmC,MAAI,CAAClC,0BAA0B,CAACkC,MAAI,CAAC3D,SAAS,EAAErB,KAAK;QAClE;MAAE,CAAC,CAAC;MACR,OAAO7D,iBAAiB,CAAC+J,MAAM,CAACP,WAAW,CAAC;IAAC;EACjD;EACMrD,eAAeA,CAAA,EAAG;IAAA,IAAA6D,MAAA;IAAA,OAAAhG,iBAAA;MACpB,MAAM+E,IAAI,GAAGjK,UAAU,CAACkL,MAAI,CAAC;MAC7B,MAAM3H,gBAAgB,GAAG2H,MAAI,CAAC3H,gBAAgB;MAC9C,MAAM4H,eAAe,GAAGd,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAEL;MAAK,CAAC,EAAE1G,gBAAgB,CAAC,EAAE;QAAEkE,OAAO,EAAEyD,MAAI,CAACxD,wBAAwB,CAACwD,MAAI,CAAC9E,SAAS,EAAE8E,MAAI,CAACnG,KAAK,CAAC;QAAEsE,QAAQ,EAAE,CAAC,qBAAqB,EAAE9F,gBAAgB,CAAC8F,QAAQ;MAAE,CAAC,CAAC;MACtN,OAAOlI,qBAAqB,CAAC8J,MAAM,CAACE,eAAe,CAAC;IAAC;EACzD;EACM3D,SAASA,CAAA,EAAG;IAAA,IAAA4D,MAAA;IAAA,OAAAlG,iBAAA;MACd,MAAM3B,gBAAgB,GAAG6H,MAAI,CAAC7H,gBAAgB;MAC9C,MAAMwE,SAAS,GAAGqD,MAAI,CAAC3H,QAAQ,GAAG,UAAU,GAAG,OAAO;MACtD,MAAMwG,IAAI,GAAGjK,UAAU,CAACoL,MAAI,CAAC;MAC7B,MAAMC,SAAS,GAAGhB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAEL;MAAK,CAAC,EAAE1G,gBAAgB,CAAC,EAAE;QAAEuH,MAAM,EAAEvH,gBAAgB,CAACuH,MAAM,GAAGvH,gBAAgB,CAACuH,MAAM,GAAGM,MAAI,CAACE,SAAS;QAAEtD,MAAM,EAAEoD,MAAI,CAACnD,iBAAiB,CAACmD,MAAI,CAAChF,SAAS,EAAE2B,SAAS,EAAEqD,MAAI,CAACrG,KAAK,CAAC;QAAE0C,OAAO,EAAE,CACjO;UACI0B,IAAI,EAAEiC,MAAI,CAAChI,UAAU;UACrB6F,IAAI,EAAE,QAAQ;UACdK,OAAO,EAAEA,CAAA,KAAM;YACX8B,MAAI,CAAC5I,SAAS,CAAC+B,IAAI,CAAC,CAAC;UACzB;QACJ,CAAC,EACD;UACI4E,IAAI,EAAEiC,MAAI,CAACzH,MAAM;UACjB2F,OAAO,EAAGiC,cAAc,IAAK;YACzBH,MAAI,CAACtG,QAAQ,CAACyG,cAAc,CAAC;UACjC;QACJ,CAAC,CACJ;QAAElC,QAAQ,EAAE,CACT,cAAc,EACd9F,gBAAgB,CAAC8F,QAAQ,EACzB+B,MAAI,CAAC3H,QAAQ,GAAG,uBAAuB,GAAG,qBAAqB;MACjE,CAAC,CAAC;MACR,OAAOrC,eAAe,CAAC6J,MAAM,CAACI,SAAS,CAAC;IAAC;EAC7C;EACA9D,SAASA,CAAA,EAAG;IACR,MAAM;MAAE9D,QAAQ;MAAEsB,KAAK;MAAExB;IAAiB,CAAC,GAAG,IAAI;IAClD,MAAM0G,IAAI,GAAGjK,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMwL,SAAS,GAAGnB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE/G,gBAAgB,CAAC,EAAE;MAAE0G,IAAI;MAAEZ,QAAQ,EAAE,CAAC,cAAc,EAAE9F,gBAAgB,CAAC8F,QAAQ,CAAC;MAAEuB,SAAS,EAAE,kBAAkB;MAAEC,cAAc,EAAE;QAC3KC,MAAM,EAAEvH,gBAAgB,CAACuH,MAAM;QAC/BrH,QAAQ;QACRsB,KAAK;QACL6C,OAAO,EAAE,IAAI,CAACC,0BAA0B,CAAC,IAAI,CAACzB,SAAS,EAAErB,KAAK;MAClE;IAAE,CAAC,CAAC;IACR,OAAO1D,eAAe,CAAC4J,MAAM,CAACO,SAAS,CAAC;EAC5C;EACA;AACJ;AACA;EACI1B,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAAC9D,OAAO,EAAE;MACf,OAAOyF,OAAO,CAACC,OAAO,CAAC,KAAK,CAAC;IACjC;IACA,OAAO,IAAI,CAAC1F,OAAO,CAAC2F,OAAO,CAAC,CAAC;EACjC;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,OAAO,CAAC,CAAC,KAAK,EAAE;EAChC;EACA,IAAIzF,SAASA,CAAA,EAAG;IACZ,OAAOqC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACxE,EAAE,CAAC4H,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;EACpE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIR,SAASA,CAAA,EAAG;IACZ,MAAM;MAAE5B;IAAM,CAAC,GAAG,IAAI;IACtB,IAAIA,KAAK,KAAK9D,SAAS,EAAE;MACrB,OAAO8D,KAAK;IAChB;IACA,MAAM;MAAErE;IAAU,CAAC,GAAG,IAAI;IAC1B,IAAIA,SAAS,KAAK,IAAI,EAAE;MACpB,OAAOA,SAAS,CAAC+D,WAAW;IAChC;IACA;EACJ;EACAyC,OAAOA,CAAA,EAAG;IACN,MAAME,YAAY,GAAG,IAAI,CAACA,YAAY;IACtC,IAAIA,YAAY,IAAI,IAAI,IAAIA,YAAY,KAAK,EAAE,EAAE;MAC7C,OAAOA,YAAY;IACvB;IACA,OAAOC,YAAY,CAAC,IAAI,CAAC5F,SAAS,EAAE,IAAI,CAACrB,KAAK,EAAE,IAAI,CAACmE,WAAW,CAAC;EACrE;EACA/E,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAAC8H,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,KAAK,CAAC,CAAC;IACxB;EACJ;EACArH,SAASA,CAAA,EAAG;IACR,MAAM;MAAExB;IAAS,CAAC,GAAG,IAAI;IACzB,MAAM8I,KAAK,GAAG;MACV,sBAAsB,EAAE9I;IAC5B,CAAC;IACD,IAAI,CAACT,QAAQ,CAAC2B,IAAI,CAAC4H,KAAK,CAAC;EAC7B;EACAC,WAAWA,CAAA,EAAG;IACV,MAAM;MAAE1C;IAAM,CAAC,GAAG,IAAI;IACtB,OAAQzJ,CAAC,CAAC,KAAK,EAAE;MAAEoM,KAAK,EAAE;QAClB,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAAC,IAAI,CAACC;MACvC,CAAC;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAAE7C,KAAK,KAAK9D,SAAS,GAAG3F,CAAC,CAAC,MAAM,EAAE;MAAEyD,IAAI,EAAE;IAAQ,CAAC,CAAC,GAAGzD,CAAC,CAAC,KAAK,EAAE;MAAEoM,KAAK,EAAE;IAAa,CAAC,EAAE3C,KAAK,CAAC,CAAC;EAC1H;EACA8C,kBAAkBA,CAAA,EAAG;IACjB,IAAIC,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAACtH,eAAe,MAAM,IAAI,IAAIsH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,mBAAmB,CAAC,CAAC;EAC7F;EACA;AACJ;AACA;AACA;EACI,IAAIrH,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACnB,EAAE,CAACsC,aAAa,CAAC,gBAAgB,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAI8F,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC5C,KAAK,KAAK9D,SAAS,IAAI,IAAI,CAACP,SAAS,KAAK,IAAI;EAC9D;EACA;AACJ;AACA;AACA;EACIsH,oBAAoBA,CAAA,EAAG;IACnB,MAAM1C,IAAI,GAAGjK,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM4M,cAAc,GAAG3C,IAAI,KAAK,IAAI,IAAI,IAAI,CAACD,IAAI,KAAK,SAAS;IAC/D,IAAI4C,cAAc,EAAE;MAChB;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,OAAO,CACH3M,CAAC,CAAC,KAAK,EAAE;QAAEoM,KAAK,EAAE;MAA2B,CAAC,EAAEpM,CAAC,CAAC,KAAK,EAAE;QAAEoM,KAAK,EAAE;MAAuB,CAAC,CAAC,EAAEpM,CAAC,CAAC,KAAK,EAAE;QAAEoM,KAAK,EAAE;UACvG,sBAAsB,EAAE,IAAI;UAC5B,6BAA6B,EAAE,CAAC,IAAI,CAACC;QACzC;MAAE,CAAC,EAAErM,CAAC,CAAC,KAAK,EAAE;QAAEoM,KAAK,EAAE,cAAc;QAAE,aAAa,EAAE,MAAM;QAAEQ,GAAG,EAAG3I,EAAE,IAAM,IAAI,CAACkB,aAAa,GAAGlB;MAAI,CAAC,EAAE,IAAI,CAACwF,KAAK,CAAC,CAAC,EAAEzJ,CAAC,CAAC,KAAK,EAAE;QAAEoM,KAAK,EAAE;MAAqB,CAAC,CAAC,CAAC,EACpK,IAAI,CAACD,WAAW,CAAC,CAAC,CACrB;IACL;IACA;AACR;AACA;AACA;IACQ,OAAO,IAAI,CAACA,WAAW,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACIU,gBAAgBA,CAAA,EAAG;IACf,MAAM;MAAEC;IAAY,CAAC,GAAG,IAAI;IAC5B,MAAMC,YAAY,GAAG,IAAI,CAACnB,OAAO,CAAC,CAAC;IACnC,IAAIoB,mBAAmB,GAAG,KAAK;IAC/B,IAAIC,UAAU,GAAGF,YAAY;IAC7B,IAAIE,UAAU,KAAK,EAAE,IAAIH,WAAW,KAAKnH,SAAS,EAAE;MAChDsH,UAAU,GAAGH,WAAW;MACxBE,mBAAmB,GAAG,IAAI;IAC9B;IACA,MAAME,iBAAiB,GAAG;MACtB,aAAa,EAAE,IAAI;MACnB,oBAAoB,EAAEF;IAC1B,CAAC;IACD,MAAMG,QAAQ,GAAGH,mBAAmB,GAAG,aAAa,GAAG,MAAM;IAC7D,OAAQhN,CAAC,CAAC,KAAK,EAAE;MAAE,aAAa,EAAE,MAAM;MAAEoM,KAAK,EAAEc,iBAAiB;MAAEZ,IAAI,EAAEa;IAAS,CAAC,EAAEF,UAAU,CAAC;EACrG;EACA;AACJ;AACA;AACA;EACIG,gBAAgBA,CAAA,EAAG;IACf,MAAMpD,IAAI,GAAGjK,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAEkD,UAAU;MAAEoK,UAAU;MAAEC;IAAa,CAAC,GAAG,IAAI;IACrD,IAAIC,IAAI;IACR,IAAItK,UAAU,IAAIqK,YAAY,KAAK3H,SAAS,EAAE;MAC1C4H,IAAI,GAAGD,YAAY;IACvB,CAAC,MACI;MACD,MAAME,WAAW,GAAGxD,IAAI,KAAK,KAAK,GAAGlI,aAAa,GAAGE,cAAc;MACnEuL,IAAI,GAAGF,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGG,WAAW;IAClF;IACA,OAAOxN,CAAC,CAAC,UAAU,EAAE;MAAEoM,KAAK,EAAE,aAAa;MAAEE,IAAI,EAAE,MAAM;MAAE,aAAa,EAAE,MAAM;MAAEiB,IAAI,EAAEA;IAAK,CAAC,CAAC;EACnG;EACA,IAAIE,SAASA,CAAA,EAAG;IACZ,IAAIjB,EAAE;IACN,MAAM;MAAEM,WAAW;MAAE9J;IAAoB,CAAC,GAAG,IAAI;IACjD,MAAM+J,YAAY,GAAG,IAAI,CAACnB,OAAO,CAAC,CAAC;IACnC;IACA,MAAM8B,YAAY,GAAG,CAAClB,EAAE,GAAGxJ,mBAAmB,CAAC,YAAY,CAAC,MAAM,IAAI,IAAIwJ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAACnB,SAAS;IAC7G;AACR;AACA;AACA;AACA;IACQ,IAAIsC,aAAa,GAAGZ,YAAY;IAChC,IAAIY,aAAa,KAAK,EAAE,IAAIb,WAAW,KAAKnH,SAAS,EAAE;MACnDgI,aAAa,GAAGb,WAAW;IAC/B;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIY,YAAY,KAAK/H,SAAS,EAAE;MAC5BgI,aAAa,GAAGA,aAAa,KAAK,EAAE,GAAGD,YAAY,GAAG,GAAGA,YAAY,KAAKC,aAAa,EAAE;IAC7F;IACA,OAAOA,aAAa;EACxB;EACAC,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAExK,QAAQ;MAAER,OAAO;MAAEK,UAAU;MAAEU;IAAS,CAAC,GAAG,IAAI;IACxD,OAAQ3D,CAAC,CAAC,QAAQ,EAAE;MAAEoD,QAAQ,EAAEA,QAAQ;MAAEyK,EAAE,EAAEjL,OAAO;MAAE,YAAY,EAAE,IAAI,CAAC6K,SAAS;MAAE,eAAe,EAAE,QAAQ;MAAE,eAAe,EAAE,GAAGxK,UAAU,EAAE;MAAE,kBAAkB,EAAE,IAAI,CAAC6K,aAAa,CAAC,CAAC;MAAE,cAAc,EAAE,IAAI,CAACA,aAAa,CAAC,CAAC,KAAK,IAAI,CAAC/K,WAAW;MAAE,eAAe,EAAE,GAAGY,QAAQ,EAAE;MAAEU,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEE,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEqI,GAAG,EAAGZ,OAAO,IAAM,IAAI,CAACA,OAAO,GAAGA;IAAS,CAAC,CAAC;EACpX;EACA8B,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAE7J,EAAE;MAAE8J,UAAU;MAAEC,SAAS;MAAElL,YAAY;MAAEC;IAAY,CAAC,GAAG,IAAI;IACrE,IAAIkB,EAAE,CAACyE,SAAS,CAACuF,QAAQ,CAAC,aAAa,CAAC,IAAIhK,EAAE,CAACyE,SAAS,CAACuF,QAAQ,CAAC,aAAa,CAAC,IAAID,SAAS,EAAE;MAC3F,OAAOjL,WAAW;IACtB;IACA,IAAIgL,UAAU,EAAE;MACZ,OAAOjL,YAAY;IACvB;IACA,OAAO6C,SAAS;EACpB;EACA;AACJ;AACA;EACIuI,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEH,UAAU;MAAEC,SAAS;MAAElL,YAAY;MAAEC;IAAY,CAAC,GAAG,IAAI;IACjE,OAAO,CACH/C,CAAC,CAAC,KAAK,EAAE;MAAE6N,EAAE,EAAE/K,YAAY;MAAEsJ,KAAK,EAAE,aAAa;MAAEE,IAAI,EAAE;IAA8B,CAAC,EAAEyB,UAAU,CAAC,EACrG/N,CAAC,CAAC,KAAK,EAAE;MAAE6N,EAAE,EAAE9K,WAAW;MAAEqJ,KAAK,EAAE,YAAY;MAAEE,IAAI,EAAE;IAA6B,CAAC,EAAE0B,SAAS,CAAC,CACpG;EACL;EACA;AACJ;AACA;AACA;EACIG,mBAAmBA,CAAA,EAAG;IAClB,MAAM;MAAEJ,UAAU;MAAEC;IAAU,CAAC,GAAG,IAAI;IACtC;AACR;AACA;AACA;IACQ,MAAMI,WAAW,GAAG,CAAC,CAACL,UAAU,IAAI,CAAC,CAACC,SAAS;IAC/C,IAAI,CAACI,WAAW,EAAE;MACd;IACJ;IACA,OAAOpO,CAAC,CAAC,KAAK,EAAE;MAAEoM,KAAK,EAAE;IAAgB,CAAC,EAAE,IAAI,CAAC8B,cAAc,CAAC,CAAC,CAAC;EACtE;EACAG,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEjL,QAAQ;MAAEa,EAAE;MAAEhB,UAAU;MAAEqK,YAAY;MAAE/J,cAAc;MAAE+K,OAAO;MAAExB,WAAW;MAAE/C,IAAI;MAAEwE,KAAK;MAAE9K,IAAI;MAAEqB,KAAK;MAAE5B;IAAU,CAAC,GAAG,IAAI;IAClI,MAAM8G,IAAI,GAAGjK,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMoK,yBAAyB,GAAG5G,cAAc,KAAK,UAAU,IAAIA,cAAc,KAAK,SAAS;IAC/F,MAAMiL,cAAc,GAAG,CAACrE,yBAAyB,IAAImE,OAAO,KAAK3I,SAAS;IAC1E,MAAM8I,GAAG,GAAGlN,KAAK,CAAC0C,EAAE,CAAC,GAAG,KAAK,GAAG,KAAK;IACrC,MAAMyK,MAAM,GAAGlN,WAAW,CAAC,UAAU,EAAE,IAAI,CAACyC,EAAE,CAAC;IAC/C,MAAM0K,qBAAqB,GAAG3E,IAAI,KAAK,IAAI,IAAID,IAAI,KAAK,SAAS,IAAI,CAAC2E,MAAM;IAC5E,MAAM/C,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;IAChC,MAAMiD,gBAAgB,GAAG3K,EAAE,CAACsC,aAAa,CAAC,8BAA8B,CAAC,KAAK,IAAI;IAClFxF,iBAAiB,CAAC,IAAI,EAAEkD,EAAE,EAAER,IAAI,EAAEoL,UAAU,CAAC/J,KAAK,CAAC,EAAE1B,QAAQ,CAAC;IAC9D;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM0L,gBAAgB,GAAGvL,cAAc,KAAK,SAAS,IAAKA,cAAc,KAAK,UAAU,KAAKoI,QAAQ,IAAI1I,UAAU,IAAI2L,gBAAgB,CAAE;IACxI,OAAQ5O,CAAC,CAACE,IAAI,EAAE;MAAE6O,GAAG,EAAE,0CAA0C;MAAEnL,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEwI,KAAK,EAAE3K,kBAAkB,CAAC,IAAI,CAACuN,KAAK,EAAE;QACxH,CAAChF,IAAI,GAAG,IAAI;QACZ,SAAS,EAAE0E,MAAM;QACjB,eAAe,EAAElN,WAAW,CAAC,oBAAoB,EAAEyC,EAAE,CAAC;QACtD,iBAAiB,EAAEb,QAAQ;QAC3B,iBAAiB,EAAEH,UAAU;QAC7B,mBAAmB,EAAEqK,YAAY,KAAK3H,SAAS;QAC/C,WAAW,EAAEgG,QAAQ;QACrB,gBAAgB,EAAEmD,gBAAgB;QAClC,iBAAiB,EAAEhC,WAAW,KAAKnH,SAAS;QAC5C,WAAW,EAAEzC,QAAQ;QACrB;QACA,eAAe,EAAE,IAAI;QACrB,CAAC,UAAUuL,GAAG,EAAE,GAAG,IAAI;QACvB,CAAC,eAAe1E,IAAI,EAAE,GAAGA,IAAI,KAAKpE,SAAS;QAC3C,CAAC,kBAAkB2I,OAAO,EAAE,GAAGE,cAAc;QAC7C,CAAC,gBAAgBD,KAAK,EAAE,GAAGA,KAAK,KAAK5I,SAAS;QAC9C,CAAC,0BAA0BpC,cAAc,EAAE,GAAG;MAClD,CAAC;IAAE,CAAC,EAAEvD,CAAC,CAAC,OAAO,EAAE;MAAE+O,GAAG,EAAE,0CAA0C;MAAE3C,KAAK,EAAE,gBAAgB;MAAEyB,EAAE,EAAE,cAAc;MAAEjK,OAAO,EAAE,IAAI,CAACY;IAAa,CAAC,EAAE,IAAI,CAACkI,oBAAoB,CAAC,CAAC,EAAE1M,CAAC,CAAC,KAAK,EAAE;MAAE+O,GAAG,EAAE,0CAA0C;MAAE3C,KAAK,EAAE;IAAuB,CAAC,EAAEpM,CAAC,CAAC,MAAM,EAAE;MAAE+O,GAAG,EAAE,0CAA0C;MAAEtL,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAEzD,CAAC,CAAC,KAAK,EAAE;MAAE+O,GAAG,EAAE,0CAA0C;MAAE3C,KAAK,EAAE,gBAAgB;MAAEQ,GAAG,EAAG3I,EAAE,IAAM,IAAI,CAACuG,eAAe,GAAGvG,EAAG;MAAEqI,IAAI,EAAE;IAAY,CAAC,EAAE,IAAI,CAACO,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAACe,aAAa,CAAC,CAAC,CAAC,EAAE5N,CAAC,CAAC,MAAM,EAAE;MAAE+O,GAAG,EAAE,0CAA0C;MAAEtL,IAAI,EAAE;IAAM,CAAC,CAAC,EAAE,CAAC0G,yBAAyB,IAAI,IAAI,CAACiD,gBAAgB,CAAC,CAAC,CAAC,EAAEjD,yBAAyB,IAAI,IAAI,CAACiD,gBAAgB,CAAC,CAAC,EAAEuB,qBAAqB,IAAI3O,CAAC,CAAC,KAAK,EAAE;MAAE+O,GAAG,EAAE,0CAA0C;MAAE3C,KAAK,EAAE;IAAmB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC+B,mBAAmB,CAAC,CAAC,CAAC;EAC92B;EACA,IAAIlK,EAAEA,CAAA,EAAG;IAAE,OAAO7D,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW6O,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,cAAc,CAAC;MAC5B,YAAY,EAAE,CAAC,cAAc,CAAC;MAC9B,aAAa,EAAE,CAAC,cAAc,CAAC;MAC/B,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,MAAM3G,cAAc,GAAIrE,EAAE,IAAK;EAC3B,MAAMa,KAAK,GAAGb,EAAE,CAACa,KAAK;EACtB,OAAOA,KAAK,KAAKa,SAAS,GAAG1B,EAAE,CAACkF,WAAW,IAAI,EAAE,GAAGrE,KAAK;AAC7D,CAAC;AACD,MAAM+J,UAAU,GAAI/J,KAAK,IAAK;EAC1B,IAAIA,KAAK,IAAI,IAAI,EAAE;IACf,OAAOa,SAAS;EACpB;EACA,IAAI6C,KAAK,CAAC0G,OAAO,CAACpK,KAAK,CAAC,EAAE;IACtB,OAAOA,KAAK,CAAC+D,IAAI,CAAC,GAAG,CAAC;EAC1B;EACA,OAAO/D,KAAK,CAACqK,QAAQ,CAAC,CAAC;AAC3B,CAAC;AACD,MAAMpD,YAAY,GAAGA,CAACqD,IAAI,EAAEtK,KAAK,EAAEmE,WAAW,KAAK;EAC/C,IAAInE,KAAK,KAAKa,SAAS,EAAE;IACrB,OAAO,EAAE;EACb;EACA,IAAI6C,KAAK,CAAC0G,OAAO,CAACpK,KAAK,CAAC,EAAE;IACtB,OAAOA,KAAK,CACPsD,GAAG,CAAEiH,CAAC,IAAKC,YAAY,CAACF,IAAI,EAAEC,CAAC,EAAEpG,WAAW,CAAC,CAAC,CAC9CN,MAAM,CAAE4G,GAAG,IAAKA,GAAG,KAAK,IAAI,CAAC,CAC7B1G,IAAI,CAAC,IAAI,CAAC;EACnB,CAAC,MACI;IACD,OAAOyG,YAAY,CAACF,IAAI,EAAEtK,KAAK,EAAEmE,WAAW,CAAC,IAAI,EAAE;EACvD;AACJ,CAAC;AACD,MAAMqG,YAAY,GAAGA,CAACF,IAAI,EAAEtK,KAAK,EAAEmE,WAAW,KAAK;EAC/C,MAAMuG,SAAS,GAAGJ,IAAI,CAACK,IAAI,CAAEF,GAAG,IAAK;IACjC,OAAO5O,cAAc,CAACmE,KAAK,EAAEwD,cAAc,CAACiH,GAAG,CAAC,EAAEtG,WAAW,CAAC;EAClE,CAAC,CAAC;EACF,OAAOuG,SAAS,GAAGA,SAAS,CAACrG,WAAW,GAAG,IAAI;AACnD,CAAC;AACD,IAAItG,SAAS,GAAG,CAAC;AACjB,MAAMkG,YAAY,GAAG,yBAAyB;AAC9C5G,MAAM,CAAC+J,KAAK,GAAG;EACXwD,GAAG,EAAEzN,YAAY;EACjB0N,EAAE,EAAEzN;AACR,CAAC;AAED,MAAM0N,eAAe,GAAG,qBAAqB;AAE7C,MAAMC,YAAY,GAAG,MAAM;EACvBzN,WAAWA,CAACC,OAAO,EAAE;IACjB5C,gBAAgB,CAAC,IAAI,EAAE4C,OAAO,CAAC;IAC/B,IAAI,CAACO,OAAO,GAAG,cAAckN,eAAe,EAAE,EAAE;IAChD;AACR;AACA;IACQ,IAAI,CAAC1M,QAAQ,GAAG,KAAK;EACzB;EACAiL,MAAMA,CAAA,EAAG;IACL,OAAOrO,CAAC,CAACE,IAAI,EAAE;MAAE6O,GAAG,EAAE,0CAA0C;MAAE/F,IAAI,EAAE,QAAQ;MAAE6E,EAAE,EAAE,IAAI,CAACjL,OAAO;MAAEwJ,KAAK,EAAErM,UAAU,CAAC,IAAI;IAAE,CAAC,CAAC;EAClI;EACA,IAAIkE,EAAEA,CAAA,EAAG;IAAE,OAAO7D,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD,IAAI0P,eAAe,GAAG,CAAC;AACvBD,YAAY,CAAC3D,KAAK,GAAG0D,eAAe;AAEpC,MAAMG,mBAAmB,GAAG,iTAAiT;AAE7U,MAAMC,kBAAkB,GAAG,kqCAAkqC;AAE7rC,MAAMC,aAAa,GAAG,MAAM;EACxB7N,WAAWA,CAACC,OAAO,EAAE;IACjB5C,gBAAgB,CAAC,IAAI,EAAE4C,OAAO,CAAC;IAC/B;AACR;AACA;IACQ,IAAI,CAACsF,OAAO,GAAG,EAAE;EACrB;EACAuI,mBAAmBA,CAACrM,EAAE,EAAE;IACpB,MAAM;MAAE8D;IAAQ,CAAC,GAAG,IAAI;IACxB,OAAOA,OAAO,CAAC8H,IAAI,CAAEpJ,CAAC,IAAKA,CAAC,CAACvB,KAAK,KAAKjB,EAAE,CAACC,MAAM,CAACgB,KAAK,CAAC;EAC3D;EACA;AACJ;AACA;AACA;AACA;EACIqL,iBAAiBA,CAACtM,EAAE,EAAE;IAClB,MAAMwE,MAAM,GAAG,IAAI,CAAC6H,mBAAmB,CAACrM,EAAE,CAAC;IAC3C,MAAMuM,MAAM,GAAG,IAAI,CAACC,SAAS,CAACxM,EAAE,CAAC;IACjC,IAAIwE,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACgB,OAAO,EAAE;MAChE/H,QAAQ,CAAC+G,MAAM,CAACgB,OAAO,EAAE+G,MAAM,CAAC;IACpC;EACJ;EACA;AACJ;AACA;AACA;EACIE,oBAAoBA,CAAA,EAAG;IACnB,MAAM5I,OAAO,GAAG,IAAI,CAACzD,EAAE,CAACD,OAAO,CAAC,aAAa,CAAC;IAC9C,IAAI0D,OAAO,EAAE;MACTA,OAAO,CAACgE,OAAO,CAAC,CAAC;IACrB;EACJ;EACA6E,UAAUA,CAAC1M,EAAE,EAAE;IACX,MAAM;MAAEL;IAAS,CAAC,GAAG,IAAI;IACzB,MAAM6E,MAAM,GAAG,IAAI,CAAC6H,mBAAmB,CAACrM,EAAE,CAAC;IAC3C;IACA;IACA,IAAIL,QAAQ,IAAI6E,MAAM,EAAE;MACpBA,MAAM,CAACqB,OAAO,GAAG7F,EAAE,CAACyG,MAAM,CAACZ,OAAO;IACtC;EACJ;EACA2G,SAASA,CAACxM,EAAE,EAAE;IACV,MAAM;MAAEL,QAAQ;MAAEmE;IAAQ,CAAC,GAAG,IAAI;IAClC,IAAInE,QAAQ,EAAE;MACV;MACA;MACA,OAAOmE,OAAO,CAACgB,MAAM,CAAEtC,CAAC,IAAKA,CAAC,CAACqD,OAAO,CAAC,CAACtB,GAAG,CAAE/B,CAAC,IAAKA,CAAC,CAACvB,KAAK,CAAC;IAC/D;IACA;IACA;IACA,MAAMuD,MAAM,GAAG,IAAI,CAAC6H,mBAAmB,CAACrM,EAAE,CAAC;IAC3C,OAAOwE,MAAM,GAAGA,MAAM,CAACvD,KAAK,GAAGa,SAAS;EAC5C;EACA6K,aAAaA,CAAC7I,OAAO,EAAE;IACnB,MAAM;MAAEnE;IAAS,CAAC,GAAG,IAAI;IACzB,QAAQA,QAAQ;MACZ,KAAK,IAAI;QACL,OAAO,IAAI,CAACiN,qBAAqB,CAAC9I,OAAO,CAAC;MAC9C;QACI,OAAO,IAAI,CAAC+I,kBAAkB,CAAC/I,OAAO,CAAC;IAC/C;EACJ;EACA8I,qBAAqBA,CAAC9I,OAAO,EAAE;IAC3B,OAAOA,OAAO,CAACS,GAAG,CAAEC,MAAM,IAAMrI,CAAC,CAAC,UAAU,EAAE;MAAEoM,KAAK,EAAEhC,MAAM,CAACC,MAAM,CAAC;QAC7D;QACA,uBAAuB,EAAEhC,MAAM,CAACqB;MACpC,CAAC,EAAE/H,WAAW,CAAC0G,MAAM,CAACe,QAAQ,CAAC;IAAE,CAAC,EAAEpJ,CAAC,CAAC,cAAc,EAAE;MAAE8E,KAAK,EAAEuD,MAAM,CAACvD,KAAK;MAAE1B,QAAQ,EAAEiF,MAAM,CAACjF,QAAQ;MAAEsG,OAAO,EAAErB,MAAM,CAACqB,OAAO;MAAE4E,OAAO,EAAE,OAAO;MAAE/K,cAAc,EAAE,KAAK;MAAEoN,WAAW,EAAG9M,EAAE,IAAK;QAC3L,IAAI,CAAC0M,UAAU,CAAC1M,EAAE,CAAC;QACnB,IAAI,CAACsM,iBAAiB,CAACtM,EAAE,CAAC;QAC1B;QACAvD,WAAW,CAAC,IAAI,CAAC;MACrB;IAAE,CAAC,EAAE+H,MAAM,CAACa,IAAI,CAAC,CAAE,CAAC;EAC5B;EACAwH,kBAAkBA,CAAC/I,OAAO,EAAE;IACxB,MAAM+B,OAAO,GAAG/B,OAAO,CAACgB,MAAM,CAAEtC,CAAC,IAAKA,CAAC,CAACqD,OAAO,CAAC,CAACtB,GAAG,CAAE/B,CAAC,IAAKA,CAAC,CAACvB,KAAK,CAAC,CAAC,CAAC,CAAC;IACvE,OAAQ9E,CAAC,CAAC,iBAAiB,EAAE;MAAE8E,KAAK,EAAE4E,OAAO;MAAEiH,WAAW,EAAG9M,EAAE,IAAK,IAAI,CAACsM,iBAAiB,CAACtM,EAAE;IAAE,CAAC,EAAE8D,OAAO,CAACS,GAAG,CAAEC,MAAM,IAAMrI,CAAC,CAAC,UAAU,EAAE;MAAEoM,KAAK,EAAEhC,MAAM,CAACC,MAAM,CAAC;QACxJ;QACA,oBAAoB,EAAEhC,MAAM,CAACvD,KAAK,KAAK4E;MAC3C,CAAC,EAAE/H,WAAW,CAAC0G,MAAM,CAACe,QAAQ,CAAC;IAAE,CAAC,EAAEpJ,CAAC,CAAC,WAAW,EAAE;MAAE8E,KAAK,EAAEuD,MAAM,CAACvD,KAAK;MAAE1B,QAAQ,EAAEiF,MAAM,CAACjF,QAAQ;MAAEQ,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC0M,oBAAoB,CAAC,CAAC;MAAEM,OAAO,EAAG/M,EAAE,IAAK;QAC9J,IAAIA,EAAE,CAACkL,GAAG,KAAK,GAAG,EAAE;UAChB;AACpB;AACA;AACA;AACA;UACoB,IAAI,CAACuB,oBAAoB,CAAC,CAAC;QAC/B;MACJ;IAAE,CAAC,EAAEjI,MAAM,CAACa,IAAI,CAAC,CAAE,CAAC,CAAC;EAC7B;EACAmF,MAAMA,CAAA,EAAG;IACL,MAAM;MAAExD,MAAM;MAAEE,OAAO;MAAEpD,OAAO;MAAEmD;IAAU,CAAC,GAAG,IAAI;IACpD,MAAM+F,qBAAqB,GAAG/F,SAAS,KAAKnF,SAAS,IAAIoF,OAAO,KAAKpF,SAAS;IAC9E,OAAQ3F,CAAC,CAACE,IAAI,EAAE;MAAE6O,GAAG,EAAE,0CAA0C;MAAE3C,KAAK,EAAErM,UAAU,CAAC,IAAI;IAAE,CAAC,EAAEC,CAAC,CAAC,UAAU,EAAE;MAAE+O,GAAG,EAAE;IAA2C,CAAC,EAAElE,MAAM,KAAKlF,SAAS,IAAI3F,CAAC,CAAC,iBAAiB,EAAE;MAAE+O,GAAG,EAAE;IAA2C,CAAC,EAAElE,MAAM,CAAC,EAAEgG,qBAAqB,IAAK7Q,CAAC,CAAC,UAAU,EAAE;MAAE+O,GAAG,EAAE;IAA2C,CAAC,EAAE/O,CAAC,CAAC,WAAW,EAAE;MAAE+O,GAAG,EAAE,0CAA0C;MAAE3C,KAAK,EAAE;IAAgB,CAAC,EAAEtB,SAAS,KAAKnF,SAAS,IAAI3F,CAAC,CAAC,IAAI,EAAE;MAAE+O,GAAG,EAAE;IAA2C,CAAC,EAAEjE,SAAS,CAAC,EAAEC,OAAO,KAAKpF,SAAS,IAAI3F,CAAC,CAAC,GAAG,EAAE;MAAE+O,GAAG,EAAE;IAA2C,CAAC,EAAEhE,OAAO,CAAC,CAAC,CAAE,EAAE,IAAI,CAACyF,aAAa,CAAC7I,OAAO,CAAC,CAAC,CAAC;EAC5qB;EACA,IAAI1D,EAAEA,CAAA,EAAG;IAAE,OAAO7D,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD6P,aAAa,CAAC/D,KAAK,GAAG;EAClBwD,GAAG,EAAEK,mBAAmB;EACxBJ,EAAE,EAAEK;AACR,CAAC;AAED,SAAS7N,MAAM,IAAI2O,UAAU,EAAEjB,YAAY,IAAIkB,iBAAiB,EAAEd,aAAa,IAAIe,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}