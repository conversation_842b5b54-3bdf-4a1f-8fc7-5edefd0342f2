{"ast": null, "code": "import { of } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class EvaluationDataService {\n  constructor(http) {\n    this.http = http;\n  }\n  /**\n   * Load evaluation configuration from static JSON file\n   * This serves as a fallback when API is not available\n   */\n  loadStaticConfig() {\n    return this.http.get('./assets/evaluation-config.json').pipe(catchError(error => {\n      console.error('Failed to load static evaluation config:', error);\n      return of(this.getDefaultConfig());\n    }));\n  }\n  /**\n   * Load evaluation data from XML file (if needed for parsing)\n   */\n  loadEvaluationDataXml() {\n    return this.http.get('./assets/evaluation_data.xml', {\n      responseType: 'text'\n    }).pipe(catchError(error => {\n      console.error('Failed to load evaluation data XML:', error);\n      return of('');\n    }));\n  }\n  /**\n   * Parse XML evaluation data and convert to JSON format\n   */\n  parseEvaluationXml(xmlContent) {\n    try {\n      const parser = new DOMParser();\n      const xmlDoc = parser.parseFromString(xmlContent, 'text/xml');\n      // Check for parsing errors\n      const parserError = xmlDoc.querySelector('parsererror');\n      if (parserError) {\n        console.error('XML parsing error:', parserError.textContent);\n        return of(null);\n      }\n      // Extract evaluation criteria from XML\n      const criteriaField = xmlDoc.querySelector('field[name=\"evaluation_criteria\"]');\n      if (!criteriaField) {\n        console.error('No evaluation criteria found in XML');\n        return of(null);\n      }\n      const criteriaJson = criteriaField.textContent?.trim();\n      if (!criteriaJson) {\n        console.error('Empty evaluation criteria in XML');\n        return of(null);\n      }\n      // Parse the JSON content\n      const criteria = JSON.parse(criteriaJson);\n      // Extract other fields\n      const durationField = xmlDoc.querySelector('field[name=\"evaluation_duration\"]');\n      const attemptsField = xmlDoc.querySelector('field[name=\"max_attempts\"]');\n      const config = {\n        evaluation_duration: durationField ? parseInt(durationField.textContent || '7') : 7,\n        max_attempts: attemptsField ? parseInt(attemptsField.textContent || '1') : 1,\n        criteria: criteria\n      };\n      return of(config);\n    } catch (error) {\n      console.error('Error parsing evaluation XML:', error);\n      return of(null);\n    }\n  }\n  /**\n   * Get evaluation configuration with fallback strategy:\n   * 1. Try to load from static JSON\n   * 2. If that fails, use default config\n   */\n  getEvaluationConfig() {\n    return this.loadStaticConfig().pipe(catchError(() => {\n      console.log('Static JSON config failed, using default config...');\n      return of(this.getDefaultConfig());\n    }));\n  }\n  /**\n   * Minimal default configuration as emergency fallback only\n   * This should only be used if both API and static file fail\n   */\n  getDefaultConfig() {\n    return {\n      evaluation_duration: 7,\n      max_attempts: 1,\n      criteria: {\n        categories: [{\n          id: 'question_1',\n          name: 'General Evaluation',\n          description: 'Please rate the overall performance',\n          max_score: 10,\n          questions: [{\n            id: 'general_rating',\n            text: 'How would you rate the overall performance?',\n            type: 'rating',\n            scale: 10\n          }]\n        }]\n      }\n    };\n  }\n  static {\n    this.ɵfac = function EvaluationDataService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EvaluationDataService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: EvaluationDataService,\n      factory: EvaluationDataService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "catchError", "EvaluationDataService", "constructor", "http", "loadStaticConfig", "get", "pipe", "error", "console", "getDefaultConfig", "loadEvaluationDataXml", "responseType", "parseEvaluationXml", "xmlContent", "parser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xmlDoc", "parseFromString", "parserE<PERSON>r", "querySelector", "textContent", "criteriaField", "<PERSON><PERSON><PERSON>", "trim", "criteria", "JSON", "parse", "durationField", "<PERSON><PERSON>ield", "config", "evaluation_duration", "parseInt", "max_attempts", "getEvaluationConfig", "log", "categories", "id", "name", "description", "max_score", "questions", "text", "type", "scale", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/src/app/services/evaluation-data.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, of } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport { EvaluationConfig } from '../models/evaluation.models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class EvaluationDataService {\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * Load evaluation configuration from static JSON file\n   * This serves as a fallback when API is not available\n   */\n  loadStaticConfig(): Observable<EvaluationConfig> {\n    return this.http.get<EvaluationConfig>('./assets/evaluation-config.json').pipe(\n      catchError(error => {\n        console.error('Failed to load static evaluation config:', error);\n        return of(this.getDefaultConfig());\n      })\n    );\n  }\n\n  /**\n   * Load evaluation data from XML file (if needed for parsing)\n   */\n  loadEvaluationDataXml(): Observable<string> {\n    return this.http.get('./assets/evaluation_data.xml', { responseType: 'text' }).pipe(\n      catchError(error => {\n        console.error('Failed to load evaluation data XML:', error);\n        return of('');\n      })\n    );\n  }\n\n  /**\n   * Parse XML evaluation data and convert to JSON format\n   */\n  parseEvaluationXml(xmlContent: string): Observable<EvaluationConfig | null> {\n    try {\n      const parser = new DOMParser();\n      const xmlDoc = parser.parseFromString(xmlContent, 'text/xml');\n      \n      // Check for parsing errors\n      const parserError = xmlDoc.querySelector('parsererror');\n      if (parserError) {\n        console.error('XML parsing error:', parserError.textContent);\n        return of(null);\n      }\n\n      // Extract evaluation criteria from XML\n      const criteriaField = xmlDoc.querySelector('field[name=\"evaluation_criteria\"]');\n      if (!criteriaField) {\n        console.error('No evaluation criteria found in XML');\n        return of(null);\n      }\n\n      const criteriaJson = criteriaField.textContent?.trim();\n      if (!criteriaJson) {\n        console.error('Empty evaluation criteria in XML');\n        return of(null);\n      }\n\n      // Parse the JSON content\n      const criteria = JSON.parse(criteriaJson);\n      \n      // Extract other fields\n      const durationField = xmlDoc.querySelector('field[name=\"evaluation_duration\"]');\n      const attemptsField = xmlDoc.querySelector('field[name=\"max_attempts\"]');\n\n      const config: EvaluationConfig = {\n        evaluation_duration: durationField ? parseInt(durationField.textContent || '7') : 7,\n        max_attempts: attemptsField ? parseInt(attemptsField.textContent || '1') : 1,\n        criteria: criteria\n      };\n\n      return of(config);\n\n    } catch (error) {\n      console.error('Error parsing evaluation XML:', error);\n      return of(null);\n    }\n  }\n\n  /**\n   * Get evaluation configuration with fallback strategy:\n   * 1. Try to load from static JSON\n   * 2. If that fails, use default config\n   */\n  getEvaluationConfig(): Observable<EvaluationConfig> {\n    return this.loadStaticConfig().pipe(\n      catchError(() => {\n        console.log('Static JSON config failed, using default config...');\n        return of(this.getDefaultConfig());\n      })\n    );\n  }\n\n  /**\n   * Minimal default configuration as emergency fallback only\n   * This should only be used if both API and static file fail\n   */\n  private getDefaultConfig(): EvaluationConfig {\n    return {\n      evaluation_duration: 7,\n      max_attempts: 1,\n      criteria: {\n        categories: [\n          {\n            id: 'question_1',\n            name: 'General Evaluation',\n            description: 'Please rate the overall performance',\n            max_score: 10,\n            questions: [\n              {\n                id: 'general_rating',\n                text: 'How would you rate the overall performance?',\n                type: 'rating',\n                scale: 10\n              }\n            ]\n          }\n        ]\n      }\n    };\n  }\n}\n"], "mappings": "AAEA,SAAqBA,EAAE,QAAQ,MAAM;AACrC,SAASC,UAAU,QAAa,gBAAgB;;;AAMhD,OAAM,MAAOC,qBAAqB;EAEhCC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAe;EAEvC;;;;EAIAC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACD,IAAI,CAACE,GAAG,CAAmB,iCAAiC,CAAC,CAACC,IAAI,CAC5EN,UAAU,CAACO,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,OAAOR,EAAE,CAAC,IAAI,CAACU,gBAAgB,EAAE,CAAC;IACpC,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAC,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACP,IAAI,CAACE,GAAG,CAAC,8BAA8B,EAAE;MAAEM,YAAY,EAAE;IAAM,CAAE,CAAC,CAACL,IAAI,CACjFN,UAAU,CAACO,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAOR,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAa,kBAAkBA,CAACC,UAAkB;IACnC,IAAI;MACF,MAAMC,MAAM,GAAG,IAAIC,SAAS,EAAE;MAC9B,MAAMC,MAAM,GAAGF,MAAM,CAACG,eAAe,CAACJ,UAAU,EAAE,UAAU,CAAC;MAE7D;MACA,MAAMK,WAAW,GAAGF,MAAM,CAACG,aAAa,CAAC,aAAa,CAAC;MACvD,IAAID,WAAW,EAAE;QACfV,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEW,WAAW,CAACE,WAAW,CAAC;QAC5D,OAAOrB,EAAE,CAAC,IAAI,CAAC;MACjB;MAEA;MACA,MAAMsB,aAAa,GAAGL,MAAM,CAACG,aAAa,CAAC,mCAAmC,CAAC;MAC/E,IAAI,CAACE,aAAa,EAAE;QAClBb,OAAO,CAACD,KAAK,CAAC,qCAAqC,CAAC;QACpD,OAAOR,EAAE,CAAC,IAAI,CAAC;MACjB;MAEA,MAAMuB,YAAY,GAAGD,aAAa,CAACD,WAAW,EAAEG,IAAI,EAAE;MACtD,IAAI,CAACD,YAAY,EAAE;QACjBd,OAAO,CAACD,KAAK,CAAC,kCAAkC,CAAC;QACjD,OAAOR,EAAE,CAAC,IAAI,CAAC;MACjB;MAEA;MACA,MAAMyB,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC;MAEzC;MACA,MAAMK,aAAa,GAAGX,MAAM,CAACG,aAAa,CAAC,mCAAmC,CAAC;MAC/E,MAAMS,aAAa,GAAGZ,MAAM,CAACG,aAAa,CAAC,4BAA4B,CAAC;MAExE,MAAMU,MAAM,GAAqB;QAC/BC,mBAAmB,EAAEH,aAAa,GAAGI,QAAQ,CAACJ,aAAa,CAACP,WAAW,IAAI,GAAG,CAAC,GAAG,CAAC;QACnFY,YAAY,EAAEJ,aAAa,GAAGG,QAAQ,CAACH,aAAa,CAACR,WAAW,IAAI,GAAG,CAAC,GAAG,CAAC;QAC5EI,QAAQ,EAAEA;OACX;MAED,OAAOzB,EAAE,CAAC8B,MAAM,CAAC;IAEnB,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAOR,EAAE,CAAC,IAAI,CAAC;IACjB;EACF;EAEA;;;;;EAKAkC,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC7B,gBAAgB,EAAE,CAACE,IAAI,CACjCN,UAAU,CAAC,MAAK;MACdQ,OAAO,CAAC0B,GAAG,CAAC,oDAAoD,CAAC;MACjE,OAAOnC,EAAE,CAAC,IAAI,CAACU,gBAAgB,EAAE,CAAC;IACpC,CAAC,CAAC,CACH;EACH;EAEA;;;;EAIQA,gBAAgBA,CAAA;IACtB,OAAO;MACLqB,mBAAmB,EAAE,CAAC;MACtBE,YAAY,EAAE,CAAC;MACfR,QAAQ,EAAE;QACRW,UAAU,EAAE,CACV;UACEC,EAAE,EAAE,YAAY;UAChBC,IAAI,EAAE,oBAAoB;UAC1BC,WAAW,EAAE,qCAAqC;UAClDC,SAAS,EAAE,EAAE;UACbC,SAAS,EAAE,CACT;YACEJ,EAAE,EAAE,gBAAgB;YACpBK,IAAI,EAAE,6CAA6C;YACnDC,IAAI,EAAE,QAAQ;YACdC,KAAK,EAAE;WACR;SAEJ;;KAGN;EACH;;;uCAvHW1C,qBAAqB,EAAA2C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAArB9C,qBAAqB;MAAA+C,OAAA,EAArB/C,qBAAqB,CAAAgD,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}