{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class EvaluationApiService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = '/api/evaluation';\n    this.httpOptions = {\n      headers: new HttpHeaders({\n        'Content-Type': 'application/json'\n      })\n    };\n  }\n  validateToken(token) {\n    return this.http.post(`${this.baseUrl}/validate`, {\n      'params': {\n        'token': token\n      }\n    }, this.httpOptions).pipe(map(response => {\n      if (response.error) {\n        return {\n          success: false,\n          error: response.error.message,\n          code: response.error.code?.toString()\n        };\n      }\n      return response.result || {\n        success: false,\n        error: 'No result in response'\n      };\n    }));\n  }\n  getConfig(token) {\n    return this.http.post(`${this.baseUrl}/config`, {\n      'params': {\n        'token': token\n      }\n    }, this.httpOptions).pipe(map(response => {\n      if (response.error) {\n        return {\n          success: false,\n          error: response.error.message,\n          code: response.error.code?.toString()\n        };\n      }\n      return response.result || {\n        success: false,\n        error: 'No result in response'\n      };\n    }));\n  }\n  submitEvaluation(token, evaluationData) {\n    return this.http.post(`${this.baseUrl}/submit`, {\n      params: {\n        'token': token,\n        evaluation_data: evaluationData\n      }\n    }, this.httpOptions).pipe(map(response => {\n      if (response.error) {\n        return {\n          success: false,\n          error: response.error.message,\n          code: response.error.code?.toString()\n        };\n      }\n      return response.result || {\n        success: false,\n        error: 'No result in response'\n      };\n    }));\n  }\n  static {\n    this.ɵfac = function EvaluationApiService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EvaluationApiService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: EvaluationApiService,\n      factory: EvaluationApiService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "map", "EvaluationApiService", "constructor", "http", "baseUrl", "httpOptions", "headers", "validateToken", "token", "post", "pipe", "response", "error", "success", "message", "code", "toString", "result", "getConfig", "submitEvaluation", "evaluationData", "params", "evaluation_data", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/src/app/services/evaluation-api.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport {\n  ApiResponse,\n  ValidationResponse,\n  EvaluationConfig,\n  EvaluationSubmission,\n  SubmissionResponse\n} from '../models/evaluation.models';\n\n// JSON-RPC response wrapper\ninterface JsonRpcResponse<T = any> {\n  jsonrpc: string;\n  id: any;\n  result?: T;\n  error?: {\n    code: number;\n    message: string;\n    data?: any;\n  };\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class EvaluationApiService {\n  private baseUrl = '/api/evaluation';\n  \n  private httpOptions = {\n    headers: new HttpHeaders({\n      'Content-Type': 'application/json'\n    })\n  };\n\n  constructor(private http: HttpClient) {}\n\n  validateToken(token: string): Observable<ApiResponse<ValidationResponse>> {\n    return this.http.post<JsonRpcResponse<ApiResponse<ValidationResponse>>>(\n      `${this.baseUrl}/validate`,\n      { 'params': {'token': token} },\n      this.httpOptions\n    ).pipe(\n      map(response => {\n        if (response.error) {\n          return {\n            success: false,\n            error: response.error.message,\n            code: response.error.code?.toString()\n          };\n        }\n        return response.result || { success: false, error: 'No result in response' };\n      })\n    );\n  }\n\n  getConfig(token: string): Observable<ApiResponse<EvaluationConfig>> {\n    return this.http.post<JsonRpcResponse<ApiResponse<EvaluationConfig>>>(\n      `${this.baseUrl}/config`,\n      { 'params': {'token': token} },\n      this.httpOptions\n    ).pipe(\n      map(response => {\n        if (response.error) {\n          return {\n            success: false,\n            error: response.error.message,\n            code: response.error.code?.toString()\n          };\n        }\n        return response.result || { success: false, error: 'No result in response' };\n      })\n    );\n  }\n\n  submitEvaluation(token: string, evaluationData: EvaluationSubmission): Observable<ApiResponse<SubmissionResponse>> {\n    return this.http.post<JsonRpcResponse<ApiResponse<SubmissionResponse>>>(\n      `${this.baseUrl}/submit`,\n      {\n        params: {'token': token, evaluation_data: evaluationData}\n      },\n      this.httpOptions\n    ).pipe(\n      map(response => {\n        if (response.error) {\n          return {\n            success: false,\n            error: response.error.message,\n            code: response.error.code?.toString()\n          };\n        }\n        return response.result || { success: false, error: 'No result in response' };\n      })\n    );\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAE9D,SAASC,GAAG,QAAQ,gBAAgB;;;AAwBpC,OAAM,MAAOC,oBAAoB;EAS/BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IARhB,KAAAC,OAAO,GAAG,iBAAiB;IAE3B,KAAAC,WAAW,GAAG;MACpBC,OAAO,EAAE,IAAIP,WAAW,CAAC;QACvB,cAAc,EAAE;OACjB;KACF;EAEsC;EAEvCQ,aAAaA,CAACC,KAAa;IACzB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CACnB,GAAG,IAAI,CAACL,OAAO,WAAW,EAC1B;MAAE,QAAQ,EAAE;QAAC,OAAO,EAAEI;MAAK;IAAC,CAAE,EAC9B,IAAI,CAACH,WAAW,CACjB,CAACK,IAAI,CACJV,GAAG,CAACW,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAClB,OAAO;UACLC,OAAO,EAAE,KAAK;UACdD,KAAK,EAAED,QAAQ,CAACC,KAAK,CAACE,OAAO;UAC7BC,IAAI,EAAEJ,QAAQ,CAACC,KAAK,CAACG,IAAI,EAAEC,QAAQ;SACpC;MACH;MACA,OAAOL,QAAQ,CAACM,MAAM,IAAI;QAAEJ,OAAO,EAAE,KAAK;QAAED,KAAK,EAAE;MAAuB,CAAE;IAC9E,CAAC,CAAC,CACH;EACH;EAEAM,SAASA,CAACV,KAAa;IACrB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CACnB,GAAG,IAAI,CAACL,OAAO,SAAS,EACxB;MAAE,QAAQ,EAAE;QAAC,OAAO,EAAEI;MAAK;IAAC,CAAE,EAC9B,IAAI,CAACH,WAAW,CACjB,CAACK,IAAI,CACJV,GAAG,CAACW,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAClB,OAAO;UACLC,OAAO,EAAE,KAAK;UACdD,KAAK,EAAED,QAAQ,CAACC,KAAK,CAACE,OAAO;UAC7BC,IAAI,EAAEJ,QAAQ,CAACC,KAAK,CAACG,IAAI,EAAEC,QAAQ;SACpC;MACH;MACA,OAAOL,QAAQ,CAACM,MAAM,IAAI;QAAEJ,OAAO,EAAE,KAAK;QAAED,KAAK,EAAE;MAAuB,CAAE;IAC9E,CAAC,CAAC,CACH;EACH;EAEAO,gBAAgBA,CAACX,KAAa,EAAEY,cAAoC;IAClE,OAAO,IAAI,CAACjB,IAAI,CAACM,IAAI,CACnB,GAAG,IAAI,CAACL,OAAO,SAAS,EACxB;MACEiB,MAAM,EAAE;QAAC,OAAO,EAAEb,KAAK;QAAEc,eAAe,EAAEF;MAAc;KACzD,EACD,IAAI,CAACf,WAAW,CACjB,CAACK,IAAI,CACJV,GAAG,CAACW,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAClB,OAAO;UACLC,OAAO,EAAE,KAAK;UACdD,KAAK,EAAED,QAAQ,CAACC,KAAK,CAACE,OAAO;UAC7BC,IAAI,EAAEJ,QAAQ,CAACC,KAAK,CAACG,IAAI,EAAEC,QAAQ;SACpC;MACH;MACA,OAAOL,QAAQ,CAACM,MAAM,IAAI;QAAEJ,OAAO,EAAE,KAAK;QAAED,KAAK,EAAE;MAAuB,CAAE;IAC9E,CAAC,CAAC,CACH;EACH;;;uCApEWX,oBAAoB,EAAAsB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAApBzB,oBAAoB;MAAA0B,OAAA,EAApB1B,oBAAoB,CAAA2B,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}