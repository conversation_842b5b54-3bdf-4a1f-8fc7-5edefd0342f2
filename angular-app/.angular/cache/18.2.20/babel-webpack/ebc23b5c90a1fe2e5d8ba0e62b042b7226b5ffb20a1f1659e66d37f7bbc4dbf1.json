{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport { TranslateModule } from '@ngx-translate/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@ionic/angular\";\nimport * as i3 from \"@ngx-translate/core\";\nfunction EvaluationCardComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵlistener(\"click\", function EvaluationCardComponent_div_7_Template_div_click_0_listener() {\n      const option_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.setRating(option_r2.value));\n    });\n    i0.ɵɵelementStart(3, \"div\", 5)(4, \"span\", 6);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 7);\n    i0.ɵɵelement(8, \"div\", 8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const option_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"selected\", ctx_r2.currentRating === option_r2.value);\n    i0.ɵɵattribute(\"aria-label\", i0.ɵɵpipeBind1(1, 6, option_r2.label) + \": \" + i0.ɵɵpipeBind1(2, 8, option_r2.description));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 10, option_r2.label));\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.currentRating === option_r2.value);\n  }\n}\nexport class EvaluationCardComponent {\n  constructor() {\n    this.currentRating = 0;\n    this.ratingChange = new EventEmitter();\n    // Rating options with 4-point scale\n    this.ratingOptions = [{\n      value: 4,\n      label: 'RATING.VERY_GOOD',\n      description: 'RATING.VERY_GOOD_DESC'\n    }, {\n      value: 3,\n      label: 'RATING.GOOD',\n      description: 'RATING.GOOD_DESC'\n    }, {\n      value: 2,\n      label: 'RATING.BAD',\n      description: 'RATING.BAD_DESC'\n    }, {\n      value: 1,\n      label: 'RATING.VERY_BAD',\n      description: 'RATING.VERY_BAD_DESC'\n    }];\n  }\n  setRating(score) {\n    this.ratingChange.emit(score);\n  }\n  static {\n    this.ɵfac = function EvaluationCardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EvaluationCardComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EvaluationCardComponent,\n      selectors: [[\"app-evaluation-card\"]],\n      inputs: {\n        category: \"category\",\n        currentRating: \"currentRating\"\n      },\n      outputs: {\n        ratingChange: \"ratingChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 8,\n      vars: 3,\n      consts: [[1, \"evaluation-card\"], [3, \"title\"], [1, \"rating-options\"], [\"class\", \"rating-option\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-option\", 3, \"click\"], [1, \"option-content\"], [1, \"option-label\"], [1, \"option-circle\"], [1, \"circle-inner\"]],\n      template: function EvaluationCardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"ion-card\")(2, \"ion-card-header\")(3, \"ion-card-title\", 1);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"ion-card-content\")(6, \"div\", 2);\n          i0.ɵɵtemplate(7, EvaluationCardComponent_div_7_Template, 9, 12, \"div\", 3);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"title\", ctx.category.description);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(ctx.category.name);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.ratingOptions);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, IonicModule, i2.IonCard, i2.IonCardContent, i2.IonCardHeader, i2.IonCardTitle, TranslateModule, i3.TranslatePipe],\n      styles: [\".evaluation-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  font-family: \\\"Cairo\\\", sans-serif;\\n}\\n.evaluation-card[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%] {\\n  margin: 0;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n}\\n.evaluation-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%] {\\n  padding-bottom: 10px;\\n}\\n.evaluation-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%] {\\n  font-family: \\\"Cairo\\\", sans-serif;\\n  font-weight: 500;\\n  cursor: help;\\n}\\n.evaluation-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]:hover {\\n  color: #EBC940;\\n}\\n.evaluation-card[_ngcontent-%COMP%]   .rating-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n  margin: 20px 0;\\n}\\n.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 56pt;\\n  background-color: #ffffff;\\n  border: 1px solid #DFDFDF;\\n  border-radius: 16pt;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  position: relative;\\n}\\n.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]:hover {\\n  background-color: #fafafa;\\n}\\n.evaluation-card[_ngcontent-%COMP%]   .rating-option.selected[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border: 1px solid #EBC940;\\n  box-shadow: 0 0 0 4px rgba(235, 201, 64, 0.1607843137);\\n}\\n.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 0 24px;\\n  height: 100%;\\n  font-family: \\\"Cairo\\\", sans-serif;\\n  font-weight: 500;\\n}\\n.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-label[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #333;\\n}\\n.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-circle[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  border: 2px solid #ccc;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n}\\n.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-circle[_ngcontent-%COMP%]   .circle-inner[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  background-color: #ccc;\\n  transition: all 0.2s ease;\\n}\\n.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-circle[_ngcontent-%COMP%]   .circle-inner.selected[_ngcontent-%COMP%] {\\n  background-color: #EBC940;\\n}\\n.evaluation-card[_ngcontent-%COMP%]   .rating-option.selected[_ngcontent-%COMP%]   .option-circle[_ngcontent-%COMP%] {\\n  border-color: #EBC940;\\n  box-shadow: 0 0 0 2px rgba(235, 201, 64, 0.1607843137);\\n}\\n.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]:not(.selected)   .option-circle[_ngcontent-%COMP%] {\\n  box-shadow: none;\\n}\\n\\n@media (max-width: 768px) {\\n  .evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%] {\\n    height: 48pt;\\n  }\\n  .evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-content[_ngcontent-%COMP%] {\\n    padding: 0 20px;\\n  }\\n  .evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-label[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%] {\\n    height: 44pt;\\n  }\\n  .evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-content[_ngcontent-%COMP%] {\\n    padding: 0 16px;\\n  }\\n  .evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-label[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  .evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-circle[_ngcontent-%COMP%] {\\n    width: 20px;\\n    height: 20px;\\n  }\\n  .evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-circle[_ngcontent-%COMP%]   .circle-inner[_ngcontent-%COMP%] {\\n    width: 10px;\\n    height: 10px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "IonicModule", "TranslateModule", "i0", "ɵɵelementStart", "ɵɵlistener", "EvaluationCardComponent_div_7_Template_div_click_0_listener", "option_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "setRating", "value", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵclassProp", "currentRating", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "label", "EvaluationCardComponent", "constructor", "ratingChange", "ratingOptions", "description", "score", "emit", "selectors", "inputs", "category", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "EvaluationCardComponent_Template", "rf", "ctx", "ɵɵtemplate", "EvaluationCardComponent_div_7_Template", "ɵɵproperty", "name", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i2", "IonCard", "IonCardContent", "IonCardHeader", "IonCardTitle", "i3", "TranslatePipe", "styles"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/src/app/components/evaluation-card/evaluation-card.component.ts", "/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/src/app/components/evaluation-card/evaluation-card.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { EvaluationCategory, EvaluationOption } from '../../models/evaluation.models';\n\n@Component({\n  selector: 'app-evaluation-card',\n  standalone: true,\n  imports: [CommonModule, IonicModule, TranslateModule],\n  templateUrl: './evaluation-card.component.html',\n  styleUrls: ['./evaluation-card.component.scss']\n})\nexport class EvaluationCardComponent {\n  @Input() category!: EvaluationCategory;\n  @Input() currentRating: number = 0;\n  @Output() ratingChange = new EventEmitter<number>();\n\n  // Rating options with 4-point scale\n  ratingOptions: EvaluationOption[] = [\n    { value: 4, label: 'RATING.VERY_GOOD', description: 'RATING.VERY_GOOD_DESC' },\n    { value: 3, label: 'RATING.GOOD', description: 'RATING.GOOD_DESC' },\n    { value: 2, label: 'RATING.BAD', description: 'RATING.BAD_DESC' },\n    { value: 1, label: 'RATING.VERY_BAD', description: 'RATING.VERY_BAD_DESC' }\n  ];\n\n  setRating(score: number): void {\n    this.ratingChange.emit(score);\n  }\n}\n", "<div class=\"evaluation-card\">\n  <ion-card>\n    <ion-card-header>\n      <ion-card-title [title]=\"category.description\">{{ category.name }}</ion-card-title>\n    </ion-card-header>\n    <ion-card-content>\n      <div class=\"rating-options\">\n        <div\n          *ngFor=\"let option of ratingOptions\"\n          class=\"rating-option\"\n          [class.selected]=\"currentRating === option.value\"\n          (click)=\"setRating(option.value)\"\n          [attr.aria-label]=\"(option.label | translate) + ': ' + (option.description | translate)\">\n          <div class=\"option-content\">\n            <span class=\"option-label\">{{ option.label | translate }}</span>\n            <div class=\"option-circle\">\n              <div class=\"circle-inner\" [class.selected]=\"currentRating === option.value\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </ion-card-content>\n  </ion-card>\n</div>\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAQ,eAAe;AACtE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,eAAe,QAAQ,qBAAqB;;;;;;;;ICI7CC,EAAA,CAAAC,cAAA,aAK2F;;;IADzFD,EAAA,CAAAE,UAAA,mBAAAC,4DAAA;MAAA,MAAAC,SAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,SAAA,CAAAP,SAAA,CAAAQ,KAAA,CAAuB;IAAA,EAAC;IAG/BZ,EADF,CAAAC,cAAA,aAA4B,cACC;IAAAD,EAAA,CAAAa,MAAA,GAA8B;;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAChEd,EAAA,CAAAC,cAAA,aAA2B;IACzBD,EAAA,CAAAe,SAAA,aAAkF;IAGxFf,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;;;;;IATJd,EAAA,CAAAgB,WAAA,aAAAR,MAAA,CAAAS,aAAA,KAAAb,SAAA,CAAAQ,KAAA,CAAiD;;IAIpBZ,EAAA,CAAAkB,SAAA,GAA8B;IAA9BlB,EAAA,CAAAmB,iBAAA,CAAAnB,EAAA,CAAAoB,WAAA,QAAAhB,SAAA,CAAAiB,KAAA,EAA8B;IAE7BrB,EAAA,CAAAkB,SAAA,GAAiD;IAAjDlB,EAAA,CAAAgB,WAAA,aAAAR,MAAA,CAAAS,aAAA,KAAAb,SAAA,CAAAQ,KAAA,CAAiD;;;ADHzF,OAAM,MAAOU,uBAAuB;EAPpCC,YAAA;IASW,KAAAN,aAAa,GAAW,CAAC;IACxB,KAAAO,YAAY,GAAG,IAAI5B,YAAY,EAAU;IAEnD;IACA,KAAA6B,aAAa,GAAuB,CAClC;MAAEb,KAAK,EAAE,CAAC;MAAES,KAAK,EAAE,kBAAkB;MAAEK,WAAW,EAAE;IAAuB,CAAE,EAC7E;MAAEd,KAAK,EAAE,CAAC;MAAES,KAAK,EAAE,aAAa;MAAEK,WAAW,EAAE;IAAkB,CAAE,EACnE;MAAEd,KAAK,EAAE,CAAC;MAAES,KAAK,EAAE,YAAY;MAAEK,WAAW,EAAE;IAAiB,CAAE,EACjE;MAAEd,KAAK,EAAE,CAAC;MAAES,KAAK,EAAE,iBAAiB;MAAEK,WAAW,EAAE;IAAsB,CAAE,CAC5E;;EAEDf,SAASA,CAACgB,KAAa;IACrB,IAAI,CAACH,YAAY,CAACI,IAAI,CAACD,KAAK,CAAC;EAC/B;;;uCAfWL,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAO,SAAA;MAAAC,MAAA;QAAAC,QAAA;QAAAd,aAAA;MAAA;MAAAe,OAAA;QAAAR,YAAA;MAAA;MAAAS,UAAA;MAAAC,QAAA,GAAAlC,EAAA,CAAAmC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV9BzC,EAHN,CAAAC,cAAA,aAA6B,eACjB,sBACS,wBACgC;UAAAD,EAAA,CAAAa,MAAA,GAAmB;UACpEb,EADoE,CAAAc,YAAA,EAAiB,EACnE;UAEhBd,EADF,CAAAC,cAAA,uBAAkB,aACY;UAC1BD,EAAA,CAAA2C,UAAA,IAAAC,sCAAA,kBAK2F;UAWnG5C,EAHM,CAAAc,YAAA,EAAM,EACW,EACV,EACP;;;UApBgBd,EAAA,CAAAkB,SAAA,GAA8B;UAA9BlB,EAAA,CAAA6C,UAAA,UAAAH,GAAA,CAAAX,QAAA,CAAAL,WAAA,CAA8B;UAAC1B,EAAA,CAAAkB,SAAA,EAAmB;UAAnBlB,EAAA,CAAAmB,iBAAA,CAAAuB,GAAA,CAAAX,QAAA,CAAAe,IAAA,CAAmB;UAK3C9C,EAAA,CAAAkB,SAAA,GAAgB;UAAhBlB,EAAA,CAAA6C,UAAA,YAAAH,GAAA,CAAAjB,aAAA,CAAgB;;;qBDCjC5B,YAAY,EAAAkD,EAAA,CAAAC,OAAA,EAAElD,WAAW,EAAAmD,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EAAEtD,eAAe,EAAAuD,EAAA,CAAAC,aAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}