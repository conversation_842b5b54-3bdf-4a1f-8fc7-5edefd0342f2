{"ast": null, "code": "import { EmptyError } from './util/EmptyError';\nimport { SafeSubscriber } from './Subscriber';\nexport function firstValueFrom(source, config) {\n  const hasConfig = typeof config === 'object';\n  return new Promise((resolve, reject) => {\n    const subscriber = new SafeSubscriber({\n      next: value => {\n        resolve(value);\n        subscriber.unsubscribe();\n      },\n      error: reject,\n      complete: () => {\n        if (hasConfig) {\n          resolve(config.defaultValue);\n        } else {\n          reject(new EmptyError());\n        }\n      }\n    });\n    source.subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["EmptyError", "SafeSubscriber", "firstValueFrom", "source", "config", "hasConfig", "Promise", "resolve", "reject", "subscriber", "next", "value", "unsubscribe", "error", "complete", "defaultValue", "subscribe"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/rxjs/dist/esm/internal/firstValueFrom.js"], "sourcesContent": ["import { EmptyError } from './util/EmptyError';\nimport { SafeSubscriber } from './Subscriber';\nexport function firstValueFrom(source, config) {\n    const hasConfig = typeof config === 'object';\n    return new Promise((resolve, reject) => {\n        const subscriber = new SafeSubscriber({\n            next: (value) => {\n                resolve(value);\n                subscriber.unsubscribe();\n            },\n            error: reject,\n            complete: () => {\n                if (hasConfig) {\n                    resolve(config.defaultValue);\n                }\n                else {\n                    reject(new EmptyError());\n                }\n            },\n        });\n        source.subscribe(subscriber);\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,cAAc,QAAQ,cAAc;AAC7C,OAAO,SAASC,cAAcA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC3C,MAAMC,SAAS,GAAG,OAAOD,MAAM,KAAK,QAAQ;EAC5C,OAAO,IAAIE,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACpC,MAAMC,UAAU,GAAG,IAAIR,cAAc,CAAC;MAClCS,IAAI,EAAGC,KAAK,IAAK;QACbJ,OAAO,CAACI,KAAK,CAAC;QACdF,UAAU,CAACG,WAAW,CAAC,CAAC;MAC5B,CAAC;MACDC,KAAK,EAAEL,MAAM;MACbM,QAAQ,EAAEA,CAAA,KAAM;QACZ,IAAIT,SAAS,EAAE;UACXE,OAAO,CAACH,MAAM,CAACW,YAAY,CAAC;QAChC,CAAC,MACI;UACDP,MAAM,CAAC,IAAIR,UAAU,CAAC,CAAC,CAAC;QAC5B;MACJ;IACJ,CAAC,CAAC;IACFG,MAAM,CAACa,SAAS,CAACP,UAAU,CAAC;EAChC,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}