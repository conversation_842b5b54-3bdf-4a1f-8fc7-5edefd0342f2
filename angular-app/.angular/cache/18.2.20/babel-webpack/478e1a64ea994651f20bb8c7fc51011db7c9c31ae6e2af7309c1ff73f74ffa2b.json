{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { noop } from '../util/noop';\nexport function skipUntil(notifier) {\n  return operate((source, subscriber) => {\n    let taking = false;\n    const skipSubscriber = createOperatorSubscriber(subscriber, () => {\n      skipSubscriber === null || skipSubscriber === void 0 ? void 0 : skipSubscriber.unsubscribe();\n      taking = true;\n    }, noop);\n    innerFrom(notifier).subscribe(skipSubscriber);\n    source.subscribe(createOperatorSubscriber(subscriber, value => taking && subscriber.next(value)));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "innerFrom", "noop", "<PERSON><PERSON><PERSON><PERSON>", "notifier", "source", "subscriber", "taking", "skipSubscriber", "unsubscribe", "subscribe", "value", "next"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/rxjs/dist/esm/internal/operators/skipUntil.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { noop } from '../util/noop';\nexport function skipUntil(notifier) {\n    return operate((source, subscriber) => {\n        let taking = false;\n        const skipSubscriber = createOperatorSubscriber(subscriber, () => {\n            skipSubscriber === null || skipSubscriber === void 0 ? void 0 : skipSubscriber.unsubscribe();\n            taking = true;\n        }, noop);\n        innerFrom(notifier).subscribe(skipSubscriber);\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => taking && subscriber.next(value)));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,IAAI,QAAQ,cAAc;AACnC,OAAO,SAASC,SAASA,CAACC,QAAQ,EAAE;EAChC,OAAOL,OAAO,CAAC,CAACM,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,MAAM,GAAG,KAAK;IAClB,MAAMC,cAAc,GAAGR,wBAAwB,CAACM,UAAU,EAAE,MAAM;MAC9DE,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACC,WAAW,CAAC,CAAC;MAC5FF,MAAM,GAAG,IAAI;IACjB,CAAC,EAAEL,IAAI,CAAC;IACRD,SAAS,CAACG,QAAQ,CAAC,CAACM,SAAS,CAACF,cAAc,CAAC;IAC7CH,MAAM,CAACK,SAAS,CAACV,wBAAwB,CAACM,UAAU,EAAGK,KAAK,IAAKJ,MAAM,IAAID,UAAU,CAACM,IAAI,CAACD,KAAK,CAAC,CAAC,CAAC;EACvG,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}