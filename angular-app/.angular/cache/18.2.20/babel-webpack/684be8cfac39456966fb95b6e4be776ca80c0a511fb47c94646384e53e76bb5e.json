{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nconst segmentViewIosCss = \":host{display:-ms-flexbox;display:flex;height:100%;overflow-x:scroll;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;scrollbar-width:none;-ms-overflow-style:none}:host::-webkit-scrollbar{display:none}:host(.segment-view-disabled){-ms-touch-action:none;touch-action:none;overflow-x:hidden}:host(.segment-view-scroll-disabled){pointer-events:none}:host(.segment-view-disabled){opacity:0.3}\";\nconst segmentViewMdCss = \":host{display:-ms-flexbox;display:flex;height:100%;overflow-x:scroll;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;scrollbar-width:none;-ms-overflow-style:none}:host::-webkit-scrollbar{display:none}:host(.segment-view-disabled){-ms-touch-action:none;touch-action:none;overflow-x:hidden}:host(.segment-view-scroll-disabled){pointer-events:none}:host(.segment-view-disabled){opacity:0.3}\";\nconst SegmentView = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionSegmentViewScroll = createEvent(this, \"ionSegmentViewScroll\", 7);\n    this.scrollEndTimeout = null;\n    this.isTouching = false;\n    /**\n     * If `true`, the segment view cannot be interacted with.\n     */\n    this.disabled = false;\n  }\n  handleScroll(ev) {\n    var _a;\n    const {\n      scrollLeft,\n      scrollWidth,\n      clientWidth\n    } = ev.target;\n    const scrollRatio = scrollLeft / (scrollWidth - clientWidth);\n    this.ionSegmentViewScroll.emit({\n      scrollRatio,\n      isManualScroll: (_a = this.isManualScroll) !== null && _a !== void 0 ? _a : true\n    });\n    // Reset the timeout to check for scroll end\n    this.resetScrollEndTimeout();\n  }\n  /**\n   * Handle touch start event to know when the user is actively dragging the segment view.\n   */\n  handleScrollStart() {\n    if (this.scrollEndTimeout) {\n      clearTimeout(this.scrollEndTimeout);\n      this.scrollEndTimeout = null;\n    }\n    this.isTouching = true;\n  }\n  /**\n   * Handle touch end event to know when the user is no longer dragging the segment view.\n   */\n  handleTouchEnd() {\n    this.isTouching = false;\n  }\n  /**\n   * Reset the scroll end detection timer. This is called on every scroll event.\n   */\n  resetScrollEndTimeout() {\n    if (this.scrollEndTimeout) {\n      clearTimeout(this.scrollEndTimeout);\n      this.scrollEndTimeout = null;\n    }\n    this.scrollEndTimeout = setTimeout(() => {\n      this.checkForScrollEnd();\n    },\n    // Setting this to a lower value may result in inconsistencies in behavior\n    // across browsers (particularly Firefox).\n    // Ideally, all of this logic is removed once the scroll end event is\n    // supported on all browsers (https://caniuse.com/?search=scrollend)\n    100);\n  }\n  /**\n   * Check if the scroll has ended and the user is not actively touching.\n   * If the conditions are met (active content is enabled and no active touch),\n   * reset the scroll position and emit the scroll end event.\n   */\n  checkForScrollEnd() {\n    // Only emit scroll end event if the active content is not disabled and\n    // the user is not touching the segment view\n    if (!this.isTouching) {\n      this.isManualScroll = undefined;\n    }\n  }\n  /**\n   * @internal\n   *\n   * This method is used to programmatically set the displayed segment content\n   * in the segment view. Calling this method will update the `value` of the\n   * corresponding segment button.\n   *\n   * @param id: The id of the segment content to display.\n   * @param smoothScroll: Whether to animate the scroll transition.\n   */\n  setContent(_x) {\n    var _this = this;\n    return _asyncToGenerator(function* (id, smoothScroll = true) {\n      const contents = _this.getSegmentContents();\n      const index = contents.findIndex(content => content.id === id);\n      if (index === -1) return;\n      _this.isManualScroll = false;\n      _this.resetScrollEndTimeout();\n      const contentWidth = _this.el.offsetWidth;\n      _this.el.scrollTo({\n        top: 0,\n        left: index * contentWidth,\n        behavior: smoothScroll ? 'smooth' : 'instant'\n      });\n    }).apply(this, arguments);\n  }\n  getSegmentContents() {\n    return Array.from(this.el.querySelectorAll('ion-segment-content'));\n  }\n  render() {\n    const {\n      disabled,\n      isManualScroll\n    } = this;\n    return h(Host, {\n      key: '754a374e89fd4dd682eb00497e717242a6f83357',\n      class: {\n        'segment-view-disabled': disabled,\n        'segment-view-scroll-disabled': isManualScroll === false\n      }\n    }, h(\"slot\", {\n      key: '77366044eb61f0d4bba305bd6f0ef8fd1e25194b'\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nSegmentView.style = {\n  ios: segmentViewIosCss,\n  md: segmentViewMdCss\n};\nexport { SegmentView as ion_segment_view };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "h", "j", "Host", "k", "getElement", "segmentViewIosCss", "segmentViewMdCss", "SegmentView", "constructor", "hostRef", "ionSegmentViewScroll", "scrollEndTimeout", "isTouching", "disabled", "handleScroll", "ev", "_a", "scrollLeft", "scrollWidth", "clientWidth", "target", "scrollRatio", "emit", "isManualScroll", "resetScrollEndTimeout", "handleScrollStart", "clearTimeout", "handleTouchEnd", "setTimeout", "checkForScrollEnd", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "_x", "_this", "_asyncToGenerator", "id", "smoothScroll", "contents", "getSegmentContents", "index", "findIndex", "content", "contentWidth", "el", "offsetWidth", "scrollTo", "top", "left", "behavior", "apply", "arguments", "Array", "from", "querySelectorAll", "render", "key", "class", "style", "ios", "md", "ion_segment_view"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@ionic/core/dist/esm/ion-segment-view.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\n\nconst segmentViewIosCss = \":host{display:-ms-flexbox;display:flex;height:100%;overflow-x:scroll;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;scrollbar-width:none;-ms-overflow-style:none}:host::-webkit-scrollbar{display:none}:host(.segment-view-disabled){-ms-touch-action:none;touch-action:none;overflow-x:hidden}:host(.segment-view-scroll-disabled){pointer-events:none}:host(.segment-view-disabled){opacity:0.3}\";\n\nconst segmentViewMdCss = \":host{display:-ms-flexbox;display:flex;height:100%;overflow-x:scroll;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;scrollbar-width:none;-ms-overflow-style:none}:host::-webkit-scrollbar{display:none}:host(.segment-view-disabled){-ms-touch-action:none;touch-action:none;overflow-x:hidden}:host(.segment-view-scroll-disabled){pointer-events:none}:host(.segment-view-disabled){opacity:0.3}\";\n\nconst SegmentView = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionSegmentViewScroll = createEvent(this, \"ionSegmentViewScroll\", 7);\n        this.scrollEndTimeout = null;\n        this.isTouching = false;\n        /**\n         * If `true`, the segment view cannot be interacted with.\n         */\n        this.disabled = false;\n    }\n    handleScroll(ev) {\n        var _a;\n        const { scrollLeft, scrollWidth, clientWidth } = ev.target;\n        const scrollRatio = scrollLeft / (scrollWidth - clientWidth);\n        this.ionSegmentViewScroll.emit({\n            scrollRatio,\n            isManualScroll: (_a = this.isManualScroll) !== null && _a !== void 0 ? _a : true,\n        });\n        // Reset the timeout to check for scroll end\n        this.resetScrollEndTimeout();\n    }\n    /**\n     * Handle touch start event to know when the user is actively dragging the segment view.\n     */\n    handleScrollStart() {\n        if (this.scrollEndTimeout) {\n            clearTimeout(this.scrollEndTimeout);\n            this.scrollEndTimeout = null;\n        }\n        this.isTouching = true;\n    }\n    /**\n     * Handle touch end event to know when the user is no longer dragging the segment view.\n     */\n    handleTouchEnd() {\n        this.isTouching = false;\n    }\n    /**\n     * Reset the scroll end detection timer. This is called on every scroll event.\n     */\n    resetScrollEndTimeout() {\n        if (this.scrollEndTimeout) {\n            clearTimeout(this.scrollEndTimeout);\n            this.scrollEndTimeout = null;\n        }\n        this.scrollEndTimeout = setTimeout(() => {\n            this.checkForScrollEnd();\n        }, \n        // Setting this to a lower value may result in inconsistencies in behavior\n        // across browsers (particularly Firefox).\n        // Ideally, all of this logic is removed once the scroll end event is\n        // supported on all browsers (https://caniuse.com/?search=scrollend)\n        100);\n    }\n    /**\n     * Check if the scroll has ended and the user is not actively touching.\n     * If the conditions are met (active content is enabled and no active touch),\n     * reset the scroll position and emit the scroll end event.\n     */\n    checkForScrollEnd() {\n        // Only emit scroll end event if the active content is not disabled and\n        // the user is not touching the segment view\n        if (!this.isTouching) {\n            this.isManualScroll = undefined;\n        }\n    }\n    /**\n     * @internal\n     *\n     * This method is used to programmatically set the displayed segment content\n     * in the segment view. Calling this method will update the `value` of the\n     * corresponding segment button.\n     *\n     * @param id: The id of the segment content to display.\n     * @param smoothScroll: Whether to animate the scroll transition.\n     */\n    async setContent(id, smoothScroll = true) {\n        const contents = this.getSegmentContents();\n        const index = contents.findIndex((content) => content.id === id);\n        if (index === -1)\n            return;\n        this.isManualScroll = false;\n        this.resetScrollEndTimeout();\n        const contentWidth = this.el.offsetWidth;\n        this.el.scrollTo({\n            top: 0,\n            left: index * contentWidth,\n            behavior: smoothScroll ? 'smooth' : 'instant',\n        });\n    }\n    getSegmentContents() {\n        return Array.from(this.el.querySelectorAll('ion-segment-content'));\n    }\n    render() {\n        const { disabled, isManualScroll } = this;\n        return (h(Host, { key: '754a374e89fd4dd682eb00497e717242a6f83357', class: {\n                'segment-view-disabled': disabled,\n                'segment-view-scroll-disabled': isManualScroll === false,\n            } }, h(\"slot\", { key: '77366044eb61f0d4bba305bd6f0ef8fd1e25194b' })));\n    }\n    get el() { return getElement(this); }\n};\nSegmentView.style = {\n    ios: segmentViewIosCss,\n    md: segmentViewMdCss\n};\n\nexport { SegmentView as ion_segment_view };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAE5G,MAAMC,iBAAiB,GAAG,wbAAwb;AAEld,MAAMC,gBAAgB,GAAG,wbAAwb;AAEjd,MAAMC,WAAW,GAAG,MAAM;EACtBC,WAAWA,CAACC,OAAO,EAAE;IACjBZ,gBAAgB,CAAC,IAAI,EAAEY,OAAO,CAAC;IAC/B,IAAI,CAACC,oBAAoB,GAAGX,WAAW,CAAC,IAAI,EAAE,sBAAsB,EAAE,CAAC,CAAC;IACxE,IAAI,CAACY,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;EACzB;EACAC,YAAYA,CAACC,EAAE,EAAE;IACb,IAAIC,EAAE;IACN,MAAM;MAAEC,UAAU;MAAEC,WAAW;MAAEC;IAAY,CAAC,GAAGJ,EAAE,CAACK,MAAM;IAC1D,MAAMC,WAAW,GAAGJ,UAAU,IAAIC,WAAW,GAAGC,WAAW,CAAC;IAC5D,IAAI,CAACT,oBAAoB,CAACY,IAAI,CAAC;MAC3BD,WAAW;MACXE,cAAc,EAAE,CAACP,EAAE,GAAG,IAAI,CAACO,cAAc,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;IAChF,CAAC,CAAC;IACF;IACA,IAAI,CAACQ,qBAAqB,CAAC,CAAC;EAChC;EACA;AACJ;AACA;EACIC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACd,gBAAgB,EAAE;MACvBe,YAAY,CAAC,IAAI,CAACf,gBAAgB,CAAC;MACnC,IAAI,CAACA,gBAAgB,GAAG,IAAI;IAChC;IACA,IAAI,CAACC,UAAU,GAAG,IAAI;EAC1B;EACA;AACJ;AACA;EACIe,cAAcA,CAAA,EAAG;IACb,IAAI,CAACf,UAAU,GAAG,KAAK;EAC3B;EACA;AACJ;AACA;EACIY,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACb,gBAAgB,EAAE;MACvBe,YAAY,CAAC,IAAI,CAACf,gBAAgB,CAAC;MACnC,IAAI,CAACA,gBAAgB,GAAG,IAAI;IAChC;IACA,IAAI,CAACA,gBAAgB,GAAGiB,UAAU,CAAC,MAAM;MACrC,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC5B,CAAC;IACD;IACA;IACA;IACA;IACA,GAAG,CAAC;EACR;EACA;AACJ;AACA;AACA;AACA;EACIA,iBAAiBA,CAAA,EAAG;IAChB;IACA;IACA,IAAI,CAAC,IAAI,CAACjB,UAAU,EAAE;MAClB,IAAI,CAACW,cAAc,GAAGO,SAAS;IACnC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUC,UAAUA,CAAAC,EAAA,EAA0B;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,YAAzBC,EAAE,EAAEC,YAAY,GAAG,IAAI;MACpC,MAAMC,QAAQ,GAAGJ,KAAI,CAACK,kBAAkB,CAAC,CAAC;MAC1C,MAAMC,KAAK,GAAGF,QAAQ,CAACG,SAAS,CAAEC,OAAO,IAAKA,OAAO,CAACN,EAAE,KAAKA,EAAE,CAAC;MAChE,IAAII,KAAK,KAAK,CAAC,CAAC,EACZ;MACJN,KAAI,CAACV,cAAc,GAAG,KAAK;MAC3BU,KAAI,CAACT,qBAAqB,CAAC,CAAC;MAC5B,MAAMkB,YAAY,GAAGT,KAAI,CAACU,EAAE,CAACC,WAAW;MACxCX,KAAI,CAACU,EAAE,CAACE,QAAQ,CAAC;QACbC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAER,KAAK,GAAGG,YAAY;QAC1BM,QAAQ,EAAEZ,YAAY,GAAG,QAAQ,GAAG;MACxC,CAAC,CAAC;IAAC,GAAAa,KAAA,OAAAC,SAAA;EACP;EACAZ,kBAAkBA,CAAA,EAAG;IACjB,OAAOa,KAAK,CAACC,IAAI,CAAC,IAAI,CAACT,EAAE,CAACU,gBAAgB,CAAC,qBAAqB,CAAC,CAAC;EACtE;EACAC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEzC,QAAQ;MAAEU;IAAe,CAAC,GAAG,IAAI;IACzC,OAAQvB,CAAC,CAACE,IAAI,EAAE;MAAEqD,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,uBAAuB,EAAE3C,QAAQ;QACjC,8BAA8B,EAAEU,cAAc,KAAK;MACvD;IAAE,CAAC,EAAEvB,CAAC,CAAC,MAAM,EAAE;MAAEuD,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC5E;EACA,IAAIZ,EAAEA,CAAA,EAAG;IAAE,OAAOvC,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDG,WAAW,CAACkD,KAAK,GAAG;EAChBC,GAAG,EAAErD,iBAAiB;EACtBsD,EAAE,EAAErD;AACR,CAAC;AAED,SAASC,WAAW,IAAIqD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}