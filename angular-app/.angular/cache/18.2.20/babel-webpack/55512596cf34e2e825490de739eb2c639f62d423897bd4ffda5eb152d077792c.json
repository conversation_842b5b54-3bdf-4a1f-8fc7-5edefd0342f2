{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, InjectionToken, Inject, Directive, Input, Pipe, NgModule } from '@angular/core';\nimport { of, isObservable, forkJoin, concat, defer } from 'rxjs';\nimport { take, shareReplay, map, concatMap, switchMap } from 'rxjs/operators';\nclass TranslateLoader {}\n/**\n * This loader is just a placeholder that does nothing, in case you don't need a loader at all\n */\nclass TranslateFakeLoader extends TranslateLoader {\n  getTranslation(lang) {\n    return of({});\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵTranslateFakeLoader_BaseFactory;\n    return function TranslateFakeLoader_Factory(__ngFactoryType__) {\n      return (ɵTranslateFakeLoader_BaseFactory || (ɵTranslateFakeLoader_BaseFactory = i0.ɵɵgetInheritedFactory(TranslateFakeLoader)))(__ngFactoryType__ || TranslateFakeLoader);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TranslateFakeLoader,\n    factory: TranslateFakeLoader.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateFakeLoader, [{\n    type: Injectable\n  }], null, null);\n})();\nclass MissingTranslationHandler {}\n/**\n * This handler is just a placeholder that does nothing, in case you don't need a missing translation handler at all\n */\nclass FakeMissingTranslationHandler {\n  handle(params) {\n    return params.key;\n  }\n  static ɵfac = function FakeMissingTranslationHandler_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FakeMissingTranslationHandler)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FakeMissingTranslationHandler,\n    factory: FakeMissingTranslationHandler.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FakeMissingTranslationHandler, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/* tslint:disable */\n/**\n * Determines if two objects or two values are equivalent.\n *\n * Two objects or values are considered equivalent if at least one of the following is true:\n *\n * * Both objects or values pass `===` comparison.\n * * Both objects or values are of the same type and all of their properties are equal by\n *   comparing them with `equals`.\n *\n * @param o1 Object or value to compare.\n * @param o2 Object or value to compare.\n * @returns true if arguments are equal.\n */\nfunction equals(o1, o2) {\n  if (o1 === o2) return true;\n  if (o1 === null || o2 === null) return false;\n  if (o1 !== o1 && o2 !== o2) return true; // NaN === NaN\n  let t1 = typeof o1,\n    t2 = typeof o2,\n    length,\n    key,\n    keySet;\n  if (t1 == t2 && t1 == 'object') {\n    if (Array.isArray(o1)) {\n      if (!Array.isArray(o2)) return false;\n      if ((length = o1.length) == o2.length) {\n        for (key = 0; key < length; key++) {\n          if (!equals(o1[key], o2[key])) return false;\n        }\n        return true;\n      }\n    } else {\n      if (Array.isArray(o2)) {\n        return false;\n      }\n      keySet = Object.create(null);\n      for (key in o1) {\n        if (!equals(o1[key], o2[key])) {\n          return false;\n        }\n        keySet[key] = true;\n      }\n      for (key in o2) {\n        if (!(key in keySet) && typeof o2[key] !== 'undefined') {\n          return false;\n        }\n      }\n      return true;\n    }\n  }\n  return false;\n}\n/* tslint:enable */\nfunction isDefined(value) {\n  return typeof value !== 'undefined' && value !== null;\n}\nfunction isObject(item) {\n  return item && typeof item === 'object' && !Array.isArray(item);\n}\nfunction mergeDeep(target, source) {\n  let output = Object.assign({}, target);\n  if (isObject(target) && isObject(source)) {\n    Object.keys(source).forEach(key => {\n      if (isObject(source[key])) {\n        if (!(key in target)) {\n          Object.assign(output, {\n            [key]: source[key]\n          });\n        } else {\n          output[key] = mergeDeep(target[key], source[key]);\n        }\n      } else {\n        Object.assign(output, {\n          [key]: source[key]\n        });\n      }\n    });\n  }\n  return output;\n}\nclass TranslateParser {}\nclass TranslateDefaultParser extends TranslateParser {\n  templateMatcher = /{{\\s?([^{}\\s]*)\\s?}}/g;\n  interpolate(expr, params) {\n    let result;\n    if (typeof expr === 'string') {\n      result = this.interpolateString(expr, params);\n    } else if (typeof expr === 'function') {\n      result = this.interpolateFunction(expr, params);\n    } else {\n      // this should not happen, but an unrelated TranslateService test depends on it\n      result = expr;\n    }\n    return result;\n  }\n  getValue(target, key) {\n    let keys = typeof key === 'string' ? key.split('.') : [key];\n    key = '';\n    do {\n      key += keys.shift();\n      if (isDefined(target) && isDefined(target[key]) && (typeof target[key] === 'object' || !keys.length)) {\n        target = target[key];\n        key = '';\n      } else if (!keys.length) {\n        target = undefined;\n      } else {\n        key += '.';\n      }\n    } while (keys.length);\n    return target;\n  }\n  interpolateFunction(fn, params) {\n    return fn(params);\n  }\n  interpolateString(expr, params) {\n    if (!params) {\n      return expr;\n    }\n    return expr.replace(this.templateMatcher, (substring, b) => {\n      let r = this.getValue(params, b);\n      return isDefined(r) ? r : substring;\n    });\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵTranslateDefaultParser_BaseFactory;\n    return function TranslateDefaultParser_Factory(__ngFactoryType__) {\n      return (ɵTranslateDefaultParser_BaseFactory || (ɵTranslateDefaultParser_BaseFactory = i0.ɵɵgetInheritedFactory(TranslateDefaultParser)))(__ngFactoryType__ || TranslateDefaultParser);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TranslateDefaultParser,\n    factory: TranslateDefaultParser.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateDefaultParser, [{\n    type: Injectable\n  }], null, null);\n})();\nclass TranslateCompiler {}\n/**\n * This compiler is just a placeholder that does nothing, in case you don't need a compiler at all\n */\nclass TranslateFakeCompiler extends TranslateCompiler {\n  compile(value, lang) {\n    return value;\n  }\n  compileTranslations(translations, lang) {\n    return translations;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵTranslateFakeCompiler_BaseFactory;\n    return function TranslateFakeCompiler_Factory(__ngFactoryType__) {\n      return (ɵTranslateFakeCompiler_BaseFactory || (ɵTranslateFakeCompiler_BaseFactory = i0.ɵɵgetInheritedFactory(TranslateFakeCompiler)))(__ngFactoryType__ || TranslateFakeCompiler);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TranslateFakeCompiler,\n    factory: TranslateFakeCompiler.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateFakeCompiler, [{\n    type: Injectable\n  }], null, null);\n})();\nclass TranslateStore {\n  /**\n   * The default lang to fallback when translations are missing on the current lang\n   */\n  defaultLang;\n  /**\n   * The lang currently used\n   */\n  currentLang = this.defaultLang;\n  /**\n   * a list of translations per lang\n   */\n  translations = {};\n  /**\n   * an array of langs\n   */\n  langs = [];\n  /**\n   * An EventEmitter to listen to translation change events\n   * onTranslationChange.subscribe((params: TranslationChangeEvent) => {\n     *     // do something\n     * });\n   */\n  onTranslationChange = new EventEmitter();\n  /**\n   * An EventEmitter to listen to lang change events\n   * onLangChange.subscribe((params: LangChangeEvent) => {\n     *     // do something\n     * });\n   */\n  onLangChange = new EventEmitter();\n  /**\n   * An EventEmitter to listen to default lang change events\n   * onDefaultLangChange.subscribe((params: DefaultLangChangeEvent) => {\n     *     // do something\n     * });\n   */\n  onDefaultLangChange = new EventEmitter();\n}\nconst USE_STORE = new InjectionToken('USE_STORE');\nconst USE_DEFAULT_LANG = new InjectionToken('USE_DEFAULT_LANG');\nconst DEFAULT_LANGUAGE = new InjectionToken('DEFAULT_LANGUAGE');\nconst USE_EXTEND = new InjectionToken('USE_EXTEND');\nclass TranslateService {\n  store;\n  currentLoader;\n  compiler;\n  parser;\n  missingTranslationHandler;\n  useDefaultLang;\n  isolate;\n  extend;\n  loadingTranslations;\n  pending = false;\n  _onTranslationChange = new EventEmitter();\n  _onLangChange = new EventEmitter();\n  _onDefaultLangChange = new EventEmitter();\n  _defaultLang;\n  _currentLang;\n  _langs = [];\n  _translations = {};\n  _translationRequests = {};\n  /**\n   * An EventEmitter to listen to translation change events\n   * onTranslationChange.subscribe((params: TranslationChangeEvent) => {\n     *     // do something\n     * });\n   */\n  get onTranslationChange() {\n    return this.isolate ? this._onTranslationChange : this.store.onTranslationChange;\n  }\n  /**\n   * An EventEmitter to listen to lang change events\n   * onLangChange.subscribe((params: LangChangeEvent) => {\n     *     // do something\n     * });\n   */\n  get onLangChange() {\n    return this.isolate ? this._onLangChange : this.store.onLangChange;\n  }\n  /**\n   * An EventEmitter to listen to default lang change events\n   * onDefaultLangChange.subscribe((params: DefaultLangChangeEvent) => {\n     *     // do something\n     * });\n   */\n  get onDefaultLangChange() {\n    return this.isolate ? this._onDefaultLangChange : this.store.onDefaultLangChange;\n  }\n  /**\n   * The default lang to fallback when translations are missing on the current lang\n   */\n  get defaultLang() {\n    return this.isolate ? this._defaultLang : this.store.defaultLang;\n  }\n  set defaultLang(defaultLang) {\n    if (this.isolate) {\n      this._defaultLang = defaultLang;\n    } else {\n      this.store.defaultLang = defaultLang;\n    }\n  }\n  /**\n   * The lang currently used\n   */\n  get currentLang() {\n    return this.isolate ? this._currentLang : this.store.currentLang;\n  }\n  set currentLang(currentLang) {\n    if (this.isolate) {\n      this._currentLang = currentLang;\n    } else {\n      this.store.currentLang = currentLang;\n    }\n  }\n  /**\n   * an array of langs\n   */\n  get langs() {\n    return this.isolate ? this._langs : this.store.langs;\n  }\n  set langs(langs) {\n    if (this.isolate) {\n      this._langs = langs;\n    } else {\n      this.store.langs = langs;\n    }\n  }\n  /**\n   * a list of translations per lang\n   */\n  get translations() {\n    return this.isolate ? this._translations : this.store.translations;\n  }\n  set translations(translations) {\n    if (this.isolate) {\n      this._translations = translations;\n    } else {\n      this.store.translations = translations;\n    }\n  }\n  /**\n   *\n   * @param store an instance of the store (that is supposed to be unique)\n   * @param currentLoader An instance of the loader currently used\n   * @param compiler An instance of the compiler currently used\n   * @param parser An instance of the parser currently used\n   * @param missingTranslationHandler A handler for missing translations.\n   * @param useDefaultLang whether we should use default language translation when current language translation is missing.\n   * @param isolate whether this service should use the store or not\n   * @param extend To make a child module extend (and use) translations from parent modules.\n   * @param defaultLanguage Set the default language using configuration\n   */\n  constructor(store, currentLoader, compiler, parser, missingTranslationHandler, useDefaultLang = true, isolate = false, extend = false, defaultLanguage) {\n    this.store = store;\n    this.currentLoader = currentLoader;\n    this.compiler = compiler;\n    this.parser = parser;\n    this.missingTranslationHandler = missingTranslationHandler;\n    this.useDefaultLang = useDefaultLang;\n    this.isolate = isolate;\n    this.extend = extend;\n    /** set the default language from configuration */\n    if (defaultLanguage) {\n      this.setDefaultLang(defaultLanguage);\n    }\n  }\n  /**\n   * Sets the default language to use as a fallback\n   */\n  setDefaultLang(lang) {\n    if (lang === this.defaultLang) {\n      return;\n    }\n    let pending = this.retrieveTranslations(lang);\n    if (typeof pending !== \"undefined\") {\n      // on init set the defaultLang immediately\n      if (this.defaultLang == null) {\n        this.defaultLang = lang;\n      }\n      pending.pipe(take(1)).subscribe(res => {\n        this.changeDefaultLang(lang);\n      });\n    } else {\n      // we already have this language\n      this.changeDefaultLang(lang);\n    }\n  }\n  /**\n   * Gets the default language used\n   */\n  getDefaultLang() {\n    return this.defaultLang;\n  }\n  /**\n   * Changes the lang currently used\n   */\n  use(lang) {\n    // don't change the language if the language given is already selected\n    if (lang === this.currentLang) {\n      return of(this.translations[lang]);\n    }\n    let pending = this.retrieveTranslations(lang);\n    if (typeof pending !== \"undefined\") {\n      // on init set the currentLang immediately\n      if (!this.currentLang) {\n        this.currentLang = lang;\n      }\n      pending.pipe(take(1)).subscribe(res => {\n        this.changeLang(lang);\n      });\n      return pending;\n    } else {\n      // we have this language, return an Observable\n      this.changeLang(lang);\n      return of(this.translations[lang]);\n    }\n  }\n  /**\n   * Retrieves the given translations\n   */\n  retrieveTranslations(lang) {\n    let pending;\n    // if this language is unavailable or extend is true, ask for it\n    if (typeof this.translations[lang] === \"undefined\" || this.extend) {\n      this._translationRequests[lang] = this._translationRequests[lang] || this.getTranslation(lang);\n      pending = this._translationRequests[lang];\n    }\n    return pending;\n  }\n  /**\n   * Gets an object of translations for a given language with the current loader\n   * and passes it through the compiler\n   */\n  getTranslation(lang) {\n    this.pending = true;\n    const loadingTranslations = this.currentLoader.getTranslation(lang).pipe(shareReplay(1), take(1));\n    this.loadingTranslations = loadingTranslations.pipe(map(res => this.compiler.compileTranslations(res, lang)), shareReplay(1), take(1));\n    this.loadingTranslations.subscribe({\n      next: res => {\n        this.translations[lang] = this.extend && this.translations[lang] ? {\n          ...res,\n          ...this.translations[lang]\n        } : res;\n        this.updateLangs();\n        this.pending = false;\n      },\n      error: err => {\n        this.pending = false;\n      }\n    });\n    return loadingTranslations;\n  }\n  /**\n   * Manually sets an object of translations for a given language\n   * after passing it through the compiler\n   */\n  setTranslation(lang, translations, shouldMerge = false) {\n    translations = this.compiler.compileTranslations(translations, lang);\n    if ((shouldMerge || this.extend) && this.translations[lang]) {\n      this.translations[lang] = mergeDeep(this.translations[lang], translations);\n    } else {\n      this.translations[lang] = translations;\n    }\n    this.updateLangs();\n    this.onTranslationChange.emit({\n      lang: lang,\n      translations: this.translations[lang]\n    });\n  }\n  /**\n   * Returns an array of currently available langs\n   */\n  getLangs() {\n    return this.langs;\n  }\n  /**\n   * Add available langs\n   */\n  addLangs(langs) {\n    langs.forEach(lang => {\n      if (this.langs.indexOf(lang) === -1) {\n        this.langs.push(lang);\n      }\n    });\n  }\n  /**\n   * Update the list of available langs\n   */\n  updateLangs() {\n    this.addLangs(Object.keys(this.translations));\n  }\n  /**\n   * Returns the parsed result of the translations\n   */\n  getParsedResult(translations, key, interpolateParams) {\n    let res;\n    if (key instanceof Array) {\n      let result = {},\n        observables = false;\n      for (let k of key) {\n        result[k] = this.getParsedResult(translations, k, interpolateParams);\n        if (isObservable(result[k])) {\n          observables = true;\n        }\n      }\n      if (observables) {\n        const sources = key.map(k => isObservable(result[k]) ? result[k] : of(result[k]));\n        return forkJoin(sources).pipe(map(arr => {\n          let obj = {};\n          arr.forEach((value, index) => {\n            obj[key[index]] = value;\n          });\n          return obj;\n        }));\n      }\n      return result;\n    }\n    if (translations) {\n      res = this.parser.interpolate(this.parser.getValue(translations, key), interpolateParams);\n    }\n    if (typeof res === \"undefined\" && this.defaultLang != null && this.defaultLang !== this.currentLang && this.useDefaultLang) {\n      res = this.parser.interpolate(this.parser.getValue(this.translations[this.defaultLang], key), interpolateParams);\n    }\n    if (typeof res === \"undefined\") {\n      let params = {\n        key,\n        translateService: this\n      };\n      if (typeof interpolateParams !== 'undefined') {\n        params.interpolateParams = interpolateParams;\n      }\n      res = this.missingTranslationHandler.handle(params);\n    }\n    return typeof res !== \"undefined\" ? res : key;\n  }\n  /**\n   * Gets the translated value of a key (or an array of keys)\n   * @returns the translated key, or an object of translated keys\n   */\n  get(key, interpolateParams) {\n    if (!isDefined(key) || !key.length) {\n      throw new Error(`Parameter \"key\" required`);\n    }\n    // check if we are loading a new translation to use\n    if (this.pending) {\n      return this.loadingTranslations.pipe(concatMap(res => {\n        res = this.getParsedResult(res, key, interpolateParams);\n        return isObservable(res) ? res : of(res);\n      }));\n    } else {\n      let res = this.getParsedResult(this.translations[this.currentLang], key, interpolateParams);\n      return isObservable(res) ? res : of(res);\n    }\n  }\n  /**\n   * Returns a stream of translated values of a key (or an array of keys) which updates\n   * whenever the translation changes.\n   * @returns A stream of the translated key, or an object of translated keys\n   */\n  getStreamOnTranslationChange(key, interpolateParams) {\n    if (!isDefined(key) || !key.length) {\n      throw new Error(`Parameter \"key\" required`);\n    }\n    return concat(defer(() => this.get(key, interpolateParams)), this.onTranslationChange.pipe(switchMap(event => {\n      const res = this.getParsedResult(event.translations, key, interpolateParams);\n      if (typeof res.subscribe === 'function') {\n        return res;\n      } else {\n        return of(res);\n      }\n    })));\n  }\n  /**\n   * Returns a stream of translated values of a key (or an array of keys) which updates\n   * whenever the language changes.\n   * @returns A stream of the translated key, or an object of translated keys\n   */\n  stream(key, interpolateParams) {\n    if (!isDefined(key) || !key.length) {\n      throw new Error(`Parameter \"key\" required`);\n    }\n    return concat(defer(() => this.get(key, interpolateParams)), this.onLangChange.pipe(switchMap(event => {\n      const res = this.getParsedResult(event.translations, key, interpolateParams);\n      return isObservable(res) ? res : of(res);\n    })));\n  }\n  /**\n   * Returns a translation instantly from the internal state of loaded translation.\n   * All rules regarding the current language, the preferred language of even fallback languages will be used except any promise handling.\n   */\n  instant(key, interpolateParams) {\n    if (!isDefined(key) || !key.length) {\n      throw new Error(`Parameter \"key\" required`);\n    }\n    let res = this.getParsedResult(this.translations[this.currentLang], key, interpolateParams);\n    if (isObservable(res)) {\n      if (key instanceof Array) {\n        let obj = {};\n        key.forEach((value, index) => {\n          obj[key[index]] = key[index];\n        });\n        return obj;\n      }\n      return key;\n    } else {\n      return res;\n    }\n  }\n  /**\n   * Sets the translated value of a key, after compiling it\n   */\n  set(key, value, lang = this.currentLang) {\n    this.translations[lang][key] = this.compiler.compile(value, lang);\n    this.updateLangs();\n    this.onTranslationChange.emit({\n      lang: lang,\n      translations: this.translations[lang]\n    });\n  }\n  /**\n   * Changes the current lang\n   */\n  changeLang(lang) {\n    this.currentLang = lang;\n    this.onLangChange.emit({\n      lang: lang,\n      translations: this.translations[lang]\n    });\n    // if there is no default lang, use the one that we just set\n    if (this.defaultLang == null) {\n      this.changeDefaultLang(lang);\n    }\n  }\n  /**\n   * Changes the default lang\n   */\n  changeDefaultLang(lang) {\n    this.defaultLang = lang;\n    this.onDefaultLangChange.emit({\n      lang: lang,\n      translations: this.translations[lang]\n    });\n  }\n  /**\n   * Allows to reload the lang file from the file\n   */\n  reloadLang(lang) {\n    this.resetLang(lang);\n    return this.getTranslation(lang);\n  }\n  /**\n   * Deletes inner translation\n   */\n  resetLang(lang) {\n    this._translationRequests[lang] = undefined;\n    this.translations[lang] = undefined;\n  }\n  /**\n   * Returns the language code name from the browser, e.g. \"de\"\n   */\n  getBrowserLang() {\n    if (typeof window === 'undefined' || typeof window.navigator === 'undefined') {\n      return undefined;\n    }\n    let browserLang = window.navigator.languages ? window.navigator.languages[0] : null;\n    browserLang = browserLang || window.navigator.language || window.navigator.browserLanguage || window.navigator.userLanguage;\n    if (typeof browserLang === 'undefined') {\n      return undefined;\n    }\n    if (browserLang.indexOf('-') !== -1) {\n      browserLang = browserLang.split('-')[0];\n    }\n    if (browserLang.indexOf('_') !== -1) {\n      browserLang = browserLang.split('_')[0];\n    }\n    return browserLang;\n  }\n  /**\n   * Returns the culture language code name from the browser, e.g. \"de-DE\"\n   */\n  getBrowserCultureLang() {\n    if (typeof window === 'undefined' || typeof window.navigator === 'undefined') {\n      return undefined;\n    }\n    let browserCultureLang = window.navigator.languages ? window.navigator.languages[0] : null;\n    browserCultureLang = browserCultureLang || window.navigator.language || window.navigator.browserLanguage || window.navigator.userLanguage;\n    return browserCultureLang;\n  }\n  static ɵfac = function TranslateService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TranslateService)(i0.ɵɵinject(TranslateStore), i0.ɵɵinject(TranslateLoader), i0.ɵɵinject(TranslateCompiler), i0.ɵɵinject(TranslateParser), i0.ɵɵinject(MissingTranslationHandler), i0.ɵɵinject(USE_DEFAULT_LANG), i0.ɵɵinject(USE_STORE), i0.ɵɵinject(USE_EXTEND), i0.ɵɵinject(DEFAULT_LANGUAGE));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TranslateService,\n    factory: TranslateService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: TranslateStore\n    }, {\n      type: TranslateLoader\n    }, {\n      type: TranslateCompiler\n    }, {\n      type: TranslateParser\n    }, {\n      type: MissingTranslationHandler\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [USE_DEFAULT_LANG]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [USE_STORE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [USE_EXTEND]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DEFAULT_LANGUAGE]\n      }]\n    }];\n  }, null);\n})();\nclass TranslateDirective {\n  translateService;\n  element;\n  _ref;\n  key;\n  lastParams;\n  currentParams;\n  onLangChangeSub;\n  onDefaultLangChangeSub;\n  onTranslationChangeSub;\n  set translate(key) {\n    if (key) {\n      this.key = key;\n      this.checkNodes();\n    }\n  }\n  set translateParams(params) {\n    if (!equals(this.currentParams, params)) {\n      this.currentParams = params;\n      this.checkNodes(true);\n    }\n  }\n  constructor(translateService, element, _ref) {\n    this.translateService = translateService;\n    this.element = element;\n    this._ref = _ref;\n    // subscribe to onTranslationChange event, in case the translations of the current lang change\n    if (!this.onTranslationChangeSub) {\n      this.onTranslationChangeSub = this.translateService.onTranslationChange.subscribe(event => {\n        if (event.lang === this.translateService.currentLang) {\n          this.checkNodes(true, event.translations);\n        }\n      });\n    }\n    // subscribe to onLangChange event, in case the language changes\n    if (!this.onLangChangeSub) {\n      this.onLangChangeSub = this.translateService.onLangChange.subscribe(event => {\n        this.checkNodes(true, event.translations);\n      });\n    }\n    // subscribe to onDefaultLangChange event, in case the default language changes\n    if (!this.onDefaultLangChangeSub) {\n      this.onDefaultLangChangeSub = this.translateService.onDefaultLangChange.subscribe(event => {\n        this.checkNodes(true);\n      });\n    }\n  }\n  ngAfterViewChecked() {\n    this.checkNodes();\n  }\n  checkNodes(forceUpdate = false, translations) {\n    let nodes = this.element.nativeElement.childNodes;\n    // if the element is empty\n    if (!nodes.length) {\n      // we add the key as content\n      this.setContent(this.element.nativeElement, this.key);\n      nodes = this.element.nativeElement.childNodes;\n    }\n    for (let i = 0; i < nodes.length; ++i) {\n      let node = nodes[i];\n      if (node.nodeType === 3) {\n        // node type 3 is a text node\n        let key;\n        if (forceUpdate) {\n          node.lastKey = null;\n        }\n        if (isDefined(node.lookupKey)) {\n          key = node.lookupKey;\n        } else if (this.key) {\n          key = this.key;\n        } else {\n          let content = this.getContent(node);\n          let trimmedContent = content.trim();\n          if (trimmedContent.length) {\n            node.lookupKey = trimmedContent;\n            // we want to use the content as a key, not the translation value\n            if (content !== node.currentValue) {\n              key = trimmedContent;\n              // the content was changed from the user, we'll use it as a reference if needed\n              node.originalContent = content || node.originalContent;\n            } else if (node.originalContent) {\n              // the content seems ok, but the lang has changed\n              // the current content is the translation, not the key, use the last real content as key\n              key = node.originalContent.trim();\n            } else if (content !== node.currentValue) {\n              // we want to use the content as a key, not the translation value\n              key = trimmedContent;\n              // the content was changed from the user, we'll use it as a reference if needed\n              node.originalContent = content || node.originalContent;\n            }\n          }\n        }\n        this.updateValue(key, node, translations);\n      }\n    }\n  }\n  updateValue(key, node, translations) {\n    if (key) {\n      if (node.lastKey === key && this.lastParams === this.currentParams) {\n        return;\n      }\n      this.lastParams = this.currentParams;\n      let onTranslation = res => {\n        if (res !== key) {\n          node.lastKey = key;\n        }\n        if (!node.originalContent) {\n          node.originalContent = this.getContent(node);\n        }\n        node.currentValue = isDefined(res) ? res : node.originalContent || key;\n        // we replace in the original content to preserve spaces that we might have trimmed\n        this.setContent(node, this.key ? node.currentValue : node.originalContent.replace(key, node.currentValue));\n        this._ref.markForCheck();\n      };\n      if (isDefined(translations)) {\n        let res = this.translateService.getParsedResult(translations, key, this.currentParams);\n        if (isObservable(res)) {\n          res.subscribe({\n            next: onTranslation\n          });\n        } else {\n          onTranslation(res);\n        }\n      } else {\n        this.translateService.get(key, this.currentParams).subscribe(onTranslation);\n      }\n    }\n  }\n  getContent(node) {\n    return isDefined(node.textContent) ? node.textContent : node.data;\n  }\n  setContent(node, content) {\n    if (isDefined(node.textContent)) {\n      node.textContent = content;\n    } else {\n      node.data = content;\n    }\n  }\n  ngOnDestroy() {\n    if (this.onLangChangeSub) {\n      this.onLangChangeSub.unsubscribe();\n    }\n    if (this.onDefaultLangChangeSub) {\n      this.onDefaultLangChangeSub.unsubscribe();\n    }\n    if (this.onTranslationChangeSub) {\n      this.onTranslationChangeSub.unsubscribe();\n    }\n  }\n  static ɵfac = function TranslateDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TranslateDirective)(i0.ɵɵdirectiveInject(TranslateService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: TranslateDirective,\n    selectors: [[\"\", \"translate\", \"\"], [\"\", \"ngx-translate\", \"\"]],\n    inputs: {\n      translate: \"translate\",\n      translateParams: \"translateParams\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[translate],[ngx-translate]'\n    }]\n  }], function () {\n    return [{\n      type: TranslateService\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    translate: [{\n      type: Input\n    }],\n    translateParams: [{\n      type: Input\n    }]\n  });\n})();\nclass TranslatePipe {\n  translate;\n  _ref;\n  value = '';\n  lastKey = null;\n  lastParams = [];\n  onTranslationChange;\n  onLangChange;\n  onDefaultLangChange;\n  constructor(translate, _ref) {\n    this.translate = translate;\n    this._ref = _ref;\n  }\n  updateValue(key, interpolateParams, translations) {\n    let onTranslation = res => {\n      this.value = res !== undefined ? res : key;\n      this.lastKey = key;\n      this._ref.markForCheck();\n    };\n    if (translations) {\n      let res = this.translate.getParsedResult(translations, key, interpolateParams);\n      if (isObservable(res.subscribe)) {\n        res.subscribe(onTranslation);\n      } else {\n        onTranslation(res);\n      }\n    }\n    this.translate.get(key, interpolateParams).subscribe(onTranslation);\n  }\n  transform(query, ...args) {\n    if (!query || !query.length) {\n      return query;\n    }\n    // if we ask another time for the same key, return the last value\n    if (equals(query, this.lastKey) && equals(args, this.lastParams)) {\n      return this.value;\n    }\n    let interpolateParams = undefined;\n    if (isDefined(args[0]) && args.length) {\n      if (typeof args[0] === 'string' && args[0].length) {\n        // we accept objects written in the template such as {n:1}, {'n':1}, {n:'v'}\n        // which is why we might need to change it to real JSON objects such as {\"n\":1} or {\"n\":\"v\"}\n        let validArgs = args[0].replace(/(\\')?([a-zA-Z0-9_]+)(\\')?(\\s)?:/g, '\"$2\":').replace(/:(\\s)?(\\')(.*?)(\\')/g, ':\"$3\"');\n        try {\n          interpolateParams = JSON.parse(validArgs);\n        } catch (e) {\n          throw new SyntaxError(`Wrong parameter in TranslatePipe. Expected a valid Object, received: ${args[0]}`);\n        }\n      } else if (typeof args[0] === 'object' && !Array.isArray(args[0])) {\n        interpolateParams = args[0];\n      }\n    }\n    // store the query, in case it changes\n    this.lastKey = query;\n    // store the params, in case they change\n    this.lastParams = args;\n    // set the value\n    this.updateValue(query, interpolateParams);\n    // if there is a subscription to onLangChange, clean it\n    this._dispose();\n    // subscribe to onTranslationChange event, in case the translations change\n    if (!this.onTranslationChange) {\n      this.onTranslationChange = this.translate.onTranslationChange.subscribe(event => {\n        if (this.lastKey && event.lang === this.translate.currentLang) {\n          this.lastKey = null;\n          this.updateValue(query, interpolateParams, event.translations);\n        }\n      });\n    }\n    // subscribe to onLangChange event, in case the language changes\n    if (!this.onLangChange) {\n      this.onLangChange = this.translate.onLangChange.subscribe(event => {\n        if (this.lastKey) {\n          this.lastKey = null; // we want to make sure it doesn't return the same value until it's been updated\n          this.updateValue(query, interpolateParams, event.translations);\n        }\n      });\n    }\n    // subscribe to onDefaultLangChange event, in case the default language changes\n    if (!this.onDefaultLangChange) {\n      this.onDefaultLangChange = this.translate.onDefaultLangChange.subscribe(() => {\n        if (this.lastKey) {\n          this.lastKey = null; // we want to make sure it doesn't return the same value until it's been updated\n          this.updateValue(query, interpolateParams);\n        }\n      });\n    }\n    return this.value;\n  }\n  /**\n   * Clean any existing subscription to change events\n   */\n  _dispose() {\n    if (typeof this.onTranslationChange !== 'undefined') {\n      this.onTranslationChange.unsubscribe();\n      this.onTranslationChange = undefined;\n    }\n    if (typeof this.onLangChange !== 'undefined') {\n      this.onLangChange.unsubscribe();\n      this.onLangChange = undefined;\n    }\n    if (typeof this.onDefaultLangChange !== 'undefined') {\n      this.onDefaultLangChange.unsubscribe();\n      this.onDefaultLangChange = undefined;\n    }\n  }\n  ngOnDestroy() {\n    this._dispose();\n  }\n  static ɵfac = function TranslatePipe_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TranslatePipe)(i0.ɵɵdirectiveInject(TranslateService, 16), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef, 16));\n  };\n  static ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"translate\",\n    type: TranslatePipe,\n    pure: false\n  });\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TranslatePipe,\n    factory: TranslatePipe.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslatePipe, [{\n    type: Injectable\n  }, {\n    type: Pipe,\n    args: [{\n      name: 'translate',\n      pure: false // required to update the value when the promise is resolved\n    }]\n  }], function () {\n    return [{\n      type: TranslateService\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, null);\n})();\nclass TranslateModule {\n  /**\n   * Use this method in your root module to provide the TranslateService\n   */\n  static forRoot(config = {}) {\n    return {\n      ngModule: TranslateModule,\n      providers: [config.loader || {\n        provide: TranslateLoader,\n        useClass: TranslateFakeLoader\n      }, config.compiler || {\n        provide: TranslateCompiler,\n        useClass: TranslateFakeCompiler\n      }, config.parser || {\n        provide: TranslateParser,\n        useClass: TranslateDefaultParser\n      }, config.missingTranslationHandler || {\n        provide: MissingTranslationHandler,\n        useClass: FakeMissingTranslationHandler\n      }, TranslateStore, {\n        provide: USE_STORE,\n        useValue: config.isolate\n      }, {\n        provide: USE_DEFAULT_LANG,\n        useValue: config.useDefaultLang\n      }, {\n        provide: USE_EXTEND,\n        useValue: config.extend\n      }, {\n        provide: DEFAULT_LANGUAGE,\n        useValue: config.defaultLanguage\n      }, TranslateService]\n    };\n  }\n  /**\n   * Use this method in your other (non root) modules to import the directive/pipe\n   */\n  static forChild(config = {}) {\n    return {\n      ngModule: TranslateModule,\n      providers: [config.loader || {\n        provide: TranslateLoader,\n        useClass: TranslateFakeLoader\n      }, config.compiler || {\n        provide: TranslateCompiler,\n        useClass: TranslateFakeCompiler\n      }, config.parser || {\n        provide: TranslateParser,\n        useClass: TranslateDefaultParser\n      }, config.missingTranslationHandler || {\n        provide: MissingTranslationHandler,\n        useClass: FakeMissingTranslationHandler\n      }, {\n        provide: USE_STORE,\n        useValue: config.isolate\n      }, {\n        provide: USE_DEFAULT_LANG,\n        useValue: config.useDefaultLang\n      }, {\n        provide: USE_EXTEND,\n        useValue: config.extend\n      }, {\n        provide: DEFAULT_LANGUAGE,\n        useValue: config.defaultLanguage\n      }, TranslateService]\n    };\n  }\n  static ɵfac = function TranslateModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TranslateModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TranslateModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [TranslatePipe, TranslateDirective],\n      exports: [TranslatePipe, TranslateDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DEFAULT_LANGUAGE, FakeMissingTranslationHandler, MissingTranslationHandler, TranslateCompiler, TranslateDefaultParser, TranslateDirective, TranslateFakeCompiler, TranslateFakeLoader, TranslateLoader, TranslateModule, TranslateParser, TranslatePipe, TranslateService, TranslateStore, USE_DEFAULT_LANG, USE_EXTEND, USE_STORE };", "map": {"version": 3, "names": ["i0", "Injectable", "EventEmitter", "InjectionToken", "Inject", "Directive", "Input", "<PERSON><PERSON>", "NgModule", "of", "isObservable", "fork<PERSON><PERSON>n", "concat", "defer", "take", "shareReplay", "map", "concatMap", "switchMap", "Translate<PERSON><PERSON><PERSON>", "TranslateFakeLoader", "getTranslation", "lang", "ɵfac", "ɵTranslateFakeLoader_BaseFactory", "TranslateFakeLoader_Factory", "__ngFactoryType__", "ɵɵgetInheritedFactory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "MissingTranslationHandler", "FakeMissingTranslationHandler", "handle", "params", "key", "FakeMissingTranslationHandler_Factory", "equals", "o1", "o2", "t1", "t2", "length", "keySet", "Array", "isArray", "Object", "create", "isDefined", "value", "isObject", "item", "mergeDeep", "target", "source", "output", "assign", "keys", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TranslateDefault<PERSON><PERSON><PERSON>", "templateMatcher", "interpolate", "expr", "result", "interpolateString", "interpolateFunction", "getValue", "split", "shift", "undefined", "fn", "replace", "substring", "b", "r", "ɵTranslateDefaultParser_BaseFactory", "TranslateDefaultParser_Factory", "TranslateCompiler", "TranslateFakeCompiler", "compile", "compileTranslations", "translations", "ɵTranslateFakeCompiler_BaseFactory", "TranslateFakeCompiler_Factory", "TranslateStore", "defaultLang", "currentLang", "langs", "onTranslationChange", "onLangChange", "onDefaultLangChange", "USE_STORE", "USE_DEFAULT_LANG", "DEFAULT_LANGUAGE", "USE_EXTEND", "TranslateService", "store", "<PERSON><PERSON><PERSON><PERSON>", "compiler", "parser", "missingTranslation<PERSON><PERSON><PERSON>", "useDefaultLang", "isolate", "extend", "loadingTranslations", "pending", "_onTranslationChange", "_onLangChange", "_onDefaultLangChange", "_defaultLang", "_currentLang", "_langs", "_translations", "_translationRequests", "constructor", "defaultLanguage", "setDefaultLang", "retrieveTranslations", "pipe", "subscribe", "res", "changeDefaultLang", "getDefaultLang", "use", "changeLang", "next", "updateLangs", "error", "err", "setTranslation", "shouldMerge", "emit", "get<PERSON>angs", "addLangs", "indexOf", "push", "getParsedResult", "interpolateParams", "observables", "k", "sources", "arr", "obj", "index", "translateService", "get", "Error", "getStreamOnTranslationChange", "event", "stream", "instant", "set", "reloadLang", "resetLang", "getBrowserLang", "window", "navigator", "browserLang", "languages", "language", "browserLanguage", "userLanguage", "getBrowserCultureLang", "browserCultureLang", "TranslateService_Factory", "ɵɵinject", "decorators", "args", "TranslateDirective", "element", "_ref", "lastParams", "currentParams", "onLangChangeSub", "onDefaultLangChangeSub", "onTranslationChangeSub", "translate", "checkNodes", "translateParams", "ngAfterViewChecked", "forceUpdate", "nodes", "nativeElement", "childNodes", "<PERSON><PERSON><PERSON><PERSON>", "i", "node", "nodeType", "last<PERSON>ey", "lookup<PERSON><PERSON>", "content", "get<PERSON>ontent", "<PERSON><PERSON><PERSON>nt", "trim", "currentValue", "originalContent", "updateValue", "onTranslation", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "textContent", "data", "ngOnDestroy", "unsubscribe", "TranslateDirective_Factory", "ɵɵdirectiveInject", "ElementRef", "ChangeDetectorRef", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "selector", "TranslatePipe", "transform", "query", "validArgs", "JSON", "parse", "e", "SyntaxError", "_dispose", "TranslatePipe_Factory", "ɵpipe", "ɵɵdefinePipe", "name", "pure", "TranslateModule", "forRoot", "config", "ngModule", "providers", "loader", "provide", "useClass", "useValue", "<PERSON><PERSON><PERSON><PERSON>", "TranslateModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "declarations", "exports"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@ngx-translate/core/dist/fesm2022/ngx-translate-core.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, InjectionToken, Inject, Directive, Input, Pipe, NgModule } from '@angular/core';\nimport { of, isObservable, forkJoin, concat, defer } from 'rxjs';\nimport { take, shareReplay, map, concatMap, switchMap } from 'rxjs/operators';\n\nclass TranslateLoader {\n}\n/**\n * This loader is just a placeholder that does nothing, in case you don't need a loader at all\n */\nclass TranslateFakeLoader extends TranslateLoader {\n    getTranslation(lang) {\n        return of({});\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslateFakeLoader, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslateFakeLoader });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslateFakeLoader, decorators: [{\n            type: Injectable\n        }] });\n\nclass MissingTranslationHandler {\n}\n/**\n * This handler is just a placeholder that does nothing, in case you don't need a missing translation handler at all\n */\nclass FakeMissingTranslationHandler {\n    handle(params) {\n        return params.key;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: FakeMissingTranslationHandler, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: FakeMissingTranslationHandler });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: FakeMissingTranslationHandler, decorators: [{\n            type: Injectable\n        }] });\n\n/* tslint:disable */\n/**\n * Determines if two objects or two values are equivalent.\n *\n * Two objects or values are considered equivalent if at least one of the following is true:\n *\n * * Both objects or values pass `===` comparison.\n * * Both objects or values are of the same type and all of their properties are equal by\n *   comparing them with `equals`.\n *\n * @param o1 Object or value to compare.\n * @param o2 Object or value to compare.\n * @returns true if arguments are equal.\n */\nfunction equals(o1, o2) {\n    if (o1 === o2)\n        return true;\n    if (o1 === null || o2 === null)\n        return false;\n    if (o1 !== o1 && o2 !== o2)\n        return true; // NaN === NaN\n    let t1 = typeof o1, t2 = typeof o2, length, key, keySet;\n    if (t1 == t2 && t1 == 'object') {\n        if (Array.isArray(o1)) {\n            if (!Array.isArray(o2))\n                return false;\n            if ((length = o1.length) == o2.length) {\n                for (key = 0; key < length; key++) {\n                    if (!equals(o1[key], o2[key]))\n                        return false;\n                }\n                return true;\n            }\n        }\n        else {\n            if (Array.isArray(o2)) {\n                return false;\n            }\n            keySet = Object.create(null);\n            for (key in o1) {\n                if (!equals(o1[key], o2[key])) {\n                    return false;\n                }\n                keySet[key] = true;\n            }\n            for (key in o2) {\n                if (!(key in keySet) && typeof o2[key] !== 'undefined') {\n                    return false;\n                }\n            }\n            return true;\n        }\n    }\n    return false;\n}\n/* tslint:enable */\nfunction isDefined(value) {\n    return typeof value !== 'undefined' && value !== null;\n}\nfunction isObject(item) {\n    return (item && typeof item === 'object' && !Array.isArray(item));\n}\nfunction mergeDeep(target, source) {\n    let output = Object.assign({}, target);\n    if (isObject(target) && isObject(source)) {\n        Object.keys(source).forEach((key) => {\n            if (isObject(source[key])) {\n                if (!(key in target)) {\n                    Object.assign(output, { [key]: source[key] });\n                }\n                else {\n                    output[key] = mergeDeep(target[key], source[key]);\n                }\n            }\n            else {\n                Object.assign(output, { [key]: source[key] });\n            }\n        });\n    }\n    return output;\n}\n\nclass TranslateParser {\n}\nclass TranslateDefaultParser extends TranslateParser {\n    templateMatcher = /{{\\s?([^{}\\s]*)\\s?}}/g;\n    interpolate(expr, params) {\n        let result;\n        if (typeof expr === 'string') {\n            result = this.interpolateString(expr, params);\n        }\n        else if (typeof expr === 'function') {\n            result = this.interpolateFunction(expr, params);\n        }\n        else {\n            // this should not happen, but an unrelated TranslateService test depends on it\n            result = expr;\n        }\n        return result;\n    }\n    getValue(target, key) {\n        let keys = typeof key === 'string' ? key.split('.') : [key];\n        key = '';\n        do {\n            key += keys.shift();\n            if (isDefined(target) && isDefined(target[key]) && (typeof target[key] === 'object' || !keys.length)) {\n                target = target[key];\n                key = '';\n            }\n            else if (!keys.length) {\n                target = undefined;\n            }\n            else {\n                key += '.';\n            }\n        } while (keys.length);\n        return target;\n    }\n    interpolateFunction(fn, params) {\n        return fn(params);\n    }\n    interpolateString(expr, params) {\n        if (!params) {\n            return expr;\n        }\n        return expr.replace(this.templateMatcher, (substring, b) => {\n            let r = this.getValue(params, b);\n            return isDefined(r) ? r : substring;\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslateDefaultParser, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslateDefaultParser });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslateDefaultParser, decorators: [{\n            type: Injectable\n        }] });\n\nclass TranslateCompiler {\n}\n/**\n * This compiler is just a placeholder that does nothing, in case you don't need a compiler at all\n */\nclass TranslateFakeCompiler extends TranslateCompiler {\n    compile(value, lang) {\n        return value;\n    }\n    compileTranslations(translations, lang) {\n        return translations;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslateFakeCompiler, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslateFakeCompiler });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslateFakeCompiler, decorators: [{\n            type: Injectable\n        }] });\n\nclass TranslateStore {\n    /**\n     * The default lang to fallback when translations are missing on the current lang\n     */\n    defaultLang;\n    /**\n     * The lang currently used\n     */\n    currentLang = this.defaultLang;\n    /**\n     * a list of translations per lang\n     */\n    translations = {};\n    /**\n     * an array of langs\n     */\n    langs = [];\n    /**\n     * An EventEmitter to listen to translation change events\n     * onTranslationChange.subscribe((params: TranslationChangeEvent) => {\n       *     // do something\n       * });\n     */\n    onTranslationChange = new EventEmitter();\n    /**\n     * An EventEmitter to listen to lang change events\n     * onLangChange.subscribe((params: LangChangeEvent) => {\n       *     // do something\n       * });\n     */\n    onLangChange = new EventEmitter();\n    /**\n     * An EventEmitter to listen to default lang change events\n     * onDefaultLangChange.subscribe((params: DefaultLangChangeEvent) => {\n       *     // do something\n       * });\n     */\n    onDefaultLangChange = new EventEmitter();\n}\n\nconst USE_STORE = new InjectionToken('USE_STORE');\nconst USE_DEFAULT_LANG = new InjectionToken('USE_DEFAULT_LANG');\nconst DEFAULT_LANGUAGE = new InjectionToken('DEFAULT_LANGUAGE');\nconst USE_EXTEND = new InjectionToken('USE_EXTEND');\nclass TranslateService {\n    store;\n    currentLoader;\n    compiler;\n    parser;\n    missingTranslationHandler;\n    useDefaultLang;\n    isolate;\n    extend;\n    loadingTranslations;\n    pending = false;\n    _onTranslationChange = new EventEmitter();\n    _onLangChange = new EventEmitter();\n    _onDefaultLangChange = new EventEmitter();\n    _defaultLang;\n    _currentLang;\n    _langs = [];\n    _translations = {};\n    _translationRequests = {};\n    /**\n     * An EventEmitter to listen to translation change events\n     * onTranslationChange.subscribe((params: TranslationChangeEvent) => {\n       *     // do something\n       * });\n     */\n    get onTranslationChange() {\n        return this.isolate ? this._onTranslationChange : this.store.onTranslationChange;\n    }\n    /**\n     * An EventEmitter to listen to lang change events\n     * onLangChange.subscribe((params: LangChangeEvent) => {\n       *     // do something\n       * });\n     */\n    get onLangChange() {\n        return this.isolate ? this._onLangChange : this.store.onLangChange;\n    }\n    /**\n     * An EventEmitter to listen to default lang change events\n     * onDefaultLangChange.subscribe((params: DefaultLangChangeEvent) => {\n       *     // do something\n       * });\n     */\n    get onDefaultLangChange() {\n        return this.isolate ? this._onDefaultLangChange : this.store.onDefaultLangChange;\n    }\n    /**\n     * The default lang to fallback when translations are missing on the current lang\n     */\n    get defaultLang() {\n        return this.isolate ? this._defaultLang : this.store.defaultLang;\n    }\n    set defaultLang(defaultLang) {\n        if (this.isolate) {\n            this._defaultLang = defaultLang;\n        }\n        else {\n            this.store.defaultLang = defaultLang;\n        }\n    }\n    /**\n     * The lang currently used\n     */\n    get currentLang() {\n        return this.isolate ? this._currentLang : this.store.currentLang;\n    }\n    set currentLang(currentLang) {\n        if (this.isolate) {\n            this._currentLang = currentLang;\n        }\n        else {\n            this.store.currentLang = currentLang;\n        }\n    }\n    /**\n     * an array of langs\n     */\n    get langs() {\n        return this.isolate ? this._langs : this.store.langs;\n    }\n    set langs(langs) {\n        if (this.isolate) {\n            this._langs = langs;\n        }\n        else {\n            this.store.langs = langs;\n        }\n    }\n    /**\n     * a list of translations per lang\n     */\n    get translations() {\n        return this.isolate ? this._translations : this.store.translations;\n    }\n    set translations(translations) {\n        if (this.isolate) {\n            this._translations = translations;\n        }\n        else {\n            this.store.translations = translations;\n        }\n    }\n    /**\n     *\n     * @param store an instance of the store (that is supposed to be unique)\n     * @param currentLoader An instance of the loader currently used\n     * @param compiler An instance of the compiler currently used\n     * @param parser An instance of the parser currently used\n     * @param missingTranslationHandler A handler for missing translations.\n     * @param useDefaultLang whether we should use default language translation when current language translation is missing.\n     * @param isolate whether this service should use the store or not\n     * @param extend To make a child module extend (and use) translations from parent modules.\n     * @param defaultLanguage Set the default language using configuration\n     */\n    constructor(store, currentLoader, compiler, parser, missingTranslationHandler, useDefaultLang = true, isolate = false, extend = false, defaultLanguage) {\n        this.store = store;\n        this.currentLoader = currentLoader;\n        this.compiler = compiler;\n        this.parser = parser;\n        this.missingTranslationHandler = missingTranslationHandler;\n        this.useDefaultLang = useDefaultLang;\n        this.isolate = isolate;\n        this.extend = extend;\n        /** set the default language from configuration */\n        if (defaultLanguage) {\n            this.setDefaultLang(defaultLanguage);\n        }\n    }\n    /**\n     * Sets the default language to use as a fallback\n     */\n    setDefaultLang(lang) {\n        if (lang === this.defaultLang) {\n            return;\n        }\n        let pending = this.retrieveTranslations(lang);\n        if (typeof pending !== \"undefined\") {\n            // on init set the defaultLang immediately\n            if (this.defaultLang == null) {\n                this.defaultLang = lang;\n            }\n            pending.pipe(take(1))\n                .subscribe((res) => {\n                this.changeDefaultLang(lang);\n            });\n        }\n        else { // we already have this language\n            this.changeDefaultLang(lang);\n        }\n    }\n    /**\n     * Gets the default language used\n     */\n    getDefaultLang() {\n        return this.defaultLang;\n    }\n    /**\n     * Changes the lang currently used\n     */\n    use(lang) {\n        // don't change the language if the language given is already selected\n        if (lang === this.currentLang) {\n            return of(this.translations[lang]);\n        }\n        let pending = this.retrieveTranslations(lang);\n        if (typeof pending !== \"undefined\") {\n            // on init set the currentLang immediately\n            if (!this.currentLang) {\n                this.currentLang = lang;\n            }\n            pending.pipe(take(1))\n                .subscribe((res) => {\n                this.changeLang(lang);\n            });\n            return pending;\n        }\n        else { // we have this language, return an Observable\n            this.changeLang(lang);\n            return of(this.translations[lang]);\n        }\n    }\n    /**\n     * Retrieves the given translations\n     */\n    retrieveTranslations(lang) {\n        let pending;\n        // if this language is unavailable or extend is true, ask for it\n        if (typeof this.translations[lang] === \"undefined\" || this.extend) {\n            this._translationRequests[lang] = this._translationRequests[lang] || this.getTranslation(lang);\n            pending = this._translationRequests[lang];\n        }\n        return pending;\n    }\n    /**\n     * Gets an object of translations for a given language with the current loader\n     * and passes it through the compiler\n     */\n    getTranslation(lang) {\n        this.pending = true;\n        const loadingTranslations = this.currentLoader.getTranslation(lang).pipe(shareReplay(1), take(1));\n        this.loadingTranslations = loadingTranslations.pipe(map((res) => this.compiler.compileTranslations(res, lang)), shareReplay(1), take(1));\n        this.loadingTranslations\n            .subscribe({\n            next: (res) => {\n                this.translations[lang] = this.extend && this.translations[lang] ? { ...res, ...this.translations[lang] } : res;\n                this.updateLangs();\n                this.pending = false;\n            },\n            error: (err) => {\n                this.pending = false;\n            }\n        });\n        return loadingTranslations;\n    }\n    /**\n     * Manually sets an object of translations for a given language\n     * after passing it through the compiler\n     */\n    setTranslation(lang, translations, shouldMerge = false) {\n        translations = this.compiler.compileTranslations(translations, lang);\n        if ((shouldMerge || this.extend) && this.translations[lang]) {\n            this.translations[lang] = mergeDeep(this.translations[lang], translations);\n        }\n        else {\n            this.translations[lang] = translations;\n        }\n        this.updateLangs();\n        this.onTranslationChange.emit({ lang: lang, translations: this.translations[lang] });\n    }\n    /**\n     * Returns an array of currently available langs\n     */\n    getLangs() {\n        return this.langs;\n    }\n    /**\n     * Add available langs\n     */\n    addLangs(langs) {\n        langs.forEach((lang) => {\n            if (this.langs.indexOf(lang) === -1) {\n                this.langs.push(lang);\n            }\n        });\n    }\n    /**\n     * Update the list of available langs\n     */\n    updateLangs() {\n        this.addLangs(Object.keys(this.translations));\n    }\n    /**\n     * Returns the parsed result of the translations\n     */\n    getParsedResult(translations, key, interpolateParams) {\n        let res;\n        if (key instanceof Array) {\n            let result = {}, observables = false;\n            for (let k of key) {\n                result[k] = this.getParsedResult(translations, k, interpolateParams);\n                if (isObservable(result[k])) {\n                    observables = true;\n                }\n            }\n            if (observables) {\n                const sources = key.map(k => isObservable(result[k]) ? result[k] : of(result[k]));\n                return forkJoin(sources).pipe(map((arr) => {\n                    let obj = {};\n                    arr.forEach((value, index) => {\n                        obj[key[index]] = value;\n                    });\n                    return obj;\n                }));\n            }\n            return result;\n        }\n        if (translations) {\n            res = this.parser.interpolate(this.parser.getValue(translations, key), interpolateParams);\n        }\n        if (typeof res === \"undefined\" && this.defaultLang != null && this.defaultLang !== this.currentLang && this.useDefaultLang) {\n            res = this.parser.interpolate(this.parser.getValue(this.translations[this.defaultLang], key), interpolateParams);\n        }\n        if (typeof res === \"undefined\") {\n            let params = { key, translateService: this };\n            if (typeof interpolateParams !== 'undefined') {\n                params.interpolateParams = interpolateParams;\n            }\n            res = this.missingTranslationHandler.handle(params);\n        }\n        return typeof res !== \"undefined\" ? res : key;\n    }\n    /**\n     * Gets the translated value of a key (or an array of keys)\n     * @returns the translated key, or an object of translated keys\n     */\n    get(key, interpolateParams) {\n        if (!isDefined(key) || !key.length) {\n            throw new Error(`Parameter \"key\" required`);\n        }\n        // check if we are loading a new translation to use\n        if (this.pending) {\n            return this.loadingTranslations.pipe(concatMap((res) => {\n                res = this.getParsedResult(res, key, interpolateParams);\n                return isObservable(res) ? res : of(res);\n            }));\n        }\n        else {\n            let res = this.getParsedResult(this.translations[this.currentLang], key, interpolateParams);\n            return isObservable(res) ? res : of(res);\n        }\n    }\n    /**\n     * Returns a stream of translated values of a key (or an array of keys) which updates\n     * whenever the translation changes.\n     * @returns A stream of the translated key, or an object of translated keys\n     */\n    getStreamOnTranslationChange(key, interpolateParams) {\n        if (!isDefined(key) || !key.length) {\n            throw new Error(`Parameter \"key\" required`);\n        }\n        return concat(defer(() => this.get(key, interpolateParams)), this.onTranslationChange.pipe(switchMap((event) => {\n            const res = this.getParsedResult(event.translations, key, interpolateParams);\n            if (typeof res.subscribe === 'function') {\n                return res;\n            }\n            else {\n                return of(res);\n            }\n        })));\n    }\n    /**\n     * Returns a stream of translated values of a key (or an array of keys) which updates\n     * whenever the language changes.\n     * @returns A stream of the translated key, or an object of translated keys\n     */\n    stream(key, interpolateParams) {\n        if (!isDefined(key) || !key.length) {\n            throw new Error(`Parameter \"key\" required`);\n        }\n        return concat(defer(() => this.get(key, interpolateParams)), this.onLangChange.pipe(switchMap((event) => {\n            const res = this.getParsedResult(event.translations, key, interpolateParams);\n            return isObservable(res) ? res : of(res);\n        })));\n    }\n    /**\n     * Returns a translation instantly from the internal state of loaded translation.\n     * All rules regarding the current language, the preferred language of even fallback languages will be used except any promise handling.\n     */\n    instant(key, interpolateParams) {\n        if (!isDefined(key) || !key.length) {\n            throw new Error(`Parameter \"key\" required`);\n        }\n        let res = this.getParsedResult(this.translations[this.currentLang], key, interpolateParams);\n        if (isObservable(res)) {\n            if (key instanceof Array) {\n                let obj = {};\n                key.forEach((value, index) => {\n                    obj[key[index]] = key[index];\n                });\n                return obj;\n            }\n            return key;\n        }\n        else {\n            return res;\n        }\n    }\n    /**\n     * Sets the translated value of a key, after compiling it\n     */\n    set(key, value, lang = this.currentLang) {\n        this.translations[lang][key] = this.compiler.compile(value, lang);\n        this.updateLangs();\n        this.onTranslationChange.emit({ lang: lang, translations: this.translations[lang] });\n    }\n    /**\n     * Changes the current lang\n     */\n    changeLang(lang) {\n        this.currentLang = lang;\n        this.onLangChange.emit({ lang: lang, translations: this.translations[lang] });\n        // if there is no default lang, use the one that we just set\n        if (this.defaultLang == null) {\n            this.changeDefaultLang(lang);\n        }\n    }\n    /**\n     * Changes the default lang\n     */\n    changeDefaultLang(lang) {\n        this.defaultLang = lang;\n        this.onDefaultLangChange.emit({ lang: lang, translations: this.translations[lang] });\n    }\n    /**\n     * Allows to reload the lang file from the file\n     */\n    reloadLang(lang) {\n        this.resetLang(lang);\n        return this.getTranslation(lang);\n    }\n    /**\n     * Deletes inner translation\n     */\n    resetLang(lang) {\n        this._translationRequests[lang] = undefined;\n        this.translations[lang] = undefined;\n    }\n    /**\n     * Returns the language code name from the browser, e.g. \"de\"\n     */\n    getBrowserLang() {\n        if (typeof window === 'undefined' || typeof window.navigator === 'undefined') {\n            return undefined;\n        }\n        let browserLang = window.navigator.languages ? window.navigator.languages[0] : null;\n        browserLang = browserLang || window.navigator.language || window.navigator.browserLanguage || window.navigator.userLanguage;\n        if (typeof browserLang === 'undefined') {\n            return undefined;\n        }\n        if (browserLang.indexOf('-') !== -1) {\n            browserLang = browserLang.split('-')[0];\n        }\n        if (browserLang.indexOf('_') !== -1) {\n            browserLang = browserLang.split('_')[0];\n        }\n        return browserLang;\n    }\n    /**\n     * Returns the culture language code name from the browser, e.g. \"de-DE\"\n     */\n    getBrowserCultureLang() {\n        if (typeof window === 'undefined' || typeof window.navigator === 'undefined') {\n            return undefined;\n        }\n        let browserCultureLang = window.navigator.languages ? window.navigator.languages[0] : null;\n        browserCultureLang = browserCultureLang || window.navigator.language || window.navigator.browserLanguage || window.navigator.userLanguage;\n        return browserCultureLang;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslateService, deps: [{ token: TranslateStore }, { token: TranslateLoader }, { token: TranslateCompiler }, { token: TranslateParser }, { token: MissingTranslationHandler }, { token: USE_DEFAULT_LANG }, { token: USE_STORE }, { token: USE_EXTEND }, { token: DEFAULT_LANGUAGE }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslateService });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslateService, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: TranslateStore }, { type: TranslateLoader }, { type: TranslateCompiler }, { type: TranslateParser }, { type: MissingTranslationHandler }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [USE_DEFAULT_LANG]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [USE_STORE]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [USE_EXTEND]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DEFAULT_LANGUAGE]\n                }] }]; } });\n\nclass TranslateDirective {\n    translateService;\n    element;\n    _ref;\n    key;\n    lastParams;\n    currentParams;\n    onLangChangeSub;\n    onDefaultLangChangeSub;\n    onTranslationChangeSub;\n    set translate(key) {\n        if (key) {\n            this.key = key;\n            this.checkNodes();\n        }\n    }\n    set translateParams(params) {\n        if (!equals(this.currentParams, params)) {\n            this.currentParams = params;\n            this.checkNodes(true);\n        }\n    }\n    constructor(translateService, element, _ref) {\n        this.translateService = translateService;\n        this.element = element;\n        this._ref = _ref;\n        // subscribe to onTranslationChange event, in case the translations of the current lang change\n        if (!this.onTranslationChangeSub) {\n            this.onTranslationChangeSub = this.translateService.onTranslationChange.subscribe((event) => {\n                if (event.lang === this.translateService.currentLang) {\n                    this.checkNodes(true, event.translations);\n                }\n            });\n        }\n        // subscribe to onLangChange event, in case the language changes\n        if (!this.onLangChangeSub) {\n            this.onLangChangeSub = this.translateService.onLangChange.subscribe((event) => {\n                this.checkNodes(true, event.translations);\n            });\n        }\n        // subscribe to onDefaultLangChange event, in case the default language changes\n        if (!this.onDefaultLangChangeSub) {\n            this.onDefaultLangChangeSub = this.translateService.onDefaultLangChange.subscribe((event) => {\n                this.checkNodes(true);\n            });\n        }\n    }\n    ngAfterViewChecked() {\n        this.checkNodes();\n    }\n    checkNodes(forceUpdate = false, translations) {\n        let nodes = this.element.nativeElement.childNodes;\n        // if the element is empty\n        if (!nodes.length) {\n            // we add the key as content\n            this.setContent(this.element.nativeElement, this.key);\n            nodes = this.element.nativeElement.childNodes;\n        }\n        for (let i = 0; i < nodes.length; ++i) {\n            let node = nodes[i];\n            if (node.nodeType === 3) { // node type 3 is a text node\n                let key;\n                if (forceUpdate) {\n                    node.lastKey = null;\n                }\n                if (isDefined(node.lookupKey)) {\n                    key = node.lookupKey;\n                }\n                else if (this.key) {\n                    key = this.key;\n                }\n                else {\n                    let content = this.getContent(node);\n                    let trimmedContent = content.trim();\n                    if (trimmedContent.length) {\n                        node.lookupKey = trimmedContent;\n                        // we want to use the content as a key, not the translation value\n                        if (content !== node.currentValue) {\n                            key = trimmedContent;\n                            // the content was changed from the user, we'll use it as a reference if needed\n                            node.originalContent = content || node.originalContent;\n                        }\n                        else if (node.originalContent) { // the content seems ok, but the lang has changed\n                            // the current content is the translation, not the key, use the last real content as key\n                            key = node.originalContent.trim();\n                        }\n                        else if (content !== node.currentValue) {\n                            // we want to use the content as a key, not the translation value\n                            key = trimmedContent;\n                            // the content was changed from the user, we'll use it as a reference if needed\n                            node.originalContent = content || node.originalContent;\n                        }\n                    }\n                }\n                this.updateValue(key, node, translations);\n            }\n        }\n    }\n    updateValue(key, node, translations) {\n        if (key) {\n            if (node.lastKey === key && this.lastParams === this.currentParams) {\n                return;\n            }\n            this.lastParams = this.currentParams;\n            let onTranslation = (res) => {\n                if (res !== key) {\n                    node.lastKey = key;\n                }\n                if (!node.originalContent) {\n                    node.originalContent = this.getContent(node);\n                }\n                node.currentValue = isDefined(res) ? res : (node.originalContent || key);\n                // we replace in the original content to preserve spaces that we might have trimmed\n                this.setContent(node, this.key ? node.currentValue : node.originalContent.replace(key, node.currentValue));\n                this._ref.markForCheck();\n            };\n            if (isDefined(translations)) {\n                let res = this.translateService.getParsedResult(translations, key, this.currentParams);\n                if (isObservable(res)) {\n                    res.subscribe({ next: onTranslation });\n                }\n                else {\n                    onTranslation(res);\n                }\n            }\n            else {\n                this.translateService.get(key, this.currentParams).subscribe(onTranslation);\n            }\n        }\n    }\n    getContent(node) {\n        return isDefined(node.textContent) ? node.textContent : node.data;\n    }\n    setContent(node, content) {\n        if (isDefined(node.textContent)) {\n            node.textContent = content;\n        }\n        else {\n            node.data = content;\n        }\n    }\n    ngOnDestroy() {\n        if (this.onLangChangeSub) {\n            this.onLangChangeSub.unsubscribe();\n        }\n        if (this.onDefaultLangChangeSub) {\n            this.onDefaultLangChangeSub.unsubscribe();\n        }\n        if (this.onTranslationChangeSub) {\n            this.onTranslationChangeSub.unsubscribe();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslateDirective, deps: [{ token: TranslateService }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.0.0\", type: TranslateDirective, selector: \"[translate],[ngx-translate]\", inputs: { translate: \"translate\", translateParams: \"translateParams\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslateDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[translate],[ngx-translate]'\n                }]\n        }], ctorParameters: function () { return [{ type: TranslateService }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { translate: [{\n                type: Input\n            }], translateParams: [{\n                type: Input\n            }] } });\n\nclass TranslatePipe {\n    translate;\n    _ref;\n    value = '';\n    lastKey = null;\n    lastParams = [];\n    onTranslationChange;\n    onLangChange;\n    onDefaultLangChange;\n    constructor(translate, _ref) {\n        this.translate = translate;\n        this._ref = _ref;\n    }\n    updateValue(key, interpolateParams, translations) {\n        let onTranslation = (res) => {\n            this.value = res !== undefined ? res : key;\n            this.lastKey = key;\n            this._ref.markForCheck();\n        };\n        if (translations) {\n            let res = this.translate.getParsedResult(translations, key, interpolateParams);\n            if (isObservable(res.subscribe)) {\n                res.subscribe(onTranslation);\n            }\n            else {\n                onTranslation(res);\n            }\n        }\n        this.translate.get(key, interpolateParams).subscribe(onTranslation);\n    }\n    transform(query, ...args) {\n        if (!query || !query.length) {\n            return query;\n        }\n        // if we ask another time for the same key, return the last value\n        if (equals(query, this.lastKey) && equals(args, this.lastParams)) {\n            return this.value;\n        }\n        let interpolateParams = undefined;\n        if (isDefined(args[0]) && args.length) {\n            if (typeof args[0] === 'string' && args[0].length) {\n                // we accept objects written in the template such as {n:1}, {'n':1}, {n:'v'}\n                // which is why we might need to change it to real JSON objects such as {\"n\":1} or {\"n\":\"v\"}\n                let validArgs = args[0]\n                    .replace(/(\\')?([a-zA-Z0-9_]+)(\\')?(\\s)?:/g, '\"$2\":')\n                    .replace(/:(\\s)?(\\')(.*?)(\\')/g, ':\"$3\"');\n                try {\n                    interpolateParams = JSON.parse(validArgs);\n                }\n                catch (e) {\n                    throw new SyntaxError(`Wrong parameter in TranslatePipe. Expected a valid Object, received: ${args[0]}`);\n                }\n            }\n            else if (typeof args[0] === 'object' && !Array.isArray(args[0])) {\n                interpolateParams = args[0];\n            }\n        }\n        // store the query, in case it changes\n        this.lastKey = query;\n        // store the params, in case they change\n        this.lastParams = args;\n        // set the value\n        this.updateValue(query, interpolateParams);\n        // if there is a subscription to onLangChange, clean it\n        this._dispose();\n        // subscribe to onTranslationChange event, in case the translations change\n        if (!this.onTranslationChange) {\n            this.onTranslationChange = this.translate.onTranslationChange.subscribe((event) => {\n                if (this.lastKey && event.lang === this.translate.currentLang) {\n                    this.lastKey = null;\n                    this.updateValue(query, interpolateParams, event.translations);\n                }\n            });\n        }\n        // subscribe to onLangChange event, in case the language changes\n        if (!this.onLangChange) {\n            this.onLangChange = this.translate.onLangChange.subscribe((event) => {\n                if (this.lastKey) {\n                    this.lastKey = null; // we want to make sure it doesn't return the same value until it's been updated\n                    this.updateValue(query, interpolateParams, event.translations);\n                }\n            });\n        }\n        // subscribe to onDefaultLangChange event, in case the default language changes\n        if (!this.onDefaultLangChange) {\n            this.onDefaultLangChange = this.translate.onDefaultLangChange.subscribe(() => {\n                if (this.lastKey) {\n                    this.lastKey = null; // we want to make sure it doesn't return the same value until it's been updated\n                    this.updateValue(query, interpolateParams);\n                }\n            });\n        }\n        return this.value;\n    }\n    /**\n     * Clean any existing subscription to change events\n     */\n    _dispose() {\n        if (typeof this.onTranslationChange !== 'undefined') {\n            this.onTranslationChange.unsubscribe();\n            this.onTranslationChange = undefined;\n        }\n        if (typeof this.onLangChange !== 'undefined') {\n            this.onLangChange.unsubscribe();\n            this.onLangChange = undefined;\n        }\n        if (typeof this.onDefaultLangChange !== 'undefined') {\n            this.onDefaultLangChange.unsubscribe();\n            this.onDefaultLangChange = undefined;\n        }\n    }\n    ngOnDestroy() {\n        this._dispose();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslatePipe, deps: [{ token: TranslateService }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Pipe });\n    static ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslatePipe, name: \"translate\", pure: false });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslatePipe });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslatePipe, decorators: [{\n            type: Injectable\n        }, {\n            type: Pipe,\n            args: [{\n                    name: 'translate',\n                    pure: false // required to update the value when the promise is resolved\n                }]\n        }], ctorParameters: function () { return [{ type: TranslateService }, { type: i0.ChangeDetectorRef }]; } });\n\nclass TranslateModule {\n    /**\n     * Use this method in your root module to provide the TranslateService\n     */\n    static forRoot(config = {}) {\n        return {\n            ngModule: TranslateModule,\n            providers: [\n                config.loader || { provide: TranslateLoader, useClass: TranslateFakeLoader },\n                config.compiler || { provide: TranslateCompiler, useClass: TranslateFakeCompiler },\n                config.parser || { provide: TranslateParser, useClass: TranslateDefaultParser },\n                config.missingTranslationHandler || { provide: MissingTranslationHandler, useClass: FakeMissingTranslationHandler },\n                TranslateStore,\n                { provide: USE_STORE, useValue: config.isolate },\n                { provide: USE_DEFAULT_LANG, useValue: config.useDefaultLang },\n                { provide: USE_EXTEND, useValue: config.extend },\n                { provide: DEFAULT_LANGUAGE, useValue: config.defaultLanguage },\n                TranslateService\n            ]\n        };\n    }\n    /**\n     * Use this method in your other (non root) modules to import the directive/pipe\n     */\n    static forChild(config = {}) {\n        return {\n            ngModule: TranslateModule,\n            providers: [\n                config.loader || { provide: TranslateLoader, useClass: TranslateFakeLoader },\n                config.compiler || { provide: TranslateCompiler, useClass: TranslateFakeCompiler },\n                config.parser || { provide: TranslateParser, useClass: TranslateDefaultParser },\n                config.missingTranslationHandler || { provide: MissingTranslationHandler, useClass: FakeMissingTranslationHandler },\n                { provide: USE_STORE, useValue: config.isolate },\n                { provide: USE_DEFAULT_LANG, useValue: config.useDefaultLang },\n                { provide: USE_EXTEND, useValue: config.extend },\n                { provide: DEFAULT_LANGUAGE, useValue: config.defaultLanguage },\n                TranslateService\n            ]\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslateModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslateModule, declarations: [TranslatePipe,\n            TranslateDirective], exports: [TranslatePipe,\n            TranslateDirective] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslateModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.0\", ngImport: i0, type: TranslateModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [\n                        TranslatePipe,\n                        TranslateDirective\n                    ],\n                    exports: [\n                        TranslatePipe,\n                        TranslateDirective\n                    ]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DEFAULT_LANGUAGE, FakeMissingTranslationHandler, MissingTranslationHandler, TranslateCompiler, TranslateDefaultParser, TranslateDirective, TranslateFakeCompiler, TranslateFakeLoader, TranslateLoader, TranslateModule, TranslateParser, TranslatePipe, TranslateService, TranslateStore, USE_DEFAULT_LANG, USE_EXTEND, USE_STORE };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,cAAc,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,eAAe;AAClH,SAASC,EAAE,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AAChE,SAASC,IAAI,EAAEC,WAAW,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AAE7E,MAAMC,eAAe,CAAC;AAEtB;AACA;AACA;AACA,MAAMC,mBAAmB,SAASD,eAAe,CAAC;EAC9CE,cAAcA,CAACC,IAAI,EAAE;IACjB,OAAOb,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;EACA,OAAOc,IAAI;IAAA,IAAAC,gCAAA;IAAA,gBAAAC,4BAAAC,iBAAA;MAAA,QAAAF,gCAAA,KAAAA,gCAAA,GAA8ExB,EAAE,CAAA2B,qBAAA,CAAQP,mBAAmB,IAAAM,iBAAA,IAAnBN,mBAAmB;IAAA;EAAA;EACtH,OAAOQ,KAAK,kBAD6E5B,EAAE,CAAA6B,kBAAA;IAAAC,KAAA,EACYV,mBAAmB;IAAAW,OAAA,EAAnBX,mBAAmB,CAAAG;EAAA;AAC9H;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAH6FhC,EAAE,CAAAiC,iBAAA,CAGJb,mBAAmB,EAAc,CAAC;IACjHc,IAAI,EAAEjC;EACV,CAAC,CAAC;AAAA;AAEV,MAAMkC,yBAAyB,CAAC;AAEhC;AACA;AACA;AACA,MAAMC,6BAA6B,CAAC;EAChCC,MAAMA,CAACC,MAAM,EAAE;IACX,OAAOA,MAAM,CAACC,GAAG;EACrB;EACA,OAAOhB,IAAI,YAAAiB,sCAAAd,iBAAA;IAAA,YAAAA,iBAAA,IAAwFU,6BAA6B;EAAA;EAChI,OAAOR,KAAK,kBAjB6E5B,EAAE,CAAA6B,kBAAA;IAAAC,KAAA,EAiBYM,6BAA6B;IAAAL,OAAA,EAA7BK,6BAA6B,CAAAb;EAAA;AACxI;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAnB6FhC,EAAE,CAAAiC,iBAAA,CAmBJG,6BAA6B,EAAc,CAAC;IAC3HF,IAAI,EAAEjC;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwC,MAAMA,CAACC,EAAE,EAAEC,EAAE,EAAE;EACpB,IAAID,EAAE,KAAKC,EAAE,EACT,OAAO,IAAI;EACf,IAAID,EAAE,KAAK,IAAI,IAAIC,EAAE,KAAK,IAAI,EAC1B,OAAO,KAAK;EAChB,IAAID,EAAE,KAAKA,EAAE,IAAIC,EAAE,KAAKA,EAAE,EACtB,OAAO,IAAI,CAAC,CAAC;EACjB,IAAIC,EAAE,GAAG,OAAOF,EAAE;IAAEG,EAAE,GAAG,OAAOF,EAAE;IAAEG,MAAM;IAAEP,GAAG;IAAEQ,MAAM;EACvD,IAAIH,EAAE,IAAIC,EAAE,IAAID,EAAE,IAAI,QAAQ,EAAE;IAC5B,IAAII,KAAK,CAACC,OAAO,CAACP,EAAE,CAAC,EAAE;MACnB,IAAI,CAACM,KAAK,CAACC,OAAO,CAACN,EAAE,CAAC,EAClB,OAAO,KAAK;MAChB,IAAI,CAACG,MAAM,GAAGJ,EAAE,CAACI,MAAM,KAAKH,EAAE,CAACG,MAAM,EAAE;QACnC,KAAKP,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGO,MAAM,EAAEP,GAAG,EAAE,EAAE;UAC/B,IAAI,CAACE,MAAM,CAACC,EAAE,CAACH,GAAG,CAAC,EAAEI,EAAE,CAACJ,GAAG,CAAC,CAAC,EACzB,OAAO,KAAK;QACpB;QACA,OAAO,IAAI;MACf;IACJ,CAAC,MACI;MACD,IAAIS,KAAK,CAACC,OAAO,CAACN,EAAE,CAAC,EAAE;QACnB,OAAO,KAAK;MAChB;MACAI,MAAM,GAAGG,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MAC5B,KAAKZ,GAAG,IAAIG,EAAE,EAAE;QACZ,IAAI,CAACD,MAAM,CAACC,EAAE,CAACH,GAAG,CAAC,EAAEI,EAAE,CAACJ,GAAG,CAAC,CAAC,EAAE;UAC3B,OAAO,KAAK;QAChB;QACAQ,MAAM,CAACR,GAAG,CAAC,GAAG,IAAI;MACtB;MACA,KAAKA,GAAG,IAAII,EAAE,EAAE;QACZ,IAAI,EAAEJ,GAAG,IAAIQ,MAAM,CAAC,IAAI,OAAOJ,EAAE,CAACJ,GAAG,CAAC,KAAK,WAAW,EAAE;UACpD,OAAO,KAAK;QAChB;MACJ;MACA,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;AACA;AACA,SAASa,SAASA,CAACC,KAAK,EAAE;EACtB,OAAO,OAAOA,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,IAAI;AACzD;AACA,SAASC,QAAQA,CAACC,IAAI,EAAE;EACpB,OAAQA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACP,KAAK,CAACC,OAAO,CAACM,IAAI,CAAC;AACpE;AACA,SAASC,SAASA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC/B,IAAIC,MAAM,GAAGT,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEH,MAAM,CAAC;EACtC,IAAIH,QAAQ,CAACG,MAAM,CAAC,IAAIH,QAAQ,CAACI,MAAM,CAAC,EAAE;IACtCR,MAAM,CAACW,IAAI,CAACH,MAAM,CAAC,CAACI,OAAO,CAAEvB,GAAG,IAAK;MACjC,IAAIe,QAAQ,CAACI,MAAM,CAACnB,GAAG,CAAC,CAAC,EAAE;QACvB,IAAI,EAAEA,GAAG,IAAIkB,MAAM,CAAC,EAAE;UAClBP,MAAM,CAACU,MAAM,CAACD,MAAM,EAAE;YAAE,CAACpB,GAAG,GAAGmB,MAAM,CAACnB,GAAG;UAAE,CAAC,CAAC;QACjD,CAAC,MACI;UACDoB,MAAM,CAACpB,GAAG,CAAC,GAAGiB,SAAS,CAACC,MAAM,CAAClB,GAAG,CAAC,EAAEmB,MAAM,CAACnB,GAAG,CAAC,CAAC;QACrD;MACJ,CAAC,MACI;QACDW,MAAM,CAACU,MAAM,CAACD,MAAM,EAAE;UAAE,CAACpB,GAAG,GAAGmB,MAAM,CAACnB,GAAG;QAAE,CAAC,CAAC;MACjD;IACJ,CAAC,CAAC;EACN;EACA,OAAOoB,MAAM;AACjB;AAEA,MAAMI,eAAe,CAAC;AAEtB,MAAMC,sBAAsB,SAASD,eAAe,CAAC;EACjDE,eAAe,GAAG,uBAAuB;EACzCC,WAAWA,CAACC,IAAI,EAAE7B,MAAM,EAAE;IACtB,IAAI8B,MAAM;IACV,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;MAC1BC,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAACF,IAAI,EAAE7B,MAAM,CAAC;IACjD,CAAC,MACI,IAAI,OAAO6B,IAAI,KAAK,UAAU,EAAE;MACjCC,MAAM,GAAG,IAAI,CAACE,mBAAmB,CAACH,IAAI,EAAE7B,MAAM,CAAC;IACnD,CAAC,MACI;MACD;MACA8B,MAAM,GAAGD,IAAI;IACjB;IACA,OAAOC,MAAM;EACjB;EACAG,QAAQA,CAACd,MAAM,EAAElB,GAAG,EAAE;IAClB,IAAIsB,IAAI,GAAG,OAAOtB,GAAG,KAAK,QAAQ,GAAGA,GAAG,CAACiC,KAAK,CAAC,GAAG,CAAC,GAAG,CAACjC,GAAG,CAAC;IAC3DA,GAAG,GAAG,EAAE;IACR,GAAG;MACCA,GAAG,IAAIsB,IAAI,CAACY,KAAK,CAAC,CAAC;MACnB,IAAIrB,SAAS,CAACK,MAAM,CAAC,IAAIL,SAAS,CAACK,MAAM,CAAClB,GAAG,CAAC,CAAC,KAAK,OAAOkB,MAAM,CAAClB,GAAG,CAAC,KAAK,QAAQ,IAAI,CAACsB,IAAI,CAACf,MAAM,CAAC,EAAE;QAClGW,MAAM,GAAGA,MAAM,CAAClB,GAAG,CAAC;QACpBA,GAAG,GAAG,EAAE;MACZ,CAAC,MACI,IAAI,CAACsB,IAAI,CAACf,MAAM,EAAE;QACnBW,MAAM,GAAGiB,SAAS;MACtB,CAAC,MACI;QACDnC,GAAG,IAAI,GAAG;MACd;IACJ,CAAC,QAAQsB,IAAI,CAACf,MAAM;IACpB,OAAOW,MAAM;EACjB;EACAa,mBAAmBA,CAACK,EAAE,EAAErC,MAAM,EAAE;IAC5B,OAAOqC,EAAE,CAACrC,MAAM,CAAC;EACrB;EACA+B,iBAAiBA,CAACF,IAAI,EAAE7B,MAAM,EAAE;IAC5B,IAAI,CAACA,MAAM,EAAE;MACT,OAAO6B,IAAI;IACf;IACA,OAAOA,IAAI,CAACS,OAAO,CAAC,IAAI,CAACX,eAAe,EAAE,CAACY,SAAS,EAAEC,CAAC,KAAK;MACxD,IAAIC,CAAC,GAAG,IAAI,CAACR,QAAQ,CAACjC,MAAM,EAAEwC,CAAC,CAAC;MAChC,OAAO1B,SAAS,CAAC2B,CAAC,CAAC,GAAGA,CAAC,GAAGF,SAAS;IACvC,CAAC,CAAC;EACN;EACA,OAAOtD,IAAI;IAAA,IAAAyD,mCAAA;IAAA,gBAAAC,+BAAAvD,iBAAA;MAAA,QAAAsD,mCAAA,KAAAA,mCAAA,GAzJ8EhF,EAAE,CAAA2B,qBAAA,CAyJQqC,sBAAsB,IAAAtC,iBAAA,IAAtBsC,sBAAsB;IAAA;EAAA;EACzH,OAAOpC,KAAK,kBA1J6E5B,EAAE,CAAA6B,kBAAA;IAAAC,KAAA,EA0JYkC,sBAAsB;IAAAjC,OAAA,EAAtBiC,sBAAsB,CAAAzC;EAAA;AACjI;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KA5J6FhC,EAAE,CAAAiC,iBAAA,CA4JJ+B,sBAAsB,EAAc,CAAC;IACpH9B,IAAI,EAAEjC;EACV,CAAC,CAAC;AAAA;AAEV,MAAMiF,iBAAiB,CAAC;AAExB;AACA;AACA;AACA,MAAMC,qBAAqB,SAASD,iBAAiB,CAAC;EAClDE,OAAOA,CAAC/B,KAAK,EAAE/B,IAAI,EAAE;IACjB,OAAO+B,KAAK;EAChB;EACAgC,mBAAmBA,CAACC,YAAY,EAAEhE,IAAI,EAAE;IACpC,OAAOgE,YAAY;EACvB;EACA,OAAO/D,IAAI;IAAA,IAAAgE,kCAAA;IAAA,gBAAAC,8BAAA9D,iBAAA;MAAA,QAAA6D,kCAAA,KAAAA,kCAAA,GA5K8EvF,EAAE,CAAA2B,qBAAA,CA4KQwD,qBAAqB,IAAAzD,iBAAA,IAArByD,qBAAqB;IAAA;EAAA;EACxH,OAAOvD,KAAK,kBA7K6E5B,EAAE,CAAA6B,kBAAA;IAAAC,KAAA,EA6KYqD,qBAAqB;IAAApD,OAAA,EAArBoD,qBAAqB,CAAA5D;EAAA;AAChI;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KA/K6FhC,EAAE,CAAAiC,iBAAA,CA+KJkD,qBAAqB,EAAc,CAAC;IACnHjD,IAAI,EAAEjC;EACV,CAAC,CAAC;AAAA;AAEV,MAAMwF,cAAc,CAAC;EACjB;AACJ;AACA;EACIC,WAAW;EACX;AACJ;AACA;EACIC,WAAW,GAAG,IAAI,CAACD,WAAW;EAC9B;AACJ;AACA;EACIJ,YAAY,GAAG,CAAC,CAAC;EACjB;AACJ;AACA;EACIM,KAAK,GAAG,EAAE;EACV;AACJ;AACA;AACA;AACA;AACA;EACIC,mBAAmB,GAAG,IAAI3F,YAAY,CAAC,CAAC;EACxC;AACJ;AACA;AACA;AACA;AACA;EACI4F,YAAY,GAAG,IAAI5F,YAAY,CAAC,CAAC;EACjC;AACJ;AACA;AACA;AACA;AACA;EACI6F,mBAAmB,GAAG,IAAI7F,YAAY,CAAC,CAAC;AAC5C;AAEA,MAAM8F,SAAS,GAAG,IAAI7F,cAAc,CAAC,WAAW,CAAC;AACjD,MAAM8F,gBAAgB,GAAG,IAAI9F,cAAc,CAAC,kBAAkB,CAAC;AAC/D,MAAM+F,gBAAgB,GAAG,IAAI/F,cAAc,CAAC,kBAAkB,CAAC;AAC/D,MAAMgG,UAAU,GAAG,IAAIhG,cAAc,CAAC,YAAY,CAAC;AACnD,MAAMiG,gBAAgB,CAAC;EACnBC,KAAK;EACLC,aAAa;EACbC,QAAQ;EACRC,MAAM;EACNC,yBAAyB;EACzBC,cAAc;EACdC,OAAO;EACPC,MAAM;EACNC,mBAAmB;EACnBC,OAAO,GAAG,KAAK;EACfC,oBAAoB,GAAG,IAAI7G,YAAY,CAAC,CAAC;EACzC8G,aAAa,GAAG,IAAI9G,YAAY,CAAC,CAAC;EAClC+G,oBAAoB,GAAG,IAAI/G,YAAY,CAAC,CAAC;EACzCgH,YAAY;EACZC,YAAY;EACZC,MAAM,GAAG,EAAE;EACXC,aAAa,GAAG,CAAC,CAAC;EAClBC,oBAAoB,GAAG,CAAC,CAAC;EACzB;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIzB,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACc,OAAO,GAAG,IAAI,CAACI,oBAAoB,GAAG,IAAI,CAACV,KAAK,CAACR,mBAAmB;EACpF;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACa,OAAO,GAAG,IAAI,CAACK,aAAa,GAAG,IAAI,CAACX,KAAK,CAACP,YAAY;EACtE;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIC,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACY,OAAO,GAAG,IAAI,CAACM,oBAAoB,GAAG,IAAI,CAACZ,KAAK,CAACN,mBAAmB;EACpF;EACA;AACJ;AACA;EACI,IAAIL,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACiB,OAAO,GAAG,IAAI,CAACO,YAAY,GAAG,IAAI,CAACb,KAAK,CAACX,WAAW;EACpE;EACA,IAAIA,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,IAAI,CAACiB,OAAO,EAAE;MACd,IAAI,CAACO,YAAY,GAAGxB,WAAW;IACnC,CAAC,MACI;MACD,IAAI,CAACW,KAAK,CAACX,WAAW,GAAGA,WAAW;IACxC;EACJ;EACA;AACJ;AACA;EACI,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACgB,OAAO,GAAG,IAAI,CAACQ,YAAY,GAAG,IAAI,CAACd,KAAK,CAACV,WAAW;EACpE;EACA,IAAIA,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,IAAI,CAACgB,OAAO,EAAE;MACd,IAAI,CAACQ,YAAY,GAAGxB,WAAW;IACnC,CAAC,MACI;MACD,IAAI,CAACU,KAAK,CAACV,WAAW,GAAGA,WAAW;IACxC;EACJ;EACA;AACJ;AACA;EACI,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACe,OAAO,GAAG,IAAI,CAACS,MAAM,GAAG,IAAI,CAACf,KAAK,CAACT,KAAK;EACxD;EACA,IAAIA,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,IAAI,CAACe,OAAO,EAAE;MACd,IAAI,CAACS,MAAM,GAAGxB,KAAK;IACvB,CAAC,MACI;MACD,IAAI,CAACS,KAAK,CAACT,KAAK,GAAGA,KAAK;IAC5B;EACJ;EACA;AACJ;AACA;EACI,IAAIN,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACqB,OAAO,GAAG,IAAI,CAACU,aAAa,GAAG,IAAI,CAAChB,KAAK,CAACf,YAAY;EACtE;EACA,IAAIA,YAAYA,CAACA,YAAY,EAAE;IAC3B,IAAI,IAAI,CAACqB,OAAO,EAAE;MACd,IAAI,CAACU,aAAa,GAAG/B,YAAY;IACrC,CAAC,MACI;MACD,IAAI,CAACe,KAAK,CAACf,YAAY,GAAGA,YAAY;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIiC,WAAWA,CAAClB,KAAK,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,yBAAyB,EAAEC,cAAc,GAAG,IAAI,EAAEC,OAAO,GAAG,KAAK,EAAEC,MAAM,GAAG,KAAK,EAAEY,eAAe,EAAE;IACpJ,IAAI,CAACnB,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB;IACA,IAAIY,eAAe,EAAE;MACjB,IAAI,CAACC,cAAc,CAACD,eAAe,CAAC;IACxC;EACJ;EACA;AACJ;AACA;EACIC,cAAcA,CAACnG,IAAI,EAAE;IACjB,IAAIA,IAAI,KAAK,IAAI,CAACoE,WAAW,EAAE;MAC3B;IACJ;IACA,IAAIoB,OAAO,GAAG,IAAI,CAACY,oBAAoB,CAACpG,IAAI,CAAC;IAC7C,IAAI,OAAOwF,OAAO,KAAK,WAAW,EAAE;MAChC;MACA,IAAI,IAAI,CAACpB,WAAW,IAAI,IAAI,EAAE;QAC1B,IAAI,CAACA,WAAW,GAAGpE,IAAI;MAC3B;MACAwF,OAAO,CAACa,IAAI,CAAC7G,IAAI,CAAC,CAAC,CAAC,CAAC,CAChB8G,SAAS,CAAEC,GAAG,IAAK;QACpB,IAAI,CAACC,iBAAiB,CAACxG,IAAI,CAAC;MAChC,CAAC,CAAC;IACN,CAAC,MACI;MAAE;MACH,IAAI,CAACwG,iBAAiB,CAACxG,IAAI,CAAC;IAChC;EACJ;EACA;AACJ;AACA;EACIyG,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACrC,WAAW;EAC3B;EACA;AACJ;AACA;EACIsC,GAAGA,CAAC1G,IAAI,EAAE;IACN;IACA,IAAIA,IAAI,KAAK,IAAI,CAACqE,WAAW,EAAE;MAC3B,OAAOlF,EAAE,CAAC,IAAI,CAAC6E,YAAY,CAAChE,IAAI,CAAC,CAAC;IACtC;IACA,IAAIwF,OAAO,GAAG,IAAI,CAACY,oBAAoB,CAACpG,IAAI,CAAC;IAC7C,IAAI,OAAOwF,OAAO,KAAK,WAAW,EAAE;MAChC;MACA,IAAI,CAAC,IAAI,CAACnB,WAAW,EAAE;QACnB,IAAI,CAACA,WAAW,GAAGrE,IAAI;MAC3B;MACAwF,OAAO,CAACa,IAAI,CAAC7G,IAAI,CAAC,CAAC,CAAC,CAAC,CAChB8G,SAAS,CAAEC,GAAG,IAAK;QACpB,IAAI,CAACI,UAAU,CAAC3G,IAAI,CAAC;MACzB,CAAC,CAAC;MACF,OAAOwF,OAAO;IAClB,CAAC,MACI;MAAE;MACH,IAAI,CAACmB,UAAU,CAAC3G,IAAI,CAAC;MACrB,OAAOb,EAAE,CAAC,IAAI,CAAC6E,YAAY,CAAChE,IAAI,CAAC,CAAC;IACtC;EACJ;EACA;AACJ;AACA;EACIoG,oBAAoBA,CAACpG,IAAI,EAAE;IACvB,IAAIwF,OAAO;IACX;IACA,IAAI,OAAO,IAAI,CAACxB,YAAY,CAAChE,IAAI,CAAC,KAAK,WAAW,IAAI,IAAI,CAACsF,MAAM,EAAE;MAC/D,IAAI,CAACU,oBAAoB,CAAChG,IAAI,CAAC,GAAG,IAAI,CAACgG,oBAAoB,CAAChG,IAAI,CAAC,IAAI,IAAI,CAACD,cAAc,CAACC,IAAI,CAAC;MAC9FwF,OAAO,GAAG,IAAI,CAACQ,oBAAoB,CAAChG,IAAI,CAAC;IAC7C;IACA,OAAOwF,OAAO;EAClB;EACA;AACJ;AACA;AACA;EACIzF,cAAcA,CAACC,IAAI,EAAE;IACjB,IAAI,CAACwF,OAAO,GAAG,IAAI;IACnB,MAAMD,mBAAmB,GAAG,IAAI,CAACP,aAAa,CAACjF,cAAc,CAACC,IAAI,CAAC,CAACqG,IAAI,CAAC5G,WAAW,CAAC,CAAC,CAAC,EAAED,IAAI,CAAC,CAAC,CAAC,CAAC;IACjG,IAAI,CAAC+F,mBAAmB,GAAGA,mBAAmB,CAACc,IAAI,CAAC3G,GAAG,CAAE6G,GAAG,IAAK,IAAI,CAACtB,QAAQ,CAAClB,mBAAmB,CAACwC,GAAG,EAAEvG,IAAI,CAAC,CAAC,EAAEP,WAAW,CAAC,CAAC,CAAC,EAAED,IAAI,CAAC,CAAC,CAAC,CAAC;IACxI,IAAI,CAAC+F,mBAAmB,CACnBe,SAAS,CAAC;MACXM,IAAI,EAAGL,GAAG,IAAK;QACX,IAAI,CAACvC,YAAY,CAAChE,IAAI,CAAC,GAAG,IAAI,CAACsF,MAAM,IAAI,IAAI,CAACtB,YAAY,CAAChE,IAAI,CAAC,GAAG;UAAE,GAAGuG,GAAG;UAAE,GAAG,IAAI,CAACvC,YAAY,CAAChE,IAAI;QAAE,CAAC,GAAGuG,GAAG;QAC/G,IAAI,CAACM,WAAW,CAAC,CAAC;QAClB,IAAI,CAACrB,OAAO,GAAG,KAAK;MACxB,CAAC;MACDsB,KAAK,EAAGC,GAAG,IAAK;QACZ,IAAI,CAACvB,OAAO,GAAG,KAAK;MACxB;IACJ,CAAC,CAAC;IACF,OAAOD,mBAAmB;EAC9B;EACA;AACJ;AACA;AACA;EACIyB,cAAcA,CAAChH,IAAI,EAAEgE,YAAY,EAAEiD,WAAW,GAAG,KAAK,EAAE;IACpDjD,YAAY,GAAG,IAAI,CAACiB,QAAQ,CAAClB,mBAAmB,CAACC,YAAY,EAAEhE,IAAI,CAAC;IACpE,IAAI,CAACiH,WAAW,IAAI,IAAI,CAAC3B,MAAM,KAAK,IAAI,CAACtB,YAAY,CAAChE,IAAI,CAAC,EAAE;MACzD,IAAI,CAACgE,YAAY,CAAChE,IAAI,CAAC,GAAGkC,SAAS,CAAC,IAAI,CAAC8B,YAAY,CAAChE,IAAI,CAAC,EAAEgE,YAAY,CAAC;IAC9E,CAAC,MACI;MACD,IAAI,CAACA,YAAY,CAAChE,IAAI,CAAC,GAAGgE,YAAY;IAC1C;IACA,IAAI,CAAC6C,WAAW,CAAC,CAAC;IAClB,IAAI,CAACtC,mBAAmB,CAAC2C,IAAI,CAAC;MAAElH,IAAI,EAAEA,IAAI;MAAEgE,YAAY,EAAE,IAAI,CAACA,YAAY,CAAChE,IAAI;IAAE,CAAC,CAAC;EACxF;EACA;AACJ;AACA;EACImH,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC7C,KAAK;EACrB;EACA;AACJ;AACA;EACI8C,QAAQA,CAAC9C,KAAK,EAAE;IACZA,KAAK,CAAC9B,OAAO,CAAExC,IAAI,IAAK;MACpB,IAAI,IAAI,CAACsE,KAAK,CAAC+C,OAAO,CAACrH,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QACjC,IAAI,CAACsE,KAAK,CAACgD,IAAI,CAACtH,IAAI,CAAC;MACzB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACI6G,WAAWA,CAAA,EAAG;IACV,IAAI,CAACO,QAAQ,CAACxF,MAAM,CAACW,IAAI,CAAC,IAAI,CAACyB,YAAY,CAAC,CAAC;EACjD;EACA;AACJ;AACA;EACIuD,eAAeA,CAACvD,YAAY,EAAE/C,GAAG,EAAEuG,iBAAiB,EAAE;IAClD,IAAIjB,GAAG;IACP,IAAItF,GAAG,YAAYS,KAAK,EAAE;MACtB,IAAIoB,MAAM,GAAG,CAAC,CAAC;QAAE2E,WAAW,GAAG,KAAK;MACpC,KAAK,IAAIC,CAAC,IAAIzG,GAAG,EAAE;QACf6B,MAAM,CAAC4E,CAAC,CAAC,GAAG,IAAI,CAACH,eAAe,CAACvD,YAAY,EAAE0D,CAAC,EAAEF,iBAAiB,CAAC;QACpE,IAAIpI,YAAY,CAAC0D,MAAM,CAAC4E,CAAC,CAAC,CAAC,EAAE;UACzBD,WAAW,GAAG,IAAI;QACtB;MACJ;MACA,IAAIA,WAAW,EAAE;QACb,MAAME,OAAO,GAAG1G,GAAG,CAACvB,GAAG,CAACgI,CAAC,IAAItI,YAAY,CAAC0D,MAAM,CAAC4E,CAAC,CAAC,CAAC,GAAG5E,MAAM,CAAC4E,CAAC,CAAC,GAAGvI,EAAE,CAAC2D,MAAM,CAAC4E,CAAC,CAAC,CAAC,CAAC;QACjF,OAAOrI,QAAQ,CAACsI,OAAO,CAAC,CAACtB,IAAI,CAAC3G,GAAG,CAAEkI,GAAG,IAAK;UACvC,IAAIC,GAAG,GAAG,CAAC,CAAC;UACZD,GAAG,CAACpF,OAAO,CAAC,CAACT,KAAK,EAAE+F,KAAK,KAAK;YAC1BD,GAAG,CAAC5G,GAAG,CAAC6G,KAAK,CAAC,CAAC,GAAG/F,KAAK;UAC3B,CAAC,CAAC;UACF,OAAO8F,GAAG;QACd,CAAC,CAAC,CAAC;MACP;MACA,OAAO/E,MAAM;IACjB;IACA,IAAIkB,YAAY,EAAE;MACduC,GAAG,GAAG,IAAI,CAACrB,MAAM,CAACtC,WAAW,CAAC,IAAI,CAACsC,MAAM,CAACjC,QAAQ,CAACe,YAAY,EAAE/C,GAAG,CAAC,EAAEuG,iBAAiB,CAAC;IAC7F;IACA,IAAI,OAAOjB,GAAG,KAAK,WAAW,IAAI,IAAI,CAACnC,WAAW,IAAI,IAAI,IAAI,IAAI,CAACA,WAAW,KAAK,IAAI,CAACC,WAAW,IAAI,IAAI,CAACe,cAAc,EAAE;MACxHmB,GAAG,GAAG,IAAI,CAACrB,MAAM,CAACtC,WAAW,CAAC,IAAI,CAACsC,MAAM,CAACjC,QAAQ,CAAC,IAAI,CAACe,YAAY,CAAC,IAAI,CAACI,WAAW,CAAC,EAAEnD,GAAG,CAAC,EAAEuG,iBAAiB,CAAC;IACpH;IACA,IAAI,OAAOjB,GAAG,KAAK,WAAW,EAAE;MAC5B,IAAIvF,MAAM,GAAG;QAAEC,GAAG;QAAE8G,gBAAgB,EAAE;MAAK,CAAC;MAC5C,IAAI,OAAOP,iBAAiB,KAAK,WAAW,EAAE;QAC1CxG,MAAM,CAACwG,iBAAiB,GAAGA,iBAAiB;MAChD;MACAjB,GAAG,GAAG,IAAI,CAACpB,yBAAyB,CAACpE,MAAM,CAACC,MAAM,CAAC;IACvD;IACA,OAAO,OAAOuF,GAAG,KAAK,WAAW,GAAGA,GAAG,GAAGtF,GAAG;EACjD;EACA;AACJ;AACA;AACA;EACI+G,GAAGA,CAAC/G,GAAG,EAAEuG,iBAAiB,EAAE;IACxB,IAAI,CAAC1F,SAAS,CAACb,GAAG,CAAC,IAAI,CAACA,GAAG,CAACO,MAAM,EAAE;MAChC,MAAM,IAAIyG,KAAK,CAAC,0BAA0B,CAAC;IAC/C;IACA;IACA,IAAI,IAAI,CAACzC,OAAO,EAAE;MACd,OAAO,IAAI,CAACD,mBAAmB,CAACc,IAAI,CAAC1G,SAAS,CAAE4G,GAAG,IAAK;QACpDA,GAAG,GAAG,IAAI,CAACgB,eAAe,CAAChB,GAAG,EAAEtF,GAAG,EAAEuG,iBAAiB,CAAC;QACvD,OAAOpI,YAAY,CAACmH,GAAG,CAAC,GAAGA,GAAG,GAAGpH,EAAE,CAACoH,GAAG,CAAC;MAC5C,CAAC,CAAC,CAAC;IACP,CAAC,MACI;MACD,IAAIA,GAAG,GAAG,IAAI,CAACgB,eAAe,CAAC,IAAI,CAACvD,YAAY,CAAC,IAAI,CAACK,WAAW,CAAC,EAAEpD,GAAG,EAAEuG,iBAAiB,CAAC;MAC3F,OAAOpI,YAAY,CAACmH,GAAG,CAAC,GAAGA,GAAG,GAAGpH,EAAE,CAACoH,GAAG,CAAC;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI2B,4BAA4BA,CAACjH,GAAG,EAAEuG,iBAAiB,EAAE;IACjD,IAAI,CAAC1F,SAAS,CAACb,GAAG,CAAC,IAAI,CAACA,GAAG,CAACO,MAAM,EAAE;MAChC,MAAM,IAAIyG,KAAK,CAAC,0BAA0B,CAAC;IAC/C;IACA,OAAO3I,MAAM,CAACC,KAAK,CAAC,MAAM,IAAI,CAACyI,GAAG,CAAC/G,GAAG,EAAEuG,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAACjD,mBAAmB,CAAC8B,IAAI,CAACzG,SAAS,CAAEuI,KAAK,IAAK;MAC5G,MAAM5B,GAAG,GAAG,IAAI,CAACgB,eAAe,CAACY,KAAK,CAACnE,YAAY,EAAE/C,GAAG,EAAEuG,iBAAiB,CAAC;MAC5E,IAAI,OAAOjB,GAAG,CAACD,SAAS,KAAK,UAAU,EAAE;QACrC,OAAOC,GAAG;MACd,CAAC,MACI;QACD,OAAOpH,EAAE,CAACoH,GAAG,CAAC;MAClB;IACJ,CAAC,CAAC,CAAC,CAAC;EACR;EACA;AACJ;AACA;AACA;AACA;EACI6B,MAAMA,CAACnH,GAAG,EAAEuG,iBAAiB,EAAE;IAC3B,IAAI,CAAC1F,SAAS,CAACb,GAAG,CAAC,IAAI,CAACA,GAAG,CAACO,MAAM,EAAE;MAChC,MAAM,IAAIyG,KAAK,CAAC,0BAA0B,CAAC;IAC/C;IACA,OAAO3I,MAAM,CAACC,KAAK,CAAC,MAAM,IAAI,CAACyI,GAAG,CAAC/G,GAAG,EAAEuG,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAChD,YAAY,CAAC6B,IAAI,CAACzG,SAAS,CAAEuI,KAAK,IAAK;MACrG,MAAM5B,GAAG,GAAG,IAAI,CAACgB,eAAe,CAACY,KAAK,CAACnE,YAAY,EAAE/C,GAAG,EAAEuG,iBAAiB,CAAC;MAC5E,OAAOpI,YAAY,CAACmH,GAAG,CAAC,GAAGA,GAAG,GAAGpH,EAAE,CAACoH,GAAG,CAAC;IAC5C,CAAC,CAAC,CAAC,CAAC;EACR;EACA;AACJ;AACA;AACA;EACI8B,OAAOA,CAACpH,GAAG,EAAEuG,iBAAiB,EAAE;IAC5B,IAAI,CAAC1F,SAAS,CAACb,GAAG,CAAC,IAAI,CAACA,GAAG,CAACO,MAAM,EAAE;MAChC,MAAM,IAAIyG,KAAK,CAAC,0BAA0B,CAAC;IAC/C;IACA,IAAI1B,GAAG,GAAG,IAAI,CAACgB,eAAe,CAAC,IAAI,CAACvD,YAAY,CAAC,IAAI,CAACK,WAAW,CAAC,EAAEpD,GAAG,EAAEuG,iBAAiB,CAAC;IAC3F,IAAIpI,YAAY,CAACmH,GAAG,CAAC,EAAE;MACnB,IAAItF,GAAG,YAAYS,KAAK,EAAE;QACtB,IAAImG,GAAG,GAAG,CAAC,CAAC;QACZ5G,GAAG,CAACuB,OAAO,CAAC,CAACT,KAAK,EAAE+F,KAAK,KAAK;UAC1BD,GAAG,CAAC5G,GAAG,CAAC6G,KAAK,CAAC,CAAC,GAAG7G,GAAG,CAAC6G,KAAK,CAAC;QAChC,CAAC,CAAC;QACF,OAAOD,GAAG;MACd;MACA,OAAO5G,GAAG;IACd,CAAC,MACI;MACD,OAAOsF,GAAG;IACd;EACJ;EACA;AACJ;AACA;EACI+B,GAAGA,CAACrH,GAAG,EAAEc,KAAK,EAAE/B,IAAI,GAAG,IAAI,CAACqE,WAAW,EAAE;IACrC,IAAI,CAACL,YAAY,CAAChE,IAAI,CAAC,CAACiB,GAAG,CAAC,GAAG,IAAI,CAACgE,QAAQ,CAACnB,OAAO,CAAC/B,KAAK,EAAE/B,IAAI,CAAC;IACjE,IAAI,CAAC6G,WAAW,CAAC,CAAC;IAClB,IAAI,CAACtC,mBAAmB,CAAC2C,IAAI,CAAC;MAAElH,IAAI,EAAEA,IAAI;MAAEgE,YAAY,EAAE,IAAI,CAACA,YAAY,CAAChE,IAAI;IAAE,CAAC,CAAC;EACxF;EACA;AACJ;AACA;EACI2G,UAAUA,CAAC3G,IAAI,EAAE;IACb,IAAI,CAACqE,WAAW,GAAGrE,IAAI;IACvB,IAAI,CAACwE,YAAY,CAAC0C,IAAI,CAAC;MAAElH,IAAI,EAAEA,IAAI;MAAEgE,YAAY,EAAE,IAAI,CAACA,YAAY,CAAChE,IAAI;IAAE,CAAC,CAAC;IAC7E;IACA,IAAI,IAAI,CAACoE,WAAW,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACoC,iBAAiB,CAACxG,IAAI,CAAC;IAChC;EACJ;EACA;AACJ;AACA;EACIwG,iBAAiBA,CAACxG,IAAI,EAAE;IACpB,IAAI,CAACoE,WAAW,GAAGpE,IAAI;IACvB,IAAI,CAACyE,mBAAmB,CAACyC,IAAI,CAAC;MAAElH,IAAI,EAAEA,IAAI;MAAEgE,YAAY,EAAE,IAAI,CAACA,YAAY,CAAChE,IAAI;IAAE,CAAC,CAAC;EACxF;EACA;AACJ;AACA;EACIuI,UAAUA,CAACvI,IAAI,EAAE;IACb,IAAI,CAACwI,SAAS,CAACxI,IAAI,CAAC;IACpB,OAAO,IAAI,CAACD,cAAc,CAACC,IAAI,CAAC;EACpC;EACA;AACJ;AACA;EACIwI,SAASA,CAACxI,IAAI,EAAE;IACZ,IAAI,CAACgG,oBAAoB,CAAChG,IAAI,CAAC,GAAGoD,SAAS;IAC3C,IAAI,CAACY,YAAY,CAAChE,IAAI,CAAC,GAAGoD,SAAS;EACvC;EACA;AACJ;AACA;EACIqF,cAAcA,CAAA,EAAG;IACb,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,SAAS,KAAK,WAAW,EAAE;MAC1E,OAAOvF,SAAS;IACpB;IACA,IAAIwF,WAAW,GAAGF,MAAM,CAACC,SAAS,CAACE,SAAS,GAAGH,MAAM,CAACC,SAAS,CAACE,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;IACnFD,WAAW,GAAGA,WAAW,IAAIF,MAAM,CAACC,SAAS,CAACG,QAAQ,IAAIJ,MAAM,CAACC,SAAS,CAACI,eAAe,IAAIL,MAAM,CAACC,SAAS,CAACK,YAAY;IAC3H,IAAI,OAAOJ,WAAW,KAAK,WAAW,EAAE;MACpC,OAAOxF,SAAS;IACpB;IACA,IAAIwF,WAAW,CAACvB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACjCuB,WAAW,GAAGA,WAAW,CAAC1F,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3C;IACA,IAAI0F,WAAW,CAACvB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACjCuB,WAAW,GAAGA,WAAW,CAAC1F,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3C;IACA,OAAO0F,WAAW;EACtB;EACA;AACJ;AACA;EACIK,qBAAqBA,CAAA,EAAG;IACpB,IAAI,OAAOP,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,SAAS,KAAK,WAAW,EAAE;MAC1E,OAAOvF,SAAS;IACpB;IACA,IAAI8F,kBAAkB,GAAGR,MAAM,CAACC,SAAS,CAACE,SAAS,GAAGH,MAAM,CAACC,SAAS,CAACE,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;IAC1FK,kBAAkB,GAAGA,kBAAkB,IAAIR,MAAM,CAACC,SAAS,CAACG,QAAQ,IAAIJ,MAAM,CAACC,SAAS,CAACI,eAAe,IAAIL,MAAM,CAACC,SAAS,CAACK,YAAY;IACzI,OAAOE,kBAAkB;EAC7B;EACA,OAAOjJ,IAAI,YAAAkJ,yBAAA/I,iBAAA;IAAA,YAAAA,iBAAA,IAAwF0E,gBAAgB,EArpB1BpG,EAAE,CAAA0K,QAAA,CAqpB0CjF,cAAc,GArpB1DzF,EAAE,CAAA0K,QAAA,CAqpBqEvJ,eAAe,GArpBtFnB,EAAE,CAAA0K,QAAA,CAqpBiGxF,iBAAiB,GArpBpHlF,EAAE,CAAA0K,QAAA,CAqpB+H3G,eAAe,GArpBhJ/D,EAAE,CAAA0K,QAAA,CAqpB2JvI,yBAAyB,GArpBtLnC,EAAE,CAAA0K,QAAA,CAqpBiMzE,gBAAgB,GArpBnNjG,EAAE,CAAA0K,QAAA,CAqpB8N1E,SAAS,GArpBzOhG,EAAE,CAAA0K,QAAA,CAqpBoPvE,UAAU,GArpBhQnG,EAAE,CAAA0K,QAAA,CAqpB2QxE,gBAAgB;EAAA;EACtX,OAAOtE,KAAK,kBAtpB6E5B,EAAE,CAAA6B,kBAAA;IAAAC,KAAA,EAspBYsE,gBAAgB;IAAArE,OAAA,EAAhBqE,gBAAgB,CAAA7E;EAAA;AAC3H;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAxpB6FhC,EAAE,CAAAiC,iBAAA,CAwpBJmE,gBAAgB,EAAc,CAAC;IAC9GlE,IAAI,EAAEjC;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEiC,IAAI,EAAEuD;IAAe,CAAC,EAAE;MAAEvD,IAAI,EAAEf;IAAgB,CAAC,EAAE;MAAEe,IAAI,EAAEgD;IAAkB,CAAC,EAAE;MAAEhD,IAAI,EAAE6B;IAAgB,CAAC,EAAE;MAAE7B,IAAI,EAAEC;IAA0B,CAAC,EAAE;MAAED,IAAI,EAAEwC,SAAS;MAAEiG,UAAU,EAAE,CAAC;QAChNzI,IAAI,EAAE9B,MAAM;QACZwK,IAAI,EAAE,CAAC3E,gBAAgB;MAC3B,CAAC;IAAE,CAAC,EAAE;MAAE/D,IAAI,EAAEwC,SAAS;MAAEiG,UAAU,EAAE,CAAC;QAClCzI,IAAI,EAAE9B,MAAM;QACZwK,IAAI,EAAE,CAAC5E,SAAS;MACpB,CAAC;IAAE,CAAC,EAAE;MAAE9D,IAAI,EAAEwC,SAAS;MAAEiG,UAAU,EAAE,CAAC;QAClCzI,IAAI,EAAE9B,MAAM;QACZwK,IAAI,EAAE,CAACzE,UAAU;MACrB,CAAC;IAAE,CAAC,EAAE;MAAEjE,IAAI,EAAEwC,SAAS;MAAEiG,UAAU,EAAE,CAAC;QAClCzI,IAAI,EAAE9B,MAAM;QACZwK,IAAI,EAAE,CAAC1E,gBAAgB;MAC3B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAM2E,kBAAkB,CAAC;EACrBxB,gBAAgB;EAChByB,OAAO;EACPC,IAAI;EACJxI,GAAG;EACHyI,UAAU;EACVC,aAAa;EACbC,eAAe;EACfC,sBAAsB;EACtBC,sBAAsB;EACtB,IAAIC,SAASA,CAAC9I,GAAG,EAAE;IACf,IAAIA,GAAG,EAAE;MACL,IAAI,CAACA,GAAG,GAAGA,GAAG;MACd,IAAI,CAAC+I,UAAU,CAAC,CAAC;IACrB;EACJ;EACA,IAAIC,eAAeA,CAACjJ,MAAM,EAAE;IACxB,IAAI,CAACG,MAAM,CAAC,IAAI,CAACwI,aAAa,EAAE3I,MAAM,CAAC,EAAE;MACrC,IAAI,CAAC2I,aAAa,GAAG3I,MAAM;MAC3B,IAAI,CAACgJ,UAAU,CAAC,IAAI,CAAC;IACzB;EACJ;EACA/D,WAAWA,CAAC8B,gBAAgB,EAAEyB,OAAO,EAAEC,IAAI,EAAE;IACzC,IAAI,CAAC1B,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACyB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB;IACA,IAAI,CAAC,IAAI,CAACK,sBAAsB,EAAE;MAC9B,IAAI,CAACA,sBAAsB,GAAG,IAAI,CAAC/B,gBAAgB,CAACxD,mBAAmB,CAAC+B,SAAS,CAAE6B,KAAK,IAAK;QACzF,IAAIA,KAAK,CAACnI,IAAI,KAAK,IAAI,CAAC+H,gBAAgB,CAAC1D,WAAW,EAAE;UAClD,IAAI,CAAC2F,UAAU,CAAC,IAAI,EAAE7B,KAAK,CAACnE,YAAY,CAAC;QAC7C;MACJ,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAAC,IAAI,CAAC4F,eAAe,EAAE;MACvB,IAAI,CAACA,eAAe,GAAG,IAAI,CAAC7B,gBAAgB,CAACvD,YAAY,CAAC8B,SAAS,CAAE6B,KAAK,IAAK;QAC3E,IAAI,CAAC6B,UAAU,CAAC,IAAI,EAAE7B,KAAK,CAACnE,YAAY,CAAC;MAC7C,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAAC,IAAI,CAAC6F,sBAAsB,EAAE;MAC9B,IAAI,CAACA,sBAAsB,GAAG,IAAI,CAAC9B,gBAAgB,CAACtD,mBAAmB,CAAC6B,SAAS,CAAE6B,KAAK,IAAK;QACzF,IAAI,CAAC6B,UAAU,CAAC,IAAI,CAAC;MACzB,CAAC,CAAC;IACN;EACJ;EACAE,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACF,UAAU,CAAC,CAAC;EACrB;EACAA,UAAUA,CAACG,WAAW,GAAG,KAAK,EAAEnG,YAAY,EAAE;IAC1C,IAAIoG,KAAK,GAAG,IAAI,CAACZ,OAAO,CAACa,aAAa,CAACC,UAAU;IACjD;IACA,IAAI,CAACF,KAAK,CAAC5I,MAAM,EAAE;MACf;MACA,IAAI,CAAC+I,UAAU,CAAC,IAAI,CAACf,OAAO,CAACa,aAAa,EAAE,IAAI,CAACpJ,GAAG,CAAC;MACrDmJ,KAAK,GAAG,IAAI,CAACZ,OAAO,CAACa,aAAa,CAACC,UAAU;IACjD;IACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAAC5I,MAAM,EAAE,EAAEgJ,CAAC,EAAE;MACnC,IAAIC,IAAI,GAAGL,KAAK,CAACI,CAAC,CAAC;MACnB,IAAIC,IAAI,CAACC,QAAQ,KAAK,CAAC,EAAE;QAAE;QACvB,IAAIzJ,GAAG;QACP,IAAIkJ,WAAW,EAAE;UACbM,IAAI,CAACE,OAAO,GAAG,IAAI;QACvB;QACA,IAAI7I,SAAS,CAAC2I,IAAI,CAACG,SAAS,CAAC,EAAE;UAC3B3J,GAAG,GAAGwJ,IAAI,CAACG,SAAS;QACxB,CAAC,MACI,IAAI,IAAI,CAAC3J,GAAG,EAAE;UACfA,GAAG,GAAG,IAAI,CAACA,GAAG;QAClB,CAAC,MACI;UACD,IAAI4J,OAAO,GAAG,IAAI,CAACC,UAAU,CAACL,IAAI,CAAC;UACnC,IAAIM,cAAc,GAAGF,OAAO,CAACG,IAAI,CAAC,CAAC;UACnC,IAAID,cAAc,CAACvJ,MAAM,EAAE;YACvBiJ,IAAI,CAACG,SAAS,GAAGG,cAAc;YAC/B;YACA,IAAIF,OAAO,KAAKJ,IAAI,CAACQ,YAAY,EAAE;cAC/BhK,GAAG,GAAG8J,cAAc;cACpB;cACAN,IAAI,CAACS,eAAe,GAAGL,OAAO,IAAIJ,IAAI,CAACS,eAAe;YAC1D,CAAC,MACI,IAAIT,IAAI,CAACS,eAAe,EAAE;cAAE;cAC7B;cACAjK,GAAG,GAAGwJ,IAAI,CAACS,eAAe,CAACF,IAAI,CAAC,CAAC;YACrC,CAAC,MACI,IAAIH,OAAO,KAAKJ,IAAI,CAACQ,YAAY,EAAE;cACpC;cACAhK,GAAG,GAAG8J,cAAc;cACpB;cACAN,IAAI,CAACS,eAAe,GAAGL,OAAO,IAAIJ,IAAI,CAACS,eAAe;YAC1D;UACJ;QACJ;QACA,IAAI,CAACC,WAAW,CAAClK,GAAG,EAAEwJ,IAAI,EAAEzG,YAAY,CAAC;MAC7C;IACJ;EACJ;EACAmH,WAAWA,CAAClK,GAAG,EAAEwJ,IAAI,EAAEzG,YAAY,EAAE;IACjC,IAAI/C,GAAG,EAAE;MACL,IAAIwJ,IAAI,CAACE,OAAO,KAAK1J,GAAG,IAAI,IAAI,CAACyI,UAAU,KAAK,IAAI,CAACC,aAAa,EAAE;QAChE;MACJ;MACA,IAAI,CAACD,UAAU,GAAG,IAAI,CAACC,aAAa;MACpC,IAAIyB,aAAa,GAAI7E,GAAG,IAAK;QACzB,IAAIA,GAAG,KAAKtF,GAAG,EAAE;UACbwJ,IAAI,CAACE,OAAO,GAAG1J,GAAG;QACtB;QACA,IAAI,CAACwJ,IAAI,CAACS,eAAe,EAAE;UACvBT,IAAI,CAACS,eAAe,GAAG,IAAI,CAACJ,UAAU,CAACL,IAAI,CAAC;QAChD;QACAA,IAAI,CAACQ,YAAY,GAAGnJ,SAAS,CAACyE,GAAG,CAAC,GAAGA,GAAG,GAAIkE,IAAI,CAACS,eAAe,IAAIjK,GAAI;QACxE;QACA,IAAI,CAACsJ,UAAU,CAACE,IAAI,EAAE,IAAI,CAACxJ,GAAG,GAAGwJ,IAAI,CAACQ,YAAY,GAAGR,IAAI,CAACS,eAAe,CAAC5H,OAAO,CAACrC,GAAG,EAAEwJ,IAAI,CAACQ,YAAY,CAAC,CAAC;QAC1G,IAAI,CAACxB,IAAI,CAAC4B,YAAY,CAAC,CAAC;MAC5B,CAAC;MACD,IAAIvJ,SAAS,CAACkC,YAAY,CAAC,EAAE;QACzB,IAAIuC,GAAG,GAAG,IAAI,CAACwB,gBAAgB,CAACR,eAAe,CAACvD,YAAY,EAAE/C,GAAG,EAAE,IAAI,CAAC0I,aAAa,CAAC;QACtF,IAAIvK,YAAY,CAACmH,GAAG,CAAC,EAAE;UACnBA,GAAG,CAACD,SAAS,CAAC;YAAEM,IAAI,EAAEwE;UAAc,CAAC,CAAC;QAC1C,CAAC,MACI;UACDA,aAAa,CAAC7E,GAAG,CAAC;QACtB;MACJ,CAAC,MACI;QACD,IAAI,CAACwB,gBAAgB,CAACC,GAAG,CAAC/G,GAAG,EAAE,IAAI,CAAC0I,aAAa,CAAC,CAACrD,SAAS,CAAC8E,aAAa,CAAC;MAC/E;IACJ;EACJ;EACAN,UAAUA,CAACL,IAAI,EAAE;IACb,OAAO3I,SAAS,CAAC2I,IAAI,CAACa,WAAW,CAAC,GAAGb,IAAI,CAACa,WAAW,GAAGb,IAAI,CAACc,IAAI;EACrE;EACAhB,UAAUA,CAACE,IAAI,EAAEI,OAAO,EAAE;IACtB,IAAI/I,SAAS,CAAC2I,IAAI,CAACa,WAAW,CAAC,EAAE;MAC7Bb,IAAI,CAACa,WAAW,GAAGT,OAAO;IAC9B,CAAC,MACI;MACDJ,IAAI,CAACc,IAAI,GAAGV,OAAO;IACvB;EACJ;EACAW,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC5B,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAAC6B,WAAW,CAAC,CAAC;IACtC;IACA,IAAI,IAAI,CAAC5B,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC4B,WAAW,CAAC,CAAC;IAC7C;IACA,IAAI,IAAI,CAAC3B,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC2B,WAAW,CAAC,CAAC;IAC7C;EACJ;EACA,OAAOxL,IAAI,YAAAyL,2BAAAtL,iBAAA;IAAA,YAAAA,iBAAA,IAAwFmJ,kBAAkB,EAh0B5B7K,EAAE,CAAAiN,iBAAA,CAg0B4C7G,gBAAgB,GAh0B9DpG,EAAE,CAAAiN,iBAAA,CAg0ByEjN,EAAE,CAACkN,UAAU,GAh0BxFlN,EAAE,CAAAiN,iBAAA,CAg0BmGjN,EAAE,CAACmN,iBAAiB;EAAA;EAClN,OAAOC,IAAI,kBAj0B8EpN,EAAE,CAAAqN,iBAAA;IAAAnL,IAAA,EAi0BJ2I,kBAAkB;IAAAyC,SAAA;IAAAC,MAAA;MAAAlC,SAAA;MAAAE,eAAA;IAAA;EAAA;AAC7G;AACA;EAAA,QAAAvJ,SAAA,oBAAAA,SAAA,KAn0B6FhC,EAAE,CAAAiC,iBAAA,CAm0BJ4I,kBAAkB,EAAc,CAAC;IAChH3I,IAAI,EAAE7B,SAAS;IACfuK,IAAI,EAAE,CAAC;MACC4C,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEtL,IAAI,EAAEkE;IAAiB,CAAC,EAAE;MAAElE,IAAI,EAAElC,EAAE,CAACkN;IAAW,CAAC,EAAE;MAAEhL,IAAI,EAAElC,EAAE,CAACmN;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE9B,SAAS,EAAE,CAAC;MACzJnJ,IAAI,EAAE5B;IACV,CAAC,CAAC;IAAEiL,eAAe,EAAE,CAAC;MAClBrJ,IAAI,EAAE5B;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMmN,aAAa,CAAC;EAChBpC,SAAS;EACTN,IAAI;EACJ1H,KAAK,GAAG,EAAE;EACV4I,OAAO,GAAG,IAAI;EACdjB,UAAU,GAAG,EAAE;EACfnF,mBAAmB;EACnBC,YAAY;EACZC,mBAAmB;EACnBwB,WAAWA,CAAC8D,SAAS,EAAEN,IAAI,EAAE;IACzB,IAAI,CAACM,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACN,IAAI,GAAGA,IAAI;EACpB;EACA0B,WAAWA,CAAClK,GAAG,EAAEuG,iBAAiB,EAAExD,YAAY,EAAE;IAC9C,IAAIoH,aAAa,GAAI7E,GAAG,IAAK;MACzB,IAAI,CAACxE,KAAK,GAAGwE,GAAG,KAAKnD,SAAS,GAAGmD,GAAG,GAAGtF,GAAG;MAC1C,IAAI,CAAC0J,OAAO,GAAG1J,GAAG;MAClB,IAAI,CAACwI,IAAI,CAAC4B,YAAY,CAAC,CAAC;IAC5B,CAAC;IACD,IAAIrH,YAAY,EAAE;MACd,IAAIuC,GAAG,GAAG,IAAI,CAACwD,SAAS,CAACxC,eAAe,CAACvD,YAAY,EAAE/C,GAAG,EAAEuG,iBAAiB,CAAC;MAC9E,IAAIpI,YAAY,CAACmH,GAAG,CAACD,SAAS,CAAC,EAAE;QAC7BC,GAAG,CAACD,SAAS,CAAC8E,aAAa,CAAC;MAChC,CAAC,MACI;QACDA,aAAa,CAAC7E,GAAG,CAAC;MACtB;IACJ;IACA,IAAI,CAACwD,SAAS,CAAC/B,GAAG,CAAC/G,GAAG,EAAEuG,iBAAiB,CAAC,CAAClB,SAAS,CAAC8E,aAAa,CAAC;EACvE;EACAgB,SAASA,CAACC,KAAK,EAAE,GAAG/C,IAAI,EAAE;IACtB,IAAI,CAAC+C,KAAK,IAAI,CAACA,KAAK,CAAC7K,MAAM,EAAE;MACzB,OAAO6K,KAAK;IAChB;IACA;IACA,IAAIlL,MAAM,CAACkL,KAAK,EAAE,IAAI,CAAC1B,OAAO,CAAC,IAAIxJ,MAAM,CAACmI,IAAI,EAAE,IAAI,CAACI,UAAU,CAAC,EAAE;MAC9D,OAAO,IAAI,CAAC3H,KAAK;IACrB;IACA,IAAIyF,iBAAiB,GAAGpE,SAAS;IACjC,IAAItB,SAAS,CAACwH,IAAI,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC9H,MAAM,EAAE;MACnC,IAAI,OAAO8H,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC9H,MAAM,EAAE;QAC/C;QACA;QACA,IAAI8K,SAAS,GAAGhD,IAAI,CAAC,CAAC,CAAC,CAClBhG,OAAO,CAAC,kCAAkC,EAAE,OAAO,CAAC,CACpDA,OAAO,CAAC,sBAAsB,EAAE,OAAO,CAAC;QAC7C,IAAI;UACAkE,iBAAiB,GAAG+E,IAAI,CAACC,KAAK,CAACF,SAAS,CAAC;QAC7C,CAAC,CACD,OAAOG,CAAC,EAAE;UACN,MAAM,IAAIC,WAAW,CAAC,wEAAwEpD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5G;MACJ,CAAC,MACI,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,CAAC5H,KAAK,CAACC,OAAO,CAAC2H,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7D9B,iBAAiB,GAAG8B,IAAI,CAAC,CAAC,CAAC;MAC/B;IACJ;IACA;IACA,IAAI,CAACqB,OAAO,GAAG0B,KAAK;IACpB;IACA,IAAI,CAAC3C,UAAU,GAAGJ,IAAI;IACtB;IACA,IAAI,CAAC6B,WAAW,CAACkB,KAAK,EAAE7E,iBAAiB,CAAC;IAC1C;IACA,IAAI,CAACmF,QAAQ,CAAC,CAAC;IACf;IACA,IAAI,CAAC,IAAI,CAACpI,mBAAmB,EAAE;MAC3B,IAAI,CAACA,mBAAmB,GAAG,IAAI,CAACwF,SAAS,CAACxF,mBAAmB,CAAC+B,SAAS,CAAE6B,KAAK,IAAK;QAC/E,IAAI,IAAI,CAACwC,OAAO,IAAIxC,KAAK,CAACnI,IAAI,KAAK,IAAI,CAAC+J,SAAS,CAAC1F,WAAW,EAAE;UAC3D,IAAI,CAACsG,OAAO,GAAG,IAAI;UACnB,IAAI,CAACQ,WAAW,CAACkB,KAAK,EAAE7E,iBAAiB,EAAEW,KAAK,CAACnE,YAAY,CAAC;QAClE;MACJ,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAAC,IAAI,CAACQ,YAAY,EAAE;MACpB,IAAI,CAACA,YAAY,GAAG,IAAI,CAACuF,SAAS,CAACvF,YAAY,CAAC8B,SAAS,CAAE6B,KAAK,IAAK;QACjE,IAAI,IAAI,CAACwC,OAAO,EAAE;UACd,IAAI,CAACA,OAAO,GAAG,IAAI,CAAC,CAAC;UACrB,IAAI,CAACQ,WAAW,CAACkB,KAAK,EAAE7E,iBAAiB,EAAEW,KAAK,CAACnE,YAAY,CAAC;QAClE;MACJ,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAAC,IAAI,CAACS,mBAAmB,EAAE;MAC3B,IAAI,CAACA,mBAAmB,GAAG,IAAI,CAACsF,SAAS,CAACtF,mBAAmB,CAAC6B,SAAS,CAAC,MAAM;QAC1E,IAAI,IAAI,CAACqE,OAAO,EAAE;UACd,IAAI,CAACA,OAAO,GAAG,IAAI,CAAC,CAAC;UACrB,IAAI,CAACQ,WAAW,CAACkB,KAAK,EAAE7E,iBAAiB,CAAC;QAC9C;MACJ,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAACzF,KAAK;EACrB;EACA;AACJ;AACA;EACI4K,QAAQA,CAAA,EAAG;IACP,IAAI,OAAO,IAAI,CAACpI,mBAAmB,KAAK,WAAW,EAAE;MACjD,IAAI,CAACA,mBAAmB,CAACkH,WAAW,CAAC,CAAC;MACtC,IAAI,CAAClH,mBAAmB,GAAGnB,SAAS;IACxC;IACA,IAAI,OAAO,IAAI,CAACoB,YAAY,KAAK,WAAW,EAAE;MAC1C,IAAI,CAACA,YAAY,CAACiH,WAAW,CAAC,CAAC;MAC/B,IAAI,CAACjH,YAAY,GAAGpB,SAAS;IACjC;IACA,IAAI,OAAO,IAAI,CAACqB,mBAAmB,KAAK,WAAW,EAAE;MACjD,IAAI,CAACA,mBAAmB,CAACgH,WAAW,CAAC,CAAC;MACtC,IAAI,CAAChH,mBAAmB,GAAGrB,SAAS;IACxC;EACJ;EACAoI,WAAWA,CAAA,EAAG;IACV,IAAI,CAACmB,QAAQ,CAAC,CAAC;EACnB;EACA,OAAO1M,IAAI,YAAA2M,sBAAAxM,iBAAA;IAAA,YAAAA,iBAAA,IAAwF+L,aAAa,EAh8BvBzN,EAAE,CAAAiN,iBAAA,CAg8BuC7G,gBAAgB,OAh8BzDpG,EAAE,CAAAiN,iBAAA,CAg8BoEjN,EAAE,CAACmN,iBAAiB;EAAA;EACnL,OAAOgB,KAAK,kBAj8B6EnO,EAAE,CAAAoO,YAAA;IAAAC,IAAA;IAAAnM,IAAA,EAi8BMuL,aAAa;IAAAa,IAAA;EAAA;EAC9G,OAAO1M,KAAK,kBAl8B6E5B,EAAE,CAAA6B,kBAAA;IAAAC,KAAA,EAk8BY2L,aAAa;IAAA1L,OAAA,EAAb0L,aAAa,CAAAlM;EAAA;AACxH;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAp8B6FhC,EAAE,CAAAiC,iBAAA,CAo8BJwL,aAAa,EAAc,CAAC;IAC3GvL,IAAI,EAAEjC;EACV,CAAC,EAAE;IACCiC,IAAI,EAAE3B,IAAI;IACVqK,IAAI,EAAE,CAAC;MACCyD,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,KAAK,CAAC;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEpM,IAAI,EAAEkE;IAAiB,CAAC,EAAE;MAAElE,IAAI,EAAElC,EAAE,CAACmN;IAAkB,CAAC,CAAC;EAAE,CAAC;AAAA;AAEhH,MAAMoB,eAAe,CAAC;EAClB;AACJ;AACA;EACI,OAAOC,OAAOA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;IACxB,OAAO;MACHC,QAAQ,EAAEH,eAAe;MACzBI,SAAS,EAAE,CACPF,MAAM,CAACG,MAAM,IAAI;QAAEC,OAAO,EAAE1N,eAAe;QAAE2N,QAAQ,EAAE1N;MAAoB,CAAC,EAC5EqN,MAAM,CAAClI,QAAQ,IAAI;QAAEsI,OAAO,EAAE3J,iBAAiB;QAAE4J,QAAQ,EAAE3J;MAAsB,CAAC,EAClFsJ,MAAM,CAACjI,MAAM,IAAI;QAAEqI,OAAO,EAAE9K,eAAe;QAAE+K,QAAQ,EAAE9K;MAAuB,CAAC,EAC/EyK,MAAM,CAAChI,yBAAyB,IAAI;QAAEoI,OAAO,EAAE1M,yBAAyB;QAAE2M,QAAQ,EAAE1M;MAA8B,CAAC,EACnHqD,cAAc,EACd;QAAEoJ,OAAO,EAAE7I,SAAS;QAAE+I,QAAQ,EAAEN,MAAM,CAAC9H;MAAQ,CAAC,EAChD;QAAEkI,OAAO,EAAE5I,gBAAgB;QAAE8I,QAAQ,EAAEN,MAAM,CAAC/H;MAAe,CAAC,EAC9D;QAAEmI,OAAO,EAAE1I,UAAU;QAAE4I,QAAQ,EAAEN,MAAM,CAAC7H;MAAO,CAAC,EAChD;QAAEiI,OAAO,EAAE3I,gBAAgB;QAAE6I,QAAQ,EAAEN,MAAM,CAACjH;MAAgB,CAAC,EAC/DpB,gBAAgB;IAExB,CAAC;EACL;EACA;AACJ;AACA;EACI,OAAO4I,QAAQA,CAACP,MAAM,GAAG,CAAC,CAAC,EAAE;IACzB,OAAO;MACHC,QAAQ,EAAEH,eAAe;MACzBI,SAAS,EAAE,CACPF,MAAM,CAACG,MAAM,IAAI;QAAEC,OAAO,EAAE1N,eAAe;QAAE2N,QAAQ,EAAE1N;MAAoB,CAAC,EAC5EqN,MAAM,CAAClI,QAAQ,IAAI;QAAEsI,OAAO,EAAE3J,iBAAiB;QAAE4J,QAAQ,EAAE3J;MAAsB,CAAC,EAClFsJ,MAAM,CAACjI,MAAM,IAAI;QAAEqI,OAAO,EAAE9K,eAAe;QAAE+K,QAAQ,EAAE9K;MAAuB,CAAC,EAC/EyK,MAAM,CAAChI,yBAAyB,IAAI;QAAEoI,OAAO,EAAE1M,yBAAyB;QAAE2M,QAAQ,EAAE1M;MAA8B,CAAC,EACnH;QAAEyM,OAAO,EAAE7I,SAAS;QAAE+I,QAAQ,EAAEN,MAAM,CAAC9H;MAAQ,CAAC,EAChD;QAAEkI,OAAO,EAAE5I,gBAAgB;QAAE8I,QAAQ,EAAEN,MAAM,CAAC/H;MAAe,CAAC,EAC9D;QAAEmI,OAAO,EAAE1I,UAAU;QAAE4I,QAAQ,EAAEN,MAAM,CAAC7H;MAAO,CAAC,EAChD;QAAEiI,OAAO,EAAE3I,gBAAgB;QAAE6I,QAAQ,EAAEN,MAAM,CAACjH;MAAgB,CAAC,EAC/DpB,gBAAgB;IAExB,CAAC;EACL;EACA,OAAO7E,IAAI,YAAA0N,wBAAAvN,iBAAA;IAAA,YAAAA,iBAAA,IAAwF6M,eAAe;EAAA;EAClH,OAAOW,IAAI,kBAv/B8ElP,EAAE,CAAAmP,gBAAA;IAAAjN,IAAA,EAu/BSqM;EAAe;EAGnH,OAAOa,IAAI,kBA1/B8EpP,EAAE,CAAAqP,gBAAA;AA2/B/F;AACA;EAAA,QAAArN,SAAA,oBAAAA,SAAA,KA5/B6FhC,EAAE,CAAAiC,iBAAA,CA4/BJsM,eAAe,EAAc,CAAC;IAC7GrM,IAAI,EAAE1B,QAAQ;IACdoK,IAAI,EAAE,CAAC;MACC0E,YAAY,EAAE,CACV7B,aAAa,EACb5C,kBAAkB,CACrB;MACD0E,OAAO,EAAE,CACL9B,aAAa,EACb5C,kBAAkB;IAE1B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS3E,gBAAgB,EAAE9D,6BAA6B,EAAED,yBAAyB,EAAE+C,iBAAiB,EAAElB,sBAAsB,EAAE6G,kBAAkB,EAAE1F,qBAAqB,EAAE/D,mBAAmB,EAAED,eAAe,EAAEoN,eAAe,EAAExK,eAAe,EAAE0J,aAAa,EAAErH,gBAAgB,EAAEX,cAAc,EAAEQ,gBAAgB,EAAEE,UAAU,EAAEH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}