{"ast": null, "code": "import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient, HttpClient } from '@angular/common/http';\nimport { importProvidersFrom } from '@angular/core';\nimport { IonicModule } from '@ionic/angular';\nimport { TranslateModule, TranslateLoader } from '@ngx-translate/core';\nimport { TranslateHttpLoader } from '@ngx-translate/http-loader';\nimport { routes } from './app/app.routes';\n// Translation loader factory\nexport function HttpLoaderFactory(http) {\n  return new TranslateHttpLoader(http, './assets/i18n/', '.json');\n}\nbootstrapApplication(AppComponent, {\n  providers: [provideRouter(routes), provideHttpClient(), importProvidersFrom(IonicModule.forRoot(), TranslateModule.forRoot({\n    loader: {\n      provide: TranslateLoader,\n      useFactory: HttpLoaderFactory,\n      deps: [HttpClient]\n    },\n    defaultLanguage: 'ar'\n  }))]\n}).catch(err => console.error(err));", "map": {"version": 3, "names": ["bootstrapApplication", "AppComponent", "provideRouter", "provideHttpClient", "HttpClient", "importProvidersFrom", "IonicModule", "TranslateModule", "Translate<PERSON><PERSON><PERSON>", "TranslateHttpLoader", "routes", "HttpLoaderFactory", "http", "providers", "forRoot", "loader", "provide", "useFactory", "deps", "defaultLanguage", "catch", "err", "console", "error"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/src/main.ts"], "sourcesContent": ["import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient, HttpClient } from '@angular/common/http';\nimport { importProvidersFrom } from '@angular/core';\nimport { IonicModule } from '@ionic/angular';\nimport { TranslateModule, TranslateLoader } from '@ngx-translate/core';\nimport { TranslateHttpLoader } from '@ngx-translate/http-loader';\nimport { routes } from './app/app.routes';\n\n// Translation loader factory\nexport function HttpLoaderFactory(http: HttpClient) {\n  return new TranslateHttpLoader(http, './assets/i18n/', '.json');\n}\n\nbootstrapApplication(AppComponent, {\n  providers: [\n    provideRouter(routes),\n    provideHttpClient(),\n    importProvidersFrom(\n      IonicModule.forRoot(),\n      TranslateModule.forRoot({\n        loader: {\n          provide: TranslateLoader,\n          useFactory: HttpLoaderFactory,\n          deps: [HttpClient]\n        },\n        defaultLanguage: 'ar'\n      })\n    )\n  ]\n}).catch(err => console.error(err));\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,EAAEC,UAAU,QAAQ,sBAAsB;AACpE,SAASC,mBAAmB,QAAQ,eAAe;AACnD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,eAAe,EAAEC,eAAe,QAAQ,qBAAqB;AACtE,SAASC,mBAAmB,QAAQ,4BAA4B;AAChE,SAASC,MAAM,QAAQ,kBAAkB;AAEzC;AACA,OAAM,SAAUC,iBAAiBA,CAACC,IAAgB;EAChD,OAAO,IAAIH,mBAAmB,CAACG,IAAI,EAAE,gBAAgB,EAAE,OAAO,CAAC;AACjE;AAEAZ,oBAAoB,CAACC,YAAY,EAAE;EACjCY,SAAS,EAAE,CACTX,aAAa,CAACQ,MAAM,CAAC,EACrBP,iBAAiB,EAAE,EACnBE,mBAAmB,CACjBC,WAAW,CAACQ,OAAO,EAAE,EACrBP,eAAe,CAACO,OAAO,CAAC;IACtBC,MAAM,EAAE;MACNC,OAAO,EAAER,eAAe;MACxBS,UAAU,EAAEN,iBAAiB;MAC7BO,IAAI,EAAE,CAACd,UAAU;KAClB;IACDe,eAAe,EAAE;GAClB,CAAC,CACH;CAEJ,CAAC,CAACC,KAAK,CAACC,GAAG,IAAIC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}