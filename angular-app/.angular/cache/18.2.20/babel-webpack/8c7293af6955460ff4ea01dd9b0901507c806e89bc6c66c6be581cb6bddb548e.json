{"ast": null, "code": "// src/app-globals/index.ts\nvar globalScripts = /* default */\n() => {};\nvar globalStyles = /* default */\n\"\";\nexport { globalScripts, globalStyles };", "map": {"version": 3, "names": ["globalScripts", "globalStyles"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@stencil/core/internal/app-globals/index.js"], "sourcesContent": ["// src/app-globals/index.ts\nvar globalScripts = (\n  /* default */\n  () => {\n  }\n);\nvar globalStyles = (\n  /* default */\n  \"\"\n);\nexport {\n  globalScripts,\n  globalStyles\n};\n"], "mappings": "AAAA;AACA,IAAIA,aAAa,GACf;AACAA,CAAA,KAAM,CACN,CACD;AACD,IAAIC,YAAY,GACd;AACA,EACD;AACD,SACED,aAAa,EACbC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}