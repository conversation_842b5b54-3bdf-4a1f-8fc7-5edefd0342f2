{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class IframeCommunicationService {\n  constructor() {\n    this.evaluationDataSubject = new BehaviorSubject(null);\n    this.evaluationData$ = this.evaluationDataSubject.asObservable();\n    this.initializeMessageListener();\n    this.requestEvaluationData();\n  }\n  initializeMessageListener() {\n    window.addEventListener('message', event => {\n      // Verify origin for security (adjust as needed)\n      if (event.origin !== window.location.origin && !event.origin.includes('localhost')) {\n        return;\n      }\n      if (event.data && event.data.type === 'EVALUATION_DATA') {\n        this.evaluationDataSubject.next(event.data.payload);\n      }\n    });\n  }\n  requestEvaluationData() {\n    // Try to get data from URL parameters first\n    const urlParams = new URLSearchParams(window.location.search);\n    const token = urlParams.get('token');\n    const driverName = urlParams.get('driverName');\n    const driverId = urlParams.get('driverId');\n    const linkId = urlParams.get('linkId');\n    const expiryDate = urlParams.get('expiryDate');\n    if (token && driverName && driverId && linkId && expiryDate) {\n      const evaluationData = {\n        token,\n        driverName,\n        driverId: parseInt(driverId),\n        linkId: parseInt(linkId),\n        expiryDate\n      };\n      this.evaluationDataSubject.next(evaluationData);\n    } else {\n      // Request data from parent window\n      if (window.parent !== window) {\n        window.parent.postMessage({\n          type: 'REQUEST_EVALUATION_DATA'\n        }, '*');\n      }\n    }\n  }\n  sendMessage(type, payload) {\n    if (window.parent !== window) {\n      window.parent.postMessage({\n        type,\n        payload\n      }, '*');\n    }\n  }\n  notifyEvaluationComplete(result) {\n    this.sendMessage('EVALUATION_COMPLETE', result);\n  }\n  notifyEvaluationError(error) {\n    const errorPayload = typeof error === 'string' ? {\n      error\n    } : error;\n    this.sendMessage('EVALUATION_ERROR', errorPayload);\n  }\n  static {\n    this.ɵfac = function IframeCommunicationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || IframeCommunicationService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: IframeCommunicationService,\n      factory: IframeCommunicationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "IframeCommunicationService", "constructor", "evaluationDataSubject", "evaluationData$", "asObservable", "initializeMessageListener", "requestEvaluationData", "window", "addEventListener", "event", "origin", "location", "includes", "data", "type", "next", "payload", "urlParams", "URLSearchParams", "search", "token", "get", "<PERSON><PERSON><PERSON>", "driverId", "linkId", "expiryDate", "evaluationData", "parseInt", "parent", "postMessage", "sendMessage", "notifyEvaluationComplete", "result", "notifyEvaluationError", "error", "errorPayload", "factory", "ɵfac", "providedIn"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/src/app/services/iframe-communication.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable } from 'rxjs';\nimport { EvaluationData } from '../models/evaluation.models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class IframeCommunicationService {\n  private evaluationDataSubject = new BehaviorSubject<EvaluationData | null>(null);\n  public evaluationData$: Observable<EvaluationData | null> = this.evaluationDataSubject.asObservable();\n\n  constructor() {\n    this.initializeMessageListener();\n    this.requestEvaluationData();\n  }\n\n  private initializeMessageListener(): void {\n    window.addEventListener('message', (event) => {\n      // Verify origin for security (adjust as needed)\n      if (event.origin !== window.location.origin && !event.origin.includes('localhost')) {\n        return;\n      }\n\n      if (event.data && event.data.type === 'EVALUATION_DATA') {\n        this.evaluationDataSubject.next(event.data.payload);\n      }\n    });\n  }\n\n  private requestEvaluationData(): void {\n    // Try to get data from URL parameters first\n    const urlParams = new URLSearchParams(window.location.search);\n    const token = urlParams.get('token');\n    const driverName = urlParams.get('driverName');\n    const driverId = urlParams.get('driverId');\n    const linkId = urlParams.get('linkId');\n    const expiryDate = urlParams.get('expiryDate');\n\n    if (token && driverName && driverId && linkId && expiryDate) {\n      const evaluationData: EvaluationData = {\n        token,\n        driverName,\n        driverId: parseInt(driverId),\n        linkId: parseInt(linkId),\n        expiryDate\n      };\n      this.evaluationDataSubject.next(evaluationData);\n    } else {\n      // Request data from parent window\n      if (window.parent !== window) {\n        window.parent.postMessage({\n          type: 'REQUEST_EVALUATION_DATA'\n        }, '*');\n      }\n    }\n  }\n\n  sendMessage(type: string, payload: any): void {\n    if (window.parent !== window) {\n      window.parent.postMessage({\n        type,\n        payload\n      }, '*');\n    }\n  }\n\n  notifyEvaluationComplete(result: any): void {\n    this.sendMessage('EVALUATION_COMPLETE', result);\n  }\n\n  notifyEvaluationError(error: string | { error: string; isArabic?: boolean }): void {\n    const errorPayload = typeof error === 'string' ? { error } : error;\n    this.sendMessage('EVALUATION_ERROR', errorPayload);\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAoB,MAAM;;AAMlD,OAAM,MAAOC,0BAA0B;EAIrCC,YAAA;IAHQ,KAAAC,qBAAqB,GAAG,IAAIH,eAAe,CAAwB,IAAI,CAAC;IACzE,KAAAI,eAAe,GAAsC,IAAI,CAACD,qBAAqB,CAACE,YAAY,EAAE;IAGnG,IAAI,CAACC,yBAAyB,EAAE;IAChC,IAAI,CAACC,qBAAqB,EAAE;EAC9B;EAEQD,yBAAyBA,CAAA;IAC/BE,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAGC,KAAK,IAAI;MAC3C;MACA,IAAIA,KAAK,CAACC,MAAM,KAAKH,MAAM,CAACI,QAAQ,CAACD,MAAM,IAAI,CAACD,KAAK,CAACC,MAAM,CAACE,QAAQ,CAAC,WAAW,CAAC,EAAE;QAClF;MACF;MAEA,IAAIH,KAAK,CAACI,IAAI,IAAIJ,KAAK,CAACI,IAAI,CAACC,IAAI,KAAK,iBAAiB,EAAE;QACvD,IAAI,CAACZ,qBAAqB,CAACa,IAAI,CAACN,KAAK,CAACI,IAAI,CAACG,OAAO,CAAC;MACrD;IACF,CAAC,CAAC;EACJ;EAEQV,qBAAqBA,CAAA;IAC3B;IACA,MAAMW,SAAS,GAAG,IAAIC,eAAe,CAACX,MAAM,CAACI,QAAQ,CAACQ,MAAM,CAAC;IAC7D,MAAMC,KAAK,GAAGH,SAAS,CAACI,GAAG,CAAC,OAAO,CAAC;IACpC,MAAMC,UAAU,GAAGL,SAAS,CAACI,GAAG,CAAC,YAAY,CAAC;IAC9C,MAAME,QAAQ,GAAGN,SAAS,CAACI,GAAG,CAAC,UAAU,CAAC;IAC1C,MAAMG,MAAM,GAAGP,SAAS,CAACI,GAAG,CAAC,QAAQ,CAAC;IACtC,MAAMI,UAAU,GAAGR,SAAS,CAACI,GAAG,CAAC,YAAY,CAAC;IAE9C,IAAID,KAAK,IAAIE,UAAU,IAAIC,QAAQ,IAAIC,MAAM,IAAIC,UAAU,EAAE;MAC3D,MAAMC,cAAc,GAAmB;QACrCN,KAAK;QACLE,UAAU;QACVC,QAAQ,EAAEI,QAAQ,CAACJ,QAAQ,CAAC;QAC5BC,MAAM,EAAEG,QAAQ,CAACH,MAAM,CAAC;QACxBC;OACD;MACD,IAAI,CAACvB,qBAAqB,CAACa,IAAI,CAACW,cAAc,CAAC;IACjD,CAAC,MAAM;MACL;MACA,IAAInB,MAAM,CAACqB,MAAM,KAAKrB,MAAM,EAAE;QAC5BA,MAAM,CAACqB,MAAM,CAACC,WAAW,CAAC;UACxBf,IAAI,EAAE;SACP,EAAE,GAAG,CAAC;MACT;IACF;EACF;EAEAgB,WAAWA,CAAChB,IAAY,EAAEE,OAAY;IACpC,IAAIT,MAAM,CAACqB,MAAM,KAAKrB,MAAM,EAAE;MAC5BA,MAAM,CAACqB,MAAM,CAACC,WAAW,CAAC;QACxBf,IAAI;QACJE;OACD,EAAE,GAAG,CAAC;IACT;EACF;EAEAe,wBAAwBA,CAACC,MAAW;IAClC,IAAI,CAACF,WAAW,CAAC,qBAAqB,EAAEE,MAAM,CAAC;EACjD;EAEAC,qBAAqBA,CAACC,KAAqD;IACzE,MAAMC,YAAY,GAAG,OAAOD,KAAK,KAAK,QAAQ,GAAG;MAAEA;IAAK,CAAE,GAAGA,KAAK;IAClE,IAAI,CAACJ,WAAW,CAAC,kBAAkB,EAAEK,YAAY,CAAC;EACpD;;;uCAlEWnC,0BAA0B;IAAA;EAAA;;;aAA1BA,0BAA0B;MAAAoC,OAAA,EAA1BpC,0BAA0B,CAAAqC,IAAA;MAAAC,UAAA,EAFzB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}