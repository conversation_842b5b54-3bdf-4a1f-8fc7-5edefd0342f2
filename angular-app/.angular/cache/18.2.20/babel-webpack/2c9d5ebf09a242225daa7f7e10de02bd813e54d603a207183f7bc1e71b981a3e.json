{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst win = typeof window !== 'undefined' ? window : undefined;\nconst doc = typeof document !== 'undefined' ? document : undefined;\nexport { doc as d, win as w };", "map": {"version": 3, "names": ["win", "window", "undefined", "doc", "document", "d", "w"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@ionic/core/components/index9.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst win = typeof window !== 'undefined' ? window : undefined;\nconst doc = typeof document !== 'undefined' ? document : undefined;\n\nexport { doc as d, win as w };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,GAAG,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAGC,SAAS;AAC9D,MAAMC,GAAG,GAAG,OAAOC,QAAQ,KAAK,WAAW,GAAGA,QAAQ,GAAGF,SAAS;AAElE,SAASC,GAAG,IAAIE,CAAC,EAAEL,GAAG,IAAIM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}