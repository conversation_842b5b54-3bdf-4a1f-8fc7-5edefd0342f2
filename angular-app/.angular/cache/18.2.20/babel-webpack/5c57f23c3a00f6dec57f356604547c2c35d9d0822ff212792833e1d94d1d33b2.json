{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, o as printIonError, m as printIonWarning, k as getElement, e as getIonMode, h, j as Host } from './index-B_U9CtaY.js';\nimport { c as componentOnReady, p as debounce } from './helpers-1O4D2b7y.js';\nimport { o as openURL, c as createColorClasses } from './theme-DiVJyqlX.js';\nconst Route = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionRouteDataChanged = createEvent(this, \"ionRouteDataChanged\", 7);\n    /**\n     * Relative path that needs to match in order for this route to apply.\n     *\n     * Accepts paths similar to expressjs so that you can define parameters\n     * in the url /foo/:bar where bar would be available in incoming props.\n     */\n    this.url = '';\n  }\n  onUpdate(newValue) {\n    this.ionRouteDataChanged.emit(newValue);\n  }\n  onComponentProps(newValue, oldValue) {\n    if (newValue === oldValue) {\n      return;\n    }\n    const keys1 = newValue ? Object.keys(newValue) : [];\n    const keys2 = oldValue ? Object.keys(oldValue) : [];\n    if (keys1.length !== keys2.length) {\n      this.onUpdate(newValue);\n      return;\n    }\n    for (const key of keys1) {\n      if (newValue[key] !== oldValue[key]) {\n        this.onUpdate(newValue);\n        return;\n      }\n    }\n  }\n  connectedCallback() {\n    this.ionRouteDataChanged.emit();\n  }\n  static get watchers() {\n    return {\n      \"url\": [\"onUpdate\"],\n      \"component\": [\"onUpdate\"],\n      \"componentProps\": [\"onComponentProps\"]\n    };\n  }\n};\nconst RouteRedirect = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionRouteRedirectChanged = createEvent(this, \"ionRouteRedirectChanged\", 7);\n  }\n  propDidChange() {\n    this.ionRouteRedirectChanged.emit();\n  }\n  connectedCallback() {\n    this.ionRouteRedirectChanged.emit();\n  }\n  static get watchers() {\n    return {\n      \"from\": [\"propDidChange\"],\n      \"to\": [\"propDidChange\"]\n    };\n  }\n};\nconst ROUTER_INTENT_NONE = 'root';\nconst ROUTER_INTENT_FORWARD = 'forward';\nconst ROUTER_INTENT_BACK = 'back';\n\n/** Join the non empty segments with \"/\". */\nconst generatePath = segments => {\n  const path = segments.filter(s => s.length > 0).join('/');\n  return '/' + path;\n};\nconst generateUrl = (segments, useHash, queryString) => {\n  let url = generatePath(segments);\n  if (useHash) {\n    url = '#' + url;\n  }\n  if (queryString !== undefined) {\n    url += '?' + queryString;\n  }\n  return url;\n};\nconst writeSegments = (history, root, useHash, segments, direction, state, queryString) => {\n  const url = generateUrl([...parsePath(root).segments, ...segments], useHash, queryString);\n  if (direction === ROUTER_INTENT_FORWARD) {\n    history.pushState(state, '', url);\n  } else {\n    history.replaceState(state, '', url);\n  }\n};\n/**\n * Transforms a chain to a list of segments.\n *\n * Notes:\n * - parameter segments of the form :param are replaced with their value,\n * - null is returned when a value is missing for any parameter segment.\n */\nconst chainToSegments = chain => {\n  const segments = [];\n  for (const route of chain) {\n    for (const segment of route.segments) {\n      if (segment[0] === ':') {\n        // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n        const param = route.params && route.params[segment.slice(1)];\n        if (!param) {\n          return null;\n        }\n        segments.push(param);\n      } else if (segment !== '') {\n        segments.push(segment);\n      }\n    }\n  }\n  return segments;\n};\n/**\n * Removes the prefix segments from the path segments.\n *\n * Return:\n * - null when the path segments do not start with the passed prefix,\n * - the path segments after the prefix otherwise.\n */\nconst removePrefix = (prefix, segments) => {\n  if (prefix.length > segments.length) {\n    return null;\n  }\n  if (prefix.length <= 1 && prefix[0] === '') {\n    return segments;\n  }\n  for (let i = 0; i < prefix.length; i++) {\n    if (prefix[i] !== segments[i]) {\n      return null;\n    }\n  }\n  if (segments.length === prefix.length) {\n    return [''];\n  }\n  return segments.slice(prefix.length);\n};\nconst readSegments = (loc, root, useHash) => {\n  const prefix = parsePath(root).segments;\n  const pathname = useHash ? loc.hash.slice(1) : loc.pathname;\n  const segments = parsePath(pathname).segments;\n  return removePrefix(prefix, segments);\n};\n/**\n * Parses the path to:\n * - segments an array of '/' separated parts,\n * - queryString (undefined when no query string).\n */\nconst parsePath = path => {\n  let segments = [''];\n  let queryString;\n  if (path != null) {\n    const qsStart = path.indexOf('?');\n    if (qsStart > -1) {\n      queryString = path.substring(qsStart + 1);\n      path = path.substring(0, qsStart);\n    }\n    segments = path.split('/').map(s => s.trim()).filter(s => s.length > 0);\n    if (segments.length === 0) {\n      segments = [''];\n    }\n  }\n  return {\n    segments,\n    queryString\n  };\n};\nconst printRoutes = routes => {\n  console.group(`[ion-core] ROUTES[${routes.length}]`);\n  for (const chain of routes) {\n    const segments = [];\n    chain.forEach(r => segments.push(...r.segments));\n    const ids = chain.map(r => r.id);\n    console.debug(`%c ${generatePath(segments)}`, 'font-weight: bold; padding-left: 20px', '=>\\t', `(${ids.join(', ')})`);\n  }\n  console.groupEnd();\n};\nconst printRedirects = redirects => {\n  console.group(`[ion-core] REDIRECTS[${redirects.length}]`);\n  for (const redirect of redirects) {\n    if (redirect.to) {\n      console.debug('FROM: ', `$c ${generatePath(redirect.from)}`, 'font-weight: bold', ' TO: ', `$c ${generatePath(redirect.to.segments)}`, 'font-weight: bold');\n    }\n  }\n  console.groupEnd();\n};\n\n/**\n * Activates the passed route chain.\n *\n * There must be exactly one outlet per route entry in the chain.\n *\n * The methods calls setRouteId on each of the outlet with the corresponding route entry in the chain.\n * setRouteId will create or select the view in the outlet.\n */\nconst _writeNavState = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (root, chain, direction, index, changed = false, animation) {\n    try {\n      // find next navigation outlet in the DOM\n      const outlet = searchNavNode(root);\n      // make sure we can continue interacting the DOM, otherwise abort\n      if (index >= chain.length || !outlet) {\n        return changed;\n      }\n      yield new Promise(resolve => componentOnReady(outlet, resolve));\n      const route = chain[index];\n      const result = yield outlet.setRouteId(route.id, route.params, direction, animation);\n      // if the outlet changed the page, reset navigation to neutral (no direction)\n      // this means nested outlets will not animate\n      if (result.changed) {\n        direction = ROUTER_INTENT_NONE;\n        changed = true;\n      }\n      // recursively set nested outlets\n      changed = yield _writeNavState(result.element, chain, direction, index + 1, changed, animation);\n      // once all nested outlets are visible let's make the parent visible too,\n      // using markVisible prevents flickering\n      if (result.markVisible) {\n        yield result.markVisible();\n      }\n      return changed;\n    } catch (e) {\n      printIonError('[ion-router] - Exception in writeNavState:', e);\n      return false;\n    }\n  });\n  return function writeNavState(_x, _x2, _x3, _x4) {\n    return _ref.apply(this, arguments);\n  };\n}();\n/**\n * Recursively walks the outlet in the DOM.\n *\n * The function returns a list of RouteID corresponding to each of the outlet and the last outlet without a RouteID.\n */\nconst readNavState = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(function* (root) {\n    const ids = [];\n    let outlet;\n    let node = root;\n    // eslint-disable-next-line no-cond-assign\n    while (outlet = searchNavNode(node)) {\n      const id = yield outlet.getRouteId();\n      if (id) {\n        node = id.element;\n        id.element = undefined;\n        ids.push(id);\n      } else {\n        break;\n      }\n    }\n    return {\n      ids,\n      outlet\n    };\n  });\n  return function readNavState(_x5) {\n    return _ref2.apply(this, arguments);\n  };\n}();\nconst waitUntilNavNode = () => {\n  if (searchNavNode(document.body)) {\n    return Promise.resolve();\n  }\n  return new Promise(resolve => {\n    window.addEventListener('ionNavWillLoad', () => resolve(), {\n      once: true\n    });\n  });\n};\n/** Selector for all the outlets supported by the router. */\nconst OUTLET_SELECTOR = ':not([no-router]) ion-nav, :not([no-router]) ion-tabs, :not([no-router]) ion-router-outlet';\nconst searchNavNode = root => {\n  if (!root) {\n    return undefined;\n  }\n  if (root.matches(OUTLET_SELECTOR)) {\n    return root;\n  }\n  const outlet = root.querySelector(OUTLET_SELECTOR);\n  return outlet !== null && outlet !== void 0 ? outlet : undefined;\n};\n\n/**\n * Returns whether the given redirect matches the given path segments.\n *\n * A redirect matches when the segments of the path and redirect.from are equal.\n * Note that segments are only checked until redirect.from contains a '*' which matches any path segment.\n * The path ['some', 'path', 'to', 'page'] matches both ['some', 'path', 'to', 'page'] and ['some', 'path', '*'].\n */\nconst matchesRedirect = (segments, redirect) => {\n  const {\n    from,\n    to\n  } = redirect;\n  if (to === undefined) {\n    return false;\n  }\n  if (from.length > segments.length) {\n    return false;\n  }\n  for (let i = 0; i < from.length; i++) {\n    const expected = from[i];\n    if (expected === '*') {\n      return true;\n    }\n    if (expected !== segments[i]) {\n      return false;\n    }\n  }\n  return from.length === segments.length;\n};\n/** Returns the first redirect matching the path segments or undefined when no match found. */\nconst findRouteRedirect = (segments, redirects) => {\n  return redirects.find(redirect => matchesRedirect(segments, redirect));\n};\nconst matchesIDs = (ids, chain) => {\n  const len = Math.min(ids.length, chain.length);\n  let score = 0;\n  for (let i = 0; i < len; i++) {\n    const routeId = ids[i];\n    const routeChain = chain[i];\n    // Skip results where the route id does not match the chain at the same index\n    if (routeId.id.toLowerCase() !== routeChain.id) {\n      break;\n    }\n    if (routeId.params) {\n      const routeIdParams = Object.keys(routeId.params);\n      // Only compare routes with the chain that have the same number of parameters.\n      if (routeIdParams.length === routeChain.segments.length) {\n        // Maps the route's params into a path based on the path variable names,\n        // to compare against the route chain format.\n        //\n        // Before:\n        // ```ts\n        // {\n        //  params: {\n        //    s1: 'a',\n        //    s2: 'b'\n        //  }\n        // }\n        // ```\n        //\n        // After:\n        // ```ts\n        // [':s1',':s2']\n        // ```\n        //\n        const pathWithParams = routeIdParams.map(key => `:${key}`);\n        for (let j = 0; j < pathWithParams.length; j++) {\n          // Skip results where the path variable is not a match\n          if (pathWithParams[j].toLowerCase() !== routeChain.segments[j]) {\n            break;\n          }\n          // Weight path matches for the same index higher.\n          score++;\n        }\n      }\n    }\n    // Weight id matches\n    score++;\n  }\n  return score;\n};\n/**\n * Matches the segments against the chain.\n *\n * Returns:\n * - null when there is no match,\n * - a chain with the params properties updated with the parameter segments on match.\n */\nconst matchesSegments = (segments, chain) => {\n  const inputSegments = new RouterSegments(segments);\n  let matchesDefault = false;\n  let allparams;\n  for (let i = 0; i < chain.length; i++) {\n    const chainSegments = chain[i].segments;\n    if (chainSegments[0] === '') {\n      matchesDefault = true;\n    } else {\n      for (const segment of chainSegments) {\n        const data = inputSegments.next();\n        // data param\n        if (segment[0] === ':') {\n          if (data === '') {\n            return null;\n          }\n          allparams = allparams || [];\n          const params = allparams[i] || (allparams[i] = {});\n          params[segment.slice(1)] = data;\n        } else if (data !== segment) {\n          return null;\n        }\n      }\n      matchesDefault = false;\n    }\n  }\n  const matches = matchesDefault ? matchesDefault === (inputSegments.next() === '') : true;\n  if (!matches) {\n    return null;\n  }\n  if (allparams) {\n    return chain.map((route, i) => ({\n      id: route.id,\n      segments: route.segments,\n      params: mergeParams(route.params, allparams[i]),\n      beforeEnter: route.beforeEnter,\n      beforeLeave: route.beforeLeave\n    }));\n  }\n  return chain;\n};\n/**\n * Merges the route parameter objects.\n * Returns undefined when both parameters are undefined.\n */\nconst mergeParams = (a, b) => {\n  return a || b ? Object.assign(Object.assign({}, a), b) : undefined;\n};\n/**\n * Finds the best match for the ids in the chains.\n *\n * Returns the best match or null when no match is found.\n * When a chain is returned the parameters are updated from the RouteIDs.\n * That is they contain both the componentProps of the <ion-route> and the parameter segment.\n */\nconst findChainForIDs = (ids, chains) => {\n  let match = null;\n  let maxMatches = 0;\n  for (const chain of chains) {\n    const score = matchesIDs(ids, chain);\n    if (score > maxMatches) {\n      match = chain;\n      maxMatches = score;\n    }\n  }\n  if (match) {\n    return match.map((route, i) => {\n      var _a;\n      return {\n        id: route.id,\n        segments: route.segments,\n        params: mergeParams(route.params, (_a = ids[i]) === null || _a === void 0 ? void 0 : _a.params)\n      };\n    });\n  }\n  return null;\n};\n/**\n * Finds the best match for the segments in the chains.\n *\n * Returns the best match or null when no match is found.\n * When a chain is returned the parameters are updated from the segments.\n * That is they contain both the componentProps of the <ion-route> and the parameter segments.\n */\nconst findChainForSegments = (segments, chains) => {\n  let match = null;\n  let bestScore = 0;\n  for (const chain of chains) {\n    const matchedChain = matchesSegments(segments, chain);\n    if (matchedChain !== null) {\n      const score = computePriority(matchedChain);\n      if (score > bestScore) {\n        bestScore = score;\n        match = matchedChain;\n      }\n    }\n  }\n  return match;\n};\n/**\n * Computes the priority of a chain.\n *\n * Parameter segments are given a lower priority over fixed segments.\n *\n * Considering the following 2 chains matching the path /path/to/page:\n * - /path/to/:where\n * - /path/to/page\n *\n * The second one will be given a higher priority because \"page\" is a fixed segment (vs \":where\", a parameter segment).\n */\nconst computePriority = chain => {\n  let score = 1;\n  let level = 1;\n  for (const route of chain) {\n    for (const segment of route.segments) {\n      if (segment[0] === ':') {\n        score += Math.pow(1, level);\n      } else if (segment !== '') {\n        score += Math.pow(2, level);\n      }\n      level++;\n    }\n  }\n  return score;\n};\nclass RouterSegments {\n  constructor(segments) {\n    this.segments = segments.slice();\n  }\n  next() {\n    if (this.segments.length > 0) {\n      return this.segments.shift();\n    }\n    return '';\n  }\n}\nconst readProp = (el, prop) => {\n  if (prop in el) {\n    return el[prop];\n  }\n  if (el.hasAttribute(prop)) {\n    return el.getAttribute(prop);\n  }\n  return null;\n};\n/**\n * Extracts the redirects (that is <ion-route-redirect> elements inside the root).\n *\n * The redirects are returned as a list of RouteRedirect.\n */\nconst readRedirects = root => {\n  return Array.from(root.children).filter(el => el.tagName === 'ION-ROUTE-REDIRECT').map(el => {\n    const to = readProp(el, 'to');\n    return {\n      from: parsePath(readProp(el, 'from')).segments,\n      to: to == null ? undefined : parsePath(to)\n    };\n  });\n};\n/**\n * Extracts all the routes (that is <ion-route> elements inside the root).\n *\n * The routes are returned as a list of chains - the flattened tree.\n */\nconst readRoutes = root => {\n  return flattenRouterTree(readRouteNodes(root));\n};\n/**\n * Reads the route nodes as a tree modeled after the DOM tree of <ion-route> elements.\n *\n * Note: routes without a component are ignored together with their children.\n */\nconst readRouteNodes = node => {\n  return Array.from(node.children).filter(el => el.tagName === 'ION-ROUTE' && el.component).map(el => {\n    const component = readProp(el, 'component');\n    return {\n      segments: parsePath(readProp(el, 'url')).segments,\n      id: component.toLowerCase(),\n      params: el.componentProps,\n      beforeLeave: el.beforeLeave,\n      beforeEnter: el.beforeEnter,\n      children: readRouteNodes(el)\n    };\n  });\n};\n/**\n * Flattens a RouterTree in a list of chains.\n *\n * Each chain represents a path from the root node to a terminal node.\n */\nconst flattenRouterTree = nodes => {\n  const chains = [];\n  for (const node of nodes) {\n    flattenNode([], chains, node);\n  }\n  return chains;\n};\n/** Flattens a route node recursively and push each branch to the chains list. */\nconst flattenNode = (chain, chains, node) => {\n  chain = [...chain, {\n    id: node.id,\n    segments: node.segments,\n    params: node.params,\n    beforeLeave: node.beforeLeave,\n    beforeEnter: node.beforeEnter\n  }];\n  if (node.children.length === 0) {\n    chains.push(chain);\n    return;\n  }\n  for (const child of node.children) {\n    flattenNode(chain, chains, child);\n  }\n};\nconst Router = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionRouteWillChange = createEvent(this, \"ionRouteWillChange\", 7);\n    this.ionRouteDidChange = createEvent(this, \"ionRouteDidChange\", 7);\n    this.previousPath = null;\n    this.busy = false;\n    this.state = 0;\n    this.lastState = 0;\n    /**\n     * The root path to use when matching URLs. By default, this is set to \"/\", but you can specify\n     * an alternate prefix for all URL paths.\n     */\n    this.root = '/';\n    /**\n     * The router can work in two \"modes\":\n     * - With hash: `/index.html#/path/to/page`\n     * - Without hash: `/path/to/page`\n     *\n     * Using one or another might depend in the requirements of your app and/or where it's deployed.\n     *\n     * Usually \"hash-less\" navigation works better for SEO and it's more user friendly too, but it might\n     * requires additional server-side configuration in order to properly work.\n     *\n     * On the other side hash-navigation is much easier to deploy, it even works over the file protocol.\n     *\n     * By default, this property is `true`, change to `false` to allow hash-less URLs.\n     */\n    this.useHash = true;\n  }\n  componentWillLoad() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield waitUntilNavNode();\n      const canProceed = yield _this.runGuards(_this.getSegments());\n      if (canProceed !== true) {\n        if (typeof canProceed === 'object') {\n          const {\n            redirect\n          } = canProceed;\n          const path = parsePath(redirect);\n          _this.setSegments(path.segments, ROUTER_INTENT_NONE, path.queryString);\n          yield _this.writeNavStateRoot(path.segments, ROUTER_INTENT_NONE);\n        }\n      } else {\n        yield _this.onRoutesChanged();\n      }\n    })();\n  }\n  componentDidLoad() {\n    window.addEventListener('ionRouteRedirectChanged', debounce(this.onRedirectChanged.bind(this), 10));\n    window.addEventListener('ionRouteDataChanged', debounce(this.onRoutesChanged.bind(this), 100));\n  }\n  onPopState() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const direction = _this2.historyDirection();\n      let segments = _this2.getSegments();\n      const canProceed = yield _this2.runGuards(segments);\n      if (canProceed !== true) {\n        if (typeof canProceed === 'object') {\n          segments = parsePath(canProceed.redirect).segments;\n        } else {\n          return false;\n        }\n      }\n      return _this2.writeNavStateRoot(segments, direction);\n    })();\n  }\n  onBackButton(ev) {\n    ev.detail.register(0, processNextHandler => {\n      this.back();\n      processNextHandler();\n    });\n  }\n  /** @internal */\n  canTransition() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const canProceed = yield _this3.runGuards();\n      if (canProceed !== true) {\n        if (typeof canProceed === 'object') {\n          return canProceed.redirect;\n        } else {\n          return false;\n        }\n      }\n      return true;\n    })();\n  }\n  /**\n   * Navigate to the specified path.\n   *\n   * @param path The path to navigate to.\n   * @param direction The direction of the animation. Defaults to `\"forward\"`.\n   * @param animation A custom animation to use for the transition.\n   */\n  push(_x6) {\n    var _this4 = this;\n    return _asyncToGenerator(function* (path, direction = 'forward', animation) {\n      var _a;\n      if (path.startsWith('.')) {\n        const currentPath = (_a = _this4.previousPath) !== null && _a !== void 0 ? _a : '/';\n        // Convert currentPath to an URL by pre-pending a protocol and a host to resolve the relative path.\n        const url = new URL(path, `https://host/${currentPath}`);\n        path = url.pathname + url.search;\n      }\n      let parsedPath = parsePath(path);\n      const canProceed = yield _this4.runGuards(parsedPath.segments);\n      if (canProceed !== true) {\n        if (typeof canProceed === 'object') {\n          parsedPath = parsePath(canProceed.redirect);\n        } else {\n          return false;\n        }\n      }\n      _this4.setSegments(parsedPath.segments, direction, parsedPath.queryString);\n      return _this4.writeNavStateRoot(parsedPath.segments, direction, animation);\n    }).apply(this, arguments);\n  }\n  /** Go back to previous page in the window.history. */\n  back() {\n    window.history.back();\n    return Promise.resolve(this.waitPromise);\n  }\n  /** @internal */\n  printDebug() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      printRoutes(readRoutes(_this5.el));\n      printRedirects(readRedirects(_this5.el));\n    })();\n  }\n  /** @internal */\n  navChanged(direction) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (_this6.busy) {\n        printIonWarning('[ion-router] - Router is busy, navChanged was cancelled.');\n        return false;\n      }\n      const {\n        ids,\n        outlet\n      } = yield readNavState(window.document.body);\n      const routes = readRoutes(_this6.el);\n      const chain = findChainForIDs(ids, routes);\n      if (!chain) {\n        printIonWarning('[ion-router] - No matching URL for', ids.map(i => i.id));\n        return false;\n      }\n      const segments = chainToSegments(chain);\n      if (!segments) {\n        printIonWarning('[ion-router] - Router could not match path because some required param is missing.');\n        return false;\n      }\n      _this6.setSegments(segments, direction);\n      yield _this6.safeWriteNavState(outlet, chain, ROUTER_INTENT_NONE, segments, null, ids.length);\n      return true;\n    })();\n  }\n  /** This handler gets called when a `ion-route-redirect` component is added to the DOM or if the from or to property of such node changes. */\n  onRedirectChanged() {\n    const segments = this.getSegments();\n    if (segments && findRouteRedirect(segments, readRedirects(this.el))) {\n      this.writeNavStateRoot(segments, ROUTER_INTENT_NONE);\n    }\n  }\n  /** This handler gets called when a `ion-route` component is added to the DOM or if the from or to property of such node changes. */\n  onRoutesChanged() {\n    return this.writeNavStateRoot(this.getSegments(), ROUTER_INTENT_NONE);\n  }\n  historyDirection() {\n    var _a;\n    const win = window;\n    if (win.history.state === null) {\n      this.state++;\n      win.history.replaceState(this.state, win.document.title, (_a = win.document.location) === null || _a === void 0 ? void 0 : _a.href);\n    }\n    const state = win.history.state;\n    const lastState = this.lastState;\n    this.lastState = state;\n    if (state > lastState || state >= lastState && lastState > 0) {\n      return ROUTER_INTENT_FORWARD;\n    }\n    if (state < lastState) {\n      return ROUTER_INTENT_BACK;\n    }\n    return ROUTER_INTENT_NONE;\n  }\n  writeNavStateRoot(segments, direction, animation) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      if (!segments) {\n        printIonError('[ion-router] - URL is not part of the routing set.');\n        return false;\n      }\n      // lookup redirect rule\n      const redirects = readRedirects(_this7.el);\n      const redirect = findRouteRedirect(segments, redirects);\n      let redirectFrom = null;\n      if (redirect) {\n        const {\n          segments: toSegments,\n          queryString\n        } = redirect.to;\n        _this7.setSegments(toSegments, direction, queryString);\n        redirectFrom = redirect.from;\n        segments = toSegments;\n      }\n      // lookup route chain\n      const routes = readRoutes(_this7.el);\n      const chain = findChainForSegments(segments, routes);\n      if (!chain) {\n        printIonError('[ion-router] - The path does not match any route.');\n        return false;\n      }\n      // write DOM give\n      return _this7.safeWriteNavState(document.body, chain, direction, segments, redirectFrom, 0, animation);\n    })();\n  }\n  safeWriteNavState(_x7, _x8, _x9, _x0, _x1) {\n    var _this8 = this;\n    return _asyncToGenerator(function* (node, chain, direction, segments, redirectFrom, index = 0, animation) {\n      const unlock = yield _this8.lock();\n      let changed = false;\n      try {\n        changed = yield _this8.writeNavState(node, chain, direction, segments, redirectFrom, index, animation);\n      } catch (e) {\n        printIonError('[ion-router] - Exception in safeWriteNavState:', e);\n      }\n      unlock();\n      return changed;\n    }).apply(this, arguments);\n  }\n  lock() {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      const p = _this9.waitPromise;\n      let resolve;\n      _this9.waitPromise = new Promise(r => resolve = r);\n      if (p !== undefined) {\n        yield p;\n      }\n      return resolve;\n    })();\n  }\n  /**\n   * Executes the beforeLeave hook of the source route and the beforeEnter hook of the target route if they exist.\n   *\n   * When the beforeLeave hook does not return true (to allow navigating) then that value is returned early and the beforeEnter is executed.\n   * Otherwise the beforeEnterHook hook of the target route is executed.\n   */\n  runGuards() {\n    var _this0 = this;\n    return _asyncToGenerator(function* (to = _this0.getSegments(), from) {\n      if (from === undefined) {\n        from = parsePath(_this0.previousPath).segments;\n      }\n      if (!to || !from) {\n        return true;\n      }\n      const routes = readRoutes(_this0.el);\n      const fromChain = findChainForSegments(from, routes);\n      // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n      const beforeLeaveHook = fromChain && fromChain[fromChain.length - 1].beforeLeave;\n      const canLeave = beforeLeaveHook ? yield beforeLeaveHook() : true;\n      if (canLeave === false || typeof canLeave === 'object') {\n        return canLeave;\n      }\n      const toChain = findChainForSegments(to, routes);\n      // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n      const beforeEnterHook = toChain && toChain[toChain.length - 1].beforeEnter;\n      return beforeEnterHook ? beforeEnterHook() : true;\n    }).apply(this, arguments);\n  }\n  writeNavState(_x10, _x11, _x12, _x13, _x14) {\n    var _this1 = this;\n    return _asyncToGenerator(function* (node, chain, direction, segments, redirectFrom, index = 0, animation) {\n      if (_this1.busy) {\n        printIonWarning('[ion-router] - Router is busy, transition was cancelled.');\n        return false;\n      }\n      _this1.busy = true;\n      // generate route event and emit will change\n      const routeEvent = _this1.routeChangeEvent(segments, redirectFrom);\n      if (routeEvent) {\n        _this1.ionRouteWillChange.emit(routeEvent);\n      }\n      const changed = yield _writeNavState(node, chain, direction, index, false, animation);\n      _this1.busy = false;\n      // emit did change\n      if (routeEvent) {\n        _this1.ionRouteDidChange.emit(routeEvent);\n      }\n      return changed;\n    }).apply(this, arguments);\n  }\n  setSegments(segments, direction, queryString) {\n    this.state++;\n    writeSegments(window.history, this.root, this.useHash, segments, direction, this.state, queryString);\n  }\n  getSegments() {\n    return readSegments(window.location, this.root, this.useHash);\n  }\n  routeChangeEvent(toSegments, redirectFromSegments) {\n    const from = this.previousPath;\n    const to = generatePath(toSegments);\n    this.previousPath = to;\n    if (to === from) {\n      return null;\n    }\n    const redirectedFrom = redirectFromSegments ? generatePath(redirectFromSegments) : null;\n    return {\n      from,\n      redirectedFrom,\n      to\n    };\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nconst routerLinkCss = \":host{--background:transparent;--color:var(--ion-color-primary, #0054e9);background:var(--background);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}a{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit}\";\nconst RouterLink = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    /**\n     * When using a router, it specifies the transition direction when navigating to\n     * another page using `href`.\n     */\n    this.routerDirection = 'forward';\n    this.onClick = ev => {\n      openURL(this.href, ev, this.routerDirection, this.routerAnimation);\n    };\n  }\n  render() {\n    const mode = getIonMode(this);\n    const attrs = {\n      href: this.href,\n      rel: this.rel,\n      target: this.target\n    };\n    return h(Host, {\n      key: 'd7f2affcde45c5fbb6cb46cd1c30008ee92a68c5',\n      onClick: this.onClick,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'ion-activatable': true\n      })\n    }, h(\"a\", Object.assign({\n      key: 'babafae85ca5c6429958d383feff0493ff8cf33e'\n    }, attrs), h(\"slot\", {\n      key: '50314e9555bbf6dffa0c50c3f763009dee59b10b'\n    })));\n  }\n};\nRouterLink.style = routerLinkCss;\nexport { Route as ion_route, RouteRedirect as ion_route_redirect, Router as ion_router, RouterLink as ion_router_link };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "o", "printIonError", "m", "printIonWarning", "k", "getElement", "e", "getIonMode", "h", "j", "Host", "c", "componentOnReady", "p", "debounce", "openURL", "createColorClasses", "Route", "constructor", "hostRef", "ionRouteDataChanged", "url", "onUpdate", "newValue", "emit", "onComponentProps", "oldValue", "keys1", "Object", "keys", "keys2", "length", "key", "connectedCallback", "watchers", "RouteRedirect", "ionRouteRedirectChanged", "propDidChange", "ROUTER_INTENT_NONE", "ROUTER_INTENT_FORWARD", "ROUTER_INTENT_BACK", "generatePath", "segments", "path", "filter", "s", "join", "generateUrl", "useHash", "queryString", "undefined", "writeSegments", "history", "root", "direction", "state", "parsePath", "pushState", "replaceState", "chainToSegments", "chain", "route", "segment", "param", "params", "slice", "push", "removePrefix", "prefix", "i", "readSegments", "loc", "pathname", "hash", "qsStart", "indexOf", "substring", "split", "map", "trim", "printRoutes", "routes", "console", "group", "for<PERSON>ach", "ids", "id", "debug", "groupEnd", "printRedirects", "redirects", "redirect", "to", "from", "writeNavState", "_ref", "_asyncToGenerator", "index", "changed", "animation", "outlet", "searchNavNode", "Promise", "resolve", "result", "setRouteId", "element", "markVisible", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "readNavState", "_ref2", "node", "getRouteId", "_x5", "waitUntilNavNode", "document", "body", "window", "addEventListener", "once", "OUTLET_SELECTOR", "matches", "querySelector", "matchesRedirect", "expected", "findRouteRedirect", "find", "matchesIDs", "len", "Math", "min", "score", "routeId", "routeChain", "toLowerCase", "routeIdParams", "pathWithParams", "matchesSegments", "inputSegments", "RouterSegments", "matchesDefault", "allparams", "chainSegments", "data", "next", "mergeParams", "beforeEnter", "beforeLeave", "a", "b", "assign", "findChainForIDs", "chains", "match", "max<PERSON><PERSON><PERSON>", "_a", "findChainForSegments", "bestScore", "<PERSON><PERSON><PERSON><PERSON>", "computePriority", "level", "pow", "shift", "readProp", "el", "prop", "hasAttribute", "getAttribute", "readRedirects", "Array", "children", "tagName", "readRoutes", "flattenRouterTree", "readRouteNodes", "component", "componentProps", "nodes", "flattenNode", "child", "Router", "ionRouteWillChange", "ionRouteDidChange", "previousPath", "busy", "lastState", "componentWillLoad", "_this", "canProceed", "runGuards", "getSegments", "setSegments", "writeNavStateRoot", "onRoutesChanged", "componentDidLoad", "onRedirectChanged", "bind", "onPopState", "_this2", "historyDirection", "onBackButton", "ev", "detail", "register", "processNextHandler", "back", "canTransition", "_this3", "_x6", "_this4", "startsWith", "currentPath", "URL", "search", "parsed<PERSON><PERSON>", "waitPromise", "printDebug", "_this5", "navChanged", "_this6", "safeWriteNavState", "win", "title", "location", "href", "_this7", "redirectFrom", "toSegments", "_x7", "_x8", "_x9", "_x0", "_x1", "_this8", "unlock", "lock", "_this9", "_this0", "fromChain", "beforeLeaveHook", "canLeave", "<PERSON><PERSON><PERSON><PERSON>", "beforeEnterHook", "_x10", "_x11", "_x12", "_x13", "_x14", "_this1", "routeEvent", "routeChangeEvent", "redirectFromSegments", "redirectedFrom", "routerLinkCss", "RouterLink", "routerDirection", "onClick", "routerAnimation", "render", "mode", "attrs", "rel", "target", "class", "color", "style", "ion_route", "ion_route_redirect", "ion_router", "ion_router_link"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@ionic/core/dist/esm/ion-route_4.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, o as printIonError, m as printIonWarning, k as getElement, e as getIonMode, h, j as Host } from './index-B_U9CtaY.js';\nimport { c as componentOnReady, p as debounce } from './helpers-1O4D2b7y.js';\nimport { o as openURL, c as createColorClasses } from './theme-DiVJyqlX.js';\n\nconst Route = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionRouteDataChanged = createEvent(this, \"ionRouteDataChanged\", 7);\n        /**\n         * Relative path that needs to match in order for this route to apply.\n         *\n         * Accepts paths similar to expressjs so that you can define parameters\n         * in the url /foo/:bar where bar would be available in incoming props.\n         */\n        this.url = '';\n    }\n    onUpdate(newValue) {\n        this.ionRouteDataChanged.emit(newValue);\n    }\n    onComponentProps(newValue, oldValue) {\n        if (newValue === oldValue) {\n            return;\n        }\n        const keys1 = newValue ? Object.keys(newValue) : [];\n        const keys2 = oldValue ? Object.keys(oldValue) : [];\n        if (keys1.length !== keys2.length) {\n            this.onUpdate(newValue);\n            return;\n        }\n        for (const key of keys1) {\n            if (newValue[key] !== oldValue[key]) {\n                this.onUpdate(newValue);\n                return;\n            }\n        }\n    }\n    connectedCallback() {\n        this.ionRouteDataChanged.emit();\n    }\n    static get watchers() { return {\n        \"url\": [\"onUpdate\"],\n        \"component\": [\"onUpdate\"],\n        \"componentProps\": [\"onComponentProps\"]\n    }; }\n};\n\nconst RouteRedirect = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionRouteRedirectChanged = createEvent(this, \"ionRouteRedirectChanged\", 7);\n    }\n    propDidChange() {\n        this.ionRouteRedirectChanged.emit();\n    }\n    connectedCallback() {\n        this.ionRouteRedirectChanged.emit();\n    }\n    static get watchers() { return {\n        \"from\": [\"propDidChange\"],\n        \"to\": [\"propDidChange\"]\n    }; }\n};\n\nconst ROUTER_INTENT_NONE = 'root';\nconst ROUTER_INTENT_FORWARD = 'forward';\nconst ROUTER_INTENT_BACK = 'back';\n\n/** Join the non empty segments with \"/\". */\nconst generatePath = (segments) => {\n    const path = segments.filter((s) => s.length > 0).join('/');\n    return '/' + path;\n};\nconst generateUrl = (segments, useHash, queryString) => {\n    let url = generatePath(segments);\n    if (useHash) {\n        url = '#' + url;\n    }\n    if (queryString !== undefined) {\n        url += '?' + queryString;\n    }\n    return url;\n};\nconst writeSegments = (history, root, useHash, segments, direction, state, queryString) => {\n    const url = generateUrl([...parsePath(root).segments, ...segments], useHash, queryString);\n    if (direction === ROUTER_INTENT_FORWARD) {\n        history.pushState(state, '', url);\n    }\n    else {\n        history.replaceState(state, '', url);\n    }\n};\n/**\n * Transforms a chain to a list of segments.\n *\n * Notes:\n * - parameter segments of the form :param are replaced with their value,\n * - null is returned when a value is missing for any parameter segment.\n */\nconst chainToSegments = (chain) => {\n    const segments = [];\n    for (const route of chain) {\n        for (const segment of route.segments) {\n            if (segment[0] === ':') {\n                // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n                const param = route.params && route.params[segment.slice(1)];\n                if (!param) {\n                    return null;\n                }\n                segments.push(param);\n            }\n            else if (segment !== '') {\n                segments.push(segment);\n            }\n        }\n    }\n    return segments;\n};\n/**\n * Removes the prefix segments from the path segments.\n *\n * Return:\n * - null when the path segments do not start with the passed prefix,\n * - the path segments after the prefix otherwise.\n */\nconst removePrefix = (prefix, segments) => {\n    if (prefix.length > segments.length) {\n        return null;\n    }\n    if (prefix.length <= 1 && prefix[0] === '') {\n        return segments;\n    }\n    for (let i = 0; i < prefix.length; i++) {\n        if (prefix[i] !== segments[i]) {\n            return null;\n        }\n    }\n    if (segments.length === prefix.length) {\n        return [''];\n    }\n    return segments.slice(prefix.length);\n};\nconst readSegments = (loc, root, useHash) => {\n    const prefix = parsePath(root).segments;\n    const pathname = useHash ? loc.hash.slice(1) : loc.pathname;\n    const segments = parsePath(pathname).segments;\n    return removePrefix(prefix, segments);\n};\n/**\n * Parses the path to:\n * - segments an array of '/' separated parts,\n * - queryString (undefined when no query string).\n */\nconst parsePath = (path) => {\n    let segments = [''];\n    let queryString;\n    if (path != null) {\n        const qsStart = path.indexOf('?');\n        if (qsStart > -1) {\n            queryString = path.substring(qsStart + 1);\n            path = path.substring(0, qsStart);\n        }\n        segments = path\n            .split('/')\n            .map((s) => s.trim())\n            .filter((s) => s.length > 0);\n        if (segments.length === 0) {\n            segments = [''];\n        }\n    }\n    return { segments, queryString };\n};\n\nconst printRoutes = (routes) => {\n    console.group(`[ion-core] ROUTES[${routes.length}]`);\n    for (const chain of routes) {\n        const segments = [];\n        chain.forEach((r) => segments.push(...r.segments));\n        const ids = chain.map((r) => r.id);\n        console.debug(`%c ${generatePath(segments)}`, 'font-weight: bold; padding-left: 20px', '=>\\t', `(${ids.join(', ')})`);\n    }\n    console.groupEnd();\n};\nconst printRedirects = (redirects) => {\n    console.group(`[ion-core] REDIRECTS[${redirects.length}]`);\n    for (const redirect of redirects) {\n        if (redirect.to) {\n            console.debug('FROM: ', `$c ${generatePath(redirect.from)}`, 'font-weight: bold', ' TO: ', `$c ${generatePath(redirect.to.segments)}`, 'font-weight: bold');\n        }\n    }\n    console.groupEnd();\n};\n\n/**\n * Activates the passed route chain.\n *\n * There must be exactly one outlet per route entry in the chain.\n *\n * The methods calls setRouteId on each of the outlet with the corresponding route entry in the chain.\n * setRouteId will create or select the view in the outlet.\n */\nconst writeNavState = async (root, chain, direction, index, changed = false, animation) => {\n    try {\n        // find next navigation outlet in the DOM\n        const outlet = searchNavNode(root);\n        // make sure we can continue interacting the DOM, otherwise abort\n        if (index >= chain.length || !outlet) {\n            return changed;\n        }\n        await new Promise((resolve) => componentOnReady(outlet, resolve));\n        const route = chain[index];\n        const result = await outlet.setRouteId(route.id, route.params, direction, animation);\n        // if the outlet changed the page, reset navigation to neutral (no direction)\n        // this means nested outlets will not animate\n        if (result.changed) {\n            direction = ROUTER_INTENT_NONE;\n            changed = true;\n        }\n        // recursively set nested outlets\n        changed = await writeNavState(result.element, chain, direction, index + 1, changed, animation);\n        // once all nested outlets are visible let's make the parent visible too,\n        // using markVisible prevents flickering\n        if (result.markVisible) {\n            await result.markVisible();\n        }\n        return changed;\n    }\n    catch (e) {\n        printIonError('[ion-router] - Exception in writeNavState:', e);\n        return false;\n    }\n};\n/**\n * Recursively walks the outlet in the DOM.\n *\n * The function returns a list of RouteID corresponding to each of the outlet and the last outlet without a RouteID.\n */\nconst readNavState = async (root) => {\n    const ids = [];\n    let outlet;\n    let node = root;\n    // eslint-disable-next-line no-cond-assign\n    while ((outlet = searchNavNode(node))) {\n        const id = await outlet.getRouteId();\n        if (id) {\n            node = id.element;\n            id.element = undefined;\n            ids.push(id);\n        }\n        else {\n            break;\n        }\n    }\n    return { ids, outlet };\n};\nconst waitUntilNavNode = () => {\n    if (searchNavNode(document.body)) {\n        return Promise.resolve();\n    }\n    return new Promise((resolve) => {\n        window.addEventListener('ionNavWillLoad', () => resolve(), { once: true });\n    });\n};\n/** Selector for all the outlets supported by the router. */\nconst OUTLET_SELECTOR = ':not([no-router]) ion-nav, :not([no-router]) ion-tabs, :not([no-router]) ion-router-outlet';\nconst searchNavNode = (root) => {\n    if (!root) {\n        return undefined;\n    }\n    if (root.matches(OUTLET_SELECTOR)) {\n        return root;\n    }\n    const outlet = root.querySelector(OUTLET_SELECTOR);\n    return outlet !== null && outlet !== void 0 ? outlet : undefined;\n};\n\n/**\n * Returns whether the given redirect matches the given path segments.\n *\n * A redirect matches when the segments of the path and redirect.from are equal.\n * Note that segments are only checked until redirect.from contains a '*' which matches any path segment.\n * The path ['some', 'path', 'to', 'page'] matches both ['some', 'path', 'to', 'page'] and ['some', 'path', '*'].\n */\nconst matchesRedirect = (segments, redirect) => {\n    const { from, to } = redirect;\n    if (to === undefined) {\n        return false;\n    }\n    if (from.length > segments.length) {\n        return false;\n    }\n    for (let i = 0; i < from.length; i++) {\n        const expected = from[i];\n        if (expected === '*') {\n            return true;\n        }\n        if (expected !== segments[i]) {\n            return false;\n        }\n    }\n    return from.length === segments.length;\n};\n/** Returns the first redirect matching the path segments or undefined when no match found. */\nconst findRouteRedirect = (segments, redirects) => {\n    return redirects.find((redirect) => matchesRedirect(segments, redirect));\n};\nconst matchesIDs = (ids, chain) => {\n    const len = Math.min(ids.length, chain.length);\n    let score = 0;\n    for (let i = 0; i < len; i++) {\n        const routeId = ids[i];\n        const routeChain = chain[i];\n        // Skip results where the route id does not match the chain at the same index\n        if (routeId.id.toLowerCase() !== routeChain.id) {\n            break;\n        }\n        if (routeId.params) {\n            const routeIdParams = Object.keys(routeId.params);\n            // Only compare routes with the chain that have the same number of parameters.\n            if (routeIdParams.length === routeChain.segments.length) {\n                // Maps the route's params into a path based on the path variable names,\n                // to compare against the route chain format.\n                //\n                // Before:\n                // ```ts\n                // {\n                //  params: {\n                //    s1: 'a',\n                //    s2: 'b'\n                //  }\n                // }\n                // ```\n                //\n                // After:\n                // ```ts\n                // [':s1',':s2']\n                // ```\n                //\n                const pathWithParams = routeIdParams.map((key) => `:${key}`);\n                for (let j = 0; j < pathWithParams.length; j++) {\n                    // Skip results where the path variable is not a match\n                    if (pathWithParams[j].toLowerCase() !== routeChain.segments[j]) {\n                        break;\n                    }\n                    // Weight path matches for the same index higher.\n                    score++;\n                }\n            }\n        }\n        // Weight id matches\n        score++;\n    }\n    return score;\n};\n/**\n * Matches the segments against the chain.\n *\n * Returns:\n * - null when there is no match,\n * - a chain with the params properties updated with the parameter segments on match.\n */\nconst matchesSegments = (segments, chain) => {\n    const inputSegments = new RouterSegments(segments);\n    let matchesDefault = false;\n    let allparams;\n    for (let i = 0; i < chain.length; i++) {\n        const chainSegments = chain[i].segments;\n        if (chainSegments[0] === '') {\n            matchesDefault = true;\n        }\n        else {\n            for (const segment of chainSegments) {\n                const data = inputSegments.next();\n                // data param\n                if (segment[0] === ':') {\n                    if (data === '') {\n                        return null;\n                    }\n                    allparams = allparams || [];\n                    const params = allparams[i] || (allparams[i] = {});\n                    params[segment.slice(1)] = data;\n                }\n                else if (data !== segment) {\n                    return null;\n                }\n            }\n            matchesDefault = false;\n        }\n    }\n    const matches = matchesDefault ? matchesDefault === (inputSegments.next() === '') : true;\n    if (!matches) {\n        return null;\n    }\n    if (allparams) {\n        return chain.map((route, i) => ({\n            id: route.id,\n            segments: route.segments,\n            params: mergeParams(route.params, allparams[i]),\n            beforeEnter: route.beforeEnter,\n            beforeLeave: route.beforeLeave,\n        }));\n    }\n    return chain;\n};\n/**\n * Merges the route parameter objects.\n * Returns undefined when both parameters are undefined.\n */\nconst mergeParams = (a, b) => {\n    return a || b ? Object.assign(Object.assign({}, a), b) : undefined;\n};\n/**\n * Finds the best match for the ids in the chains.\n *\n * Returns the best match or null when no match is found.\n * When a chain is returned the parameters are updated from the RouteIDs.\n * That is they contain both the componentProps of the <ion-route> and the parameter segment.\n */\nconst findChainForIDs = (ids, chains) => {\n    let match = null;\n    let maxMatches = 0;\n    for (const chain of chains) {\n        const score = matchesIDs(ids, chain);\n        if (score > maxMatches) {\n            match = chain;\n            maxMatches = score;\n        }\n    }\n    if (match) {\n        return match.map((route, i) => {\n            var _a;\n            return ({\n                id: route.id,\n                segments: route.segments,\n                params: mergeParams(route.params, (_a = ids[i]) === null || _a === void 0 ? void 0 : _a.params),\n            });\n        });\n    }\n    return null;\n};\n/**\n * Finds the best match for the segments in the chains.\n *\n * Returns the best match or null when no match is found.\n * When a chain is returned the parameters are updated from the segments.\n * That is they contain both the componentProps of the <ion-route> and the parameter segments.\n */\nconst findChainForSegments = (segments, chains) => {\n    let match = null;\n    let bestScore = 0;\n    for (const chain of chains) {\n        const matchedChain = matchesSegments(segments, chain);\n        if (matchedChain !== null) {\n            const score = computePriority(matchedChain);\n            if (score > bestScore) {\n                bestScore = score;\n                match = matchedChain;\n            }\n        }\n    }\n    return match;\n};\n/**\n * Computes the priority of a chain.\n *\n * Parameter segments are given a lower priority over fixed segments.\n *\n * Considering the following 2 chains matching the path /path/to/page:\n * - /path/to/:where\n * - /path/to/page\n *\n * The second one will be given a higher priority because \"page\" is a fixed segment (vs \":where\", a parameter segment).\n */\nconst computePriority = (chain) => {\n    let score = 1;\n    let level = 1;\n    for (const route of chain) {\n        for (const segment of route.segments) {\n            if (segment[0] === ':') {\n                score += Math.pow(1, level);\n            }\n            else if (segment !== '') {\n                score += Math.pow(2, level);\n            }\n            level++;\n        }\n    }\n    return score;\n};\nclass RouterSegments {\n    constructor(segments) {\n        this.segments = segments.slice();\n    }\n    next() {\n        if (this.segments.length > 0) {\n            return this.segments.shift();\n        }\n        return '';\n    }\n}\n\nconst readProp = (el, prop) => {\n    if (prop in el) {\n        return el[prop];\n    }\n    if (el.hasAttribute(prop)) {\n        return el.getAttribute(prop);\n    }\n    return null;\n};\n/**\n * Extracts the redirects (that is <ion-route-redirect> elements inside the root).\n *\n * The redirects are returned as a list of RouteRedirect.\n */\nconst readRedirects = (root) => {\n    return Array.from(root.children)\n        .filter((el) => el.tagName === 'ION-ROUTE-REDIRECT')\n        .map((el) => {\n        const to = readProp(el, 'to');\n        return {\n            from: parsePath(readProp(el, 'from')).segments,\n            to: to == null ? undefined : parsePath(to),\n        };\n    });\n};\n/**\n * Extracts all the routes (that is <ion-route> elements inside the root).\n *\n * The routes are returned as a list of chains - the flattened tree.\n */\nconst readRoutes = (root) => {\n    return flattenRouterTree(readRouteNodes(root));\n};\n/**\n * Reads the route nodes as a tree modeled after the DOM tree of <ion-route> elements.\n *\n * Note: routes without a component are ignored together with their children.\n */\nconst readRouteNodes = (node) => {\n    return Array.from(node.children)\n        .filter((el) => el.tagName === 'ION-ROUTE' && el.component)\n        .map((el) => {\n        const component = readProp(el, 'component');\n        return {\n            segments: parsePath(readProp(el, 'url')).segments,\n            id: component.toLowerCase(),\n            params: el.componentProps,\n            beforeLeave: el.beforeLeave,\n            beforeEnter: el.beforeEnter,\n            children: readRouteNodes(el),\n        };\n    });\n};\n/**\n * Flattens a RouterTree in a list of chains.\n *\n * Each chain represents a path from the root node to a terminal node.\n */\nconst flattenRouterTree = (nodes) => {\n    const chains = [];\n    for (const node of nodes) {\n        flattenNode([], chains, node);\n    }\n    return chains;\n};\n/** Flattens a route node recursively and push each branch to the chains list. */\nconst flattenNode = (chain, chains, node) => {\n    chain = [\n        ...chain,\n        {\n            id: node.id,\n            segments: node.segments,\n            params: node.params,\n            beforeLeave: node.beforeLeave,\n            beforeEnter: node.beforeEnter,\n        },\n    ];\n    if (node.children.length === 0) {\n        chains.push(chain);\n        return;\n    }\n    for (const child of node.children) {\n        flattenNode(chain, chains, child);\n    }\n};\n\nconst Router = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionRouteWillChange = createEvent(this, \"ionRouteWillChange\", 7);\n        this.ionRouteDidChange = createEvent(this, \"ionRouteDidChange\", 7);\n        this.previousPath = null;\n        this.busy = false;\n        this.state = 0;\n        this.lastState = 0;\n        /**\n         * The root path to use when matching URLs. By default, this is set to \"/\", but you can specify\n         * an alternate prefix for all URL paths.\n         */\n        this.root = '/';\n        /**\n         * The router can work in two \"modes\":\n         * - With hash: `/index.html#/path/to/page`\n         * - Without hash: `/path/to/page`\n         *\n         * Using one or another might depend in the requirements of your app and/or where it's deployed.\n         *\n         * Usually \"hash-less\" navigation works better for SEO and it's more user friendly too, but it might\n         * requires additional server-side configuration in order to properly work.\n         *\n         * On the other side hash-navigation is much easier to deploy, it even works over the file protocol.\n         *\n         * By default, this property is `true`, change to `false` to allow hash-less URLs.\n         */\n        this.useHash = true;\n    }\n    async componentWillLoad() {\n        await waitUntilNavNode();\n        const canProceed = await this.runGuards(this.getSegments());\n        if (canProceed !== true) {\n            if (typeof canProceed === 'object') {\n                const { redirect } = canProceed;\n                const path = parsePath(redirect);\n                this.setSegments(path.segments, ROUTER_INTENT_NONE, path.queryString);\n                await this.writeNavStateRoot(path.segments, ROUTER_INTENT_NONE);\n            }\n        }\n        else {\n            await this.onRoutesChanged();\n        }\n    }\n    componentDidLoad() {\n        window.addEventListener('ionRouteRedirectChanged', debounce(this.onRedirectChanged.bind(this), 10));\n        window.addEventListener('ionRouteDataChanged', debounce(this.onRoutesChanged.bind(this), 100));\n    }\n    async onPopState() {\n        const direction = this.historyDirection();\n        let segments = this.getSegments();\n        const canProceed = await this.runGuards(segments);\n        if (canProceed !== true) {\n            if (typeof canProceed === 'object') {\n                segments = parsePath(canProceed.redirect).segments;\n            }\n            else {\n                return false;\n            }\n        }\n        return this.writeNavStateRoot(segments, direction);\n    }\n    onBackButton(ev) {\n        ev.detail.register(0, (processNextHandler) => {\n            this.back();\n            processNextHandler();\n        });\n    }\n    /** @internal */\n    async canTransition() {\n        const canProceed = await this.runGuards();\n        if (canProceed !== true) {\n            if (typeof canProceed === 'object') {\n                return canProceed.redirect;\n            }\n            else {\n                return false;\n            }\n        }\n        return true;\n    }\n    /**\n     * Navigate to the specified path.\n     *\n     * @param path The path to navigate to.\n     * @param direction The direction of the animation. Defaults to `\"forward\"`.\n     * @param animation A custom animation to use for the transition.\n     */\n    async push(path, direction = 'forward', animation) {\n        var _a;\n        if (path.startsWith('.')) {\n            const currentPath = (_a = this.previousPath) !== null && _a !== void 0 ? _a : '/';\n            // Convert currentPath to an URL by pre-pending a protocol and a host to resolve the relative path.\n            const url = new URL(path, `https://host/${currentPath}`);\n            path = url.pathname + url.search;\n        }\n        let parsedPath = parsePath(path);\n        const canProceed = await this.runGuards(parsedPath.segments);\n        if (canProceed !== true) {\n            if (typeof canProceed === 'object') {\n                parsedPath = parsePath(canProceed.redirect);\n            }\n            else {\n                return false;\n            }\n        }\n        this.setSegments(parsedPath.segments, direction, parsedPath.queryString);\n        return this.writeNavStateRoot(parsedPath.segments, direction, animation);\n    }\n    /** Go back to previous page in the window.history. */\n    back() {\n        window.history.back();\n        return Promise.resolve(this.waitPromise);\n    }\n    /** @internal */\n    async printDebug() {\n        printRoutes(readRoutes(this.el));\n        printRedirects(readRedirects(this.el));\n    }\n    /** @internal */\n    async navChanged(direction) {\n        if (this.busy) {\n            printIonWarning('[ion-router] - Router is busy, navChanged was cancelled.');\n            return false;\n        }\n        const { ids, outlet } = await readNavState(window.document.body);\n        const routes = readRoutes(this.el);\n        const chain = findChainForIDs(ids, routes);\n        if (!chain) {\n            printIonWarning('[ion-router] - No matching URL for', ids.map((i) => i.id));\n            return false;\n        }\n        const segments = chainToSegments(chain);\n        if (!segments) {\n            printIonWarning('[ion-router] - Router could not match path because some required param is missing.');\n            return false;\n        }\n        this.setSegments(segments, direction);\n        await this.safeWriteNavState(outlet, chain, ROUTER_INTENT_NONE, segments, null, ids.length);\n        return true;\n    }\n    /** This handler gets called when a `ion-route-redirect` component is added to the DOM or if the from or to property of such node changes. */\n    onRedirectChanged() {\n        const segments = this.getSegments();\n        if (segments && findRouteRedirect(segments, readRedirects(this.el))) {\n            this.writeNavStateRoot(segments, ROUTER_INTENT_NONE);\n        }\n    }\n    /** This handler gets called when a `ion-route` component is added to the DOM or if the from or to property of such node changes. */\n    onRoutesChanged() {\n        return this.writeNavStateRoot(this.getSegments(), ROUTER_INTENT_NONE);\n    }\n    historyDirection() {\n        var _a;\n        const win = window;\n        if (win.history.state === null) {\n            this.state++;\n            win.history.replaceState(this.state, win.document.title, (_a = win.document.location) === null || _a === void 0 ? void 0 : _a.href);\n        }\n        const state = win.history.state;\n        const lastState = this.lastState;\n        this.lastState = state;\n        if (state > lastState || (state >= lastState && lastState > 0)) {\n            return ROUTER_INTENT_FORWARD;\n        }\n        if (state < lastState) {\n            return ROUTER_INTENT_BACK;\n        }\n        return ROUTER_INTENT_NONE;\n    }\n    async writeNavStateRoot(segments, direction, animation) {\n        if (!segments) {\n            printIonError('[ion-router] - URL is not part of the routing set.');\n            return false;\n        }\n        // lookup redirect rule\n        const redirects = readRedirects(this.el);\n        const redirect = findRouteRedirect(segments, redirects);\n        let redirectFrom = null;\n        if (redirect) {\n            const { segments: toSegments, queryString } = redirect.to;\n            this.setSegments(toSegments, direction, queryString);\n            redirectFrom = redirect.from;\n            segments = toSegments;\n        }\n        // lookup route chain\n        const routes = readRoutes(this.el);\n        const chain = findChainForSegments(segments, routes);\n        if (!chain) {\n            printIonError('[ion-router] - The path does not match any route.');\n            return false;\n        }\n        // write DOM give\n        return this.safeWriteNavState(document.body, chain, direction, segments, redirectFrom, 0, animation);\n    }\n    async safeWriteNavState(node, chain, direction, segments, redirectFrom, index = 0, animation) {\n        const unlock = await this.lock();\n        let changed = false;\n        try {\n            changed = await this.writeNavState(node, chain, direction, segments, redirectFrom, index, animation);\n        }\n        catch (e) {\n            printIonError('[ion-router] - Exception in safeWriteNavState:', e);\n        }\n        unlock();\n        return changed;\n    }\n    async lock() {\n        const p = this.waitPromise;\n        let resolve;\n        this.waitPromise = new Promise((r) => (resolve = r));\n        if (p !== undefined) {\n            await p;\n        }\n        return resolve;\n    }\n    /**\n     * Executes the beforeLeave hook of the source route and the beforeEnter hook of the target route if they exist.\n     *\n     * When the beforeLeave hook does not return true (to allow navigating) then that value is returned early and the beforeEnter is executed.\n     * Otherwise the beforeEnterHook hook of the target route is executed.\n     */\n    async runGuards(to = this.getSegments(), from) {\n        if (from === undefined) {\n            from = parsePath(this.previousPath).segments;\n        }\n        if (!to || !from) {\n            return true;\n        }\n        const routes = readRoutes(this.el);\n        const fromChain = findChainForSegments(from, routes);\n        // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n        const beforeLeaveHook = fromChain && fromChain[fromChain.length - 1].beforeLeave;\n        const canLeave = beforeLeaveHook ? await beforeLeaveHook() : true;\n        if (canLeave === false || typeof canLeave === 'object') {\n            return canLeave;\n        }\n        const toChain = findChainForSegments(to, routes);\n        // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n        const beforeEnterHook = toChain && toChain[toChain.length - 1].beforeEnter;\n        return beforeEnterHook ? beforeEnterHook() : true;\n    }\n    async writeNavState(node, chain, direction, segments, redirectFrom, index = 0, animation) {\n        if (this.busy) {\n            printIonWarning('[ion-router] - Router is busy, transition was cancelled.');\n            return false;\n        }\n        this.busy = true;\n        // generate route event and emit will change\n        const routeEvent = this.routeChangeEvent(segments, redirectFrom);\n        if (routeEvent) {\n            this.ionRouteWillChange.emit(routeEvent);\n        }\n        const changed = await writeNavState(node, chain, direction, index, false, animation);\n        this.busy = false;\n        // emit did change\n        if (routeEvent) {\n            this.ionRouteDidChange.emit(routeEvent);\n        }\n        return changed;\n    }\n    setSegments(segments, direction, queryString) {\n        this.state++;\n        writeSegments(window.history, this.root, this.useHash, segments, direction, this.state, queryString);\n    }\n    getSegments() {\n        return readSegments(window.location, this.root, this.useHash);\n    }\n    routeChangeEvent(toSegments, redirectFromSegments) {\n        const from = this.previousPath;\n        const to = generatePath(toSegments);\n        this.previousPath = to;\n        if (to === from) {\n            return null;\n        }\n        const redirectedFrom = redirectFromSegments ? generatePath(redirectFromSegments) : null;\n        return {\n            from,\n            redirectedFrom,\n            to,\n        };\n    }\n    get el() { return getElement(this); }\n};\n\nconst routerLinkCss = \":host{--background:transparent;--color:var(--ion-color-primary, #0054e9);background:var(--background);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}a{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit}\";\n\nconst RouterLink = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        /**\n         * When using a router, it specifies the transition direction when navigating to\n         * another page using `href`.\n         */\n        this.routerDirection = 'forward';\n        this.onClick = (ev) => {\n            openURL(this.href, ev, this.routerDirection, this.routerAnimation);\n        };\n    }\n    render() {\n        const mode = getIonMode(this);\n        const attrs = {\n            href: this.href,\n            rel: this.rel,\n            target: this.target,\n        };\n        return (h(Host, { key: 'd7f2affcde45c5fbb6cb46cd1c30008ee92a68c5', onClick: this.onClick, class: createColorClasses(this.color, {\n                [mode]: true,\n                'ion-activatable': true,\n            }) }, h(\"a\", Object.assign({ key: 'babafae85ca5c6429958d383feff0493ff8cf33e' }, attrs), h(\"slot\", { key: '50314e9555bbf6dffa0c50c3f763009dee59b10b' }))));\n    }\n};\nRouterLink.style = routerLinkCss;\n\nexport { Route as ion_route, RouteRedirect as ion_route_redirect, Router as ion_router, RouterLink as ion_router_link };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,QAAQ,qBAAqB;AACvK,SAASC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,QAAQ,QAAQ,uBAAuB;AAC5E,SAASd,CAAC,IAAIe,OAAO,EAAEJ,CAAC,IAAIK,kBAAkB,QAAQ,qBAAqB;AAE3E,MAAMC,KAAK,GAAG,MAAM;EAChBC,WAAWA,CAACC,OAAO,EAAE;IACjBtB,gBAAgB,CAAC,IAAI,EAAEsB,OAAO,CAAC;IAC/B,IAAI,CAACC,mBAAmB,GAAGrB,WAAW,CAAC,IAAI,EAAE,qBAAqB,EAAE,CAAC,CAAC;IACtE;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACsB,GAAG,GAAG,EAAE;EACjB;EACAC,QAAQA,CAACC,QAAQ,EAAE;IACf,IAAI,CAACH,mBAAmB,CAACI,IAAI,CAACD,QAAQ,CAAC;EAC3C;EACAE,gBAAgBA,CAACF,QAAQ,EAAEG,QAAQ,EAAE;IACjC,IAAIH,QAAQ,KAAKG,QAAQ,EAAE;MACvB;IACJ;IACA,MAAMC,KAAK,GAAGJ,QAAQ,GAAGK,MAAM,CAACC,IAAI,CAACN,QAAQ,CAAC,GAAG,EAAE;IACnD,MAAMO,KAAK,GAAGJ,QAAQ,GAAGE,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC,GAAG,EAAE;IACnD,IAAIC,KAAK,CAACI,MAAM,KAAKD,KAAK,CAACC,MAAM,EAAE;MAC/B,IAAI,CAACT,QAAQ,CAACC,QAAQ,CAAC;MACvB;IACJ;IACA,KAAK,MAAMS,GAAG,IAAIL,KAAK,EAAE;MACrB,IAAIJ,QAAQ,CAACS,GAAG,CAAC,KAAKN,QAAQ,CAACM,GAAG,CAAC,EAAE;QACjC,IAAI,CAACV,QAAQ,CAACC,QAAQ,CAAC;QACvB;MACJ;IACJ;EACJ;EACAU,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACb,mBAAmB,CAACI,IAAI,CAAC,CAAC;EACnC;EACA,WAAWU,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,KAAK,EAAE,CAAC,UAAU,CAAC;MACnB,WAAW,EAAE,CAAC,UAAU,CAAC;MACzB,gBAAgB,EAAE,CAAC,kBAAkB;IACzC,CAAC;EAAE;AACP,CAAC;AAED,MAAMC,aAAa,GAAG,MAAM;EACxBjB,WAAWA,CAACC,OAAO,EAAE;IACjBtB,gBAAgB,CAAC,IAAI,EAAEsB,OAAO,CAAC;IAC/B,IAAI,CAACiB,uBAAuB,GAAGrC,WAAW,CAAC,IAAI,EAAE,yBAAyB,EAAE,CAAC,CAAC;EAClF;EACAsC,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACD,uBAAuB,CAACZ,IAAI,CAAC,CAAC;EACvC;EACAS,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACG,uBAAuB,CAACZ,IAAI,CAAC,CAAC;EACvC;EACA,WAAWU,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,MAAM,EAAE,CAAC,eAAe,CAAC;MACzB,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC;EAAE;AACP,CAAC;AAED,MAAMI,kBAAkB,GAAG,MAAM;AACjC,MAAMC,qBAAqB,GAAG,SAAS;AACvC,MAAMC,kBAAkB,GAAG,MAAM;;AAEjC;AACA,MAAMC,YAAY,GAAIC,QAAQ,IAAK;EAC/B,MAAMC,IAAI,GAAGD,QAAQ,CAACE,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACd,MAAM,GAAG,CAAC,CAAC,CAACe,IAAI,CAAC,GAAG,CAAC;EAC3D,OAAO,GAAG,GAAGH,IAAI;AACrB,CAAC;AACD,MAAMI,WAAW,GAAGA,CAACL,QAAQ,EAAEM,OAAO,EAAEC,WAAW,KAAK;EACpD,IAAI5B,GAAG,GAAGoB,YAAY,CAACC,QAAQ,CAAC;EAChC,IAAIM,OAAO,EAAE;IACT3B,GAAG,GAAG,GAAG,GAAGA,GAAG;EACnB;EACA,IAAI4B,WAAW,KAAKC,SAAS,EAAE;IAC3B7B,GAAG,IAAI,GAAG,GAAG4B,WAAW;EAC5B;EACA,OAAO5B,GAAG;AACd,CAAC;AACD,MAAM8B,aAAa,GAAGA,CAACC,OAAO,EAAEC,IAAI,EAAEL,OAAO,EAAEN,QAAQ,EAAEY,SAAS,EAAEC,KAAK,EAAEN,WAAW,KAAK;EACvF,MAAM5B,GAAG,GAAG0B,WAAW,CAAC,CAAC,GAAGS,SAAS,CAACH,IAAI,CAAC,CAACX,QAAQ,EAAE,GAAGA,QAAQ,CAAC,EAAEM,OAAO,EAAEC,WAAW,CAAC;EACzF,IAAIK,SAAS,KAAKf,qBAAqB,EAAE;IACrCa,OAAO,CAACK,SAAS,CAACF,KAAK,EAAE,EAAE,EAAElC,GAAG,CAAC;EACrC,CAAC,MACI;IACD+B,OAAO,CAACM,YAAY,CAACH,KAAK,EAAE,EAAE,EAAElC,GAAG,CAAC;EACxC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsC,eAAe,GAAIC,KAAK,IAAK;EAC/B,MAAMlB,QAAQ,GAAG,EAAE;EACnB,KAAK,MAAMmB,KAAK,IAAID,KAAK,EAAE;IACvB,KAAK,MAAME,OAAO,IAAID,KAAK,CAACnB,QAAQ,EAAE;MAClC,IAAIoB,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACpB;QACA,MAAMC,KAAK,GAAGF,KAAK,CAACG,MAAM,IAAIH,KAAK,CAACG,MAAM,CAACF,OAAO,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5D,IAAI,CAACF,KAAK,EAAE;UACR,OAAO,IAAI;QACf;QACArB,QAAQ,CAACwB,IAAI,CAACH,KAAK,CAAC;MACxB,CAAC,MACI,IAAID,OAAO,KAAK,EAAE,EAAE;QACrBpB,QAAQ,CAACwB,IAAI,CAACJ,OAAO,CAAC;MAC1B;IACJ;EACJ;EACA,OAAOpB,QAAQ;AACnB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyB,YAAY,GAAGA,CAACC,MAAM,EAAE1B,QAAQ,KAAK;EACvC,IAAI0B,MAAM,CAACrC,MAAM,GAAGW,QAAQ,CAACX,MAAM,EAAE;IACjC,OAAO,IAAI;EACf;EACA,IAAIqC,MAAM,CAACrC,MAAM,IAAI,CAAC,IAAIqC,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;IACxC,OAAO1B,QAAQ;EACnB;EACA,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACrC,MAAM,EAAEsC,CAAC,EAAE,EAAE;IACpC,IAAID,MAAM,CAACC,CAAC,CAAC,KAAK3B,QAAQ,CAAC2B,CAAC,CAAC,EAAE;MAC3B,OAAO,IAAI;IACf;EACJ;EACA,IAAI3B,QAAQ,CAACX,MAAM,KAAKqC,MAAM,CAACrC,MAAM,EAAE;IACnC,OAAO,CAAC,EAAE,CAAC;EACf;EACA,OAAOW,QAAQ,CAACuB,KAAK,CAACG,MAAM,CAACrC,MAAM,CAAC;AACxC,CAAC;AACD,MAAMuC,YAAY,GAAGA,CAACC,GAAG,EAAElB,IAAI,EAAEL,OAAO,KAAK;EACzC,MAAMoB,MAAM,GAAGZ,SAAS,CAACH,IAAI,CAAC,CAACX,QAAQ;EACvC,MAAM8B,QAAQ,GAAGxB,OAAO,GAAGuB,GAAG,CAACE,IAAI,CAACR,KAAK,CAAC,CAAC,CAAC,GAAGM,GAAG,CAACC,QAAQ;EAC3D,MAAM9B,QAAQ,GAAGc,SAAS,CAACgB,QAAQ,CAAC,CAAC9B,QAAQ;EAC7C,OAAOyB,YAAY,CAACC,MAAM,EAAE1B,QAAQ,CAAC;AACzC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMc,SAAS,GAAIb,IAAI,IAAK;EACxB,IAAID,QAAQ,GAAG,CAAC,EAAE,CAAC;EACnB,IAAIO,WAAW;EACf,IAAIN,IAAI,IAAI,IAAI,EAAE;IACd,MAAM+B,OAAO,GAAG/B,IAAI,CAACgC,OAAO,CAAC,GAAG,CAAC;IACjC,IAAID,OAAO,GAAG,CAAC,CAAC,EAAE;MACdzB,WAAW,GAAGN,IAAI,CAACiC,SAAS,CAACF,OAAO,GAAG,CAAC,CAAC;MACzC/B,IAAI,GAAGA,IAAI,CAACiC,SAAS,CAAC,CAAC,EAAEF,OAAO,CAAC;IACrC;IACAhC,QAAQ,GAAGC,IAAI,CACVkC,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAEjC,CAAC,IAAKA,CAAC,CAACkC,IAAI,CAAC,CAAC,CAAC,CACpBnC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACd,MAAM,GAAG,CAAC,CAAC;IAChC,IAAIW,QAAQ,CAACX,MAAM,KAAK,CAAC,EAAE;MACvBW,QAAQ,GAAG,CAAC,EAAE,CAAC;IACnB;EACJ;EACA,OAAO;IAAEA,QAAQ;IAAEO;EAAY,CAAC;AACpC,CAAC;AAED,MAAM+B,WAAW,GAAIC,MAAM,IAAK;EAC5BC,OAAO,CAACC,KAAK,CAAC,qBAAqBF,MAAM,CAAClD,MAAM,GAAG,CAAC;EACpD,KAAK,MAAM6B,KAAK,IAAIqB,MAAM,EAAE;IACxB,MAAMvC,QAAQ,GAAG,EAAE;IACnBkB,KAAK,CAACwB,OAAO,CAAExF,CAAC,IAAK8C,QAAQ,CAACwB,IAAI,CAAC,GAAGtE,CAAC,CAAC8C,QAAQ,CAAC,CAAC;IAClD,MAAM2C,GAAG,GAAGzB,KAAK,CAACkB,GAAG,CAAElF,CAAC,IAAKA,CAAC,CAAC0F,EAAE,CAAC;IAClCJ,OAAO,CAACK,KAAK,CAAC,MAAM9C,YAAY,CAACC,QAAQ,CAAC,EAAE,EAAE,uCAAuC,EAAE,MAAM,EAAE,IAAI2C,GAAG,CAACvC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;EACzH;EACAoC,OAAO,CAACM,QAAQ,CAAC,CAAC;AACtB,CAAC;AACD,MAAMC,cAAc,GAAIC,SAAS,IAAK;EAClCR,OAAO,CAACC,KAAK,CAAC,wBAAwBO,SAAS,CAAC3D,MAAM,GAAG,CAAC;EAC1D,KAAK,MAAM4D,QAAQ,IAAID,SAAS,EAAE;IAC9B,IAAIC,QAAQ,CAACC,EAAE,EAAE;MACbV,OAAO,CAACK,KAAK,CAAC,QAAQ,EAAE,MAAM9C,YAAY,CAACkD,QAAQ,CAACE,IAAI,CAAC,EAAE,EAAE,mBAAmB,EAAE,OAAO,EAAE,MAAMpD,YAAY,CAACkD,QAAQ,CAACC,EAAE,CAAClD,QAAQ,CAAC,EAAE,EAAE,mBAAmB,CAAC;IAC/J;EACJ;EACAwC,OAAO,CAACM,QAAQ,CAAC,CAAC;AACtB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,cAAa;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAO3C,IAAI,EAAEO,KAAK,EAAEN,SAAS,EAAE2C,KAAK,EAAEC,OAAO,GAAG,KAAK,EAAEC,SAAS,EAAK;IACvF,IAAI;MACA;MACA,MAAMC,MAAM,GAAGC,aAAa,CAAChD,IAAI,CAAC;MAClC;MACA,IAAI4C,KAAK,IAAIrC,KAAK,CAAC7B,MAAM,IAAI,CAACqE,MAAM,EAAE;QAClC,OAAOF,OAAO;MAClB;MACA,MAAM,IAAII,OAAO,CAAEC,OAAO,IAAK3F,gBAAgB,CAACwF,MAAM,EAAEG,OAAO,CAAC,CAAC;MACjE,MAAM1C,KAAK,GAAGD,KAAK,CAACqC,KAAK,CAAC;MAC1B,MAAMO,MAAM,SAASJ,MAAM,CAACK,UAAU,CAAC5C,KAAK,CAACyB,EAAE,EAAEzB,KAAK,CAACG,MAAM,EAAEV,SAAS,EAAE6C,SAAS,CAAC;MACpF;MACA;MACA,IAAIK,MAAM,CAACN,OAAO,EAAE;QAChB5C,SAAS,GAAGhB,kBAAkB;QAC9B4D,OAAO,GAAG,IAAI;MAClB;MACA;MACAA,OAAO,SAASJ,cAAa,CAACU,MAAM,CAACE,OAAO,EAAE9C,KAAK,EAAEN,SAAS,EAAE2C,KAAK,GAAG,CAAC,EAAEC,OAAO,EAAEC,SAAS,CAAC;MAC9F;MACA;MACA,IAAIK,MAAM,CAACG,WAAW,EAAE;QACpB,MAAMH,MAAM,CAACG,WAAW,CAAC,CAAC;MAC9B;MACA,OAAOT,OAAO;IAClB,CAAC,CACD,OAAO5F,CAAC,EAAE;MACNL,aAAa,CAAC,4CAA4C,EAAEK,CAAC,CAAC;MAC9D,OAAO,KAAK;IAChB;EACJ,CAAC;EAAA,gBA9BKwF,aAAaA,CAAAc,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAhB,IAAA,CAAAiB,KAAA,OAAAC,SAAA;EAAA;AAAA,GA8BlB;AACD;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY;EAAA,IAAAC,KAAA,GAAAnB,iBAAA,CAAG,WAAO3C,IAAI,EAAK;IACjC,MAAMgC,GAAG,GAAG,EAAE;IACd,IAAIe,MAAM;IACV,IAAIgB,IAAI,GAAG/D,IAAI;IACf;IACA,OAAQ+C,MAAM,GAAGC,aAAa,CAACe,IAAI,CAAC,EAAG;MACnC,MAAM9B,EAAE,SAASc,MAAM,CAACiB,UAAU,CAAC,CAAC;MACpC,IAAI/B,EAAE,EAAE;QACJ8B,IAAI,GAAG9B,EAAE,CAACoB,OAAO;QACjBpB,EAAE,CAACoB,OAAO,GAAGxD,SAAS;QACtBmC,GAAG,CAACnB,IAAI,CAACoB,EAAE,CAAC;MAChB,CAAC,MACI;QACD;MACJ;IACJ;IACA,OAAO;MAAED,GAAG;MAAEe;IAAO,CAAC;EAC1B,CAAC;EAAA,gBAjBKc,YAAYA,CAAAI,GAAA;IAAA,OAAAH,KAAA,CAAAH,KAAA,OAAAC,SAAA;EAAA;AAAA,GAiBjB;AACD,MAAMM,gBAAgB,GAAGA,CAAA,KAAM;EAC3B,IAAIlB,aAAa,CAACmB,QAAQ,CAACC,IAAI,CAAC,EAAE;IAC9B,OAAOnB,OAAO,CAACC,OAAO,CAAC,CAAC;EAC5B;EACA,OAAO,IAAID,OAAO,CAAEC,OAAO,IAAK;IAC5BmB,MAAM,CAACC,gBAAgB,CAAC,gBAAgB,EAAE,MAAMpB,OAAO,CAAC,CAAC,EAAE;MAAEqB,IAAI,EAAE;IAAK,CAAC,CAAC;EAC9E,CAAC,CAAC;AACN,CAAC;AACD;AACA,MAAMC,eAAe,GAAG,4FAA4F;AACpH,MAAMxB,aAAa,GAAIhD,IAAI,IAAK;EAC5B,IAAI,CAACA,IAAI,EAAE;IACP,OAAOH,SAAS;EACpB;EACA,IAAIG,IAAI,CAACyE,OAAO,CAACD,eAAe,CAAC,EAAE;IAC/B,OAAOxE,IAAI;EACf;EACA,MAAM+C,MAAM,GAAG/C,IAAI,CAAC0E,aAAa,CAACF,eAAe,CAAC;EAClD,OAAOzB,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAGlD,SAAS;AACpE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8E,eAAe,GAAGA,CAACtF,QAAQ,EAAEiD,QAAQ,KAAK;EAC5C,MAAM;IAAEE,IAAI;IAAED;EAAG,CAAC,GAAGD,QAAQ;EAC7B,IAAIC,EAAE,KAAK1C,SAAS,EAAE;IAClB,OAAO,KAAK;EAChB;EACA,IAAI2C,IAAI,CAAC9D,MAAM,GAAGW,QAAQ,CAACX,MAAM,EAAE;IAC/B,OAAO,KAAK;EAChB;EACA,KAAK,IAAIsC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,IAAI,CAAC9D,MAAM,EAAEsC,CAAC,EAAE,EAAE;IAClC,MAAM4D,QAAQ,GAAGpC,IAAI,CAACxB,CAAC,CAAC;IACxB,IAAI4D,QAAQ,KAAK,GAAG,EAAE;MAClB,OAAO,IAAI;IACf;IACA,IAAIA,QAAQ,KAAKvF,QAAQ,CAAC2B,CAAC,CAAC,EAAE;MAC1B,OAAO,KAAK;IAChB;EACJ;EACA,OAAOwB,IAAI,CAAC9D,MAAM,KAAKW,QAAQ,CAACX,MAAM;AAC1C,CAAC;AACD;AACA,MAAMmG,iBAAiB,GAAGA,CAACxF,QAAQ,EAAEgD,SAAS,KAAK;EAC/C,OAAOA,SAAS,CAACyC,IAAI,CAAExC,QAAQ,IAAKqC,eAAe,CAACtF,QAAQ,EAAEiD,QAAQ,CAAC,CAAC;AAC5E,CAAC;AACD,MAAMyC,UAAU,GAAGA,CAAC/C,GAAG,EAAEzB,KAAK,KAAK;EAC/B,MAAMyE,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAClD,GAAG,CAACtD,MAAM,EAAE6B,KAAK,CAAC7B,MAAM,CAAC;EAC9C,IAAIyG,KAAK,GAAG,CAAC;EACb,KAAK,IAAInE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgE,GAAG,EAAEhE,CAAC,EAAE,EAAE;IAC1B,MAAMoE,OAAO,GAAGpD,GAAG,CAAChB,CAAC,CAAC;IACtB,MAAMqE,UAAU,GAAG9E,KAAK,CAACS,CAAC,CAAC;IAC3B;IACA,IAAIoE,OAAO,CAACnD,EAAE,CAACqD,WAAW,CAAC,CAAC,KAAKD,UAAU,CAACpD,EAAE,EAAE;MAC5C;IACJ;IACA,IAAImD,OAAO,CAACzE,MAAM,EAAE;MAChB,MAAM4E,aAAa,GAAGhH,MAAM,CAACC,IAAI,CAAC4G,OAAO,CAACzE,MAAM,CAAC;MACjD;MACA,IAAI4E,aAAa,CAAC7G,MAAM,KAAK2G,UAAU,CAAChG,QAAQ,CAACX,MAAM,EAAE;QACrD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAM8G,cAAc,GAAGD,aAAa,CAAC9D,GAAG,CAAE9C,GAAG,IAAK,IAAIA,GAAG,EAAE,CAAC;QAC5D,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoI,cAAc,CAAC9G,MAAM,EAAEtB,CAAC,EAAE,EAAE;UAC5C;UACA,IAAIoI,cAAc,CAACpI,CAAC,CAAC,CAACkI,WAAW,CAAC,CAAC,KAAKD,UAAU,CAAChG,QAAQ,CAACjC,CAAC,CAAC,EAAE;YAC5D;UACJ;UACA;UACA+H,KAAK,EAAE;QACX;MACJ;IACJ;IACA;IACAA,KAAK,EAAE;EACX;EACA,OAAOA,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,eAAe,GAAGA,CAACpG,QAAQ,EAAEkB,KAAK,KAAK;EACzC,MAAMmF,aAAa,GAAG,IAAIC,cAAc,CAACtG,QAAQ,CAAC;EAClD,IAAIuG,cAAc,GAAG,KAAK;EAC1B,IAAIC,SAAS;EACb,KAAK,IAAI7E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,KAAK,CAAC7B,MAAM,EAAEsC,CAAC,EAAE,EAAE;IACnC,MAAM8E,aAAa,GAAGvF,KAAK,CAACS,CAAC,CAAC,CAAC3B,QAAQ;IACvC,IAAIyG,aAAa,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;MACzBF,cAAc,GAAG,IAAI;IACzB,CAAC,MACI;MACD,KAAK,MAAMnF,OAAO,IAAIqF,aAAa,EAAE;QACjC,MAAMC,IAAI,GAAGL,aAAa,CAACM,IAAI,CAAC,CAAC;QACjC;QACA,IAAIvF,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UACpB,IAAIsF,IAAI,KAAK,EAAE,EAAE;YACb,OAAO,IAAI;UACf;UACAF,SAAS,GAAGA,SAAS,IAAI,EAAE;UAC3B,MAAMlF,MAAM,GAAGkF,SAAS,CAAC7E,CAAC,CAAC,KAAK6E,SAAS,CAAC7E,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UAClDL,MAAM,CAACF,OAAO,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGmF,IAAI;QACnC,CAAC,MACI,IAAIA,IAAI,KAAKtF,OAAO,EAAE;UACvB,OAAO,IAAI;QACf;MACJ;MACAmF,cAAc,GAAG,KAAK;IAC1B;EACJ;EACA,MAAMnB,OAAO,GAAGmB,cAAc,GAAGA,cAAc,MAAMF,aAAa,CAACM,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI;EACxF,IAAI,CAACvB,OAAO,EAAE;IACV,OAAO,IAAI;EACf;EACA,IAAIoB,SAAS,EAAE;IACX,OAAOtF,KAAK,CAACkB,GAAG,CAAC,CAACjB,KAAK,EAAEQ,CAAC,MAAM;MAC5BiB,EAAE,EAAEzB,KAAK,CAACyB,EAAE;MACZ5C,QAAQ,EAAEmB,KAAK,CAACnB,QAAQ;MACxBsB,MAAM,EAAEsF,WAAW,CAACzF,KAAK,CAACG,MAAM,EAAEkF,SAAS,CAAC7E,CAAC,CAAC,CAAC;MAC/CkF,WAAW,EAAE1F,KAAK,CAAC0F,WAAW;MAC9BC,WAAW,EAAE3F,KAAK,CAAC2F;IACvB,CAAC,CAAC,CAAC;EACP;EACA,OAAO5F,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA,MAAM0F,WAAW,GAAGA,CAACG,CAAC,EAAEC,CAAC,KAAK;EAC1B,OAAOD,CAAC,IAAIC,CAAC,GAAG9H,MAAM,CAAC+H,MAAM,CAAC/H,MAAM,CAAC+H,MAAM,CAAC,CAAC,CAAC,EAAEF,CAAC,CAAC,EAAEC,CAAC,CAAC,GAAGxG,SAAS;AACtE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0G,eAAe,GAAGA,CAACvE,GAAG,EAAEwE,MAAM,KAAK;EACrC,IAAIC,KAAK,GAAG,IAAI;EAChB,IAAIC,UAAU,GAAG,CAAC;EAClB,KAAK,MAAMnG,KAAK,IAAIiG,MAAM,EAAE;IACxB,MAAMrB,KAAK,GAAGJ,UAAU,CAAC/C,GAAG,EAAEzB,KAAK,CAAC;IACpC,IAAI4E,KAAK,GAAGuB,UAAU,EAAE;MACpBD,KAAK,GAAGlG,KAAK;MACbmG,UAAU,GAAGvB,KAAK;IACtB;EACJ;EACA,IAAIsB,KAAK,EAAE;IACP,OAAOA,KAAK,CAAChF,GAAG,CAAC,CAACjB,KAAK,EAAEQ,CAAC,KAAK;MAC3B,IAAI2F,EAAE;MACN,OAAQ;QACJ1E,EAAE,EAAEzB,KAAK,CAACyB,EAAE;QACZ5C,QAAQ,EAAEmB,KAAK,CAACnB,QAAQ;QACxBsB,MAAM,EAAEsF,WAAW,CAACzF,KAAK,CAACG,MAAM,EAAE,CAACgG,EAAE,GAAG3E,GAAG,CAAChB,CAAC,CAAC,MAAM,IAAI,IAAI2F,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAChG,MAAM;MAClG,CAAC;IACL,CAAC,CAAC;EACN;EACA,OAAO,IAAI;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiG,oBAAoB,GAAGA,CAACvH,QAAQ,EAAEmH,MAAM,KAAK;EAC/C,IAAIC,KAAK,GAAG,IAAI;EAChB,IAAII,SAAS,GAAG,CAAC;EACjB,KAAK,MAAMtG,KAAK,IAAIiG,MAAM,EAAE;IACxB,MAAMM,YAAY,GAAGrB,eAAe,CAACpG,QAAQ,EAAEkB,KAAK,CAAC;IACrD,IAAIuG,YAAY,KAAK,IAAI,EAAE;MACvB,MAAM3B,KAAK,GAAG4B,eAAe,CAACD,YAAY,CAAC;MAC3C,IAAI3B,KAAK,GAAG0B,SAAS,EAAE;QACnBA,SAAS,GAAG1B,KAAK;QACjBsB,KAAK,GAAGK,YAAY;MACxB;IACJ;EACJ;EACA,OAAOL,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,eAAe,GAAIxG,KAAK,IAAK;EAC/B,IAAI4E,KAAK,GAAG,CAAC;EACb,IAAI6B,KAAK,GAAG,CAAC;EACb,KAAK,MAAMxG,KAAK,IAAID,KAAK,EAAE;IACvB,KAAK,MAAME,OAAO,IAAID,KAAK,CAACnB,QAAQ,EAAE;MAClC,IAAIoB,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACpB0E,KAAK,IAAIF,IAAI,CAACgC,GAAG,CAAC,CAAC,EAAED,KAAK,CAAC;MAC/B,CAAC,MACI,IAAIvG,OAAO,KAAK,EAAE,EAAE;QACrB0E,KAAK,IAAIF,IAAI,CAACgC,GAAG,CAAC,CAAC,EAAED,KAAK,CAAC;MAC/B;MACAA,KAAK,EAAE;IACX;EACJ;EACA,OAAO7B,KAAK;AAChB,CAAC;AACD,MAAMQ,cAAc,CAAC;EACjB9H,WAAWA,CAACwB,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAACuB,KAAK,CAAC,CAAC;EACpC;EACAoF,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAAC3G,QAAQ,CAACX,MAAM,GAAG,CAAC,EAAE;MAC1B,OAAO,IAAI,CAACW,QAAQ,CAAC6H,KAAK,CAAC,CAAC;IAChC;IACA,OAAO,EAAE;EACb;AACJ;AAEA,MAAMC,QAAQ,GAAGA,CAACC,EAAE,EAAEC,IAAI,KAAK;EAC3B,IAAIA,IAAI,IAAID,EAAE,EAAE;IACZ,OAAOA,EAAE,CAACC,IAAI,CAAC;EACnB;EACA,IAAID,EAAE,CAACE,YAAY,CAACD,IAAI,CAAC,EAAE;IACvB,OAAOD,EAAE,CAACG,YAAY,CAACF,IAAI,CAAC;EAChC;EACA,OAAO,IAAI;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMG,aAAa,GAAIxH,IAAI,IAAK;EAC5B,OAAOyH,KAAK,CAACjF,IAAI,CAACxC,IAAI,CAAC0H,QAAQ,CAAC,CAC3BnI,MAAM,CAAE6H,EAAE,IAAKA,EAAE,CAACO,OAAO,KAAK,oBAAoB,CAAC,CACnDlG,GAAG,CAAE2F,EAAE,IAAK;IACb,MAAM7E,EAAE,GAAG4E,QAAQ,CAACC,EAAE,EAAE,IAAI,CAAC;IAC7B,OAAO;MACH5E,IAAI,EAAErC,SAAS,CAACgH,QAAQ,CAACC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC/H,QAAQ;MAC9CkD,EAAE,EAAEA,EAAE,IAAI,IAAI,GAAG1C,SAAS,GAAGM,SAAS,CAACoC,EAAE;IAC7C,CAAC;EACL,CAAC,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMqF,UAAU,GAAI5H,IAAI,IAAK;EACzB,OAAO6H,iBAAiB,CAACC,cAAc,CAAC9H,IAAI,CAAC,CAAC;AAClD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM8H,cAAc,GAAI/D,IAAI,IAAK;EAC7B,OAAO0D,KAAK,CAACjF,IAAI,CAACuB,IAAI,CAAC2D,QAAQ,CAAC,CAC3BnI,MAAM,CAAE6H,EAAE,IAAKA,EAAE,CAACO,OAAO,KAAK,WAAW,IAAIP,EAAE,CAACW,SAAS,CAAC,CAC1DtG,GAAG,CAAE2F,EAAE,IAAK;IACb,MAAMW,SAAS,GAAGZ,QAAQ,CAACC,EAAE,EAAE,WAAW,CAAC;IAC3C,OAAO;MACH/H,QAAQ,EAAEc,SAAS,CAACgH,QAAQ,CAACC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC/H,QAAQ;MACjD4C,EAAE,EAAE8F,SAAS,CAACzC,WAAW,CAAC,CAAC;MAC3B3E,MAAM,EAAEyG,EAAE,CAACY,cAAc;MACzB7B,WAAW,EAAEiB,EAAE,CAACjB,WAAW;MAC3BD,WAAW,EAAEkB,EAAE,CAAClB,WAAW;MAC3BwB,QAAQ,EAAEI,cAAc,CAACV,EAAE;IAC/B,CAAC;EACL,CAAC,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMS,iBAAiB,GAAII,KAAK,IAAK;EACjC,MAAMzB,MAAM,GAAG,EAAE;EACjB,KAAK,MAAMzC,IAAI,IAAIkE,KAAK,EAAE;IACtBC,WAAW,CAAC,EAAE,EAAE1B,MAAM,EAAEzC,IAAI,CAAC;EACjC;EACA,OAAOyC,MAAM;AACjB,CAAC;AACD;AACA,MAAM0B,WAAW,GAAGA,CAAC3H,KAAK,EAAEiG,MAAM,EAAEzC,IAAI,KAAK;EACzCxD,KAAK,GAAG,CACJ,GAAGA,KAAK,EACR;IACI0B,EAAE,EAAE8B,IAAI,CAAC9B,EAAE;IACX5C,QAAQ,EAAE0E,IAAI,CAAC1E,QAAQ;IACvBsB,MAAM,EAAEoD,IAAI,CAACpD,MAAM;IACnBwF,WAAW,EAAEpC,IAAI,CAACoC,WAAW;IAC7BD,WAAW,EAAEnC,IAAI,CAACmC;EACtB,CAAC,CACJ;EACD,IAAInC,IAAI,CAAC2D,QAAQ,CAAChJ,MAAM,KAAK,CAAC,EAAE;IAC5B8H,MAAM,CAAC3F,IAAI,CAACN,KAAK,CAAC;IAClB;EACJ;EACA,KAAK,MAAM4H,KAAK,IAAIpE,IAAI,CAAC2D,QAAQ,EAAE;IAC/BQ,WAAW,CAAC3H,KAAK,EAAEiG,MAAM,EAAE2B,KAAK,CAAC;EACrC;AACJ,CAAC;AAED,MAAMC,MAAM,GAAG,MAAM;EACjBvK,WAAWA,CAACC,OAAO,EAAE;IACjBtB,gBAAgB,CAAC,IAAI,EAAEsB,OAAO,CAAC;IAC/B,IAAI,CAACuK,kBAAkB,GAAG3L,WAAW,CAAC,IAAI,EAAE,oBAAoB,EAAE,CAAC,CAAC;IACpE,IAAI,CAAC4L,iBAAiB,GAAG5L,WAAW,CAAC,IAAI,EAAE,mBAAmB,EAAE,CAAC,CAAC;IAClE,IAAI,CAAC6L,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,IAAI,GAAG,KAAK;IACjB,IAAI,CAACtI,KAAK,GAAG,CAAC;IACd,IAAI,CAACuI,SAAS,GAAG,CAAC;IAClB;AACR;AACA;AACA;IACQ,IAAI,CAACzI,IAAI,GAAG,GAAG;IACf;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACL,OAAO,GAAG,IAAI;EACvB;EACM+I,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAhG,iBAAA;MACtB,MAAMuB,gBAAgB,CAAC,CAAC;MACxB,MAAM0E,UAAU,SAASD,KAAI,CAACE,SAAS,CAACF,KAAI,CAACG,WAAW,CAAC,CAAC,CAAC;MAC3D,IAAIF,UAAU,KAAK,IAAI,EAAE;QACrB,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;UAChC,MAAM;YAAEtG;UAAS,CAAC,GAAGsG,UAAU;UAC/B,MAAMtJ,IAAI,GAAGa,SAAS,CAACmC,QAAQ,CAAC;UAChCqG,KAAI,CAACI,WAAW,CAACzJ,IAAI,CAACD,QAAQ,EAAEJ,kBAAkB,EAAEK,IAAI,CAACM,WAAW,CAAC;UACrE,MAAM+I,KAAI,CAACK,iBAAiB,CAAC1J,IAAI,CAACD,QAAQ,EAAEJ,kBAAkB,CAAC;QACnE;MACJ,CAAC,MACI;QACD,MAAM0J,KAAI,CAACM,eAAe,CAAC,CAAC;MAChC;IAAC;EACL;EACAC,gBAAgBA,CAAA,EAAG;IACf7E,MAAM,CAACC,gBAAgB,CAAC,yBAAyB,EAAE7G,QAAQ,CAAC,IAAI,CAAC0L,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IACnG/E,MAAM,CAACC,gBAAgB,CAAC,qBAAqB,EAAE7G,QAAQ,CAAC,IAAI,CAACwL,eAAe,CAACG,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;EAClG;EACMC,UAAUA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA3G,iBAAA;MACf,MAAM1C,SAAS,GAAGqJ,MAAI,CAACC,gBAAgB,CAAC,CAAC;MACzC,IAAIlK,QAAQ,GAAGiK,MAAI,CAACR,WAAW,CAAC,CAAC;MACjC,MAAMF,UAAU,SAASU,MAAI,CAACT,SAAS,CAACxJ,QAAQ,CAAC;MACjD,IAAIuJ,UAAU,KAAK,IAAI,EAAE;QACrB,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;UAChCvJ,QAAQ,GAAGc,SAAS,CAACyI,UAAU,CAACtG,QAAQ,CAAC,CAACjD,QAAQ;QACtD,CAAC,MACI;UACD,OAAO,KAAK;QAChB;MACJ;MACA,OAAOiK,MAAI,CAACN,iBAAiB,CAAC3J,QAAQ,EAAEY,SAAS,CAAC;IAAC;EACvD;EACAuJ,YAAYA,CAACC,EAAE,EAAE;IACbA,EAAE,CAACC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAGC,kBAAkB,IAAK;MAC1C,IAAI,CAACC,IAAI,CAAC,CAAC;MACXD,kBAAkB,CAAC,CAAC;IACxB,CAAC,CAAC;EACN;EACA;EACME,aAAaA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAApH,iBAAA;MAClB,MAAMiG,UAAU,SAASmB,MAAI,CAAClB,SAAS,CAAC,CAAC;MACzC,IAAID,UAAU,KAAK,IAAI,EAAE;QACrB,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;UAChC,OAAOA,UAAU,CAACtG,QAAQ;QAC9B,CAAC,MACI;UACD,OAAO,KAAK;QAChB;MACJ;MACA,OAAO,IAAI;IAAC;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUzB,IAAIA,CAAAmJ,GAAA,EAAyC;IAAA,IAAAC,MAAA;IAAA,OAAAtH,iBAAA,YAAxCrD,IAAI,EAAEW,SAAS,GAAG,SAAS,EAAE6C,SAAS;MAC7C,IAAI6D,EAAE;MACN,IAAIrH,IAAI,CAAC4K,UAAU,CAAC,GAAG,CAAC,EAAE;QACtB,MAAMC,WAAW,GAAG,CAACxD,EAAE,GAAGsD,MAAI,CAAC1B,YAAY,MAAM,IAAI,IAAI5B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG;QACjF;QACA,MAAM3I,GAAG,GAAG,IAAIoM,GAAG,CAAC9K,IAAI,EAAE,gBAAgB6K,WAAW,EAAE,CAAC;QACxD7K,IAAI,GAAGtB,GAAG,CAACmD,QAAQ,GAAGnD,GAAG,CAACqM,MAAM;MACpC;MACA,IAAIC,UAAU,GAAGnK,SAAS,CAACb,IAAI,CAAC;MAChC,MAAMsJ,UAAU,SAASqB,MAAI,CAACpB,SAAS,CAACyB,UAAU,CAACjL,QAAQ,CAAC;MAC5D,IAAIuJ,UAAU,KAAK,IAAI,EAAE;QACrB,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;UAChC0B,UAAU,GAAGnK,SAAS,CAACyI,UAAU,CAACtG,QAAQ,CAAC;QAC/C,CAAC,MACI;UACD,OAAO,KAAK;QAChB;MACJ;MACA2H,MAAI,CAAClB,WAAW,CAACuB,UAAU,CAACjL,QAAQ,EAAEY,SAAS,EAAEqK,UAAU,CAAC1K,WAAW,CAAC;MACxE,OAAOqK,MAAI,CAACjB,iBAAiB,CAACsB,UAAU,CAACjL,QAAQ,EAAEY,SAAS,EAAE6C,SAAS,CAAC;IAAC,GAAAa,KAAA,OAAAC,SAAA;EAC7E;EACA;EACAiG,IAAIA,CAAA,EAAG;IACHxF,MAAM,CAACtE,OAAO,CAAC8J,IAAI,CAAC,CAAC;IACrB,OAAO5G,OAAO,CAACC,OAAO,CAAC,IAAI,CAACqH,WAAW,CAAC;EAC5C;EACA;EACMC,UAAUA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA9H,iBAAA;MACfhB,WAAW,CAACiG,UAAU,CAAC6C,MAAI,CAACrD,EAAE,CAAC,CAAC;MAChChF,cAAc,CAACoF,aAAa,CAACiD,MAAI,CAACrD,EAAE,CAAC,CAAC;IAAC;EAC3C;EACA;EACMsD,UAAUA,CAACzK,SAAS,EAAE;IAAA,IAAA0K,MAAA;IAAA,OAAAhI,iBAAA;MACxB,IAAIgI,MAAI,CAACnC,IAAI,EAAE;QACX1L,eAAe,CAAC,0DAA0D,CAAC;QAC3E,OAAO,KAAK;MAChB;MACA,MAAM;QAAEkF,GAAG;QAAEe;MAAO,CAAC,SAASc,YAAY,CAACQ,MAAM,CAACF,QAAQ,CAACC,IAAI,CAAC;MAChE,MAAMxC,MAAM,GAAGgG,UAAU,CAAC+C,MAAI,CAACvD,EAAE,CAAC;MAClC,MAAM7G,KAAK,GAAGgG,eAAe,CAACvE,GAAG,EAAEJ,MAAM,CAAC;MAC1C,IAAI,CAACrB,KAAK,EAAE;QACRzD,eAAe,CAAC,oCAAoC,EAAEkF,GAAG,CAACP,GAAG,CAAET,CAAC,IAAKA,CAAC,CAACiB,EAAE,CAAC,CAAC;QAC3E,OAAO,KAAK;MAChB;MACA,MAAM5C,QAAQ,GAAGiB,eAAe,CAACC,KAAK,CAAC;MACvC,IAAI,CAAClB,QAAQ,EAAE;QACXvC,eAAe,CAAC,oFAAoF,CAAC;QACrG,OAAO,KAAK;MAChB;MACA6N,MAAI,CAAC5B,WAAW,CAAC1J,QAAQ,EAAEY,SAAS,CAAC;MACrC,MAAM0K,MAAI,CAACC,iBAAiB,CAAC7H,MAAM,EAAExC,KAAK,EAAEtB,kBAAkB,EAAEI,QAAQ,EAAE,IAAI,EAAE2C,GAAG,CAACtD,MAAM,CAAC;MAC3F,OAAO,IAAI;IAAC;EAChB;EACA;EACAyK,iBAAiBA,CAAA,EAAG;IAChB,MAAM9J,QAAQ,GAAG,IAAI,CAACyJ,WAAW,CAAC,CAAC;IACnC,IAAIzJ,QAAQ,IAAIwF,iBAAiB,CAACxF,QAAQ,EAAEmI,aAAa,CAAC,IAAI,CAACJ,EAAE,CAAC,CAAC,EAAE;MACjE,IAAI,CAAC4B,iBAAiB,CAAC3J,QAAQ,EAAEJ,kBAAkB,CAAC;IACxD;EACJ;EACA;EACAgK,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACD,iBAAiB,CAAC,IAAI,CAACF,WAAW,CAAC,CAAC,EAAE7J,kBAAkB,CAAC;EACzE;EACAsK,gBAAgBA,CAAA,EAAG;IACf,IAAI5C,EAAE;IACN,MAAMkE,GAAG,GAAGxG,MAAM;IAClB,IAAIwG,GAAG,CAAC9K,OAAO,CAACG,KAAK,KAAK,IAAI,EAAE;MAC5B,IAAI,CAACA,KAAK,EAAE;MACZ2K,GAAG,CAAC9K,OAAO,CAACM,YAAY,CAAC,IAAI,CAACH,KAAK,EAAE2K,GAAG,CAAC1G,QAAQ,CAAC2G,KAAK,EAAE,CAACnE,EAAE,GAAGkE,GAAG,CAAC1G,QAAQ,CAAC4G,QAAQ,MAAM,IAAI,IAAIpE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqE,IAAI,CAAC;IACvI;IACA,MAAM9K,KAAK,GAAG2K,GAAG,CAAC9K,OAAO,CAACG,KAAK;IAC/B,MAAMuI,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,IAAI,CAACA,SAAS,GAAGvI,KAAK;IACtB,IAAIA,KAAK,GAAGuI,SAAS,IAAKvI,KAAK,IAAIuI,SAAS,IAAIA,SAAS,GAAG,CAAE,EAAE;MAC5D,OAAOvJ,qBAAqB;IAChC;IACA,IAAIgB,KAAK,GAAGuI,SAAS,EAAE;MACnB,OAAOtJ,kBAAkB;IAC7B;IACA,OAAOF,kBAAkB;EAC7B;EACM+J,iBAAiBA,CAAC3J,QAAQ,EAAEY,SAAS,EAAE6C,SAAS,EAAE;IAAA,IAAAmI,MAAA;IAAA,OAAAtI,iBAAA;MACpD,IAAI,CAACtD,QAAQ,EAAE;QACXzC,aAAa,CAAC,oDAAoD,CAAC;QACnE,OAAO,KAAK;MAChB;MACA;MACA,MAAMyF,SAAS,GAAGmF,aAAa,CAACyD,MAAI,CAAC7D,EAAE,CAAC;MACxC,MAAM9E,QAAQ,GAAGuC,iBAAiB,CAACxF,QAAQ,EAAEgD,SAAS,CAAC;MACvD,IAAI6I,YAAY,GAAG,IAAI;MACvB,IAAI5I,QAAQ,EAAE;QACV,MAAM;UAAEjD,QAAQ,EAAE8L,UAAU;UAAEvL;QAAY,CAAC,GAAG0C,QAAQ,CAACC,EAAE;QACzD0I,MAAI,CAAClC,WAAW,CAACoC,UAAU,EAAElL,SAAS,EAAEL,WAAW,CAAC;QACpDsL,YAAY,GAAG5I,QAAQ,CAACE,IAAI;QAC5BnD,QAAQ,GAAG8L,UAAU;MACzB;MACA;MACA,MAAMvJ,MAAM,GAAGgG,UAAU,CAACqD,MAAI,CAAC7D,EAAE,CAAC;MAClC,MAAM7G,KAAK,GAAGqG,oBAAoB,CAACvH,QAAQ,EAAEuC,MAAM,CAAC;MACpD,IAAI,CAACrB,KAAK,EAAE;QACR3D,aAAa,CAAC,mDAAmD,CAAC;QAClE,OAAO,KAAK;MAChB;MACA;MACA,OAAOqO,MAAI,CAACL,iBAAiB,CAACzG,QAAQ,CAACC,IAAI,EAAE7D,KAAK,EAAEN,SAAS,EAAEZ,QAAQ,EAAE6L,YAAY,EAAE,CAAC,EAAEpI,SAAS,CAAC;IAAC;EACzG;EACM8H,iBAAiBA,CAAAQ,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAuE;IAAA,IAAAC,MAAA;IAAA,OAAA9I,iBAAA,YAAtEoB,IAAI,EAAExD,KAAK,EAAEN,SAAS,EAAEZ,QAAQ,EAAE6L,YAAY,EAAEtI,KAAK,GAAG,CAAC,EAAEE,SAAS;MACxF,MAAM4I,MAAM,SAASD,MAAI,CAACE,IAAI,CAAC,CAAC;MAChC,IAAI9I,OAAO,GAAG,KAAK;MACnB,IAAI;QACAA,OAAO,SAAS4I,MAAI,CAAChJ,aAAa,CAACsB,IAAI,EAAExD,KAAK,EAAEN,SAAS,EAAEZ,QAAQ,EAAE6L,YAAY,EAAEtI,KAAK,EAAEE,SAAS,CAAC;MACxG,CAAC,CACD,OAAO7F,CAAC,EAAE;QACNL,aAAa,CAAC,gDAAgD,EAAEK,CAAC,CAAC;MACtE;MACAyO,MAAM,CAAC,CAAC;MACR,OAAO7I,OAAO;IAAC,GAAAc,KAAA,OAAAC,SAAA;EACnB;EACM+H,IAAIA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAjJ,iBAAA;MACT,MAAMnF,CAAC,GAAGoO,MAAI,CAACrB,WAAW;MAC1B,IAAIrH,OAAO;MACX0I,MAAI,CAACrB,WAAW,GAAG,IAAItH,OAAO,CAAE1G,CAAC,IAAM2G,OAAO,GAAG3G,CAAE,CAAC;MACpD,IAAIiB,CAAC,KAAKqC,SAAS,EAAE;QACjB,MAAMrC,CAAC;MACX;MACA,OAAO0F,OAAO;IAAC;EACnB;EACA;AACJ;AACA;AACA;AACA;AACA;EACU2F,SAASA,CAAA,EAAgC;IAAA,IAAAgD,MAAA;IAAA,OAAAlJ,iBAAA,YAA/BJ,EAAE,GAAGsJ,MAAI,CAAC/C,WAAW,CAAC,CAAC,EAAEtG,IAAI;MACzC,IAAIA,IAAI,KAAK3C,SAAS,EAAE;QACpB2C,IAAI,GAAGrC,SAAS,CAAC0L,MAAI,CAACtD,YAAY,CAAC,CAAClJ,QAAQ;MAChD;MACA,IAAI,CAACkD,EAAE,IAAI,CAACC,IAAI,EAAE;QACd,OAAO,IAAI;MACf;MACA,MAAMZ,MAAM,GAAGgG,UAAU,CAACiE,MAAI,CAACzE,EAAE,CAAC;MAClC,MAAM0E,SAAS,GAAGlF,oBAAoB,CAACpE,IAAI,EAAEZ,MAAM,CAAC;MACpD;MACA,MAAMmK,eAAe,GAAGD,SAAS,IAAIA,SAAS,CAACA,SAAS,CAACpN,MAAM,GAAG,CAAC,CAAC,CAACyH,WAAW;MAChF,MAAM6F,QAAQ,GAAGD,eAAe,SAASA,eAAe,CAAC,CAAC,GAAG,IAAI;MACjE,IAAIC,QAAQ,KAAK,KAAK,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QACpD,OAAOA,QAAQ;MACnB;MACA,MAAMC,OAAO,GAAGrF,oBAAoB,CAACrE,EAAE,EAAEX,MAAM,CAAC;MAChD;MACA,MAAMsK,eAAe,GAAGD,OAAO,IAAIA,OAAO,CAACA,OAAO,CAACvN,MAAM,GAAG,CAAC,CAAC,CAACwH,WAAW;MAC1E,OAAOgG,eAAe,GAAGA,eAAe,CAAC,CAAC,GAAG,IAAI;IAAC,GAAAvI,KAAA,OAAAC,SAAA;EACtD;EACMnB,aAAaA,CAAA0J,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAuE;IAAA,IAAAC,MAAA;IAAA,OAAA7J,iBAAA,YAAtEoB,IAAI,EAAExD,KAAK,EAAEN,SAAS,EAAEZ,QAAQ,EAAE6L,YAAY,EAAEtI,KAAK,GAAG,CAAC,EAAEE,SAAS;MACpF,IAAI0J,MAAI,CAAChE,IAAI,EAAE;QACX1L,eAAe,CAAC,0DAA0D,CAAC;QAC3E,OAAO,KAAK;MAChB;MACA0P,MAAI,CAAChE,IAAI,GAAG,IAAI;MAChB;MACA,MAAMiE,UAAU,GAAGD,MAAI,CAACE,gBAAgB,CAACrN,QAAQ,EAAE6L,YAAY,CAAC;MAChE,IAAIuB,UAAU,EAAE;QACZD,MAAI,CAACnE,kBAAkB,CAAClK,IAAI,CAACsO,UAAU,CAAC;MAC5C;MACA,MAAM5J,OAAO,SAASJ,cAAa,CAACsB,IAAI,EAAExD,KAAK,EAAEN,SAAS,EAAE2C,KAAK,EAAE,KAAK,EAAEE,SAAS,CAAC;MACpF0J,MAAI,CAAChE,IAAI,GAAG,KAAK;MACjB;MACA,IAAIiE,UAAU,EAAE;QACZD,MAAI,CAAClE,iBAAiB,CAACnK,IAAI,CAACsO,UAAU,CAAC;MAC3C;MACA,OAAO5J,OAAO;IAAC,GAAAc,KAAA,OAAAC,SAAA;EACnB;EACAmF,WAAWA,CAAC1J,QAAQ,EAAEY,SAAS,EAAEL,WAAW,EAAE;IAC1C,IAAI,CAACM,KAAK,EAAE;IACZJ,aAAa,CAACuE,MAAM,CAACtE,OAAO,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACL,OAAO,EAAEN,QAAQ,EAAEY,SAAS,EAAE,IAAI,CAACC,KAAK,EAAEN,WAAW,CAAC;EACxG;EACAkJ,WAAWA,CAAA,EAAG;IACV,OAAO7H,YAAY,CAACoD,MAAM,CAAC0G,QAAQ,EAAE,IAAI,CAAC/K,IAAI,EAAE,IAAI,CAACL,OAAO,CAAC;EACjE;EACA+M,gBAAgBA,CAACvB,UAAU,EAAEwB,oBAAoB,EAAE;IAC/C,MAAMnK,IAAI,GAAG,IAAI,CAAC+F,YAAY;IAC9B,MAAMhG,EAAE,GAAGnD,YAAY,CAAC+L,UAAU,CAAC;IACnC,IAAI,CAAC5C,YAAY,GAAGhG,EAAE;IACtB,IAAIA,EAAE,KAAKC,IAAI,EAAE;MACb,OAAO,IAAI;IACf;IACA,MAAMoK,cAAc,GAAGD,oBAAoB,GAAGvN,YAAY,CAACuN,oBAAoB,CAAC,GAAG,IAAI;IACvF,OAAO;MACHnK,IAAI;MACJoK,cAAc;MACdrK;IACJ,CAAC;EACL;EACA,IAAI6E,EAAEA,CAAA,EAAG;IAAE,OAAOpK,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AAED,MAAM6P,aAAa,GAAG,6ZAA6Z;AAEnb,MAAMC,UAAU,GAAG,MAAM;EACrBjP,WAAWA,CAACC,OAAO,EAAE;IACjBtB,gBAAgB,CAAC,IAAI,EAAEsB,OAAO,CAAC;IAC/B;AACR;AACA;AACA;IACQ,IAAI,CAACiP,eAAe,GAAG,SAAS;IAChC,IAAI,CAACC,OAAO,GAAIvD,EAAE,IAAK;MACnB/L,OAAO,CAAC,IAAI,CAACsN,IAAI,EAAEvB,EAAE,EAAE,IAAI,CAACsD,eAAe,EAAE,IAAI,CAACE,eAAe,CAAC;IACtE,CAAC;EACL;EACAC,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGjQ,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMkQ,KAAK,GAAG;MACVpC,IAAI,EAAE,IAAI,CAACA,IAAI;MACfqC,GAAG,EAAE,IAAI,CAACA,GAAG;MACbC,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC;IACD,OAAQnQ,CAAC,CAACE,IAAI,EAAE;MAAEsB,GAAG,EAAE,0CAA0C;MAAEqO,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEO,KAAK,EAAE5P,kBAAkB,CAAC,IAAI,CAAC6P,KAAK,EAAE;QACxH,CAACL,IAAI,GAAG,IAAI;QACZ,iBAAiB,EAAE;MACvB,CAAC;IAAE,CAAC,EAAEhQ,CAAC,CAAC,GAAG,EAAEoB,MAAM,CAAC+H,MAAM,CAAC;MAAE3H,GAAG,EAAE;IAA2C,CAAC,EAAEyO,KAAK,CAAC,EAAEjQ,CAAC,CAAC,MAAM,EAAE;MAAEwB,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,CAAC;EAChK;AACJ,CAAC;AACDmO,UAAU,CAACW,KAAK,GAAGZ,aAAa;AAEhC,SAASjP,KAAK,IAAI8P,SAAS,EAAE5O,aAAa,IAAI6O,kBAAkB,EAAEvF,MAAM,IAAIwF,UAAU,EAAEd,UAAU,IAAIe,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}