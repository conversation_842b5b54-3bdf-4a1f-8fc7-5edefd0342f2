{"ast": null, "code": "import { Observable } from '../Observable';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { OperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { innerFrom } from './innerFrom';\nexport function onErrorResumeNext(...sources) {\n  const nextSources = argsOrArgArray(sources);\n  return new Observable(subscriber => {\n    let sourceIndex = 0;\n    const subscribeNext = () => {\n      if (sourceIndex < nextSources.length) {\n        let nextSource;\n        try {\n          nextSource = innerFrom(nextSources[sourceIndex++]);\n        } catch (err) {\n          subscribeNext();\n          return;\n        }\n        const innerSubscriber = new OperatorSubscriber(subscriber, undefined, noop, noop);\n        nextSource.subscribe(innerSubscriber);\n        innerSubscriber.add(subscribeNext);\n      } else {\n        subscriber.complete();\n      }\n    };\n    subscribeNext();\n  });\n}", "map": {"version": 3, "names": ["Observable", "argsOrArgArray", "OperatorSubscriber", "noop", "innerFrom", "onErrorResumeNext", "sources", "nextSources", "subscriber", "sourceIndex", "subscribeNext", "length", "nextSource", "err", "innerSubscriber", "undefined", "subscribe", "add", "complete"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/rxjs/dist/esm/internal/observable/onErrorResumeNext.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { OperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { innerFrom } from './innerFrom';\nexport function onErrorResumeNext(...sources) {\n    const nextSources = argsOrArgArray(sources);\n    return new Observable((subscriber) => {\n        let sourceIndex = 0;\n        const subscribeNext = () => {\n            if (sourceIndex < nextSources.length) {\n                let nextSource;\n                try {\n                    nextSource = innerFrom(nextSources[sourceIndex++]);\n                }\n                catch (err) {\n                    subscribeNext();\n                    return;\n                }\n                const innerSubscriber = new OperatorSubscriber(subscriber, undefined, noop, noop);\n                nextSource.subscribe(innerSubscriber);\n                innerSubscriber.add(subscribeNext);\n            }\n            else {\n                subscriber.complete();\n            }\n        };\n        subscribeNext();\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,SAAS,QAAQ,aAAa;AACvC,OAAO,SAASC,iBAAiBA,CAAC,GAAGC,OAAO,EAAE;EAC1C,MAAMC,WAAW,GAAGN,cAAc,CAACK,OAAO,CAAC;EAC3C,OAAO,IAAIN,UAAU,CAAEQ,UAAU,IAAK;IAClC,IAAIC,WAAW,GAAG,CAAC;IACnB,MAAMC,aAAa,GAAGA,CAAA,KAAM;MACxB,IAAID,WAAW,GAAGF,WAAW,CAACI,MAAM,EAAE;QAClC,IAAIC,UAAU;QACd,IAAI;UACAA,UAAU,GAAGR,SAAS,CAACG,WAAW,CAACE,WAAW,EAAE,CAAC,CAAC;QACtD,CAAC,CACD,OAAOI,GAAG,EAAE;UACRH,aAAa,CAAC,CAAC;UACf;QACJ;QACA,MAAMI,eAAe,GAAG,IAAIZ,kBAAkB,CAACM,UAAU,EAAEO,SAAS,EAAEZ,IAAI,EAAEA,IAAI,CAAC;QACjFS,UAAU,CAACI,SAAS,CAACF,eAAe,CAAC;QACrCA,eAAe,CAACG,GAAG,CAACP,aAAa,CAAC;MACtC,CAAC,MACI;QACDF,UAAU,CAACU,QAAQ,CAAC,CAAC;MACzB;IACJ,CAAC;IACDR,aAAa,CAAC,CAAC;EACnB,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}