{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as createAnimation } from './animation-BWcUKtbn.js';\nimport { g as getIonPageElement } from './index-DfBA5ztX.js';\nimport './index-B_U9CtaY.js';\nimport './index-ZjP4CjeZ.js';\nimport './helpers-1O4D2b7y.js';\nconst mdTransitionAnimation = (_, opts) => {\n  var _a, _b, _c;\n  const OFF_BOTTOM = '40px';\n  const CENTER = '0px';\n  const backDirection = opts.direction === 'back';\n  const enteringEl = opts.enteringEl;\n  const leavingEl = opts.leavingEl;\n  const ionPageElement = getIonPageElement(enteringEl);\n  const enteringToolbarEle = ionPageElement.querySelector('ion-toolbar');\n  const rootTransition = createAnimation();\n  rootTransition.addElement(ionPageElement).fill('both').beforeRemoveClass('ion-page-invisible');\n  // animate the component itself\n  if (backDirection) {\n    rootTransition.duration(((_a = opts.duration) !== null && _a !== void 0 ? _a : 0) || 200).easing('cubic-bezier(0.47,0,0.745,0.715)');\n  } else {\n    rootTransition.duration(((_b = opts.duration) !== null && _b !== void 0 ? _b : 0) || 280).easing('cubic-bezier(0.36,0.66,0.04,1)').fromTo('transform', `translateY(${OFF_BOTTOM})`, `translateY(${CENTER})`).fromTo('opacity', 0.01, 1);\n  }\n  // Animate toolbar if it's there\n  if (enteringToolbarEle) {\n    const enteringToolBar = createAnimation();\n    enteringToolBar.addElement(enteringToolbarEle);\n    rootTransition.addAnimation(enteringToolBar);\n  }\n  // setup leaving view\n  if (leavingEl && backDirection) {\n    // leaving content\n    rootTransition.duration(((_c = opts.duration) !== null && _c !== void 0 ? _c : 0) || 200).easing('cubic-bezier(0.47,0,0.745,0.715)');\n    const leavingPage = createAnimation();\n    leavingPage.addElement(getIonPageElement(leavingEl)).onFinish(currentStep => {\n      if (currentStep === 1 && leavingPage.elements.length > 0) {\n        leavingPage.elements[0].style.setProperty('display', 'none');\n      }\n    }).fromTo('transform', `translateY(${CENTER})`, `translateY(${OFF_BOTTOM})`).fromTo('opacity', 1, 0);\n    rootTransition.addAnimation(leavingPage);\n  }\n  return rootTransition;\n};\nexport { mdTransitionAnimation };", "map": {"version": 3, "names": ["c", "createAnimation", "g", "getIonPageElement", "mdTransitionAnimation", "_", "opts", "_a", "_b", "_c", "OFF_BOTTOM", "CENTER", "backDirection", "direction", "enteringEl", "leavingEl", "ionPageElement", "enteringToolbarEle", "querySelector", "rootTransition", "addElement", "fill", "beforeRemoveClass", "duration", "easing", "fromTo", "enteringToolBar", "addAnimation", "leavingPage", "onFinish", "currentStep", "elements", "length", "style", "setProperty"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@ionic/core/dist/esm/md.transition-CLI683c7.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as createAnimation } from './animation-BWcUKtbn.js';\nimport { g as getIonPageElement } from './index-DfBA5ztX.js';\nimport './index-B_U9CtaY.js';\nimport './index-ZjP4CjeZ.js';\nimport './helpers-1O4D2b7y.js';\n\nconst mdTransitionAnimation = (_, opts) => {\n    var _a, _b, _c;\n    const OFF_BOTTOM = '40px';\n    const CENTER = '0px';\n    const backDirection = opts.direction === 'back';\n    const enteringEl = opts.enteringEl;\n    const leavingEl = opts.leavingEl;\n    const ionPageElement = getIonPageElement(enteringEl);\n    const enteringToolbarEle = ionPageElement.querySelector('ion-toolbar');\n    const rootTransition = createAnimation();\n    rootTransition.addElement(ionPageElement).fill('both').beforeRemoveClass('ion-page-invisible');\n    // animate the component itself\n    if (backDirection) {\n        rootTransition.duration(((_a = opts.duration) !== null && _a !== void 0 ? _a : 0) || 200).easing('cubic-bezier(0.47,0,0.745,0.715)');\n    }\n    else {\n        rootTransition\n            .duration(((_b = opts.duration) !== null && _b !== void 0 ? _b : 0) || 280)\n            .easing('cubic-bezier(0.36,0.66,0.04,1)')\n            .fromTo('transform', `translateY(${OFF_BOTTOM})`, `translateY(${CENTER})`)\n            .fromTo('opacity', 0.01, 1);\n    }\n    // Animate toolbar if it's there\n    if (enteringToolbarEle) {\n        const enteringToolBar = createAnimation();\n        enteringToolBar.addElement(enteringToolbarEle);\n        rootTransition.addAnimation(enteringToolBar);\n    }\n    // setup leaving view\n    if (leavingEl && backDirection) {\n        // leaving content\n        rootTransition.duration(((_c = opts.duration) !== null && _c !== void 0 ? _c : 0) || 200).easing('cubic-bezier(0.47,0,0.745,0.715)');\n        const leavingPage = createAnimation();\n        leavingPage\n            .addElement(getIonPageElement(leavingEl))\n            .onFinish((currentStep) => {\n            if (currentStep === 1 && leavingPage.elements.length > 0) {\n                leavingPage.elements[0].style.setProperty('display', 'none');\n            }\n        })\n            .fromTo('transform', `translateY(${CENTER})`, `translateY(${OFF_BOTTOM})`)\n            .fromTo('opacity', 1, 0);\n        rootTransition.addAnimation(leavingPage);\n    }\n    return rootTransition;\n};\n\nexport { mdTransitionAnimation };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,eAAe,QAAQ,yBAAyB;AAC9D,SAASC,CAAC,IAAIC,iBAAiB,QAAQ,qBAAqB;AAC5D,OAAO,qBAAqB;AAC5B,OAAO,qBAAqB;AAC5B,OAAO,uBAAuB;AAE9B,MAAMC,qBAAqB,GAAGA,CAACC,CAAC,EAAEC,IAAI,KAAK;EACvC,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACd,MAAMC,UAAU,GAAG,MAAM;EACzB,MAAMC,MAAM,GAAG,KAAK;EACpB,MAAMC,aAAa,GAAGN,IAAI,CAACO,SAAS,KAAK,MAAM;EAC/C,MAAMC,UAAU,GAAGR,IAAI,CAACQ,UAAU;EAClC,MAAMC,SAAS,GAAGT,IAAI,CAACS,SAAS;EAChC,MAAMC,cAAc,GAAGb,iBAAiB,CAACW,UAAU,CAAC;EACpD,MAAMG,kBAAkB,GAAGD,cAAc,CAACE,aAAa,CAAC,aAAa,CAAC;EACtE,MAAMC,cAAc,GAAGlB,eAAe,CAAC,CAAC;EACxCkB,cAAc,CAACC,UAAU,CAACJ,cAAc,CAAC,CAACK,IAAI,CAAC,MAAM,CAAC,CAACC,iBAAiB,CAAC,oBAAoB,CAAC;EAC9F;EACA,IAAIV,aAAa,EAAE;IACfO,cAAc,CAACI,QAAQ,CAAC,CAAC,CAAChB,EAAE,GAAGD,IAAI,CAACiB,QAAQ,MAAM,IAAI,IAAIhB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,KAAK,GAAG,CAAC,CAACiB,MAAM,CAAC,kCAAkC,CAAC;EACxI,CAAC,MACI;IACDL,cAAc,CACTI,QAAQ,CAAC,CAAC,CAACf,EAAE,GAAGF,IAAI,CAACiB,QAAQ,MAAM,IAAI,IAAIf,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,KAAK,GAAG,CAAC,CAC1EgB,MAAM,CAAC,gCAAgC,CAAC,CACxCC,MAAM,CAAC,WAAW,EAAE,cAAcf,UAAU,GAAG,EAAE,cAAcC,MAAM,GAAG,CAAC,CACzEc,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;EACnC;EACA;EACA,IAAIR,kBAAkB,EAAE;IACpB,MAAMS,eAAe,GAAGzB,eAAe,CAAC,CAAC;IACzCyB,eAAe,CAACN,UAAU,CAACH,kBAAkB,CAAC;IAC9CE,cAAc,CAACQ,YAAY,CAACD,eAAe,CAAC;EAChD;EACA;EACA,IAAIX,SAAS,IAAIH,aAAa,EAAE;IAC5B;IACAO,cAAc,CAACI,QAAQ,CAAC,CAAC,CAACd,EAAE,GAAGH,IAAI,CAACiB,QAAQ,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,KAAK,GAAG,CAAC,CAACe,MAAM,CAAC,kCAAkC,CAAC;IACpI,MAAMI,WAAW,GAAG3B,eAAe,CAAC,CAAC;IACrC2B,WAAW,CACNR,UAAU,CAACjB,iBAAiB,CAACY,SAAS,CAAC,CAAC,CACxCc,QAAQ,CAAEC,WAAW,IAAK;MAC3B,IAAIA,WAAW,KAAK,CAAC,IAAIF,WAAW,CAACG,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;QACtDJ,WAAW,CAACG,QAAQ,CAAC,CAAC,CAAC,CAACE,KAAK,CAACC,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC;MAChE;IACJ,CAAC,CAAC,CACGT,MAAM,CAAC,WAAW,EAAE,cAAcd,MAAM,GAAG,EAAE,cAAcD,UAAU,GAAG,CAAC,CACzEe,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BN,cAAc,CAACQ,YAAY,CAACC,WAAW,CAAC;EAC5C;EACA,OAAOT,cAAc;AACzB,CAAC;AAED,SAASf,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}