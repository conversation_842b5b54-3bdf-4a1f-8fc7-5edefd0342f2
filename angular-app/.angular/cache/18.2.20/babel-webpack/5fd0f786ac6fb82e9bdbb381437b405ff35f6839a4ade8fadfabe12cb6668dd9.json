{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index-ZjP4CjeZ.js';\nimport { MENU_BACK_BUTTON_PRIORITY } from './hardware-back-button-DcH0BbDp.js';\nimport { e as getIonMode, m as printIonWarning } from './index-B_U9CtaY.js';\nimport { c as componentOnReady } from './helpers-1O4D2b7y.js';\nimport { c as createAnimation } from './animation-BWcUKtbn.js';\n\n/**\n * baseAnimation\n * Base class which is extended by the various types. Each\n * type will provide their own animations for open and close\n * and registers itself with Menu.\n */\nconst baseAnimation = isIos => {\n  // https://material.io/guidelines/motion/movement.html#movement-movement-in-out-of-screen-bounds\n  // https://material.io/guidelines/motion/duration-easing.html#duration-easing-natural-easing-curves\n  /**\n   * \"Apply the sharp curve to items temporarily leaving the screen that may return\n   * from the same exit point. When they return, use the deceleration curve. On mobile,\n   * this transition typically occurs over 300ms\" -- MD Motion Guide\n   */\n  return createAnimation().duration(isIos ? 400 : 300);\n};\n\n/**\n * Menu Overlay Type\n * The menu slides over the content. The content\n * itself, which is under the menu, does not move.\n */\nconst menuOverlayAnimation = menu => {\n  let closedX;\n  let openedX;\n  const width = menu.width + 8;\n  const menuAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  if (menu.isEndSide) {\n    // right side\n    closedX = width + 'px';\n    openedX = '0px';\n  } else {\n    // left side\n    closedX = -width + 'px';\n    openedX = '0px';\n  }\n  menuAnimation.addElement(menu.menuInnerEl).fromTo('transform', `translateX(${closedX})`, `translateX(${openedX})`);\n  const mode = getIonMode(menu);\n  const isIos = mode === 'ios';\n  const opacity = isIos ? 0.2 : 0.25;\n  backdropAnimation.addElement(menu.backdropEl).fromTo('opacity', 0.01, opacity);\n  return baseAnimation(isIos).addAnimation([menuAnimation, backdropAnimation]);\n};\n\n/**\n * Menu Push Type\n * The content slides over to reveal the menu underneath.\n * The menu itself also slides over to reveal its bad self.\n */\nconst menuPushAnimation = menu => {\n  let contentOpenedX;\n  let menuClosedX;\n  const mode = getIonMode(menu);\n  const width = menu.width;\n  if (menu.isEndSide) {\n    contentOpenedX = -width + 'px';\n    menuClosedX = width + 'px';\n  } else {\n    contentOpenedX = width + 'px';\n    menuClosedX = -width + 'px';\n  }\n  const menuAnimation = createAnimation().addElement(menu.menuInnerEl).fromTo('transform', `translateX(${menuClosedX})`, 'translateX(0px)');\n  const contentAnimation = createAnimation().addElement(menu.contentEl).fromTo('transform', 'translateX(0px)', `translateX(${contentOpenedX})`);\n  const backdropAnimation = createAnimation().addElement(menu.backdropEl).fromTo('opacity', 0.01, 0.32);\n  return baseAnimation(mode === 'ios').addAnimation([menuAnimation, contentAnimation, backdropAnimation]);\n};\n\n/**\n * Menu Reveal Type\n * The content slides over to reveal the menu underneath.\n * The menu itself, which is under the content, does not move.\n */\nconst menuRevealAnimation = menu => {\n  const mode = getIonMode(menu);\n  const openedX = menu.width * (menu.isEndSide ? -1 : 1) + 'px';\n  const contentOpen = createAnimation().addElement(menu.contentEl) // REVIEW\n  .fromTo('transform', 'translateX(0px)', `translateX(${openedX})`);\n  return baseAnimation(mode === 'ios').addAnimation(contentOpen);\n};\nconst createMenuController = () => {\n  const menuAnimations = new Map();\n  const menus = [];\n  const open = /*#__PURE__*/function () {\n    var _ref = _asyncToGenerator(function* (menu) {\n      const menuEl = yield get(menu, true);\n      if (menuEl) {\n        return menuEl.open();\n      }\n      return false;\n    });\n    return function open(_x) {\n      return _ref.apply(this, arguments);\n    };\n  }();\n  const close = /*#__PURE__*/function () {\n    var _ref2 = _asyncToGenerator(function* (menu) {\n      const menuEl = yield menu !== undefined ? get(menu, true) : getOpen();\n      if (menuEl !== undefined) {\n        return menuEl.close();\n      }\n      return false;\n    });\n    return function close(_x2) {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  const toggle = /*#__PURE__*/function () {\n    var _ref3 = _asyncToGenerator(function* (menu) {\n      const menuEl = yield get(menu, true);\n      if (menuEl) {\n        return menuEl.toggle();\n      }\n      return false;\n    });\n    return function toggle(_x3) {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  const enable = /*#__PURE__*/function () {\n    var _ref4 = _asyncToGenerator(function* (shouldEnable, menu) {\n      const menuEl = yield get(menu);\n      if (menuEl) {\n        menuEl.disabled = !shouldEnable;\n      }\n      return menuEl;\n    });\n    return function enable(_x4, _x5) {\n      return _ref4.apply(this, arguments);\n    };\n  }();\n  const swipeGesture = /*#__PURE__*/function () {\n    var _ref5 = _asyncToGenerator(function* (shouldEnable, menu) {\n      const menuEl = yield get(menu);\n      if (menuEl) {\n        menuEl.swipeGesture = shouldEnable;\n      }\n      return menuEl;\n    });\n    return function swipeGesture(_x6, _x7) {\n      return _ref5.apply(this, arguments);\n    };\n  }();\n  const isOpen = /*#__PURE__*/function () {\n    var _ref6 = _asyncToGenerator(function* (menu) {\n      if (menu != null) {\n        const menuEl = yield get(menu);\n        // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n        return menuEl !== undefined && menuEl.isOpen();\n      } else {\n        const menuEl = yield getOpen();\n        return menuEl !== undefined;\n      }\n    });\n    return function isOpen(_x8) {\n      return _ref6.apply(this, arguments);\n    };\n  }();\n  const isEnabled = /*#__PURE__*/function () {\n    var _ref7 = _asyncToGenerator(function* (menu) {\n      const menuEl = yield get(menu);\n      if (menuEl) {\n        return !menuEl.disabled;\n      }\n      return false;\n    });\n    return function isEnabled(_x9) {\n      return _ref7.apply(this, arguments);\n    };\n  }();\n  /**\n   * Finds and returns the menu specified by \"menu\" if registered.\n   * @param menu - The side or ID of the desired menu\n   * @param logOnMultipleSideMenus - If true, this function will log a warning\n   * if \"menu\" is a side but multiple menus on the same side were found. Since this function\n   * is used in multiple places, we default this log to false so that the calling\n   * functions can choose whether or not it is appropriate to log this warning.\n   */\n  const get = /*#__PURE__*/function () {\n    var _ref8 = _asyncToGenerator(function* (menu, logOnMultipleSideMenus = false) {\n      yield waitUntilReady();\n      if (menu === 'start' || menu === 'end') {\n        // there could be more than one menu on the same side\n        // so first try to get the enabled one\n        const menuRefs = menus.filter(m => m.side === menu && !m.disabled);\n        if (menuRefs.length >= 1) {\n          if (menuRefs.length > 1 && logOnMultipleSideMenus) {\n            printIonWarning(`menuController queried for a menu on the \"${menu}\" side, but ${menuRefs.length} menus were found. The first menu reference will be used. If this is not the behavior you want then pass the ID of the menu instead of its side.`, menuRefs.map(m => m.el));\n          }\n          return menuRefs[0].el;\n        }\n        // didn't find a menu side that is enabled\n        // so try to get the first menu side found\n        const sideMenuRefs = menus.filter(m => m.side === menu);\n        if (sideMenuRefs.length >= 1) {\n          if (sideMenuRefs.length > 1 && logOnMultipleSideMenus) {\n            printIonWarning(`menuController queried for a menu on the \"${menu}\" side, but ${sideMenuRefs.length} menus were found. The first menu reference will be used. If this is not the behavior you want then pass the ID of the menu instead of its side.`, sideMenuRefs.map(m => m.el));\n          }\n          return sideMenuRefs[0].el;\n        }\n      } else if (menu != null) {\n        // the menuId was not left or right\n        // so try to get the menu by its \"id\"\n        return find(m => m.menuId === menu);\n      }\n      // return the first enabled menu\n      const menuEl = find(m => !m.disabled);\n      if (menuEl) {\n        return menuEl;\n      }\n      // get the first menu in the array, if one exists\n      return menus.length > 0 ? menus[0].el : undefined;\n    });\n    return function get(_x0) {\n      return _ref8.apply(this, arguments);\n    };\n  }();\n  /**\n   * Get the instance of the opened menu. Returns `null` if a menu is not found.\n   */\n  const getOpen = /*#__PURE__*/function () {\n    var _ref9 = _asyncToGenerator(function* () {\n      yield waitUntilReady();\n      return _getOpenSync();\n    });\n    return function getOpen() {\n      return _ref9.apply(this, arguments);\n    };\n  }();\n  /**\n   * Get all menu instances.\n   */\n  const getMenus = /*#__PURE__*/function () {\n    var _ref0 = _asyncToGenerator(function* () {\n      yield waitUntilReady();\n      return getMenusSync();\n    });\n    return function getMenus() {\n      return _ref0.apply(this, arguments);\n    };\n  }();\n  /**\n   * Get whether or not a menu is animating. Returns `true` if any\n   * menu is currently animating.\n   */\n  const isAnimating = /*#__PURE__*/function () {\n    var _ref1 = _asyncToGenerator(function* () {\n      yield waitUntilReady();\n      return isAnimatingSync();\n    });\n    return function isAnimating() {\n      return _ref1.apply(this, arguments);\n    };\n  }();\n  const registerAnimation = (name, animation) => {\n    menuAnimations.set(name, animation);\n  };\n  const _register = menu => {\n    if (menus.indexOf(menu) < 0) {\n      menus.push(menu);\n    }\n  };\n  const _unregister = menu => {\n    const index = menus.indexOf(menu);\n    if (index > -1) {\n      menus.splice(index, 1);\n    }\n  };\n  const _setOpen = /*#__PURE__*/function () {\n    var _ref10 = _asyncToGenerator(function* (menu, shouldOpen, animated, role) {\n      if (isAnimatingSync()) {\n        return false;\n      }\n      if (shouldOpen) {\n        const openedMenu = yield getOpen();\n        if (openedMenu && menu.el !== openedMenu) {\n          yield openedMenu.setOpen(false, false);\n        }\n      }\n      return menu._setOpen(shouldOpen, animated, role);\n    });\n    return function _setOpen(_x1, _x10, _x11, _x12) {\n      return _ref10.apply(this, arguments);\n    };\n  }();\n  const _createAnimation = (type, menuCmp) => {\n    const animationBuilder = menuAnimations.get(type); // TODO(FW-2832): type\n    if (!animationBuilder) {\n      throw new Error('animation not registered');\n    }\n    const animation = animationBuilder(menuCmp);\n    return animation;\n  };\n  const _getOpenSync = () => {\n    return find(m => m._isOpen);\n  };\n  const getMenusSync = () => {\n    return menus.map(menu => menu.el);\n  };\n  const isAnimatingSync = () => {\n    return menus.some(menu => menu.isAnimating);\n  };\n  const find = predicate => {\n    const instance = menus.find(predicate);\n    if (instance !== undefined) {\n      return instance.el;\n    }\n    return undefined;\n  };\n  const waitUntilReady = () => {\n    return Promise.all(Array.from(document.querySelectorAll('ion-menu')).map(menu => new Promise(resolve => componentOnReady(menu, resolve))));\n  };\n  registerAnimation('reveal', menuRevealAnimation);\n  registerAnimation('push', menuPushAnimation);\n  registerAnimation('overlay', menuOverlayAnimation);\n  doc === null || doc === void 0 ? void 0 : doc.addEventListener('ionBackButton', ev => {\n    const openMenu = _getOpenSync();\n    if (openMenu) {\n      ev.detail.register(MENU_BACK_BUTTON_PRIORITY, () => {\n        return openMenu.close();\n      });\n    }\n  });\n  return {\n    registerAnimation,\n    get,\n    getMenus,\n    getOpen,\n    isEnabled,\n    swipeGesture,\n    isAnimating,\n    isOpen,\n    enable,\n    toggle,\n    close,\n    open,\n    _getOpenSync,\n    _createAnimation,\n    _register,\n    _unregister,\n    _setOpen\n  };\n};\nconst menuController = /*@__PURE__*/createMenuController();\nexport { menuController as m };", "map": {"version": 3, "names": ["d", "doc", "MENU_BACK_BUTTON_PRIORITY", "e", "getIonMode", "m", "printIonWarning", "c", "componentOnReady", "createAnimation", "baseAnimation", "isIos", "duration", "menuOverlayAnimation", "menu", "closedX", "openedX", "width", "menuAnimation", "backdropAnimation", "isEndSide", "addElement", "menuInnerEl", "fromTo", "mode", "opacity", "backdropEl", "addAnimation", "menuPushAnimation", "contentOpenedX", "menuClosedX", "contentAnimation", "contentEl", "menuRevealAnimation", "contentOpen", "createMenuController", "menuAnimations", "Map", "menus", "open", "_ref", "_asyncToGenerator", "menuEl", "get", "_x", "apply", "arguments", "close", "_ref2", "undefined", "get<PERSON>pen", "_x2", "toggle", "_ref3", "_x3", "enable", "_ref4", "shouldEnable", "disabled", "_x4", "_x5", "swipeGesture", "_ref5", "_x6", "_x7", "isOpen", "_ref6", "_x8", "isEnabled", "_ref7", "_x9", "_ref8", "logOnMultipleSideMenus", "waitUntilReady", "menuRefs", "filter", "side", "length", "map", "el", "sideMenuRefs", "find", "menuId", "_x0", "_ref9", "_getOpenSync", "getMenus", "_ref0", "getMenusSync", "isAnimating", "_ref1", "isAnimatingSync", "registerAnimation", "name", "animation", "set", "_register", "indexOf", "push", "_unregister", "index", "splice", "_setOpen", "_ref10", "shouldOpen", "animated", "role", "openedMenu", "<PERSON><PERSON><PERSON>", "_x1", "_x10", "_x11", "_x12", "_createAnimation", "type", "menuCmp", "animationBuilder", "Error", "_isOpen", "some", "predicate", "instance", "Promise", "all", "Array", "from", "document", "querySelectorAll", "resolve", "addEventListener", "ev", "openMenu", "detail", "register", "menuController"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@ionic/core/dist/esm/index-D8sncTHY.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index-ZjP4CjeZ.js';\nimport { MENU_BACK_BUTTON_PRIORITY } from './hardware-back-button-DcH0BbDp.js';\nimport { e as getIonMode, m as printIonWarning } from './index-B_U9CtaY.js';\nimport { c as componentOnReady } from './helpers-1O4D2b7y.js';\nimport { c as createAnimation } from './animation-BWcUKtbn.js';\n\n/**\n * baseAnimation\n * Base class which is extended by the various types. Each\n * type will provide their own animations for open and close\n * and registers itself with Menu.\n */\nconst baseAnimation = (isIos) => {\n    // https://material.io/guidelines/motion/movement.html#movement-movement-in-out-of-screen-bounds\n    // https://material.io/guidelines/motion/duration-easing.html#duration-easing-natural-easing-curves\n    /**\n     * \"Apply the sharp curve to items temporarily leaving the screen that may return\n     * from the same exit point. When they return, use the deceleration curve. On mobile,\n     * this transition typically occurs over 300ms\" -- MD Motion Guide\n     */\n    return createAnimation().duration(isIos ? 400 : 300);\n};\n\n/**\n * Menu Overlay Type\n * The menu slides over the content. The content\n * itself, which is under the menu, does not move.\n */\nconst menuOverlayAnimation = (menu) => {\n    let closedX;\n    let openedX;\n    const width = menu.width + 8;\n    const menuAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    if (menu.isEndSide) {\n        // right side\n        closedX = width + 'px';\n        openedX = '0px';\n    }\n    else {\n        // left side\n        closedX = -width + 'px';\n        openedX = '0px';\n    }\n    menuAnimation.addElement(menu.menuInnerEl).fromTo('transform', `translateX(${closedX})`, `translateX(${openedX})`);\n    const mode = getIonMode(menu);\n    const isIos = mode === 'ios';\n    const opacity = isIos ? 0.2 : 0.25;\n    backdropAnimation.addElement(menu.backdropEl).fromTo('opacity', 0.01, opacity);\n    return baseAnimation(isIos).addAnimation([menuAnimation, backdropAnimation]);\n};\n\n/**\n * Menu Push Type\n * The content slides over to reveal the menu underneath.\n * The menu itself also slides over to reveal its bad self.\n */\nconst menuPushAnimation = (menu) => {\n    let contentOpenedX;\n    let menuClosedX;\n    const mode = getIonMode(menu);\n    const width = menu.width;\n    if (menu.isEndSide) {\n        contentOpenedX = -width + 'px';\n        menuClosedX = width + 'px';\n    }\n    else {\n        contentOpenedX = width + 'px';\n        menuClosedX = -width + 'px';\n    }\n    const menuAnimation = createAnimation()\n        .addElement(menu.menuInnerEl)\n        .fromTo('transform', `translateX(${menuClosedX})`, 'translateX(0px)');\n    const contentAnimation = createAnimation()\n        .addElement(menu.contentEl)\n        .fromTo('transform', 'translateX(0px)', `translateX(${contentOpenedX})`);\n    const backdropAnimation = createAnimation().addElement(menu.backdropEl).fromTo('opacity', 0.01, 0.32);\n    return baseAnimation(mode === 'ios').addAnimation([menuAnimation, contentAnimation, backdropAnimation]);\n};\n\n/**\n * Menu Reveal Type\n * The content slides over to reveal the menu underneath.\n * The menu itself, which is under the content, does not move.\n */\nconst menuRevealAnimation = (menu) => {\n    const mode = getIonMode(menu);\n    const openedX = menu.width * (menu.isEndSide ? -1 : 1) + 'px';\n    const contentOpen = createAnimation()\n        .addElement(menu.contentEl) // REVIEW\n        .fromTo('transform', 'translateX(0px)', `translateX(${openedX})`);\n    return baseAnimation(mode === 'ios').addAnimation(contentOpen);\n};\n\nconst createMenuController = () => {\n    const menuAnimations = new Map();\n    const menus = [];\n    const open = async (menu) => {\n        const menuEl = await get(menu, true);\n        if (menuEl) {\n            return menuEl.open();\n        }\n        return false;\n    };\n    const close = async (menu) => {\n        const menuEl = await (menu !== undefined ? get(menu, true) : getOpen());\n        if (menuEl !== undefined) {\n            return menuEl.close();\n        }\n        return false;\n    };\n    const toggle = async (menu) => {\n        const menuEl = await get(menu, true);\n        if (menuEl) {\n            return menuEl.toggle();\n        }\n        return false;\n    };\n    const enable = async (shouldEnable, menu) => {\n        const menuEl = await get(menu);\n        if (menuEl) {\n            menuEl.disabled = !shouldEnable;\n        }\n        return menuEl;\n    };\n    const swipeGesture = async (shouldEnable, menu) => {\n        const menuEl = await get(menu);\n        if (menuEl) {\n            menuEl.swipeGesture = shouldEnable;\n        }\n        return menuEl;\n    };\n    const isOpen = async (menu) => {\n        if (menu != null) {\n            const menuEl = await get(menu);\n            // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n            return menuEl !== undefined && menuEl.isOpen();\n        }\n        else {\n            const menuEl = await getOpen();\n            return menuEl !== undefined;\n        }\n    };\n    const isEnabled = async (menu) => {\n        const menuEl = await get(menu);\n        if (menuEl) {\n            return !menuEl.disabled;\n        }\n        return false;\n    };\n    /**\n     * Finds and returns the menu specified by \"menu\" if registered.\n     * @param menu - The side or ID of the desired menu\n     * @param logOnMultipleSideMenus - If true, this function will log a warning\n     * if \"menu\" is a side but multiple menus on the same side were found. Since this function\n     * is used in multiple places, we default this log to false so that the calling\n     * functions can choose whether or not it is appropriate to log this warning.\n     */\n    const get = async (menu, logOnMultipleSideMenus = false) => {\n        await waitUntilReady();\n        if (menu === 'start' || menu === 'end') {\n            // there could be more than one menu on the same side\n            // so first try to get the enabled one\n            const menuRefs = menus.filter((m) => m.side === menu && !m.disabled);\n            if (menuRefs.length >= 1) {\n                if (menuRefs.length > 1 && logOnMultipleSideMenus) {\n                    printIonWarning(`menuController queried for a menu on the \"${menu}\" side, but ${menuRefs.length} menus were found. The first menu reference will be used. If this is not the behavior you want then pass the ID of the menu instead of its side.`, menuRefs.map((m) => m.el));\n                }\n                return menuRefs[0].el;\n            }\n            // didn't find a menu side that is enabled\n            // so try to get the first menu side found\n            const sideMenuRefs = menus.filter((m) => m.side === menu);\n            if (sideMenuRefs.length >= 1) {\n                if (sideMenuRefs.length > 1 && logOnMultipleSideMenus) {\n                    printIonWarning(`menuController queried for a menu on the \"${menu}\" side, but ${sideMenuRefs.length} menus were found. The first menu reference will be used. If this is not the behavior you want then pass the ID of the menu instead of its side.`, sideMenuRefs.map((m) => m.el));\n                }\n                return sideMenuRefs[0].el;\n            }\n        }\n        else if (menu != null) {\n            // the menuId was not left or right\n            // so try to get the menu by its \"id\"\n            return find((m) => m.menuId === menu);\n        }\n        // return the first enabled menu\n        const menuEl = find((m) => !m.disabled);\n        if (menuEl) {\n            return menuEl;\n        }\n        // get the first menu in the array, if one exists\n        return menus.length > 0 ? menus[0].el : undefined;\n    };\n    /**\n     * Get the instance of the opened menu. Returns `null` if a menu is not found.\n     */\n    const getOpen = async () => {\n        await waitUntilReady();\n        return _getOpenSync();\n    };\n    /**\n     * Get all menu instances.\n     */\n    const getMenus = async () => {\n        await waitUntilReady();\n        return getMenusSync();\n    };\n    /**\n     * Get whether or not a menu is animating. Returns `true` if any\n     * menu is currently animating.\n     */\n    const isAnimating = async () => {\n        await waitUntilReady();\n        return isAnimatingSync();\n    };\n    const registerAnimation = (name, animation) => {\n        menuAnimations.set(name, animation);\n    };\n    const _register = (menu) => {\n        if (menus.indexOf(menu) < 0) {\n            menus.push(menu);\n        }\n    };\n    const _unregister = (menu) => {\n        const index = menus.indexOf(menu);\n        if (index > -1) {\n            menus.splice(index, 1);\n        }\n    };\n    const _setOpen = async (menu, shouldOpen, animated, role) => {\n        if (isAnimatingSync()) {\n            return false;\n        }\n        if (shouldOpen) {\n            const openedMenu = await getOpen();\n            if (openedMenu && menu.el !== openedMenu) {\n                await openedMenu.setOpen(false, false);\n            }\n        }\n        return menu._setOpen(shouldOpen, animated, role);\n    };\n    const _createAnimation = (type, menuCmp) => {\n        const animationBuilder = menuAnimations.get(type); // TODO(FW-2832): type\n        if (!animationBuilder) {\n            throw new Error('animation not registered');\n        }\n        const animation = animationBuilder(menuCmp);\n        return animation;\n    };\n    const _getOpenSync = () => {\n        return find((m) => m._isOpen);\n    };\n    const getMenusSync = () => {\n        return menus.map((menu) => menu.el);\n    };\n    const isAnimatingSync = () => {\n        return menus.some((menu) => menu.isAnimating);\n    };\n    const find = (predicate) => {\n        const instance = menus.find(predicate);\n        if (instance !== undefined) {\n            return instance.el;\n        }\n        return undefined;\n    };\n    const waitUntilReady = () => {\n        return Promise.all(Array.from(document.querySelectorAll('ion-menu')).map((menu) => new Promise((resolve) => componentOnReady(menu, resolve))));\n    };\n    registerAnimation('reveal', menuRevealAnimation);\n    registerAnimation('push', menuPushAnimation);\n    registerAnimation('overlay', menuOverlayAnimation);\n    doc === null || doc === void 0 ? void 0 : doc.addEventListener('ionBackButton', (ev) => {\n        const openMenu = _getOpenSync();\n        if (openMenu) {\n            ev.detail.register(MENU_BACK_BUTTON_PRIORITY, () => {\n                return openMenu.close();\n            });\n        }\n    });\n    return {\n        registerAnimation,\n        get,\n        getMenus,\n        getOpen,\n        isEnabled,\n        swipeGesture,\n        isAnimating,\n        isOpen,\n        enable,\n        toggle,\n        close,\n        open,\n        _getOpenSync,\n        _createAnimation,\n        _register,\n        _unregister,\n        _setOpen,\n    };\n};\nconst menuController = /*@__PURE__*/ createMenuController();\n\nexport { menuController as m };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,GAAG,QAAQ,qBAAqB;AAC9C,SAASC,yBAAyB,QAAQ,oCAAoC;AAC9E,SAASC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,eAAe,QAAQ,qBAAqB;AAC3E,SAASC,CAAC,IAAIC,gBAAgB,QAAQ,uBAAuB;AAC7D,SAASD,CAAC,IAAIE,eAAe,QAAQ,yBAAyB;;AAE9D;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAIC,KAAK,IAAK;EAC7B;EACA;EACA;AACJ;AACA;AACA;AACA;EACI,OAAOF,eAAe,CAAC,CAAC,CAACG,QAAQ,CAACD,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;AACxD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAME,oBAAoB,GAAIC,IAAI,IAAK;EACnC,IAAIC,OAAO;EACX,IAAIC,OAAO;EACX,MAAMC,KAAK,GAAGH,IAAI,CAACG,KAAK,GAAG,CAAC;EAC5B,MAAMC,aAAa,GAAGT,eAAe,CAAC,CAAC;EACvC,MAAMU,iBAAiB,GAAGV,eAAe,CAAC,CAAC;EAC3C,IAAIK,IAAI,CAACM,SAAS,EAAE;IAChB;IACAL,OAAO,GAAGE,KAAK,GAAG,IAAI;IACtBD,OAAO,GAAG,KAAK;EACnB,CAAC,MACI;IACD;IACAD,OAAO,GAAG,CAACE,KAAK,GAAG,IAAI;IACvBD,OAAO,GAAG,KAAK;EACnB;EACAE,aAAa,CAACG,UAAU,CAACP,IAAI,CAACQ,WAAW,CAAC,CAACC,MAAM,CAAC,WAAW,EAAE,cAAcR,OAAO,GAAG,EAAE,cAAcC,OAAO,GAAG,CAAC;EAClH,MAAMQ,IAAI,GAAGpB,UAAU,CAACU,IAAI,CAAC;EAC7B,MAAMH,KAAK,GAAGa,IAAI,KAAK,KAAK;EAC5B,MAAMC,OAAO,GAAGd,KAAK,GAAG,GAAG,GAAG,IAAI;EAClCQ,iBAAiB,CAACE,UAAU,CAACP,IAAI,CAACY,UAAU,CAAC,CAACH,MAAM,CAAC,SAAS,EAAE,IAAI,EAAEE,OAAO,CAAC;EAC9E,OAAOf,aAAa,CAACC,KAAK,CAAC,CAACgB,YAAY,CAAC,CAACT,aAAa,EAAEC,iBAAiB,CAAC,CAAC;AAChF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMS,iBAAiB,GAAId,IAAI,IAAK;EAChC,IAAIe,cAAc;EAClB,IAAIC,WAAW;EACf,MAAMN,IAAI,GAAGpB,UAAU,CAACU,IAAI,CAAC;EAC7B,MAAMG,KAAK,GAAGH,IAAI,CAACG,KAAK;EACxB,IAAIH,IAAI,CAACM,SAAS,EAAE;IAChBS,cAAc,GAAG,CAACZ,KAAK,GAAG,IAAI;IAC9Ba,WAAW,GAAGb,KAAK,GAAG,IAAI;EAC9B,CAAC,MACI;IACDY,cAAc,GAAGZ,KAAK,GAAG,IAAI;IAC7Ba,WAAW,GAAG,CAACb,KAAK,GAAG,IAAI;EAC/B;EACA,MAAMC,aAAa,GAAGT,eAAe,CAAC,CAAC,CAClCY,UAAU,CAACP,IAAI,CAACQ,WAAW,CAAC,CAC5BC,MAAM,CAAC,WAAW,EAAE,cAAcO,WAAW,GAAG,EAAE,iBAAiB,CAAC;EACzE,MAAMC,gBAAgB,GAAGtB,eAAe,CAAC,CAAC,CACrCY,UAAU,CAACP,IAAI,CAACkB,SAAS,CAAC,CAC1BT,MAAM,CAAC,WAAW,EAAE,iBAAiB,EAAE,cAAcM,cAAc,GAAG,CAAC;EAC5E,MAAMV,iBAAiB,GAAGV,eAAe,CAAC,CAAC,CAACY,UAAU,CAACP,IAAI,CAACY,UAAU,CAAC,CAACH,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC;EACrG,OAAOb,aAAa,CAACc,IAAI,KAAK,KAAK,CAAC,CAACG,YAAY,CAAC,CAACT,aAAa,EAAEa,gBAAgB,EAAEZ,iBAAiB,CAAC,CAAC;AAC3G,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMc,mBAAmB,GAAInB,IAAI,IAAK;EAClC,MAAMU,IAAI,GAAGpB,UAAU,CAACU,IAAI,CAAC;EAC7B,MAAME,OAAO,GAAGF,IAAI,CAACG,KAAK,IAAIH,IAAI,CAACM,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;EAC7D,MAAMc,WAAW,GAAGzB,eAAe,CAAC,CAAC,CAChCY,UAAU,CAACP,IAAI,CAACkB,SAAS,CAAC,CAAC;EAAA,CAC3BT,MAAM,CAAC,WAAW,EAAE,iBAAiB,EAAE,cAAcP,OAAO,GAAG,CAAC;EACrE,OAAON,aAAa,CAACc,IAAI,KAAK,KAAK,CAAC,CAACG,YAAY,CAACO,WAAW,CAAC;AAClE,CAAC;AAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAC/B,MAAMC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;EAChC,MAAMC,KAAK,GAAG,EAAE;EAChB,MAAMC,IAAI;IAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAO3B,IAAI,EAAK;MACzB,MAAM4B,MAAM,SAASC,GAAG,CAAC7B,IAAI,EAAE,IAAI,CAAC;MACpC,IAAI4B,MAAM,EAAE;QACR,OAAOA,MAAM,CAACH,IAAI,CAAC,CAAC;MACxB;MACA,OAAO,KAAK;IAChB,CAAC;IAAA,gBANKA,IAAIA,CAAAK,EAAA;MAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;IAAA;EAAA,GAMT;EACD,MAAMC,KAAK;IAAA,IAAAC,KAAA,GAAAP,iBAAA,CAAG,WAAO3B,IAAI,EAAK;MAC1B,MAAM4B,MAAM,SAAU5B,IAAI,KAAKmC,SAAS,GAAGN,GAAG,CAAC7B,IAAI,EAAE,IAAI,CAAC,GAAGoC,OAAO,CAAC,CAAE;MACvE,IAAIR,MAAM,KAAKO,SAAS,EAAE;QACtB,OAAOP,MAAM,CAACK,KAAK,CAAC,CAAC;MACzB;MACA,OAAO,KAAK;IAChB,CAAC;IAAA,gBANKA,KAAKA,CAAAI,GAAA;MAAA,OAAAH,KAAA,CAAAH,KAAA,OAAAC,SAAA;IAAA;EAAA,GAMV;EACD,MAAMM,MAAM;IAAA,IAAAC,KAAA,GAAAZ,iBAAA,CAAG,WAAO3B,IAAI,EAAK;MAC3B,MAAM4B,MAAM,SAASC,GAAG,CAAC7B,IAAI,EAAE,IAAI,CAAC;MACpC,IAAI4B,MAAM,EAAE;QACR,OAAOA,MAAM,CAACU,MAAM,CAAC,CAAC;MAC1B;MACA,OAAO,KAAK;IAChB,CAAC;IAAA,gBANKA,MAAMA,CAAAE,GAAA;MAAA,OAAAD,KAAA,CAAAR,KAAA,OAAAC,SAAA;IAAA;EAAA,GAMX;EACD,MAAMS,MAAM;IAAA,IAAAC,KAAA,GAAAf,iBAAA,CAAG,WAAOgB,YAAY,EAAE3C,IAAI,EAAK;MACzC,MAAM4B,MAAM,SAASC,GAAG,CAAC7B,IAAI,CAAC;MAC9B,IAAI4B,MAAM,EAAE;QACRA,MAAM,CAACgB,QAAQ,GAAG,CAACD,YAAY;MACnC;MACA,OAAOf,MAAM;IACjB,CAAC;IAAA,gBANKa,MAAMA,CAAAI,GAAA,EAAAC,GAAA;MAAA,OAAAJ,KAAA,CAAAX,KAAA,OAAAC,SAAA;IAAA;EAAA,GAMX;EACD,MAAMe,YAAY;IAAA,IAAAC,KAAA,GAAArB,iBAAA,CAAG,WAAOgB,YAAY,EAAE3C,IAAI,EAAK;MAC/C,MAAM4B,MAAM,SAASC,GAAG,CAAC7B,IAAI,CAAC;MAC9B,IAAI4B,MAAM,EAAE;QACRA,MAAM,CAACmB,YAAY,GAAGJ,YAAY;MACtC;MACA,OAAOf,MAAM;IACjB,CAAC;IAAA,gBANKmB,YAAYA,CAAAE,GAAA,EAAAC,GAAA;MAAA,OAAAF,KAAA,CAAAjB,KAAA,OAAAC,SAAA;IAAA;EAAA,GAMjB;EACD,MAAMmB,MAAM;IAAA,IAAAC,KAAA,GAAAzB,iBAAA,CAAG,WAAO3B,IAAI,EAAK;MAC3B,IAAIA,IAAI,IAAI,IAAI,EAAE;QACd,MAAM4B,MAAM,SAASC,GAAG,CAAC7B,IAAI,CAAC;QAC9B;QACA,OAAO4B,MAAM,KAAKO,SAAS,IAAIP,MAAM,CAACuB,MAAM,CAAC,CAAC;MAClD,CAAC,MACI;QACD,MAAMvB,MAAM,SAASQ,OAAO,CAAC,CAAC;QAC9B,OAAOR,MAAM,KAAKO,SAAS;MAC/B;IACJ,CAAC;IAAA,gBAVKgB,MAAMA,CAAAE,GAAA;MAAA,OAAAD,KAAA,CAAArB,KAAA,OAAAC,SAAA;IAAA;EAAA,GAUX;EACD,MAAMsB,SAAS;IAAA,IAAAC,KAAA,GAAA5B,iBAAA,CAAG,WAAO3B,IAAI,EAAK;MAC9B,MAAM4B,MAAM,SAASC,GAAG,CAAC7B,IAAI,CAAC;MAC9B,IAAI4B,MAAM,EAAE;QACR,OAAO,CAACA,MAAM,CAACgB,QAAQ;MAC3B;MACA,OAAO,KAAK;IAChB,CAAC;IAAA,gBANKU,SAASA,CAAAE,GAAA;MAAA,OAAAD,KAAA,CAAAxB,KAAA,OAAAC,SAAA;IAAA;EAAA,GAMd;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMH,GAAG;IAAA,IAAA4B,KAAA,GAAA9B,iBAAA,CAAG,WAAO3B,IAAI,EAAE0D,sBAAsB,GAAG,KAAK,EAAK;MACxD,MAAMC,cAAc,CAAC,CAAC;MACtB,IAAI3D,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,KAAK,EAAE;QACpC;QACA;QACA,MAAM4D,QAAQ,GAAGpC,KAAK,CAACqC,MAAM,CAAEtE,CAAC,IAAKA,CAAC,CAACuE,IAAI,KAAK9D,IAAI,IAAI,CAACT,CAAC,CAACqD,QAAQ,CAAC;QACpE,IAAIgB,QAAQ,CAACG,MAAM,IAAI,CAAC,EAAE;UACtB,IAAIH,QAAQ,CAACG,MAAM,GAAG,CAAC,IAAIL,sBAAsB,EAAE;YAC/ClE,eAAe,CAAC,6CAA6CQ,IAAI,eAAe4D,QAAQ,CAACG,MAAM,kJAAkJ,EAAEH,QAAQ,CAACI,GAAG,CAAEzE,CAAC,IAAKA,CAAC,CAAC0E,EAAE,CAAC,CAAC;UACjR;UACA,OAAOL,QAAQ,CAAC,CAAC,CAAC,CAACK,EAAE;QACzB;QACA;QACA;QACA,MAAMC,YAAY,GAAG1C,KAAK,CAACqC,MAAM,CAAEtE,CAAC,IAAKA,CAAC,CAACuE,IAAI,KAAK9D,IAAI,CAAC;QACzD,IAAIkE,YAAY,CAACH,MAAM,IAAI,CAAC,EAAE;UAC1B,IAAIG,YAAY,CAACH,MAAM,GAAG,CAAC,IAAIL,sBAAsB,EAAE;YACnDlE,eAAe,CAAC,6CAA6CQ,IAAI,eAAekE,YAAY,CAACH,MAAM,kJAAkJ,EAAEG,YAAY,CAACF,GAAG,CAAEzE,CAAC,IAAKA,CAAC,CAAC0E,EAAE,CAAC,CAAC;UACzR;UACA,OAAOC,YAAY,CAAC,CAAC,CAAC,CAACD,EAAE;QAC7B;MACJ,CAAC,MACI,IAAIjE,IAAI,IAAI,IAAI,EAAE;QACnB;QACA;QACA,OAAOmE,IAAI,CAAE5E,CAAC,IAAKA,CAAC,CAAC6E,MAAM,KAAKpE,IAAI,CAAC;MACzC;MACA;MACA,MAAM4B,MAAM,GAAGuC,IAAI,CAAE5E,CAAC,IAAK,CAACA,CAAC,CAACqD,QAAQ,CAAC;MACvC,IAAIhB,MAAM,EAAE;QACR,OAAOA,MAAM;MACjB;MACA;MACA,OAAOJ,KAAK,CAACuC,MAAM,GAAG,CAAC,GAAGvC,KAAK,CAAC,CAAC,CAAC,CAACyC,EAAE,GAAG9B,SAAS;IACrD,CAAC;IAAA,gBAlCKN,GAAGA,CAAAwC,GAAA;MAAA,OAAAZ,KAAA,CAAA1B,KAAA,OAAAC,SAAA;IAAA;EAAA,GAkCR;EACD;AACJ;AACA;EACI,MAAMI,OAAO;IAAA,IAAAkC,KAAA,GAAA3C,iBAAA,CAAG,aAAY;MACxB,MAAMgC,cAAc,CAAC,CAAC;MACtB,OAAOY,YAAY,CAAC,CAAC;IACzB,CAAC;IAAA,gBAHKnC,OAAOA,CAAA;MAAA,OAAAkC,KAAA,CAAAvC,KAAA,OAAAC,SAAA;IAAA;EAAA,GAGZ;EACD;AACJ;AACA;EACI,MAAMwC,QAAQ;IAAA,IAAAC,KAAA,GAAA9C,iBAAA,CAAG,aAAY;MACzB,MAAMgC,cAAc,CAAC,CAAC;MACtB,OAAOe,YAAY,CAAC,CAAC;IACzB,CAAC;IAAA,gBAHKF,QAAQA,CAAA;MAAA,OAAAC,KAAA,CAAA1C,KAAA,OAAAC,SAAA;IAAA;EAAA,GAGb;EACD;AACJ;AACA;AACA;EACI,MAAM2C,WAAW;IAAA,IAAAC,KAAA,GAAAjD,iBAAA,CAAG,aAAY;MAC5B,MAAMgC,cAAc,CAAC,CAAC;MACtB,OAAOkB,eAAe,CAAC,CAAC;IAC5B,CAAC;IAAA,gBAHKF,WAAWA,CAAA;MAAA,OAAAC,KAAA,CAAA7C,KAAA,OAAAC,SAAA;IAAA;EAAA,GAGhB;EACD,MAAM8C,iBAAiB,GAAGA,CAACC,IAAI,EAAEC,SAAS,KAAK;IAC3C1D,cAAc,CAAC2D,GAAG,CAACF,IAAI,EAAEC,SAAS,CAAC;EACvC,CAAC;EACD,MAAME,SAAS,GAAIlF,IAAI,IAAK;IACxB,IAAIwB,KAAK,CAAC2D,OAAO,CAACnF,IAAI,CAAC,GAAG,CAAC,EAAE;MACzBwB,KAAK,CAAC4D,IAAI,CAACpF,IAAI,CAAC;IACpB;EACJ,CAAC;EACD,MAAMqF,WAAW,GAAIrF,IAAI,IAAK;IAC1B,MAAMsF,KAAK,GAAG9D,KAAK,CAAC2D,OAAO,CAACnF,IAAI,CAAC;IACjC,IAAIsF,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ9D,KAAK,CAAC+D,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IAC1B;EACJ,CAAC;EACD,MAAME,QAAQ;IAAA,IAAAC,MAAA,GAAA9D,iBAAA,CAAG,WAAO3B,IAAI,EAAE0F,UAAU,EAAEC,QAAQ,EAAEC,IAAI,EAAK;MACzD,IAAIf,eAAe,CAAC,CAAC,EAAE;QACnB,OAAO,KAAK;MAChB;MACA,IAAIa,UAAU,EAAE;QACZ,MAAMG,UAAU,SAASzD,OAAO,CAAC,CAAC;QAClC,IAAIyD,UAAU,IAAI7F,IAAI,CAACiE,EAAE,KAAK4B,UAAU,EAAE;UACtC,MAAMA,UAAU,CAACC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;QAC1C;MACJ;MACA,OAAO9F,IAAI,CAACwF,QAAQ,CAACE,UAAU,EAAEC,QAAQ,EAAEC,IAAI,CAAC;IACpD,CAAC;IAAA,gBAXKJ,QAAQA,CAAAO,GAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;MAAA,OAAAT,MAAA,CAAA1D,KAAA,OAAAC,SAAA;IAAA;EAAA,GAWb;EACD,MAAMmE,gBAAgB,GAAGA,CAACC,IAAI,EAAEC,OAAO,KAAK;IACxC,MAAMC,gBAAgB,GAAGhF,cAAc,CAACO,GAAG,CAACuE,IAAI,CAAC,CAAC,CAAC;IACnD,IAAI,CAACE,gBAAgB,EAAE;MACnB,MAAM,IAAIC,KAAK,CAAC,0BAA0B,CAAC;IAC/C;IACA,MAAMvB,SAAS,GAAGsB,gBAAgB,CAACD,OAAO,CAAC;IAC3C,OAAOrB,SAAS;EACpB,CAAC;EACD,MAAMT,YAAY,GAAGA,CAAA,KAAM;IACvB,OAAOJ,IAAI,CAAE5E,CAAC,IAAKA,CAAC,CAACiH,OAAO,CAAC;EACjC,CAAC;EACD,MAAM9B,YAAY,GAAGA,CAAA,KAAM;IACvB,OAAOlD,KAAK,CAACwC,GAAG,CAAEhE,IAAI,IAAKA,IAAI,CAACiE,EAAE,CAAC;EACvC,CAAC;EACD,MAAMY,eAAe,GAAGA,CAAA,KAAM;IAC1B,OAAOrD,KAAK,CAACiF,IAAI,CAAEzG,IAAI,IAAKA,IAAI,CAAC2E,WAAW,CAAC;EACjD,CAAC;EACD,MAAMR,IAAI,GAAIuC,SAAS,IAAK;IACxB,MAAMC,QAAQ,GAAGnF,KAAK,CAAC2C,IAAI,CAACuC,SAAS,CAAC;IACtC,IAAIC,QAAQ,KAAKxE,SAAS,EAAE;MACxB,OAAOwE,QAAQ,CAAC1C,EAAE;IACtB;IACA,OAAO9B,SAAS;EACpB,CAAC;EACD,MAAMwB,cAAc,GAAGA,CAAA,KAAM;IACzB,OAAOiD,OAAO,CAACC,GAAG,CAACC,KAAK,CAACC,IAAI,CAACC,QAAQ,CAACC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAACjD,GAAG,CAAEhE,IAAI,IAAK,IAAI4G,OAAO,CAAEM,OAAO,IAAKxH,gBAAgB,CAACM,IAAI,EAAEkH,OAAO,CAAC,CAAC,CAAC,CAAC;EAClJ,CAAC;EACDpC,iBAAiB,CAAC,QAAQ,EAAE3D,mBAAmB,CAAC;EAChD2D,iBAAiB,CAAC,MAAM,EAAEhE,iBAAiB,CAAC;EAC5CgE,iBAAiB,CAAC,SAAS,EAAE/E,oBAAoB,CAAC;EAClDZ,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACgI,gBAAgB,CAAC,eAAe,EAAGC,EAAE,IAAK;IACpF,MAAMC,QAAQ,GAAG9C,YAAY,CAAC,CAAC;IAC/B,IAAI8C,QAAQ,EAAE;MACVD,EAAE,CAACE,MAAM,CAACC,QAAQ,CAACnI,yBAAyB,EAAE,MAAM;QAChD,OAAOiI,QAAQ,CAACpF,KAAK,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EACF,OAAO;IACH6C,iBAAiB;IACjBjD,GAAG;IACH2C,QAAQ;IACRpC,OAAO;IACPkB,SAAS;IACTP,YAAY;IACZ4B,WAAW;IACXxB,MAAM;IACNV,MAAM;IACNH,MAAM;IACNL,KAAK;IACLR,IAAI;IACJ8C,YAAY;IACZ4B,gBAAgB;IAChBjB,SAAS;IACTG,WAAW;IACXG;EACJ,CAAC;AACL,CAAC;AACD,MAAMgC,cAAc,GAAG,aAAcnG,oBAAoB,CAAC,CAAC;AAE3D,SAASmG,cAAc,IAAIjI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}