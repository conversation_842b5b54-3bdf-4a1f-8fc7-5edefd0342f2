{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, m as printIonWarning, e as getIonMode, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\nimport { x as eyeOff, y as eye } from './index-BLV6ykCk.js';\nconst iosInputPasswordToggleCss = \"\";\nconst mdInputPasswordToggleCss = \"\";\nconst InputPasswordToggle = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    /**\n     * @internal\n     */\n    this.type = 'password';\n    this.togglePasswordVisibility = () => {\n      const {\n        inputElRef\n      } = this;\n      if (!inputElRef) {\n        return;\n      }\n      inputElRef.type = inputElRef.type === 'text' ? 'password' : 'text';\n    };\n  }\n  /**\n   * Whenever the input type changes we need to re-run validation to ensure the password\n   * toggle is being used with the correct input type. If the application changes the type\n   * outside of this component we also need to re-render so the correct icon is shown.\n   */\n  onTypeChange(newValue) {\n    if (newValue !== 'text' && newValue !== 'password') {\n      printIonWarning(`[ion-input-password-toggle] - Only inputs of type \"text\" or \"password\" are supported. Input of type \"${newValue}\" is not compatible.`, this.el);\n      return;\n    }\n  }\n  connectedCallback() {\n    const {\n      el\n    } = this;\n    const inputElRef = this.inputElRef = el.closest('ion-input');\n    if (!inputElRef) {\n      printIonWarning('[ion-input-password-toggle] - No ancestor ion-input found. This component must be slotted inside of an ion-input.', el);\n      return;\n    }\n    /**\n     * Important: Set the type in connectedCallback because the default value\n     * of this.type may not always be accurate. Usually inputs have the \"password\" type\n     * but it is possible to have the input to initially have the \"text\" type. In that scenario\n     * the wrong icon will show briefly before switching to the correct icon. Setting the\n     * type here allows us to avoid that flicker.\n     */\n    this.type = inputElRef.type;\n  }\n  disconnectedCallback() {\n    this.inputElRef = null;\n  }\n  render() {\n    var _a, _b;\n    const {\n      color,\n      type\n    } = this;\n    const mode = getIonMode(this);\n    const showPasswordIcon = (_a = this.showIcon) !== null && _a !== void 0 ? _a : eye;\n    const hidePasswordIcon = (_b = this.hideIcon) !== null && _b !== void 0 ? _b : eyeOff;\n    const isPasswordVisible = type === 'text';\n    return h(Host, {\n      key: '91bc55664d496fe457518bd112865dd7811d0c17',\n      class: createColorClasses(color, {\n        [mode]: true\n      })\n    }, h(\"ion-button\", {\n      key: 'f3e436422110c9cb4d5c0b83500255b24ab4cdef',\n      mode: mode,\n      color: color,\n      fill: \"clear\",\n      shape: \"round\",\n      \"aria-checked\": isPasswordVisible ? 'true' : 'false',\n      \"aria-label\": isPasswordVisible ? 'Hide password' : 'Show password',\n      role: \"switch\",\n      type: \"button\",\n      onPointerDown: ev => {\n        /**\n         * This prevents mobile browsers from\n         * blurring the input when the password toggle\n         * button is activated.\n         */\n        ev.preventDefault();\n      },\n      onClick: this.togglePasswordVisibility\n    }, h(\"ion-icon\", {\n      key: '5c8b121153f148f92aa7cba0447673a4f6f3ad1e',\n      slot: \"icon-only\",\n      \"aria-hidden\": \"true\",\n      icon: isPasswordVisible ? hidePasswordIcon : showPasswordIcon\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"type\": [\"onTypeChange\"]\n    };\n  }\n};\nInputPasswordToggle.style = {\n  ios: iosInputPasswordToggleCss,\n  md: mdInputPasswordToggleCss\n};\nexport { InputPasswordToggle as ion_input_password_toggle };", "map": {"version": 3, "names": ["r", "registerInstance", "m", "printIonWarning", "e", "getIonMode", "h", "j", "Host", "k", "getElement", "c", "createColorClasses", "x", "eyeOff", "y", "eye", "iosInputPasswordToggleCss", "mdInputPasswordToggleCss", "InputPasswordToggle", "constructor", "hostRef", "type", "togglePasswordVisibility", "inputElRef", "onTypeChange", "newValue", "el", "connectedCallback", "closest", "disconnectedCallback", "render", "_a", "_b", "color", "mode", "showPasswordIcon", "showIcon", "hidePasswordIcon", "hideIcon", "isPasswordVisible", "key", "class", "fill", "shape", "role", "onPointerDown", "ev", "preventDefault", "onClick", "slot", "icon", "watchers", "style", "ios", "md", "ion_input_password_toggle"], "sources": ["/Users/<USER>/Desktop/olivery_web/odoo-12/extra_addons/olivery_driver_evaluation/angular-app/node_modules/@ionic/core/dist/esm/ion-input-password-toggle.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, m as printIonWarning, e as getIonMode, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\nimport { x as eyeOff, y as eye } from './index-BLV6ykCk.js';\n\nconst iosInputPasswordToggleCss = \"\";\n\nconst mdInputPasswordToggleCss = \"\";\n\nconst InputPasswordToggle = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        /**\n         * @internal\n         */\n        this.type = 'password';\n        this.togglePasswordVisibility = () => {\n            const { inputElRef } = this;\n            if (!inputElRef) {\n                return;\n            }\n            inputElRef.type = inputElRef.type === 'text' ? 'password' : 'text';\n        };\n    }\n    /**\n     * Whenever the input type changes we need to re-run validation to ensure the password\n     * toggle is being used with the correct input type. If the application changes the type\n     * outside of this component we also need to re-render so the correct icon is shown.\n     */\n    onTypeChange(newValue) {\n        if (newValue !== 'text' && newValue !== 'password') {\n            printIonWarning(`[ion-input-password-toggle] - Only inputs of type \"text\" or \"password\" are supported. Input of type \"${newValue}\" is not compatible.`, this.el);\n            return;\n        }\n    }\n    connectedCallback() {\n        const { el } = this;\n        const inputElRef = (this.inputElRef = el.closest('ion-input'));\n        if (!inputElRef) {\n            printIonWarning('[ion-input-password-toggle] - No ancestor ion-input found. This component must be slotted inside of an ion-input.', el);\n            return;\n        }\n        /**\n         * Important: Set the type in connectedCallback because the default value\n         * of this.type may not always be accurate. Usually inputs have the \"password\" type\n         * but it is possible to have the input to initially have the \"text\" type. In that scenario\n         * the wrong icon will show briefly before switching to the correct icon. Setting the\n         * type here allows us to avoid that flicker.\n         */\n        this.type = inputElRef.type;\n    }\n    disconnectedCallback() {\n        this.inputElRef = null;\n    }\n    render() {\n        var _a, _b;\n        const { color, type } = this;\n        const mode = getIonMode(this);\n        const showPasswordIcon = (_a = this.showIcon) !== null && _a !== void 0 ? _a : eye;\n        const hidePasswordIcon = (_b = this.hideIcon) !== null && _b !== void 0 ? _b : eyeOff;\n        const isPasswordVisible = type === 'text';\n        return (h(Host, { key: '91bc55664d496fe457518bd112865dd7811d0c17', class: createColorClasses(color, {\n                [mode]: true,\n            }) }, h(\"ion-button\", { key: 'f3e436422110c9cb4d5c0b83500255b24ab4cdef', mode: mode, color: color, fill: \"clear\", shape: \"round\", \"aria-checked\": isPasswordVisible ? 'true' : 'false', \"aria-label\": isPasswordVisible ? 'Hide password' : 'Show password', role: \"switch\", type: \"button\", onPointerDown: (ev) => {\n                /**\n                 * This prevents mobile browsers from\n                 * blurring the input when the password toggle\n                 * button is activated.\n                 */\n                ev.preventDefault();\n            }, onClick: this.togglePasswordVisibility }, h(\"ion-icon\", { key: '5c8b121153f148f92aa7cba0447673a4f6f3ad1e', slot: \"icon-only\", \"aria-hidden\": \"true\", icon: isPasswordVisible ? hidePasswordIcon : showPasswordIcon }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"type\": [\"onTypeChange\"]\n    }; }\n};\nInputPasswordToggle.style = {\n    ios: iosInputPasswordToggleCss,\n    md: mdInputPasswordToggleCss\n};\n\nexport { InputPasswordToggle as ion_input_password_toggle };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AACjI,SAASC,CAAC,IAAIC,kBAAkB,QAAQ,qBAAqB;AAC7D,SAASC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,GAAG,QAAQ,qBAAqB;AAE3D,MAAMC,yBAAyB,GAAG,EAAE;AAEpC,MAAMC,wBAAwB,GAAG,EAAE;AAEnC,MAAMC,mBAAmB,GAAG,MAAM;EAC9BC,WAAWA,CAACC,OAAO,EAAE;IACjBpB,gBAAgB,CAAC,IAAI,EAAEoB,OAAO,CAAC;IAC/B;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,UAAU;IACtB,IAAI,CAACC,wBAAwB,GAAG,MAAM;MAClC,MAAM;QAAEC;MAAW,CAAC,GAAG,IAAI;MAC3B,IAAI,CAACA,UAAU,EAAE;QACb;MACJ;MACAA,UAAU,CAACF,IAAI,GAAGE,UAAU,CAACF,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG,MAAM;IACtE,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;EACIG,YAAYA,CAACC,QAAQ,EAAE;IACnB,IAAIA,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,UAAU,EAAE;MAChDvB,eAAe,CAAC,wGAAwGuB,QAAQ,sBAAsB,EAAE,IAAI,CAACC,EAAE,CAAC;MAChK;IACJ;EACJ;EACAC,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAED;IAAG,CAAC,GAAG,IAAI;IACnB,MAAMH,UAAU,GAAI,IAAI,CAACA,UAAU,GAAGG,EAAE,CAACE,OAAO,CAAC,WAAW,CAAE;IAC9D,IAAI,CAACL,UAAU,EAAE;MACbrB,eAAe,CAAC,mHAAmH,EAAEwB,EAAE,CAAC;MACxI;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACL,IAAI,GAAGE,UAAU,CAACF,IAAI;EAC/B;EACAQ,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACN,UAAU,GAAG,IAAI;EAC1B;EACAO,MAAMA,CAAA,EAAG;IACL,IAAIC,EAAE,EAAEC,EAAE;IACV,MAAM;MAAEC,KAAK;MAAEZ;IAAK,CAAC,GAAG,IAAI;IAC5B,MAAMa,IAAI,GAAG9B,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM+B,gBAAgB,GAAG,CAACJ,EAAE,GAAG,IAAI,CAACK,QAAQ,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGhB,GAAG;IAClF,MAAMsB,gBAAgB,GAAG,CAACL,EAAE,GAAG,IAAI,CAACM,QAAQ,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGnB,MAAM;IACrF,MAAM0B,iBAAiB,GAAGlB,IAAI,KAAK,MAAM;IACzC,OAAQhB,CAAC,CAACE,IAAI,EAAE;MAAEiC,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE9B,kBAAkB,CAACsB,KAAK,EAAE;QAC5F,CAACC,IAAI,GAAG;MACZ,CAAC;IAAE,CAAC,EAAE7B,CAAC,CAAC,YAAY,EAAE;MAAEmC,GAAG,EAAE,0CAA0C;MAAEN,IAAI,EAAEA,IAAI;MAAED,KAAK,EAAEA,KAAK;MAAES,IAAI,EAAE,OAAO;MAAEC,KAAK,EAAE,OAAO;MAAE,cAAc,EAAEJ,iBAAiB,GAAG,MAAM,GAAG,OAAO;MAAE,YAAY,EAAEA,iBAAiB,GAAG,eAAe,GAAG,eAAe;MAAEK,IAAI,EAAE,QAAQ;MAAEvB,IAAI,EAAE,QAAQ;MAAEwB,aAAa,EAAGC,EAAE,IAAK;QAChT;AAChB;AACA;AACA;AACA;QACgBA,EAAE,CAACC,cAAc,CAAC,CAAC;MACvB,CAAC;MAAEC,OAAO,EAAE,IAAI,CAAC1B;IAAyB,CAAC,EAAEjB,CAAC,CAAC,UAAU,EAAE;MAAEmC,GAAG,EAAE,0CAA0C;MAAES,IAAI,EAAE,WAAW;MAAE,aAAa,EAAE,MAAM;MAAEC,IAAI,EAAEX,iBAAiB,GAAGF,gBAAgB,GAAGF;IAAiB,CAAC,CAAC,CAAC,CAAC;EAClO;EACA,IAAIT,EAAEA,CAAA,EAAG;IAAE,OAAOjB,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW0C,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,MAAM,EAAE,CAAC,cAAc;IAC3B,CAAC;EAAE;AACP,CAAC;AACDjC,mBAAmB,CAACkC,KAAK,GAAG;EACxBC,GAAG,EAAErC,yBAAyB;EAC9BsC,EAAE,EAAErC;AACR,CAAC;AAED,SAASC,mBAAmB,IAAIqC,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}