<div class="evaluation-container" dir="rtl">
  <!-- Language Switch -->
  <div class="language-switch">
    <div class="lang-toggle">
      <span class="lang-label" [class.active]="currentLanguage === 'ar'">AR</span>
      <ion-toggle
        [(ngModel)]="isArabic"
        (ionChange)="toggleLanguage()"
        class="lang-toggle-switch">
      </ion-toggle>
      <span class="lang-label" [class.active]="currentLanguage === 'en'">EN</span>
    </div>
  </div>

  <!-- Header -->
  <div class="evaluation-header" *ngIf="evaluationData">
    <ion-card>
      <ion-card-content>
        <h2>{{ evaluationTitle | translate }}</h2>
        <p>{{ 'EXPIRES' | translate }}: {{ evaluationData.expiryDate | date:'medium' }}</p>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <ion-spinner name="crescent"></ion-spinner>
    <p>{{ 'LOADING_EVALUATION_FORM' | translate }}</p>
  </div>

  <!-- Error State -->
  <div class="alert-danger" *ngIf="error">
    <h4>{{ 'ERROR' | translate }}</h4>
    <p>{{ error }}</p>
  </div>

  <!-- Single Page Evaluation Form -->
  <div *ngIf="!isLoading && !error && config" class="evaluation-form">

    <!-- Evaluation Categories -->
    <div class="categories-section">
      <app-evaluation-card
        *ngFor="let category of config.criteria.categories"
        [category]="category"
        [currentRating]="getRating(category.id)"
        (ratingChange)="setRating(category.id, $event)">
      </app-evaluation-card>
    </div>

    <!-- Feedback Section -->
    <div class="feedback-section">
      <ion-card>
        <ion-card-header>
          <ion-card-title [title]="'FEEDBACK.FEEDBACK_DESCRIPTION' | translate">
            {{ 'FEEDBACK.ADDITIONAL_FEEDBACK' | translate }}
          </ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <ion-item>
            <ion-textarea
              [(ngModel)]="feedback"
              [placeholder]="'FEEDBACK.FEEDBACK_PLACEHOLDER' | translate"
              rows="4">
            </ion-textarea>
          </ion-item>
        </ion-card-content>
      </ion-card>
    </div>

    <!-- Submit Section -->
    <div class="submit-section">
      <div class="submit-actions">
        <ion-button
          expand="block"
          size="large"
          class="submit-button"
          (click)="submitEvaluation()"
          [disabled]="isSubmitting || !hasValidRatings()">
          <ion-spinner name="crescent" *ngIf="isSubmitting"></ion-spinner>
          {{ isSubmitting ? ('BUTTONS.SUBMITTING' | translate) : ('BUTTONS.SUBMIT_EVALUATION' | translate) }}
        </ion-button>
      </div>
    </div>

  </div>
</div>
