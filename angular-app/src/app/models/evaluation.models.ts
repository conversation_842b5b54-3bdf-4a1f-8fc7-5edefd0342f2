export interface EvaluationData {
  token: string;
  driverName: string;
  driverId: number;
  linkId: number;
  expiryDate: string;
}

export interface EvaluationConfig {
  evaluation_duration: number;
  evaluation_title?: string;
  criteria: EvaluationCriteria;
}

export interface EvaluationCriteria {
  categories: EvaluationCategory[];
}

export interface EvaluationCategory {
  id: string;
  name: string;
  description: string;
  max_score: number;
  questions?: EvaluationQuestion[];
}

export interface EvaluationOption {
  value: number;
  label: string;
  description: string;
}

export interface EvaluationQuestion {
  id: string;
  text: string;
  type: string;
  scale: number;
}

export interface EvaluatorInfo {
  name: string;
  email: string;
  phone: string;
}

export interface EvaluationScores {
  [categoryId: string]: number;
}

export interface EvaluationSubmission {
  scores: EvaluationScores;
  evaluator: EvaluatorInfo;
  feedback: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
}

export interface ValidationResponse {
  driver_name: string;
  driver_id: number;
  link_id: number;
  expiry_date: string;
  token: string;
}

export interface SubmissionResponse {
  message: string;
  result_id: number;
  overall_score: number;
}
