"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[5887],{5887:(w,a,l)=>{l.r(a),l.d(a,{ion_split_pane:()=>c});var d=l(467),t=l(2734);const r="split-pane-main",p="split-pane-side",h={xs:"(min-width: 0px)",sm:"(min-width: 576px)",md:"(min-width: 768px)",lg:"(min-width: 992px)",xl:"(min-width: 1200px)",never:""},c=class{constructor(e){(0,t.r)(this,e),this.ionSplitPaneVisible=(0,t.d)(this,"ionSplitPaneVisible",7),this.visible=!1,this.disabled=!1,this.when=h.lg}visibleChanged(e){this.ionSplitPaneVisible.emit({visible:e})}isVisible(){var e=this;return(0,d.A)(function*(){return Promise.resolve(e.visible)})()}connectedCallback(){var e=this;return(0,d.A)(function*(){typeof customElements<"u"&&null!=customElements&&(yield customElements.whenDefined("ion-split-pane")),e.styleMainElement(),e.updateState()})()}disconnectedCallback(){this.rmL&&(this.rmL(),this.rmL=void 0)}updateState(){if(this.rmL&&(this.rmL(),this.rmL=void 0),this.disabled)return void(this.visible=!1);const e=this.when;if("boolean"==typeof e)return void(this.visible=e);const o=h[e]||e;if(0===o.length)return void(this.visible=!1);const s=n=>{this.visible=n.matches},i=window.matchMedia(o);i.addListener(s),this.rmL=()=>i.removeListener(s),this.visible=i.matches}styleMainElement(){const e=this.contentId,o=this.el.children,s=this.el.childElementCount;let i=!1;for(let n=0;n<s;n++){const m=o[n],f=void 0!==e&&m.id===e;if(f){if(i)return void(0,t.m)("[ion-split-pane] - Cannot have more than one main node.");v(m,f),i=!0}}i||(0,t.m)("[ion-split-pane] - Does not have a specified main node.")}render(){const e=(0,t.e)(this);return(0,t.h)(t.j,{key:"d5e30df12f1f1f855da4c66f98076b9dce762c59",class:{[e]:!0,[`split-pane-${e}`]:!0,"split-pane-visible":this.visible}},(0,t.h)("slot",{key:"3e30d7cf3bc1cf434e16876a0cb2a36377b8e00f"}))}get el(){return(0,t.k)(this)}static get watchers(){return{visible:["visibleChanged"],disabled:["updateState"],when:["updateState"]}}},v=(e,o)=>{let s,i;o?(s=r,i=p):(s=p,i=r);const n=e.classList;n.add(s),n.remove(i)};c.style={ios:":host{--side-width:100%;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;contain:strict}:host(.split-pane-visible) ::slotted(.split-pane-main){left:0;right:0;top:0;bottom:0;position:relative;-ms-flex:1;flex:1;-webkit-box-shadow:none;box-shadow:none;overflow:hidden;z-index:0}::slotted(.split-pane-side:not(ion-menu)){display:none}:host{--border:0.55px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--side-min-width:270px;--side-max-width:28%}",md:":host{--side-width:100%;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;contain:strict}:host(.split-pane-visible) ::slotted(.split-pane-main){left:0;right:0;top:0;bottom:0;position:relative;-ms-flex:1;flex:1;-webkit-box-shadow:none;box-shadow:none;overflow:hidden;z-index:0}::slotted(.split-pane-side:not(ion-menu)){display:none}:host{--border:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--side-min-width:270px;--side-max-width:28%}"}}}]);