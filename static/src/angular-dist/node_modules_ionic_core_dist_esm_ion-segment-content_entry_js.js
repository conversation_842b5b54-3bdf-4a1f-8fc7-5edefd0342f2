"use strict";
(self["webpackChunkdriver_evaluation_app"] = self["webpackChunkdriver_evaluation_app"] || []).push([["node_modules_ionic_core_dist_esm_ion-segment-content_entry_js"],{

/***/ 4312:
/*!************************************************************************!*\
  !*** ./node_modules/@ionic/core/dist/esm/ion-segment-content.entry.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ion_segment_content: () => (/* binding */ SegmentContent)
/* harmony export */ });
/* harmony import */ var _index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index-B_U9CtaY.js */ 4917);
/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */

const segmentContentCss = ":host{scroll-snap-align:center;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%;min-height:1px;overflow-y:scroll;scrollbar-width:none;-ms-overflow-style:none;}:host::-webkit-scrollbar{display:none}";
const SegmentContent = class {
  constructor(hostRef) {
    (0,_index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__.r)(this, hostRef);
  }
  render() {
    return (0,_index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__.h)(_index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__.j, {
      key: 'db6876f2aee7afa1ea8bc147337670faa68fae1c'
    }, (0,_index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__.h)("slot", {
      key: 'bc05714a973a5655668679033f5809a1da6db8cc'
    }));
  }
};
SegmentContent.style = segmentContentCss;


/***/ })

}]);
//# sourceMappingURL=node_modules_ionic_core_dist_esm_ion-segment-content_entry_js.js.map