{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-datetime-button_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACgI;AAC3C;AACvB;AAC0E;AAExI,MAAM0B,oBAAoB,GAAG,gmCAAgmC;AAE7nC,MAAMC,mBAAmB,GAAG,4/BAA4/B;AAExhC,MAAMC,cAAc,GAAG,MAAM;EACzBC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACjB9B,qDAAgB,CAAC,IAAI,EAAE6B,OAAO,CAAC;IAC/B,IAAI,CAACE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,oBAAoB,GAAG,WAAW;IACvC,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,SAAS;IACtB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAIC,KAAK,IAAK;MAClC,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;QACvC,OAAO,EAAE;MACb;MACA,IAAIE,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;QACtB,OAAOA,KAAK;MAChB;MACA,OAAO,CAACA,KAAK,CAAC;IAClB,CAAC;IACD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACI,eAAe,GAAG,MAAM;MACzB,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;MACtB,MAAM;QAAEhB,UAAU;QAAEE;MAAqB,CAAC,GAAG,IAAI;MACjD,IAAI,CAACF,UAAU,EAAE;QACb;MACJ;MACA,MAAM;QAAEO,KAAK;QAAEU,MAAM;QAAEC,aAAa;QAAEC,SAAS;QAAEC,WAAW;QAAEC,QAAQ;QAAEC;MAA4B,CAAC,GAAGtB,UAAU;MAClH,MAAMuB,YAAY,GAAG,IAAI,CAACjB,mBAAmB,CAACC,KAAK,CAAC;MACpD;AACZ;AACA;AACA;MACY,MAAMiB,eAAe,GAAGvC,oDAAS,CAACsC,YAAY,CAACE,MAAM,GAAG,CAAC,GAAGF,YAAY,GAAG,CAACpC,oDAAQ,CAAC,CAAC,CAAC,CAAC;MACxF,IAAI,CAACqC,eAAe,EAAE;QAClB;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,MAAME,mBAAmB,GAAGF,eAAe,CAAC,CAAC,CAAC;MAC9C,MAAMG,iBAAiB,GAAGtC,oDAAY,CAAC4B,MAAM,EAAEE,SAAS,CAAC;MACzD,IAAI,CAACS,QAAQ,GAAG,IAAI,CAACC,QAAQ,GAAGrB,SAAS;MACzC,QAAQN,oBAAoB;QACxB,KAAK,WAAW;QAChB,KAAK,WAAW;UACZ,MAAM0B,QAAQ,GAAGrC,oDAAoB,CAAC0B,MAAM,EAAES,mBAAmB,EAAE,CAACd,EAAE,GAAGM,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACY,IAAI,MAAM,IAAI,IAAIlB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;YAAEmB,KAAK,EAAE,OAAO;YAAEC,GAAG,EAAE,SAAS;YAAEC,IAAI,EAAE;UAAU,CAAC,CAAC;UAChP,MAAMJ,QAAQ,GAAGpC,oDAAgB,CAACwB,MAAM,EAAES,mBAAmB,EAAEC,iBAAiB,EAAET,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACgB,IAAI,CAAC;UACnK,IAAId,WAAW,EAAE;YACb,IAAI,CAACQ,QAAQ,GAAG,GAAGA,QAAQ,IAAIC,QAAQ,EAAE;UAC7C,CAAC,MACI;YACD,IAAI,CAACD,QAAQ,GAAGA,QAAQ;YACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;UAC5B;UACA;QACJ,KAAK,MAAM;UACP,IAAIR,QAAQ,IAAIE,YAAY,CAACE,MAAM,KAAK,CAAC,EAAE;YACvC,IAAIU,UAAU,GAAG,GAAGZ,YAAY,CAACE,MAAM,OAAO,CAAC,CAAC;YAChD,IAAIH,2BAA2B,KAAKd,SAAS,EAAE;cAC3C,IAAI;gBACA2B,UAAU,GAAGb,2BAA2B,CAACC,YAAY,CAAC;cAC1D,CAAC,CACD,OAAOnD,CAAC,EAAE;gBACND,qDAAa,CAAC,8EAA8E,EAAEC,CAAC,CAAC;cACpG;YACJ;YACA,IAAI,CAACwD,QAAQ,GAAGO,UAAU;UAC9B,CAAC,MACI;YACD,IAAI,CAACP,QAAQ,GAAGrC,oDAAoB,CAAC0B,MAAM,EAAES,mBAAmB,EAAE,CAACb,EAAE,GAAGK,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACY,IAAI,MAAM,IAAI,IAAIjB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;cAAEkB,KAAK,EAAE,OAAO;cAAEC,GAAG,EAAE,SAAS;cAAEC,IAAI,EAAE;YAAU,CAAC,CAAC;UACnP;UACA;QACJ,KAAK,MAAM;UACP,IAAI,CAACJ,QAAQ,GAAGpC,oDAAgB,CAACwB,MAAM,EAAES,mBAAmB,EAAEC,iBAAiB,EAAET,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACgB,IAAI,CAAC;UAClK;QACJ,KAAK,YAAY;UACb,IAAI,CAACN,QAAQ,GAAGrC,oDAAoB,CAAC0B,MAAM,EAAES,mBAAmB,EAAE,CAACZ,EAAE,GAAGI,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACY,IAAI,MAAM,IAAI,IAAIhB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;YAAEiB,KAAK,EAAE,MAAM;YAAEE,IAAI,EAAE;UAAU,CAAC,CAAC;UAC9N;QACJ,KAAK,OAAO;UACR,IAAI,CAACL,QAAQ,GAAGrC,oDAAoB,CAAC0B,MAAM,EAAES,mBAAmB,EAAE,CAACX,EAAE,GAAGG,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACgB,IAAI,MAAM,IAAI,IAAInB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;YAAEgB,KAAK,EAAE;UAAO,CAAC,CAAC;UAC7M;QACJ,KAAK,MAAM;UACP,IAAI,CAACH,QAAQ,GAAGrC,oDAAoB,CAAC0B,MAAM,EAAES,mBAAmB,EAAE,CAACV,EAAE,GAAGE,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACgB,IAAI,MAAM,IAAI,IAAIlB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;YAAEiB,IAAI,EAAE;UAAU,CAAC,CAAC;UAC/M;MACR;IACJ,CAAC;IACD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACG,sBAAsB,gBAAAC,oMAAA,CAAG,aAAY;MACtC,MAAM;QAAErC;MAAW,CAAC,GAAGD,KAAI;MAC3B,IAAI,CAACC,UAAU,EAAE;QACb,OAAOsC,OAAO,CAACC,OAAO,CAAC,CAAC;MAC5B;MACA,OAAO,IAAID,OAAO,CAAEC,OAAO,IAAK;QAC5B3D,uDAAgB,CAACoB,UAAU,EAAE,WAAW,EAAEuC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;MACtE,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACC,eAAe;MAAA,IAAAC,KAAA,GAAAL,oMAAA,CAAG,WAAOM,EAAE,EAAK;QACjC,MAAM;UAAE3C,UAAU;UAAEE;QAAqB,CAAC,GAAGH,KAAI;QACjD,IAAI,CAACC,UAAU,EAAE;UACb;QACJ;QACA,IAAI4C,uBAAuB,GAAG,KAAK;QACnC;AACZ;AACA;AACA;AACA;AACA;AACA;QACY,QAAQ1C,oBAAoB;UACxB,KAAK,WAAW;UAChB,KAAK,WAAW;YACZ,MAAM2C,WAAW,GAAG7C,UAAU,CAAC8C,YAAY,KAAK,MAAM;YACtD;AACpB;AACA;AACA;AACA;AACA;YACoB,IAAI,CAAC9C,UAAU,CAACoB,WAAW,IAAIyB,WAAW,EAAE;cACxC7C,UAAU,CAAC8C,YAAY,GAAG,MAAM;cAChCF,uBAAuB,GAAG,IAAI;YAClC;YACA;QACR;QACA;AACZ;AACA;AACA;AACA;AACA;AACA;QACY7C,KAAI,CAACgD,cAAc,GAAG,MAAM;QAC5BhD,KAAI,CAACiD,cAAc,CAACL,EAAE,EAAEC,uBAAuB,EAAE7C,KAAI,CAACkD,YAAY,CAAC;MACvE,CAAC;MAAA,iBAAAC,EAAA;QAAA,OAAAR,KAAA,CAAAS,KAAA,OAAAC,SAAA;MAAA;IAAA;IACD,IAAI,CAACC,eAAe,GAAIV,EAAE,IAAK;MAC3B,MAAM;QAAE3C,UAAU;QAAEE;MAAqB,CAAC,GAAG,IAAI;MACjD,IAAI,CAACF,UAAU,EAAE;QACb;MACJ;MACA,IAAI4C,uBAAuB,GAAG,KAAK;MACnC;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,QAAQ1C,oBAAoB;QACxB,KAAK,WAAW;QAChB,KAAK,WAAW;UACZ,MAAM2C,WAAW,GAAG7C,UAAU,CAAC8C,YAAY,KAAK,MAAM;UACtD,IAAID,WAAW,EAAE;YACb7C,UAAU,CAAC8C,YAAY,GAAG,MAAM;YAChCF,uBAAuB,GAAG,IAAI;UAClC;UACA;MACR;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAACG,cAAc,GAAG,MAAM;MAC5B,IAAI,CAACC,cAAc,CAACL,EAAE,EAAEC,uBAAuB,EAAE,IAAI,CAACU,YAAY,CAAC;IACvE,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACN,cAAc;MAAA,IAAAO,KAAA,GAAAlB,oMAAA,CAAG,WAAOM,EAAE,EAAEC,uBAAuB,EAAEY,SAAS,EAAK;QACpE,MAAM;UAAEvD;QAAU,CAAC,GAAGF,KAAI;QAC1B,IAAI,CAACE,SAAS,EAAE;UACZ;QACJ;QACA,IAAIA,SAAS,CAACwD,OAAO,KAAK,aAAa,EAAE;UACrC;AAChB;AACA;AACA;AACA;AACA;AACA;UACgB,IAAIb,uBAAuB,EAAE;YACzB,MAAM7C,KAAI,CAACqC,sBAAsB,CAAC,CAAC;UACvC;UACA;AAChB;AACA;AACA;AACA;UACgBnC,SAAS,CAACyD,OAAO,CAACC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjB,EAAE,CAAC,EAAE;YAAEkB,MAAM,EAAE;cACzDC,eAAe,EAAEN;YACrB;UAAE,CAAC,CAAC,CAAC;QACb,CAAC,MACI;UACDvD,SAAS,CAACyD,OAAO,CAAC,CAAC;QACvB;MACJ,CAAC;MAAA,iBAAAK,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAV,KAAA,CAAAJ,KAAA,OAAAC,SAAA;MAAA;IAAA;EACL;EACMc,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA9B,oMAAA;MACtB,MAAM;QAAE+B;MAAS,CAAC,GAAGD,MAAI;MACzB,IAAI,CAACC,QAAQ,EAAE;QACXjG,qDAAa,CAAC,0GAA0G,EAAEgG,MAAI,CAACE,EAAE,CAAC;QAClI;MACJ;MACA,MAAMrE,UAAU,GAAImE,MAAI,CAACnE,UAAU,GAAGsE,QAAQ,CAACC,cAAc,CAACH,QAAQ,CAAE;MACxE,IAAI,CAACpE,UAAU,EAAE;QACb7B,qDAAa,CAAC,kEAAkEiG,QAAQ,IAAI,EAAED,MAAI,CAACE,EAAE,CAAC;QACtG;MACJ;MACA;AACR;AACA;AACA;MACQ,IAAIrE,UAAU,CAACyD,OAAO,KAAK,cAAc,EAAE;QACvCtF,qDAAa,CAAC,qEAAqEiG,QAAQ,mBAAmBpE,UAAU,CAACyD,OAAO,CAACe,WAAW,CAAC,CAAC,YAAY,EAAExE,UAAU,CAAC;QACvK;MACJ;MACA;AACR;AACA;AACA;AACA;AACA;MACQ,MAAMyE,EAAE,GAAG,IAAIC,oBAAoB,CAAEC,OAAO,IAAK;QAC7C,MAAMhC,EAAE,GAAGgC,OAAO,CAAC,CAAC,CAAC;QACrBR,MAAI,CAAChE,cAAc,GAAGwC,EAAE,CAACiC,cAAc;MAC3C,CAAC,EAAE;QACCC,SAAS,EAAE;MACf,CAAC,CAAC;MACFJ,EAAE,CAACK,OAAO,CAAC9E,UAAU,CAAC;MACtB;AACR;AACA;AACA;AACA;MACQ,MAAMC,SAAS,GAAIkE,MAAI,CAAClE,SAAS,GAAGD,UAAU,CAAC+E,OAAO,CAAC,wBAAwB,CAAE;MACjF;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACQ,IAAI9E,SAAS,EAAE;QACXA,SAAS,CAAC+E,SAAS,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1D;MACAnG,uDAAgB,CAACkB,UAAU,EAAE,MAAM;QAC/B,MAAME,oBAAoB,GAAIiE,MAAI,CAACjE,oBAAoB,GAAGF,UAAU,CAAC8C,YAAY,IAAI,WAAY;QACjG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACYqB,MAAI,CAACxD,eAAe,CAAC,CAAC;QACtB/B,uDAAgB,CAACoB,UAAU,EAAE,gBAAgB,EAAEmE,MAAI,CAACxD,eAAe,CAAC;QACpE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACY,QAAQT,oBAAoB;UACxB,KAAK,WAAW;UAChB,KAAK,MAAM;UACX,KAAK,YAAY;UACjB,KAAK,OAAO;UACZ,KAAK,MAAM;YACPiE,MAAI,CAACpB,cAAc,GAAG,MAAM;YAC5B;UACJ,KAAK,WAAW;UAChB,KAAK,MAAM;YACPoB,MAAI,CAACpB,cAAc,GAAG,MAAM;YAC5B;QACR;MACJ,CAAC,CAAC;IAAC;EACP;EACAmC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE9E,KAAK;MAAEwB,QAAQ;MAAEC,QAAQ;MAAEkB,cAAc;MAAE5C,cAAc;MAAEE;IAAS,CAAC,GAAG,IAAI;IACpF,MAAM8E,IAAI,GAAG9G,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,qDAAC,CAACE,iDAAI,EAAE;MAAE4G,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAEtG,qDAAkB,CAACqB,KAAK,EAAE;QAC5F,CAAC+E,IAAI,GAAG,IAAI;QACZ,CAAC,GAAGpC,cAAc,SAAS,GAAG5C,cAAc;QAC5C,CAAC,0BAA0B,GAAGE;MAClC,CAAC;IAAE,CAAC,EAAEuB,QAAQ,IAAKtD,qDAAC,CAAC,QAAQ,EAAE;MAAE8G,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE,iBAAiB;MAAEC,EAAE,EAAE,aAAa;MAAE,eAAe,EAAEnF,cAAc,GAAG,MAAM,GAAG,OAAO;MAAEoF,OAAO,EAAE,IAAI,CAAC9C,eAAe;MAAEpC,QAAQ,EAAEA,QAAQ;MAAEmF,IAAI,EAAE,QAAQ;MAAEC,GAAG,EAAGpB,EAAE,IAAM,IAAI,CAACpB,YAAY,GAAGoB;IAAI,CAAC,EAAE/F,qDAAC,CAAC,MAAM,EAAE;MAAE8G,GAAG,EAAE,0CAA0C;MAAEM,IAAI,EAAE;IAAc,CAAC,EAAE9D,QAAQ,CAAC,EAAEuD,IAAI,KAAK,IAAI,IAAI7G,qDAAC,CAAC,mBAAmB,EAAE;MAAE8G,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAE,EAAEvD,QAAQ,IAAKvD,qDAAC,CAAC,QAAQ,EAAE;MAAE8G,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE,iBAAiB;MAAEC,EAAE,EAAE,aAAa;MAAE,eAAe,EAAEnF,cAAc,GAAG,MAAM,GAAG,OAAO;MAAEoF,OAAO,EAAE,IAAI,CAAClC,eAAe;MAAEhD,QAAQ,EAAEA,QAAQ;MAAEmF,IAAI,EAAE,QAAQ;MAAEC,GAAG,EAAGpB,EAAE,IAAM,IAAI,CAACf,YAAY,GAAGe;IAAI,CAAC,EAAE/F,qDAAC,CAAC,MAAM,EAAE;MAAE8G,GAAG,EAAE,0CAA0C;MAAEM,IAAI,EAAE;IAAc,CAAC,EAAE7D,QAAQ,CAAC,EAAEsD,IAAI,KAAK,IAAI,IAAI7G,qDAAC,CAAC,mBAAmB,EAAE;MAAE8G,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAE,CAAC;EAC77B;EACA,IAAIf,EAAEA,CAAA,EAAG;IAAE,OAAO3F,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDkB,cAAc,CAAC+F,KAAK,GAAG;EACnBC,GAAG,EAAElG,oBAAoB;EACzBmG,EAAE,EAAElG;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-datetime-button.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, o as printIonError, e as getIonMode, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { f as addEventListener, c as componentOnReady } from './helpers-1O4D2b7y.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\nimport { s as parseDate, z as getToday, O as getHourCycle, Q as getLocalizedDateTime, P as getLocalizedTime } from './data-GIsHsYIB.js';\n\nconst datetimeButtonIosCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}:host button{border-radius:8px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:0px;margin-bottom:0px;position:relative;-webkit-transition:150ms color ease-in-out;transition:150ms color ease-in-out;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:1rem;cursor:pointer;overflow:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host(.time-active) #time-button,:host(.date-active) #date-button{color:var(--ion-color-base)}:host(.datetime-button-disabled){pointer-events:none}:host(.datetime-button-disabled) button{opacity:0.4}:host button{-webkit-padding-start:13px;padding-inline-start:13px;-webkit-padding-end:13px;padding-inline-end:13px;padding-top:7px;padding-bottom:7px}:host button.ion-activated{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}\";\n\nconst datetimeButtonMdCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}:host button{border-radius:8px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:0px;margin-bottom:0px;position:relative;-webkit-transition:150ms color ease-in-out;transition:150ms color ease-in-out;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:1rem;cursor:pointer;overflow:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host(.time-active) #time-button,:host(.date-active) #date-button{color:var(--ion-color-base)}:host(.datetime-button-disabled){pointer-events:none}:host(.datetime-button-disabled) button{opacity:0.4}:host button{-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px}\";\n\nconst DatetimeButton = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.datetimeEl = null;\n        this.overlayEl = null;\n        this.datetimePresentation = 'date-time';\n        this.datetimeActive = false;\n        /**\n         * The color to use from your application's color palette.\n         * Default options are: `\"primary\"`, `\"secondary\"`, `\"tertiary\"`, `\"success\"`, `\"warning\"`, `\"danger\"`, `\"light\"`, `\"medium\"`, and `\"dark\"`.\n         * For more information on colors, see [theming](/docs/theming/basics).\n         */\n        this.color = 'primary';\n        /**\n         * If `true`, the user cannot interact with the button.\n         */\n        this.disabled = false;\n        /**\n         * Accepts one or more string values and converts\n         * them to DatetimeParts. This is done so datetime-button\n         * can work with an array internally and not need\n         * to keep checking if the datetime value is `string` or `string[]`.\n         */\n        this.getParsedDateValues = (value) => {\n            if (value === undefined || value === null) {\n                return [];\n            }\n            if (Array.isArray(value)) {\n                return value;\n            }\n            return [value];\n        };\n        /**\n         * Check the value property on the linked\n         * ion-datetime and then format it according\n         * to the locale specified on ion-datetime.\n         */\n        this.setDateTimeText = () => {\n            var _a, _b, _c, _d, _e;\n            const { datetimeEl, datetimePresentation } = this;\n            if (!datetimeEl) {\n                return;\n            }\n            const { value, locale, formatOptions, hourCycle, preferWheel, multiple, titleSelectedDatesFormatter } = datetimeEl;\n            const parsedValues = this.getParsedDateValues(value);\n            /**\n             * Both ion-datetime and ion-datetime-button default\n             * to today's date and time if no value is set.\n             */\n            const parsedDatetimes = parseDate(parsedValues.length > 0 ? parsedValues : [getToday()]);\n            if (!parsedDatetimes) {\n                return;\n            }\n            /**\n             * If developers incorrectly use multiple=\"true\"\n             * with non \"date\" datetimes, then just select\n             * the first value so the interface does\n             * not appear broken. Datetime will provide a\n             * warning in the console.\n             */\n            const firstParsedDatetime = parsedDatetimes[0];\n            const computedHourCycle = getHourCycle(locale, hourCycle);\n            this.dateText = this.timeText = undefined;\n            switch (datetimePresentation) {\n                case 'date-time':\n                case 'time-date':\n                    const dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_a = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) !== null && _a !== void 0 ? _a : { month: 'short', day: 'numeric', year: 'numeric' });\n                    const timeText = getLocalizedTime(locale, firstParsedDatetime, computedHourCycle, formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time);\n                    if (preferWheel) {\n                        this.dateText = `${dateText} ${timeText}`;\n                    }\n                    else {\n                        this.dateText = dateText;\n                        this.timeText = timeText;\n                    }\n                    break;\n                case 'date':\n                    if (multiple && parsedValues.length !== 1) {\n                        let headerText = `${parsedValues.length} days`; // default/fallback for multiple selection\n                        if (titleSelectedDatesFormatter !== undefined) {\n                            try {\n                                headerText = titleSelectedDatesFormatter(parsedValues);\n                            }\n                            catch (e) {\n                                printIonError('[ion-datetime-button] - Exception in provided `titleSelectedDatesFormatter`:', e);\n                            }\n                        }\n                        this.dateText = headerText;\n                    }\n                    else {\n                        this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_b = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) !== null && _b !== void 0 ? _b : { month: 'short', day: 'numeric', year: 'numeric' });\n                    }\n                    break;\n                case 'time':\n                    this.timeText = getLocalizedTime(locale, firstParsedDatetime, computedHourCycle, formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time);\n                    break;\n                case 'month-year':\n                    this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_c = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) !== null && _c !== void 0 ? _c : { month: 'long', year: 'numeric' });\n                    break;\n                case 'month':\n                    this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_d = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time) !== null && _d !== void 0 ? _d : { month: 'long' });\n                    break;\n                case 'year':\n                    this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_e = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time) !== null && _e !== void 0 ? _e : { year: 'numeric' });\n                    break;\n            }\n        };\n        /**\n         * Waits for the ion-datetime to re-render.\n         * This is needed in order to correctly position\n         * a popover relative to the trigger element.\n         */\n        this.waitForDatetimeChanges = async () => {\n            const { datetimeEl } = this;\n            if (!datetimeEl) {\n                return Promise.resolve();\n            }\n            return new Promise((resolve) => {\n                addEventListener(datetimeEl, 'ionRender', resolve, { once: true });\n            });\n        };\n        this.handleDateClick = async (ev) => {\n            const { datetimeEl, datetimePresentation } = this;\n            if (!datetimeEl) {\n                return;\n            }\n            let needsPresentationChange = false;\n            /**\n             * When clicking the date button,\n             * we need to make sure that only a date\n             * picker is displayed. For presentation styles\n             * that display content other than a date picker,\n             * we need to update the presentation style.\n             */\n            switch (datetimePresentation) {\n                case 'date-time':\n                case 'time-date':\n                    const needsChange = datetimeEl.presentation !== 'date';\n                    /**\n                     * The date+time wheel picker\n                     * shows date and time together,\n                     * so do not adjust the presentation\n                     * in that case.\n                     */\n                    if (!datetimeEl.preferWheel && needsChange) {\n                        datetimeEl.presentation = 'date';\n                        needsPresentationChange = true;\n                    }\n                    break;\n            }\n            /**\n             * Track which button was clicked\n             * so that it can have the correct\n             * activated styles applied when\n             * the modal/popover containing\n             * the datetime is opened.\n             */\n            this.selectedButton = 'date';\n            this.presentOverlay(ev, needsPresentationChange, this.dateTargetEl);\n        };\n        this.handleTimeClick = (ev) => {\n            const { datetimeEl, datetimePresentation } = this;\n            if (!datetimeEl) {\n                return;\n            }\n            let needsPresentationChange = false;\n            /**\n             * When clicking the time button,\n             * we need to make sure that only a time\n             * picker is displayed. For presentation styles\n             * that display content other than a time picker,\n             * we need to update the presentation style.\n             */\n            switch (datetimePresentation) {\n                case 'date-time':\n                case 'time-date':\n                    const needsChange = datetimeEl.presentation !== 'time';\n                    if (needsChange) {\n                        datetimeEl.presentation = 'time';\n                        needsPresentationChange = true;\n                    }\n                    break;\n            }\n            /**\n             * Track which button was clicked\n             * so that it can have the correct\n             * activated styles applied when\n             * the modal/popover containing\n             * the datetime is opened.\n             */\n            this.selectedButton = 'time';\n            this.presentOverlay(ev, needsPresentationChange, this.timeTargetEl);\n        };\n        /**\n         * If the datetime is presented in an\n         * overlay, the datetime and overlay\n         * should be appropriately sized.\n         * These classes provide default sizing values\n         * that developers can customize.\n         * The goal is to provide an overlay that is\n         * reasonably sized with a datetime that\n         * fills the entire container.\n         */\n        this.presentOverlay = async (ev, needsPresentationChange, triggerEl) => {\n            const { overlayEl } = this;\n            if (!overlayEl) {\n                return;\n            }\n            if (overlayEl.tagName === 'ION-POPOVER') {\n                /**\n                 * When the presentation on datetime changes,\n                 * we need to wait for the component to re-render\n                 * otherwise the computed width/height of the\n                 * popover content will be wrong, causing\n                 * the popover to not align with the trigger element.\n                 */\n                if (needsPresentationChange) {\n                    await this.waitForDatetimeChanges();\n                }\n                /**\n                 * We pass the trigger button element\n                 * so that the popover aligns with the individual\n                 * button that was clicked, not the component container.\n                 */\n                overlayEl.present(Object.assign(Object.assign({}, ev), { detail: {\n                        ionShadowTarget: triggerEl,\n                    } }));\n            }\n            else {\n                overlayEl.present();\n            }\n        };\n    }\n    async componentWillLoad() {\n        const { datetime } = this;\n        if (!datetime) {\n            printIonError('[ion-datetime-button] - An ID associated with an ion-datetime instance is required to function properly.', this.el);\n            return;\n        }\n        const datetimeEl = (this.datetimeEl = document.getElementById(datetime));\n        if (!datetimeEl) {\n            printIonError(`[ion-datetime-button] - No ion-datetime instance found for ID '${datetime}'.`, this.el);\n            return;\n        }\n        /**\n         * The element reference must be an ion-datetime. Print an error\n         * if a non-datetime element was provided.\n         */\n        if (datetimeEl.tagName !== 'ION-DATETIME') {\n            printIonError(`[ion-datetime-button] - Expected an ion-datetime instance for ID '${datetime}' but received '${datetimeEl.tagName.toLowerCase()}' instead.`, datetimeEl);\n            return;\n        }\n        /**\n         * Since the datetime can be used in any context (overlays, accordion, etc)\n         * we track when it is visible to determine when it is active.\n         * This informs which button is highlighted as well as the\n         * aria-expanded state.\n         */\n        const io = new IntersectionObserver((entries) => {\n            const ev = entries[0];\n            this.datetimeActive = ev.isIntersecting;\n        }, {\n            threshold: 0.01,\n        });\n        io.observe(datetimeEl);\n        /**\n         * Get a reference to any modal/popover\n         * the datetime is being used in so we can\n         * correctly size it when it is presented.\n         */\n        const overlayEl = (this.overlayEl = datetimeEl.closest('ion-modal, ion-popover'));\n        /**\n         * The .ion-datetime-button-overlay class contains\n         * styles that allow any modal/popover to be\n         * sized according to the dimensions of the datetime.\n         * If developers want a smaller/larger overlay all they need\n         * to do is change the width/height of the datetime.\n         * Additionally, this lets us avoid having to set\n         * explicit widths on each variant of datetime.\n         */\n        if (overlayEl) {\n            overlayEl.classList.add('ion-datetime-button-overlay');\n        }\n        componentOnReady(datetimeEl, () => {\n            const datetimePresentation = (this.datetimePresentation = datetimeEl.presentation || 'date-time');\n            /**\n             * Set the initial display\n             * in the rendered buttons.\n             *\n             * From there, we need to listen\n             * for ionChange to be emitted\n             * from datetime so we know when\n             * to re-render the displayed\n             * text in the buttons.\n             */\n            this.setDateTimeText();\n            addEventListener(datetimeEl, 'ionValueChange', this.setDateTimeText);\n            /**\n             * Configure the initial selected button\n             * in the event that the datetime is displayed\n             * without clicking one of the datetime buttons.\n             * For example, a datetime could be expanded\n             * in an accordion. In this case users only\n             * need to click the accordion header to show\n             * the datetime.\n             */\n            switch (datetimePresentation) {\n                case 'date-time':\n                case 'date':\n                case 'month-year':\n                case 'month':\n                case 'year':\n                    this.selectedButton = 'date';\n                    break;\n                case 'time-date':\n                case 'time':\n                    this.selectedButton = 'time';\n                    break;\n            }\n        });\n    }\n    render() {\n        const { color, dateText, timeText, selectedButton, datetimeActive, disabled } = this;\n        const mode = getIonMode(this);\n        return (h(Host, { key: '11d037e6ab061e5116842970760b04850b42f2c7', class: createColorClasses(color, {\n                [mode]: true,\n                [`${selectedButton}-active`]: datetimeActive,\n                ['datetime-button-disabled']: disabled,\n            }) }, dateText && (h(\"button\", { key: '08ecb62da0fcbf7466a1f2403276712a3ff17fbc', class: \"ion-activatable\", id: \"date-button\", \"aria-expanded\": datetimeActive ? 'true' : 'false', onClick: this.handleDateClick, disabled: disabled, part: \"native\", ref: (el) => (this.dateTargetEl = el) }, h(\"slot\", { key: '1c04853d4d23c0f1a594602bde44511c98355644', name: \"date-target\" }, dateText), mode === 'md' && h(\"ion-ripple-effect\", { key: '5fc566cd4bc885bcf983ce99e3dc65d7f485bf9b' }))), timeText && (h(\"button\", { key: 'c9c5c34ac338badf8659da22bea5829d62c51169', class: \"ion-activatable\", id: \"time-button\", \"aria-expanded\": datetimeActive ? 'true' : 'false', onClick: this.handleTimeClick, disabled: disabled, part: \"native\", ref: (el) => (this.timeTargetEl = el) }, h(\"slot\", { key: '147a9d2069dbf737f6fc64787823d6d5af5aa653', name: \"time-target\" }, timeText), mode === 'md' && h(\"ion-ripple-effect\", { key: '70a5e25b75ed90ac6bba003468435f67aa9d8f0a' })))));\n    }\n    get el() { return getElement(this); }\n};\nDatetimeButton.style = {\n    ios: datetimeButtonIosCss,\n    md: datetimeButtonMdCss\n};\n\nexport { DatetimeButton as ion_datetime_button };\n"], "names": ["r", "registerInstance", "o", "printIonError", "e", "getIonMode", "h", "j", "Host", "k", "getElement", "f", "addEventListener", "c", "componentOnReady", "createColorClasses", "s", "parseDate", "z", "get<PERSON><PERSON>y", "O", "getHourCycle", "Q", "getLocalizedDateTime", "P", "getLocalizedTime", "datetimeButtonIosCss", "datetimeButtonMdCss", "DatetimeButton", "constructor", "hostRef", "_this", "datetimeEl", "overlayEl", "datetimePresentation", "datetimeActive", "color", "disabled", "getParsedDateValues", "value", "undefined", "Array", "isArray", "setDateTimeText", "_a", "_b", "_c", "_d", "_e", "locale", "formatOptions", "hourCycle", "preferWheel", "multiple", "titleSelectedDatesFormatter", "parsed<PERSON><PERSON>ues", "parsedDatetimes", "length", "firstParsedDatetime", "computedHourCycle", "dateText", "timeText", "date", "month", "day", "year", "time", "headerText", "waitForDatetimeChanges", "_asyncToGenerator", "Promise", "resolve", "once", "handleDateClick", "_ref2", "ev", "needsPresentationChange", "needsChange", "presentation", "<PERSON><PERSON><PERSON><PERSON>", "presentOverlay", "dateTargetEl", "_x", "apply", "arguments", "handleTimeClick", "timeTargetEl", "_ref3", "triggerEl", "tagName", "present", "Object", "assign", "detail", "ionShadowTarget", "_x2", "_x3", "_x4", "componentWillLoad", "_this2", "datetime", "el", "document", "getElementById", "toLowerCase", "io", "IntersectionObserver", "entries", "isIntersecting", "threshold", "observe", "closest", "classList", "add", "render", "mode", "key", "class", "id", "onClick", "part", "ref", "name", "style", "ios", "md", "ion_datetime_button"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}