"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[6433],{6433:(re,K,v)=>{v.r(K),v.d(K,{ion_popover:()=>ee});var L=v(467),l=v(2734),A=v(3217),j=v(1653),w=v(1837),X=v(7930),B=v(4576),u=v(4590),p=v(5756);v(9596),v(1906),v(8607);const G=(o,e,t)=>{const r=e.getBoundingClientRect(),i=r.height;let n=r.width;return"cover"===o&&t&&(n=t.getBoundingClientRect().width),{contentWidth:n,contentHeight:i}},ie=(o,e,t)=>{let r=[];switch(e){case"hover":let i;r=[{eventName:"mouseenter",callback:(n=(0,L.A)(function*(s){s.stopPropagation(),i&&clearTimeout(i),i=setTimeout(()=>{(0,w.r)(()=>{t.presentFromTrigger(s),i=void 0})},100)}),function(a){return n.apply(this,arguments)})},{eventName:"mouseleave",callback:n=>{i&&clearTimeout(i);const s=n.relatedTarget;s&&s.closest("ion-popover")!==t&&t.dismiss(void 0,void 0,!1)}},{eventName:"click",callback:n=>n.stopPropagation()},{eventName:"ionPopoverActivateTrigger",callback:n=>t.presentFromTrigger(n,!0)}];break;case"context-menu":r=[{eventName:"contextmenu",callback:n=>{n.preventDefault(),t.presentFromTrigger(n)}},{eventName:"click",callback:n=>n.stopPropagation()},{eventName:"ionPopoverActivateTrigger",callback:n=>t.presentFromTrigger(n,!0)}];break;default:r=[{eventName:"click",callback:n=>t.presentFromTrigger(n)},{eventName:"ionPopoverActivateTrigger",callback:n=>t.presentFromTrigger(n,!0)}]}var n;return r.forEach(({eventName:i,callback:n})=>o.addEventListener(i,n)),o.setAttribute("data-ion-popover-trigger","true"),()=>{r.forEach(({eventName:i,callback:n})=>o.removeEventListener(i,n)),o.removeAttribute("data-ion-popover-trigger")}},H=(o,e)=>e&&"ION-ITEM"===e.tagName?o.findIndex(t=>t===e):-1,N=o=>{const t=(0,w.g)(o).querySelector("button");t&&(0,w.r)(()=>t.focus())},pe=o=>{const e=function(){var t=(0,L.A)(function*(r){var i;const n=document.activeElement;let s=[];const a=null===(i=r.target)||void 0===i?void 0:i.tagName;if("ION-POPOVER"===a||"ION-ITEM"===a){try{s=Array.from(o.querySelectorAll("ion-item:not(ion-popover ion-popover *):not([disabled])"))}catch{}switch(r.key){case"ArrowLeft":(yield o.getParentPopover())&&o.dismiss(void 0,void 0,!1);break;case"ArrowDown":r.preventDefault();const d=((o,e)=>o[H(o,e)+1])(s,n);void 0!==d&&N(d);break;case"ArrowUp":r.preventDefault();const g=((o,e)=>o[H(o,e)-1])(s,n);void 0!==g&&N(g);break;case"Home":r.preventDefault();const h=s[0];void 0!==h&&N(h);break;case"End":r.preventDefault();const b=s[s.length-1];void 0!==b&&N(b);break;case"ArrowRight":case" ":case"Enter":if(n&&(o=>o.hasAttribute("data-ion-popover-trigger"))(n)){const m=new CustomEvent("ionPopoverActivateTrigger");n.dispatchEvent(m)}}}});return function(i){return t.apply(this,arguments)}}();return o.addEventListener("keydown",e),()=>o.removeEventListener("keydown",e)},Z=(o,e,t,r,i,n,s,a,c,d,g)=>{var h;let b={top:0,left:0,width:0,height:0};if("event"===n){if(!g)return c;b={top:g.clientY,left:g.clientX,width:1,height:1}}else{const f=g,C=d||(null===(h=f?.detail)||void 0===h?void 0:h.ionShadowTarget)||f?.target;if(!C)return c;const D=C.getBoundingClientRect();b={top:D.top,left:D.left,width:D.width,height:D.height}}const m=fe(s,b,e,t,r,i,o),x=he(a,s,b,e,t),_=m.top+x.top,E=m.left+x.left,{arrowTop:y,arrowLeft:T}=de(s,r,i,_,E,e,t,o),{originX:P,originY:I}=le(s,a,o);return{top:_,left:E,referenceCoordinates:b,arrowTop:y,arrowLeft:T,originX:P,originY:I}},le=(o,e,t)=>{switch(o){case"top":return{originX:J(e),originY:"bottom"};case"bottom":return{originX:J(e),originY:"top"};case"left":return{originX:"right",originY:z(e)};case"right":return{originX:"left",originY:z(e)};case"start":return{originX:t?"left":"right",originY:z(e)};case"end":return{originX:t?"right":"left",originY:z(e)}}},J=o=>{switch(o){case"start":return"left";case"center":return"center";case"end":return"right"}},z=o=>{switch(o){case"start":return"top";case"center":return"center";case"end":return"bottom"}},de=(o,e,t,r,i,n,s,a)=>{const c={arrowTop:r+s/2-e/2,arrowLeft:i+n-e/2},d={arrowTop:r+s/2-e/2,arrowLeft:i-1.5*e};switch(o){case"top":return{arrowTop:r+s,arrowLeft:i+n/2-e/2};case"bottom":return{arrowTop:r-t,arrowLeft:i+n/2-e/2};case"left":return c;case"right":return d;case"start":return a?d:c;case"end":return a?c:d;default:return{arrowTop:0,arrowLeft:0}}},fe=(o,e,t,r,i,n,s)=>{const a={top:e.top,left:e.left-t-i},c={top:e.top,left:e.left+e.width+i};switch(o){case"top":return{top:e.top-r-n,left:e.left};case"right":return c;case"bottom":return{top:e.top+e.height+n,left:e.left};case"left":return a;case"start":return s?c:a;case"end":return s?a:c}},he=(o,e,t,r,i)=>{switch(o){case"center":return ue(e,t,r,i);case"end":return ve(e,t,r,i);default:return{top:0,left:0}}},ve=(o,e,t,r)=>{switch(o){case"start":case"end":case"left":case"right":return{top:-(r-e.height),left:0};default:return{top:0,left:-(t-e.width)}}},ue=(o,e,t,r)=>{switch(o){case"start":case"end":case"left":case"right":return{top:-(r/2-e.height/2),left:0};default:return{top:0,left:-(t/2-e.width/2)}}},Q=(o,e,t,r,i,n,s,a,c,d,g,h,b=0,m=0,x=0)=>{let _=b;const E=m;let P,y=t,T=e,I=d,O=g,f=!1,C=!1;const D=h?h.top+h.height:n/2-a/2,S=h?h.height:0;let W=!1;return y<r+c?(y=r,f=!0,I="left"):s+r+y+c>i&&(C=!0,y=i-s-r,I="right"),D+S+a>n&&("top"===o||"bottom"===o)&&(D-a>0?(T=Math.max(12,D-a-S-(x-1)),_=T+a,O="bottom",W=!0):P=r),{top:T,left:y,bottom:P,originX:I,originY:O,checkSafeAreaLeft:f,checkSafeAreaRight:C,arrowTop:_,arrowLeft:E,addPopoverBottomClass:W}},be=(o,e)=>{var t;const{event:r,size:i,trigger:n,reference:s,side:a,align:c}=e,d=o.ownerDocument,g="rtl"===d.dir,h=d.defaultView.innerWidth,b=d.defaultView.innerHeight,m=(0,w.g)(o),x=m.querySelector(".popover-content"),_=m.querySelector(".popover-arrow"),E=n||(null===(t=r?.detail)||void 0===t?void 0:t.ionShadowTarget)||r?.target,{contentWidth:y,contentHeight:T}=G(i,x,E),{arrowWidth:P,arrowHeight:I}=(o=>{if(!o)return{arrowWidth:0,arrowHeight:0};const{width:e,height:t}=o.getBoundingClientRect();return{arrowWidth:e,arrowHeight:t}})(_),f=Z(g,y,T,P,I,s,a,c,{top:b/2-T/2,left:h/2-y/2,originX:g?"right":"left",originY:"top"},n,r),C="cover"===i?0:5,D="cover"===i?0:25,{originX:S,originY:W,top:$,left:M,bottom:Y,checkSafeAreaLeft:U,checkSafeAreaRight:Ae,arrowTop:Ee,arrowLeft:Te,addPopoverBottomClass:Ie}=Q(a,f.top,f.left,C,h,b,y,T,D,f.originX,f.originY,f.referenceCoordinates,f.arrowTop,f.arrowLeft,I),Oe=(0,p.c)(),te=(0,p.c)(),oe=(0,p.c)();return te.addElement(m.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),oe.addElement(m.querySelector(".popover-arrow")).addElement(m.querySelector(".popover-content")).fromTo("opacity",.01,1),Oe.easing("ease").duration(100).beforeAddWrite(()=>{"cover"===i&&o.style.setProperty("--width",`${y}px`),Ie&&o.classList.add("popover-bottom"),void 0!==Y&&x.style.setProperty("bottom",`${Y}px`);let q=`${M}px`;U&&(q=`${M}px + var(--ion-safe-area-left, 0)`),Ae&&(q=`${M}px - var(--ion-safe-area-right, 0)`),x.style.setProperty("top",`calc(${$}px + var(--offset-y, 0))`),x.style.setProperty("left",`calc(${q} + var(--offset-x, 0))`),x.style.setProperty("transform-origin",`${W} ${S}`),null!==_&&(((o,e=!1,t,r)=>!(!t&&!r||"top"!==o&&"bottom"!==o&&e))(a,f.top!==$||f.left!==M,r,n)?(_.style.setProperty("top",`calc(${Ee}px + var(--offset-y, 0))`),_.style.setProperty("left",`calc(${Te}px + var(--offset-x, 0))`)):_.style.setProperty("display","none"))}).addAnimation([te,oe])},ye=o=>{const e=(0,w.g)(o),t=e.querySelector(".popover-content"),r=e.querySelector(".popover-arrow"),i=(0,p.c)(),n=(0,p.c)(),s=(0,p.c)();return n.addElement(e.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),s.addElement(e.querySelector(".popover-arrow")).addElement(e.querySelector(".popover-content")).fromTo("opacity",.99,0),i.easing("ease").afterAddWrite(()=>{o.style.removeProperty("--width"),o.classList.remove("popover-bottom"),t.style.removeProperty("top"),t.style.removeProperty("left"),t.style.removeProperty("bottom"),t.style.removeProperty("transform-origin"),r&&(r.style.removeProperty("top"),r.style.removeProperty("left"),r.style.removeProperty("display"))}).duration(300).addAnimation([n,s])},we=(o,e)=>{var t;const{event:r,size:i,trigger:n,reference:s,side:a,align:c}=e,d=o.ownerDocument,g="rtl"===d.dir,h=d.defaultView.innerWidth,b=d.defaultView.innerHeight,m=(0,w.g)(o),x=m.querySelector(".popover-content"),_=n||(null===(t=r?.detail)||void 0===t?void 0:t.ionShadowTarget)||r?.target,{contentWidth:E,contentHeight:y}=G(i,x,_),P=Z(g,E,y,0,0,s,a,c,{top:b/2-y/2,left:h/2-E/2,originX:g?"right":"left",originY:"top"},n,r),I="cover"===i?0:12,{originX:O,originY:f,top:C,left:D,bottom:S}=Q(a,P.top,P.left,I,h,b,E,y,0,P.originX,P.originY,P.referenceCoordinates),W=(0,p.c)(),$=(0,p.c)(),M=(0,p.c)(),Y=(0,p.c)(),U=(0,p.c)();return $.addElement(m.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),M.addElement(m.querySelector(".popover-wrapper")).duration(150).fromTo("opacity",.01,1),Y.addElement(x).beforeStyles({top:`calc(${C}px + var(--offset-y, 0px))`,left:`calc(${D}px + var(--offset-x, 0px))`,"transform-origin":`${f} ${O}`}).beforeAddWrite(()=>{void 0!==S&&x.style.setProperty("bottom",`${S}px`)}).fromTo("transform","scale(0.8)","scale(1)"),U.addElement(m.querySelector(".popover-viewport")).fromTo("opacity",.01,1),W.easing("cubic-bezier(0.36,0.66,0.04,1)").duration(300).beforeAddWrite(()=>{"cover"===i&&o.style.setProperty("--width",`${E}px`),"bottom"===f&&o.classList.add("popover-bottom")}).addAnimation([$,M,Y,U])},_e=o=>{const e=(0,w.g)(o),t=e.querySelector(".popover-content"),r=(0,p.c)(),i=(0,p.c)(),n=(0,p.c)();return i.addElement(e.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),n.addElement(e.querySelector(".popover-wrapper")).fromTo("opacity",.99,0),r.easing("ease").afterAddWrite(()=>{o.style.removeProperty("--width"),o.classList.remove("popover-bottom"),t.style.removeProperty("top"),t.style.removeProperty("left"),t.style.removeProperty("bottom"),t.style.removeProperty("transform-origin")}).duration(150).addAnimation([i,n])},ee=class{constructor(o){(0,l.r)(this,o),this.didPresent=(0,l.d)(this,"ionPopoverDidPresent",7),this.willPresent=(0,l.d)(this,"ionPopoverWillPresent",7),this.willDismiss=(0,l.d)(this,"ionPopoverWillDismiss",7),this.didDismiss=(0,l.d)(this,"ionPopoverDidDismiss",7),this.didPresentShorthand=(0,l.d)(this,"didPresent",7),this.willPresentShorthand=(0,l.d)(this,"willPresent",7),this.willDismissShorthand=(0,l.d)(this,"willDismiss",7),this.didDismissShorthand=(0,l.d)(this,"didDismiss",7),this.ionMount=(0,l.d)(this,"ionMount",7),this.parentPopover=null,this.coreDelegate=(0,j.C)(),this.lockController=(0,X.c)(),this.inline=!1,this.focusDescendantOnPresent=!1,this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.backdropDismiss=!0,this.showBackdrop=!0,this.translucent=!1,this.animated=!0,this.triggerAction="click",this.size="auto",this.dismissOnSelect=!1,this.reference="trigger",this.side="bottom",this.arrow=!0,this.isOpen=!1,this.keyboardEvents=!1,this.focusTrap=!0,this.keepContentsMounted=!1,this.onBackdropTap=()=>{this.dismiss(void 0,A.B)},this.onLifecycle=e=>{const t=this.usersElement,r=De[e.type];if(t&&r){const i=new CustomEvent(r,{bubbles:!1,cancelable:!1,detail:e.detail});t.dispatchEvent(i)}},this.configureTriggerInteraction=()=>{const{trigger:e,triggerAction:t,el:r,destroyTriggerInteraction:i}=this;if(i&&i(),void 0===e)return;const n=this.triggerEl=void 0!==e?document.getElementById(e):null;n?this.destroyTriggerInteraction=ie(n,t,r):(0,l.m)(`[ion-popover] - A trigger element with the ID "${e}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on ion-popover.`,this.el)},this.configureKeyboardInteraction=()=>{const{destroyKeyboardInteraction:e,el:t}=this;e&&e(),this.destroyKeyboardInteraction=pe(t)},this.configureDismissInteraction=()=>{const{destroyDismissInteraction:e,parentPopover:t,triggerAction:r,triggerEl:i,el:n}=this;!t||!i||(e&&e(),this.destroyDismissInteraction=((o,e,t,r)=>{let i=[];const s=(0,w.g)(r).querySelector(".popover-content");return i="hover"===e?[{eventName:"mouseenter",callback:a=>{document.elementFromPoint(a.clientX,a.clientY)!==o&&t.dismiss(void 0,void 0,!1)}}]:[{eventName:"click",callback:a=>{a.target.closest("[data-ion-popover-trigger]")!==o?t.dismiss(void 0,void 0,!1):a.stopPropagation()}}],i.forEach(({eventName:a,callback:c})=>s.addEventListener(a,c)),()=>{i.forEach(({eventName:a,callback:c})=>s.removeEventListener(a,c))}})(i,r,n,t))}}onTriggerChange(){this.configureTriggerInteraction()}onIsOpenChange(o,e){!0===o&&!1===e?this.present():!1===o&&!0===e&&this.dismiss()}connectedCallback(){const{configureTriggerInteraction:o,el:e}=this;(0,A.j)(e),o()}disconnectedCallback(){const{destroyTriggerInteraction:o}=this;o&&o()}componentWillLoad(){var o,e;const{el:t}=this,r=null!==(e=null===(o=this.htmlAttributes)||void 0===o?void 0:o.id)&&void 0!==e?e:(0,A.k)(t);this.parentPopover=t.closest(`ion-popover:not(#${r})`),void 0===this.alignment&&(this.alignment="ios"===(0,l.e)(this)?"center":"start")}componentDidLoad(){const{parentPopover:o,isOpen:e}=this;!0===e&&(0,w.r)(()=>this.present()),o&&(0,w.f)(o,"ionPopoverWillDismiss",()=>{this.dismiss(void 0,void 0,!1)}),this.configureTriggerInteraction()}presentFromTrigger(o){var e=this;return(0,L.A)(function*(t,r=!1){e.focusDescendantOnPresent=r,yield e.present(t),e.focusDescendantOnPresent=!1}).apply(this,arguments)}getDelegate(o=!1){if(this.workingDelegate&&!o)return{delegate:this.workingDelegate,inline:this.inline};const t=this.inline=null!==this.el.parentNode&&!this.hasController;return{inline:t,delegate:this.workingDelegate=t?this.delegate||this.coreDelegate:this.delegate}}present(o){var e=this;return(0,L.A)(function*(){const t=yield e.lockController.lock();if(e.presented)return void t();const{el:r}=e,{inline:i,delegate:n}=e.getDelegate(!0);e.ionMount.emit(),e.usersElement=yield(0,j.a)(n,r,e.component,["popover-viewport"],e.componentProps,i),e.keyboardEvents||e.configureKeyboardInteraction(),e.configureDismissInteraction(),(0,w.h)(r)?yield(0,u.e)(e.usersElement):e.keepContentsMounted||(yield(0,u.w)()),yield(0,A.f)(e,"popoverEnter",be,we,{event:o||e.event,size:e.size,trigger:e.triggerEl,reference:e.reference,side:e.side,align:e.alignment}),e.focusDescendantOnPresent&&(0,A.n)(r),t()})()}dismiss(o,e){var t=this;return(0,L.A)(function*(r,i,n=!0){const s=yield t.lockController.lock(),{destroyKeyboardInteraction:a,destroyDismissInteraction:c}=t;n&&t.parentPopover&&t.parentPopover.dismiss(r,i,n);const d=yield(0,A.g)(t,r,i,"popoverLeave",ye,_e,t.event);if(d){a&&(a(),t.destroyKeyboardInteraction=void 0),c&&(c(),t.destroyDismissInteraction=void 0);const{delegate:g}=t.getDelegate();yield(0,j.d)(g,t.usersElement)}return s(),d}).apply(this,arguments)}getParentPopover(){var o=this;return(0,L.A)(function*(){return o.parentPopover})()}onDidDismiss(){return(0,A.h)(this.el,"ionPopoverDidDismiss")}onWillDismiss(){return(0,A.h)(this.el,"ionPopoverWillDismiss")}render(){const o=(0,l.e)(this),{onLifecycle:e,parentPopover:t,dismissOnSelect:r,side:i,arrow:n,htmlAttributes:s,focusTrap:a}=this,c=(0,l.a)("desktop"),d=n&&!t;return(0,l.h)(l.j,Object.assign({key:"16866c02534968c982cf4730d2936d03a5107c8b","aria-modal":"true","no-router":!0,tabindex:"-1"},s,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign(Object.assign({},(0,B.g)(this.cssClass)),{[o]:!0,"popover-translucent":this.translucent,"overlay-hidden":!0,"popover-desktop":c,[`popover-side-${i}`]:!0,[A.F]:!1===a,"popover-nested":!!t}),onIonPopoverDidPresent:e,onIonPopoverWillPresent:e,onIonPopoverWillDismiss:e,onIonPopoverDidDismiss:e,onIonBackdropTap:this.onBackdropTap}),!t&&(0,l.h)("ion-backdrop",{key:"0df258601a4d30df3c27aa8234a7d5e056c3ecbb",tappable:this.backdropDismiss,visible:this.showBackdrop,part:"backdrop"}),(0,l.h)("div",{key:"f94e80ed996b957b5cd09b826472b4f60e8fcc78",class:"popover-wrapper ion-overlay-wrapper",onClick:r?()=>this.dismiss():void 0},d&&(0,l.h)("div",{key:"185ce22f6386e8444a9cc7b8818dbfc16c463c99",class:"popover-arrow",part:"arrow"}),(0,l.h)("div",{key:"206202b299404e110de5397b229678cca18568d3",class:"popover-content",part:"content"},(0,l.h)("slot",{key:"ee543a0b92d6e35a837c0a0e4617c7b0fc4ad0b0"}))))}get el(){return(0,l.k)(this)}static get watchers(){return{trigger:["onTriggerChange"],triggerAction:["onTriggerChange"],isOpen:["onIsOpenChange"]}}},De={ionPopoverDidPresent:"ionViewDidEnter",ionPopoverWillPresent:"ionViewWillEnter",ionPopoverWillDismiss:"ionViewWillLeave",ionPopoverDidDismiss:"ionViewDidLeave"};ee.style={ios:':host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:200px;--max-height:90%;--box-shadow:none;--backdrop-opacity:var(--ion-backdrop-opacity, 0.08)}:host(.popover-desktop){--box-shadow:0px 4px 16px 0px rgba(0, 0, 0, 0.12)}.popover-content{border-radius:10px}:host(.popover-desktop) .popover-content{border:0.5px solid var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.popover-arrow{display:block;position:absolute;width:20px;height:10px;overflow:hidden;z-index:11}.popover-arrow::after{top:3px;border-radius:3px;position:absolute;width:14px;height:14px;-webkit-transform:rotate(45deg);transform:rotate(45deg);background:var(--background);content:"";z-index:10}.popover-arrow::after{inset-inline-start:3px}:host(.popover-bottom) .popover-arrow{top:auto;bottom:-10px}:host(.popover-bottom) .popover-arrow::after{top:-6px}:host(.popover-side-left) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host(.popover-side-right) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host(.popover-side-top) .popover-arrow{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.popover-side-start) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host-context([dir=rtl]):host(.popover-side-start) .popover-arrow,:host-context([dir=rtl]).popover-side-start .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}@supports selector(:dir(rtl)){:host(.popover-side-start:dir(rtl)) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}}:host(.popover-side-end) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host-context([dir=rtl]):host(.popover-side-end) .popover-arrow,:host-context([dir=rtl]).popover-side-end .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}@supports selector(:dir(rtl)){:host(.popover-side-end:dir(rtl)) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}}.popover-arrow,.popover-content{opacity:0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.popover-translucent) .popover-content,:host(.popover-translucent) .popover-arrow::after{background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}',md:":host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:250px;--max-height:90%;--box-shadow:0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}.popover-content{border-radius:4px;-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]) .popover-content{-webkit-transform-origin:right top;transform-origin:right top}[dir=rtl] .popover-content{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.popover-content:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}.popover-viewport{-webkit-transition-delay:100ms;transition-delay:100ms}.popover-wrapper{opacity:0}"}},4576:(re,K,v)=>{v.d(K,{c:()=>A,g:()=>w,h:()=>l,o:()=>B});var L=v(467);const l=(u,p)=>null!==p.closest(u),A=(u,p)=>"string"==typeof u&&u.length>0?Object.assign({"ion-color":!0,[`ion-color-${u}`]:!0},p):p,w=u=>{const p={};return(u=>void 0!==u?(Array.isArray(u)?u:u.split(" ")).filter(k=>null!=k).map(k=>k.trim()).filter(k=>""!==k):[])(u).forEach(k=>p[k]=!0),p},X=/^[a-z][a-z0-9+\-.]*:/,B=function(){var u=(0,L.A)(function*(p,k,V,F){if(null!=p&&"#"!==p[0]&&!X.test(p)){const R=document.querySelector("ion-router");if(R)return k?.preventDefault(),R.push(p,V,F)}return!1});return function(k,V,F,R){return u.apply(this,arguments)}}()}}]);