"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[5712],{5712:(D,T,w)=>{w.r(T),w.d(T,{ion_nav:()=>V,ion_nav_link:()=>R});var m=w(467),u=w(2734),A=w(9166),c=w(1837),g=w(4590),I=w(1653);class _{constructor(e,t){this.component=e,this.params=t,this.state=1}init(e){var t=this;return(0,m.A)(function*(){if(t.state=2,!t.element){const n=t.component;t.element=yield(0,I.a)(t.delegate,e,n,["ion-page","ion-page-invisible"],t.params)}})()}_destroy(){(0,c.l)(3!==this.state,"view state must be ATTACHED");const e=this.element;e&&(this.delegate?this.delegate.removeViewFromDom(e.parentElement,e):e.remove()),this.nav=void 0,this.state=3}}const C=(l,e,t)=>!(!l||l.component!==e)&&(0,c.s)(l.params,t),k=(l,e)=>l?l instanceof _?l:new _(l,e):null,V=(()=>{let l=class{constructor(e){(0,u.r)(this,e),this.ionNavWillLoad=(0,u.d)(this,"ionNavWillLoad",7),this.ionNavWillChange=(0,u.d)(this,"ionNavWillChange",3),this.ionNavDidChange=(0,u.d)(this,"ionNavDidChange",3),this.transInstr=[],this.gestureOrAnimationInProgress=!1,this.useRouter=!1,this.isTransitioning=!1,this.destroyed=!1,this.views=[],this.didLoad=!1,this.animated=!0}swipeGestureChanged(){this.gesture&&this.gesture.enable(!0===this.swipeGesture)}rootChanged(){void 0!==this.root&&!1!==this.didLoad&&(this.useRouter||void 0!==this.root&&this.setRoot(this.root,this.rootParams))}componentWillLoad(){if(this.useRouter=null!==document.querySelector("ion-router")&&null===this.el.closest("[no-router]"),void 0===this.swipeGesture){const e=(0,u.e)(this);this.swipeGesture=u.l.getBoolean("swipeBackEnabled","ios"===e)}this.ionNavWillLoad.emit()}componentDidLoad(){var e=this;return(0,m.A)(function*(){e.didLoad=!0,e.rootChanged(),e.gesture=(yield w.e(2076).then(w.bind(w,6438))).createSwipeBackGesture(e.el,e.canStart.bind(e),e.onStart.bind(e),e.onMove.bind(e),e.onEnd.bind(e)),e.swipeGestureChanged()})()}connectedCallback(){this.destroyed=!1}disconnectedCallback(){for(const e of this.views)(0,g.l)(e.element,g.d),e._destroy();this.gesture&&(this.gesture.destroy(),this.gesture=void 0),this.transInstr.length=0,this.views.length=0,this.destroyed=!0}push(e,t,n,i){return this.insert(-1,e,t,n,i)}insert(e,t,n,i,s){return this.insertPages(e,[{component:t,componentProps:n}],i,s)}insertPages(e,t,n,i){return this.queueTrns({insertStart:e,insertViews:t,opts:n},i)}pop(e,t){return this.removeIndex(-1,1,e,t)}popTo(e,t,n){const i={removeStart:-1,removeCount:-1,opts:t};return"object"==typeof e&&e.component?(i.removeView=e,i.removeStart=1):"number"==typeof e&&(i.removeStart=e+1),this.queueTrns(i,n)}popToRoot(e,t){return this.removeIndex(1,-1,e,t)}removeIndex(e,t=1,n,i){return this.queueTrns({removeStart:e,removeCount:t,opts:n},i)}setRoot(e,t,n,i){return this.setPages([{component:e,componentProps:t}],n,i)}setPages(e,t,n){return t??(t={}),!0!==t.animated&&(t.animated=!1),this.queueTrns({insertStart:0,insertViews:e,removeStart:0,removeCount:-1,opts:t},n)}setRouteId(e,t,n,i){const s=this.getActiveSync();if(C(s,e,t))return Promise.resolve({changed:!1,element:s.element});let a;const o=new Promise(h=>a=h);let r;const f={updateURL:!1,viewIsReady:h=>{let v;const p=new Promise(d=>v=d);return a({changed:!0,element:h,markVisible:(d=(0,m.A)(function*(){v(),yield r}),function(){return d.apply(this,arguments)})}),p;var d}};if("root"===n)r=this.setRoot(e,t,f);else{const h=this.views.find(v=>C(v,e,t));h?r=this.popTo(h,Object.assign(Object.assign({},f),{direction:"back",animationBuilder:i})):"forward"===n?r=this.push(e,t,Object.assign(Object.assign({},f),{animationBuilder:i})):"back"===n&&(r=this.setRoot(e,t,Object.assign(Object.assign({},f),{direction:"back",animated:!0,animationBuilder:i})))}return o}getRouteId(){var e=this;return(0,m.A)(function*(){const t=e.getActiveSync();if(t)return{id:t.element.tagName,params:t.params,element:t.element}})()}getActive(){var e=this;return(0,m.A)(function*(){return e.getActiveSync()})()}getByIndex(e){var t=this;return(0,m.A)(function*(){return t.views[e]})()}canGoBack(e){var t=this;return(0,m.A)(function*(){return t.canGoBackSync(e)})()}getPrevious(e){var t=this;return(0,m.A)(function*(){return t.getPreviousSync(e)})()}getLength(){var e=this;return(0,m.A)(function*(){return Promise.resolve(e.views.length)})()}getActiveSync(){return this.views[this.views.length-1]}canGoBackSync(e=this.getActiveSync()){return!(!e||!this.getPreviousSync(e))}getPreviousSync(e=this.getActiveSync()){if(!e)return;const t=this.views,n=t.indexOf(e);return n>0?t[n-1]:void 0}queueTrns(e,t){var n=this;return(0,m.A)(function*(){var i,s;if(n.isTransitioning&&null!==(i=e.opts)&&void 0!==i&&i.skipIfBusy)return!1;const a=new Promise((o,r)=>{e.resolve=o,e.reject=r});if(e.done=t,e.opts&&!1!==e.opts.updateURL&&n.useRouter){const o=document.querySelector("ion-router");if(o){const r=yield o.canTransition();if(!1===r)return!1;if("string"==typeof r)return o.push(r,e.opts.direction||"back"),!1}}return 0===(null===(s=e.insertViews)||void 0===s?void 0:s.length)&&(e.insertViews=void 0),n.transInstr.push(e),n.nextTrns(),a})()}success(e,t){if(this.destroyed)this.fireError("nav controller was destroyed",t);else if(t.done&&t.done(e.hasCompleted,e.requiresTransition,e.enteringView,e.leavingView,e.direction),t.resolve(e.hasCompleted),!1!==t.opts.updateURL&&this.useRouter){const n=document.querySelector("ion-router");n&&n.navChanged("back"===e.direction?"back":"forward")}}failed(e,t){this.destroyed?this.fireError("nav controller was destroyed",t):(this.transInstr.length=0,this.fireError(e,t))}fireError(e,t){t.done&&t.done(!1,!1,e),t.reject&&!this.destroyed?t.reject(e):t.resolve(!1)}nextTrns(){if(this.isTransitioning)return!1;const e=this.transInstr.shift();return!!e&&(this.runTransition(e),!0)}runTransition(e){var t=this;return(0,m.A)(function*(){try{t.ionNavWillChange.emit(),t.isTransitioning=!0,t.prepareTI(e);const n=t.getActiveSync(),i=t.getEnteringView(e,n);if(!n&&!i)throw new Error("no views in the stack to be removed");i&&1===i.state&&(yield i.init(t.el)),t.postViewInit(i,n,e);const s=(e.enteringRequiresTransition||e.leavingRequiresTransition)&&i!==n;let a;s&&e.opts&&n&&("back"===e.opts.direction&&(e.opts.animationBuilder=e.opts.animationBuilder||i?.animationBuilder),n.animationBuilder=e.opts.animationBuilder),a=s?yield t.transition(i,n,e):{hasCompleted:!0,requiresTransition:!1},t.success(a,e),t.ionNavDidChange.emit()}catch(n){t.failed(n,e)}t.isTransitioning=!1,t.nextTrns()})()}prepareTI(e){var t,n,i;const s=this.views.length;if(null!==(t=e.opts)&&void 0!==t||(e.opts={}),null!==(n=(i=e.opts).delegate)&&void 0!==n||(i.delegate=this.delegate),void 0!==e.removeView){(0,c.l)(void 0!==e.removeStart,"removeView needs removeStart"),(0,c.l)(void 0!==e.removeCount,"removeView needs removeCount");const r=this.views.indexOf(e.removeView);if(r<0)throw new Error("removeView was not found");e.removeStart+=r}void 0!==e.removeStart&&(e.removeStart<0&&(e.removeStart=s-1),e.removeCount<0&&(e.removeCount=s-e.removeStart),e.leavingRequiresTransition=e.removeCount>0&&e.removeStart+e.removeCount===s),e.insertViews&&((e.insertStart<0||e.insertStart>s)&&(e.insertStart=s),e.enteringRequiresTransition=e.insertStart===s);const a=e.insertViews;if(!a)return;(0,c.l)(a.length>0,"length can not be zero");const o=(l=>l.map(e=>e instanceof _?e:"component"in e?k(e.component,null===e.componentProps?void 0:e.componentProps):k(e,void 0)).filter(e=>null!==e))(a);if(0===o.length)throw new Error("invalid views to insert");for(const r of o){r.delegate=e.opts.delegate;const f=r.nav;if(f&&f!==this)throw new Error("inserted view was already inserted");if(3===r.state)throw new Error("inserted view was already destroyed")}e.insertViews=o}getEnteringView(e,t){const n=e.insertViews;if(void 0!==n)return n[n.length-1];const i=e.removeStart;if(void 0!==i){const s=this.views,a=i+e.removeCount;for(let o=s.length-1;o>=0;o--){const r=s[o];if((o<i||o>=a)&&r!==t)return r}}}postViewInit(e,t,n){var i,s,a;(0,c.l)(t||e,"Both leavingView and enteringView are null"),(0,c.l)(n.resolve,"resolve must be valid"),(0,c.l)(n.reject,"reject must be valid");const o=n.opts,{insertViews:r,removeStart:f,removeCount:h}=n;let v;if(void 0!==f&&void 0!==h){(0,c.l)(f>=0,"removeStart can not be negative"),(0,c.l)(h>=0,"removeCount can not be negative"),v=[];for(let d=f;d<f+h;d++){const b=this.views[d];void 0!==b&&b!==e&&b!==t&&v.push(b)}null!==(i=o.direction)&&void 0!==i||(o.direction="back")}const p=this.views.length+(null!==(s=r?.length)&&void 0!==s?s:0)-(h??0);if((0,c.l)(p>=0,"final balance can not be negative"),0===p)throw(0,u.m)("[ion-nav] - You can't remove all the pages in the navigation stack. nav.pop() is probably called too many times.",this,this.el),new Error("navigation stack needs at least one root page");if(r){let d=n.insertStart;for(const b of r)this.insertViewAt(b,d),d++;n.enteringRequiresTransition&&(null!==(a=o.direction)&&void 0!==a||(o.direction="forward"))}if(v&&v.length>0){for(const d of v)(0,g.l)(d.element,g.b),(0,g.l)(d.element,g.c),(0,g.l)(d.element,g.d);for(const d of v)this.destroyView(d)}}transition(e,t,n){var i=this;return(0,m.A)(function*(){const s=n.opts,a=s.progressAnimation?p=>{void 0===p||i.gestureOrAnimationInProgress?i.sbAni=p:(i.gestureOrAnimationInProgress=!0,p.onFinish(()=>{i.gestureOrAnimationInProgress=!1},{oneTimeCallback:!0}),p.progressEnd(0,0,0))}:void 0,o=(0,u.e)(i),r=e.element,f=t&&t.element,h=Object.assign(Object.assign({mode:o,showGoBack:i.canGoBackSync(e),baseEl:i.el,progressCallback:a,animated:i.animated&&u.l.getBoolean("animated",!0),enteringEl:r,leavingEl:f},s),{animationBuilder:s.animationBuilder||i.animation||u.l.get("navAnimation")}),{hasCompleted:v}=yield(0,g.t)(h);return i.transitionFinish(v,e,t,s)})()}transitionFinish(e,t,n,i){const s=e?t:n;return s&&this.unmountInactiveViews(s),{hasCompleted:e,requiresTransition:!0,enteringView:t,leavingView:n,direction:i.direction}}insertViewAt(e,t){const n=this.views,i=n.indexOf(e);i>-1?((0,c.l)(e.nav===this,"view is not part of the nav"),n.splice(i,1),n.splice(t,0,e)):((0,c.l)(!e.nav,"nav is used"),e.nav=this,n.splice(t,0,e))}removeView(e){(0,c.l)(2===e.state||3===e.state,"view state should be loaded or destroyed");const t=this.views,n=t.indexOf(e);(0,c.l)(n>-1,"view must be part of the stack"),n>=0&&t.splice(n,1)}destroyView(e){e._destroy(),this.removeView(e)}unmountInactiveViews(e){if(this.destroyed)return;const t=this.views,n=t.indexOf(e);for(let i=t.length-1;i>=0;i--){const s=t[i],a=s.element;a&&(i>n?((0,g.l)(a,g.d),this.destroyView(s)):i<n&&(0,g.s)(a,!0))}}canStart(){return!this.gestureOrAnimationInProgress&&!!this.swipeGesture&&!this.isTransitioning&&0===this.transInstr.length&&this.canGoBackSync()}onStart(){this.gestureOrAnimationInProgress=!0,this.pop({direction:"back",progressAnimation:!0})}onMove(e){this.sbAni&&this.sbAni.progressStep(e)}onEnd(e,t,n){if(this.sbAni){this.sbAni.onFinish(()=>{this.gestureOrAnimationInProgress=!1},{oneTimeCallback:!0});let i=e?-.001:.001;e?i+=(0,A.g)([0,0],[.32,.72],[0,1],[1,1],t)[0]:(this.sbAni.easing("cubic-bezier(1, 0, 0.68, 0.28)"),i+=(0,A.g)([0,0],[1,0],[.68,.28],[1,1],t)[0]),this.sbAni.progressEnd(e?1:0,i,n)}else this.gestureOrAnimationInProgress=!1}render(){return(0,u.h)("slot",{key:"8067c9835d255daec61f33dba200fd3a6ff839a0"})}get el(){return(0,u.k)(this)}static get watchers(){return{swipeGesture:["swipeGestureChanged"],root:["rootChanged"]}}};return l.style=":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}",l})(),R=class{constructor(l){(0,u.r)(this,l),this.routerDirection="forward",this.onClick=()=>((l,e,t,n,i)=>{const s=this.el.closest("ion-nav");if(s)if("forward"===e){if(void 0!==t)return s.push(t,n,{skipIfBusy:!0,animationBuilder:i})}else if("root"===e){if(void 0!==t)return s.setRoot(t,n,{skipIfBusy:!0,animationBuilder:i})}else if("back"===e)return s.pop({skipIfBusy:!0,animationBuilder:i});return Promise.resolve(!1)})(0,this.routerDirection,this.component,this.componentProps,this.routerAnimation)}render(){return(0,u.h)(u.j,{key:"6dbb1ad4f351e9215375aac11ab9b53762e07a08",onClick:this.onClick})}get el(){return(0,u.k)(this)}}}}]);