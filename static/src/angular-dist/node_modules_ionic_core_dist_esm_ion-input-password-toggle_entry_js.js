"use strict";
(self["webpackChunkdriver_evaluation_app"] = self["webpackChunkdriver_evaluation_app"] || []).push([["node_modules_ionic_core_dist_esm_ion-input-password-toggle_entry_js"],{

/***/ 5196:
/*!******************************************************************************!*\
  !*** ./node_modules/@ionic/core/dist/esm/ion-input-password-toggle.entry.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ion_input_password_toggle: () => (/* binding */ InputPasswordToggle)
/* harmony export */ });
/* harmony import */ var _index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index-B_U9CtaY.js */ 4917);
/* harmony import */ var _theme_DiVJyqlX_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./theme-DiVJyqlX.js */ 247);
/* harmony import */ var _index_BLV6ykCk_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index-BLV6ykCk.js */ 4361);
/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */



const iosInputPasswordToggleCss = "";
const mdInputPasswordToggleCss = "";
const InputPasswordToggle = class {
  constructor(hostRef) {
    (0,_index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__.r)(this, hostRef);
    /**
     * @internal
     */
    this.type = 'password';
    this.togglePasswordVisibility = () => {
      const {
        inputElRef
      } = this;
      if (!inputElRef) {
        return;
      }
      inputElRef.type = inputElRef.type === 'text' ? 'password' : 'text';
    };
  }
  /**
   * Whenever the input type changes we need to re-run validation to ensure the password
   * toggle is being used with the correct input type. If the application changes the type
   * outside of this component we also need to re-render so the correct icon is shown.
   */
  onTypeChange(newValue) {
    if (newValue !== 'text' && newValue !== 'password') {
      (0,_index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__.m)(`[ion-input-password-toggle] - Only inputs of type "text" or "password" are supported. Input of type "${newValue}" is not compatible.`, this.el);
      return;
    }
  }
  connectedCallback() {
    const {
      el
    } = this;
    const inputElRef = this.inputElRef = el.closest('ion-input');
    if (!inputElRef) {
      (0,_index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__.m)('[ion-input-password-toggle] - No ancestor ion-input found. This component must be slotted inside of an ion-input.', el);
      return;
    }
    /**
     * Important: Set the type in connectedCallback because the default value
     * of this.type may not always be accurate. Usually inputs have the "password" type
     * but it is possible to have the input to initially have the "text" type. In that scenario
     * the wrong icon will show briefly before switching to the correct icon. Setting the
     * type here allows us to avoid that flicker.
     */
    this.type = inputElRef.type;
  }
  disconnectedCallback() {
    this.inputElRef = null;
  }
  render() {
    var _a, _b;
    const {
      color,
      type
    } = this;
    const mode = (0,_index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__.e)(this);
    const showPasswordIcon = (_a = this.showIcon) !== null && _a !== void 0 ? _a : _index_BLV6ykCk_js__WEBPACK_IMPORTED_MODULE_2__.y;
    const hidePasswordIcon = (_b = this.hideIcon) !== null && _b !== void 0 ? _b : _index_BLV6ykCk_js__WEBPACK_IMPORTED_MODULE_2__.x;
    const isPasswordVisible = type === 'text';
    return (0,_index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__.h)(_index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__.j, {
      key: '91bc55664d496fe457518bd112865dd7811d0c17',
      class: (0,_theme_DiVJyqlX_js__WEBPACK_IMPORTED_MODULE_1__.c)(color, {
        [mode]: true
      })
    }, (0,_index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__.h)("ion-button", {
      key: 'f3e436422110c9cb4d5c0b83500255b24ab4cdef',
      mode: mode,
      color: color,
      fill: "clear",
      shape: "round",
      "aria-checked": isPasswordVisible ? 'true' : 'false',
      "aria-label": isPasswordVisible ? 'Hide password' : 'Show password',
      role: "switch",
      type: "button",
      onPointerDown: ev => {
        /**
         * This prevents mobile browsers from
         * blurring the input when the password toggle
         * button is activated.
         */
        ev.preventDefault();
      },
      onClick: this.togglePasswordVisibility
    }, (0,_index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__.h)("ion-icon", {
      key: '5c8b121153f148f92aa7cba0447673a4f6f3ad1e',
      slot: "icon-only",
      "aria-hidden": "true",
      icon: isPasswordVisible ? hidePasswordIcon : showPasswordIcon
    })));
  }
  get el() {
    return (0,_index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__.k)(this);
  }
  static get watchers() {
    return {
      "type": ["onTypeChange"]
    };
  }
};
InputPasswordToggle.style = {
  ios: iosInputPasswordToggleCss,
  md: mdInputPasswordToggleCss
};


/***/ })

}]);
//# sourceMappingURL=node_modules_ionic_core_dist_esm_ion-input-password-toggle_entry_js.js.map