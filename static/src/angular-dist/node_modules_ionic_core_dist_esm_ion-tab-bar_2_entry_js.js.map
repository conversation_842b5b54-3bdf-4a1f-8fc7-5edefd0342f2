{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-tab-bar_2_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC2I;AACzD;AACpB;AACC;AAClC;AACG;AACC;AAEjC,MAAMkB,YAAY,GAAG,w1EAAw1E;AAE72E,MAAMC,WAAW,GAAG,u0DAAu0D;AAE31D,MAAMC,MAAM,GAAG,MAAM;EACjBC,WAAWA,CAACC,OAAO,EAAE;IACjBrB,qDAAgB,CAAC,IAAI,EAAEqB,OAAO,CAAC;IAC/B,IAAI,CAACC,gBAAgB,GAAGpB,qDAAW,CAAC,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAChE,IAAI,CAACqB,eAAe,GAAGrB,qDAAW,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAAC,CAAC;IAC9D,IAAI,CAACsB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,KAAK;EAC5B;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACC,WAAW,KAAKC,SAAS,EAAE;MAChC,IAAI,CAACP,gBAAgB,CAACQ,IAAI,CAAC;QACvBC,GAAG,EAAE,IAAI,CAACH;MACd,CAAC,CAAC;IACN;EACJ;EACAI,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACL,kBAAkB,CAAC,CAAC;EAC7B;EACMM,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,oMAAA;MACtBD,KAAI,CAACV,YAAY,SAASX,mEAAwB;QAAA,IAAAuB,IAAA,GAAAD,oMAAA,CAAC,WAAOE,YAAY,EAAEC,aAAa,EAAK;UACtF;AACZ;AACA;AACA;AACA;UACY,IAAID,YAAY,KAAK,KAAK,IAAIC,aAAa,KAAKT,SAAS,EAAE;YACvD,MAAMS,aAAa;UACvB;UACAJ,KAAI,CAACT,eAAe,GAAGY,YAAY,CAAC,CAAC;QACzC,CAAC;QAAA,iBAAAE,EAAA,EAAAC,GAAA;UAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;IAAC;EACP;EACAC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACnB,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACoB,OAAO,CAAC,CAAC;IAC/B;EACJ;EACAC,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACtB,eAAe,CAACO,IAAI,CAAC,CAAC;EAC/B;EACAgB,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEC,KAAK;MAAErB,WAAW;MAAED;IAAgB,CAAC,GAAG,IAAI;IACpD,MAAMuB,IAAI,GAAG5C,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAM6C,UAAU,GAAGxB,eAAe,IAAI,IAAI,CAACyB,EAAE,CAACC,YAAY,CAAC,MAAM,CAAC,KAAK,KAAK;IAC5E,OAAQ9C,qDAAC,CAACE,iDAAI,EAAE;MAAE6C,GAAG,EAAE,0CAA0C;MAAEC,IAAI,EAAE,SAAS;MAAE,aAAa,EAAEJ,UAAU,GAAG,MAAM,GAAG,IAAI;MAAEK,KAAK,EAAExC,qDAAkB,CAACiC,KAAK,EAAE;QACxJ,CAACC,IAAI,GAAG,IAAI;QACZ,qBAAqB,EAAEtB,WAAW;QAClC,gBAAgB,EAAEuB;MACtB,CAAC;IAAE,CAAC,EAAE5C,qDAAC,CAAC,MAAM,EAAE;MAAE+C,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC7E;EACA,IAAIF,EAAEA,CAAA,EAAG;IAAE,OAAOzC,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW8C,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,aAAa,EAAE,CAAC,oBAAoB;IACxC,CAAC;EAAE;AACP,CAAC;AACDpC,MAAM,CAACqC,KAAK,GAAG;EACXC,GAAG,EAAExC,YAAY;EACjByC,EAAE,EAAExC;AACR,CAAC;AAED,MAAMyC,eAAe,GAAG,s7JAAs7J;AAE98J,MAAMC,cAAc,GAAG,g8KAAg8K;AAEv9K,MAAMC,SAAS,GAAG,MAAM;EACpBzC,WAAWA,CAACC,OAAO,EAAE;IACjBrB,qDAAgB,CAAC,IAAI,EAAEqB,OAAO,CAAC;IAC/B,IAAI,CAACyC,iBAAiB,GAAG5D,qDAAW,CAAC,IAAI,EAAE,mBAAmB,EAAE,CAAC,CAAC;IAClE,IAAI,CAAC6D,mBAAmB,GAAG,CAAC,CAAC;IAC7B;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,OAAO,GAAIC,EAAE,IAAK;MACnB,IAAIA,EAAE,CAACf,GAAG,KAAK,OAAO,IAAIe,EAAE,CAACf,GAAG,KAAK,GAAG,EAAE;QACtC,IAAI,CAACgB,SAAS,CAACD,EAAE,CAAC;MACtB;IACJ,CAAC;IACD,IAAI,CAACE,OAAO,GAAIF,EAAE,IAAK;MACnB,IAAI,CAACC,SAAS,CAACD,EAAE,CAAC;IACtB,CAAC;EACL;EACAG,eAAeA,CAACH,EAAE,EAAE;IAChB,MAAMI,cAAc,GAAGJ,EAAE,CAACK,MAAM;IAChC,MAAMC,MAAM,GAAG,IAAI,CAACvB,EAAE,CAACwB,aAAa;IACpC,IAAIP,EAAE,CAACQ,YAAY,CAAC,CAAC,CAACC,QAAQ,CAACH,MAAM,CAAC,KAAKF,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACM,QAAQ,CAAC,IAAI,CAAC3B,EAAE,CAAC,CAAC,EAAE;MAC1I,IAAI,CAACe,QAAQ,GAAG,IAAI,CAAClC,GAAG,KAAKoC,EAAE,CAACW,MAAM,CAAC/C,GAAG;IAC9C;EACJ;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC+B,mBAAmB,GAAGgB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEhE,uDAAiB,CAAC,IAAI,CAACkC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;IACxF,IAAI,IAAI,CAAC+B,MAAM,KAAKpD,SAAS,EAAE;MAC3B,IAAI,CAACoD,MAAM,GAAGtE,iDAAM,CAACuE,GAAG,CAAC,iBAAiB,EAAE,UAAU,CAAC;IAC3D;EACJ;EACAd,SAASA,CAACD,EAAE,EAAE;IACV,IAAI,IAAI,CAACpC,GAAG,KAAKF,SAAS,EAAE;MACxB,IAAI,CAAC,IAAI,CAACmC,QAAQ,EAAE;QAChB,IAAI,CAACF,iBAAiB,CAAChC,IAAI,CAAC;UACxBC,GAAG,EAAE,IAAI,CAACA,GAAG;UACboD,IAAI,EAAE,IAAI,CAACA,IAAI;UACflB,QAAQ,EAAE,IAAI,CAACA;QACnB,CAAC,CAAC;MACN;MACAE,EAAE,CAACiB,cAAc,CAAC,CAAC;IACvB;EACJ;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,CAAC,CAAC,IAAI,CAACnC,EAAE,CAACoC,aAAa,CAAC,WAAW,CAAC;EAC/C;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,CAAC,CAAC,IAAI,CAACrC,EAAE,CAACoC,aAAa,CAAC,UAAU,CAAC;EAC9C;EACAxC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEkB,QAAQ;MAAEuB,OAAO;MAAEF,QAAQ;MAAEF,IAAI;MAAEK,GAAG;MAAEhB,MAAM;MAAES,MAAM;MAAEhB,QAAQ;MAAElC,GAAG;MAAEgC;IAAoB,CAAC,GAAG,IAAI;IAC3G,MAAMf,IAAI,GAAG5C,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAMqF,KAAK,GAAG;MACVC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBP,IAAI;MACJK,GAAG;MACHhB;IACJ,CAAC;IACD,OAAQnE,qDAAC,CAACE,iDAAI,EAAE;MAAE6C,GAAG,EAAE,0CAA0C;MAAEiB,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEsB,OAAO,EAAE,IAAI,CAACzB,OAAO;MAAE0B,EAAE,EAAE7D,GAAG,KAAKF,SAAS,GAAG,cAAcE,GAAG,EAAE,GAAG,IAAI;MAAEuB,KAAK,EAAE;QACpK,CAACN,IAAI,GAAG,IAAI;QACZ,cAAc,EAAEiB,QAAQ;QACxB,cAAc,EAAED,QAAQ;QACxB,eAAe,EAAEqB,QAAQ;QACzB,cAAc,EAAEE,OAAO;QACvB,oBAAoB,EAAEF,QAAQ,IAAI,CAACE,OAAO;QAC1C,mBAAmB,EAAEA,OAAO,IAAI,CAACF,QAAQ;QACzC,CAAC,cAAcJ,MAAM,EAAE,GAAG,IAAI;QAC9B,iBAAiB,EAAE,IAAI;QACvB,gBAAgB,EAAE,IAAI;QACtB,eAAe,EAAE;MACrB;IAAE,CAAC,EAAE5E,qDAAC,CAAC,GAAG,EAAE0E,MAAM,CAACC,MAAM,CAAC;MAAE5B,GAAG,EAAE;IAA2C,CAAC,EAAEqC,KAAK,EAAE;MAAEnC,KAAK,EAAE,eAAe;MAAEuC,IAAI,EAAE,QAAQ;MAAExC,IAAI,EAAE,KAAK;MAAE,eAAe,EAAEY,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAE,eAAe,EAAED,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAE8B,QAAQ,EAAE9B,QAAQ,GAAG,IAAI,GAAGnC;IAAU,CAAC,EAAEkC,mBAAmB,CAAC,EAAE1D,qDAAC,CAAC,MAAM,EAAE;MAAE+C,GAAG,EAAE,0CAA0C;MAAEE,KAAK,EAAE;IAAe,CAAC,EAAEjD,qDAAC,CAAC,MAAM,EAAE;MAAE+C,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAEJ,IAAI,KAAK,IAAI,IAAI3C,qDAAC,CAAC,mBAAmB,EAAE;MAAE+C,GAAG,EAAE,0CAA0C;MAAE2C,IAAI,EAAE;IAAY,CAAC,CAAC,CAAC,CAAC;EACljB;EACA,IAAI7C,EAAEA,CAAA,EAAG;IAAE,OAAOzC,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDoD,SAAS,CAACL,KAAK,GAAG;EACdC,GAAG,EAAEE,eAAe;EACpBD,EAAE,EAAEE;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-tab-bar_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, e as getIonMode, h, j as Host, k as getElement, l as config } from './index-B_U9CtaY.js';\nimport { c as createKeyboardController } from './keyboard-controller-BaaVITYt.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\nimport { b as inheritAttributes } from './helpers-1O4D2b7y.js';\nimport './index-ZjP4CjeZ.js';\nimport './keyboard-CUw4ekVy.js';\nimport './capacitor-CFERIeaU.js';\n\nconst tabBarIosCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:auto;padding-right:var(--ion-safe-area-right);padding-bottom:var(--ion-safe-area-bottom, 0);padding-left:var(--ion-safe-area-left);border-top:var(--border);background:var(--background);color:var(--color);text-align:center;contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:10;-webkit-box-sizing:content-box !important;box-sizing:content-box !important}:host(.ion-color) ::slotted(ion-tab-button){--background-focused:var(--ion-color-shade);--color-selected:var(--ion-color-contrast)}:host(.ion-color) ::slotted(.tab-selected){color:var(--ion-color-contrast)}:host(.ion-color),:host(.ion-color) ::slotted(ion-tab-button){color:rgba(var(--ion-color-contrast-rgb), 0.7)}:host(.ion-color),:host(.ion-color) ::slotted(ion-tab-button){background:var(--ion-color-base)}:host(.ion-color) ::slotted(ion-tab-button.ion-focused),:host(.tab-bar-translucent) ::slotted(ion-tab-button.ion-focused){background:var(--background-focused)}:host(.tab-bar-translucent) ::slotted(ion-tab-button){background:transparent}:host([slot=top]){padding-top:var(--ion-safe-area-top, 0);padding-bottom:0;border-top:0;border-bottom:var(--border)}:host(.tab-bar-hidden){display:none !important}:host{--background:var(--ion-tab-bar-background, var(--ion-color-step-50, var(--ion-background-color-step-50, #f7f7f7)));--background-focused:var(--ion-tab-bar-background-focused, #e0e0e0);--border:0.55px solid var(--ion-tab-bar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.2)))));--color:var(--ion-tab-bar-color, var(--ion-color-step-600, var(--ion-text-color-step-400, #666666)));--color-selected:var(--ion-tab-bar-color-selected, var(--ion-color-primary, #0054e9));height:50px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.tab-bar-translucent){--background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(210%) blur(20px);backdrop-filter:saturate(210%) blur(20px)}:host(.ion-color.tab-bar-translucent){background:rgba(var(--ion-color-base-rgb), 0.8)}:host(.tab-bar-translucent) ::slotted(ion-tab-button.ion-focused){background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.6)}}\";\n\nconst tabBarMdCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:auto;padding-right:var(--ion-safe-area-right);padding-bottom:var(--ion-safe-area-bottom, 0);padding-left:var(--ion-safe-area-left);border-top:var(--border);background:var(--background);color:var(--color);text-align:center;contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:10;-webkit-box-sizing:content-box !important;box-sizing:content-box !important}:host(.ion-color) ::slotted(ion-tab-button){--background-focused:var(--ion-color-shade);--color-selected:var(--ion-color-contrast)}:host(.ion-color) ::slotted(.tab-selected){color:var(--ion-color-contrast)}:host(.ion-color),:host(.ion-color) ::slotted(ion-tab-button){color:rgba(var(--ion-color-contrast-rgb), 0.7)}:host(.ion-color),:host(.ion-color) ::slotted(ion-tab-button){background:var(--ion-color-base)}:host(.ion-color) ::slotted(ion-tab-button.ion-focused),:host(.tab-bar-translucent) ::slotted(ion-tab-button.ion-focused){background:var(--background-focused)}:host(.tab-bar-translucent) ::slotted(ion-tab-button){background:transparent}:host([slot=top]){padding-top:var(--ion-safe-area-top, 0);padding-bottom:0;border-top:0;border-bottom:var(--border)}:host(.tab-bar-hidden){display:none !important}:host{--background:var(--ion-tab-bar-background, var(--ion-background-color, #fff));--background-focused:var(--ion-tab-bar-background-focused, #e0e0e0);--border:1px solid var(--ion-tab-bar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.07)))));--color:var(--ion-tab-bar-color, var(--ion-color-step-650, var(--ion-text-color-step-350, #595959)));--color-selected:var(--ion-tab-bar-color-selected, var(--ion-color-primary, #0054e9));height:56px}\";\n\nconst TabBar = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionTabBarChanged = createEvent(this, \"ionTabBarChanged\", 7);\n        this.ionTabBarLoaded = createEvent(this, \"ionTabBarLoaded\", 7);\n        this.keyboardCtrl = null;\n        this.keyboardVisible = false;\n        /**\n         * If `true`, the tab bar will be translucent.\n         * Only applies when the mode is `\"ios\"` and the device supports\n         * [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).\n         */\n        this.translucent = false;\n    }\n    selectedTabChanged() {\n        if (this.selectedTab !== undefined) {\n            this.ionTabBarChanged.emit({\n                tab: this.selectedTab,\n            });\n        }\n    }\n    componentWillLoad() {\n        this.selectedTabChanged();\n    }\n    async connectedCallback() {\n        this.keyboardCtrl = await createKeyboardController(async (keyboardOpen, waitForResize) => {\n            /**\n             * If the keyboard is hiding, then we need to wait\n             * for the webview to resize. Otherwise, the tab bar\n             * will flicker before the webview resizes.\n             */\n            if (keyboardOpen === false && waitForResize !== undefined) {\n                await waitForResize;\n            }\n            this.keyboardVisible = keyboardOpen; // trigger re-render by updating state\n        });\n    }\n    disconnectedCallback() {\n        if (this.keyboardCtrl) {\n            this.keyboardCtrl.destroy();\n        }\n    }\n    componentDidLoad() {\n        this.ionTabBarLoaded.emit();\n    }\n    render() {\n        const { color, translucent, keyboardVisible } = this;\n        const mode = getIonMode(this);\n        const shouldHide = keyboardVisible && this.el.getAttribute('slot') !== 'top';\n        return (h(Host, { key: '275dc6c1b30f6928ce9039b2f445208bb3500ddc', role: \"tablist\", \"aria-hidden\": shouldHide ? 'true' : null, class: createColorClasses(color, {\n                [mode]: true,\n                'tab-bar-translucent': translucent,\n                'tab-bar-hidden': shouldHide,\n            }) }, h(\"slot\", { key: 'ceac20128d75c6a4a0f445f2df8deb8cc71fc4da' })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"selectedTab\": [\"selectedTabChanged\"]\n    }; }\n};\nTabBar.style = {\n    ios: tabBarIosCss,\n    md: tabBarMdCss\n};\n\nconst tabButtonIosCss = \":host{--ripple-color:var(--color-selected);--background-focused-opacity:1;-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;height:100%;outline:none;background:var(--background);color:var(--color)}.button-native{border-radius:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;height:100%;border:0;outline:none;background:transparent;text-decoration:none;cursor:pointer;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-user-drag:none}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;height:100%;z-index:1}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){a:hover{color:var(--color-selected)}}:host(.tab-selected){color:var(--color-selected)}:host(.tab-hidden){display:none !important}:host(.tab-disabled){pointer-events:none;opacity:0.4}::slotted(ion-label),::slotted(ion-icon){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-label){-ms-flex-order:0;order:0}::slotted(ion-icon){-ms-flex-order:-1;order:-1;height:1em}:host(.tab-has-label-only) ::slotted(ion-label){white-space:normal}::slotted(ion-badge){-webkit-box-sizing:border-box;box-sizing:border-box;position:absolute;z-index:1}:host(.tab-layout-icon-start){-ms-flex-direction:row;flex-direction:row}:host(.tab-layout-icon-end){-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.tab-layout-icon-bottom){-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.tab-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.tab-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color)}:host{--padding-top:0;--padding-end:2px;--padding-bottom:0;--padding-start:2px;max-width:240px;font-size:10px}::slotted(ion-badge){-webkit-padding-start:6px;padding-inline-start:6px;-webkit-padding-end:6px;padding-inline-end:6px;padding-top:1px;padding-bottom:1px;top:4px;height:auto;font-size:12px;line-height:16px}::slotted(ion-badge){inset-inline-start:calc(50% + 6px)}::slotted(ion-icon){margin-top:2px;margin-bottom:2px;font-size:24px}::slotted(ion-icon::before){vertical-align:top}::slotted(ion-label){margin-top:0;margin-bottom:1px;min-height:11px;font-weight:500}:host(.tab-has-label-only) ::slotted(ion-label){margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:12px;font-size:14px;line-height:1.1}:host(.tab-layout-icon-end) ::slotted(ion-label),:host(.tab-layout-icon-start) ::slotted(ion-label),:host(.tab-layout-icon-hide) ::slotted(ion-label){margin-top:2px;margin-bottom:2px;font-size:14px;line-height:1.1}:host(.tab-layout-icon-end) ::slotted(ion-icon),:host(.tab-layout-icon-start) ::slotted(ion-icon){min-width:24px;height:26px;margin-top:2px;margin-bottom:1px;font-size:24px}:host(.tab-layout-icon-bottom) ::slotted(ion-badge){inset-inline-start:calc(50% + 12px)}:host(.tab-layout-icon-bottom) ::slotted(ion-icon){margin-top:0;margin-bottom:1px}:host(.tab-layout-icon-bottom) ::slotted(ion-label){margin-top:4px}:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){top:10px}:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){inset-inline-start:calc(50% + 35px)}:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){top:10px}:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){inset-inline-start:calc(50% + 30px)}:host(.tab-layout-label-hide) ::slotted(ion-badge),:host(.tab-has-icon-only) ::slotted(ion-badge){top:10px}:host(.tab-layout-label-hide) ::slotted(ion-icon){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}:host(.tab-layout-label-hide) ::slotted(ion-icon),:host(.tab-has-icon-only) ::slotted(ion-icon){font-size:30px}\";\n\nconst tabButtonMdCss = \":host{--ripple-color:var(--color-selected);--background-focused-opacity:1;-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;height:100%;outline:none;background:var(--background);color:var(--color)}.button-native{border-radius:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;height:100%;border:0;outline:none;background:transparent;text-decoration:none;cursor:pointer;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-user-drag:none}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;height:100%;z-index:1}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){a:hover{color:var(--color-selected)}}:host(.tab-selected){color:var(--color-selected)}:host(.tab-hidden){display:none !important}:host(.tab-disabled){pointer-events:none;opacity:0.4}::slotted(ion-label),::slotted(ion-icon){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-label){-ms-flex-order:0;order:0}::slotted(ion-icon){-ms-flex-order:-1;order:-1;height:1em}:host(.tab-has-label-only) ::slotted(ion-label){white-space:normal}::slotted(ion-badge){-webkit-box-sizing:border-box;box-sizing:border-box;position:absolute;z-index:1}:host(.tab-layout-icon-start){-ms-flex-direction:row;flex-direction:row}:host(.tab-layout-icon-end){-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.tab-layout-icon-bottom){-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.tab-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.tab-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color)}:host{--padding-top:0;--padding-end:12px;--padding-bottom:0;--padding-start:12px;max-width:168px;font-size:12px;font-weight:normal;letter-spacing:0.03em}::slotted(ion-label){margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;text-transform:none}::slotted(ion-icon){margin-left:0;margin-right:0;margin-top:16px;margin-bottom:16px;-webkit-transform-origin:center center;transform-origin:center center;font-size:22px}:host-context([dir=rtl]) ::slotted(ion-icon){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}[dir=rtl] ::slotted(ion-icon){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}@supports selector(:dir(rtl)){::slotted(ion-icon):dir(rtl){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}}::slotted(ion-badge){border-radius:8px;-webkit-padding-start:2px;padding-inline-start:2px;-webkit-padding-end:2px;padding-inline-end:2px;padding-top:3px;padding-bottom:2px;top:8px;min-width:12px;font-size:8px;font-weight:normal}::slotted(ion-badge){inset-inline-start:calc(50% + 6px)}::slotted(ion-badge:empty){display:block;min-width:8px;height:8px}:host(.tab-layout-icon-top) ::slotted(ion-icon){margin-top:6px;margin-bottom:2px}:host(.tab-layout-icon-top) ::slotted(ion-label){margin-top:0;margin-bottom:6px}:host(.tab-layout-icon-bottom) ::slotted(ion-badge){top:8px}:host(.tab-layout-icon-bottom) ::slotted(ion-badge){inset-inline-start:70%}:host(.tab-layout-icon-bottom) ::slotted(ion-icon){margin-top:0;margin-bottom:6px}:host(.tab-layout-icon-bottom) ::slotted(ion-label){margin-top:6px;margin-bottom:0}:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){top:16px}:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){inset-inline-start:80%}:host(.tab-layout-icon-start) ::slotted(ion-icon){-webkit-margin-end:6px;margin-inline-end:6px}:host(.tab-layout-icon-end) ::slotted(ion-icon){-webkit-margin-start:6px;margin-inline-start:6px}:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){top:16px}:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){inset-inline-start:70%}:host(.tab-layout-icon-hide) ::slotted(ion-label),:host(.tab-has-label-only) ::slotted(ion-label){margin-top:0;margin-bottom:0}:host(.tab-layout-label-hide) ::slotted(ion-badge),:host(.tab-has-icon-only) ::slotted(ion-badge){top:16px}:host(.tab-layout-label-hide) ::slotted(ion-icon),:host(.tab-has-icon-only) ::slotted(ion-icon){margin-top:0;margin-bottom:0;font-size:24px}\";\n\nconst TabButton = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionTabButtonClick = createEvent(this, \"ionTabButtonClick\", 7);\n        this.inheritedAttributes = {};\n        /**\n         * If `true`, the user cannot interact with the tab button.\n         */\n        this.disabled = false;\n        /**\n         * The selected tab component\n         */\n        this.selected = false;\n        this.onKeyUp = (ev) => {\n            if (ev.key === 'Enter' || ev.key === ' ') {\n                this.selectTab(ev);\n            }\n        };\n        this.onClick = (ev) => {\n            this.selectTab(ev);\n        };\n    }\n    onTabBarChanged(ev) {\n        const dispatchedFrom = ev.target;\n        const parent = this.el.parentElement;\n        if (ev.composedPath().includes(parent) || (dispatchedFrom === null || dispatchedFrom === void 0 ? void 0 : dispatchedFrom.contains(this.el))) {\n            this.selected = this.tab === ev.detail.tab;\n        }\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = Object.assign({}, inheritAttributes(this.el, ['aria-label']));\n        if (this.layout === undefined) {\n            this.layout = config.get('tabButtonLayout', 'icon-top');\n        }\n    }\n    selectTab(ev) {\n        if (this.tab !== undefined) {\n            if (!this.disabled) {\n                this.ionTabButtonClick.emit({\n                    tab: this.tab,\n                    href: this.href,\n                    selected: this.selected,\n                });\n            }\n            ev.preventDefault();\n        }\n    }\n    get hasLabel() {\n        return !!this.el.querySelector('ion-label');\n    }\n    get hasIcon() {\n        return !!this.el.querySelector('ion-icon');\n    }\n    render() {\n        const { disabled, hasIcon, hasLabel, href, rel, target, layout, selected, tab, inheritedAttributes } = this;\n        const mode = getIonMode(this);\n        const attrs = {\n            download: this.download,\n            href,\n            rel,\n            target,\n        };\n        return (h(Host, { key: 'ce9d29ced0c781d6b2fa62cd5feb801c11fc42e8', onClick: this.onClick, onKeyup: this.onKeyUp, id: tab !== undefined ? `tab-button-${tab}` : null, class: {\n                [mode]: true,\n                'tab-selected': selected,\n                'tab-disabled': disabled,\n                'tab-has-label': hasLabel,\n                'tab-has-icon': hasIcon,\n                'tab-has-label-only': hasLabel && !hasIcon,\n                'tab-has-icon-only': hasIcon && !hasLabel,\n                [`tab-layout-${layout}`]: true,\n                'ion-activatable': true,\n                'ion-selectable': true,\n                'ion-focusable': true,\n            } }, h(\"a\", Object.assign({ key: '01cb0ed2e77c5c1a8abd48da1bb07ac1b305d0b6' }, attrs, { class: \"button-native\", part: \"native\", role: \"tab\", \"aria-selected\": selected ? 'true' : null, \"aria-disabled\": disabled ? 'true' : null, tabindex: disabled ? '-1' : undefined }, inheritedAttributes), h(\"span\", { key: 'd0240c05f42217cfb186b86ff8a0c9cd70b9c8df', class: \"button-inner\" }, h(\"slot\", { key: '0a20b84925037dbaa8bb4a495b813d3f7c2e58ac' })), mode === 'md' && h(\"ion-ripple-effect\", { key: '4c92c27178cdac89d69cffef8d2c39c3644914e8', type: \"unbounded\" }))));\n    }\n    get el() { return getElement(this); }\n};\nTabButton.style = {\n    ios: tabButtonIosCss,\n    md: tabButtonMdCss\n};\n\nexport { TabBar as ion_tab_bar, TabButton as ion_tab_button };\n"], "names": ["r", "registerInstance", "d", "createEvent", "e", "getIonMode", "h", "j", "Host", "k", "getElement", "l", "config", "c", "createKeyboardController", "createColorClasses", "b", "inheritAttributes", "tabBarIosCss", "tabBarMdCss", "TabBar", "constructor", "hostRef", "ionTabBarChanged", "ionTabBarLoaded", "keyboardCtrl", "keyboardVisible", "translucent", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedTab", "undefined", "emit", "tab", "componentWillLoad", "connectedCallback", "_this", "_asyncToGenerator", "_ref", "keyboardOpen", "waitForResize", "_x", "_x2", "apply", "arguments", "disconnectedCallback", "destroy", "componentDidLoad", "render", "color", "mode", "shouldHide", "el", "getAttribute", "key", "role", "class", "watchers", "style", "ios", "md", "tabButtonIosCss", "tabButtonMdCss", "TabButton", "ionTabButtonClick", "inheritedAttributes", "disabled", "selected", "onKeyUp", "ev", "selectTab", "onClick", "onTabBarChanged", "dispatchedFrom", "target", "parent", "parentElement", "<PERSON><PERSON><PERSON>", "includes", "contains", "detail", "Object", "assign", "layout", "get", "href", "preventDefault", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "hasIcon", "rel", "attrs", "download", "onKeyup", "id", "part", "tabindex", "type", "ion_tab_bar", "ion_tab_button"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}