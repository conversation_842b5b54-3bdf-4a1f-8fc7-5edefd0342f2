"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[4630],{4630:(ln,ge,ee)=>{ee.d(ge,{c:()=>Ae});var ye=ee(7837);const ne=typeof window<"u"?window:void 0;let O;typeof document<"u"&&document;const U=(d,a,p)=>{const S=a.startsWith("animation")?(d=>(void 0===O&&(O=void 0===d.style.animationName&&void 0!==d.style.webkitAnimationName?"-webkit-":""),O))(d):"";d.style.setProperty(S+a,p)},x=(d=[],a)=>{if(void 0!==a){const p=Array.isArray(a)?a:[a];return[...d,...p]}return d},Ae=d=>{let a,p,S,z,V,M,o,b,F,C,t,i=[],N=[],B=[],y=!1,G={},H=[],J=[],Q={},A=0,P=!1,T=!1,v=!0,w=!1,_=!0,D=!1;const te=d,X=[],W=[],K=[],g=[],c=[],re=[],se=[],oe=[],ie=[],fe=[],h=[],be="function"==typeof AnimationEffect||void 0!==ne&&"function"==typeof ne.AnimationEffect,u="function"==typeof Element&&"function"==typeof Element.prototype.animate&&be,ae=()=>h,ce=(e,n)=>{const r=n.findIndex(s=>s.c===e);r>-1&&n.splice(r,1)},Y=(e,n)=>((n?.oneTimeCallback?W:X).push({c:e,o:n}),t),le=()=>{u&&(h.forEach(e=>{e.cancel()}),h.length=0)},_e=()=>{re.forEach(e=>{e?.parentNode&&e.parentNode.removeChild(e)}),re.length=0},Z=()=>void 0!==V?V:o?o.getFill():"both",I=()=>void 0!==b?b:void 0!==M?M:o?o.getDirection():"normal",$=()=>P?"linear":void 0!==S?S:o?o.getEasing():"linear",k=()=>T?0:void 0!==F?F:void 0!==p?p:o?o.getDuration():0,j=()=>void 0!==z?z:o?o.getIterations():1,q=()=>void 0!==C?C:void 0!==a?a:o?o.getDelay():0,L=()=>{0!==A&&(A--,0===A&&((()=>{ie.forEach(f=>f()),fe.forEach(f=>f());const e=v?1:0,n=H,r=J,s=Q;g.forEach(f=>{const m=f.classList;n.forEach(E=>m.add(E)),r.forEach(E=>m.remove(E));for(const E in s)s.hasOwnProperty(E)&&U(f,E,s[E])}),F=void 0,b=void 0,C=void 0,X.forEach(f=>f.c(e,t)),W.forEach(f=>f.c(e,t)),W.length=0,_=!0,v&&(w=!0),v=!0})(),o&&o.animationFinish()))},ue=()=>{(()=>{se.forEach(s=>s()),oe.forEach(s=>s());const e=N,n=B,r=G;g.forEach(s=>{const f=s.classList;e.forEach(m=>f.add(m)),n.forEach(m=>f.remove(m));for(const m in r)r.hasOwnProperty(m)&&U(s,m,r[m])})})(),i.length>0&&u&&(g.forEach(e=>{const n=e.animate(i,{id:te,delay:q(),duration:k(),easing:$(),iterations:j(),fill:Z(),direction:I()});n.pause(),h.push(n)}),h.length>0&&(h[0].onfinish=()=>{L()})),y=!0},R=e=>{e=Math.min(Math.max(e,0),.9999),u&&h.forEach(n=>{n.currentTime=n.effect.getComputedTiming().delay+k()*e,n.pause()})},de=e=>{h.forEach(n=>{n.effect.updateTiming({delay:q(),duration:k(),easing:$(),iterations:j(),fill:Z(),direction:I()})}),void 0!==e&&R(e)},l=(e=!1,n=!0,r)=>(e&&c.forEach(s=>{s.update(e,n,r)}),u&&de(r),t),he=()=>{y&&(u?h.forEach(e=>{e.pause()}):g.forEach(e=>{U(e,"animation-play-state","paused")}),D=!0)},me=e=>new Promise(n=>{e?.sync&&(T=!0,Y(()=>T=!1,{oneTimeCallback:!0})),y||ue(),w&&(u&&(R(0),de()),w=!1),_&&(A=c.length+1,_=!1);const r=()=>{ce(s,W),n()},s=()=>{ce(r,K),n()};Y(s,{oneTimeCallback:!0}),(e=>{K.push({c:e,o:{oneTimeCallback:!0}})})(r),c.forEach(f=>{f.play()}),u?(h.forEach(e=>{e.play()}),(0===i.length||0===g.length)&&L()):L(),D=!1}),pe=(e,n)=>{const r=i[0];return void 0===r||void 0!==r.offset&&0!==r.offset?i=[{offset:0,[e]:n},...i]:r[e]=n,t};return t={parentAnimation:o,elements:g,childAnimations:c,id:te,animationFinish:L,from:pe,to:(e,n)=>{const r=i[i.length-1];return void 0===r||void 0!==r.offset&&1!==r.offset?i=[...i,{offset:1,[e]:n}]:r[e]=n,t},fromTo:(e,n,r)=>pe(e,n).to(e,r),parent:e=>(o=e,t),play:me,pause:()=>(c.forEach(e=>{e.pause()}),he(),t),stop:()=>{c.forEach(e=>{e.stop()}),y&&(le(),y=!1),P=!1,T=!1,_=!0,b=void 0,F=void 0,C=void 0,A=0,w=!1,v=!0,D=!1,K.forEach(e=>e.c(0,t)),K.length=0},destroy:e=>(c.forEach(n=>{n.destroy(e)}),(e=>{le(),e&&_e()})(e),g.length=0,c.length=0,i.length=0,X.length=0,W.length=0,y=!1,_=!0,t),keyframes:e=>{const n=i!==e;return i=e,n&&(e=>{u&&ae().forEach(n=>{const r=n.effect;if(r.setKeyframes)r.setKeyframes(e);else{const s=new KeyframeEffect(r.target,e,r.getTiming());n.effect=s}})})(i),t},addAnimation:e=>{if(null!=e)if(Array.isArray(e))for(const n of e)n.parent(t),c.push(n);else e.parent(t),c.push(e);return t},addElement:e=>{if(null!=e)if(1===e.nodeType)g.push(e);else if(e.length>=0)for(let n=0;n<e.length;n++)g.push(e[n]);else(0,ye.a)("createAnimation - Invalid addElement value.");return t},update:l,fill:e=>(V=e,l(!0),t),direction:e=>(M=e,l(!0),t),iterations:e=>(z=e,l(!0),t),duration:e=>(!u&&0===e&&(e=1),p=e,l(!0),t),easing:e=>(S=e,l(!0),t),delay:e=>(a=e,l(!0),t),getWebAnimations:ae,getKeyframes:()=>i,getFill:Z,getDirection:I,getDelay:q,getIterations:j,getEasing:$,getDuration:k,afterAddRead:e=>(ie.push(e),t),afterAddWrite:e=>(fe.push(e),t),afterClearStyles:(e=[])=>{for(const n of e)Q[n]="";return t},afterStyles:(e={})=>(Q=e,t),afterRemoveClass:e=>(J=x(J,e),t),afterAddClass:e=>(H=x(H,e),t),beforeAddRead:e=>(se.push(e),t),beforeAddWrite:e=>(oe.push(e),t),beforeClearStyles:(e=[])=>{for(const n of e)G[n]="";return t},beforeStyles:(e={})=>(G=e,t),beforeRemoveClass:e=>(B=x(B,e),t),beforeAddClass:e=>(N=x(N,e),t),onFinish:Y,isRunning:()=>0!==A&&!D,progressStart:(e=!1,n)=>(c.forEach(r=>{r.progressStart(e,n)}),he(),P=e,y||ue(),l(!1,!0,n),t),progressStep:e=>(c.forEach(n=>{n.progressStep(e)}),R(e),t),progressEnd:(e,n,r)=>(P=!1,c.forEach(s=>{s.progressEnd(e,n,r)}),void 0!==r&&(F=r),w=!1,v=!0,0===e?(b="reverse"===I()?"normal":"reverse","reverse"===b&&(v=!1),u?(l(),R(1-n)):(C=(1-n)*k()*-1,l(!1,!1))):1===e&&(u?(l(),R(n)):(C=n*k()*-1,l(!1,!1))),void 0!==e&&!o&&me(),t)}}}}]);