{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-back-button_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACyH;AACtD;AAC2B;AAClB;AAE5E,MAAMqB,gBAAgB,GAAG,o0IAAo0I;AAE71I,MAAMC,eAAe,GAAG,guJAAguJ;AAExvJ,MAAMC,UAAU,GAAG,MAAM;EACrBC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACjBzB,qDAAgB,CAAC,IAAI,EAAEwB,OAAO,CAAC;IAC/B,IAAI,CAACE,mBAAmB,GAAG,CAAC,CAAC;IAC7B;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB,IAAI,CAACC,OAAO;MAAA,IAAAC,IAAA,GAAAC,oMAAA,CAAG,WAAOC,EAAE,EAAK;QACzB,MAAMC,GAAG,GAAGR,KAAI,CAACS,EAAE,CAACC,OAAO,CAAC,SAAS,CAAC;QACtCH,EAAE,CAACI,cAAc,CAAC,CAAC;QACnB,IAAIH,GAAG,WAAWA,GAAG,CAACI,SAAS,CAAC,CAAC,CAAC,EAAE;UAChC,OAAOJ,GAAG,CAACK,GAAG,CAAC;YAAEC,gBAAgB,EAAEd,KAAI,CAACe,eAAe;YAAEC,UAAU,EAAE;UAAK,CAAC,CAAC;QAChF;QACA,OAAO5B,qDAAO,CAACY,KAAI,CAACiB,WAAW,EAAEV,EAAE,EAAE,MAAM,EAAEP,KAAI,CAACe,eAAe,CAAC;MACtE,CAAC;MAAA,iBAAAG,EAAA;QAAA,OAAAb,IAAA,CAAAc,KAAA,OAAAC,SAAA;MAAA;IAAA;EACL;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACpB,mBAAmB,GAAGf,uDAAqB,CAAC,IAAI,CAACuB,EAAE,CAAC;IACzD,IAAI,IAAI,CAACQ,WAAW,KAAKK,SAAS,EAAE;MAChC,IAAI,CAACL,WAAW,GAAGxC,iDAAM,CAAC8C,GAAG,CAAC,uBAAuB,CAAC;IAC1D;EACJ;EACA,IAAIC,cAAcA,CAAA,EAAG;IACjB,MAAMC,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAIA,IAAI,IAAI,IAAI,EAAE;MACd;MACA,OAAOA,IAAI;IACf;IACA,IAAI9C,qDAAU,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE;MAC5B;MACA,OAAOF,iDAAM,CAAC8C,GAAG,CAAC,gBAAgB,EAAE/B,iDAAW,CAAC;IACpD;IACA;IACA,OAAOf,iDAAM,CAAC8C,GAAG,CAAC,gBAAgB,EAAE7B,iDAAc,CAAC;EACvD;EACA,IAAIgC,cAAcA,CAAA,EAAG;IACjB,MAAMC,qBAAqB,GAAGhD,qDAAU,CAAC,IAAI,CAAC,KAAK,KAAK,GAAG,MAAM,GAAG,IAAI;IACxE,OAAO,IAAI,CAACiD,IAAI,IAAI,IAAI,GAAG,IAAI,CAACA,IAAI,GAAGnD,iDAAM,CAAC8C,GAAG,CAAC,gBAAgB,EAAEI,qBAAqB,CAAC;EAC9F;EACA,IAAIE,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACL,cAAc,IAAI,CAAC,IAAI,CAACE,cAAc;EACtD;EACA,IAAII,UAAUA,CAAA,EAAG;IACb;IACA;IACA,IAAI,IAAI,CAACD,WAAW,EAAE;MAClB,OAAO,WAAW;IACtB;IACA,OAAO,SAAS;EACpB;EACAE,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEC,KAAK;MAAEf,WAAW;MAAEf,QAAQ;MAAEC,IAAI;MAAE0B,WAAW;MAAEL,cAAc;MAAEE,cAAc;MAAED,IAAI;MAAExB;IAAqB,CAAC,GAAG,IAAI;IAC5H,MAAMgC,cAAc,GAAGhB,WAAW,KAAKK,SAAS;IAChD,MAAMY,IAAI,GAAGvD,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAMwD,SAAS,GAAGlC,mBAAmB,CAAC,YAAY,CAAC,IAAIyB,cAAc,IAAI,MAAM;IAC/E,OAAQ9C,qDAAC,CAACE,iDAAI,EAAE;MAAEsD,GAAG,EAAE,0CAA0C;MAAEhC,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEiC,KAAK,EAAE/C,qDAAkB,CAAC0C,KAAK,EAAE;QACnH,CAACE,IAAI,GAAG,IAAI;QACZI,MAAM,EAAE,IAAI;QAAE;QACd,sBAAsB,EAAEpC,QAAQ;QAChC,2BAA2B,EAAE2B,WAAW;QACxC,YAAY,EAAEtC,qDAAW,CAAC,aAAa,EAAE,IAAI,CAACkB,EAAE,CAAC;QACjD,kBAAkB,EAAElB,qDAAW,CAAC,oBAAoB,EAAE,IAAI,CAACkB,EAAE,CAAC;QAC9D,iBAAiB,EAAE,IAAI;QACvB,eAAe,EAAE,IAAI;QACrB,kBAAkB,EAAEwB;MACxB,CAAC;IAAE,CAAC,EAAErD,qDAAC,CAAC,QAAQ,EAAE;MAAEwD,GAAG,EAAE,0CAA0C;MAAEjC,IAAI,EAAEA,IAAI;MAAED,QAAQ,EAAEA,QAAQ;MAAEmC,KAAK,EAAE,eAAe;MAAEE,IAAI,EAAE,QAAQ;MAAE,YAAY,EAAEJ;IAAU,CAAC,EAAEvD,qDAAC,CAAC,MAAM,EAAE;MAAEwD,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAe,CAAC,EAAEb,cAAc,IAAK5C,qDAAC,CAAC,UAAU,EAAE;MAAEwD,GAAG,EAAE,0CAA0C;MAAEG,IAAI,EAAE,MAAM;MAAEd,IAAI,EAAED,cAAc;MAAE,aAAa,EAAE,MAAM;MAAEgB,IAAI,EAAE,KAAK;MAAE,UAAU,EAAEf,IAAI,KAAKH;IAAU,CAAC,CAAE,EAAEI,cAAc,IAAK9C,qDAAC,CAAC,MAAM,EAAE;MAAEwD,GAAG,EAAE,0CAA0C;MAAEG,IAAI,EAAE,MAAM;MAAE,aAAa,EAAE,MAAM;MAAEF,KAAK,EAAE;IAAc,CAAC,EAAEX,cAAc,CAAE,CAAC,EAAEQ,IAAI,KAAK,IAAI,IAAItD,qDAAC,CAAC,mBAAmB,EAAE;MAAEwD,GAAG,EAAE,0CAA0C;MAAEjC,IAAI,EAAE,IAAI,CAAC2B;IAAW,CAAC,CAAC,CAAC,CAAC;EAC3tB;EACA,IAAIrB,EAAEA,CAAA,EAAG;IAAE,OAAOzB,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDa,UAAU,CAAC4C,KAAK,GAAG;EACfC,GAAG,EAAE/C,gBAAgB;EACrBgD,EAAE,EAAE/C;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-back-button.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, l as config, e as getIonMode, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { i as inheritAriaAttributes } from './helpers-1O4D2b7y.js';\nimport { o as openURL, c as createColorClasses, h as hostContext } from './theme-DiVJyqlX.js';\nimport { c as chevronBack, a as arrowBackSharp } from './index-BLV6ykCk.js';\n\nconst backButtonIosCss = \":host{--background:transparent;--color-focused:currentColor;--color-hover:currentColor;--icon-margin-top:0;--icon-margin-bottom:0;--icon-padding-top:0;--icon-padding-end:0;--icon-padding-bottom:0;--icon-padding-start:0;--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--min-width:auto;--min-height:auto;--padding-top:0;--padding-end:0;--padding-bottom:0;--padding-start:0;--opacity:1;--ripple-color:currentColor;--transition:background-color, opacity 100ms linear;display:none;min-width:var(--min-width);min-height:var(--min-height);color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-font-kerning:none;font-kerning:none}ion-ripple-effect{color:var(--ripple-color)}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.show-back-button){display:block}:host(.back-button-disabled){cursor:default;opacity:0.5;pointer-events:none}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:block;position:relative;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;opacity:var(--opacity);overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}ion-icon{-webkit-padding-start:var(--icon-padding-start);padding-inline-start:var(--icon-padding-start);-webkit-padding-end:var(--icon-padding-end);padding-inline-end:var(--icon-padding-end);padding-top:var(--icon-padding-top);padding-bottom:var(--icon-padding-bottom);-webkit-margin-start:var(--icon-margin-start);margin-inline-start:var(--icon-margin-start);-webkit-margin-end:var(--icon-margin-end);margin-inline-end:var(--icon-margin-end);margin-top:var(--icon-margin-top);margin-bottom:var(--icon-margin-bottom);display:inherit;font-size:var(--icon-font-size);font-weight:var(--icon-font-weight);pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-color.ion-focused) .button-native{color:var(--ion-color-base)}@media (any-hover: hover){:host(.ion-color:hover) .button-native{color:var(--ion-color-base)}}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-hover:transparent;--background-hover-opacity:1;--background-focused:currentColor;--background-focused-opacity:.1;--border-radius:4px;--color:var(--ion-color-primary, #0054e9);--icon-margin-end:1px;--icon-margin-start:-4px;--icon-font-size:1.6em;--min-height:32px;font-size:clamp(17px, 1.0625rem, 21.998px)}.button-native{-webkit-transform:translateZ(0);transform:translateZ(0);overflow:visible;z-index:99}:host(.ion-activated) .button-native{opacity:0.4}@media (any-hover: hover){:host(:hover){opacity:0.6}}\";\n\nconst backButtonMdCss = \":host{--background:transparent;--color-focused:currentColor;--color-hover:currentColor;--icon-margin-top:0;--icon-margin-bottom:0;--icon-padding-top:0;--icon-padding-end:0;--icon-padding-bottom:0;--icon-padding-start:0;--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--min-width:auto;--min-height:auto;--padding-top:0;--padding-end:0;--padding-bottom:0;--padding-start:0;--opacity:1;--ripple-color:currentColor;--transition:background-color, opacity 100ms linear;display:none;min-width:var(--min-width);min-height:var(--min-height);color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-font-kerning:none;font-kerning:none}ion-ripple-effect{color:var(--ripple-color)}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.show-back-button){display:block}:host(.back-button-disabled){cursor:default;opacity:0.5;pointer-events:none}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:block;position:relative;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;opacity:var(--opacity);overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}ion-icon{-webkit-padding-start:var(--icon-padding-start);padding-inline-start:var(--icon-padding-start);-webkit-padding-end:var(--icon-padding-end);padding-inline-end:var(--icon-padding-end);padding-top:var(--icon-padding-top);padding-bottom:var(--icon-padding-bottom);-webkit-margin-start:var(--icon-margin-start);margin-inline-start:var(--icon-margin-start);-webkit-margin-end:var(--icon-margin-end);margin-inline-end:var(--icon-margin-end);margin-top:var(--icon-margin-top);margin-bottom:var(--icon-margin-bottom);display:inherit;font-size:var(--icon-font-size);font-weight:var(--icon-font-weight);pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-color.ion-focused) .button-native{color:var(--ion-color-base)}@media (any-hover: hover){:host(.ion-color:hover) .button-native{color:var(--ion-color-base)}}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--border-radius:4px;--background-focused:currentColor;--background-focused-opacity:.12;--background-hover:currentColor;--background-hover-opacity:0.04;--color:currentColor;--icon-margin-end:0;--icon-margin-start:0;--icon-font-size:1.5rem;--icon-font-weight:normal;--min-height:32px;--min-width:44px;--padding-start:12px;--padding-end:12px;font-size:0.875rem;font-weight:500;text-transform:uppercase}:host(.back-button-has-icon-only){--border-radius:50%;min-width:48px;min-height:48px;aspect-ratio:1/1}.button-native{-webkit-box-shadow:none;box-shadow:none}.button-text{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:0;padding-bottom:0}ion-icon{line-height:0.67;text-align:start}@media (any-hover: hover){:host(.ion-color:hover) .button-native::after{background:var(--ion-color-base)}}:host(.ion-color.ion-focused) .button-native::after{background:var(--ion-color-base)}\";\n\nconst BackButton = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.inheritedAttributes = {};\n        /**\n         * If `true`, the user cannot interact with the button.\n         */\n        this.disabled = false;\n        /**\n         * The type of the button.\n         */\n        this.type = 'button';\n        this.onClick = async (ev) => {\n            const nav = this.el.closest('ion-nav');\n            ev.preventDefault();\n            if (nav && (await nav.canGoBack())) {\n                return nav.pop({ animationBuilder: this.routerAnimation, skipIfBusy: true });\n            }\n            return openURL(this.defaultHref, ev, 'back', this.routerAnimation);\n        };\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n        if (this.defaultHref === undefined) {\n            this.defaultHref = config.get('backButtonDefaultHref');\n        }\n    }\n    get backButtonIcon() {\n        const icon = this.icon;\n        if (icon != null) {\n            // icon is set on the component or by the config\n            return icon;\n        }\n        if (getIonMode(this) === 'ios') {\n            // default ios back button icon\n            return config.get('backButtonIcon', chevronBack);\n        }\n        // default md back button icon\n        return config.get('backButtonIcon', arrowBackSharp);\n    }\n    get backButtonText() {\n        const defaultBackButtonText = getIonMode(this) === 'ios' ? 'Back' : null;\n        return this.text != null ? this.text : config.get('backButtonText', defaultBackButtonText);\n    }\n    get hasIconOnly() {\n        return this.backButtonIcon && !this.backButtonText;\n    }\n    get rippleType() {\n        // If the button only has an icon we use the unbounded\n        // \"circular\" ripple effect\n        if (this.hasIconOnly) {\n            return 'unbounded';\n        }\n        return 'bounded';\n    }\n    render() {\n        const { color, defaultHref, disabled, type, hasIconOnly, backButtonIcon, backButtonText, icon, inheritedAttributes, } = this;\n        const showBackButton = defaultHref !== undefined;\n        const mode = getIonMode(this);\n        const ariaLabel = inheritedAttributes['aria-label'] || backButtonText || 'back';\n        return (h(Host, { key: '5466624a10f1ab56f5469e6dc07080303880f2fe', onClick: this.onClick, class: createColorClasses(color, {\n                [mode]: true,\n                button: true, // ion-buttons target .button\n                'back-button-disabled': disabled,\n                'back-button-has-icon-only': hasIconOnly,\n                'in-toolbar': hostContext('ion-toolbar', this.el),\n                'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n                'ion-activatable': true,\n                'ion-focusable': true,\n                'show-back-button': showBackButton,\n            }) }, h(\"button\", { key: '63bc75ef0ad7cc9fb79e58217a3314b20acd73e3', type: type, disabled: disabled, class: \"button-native\", part: \"native\", \"aria-label\": ariaLabel }, h(\"span\", { key: '5d3eacbd11af2245c6e1151cab446a0d96559ad8', class: \"button-inner\" }, backButtonIcon && (h(\"ion-icon\", { key: '6439af0ae463764174e7d3207f02267811df666d', part: \"icon\", icon: backButtonIcon, \"aria-hidden\": \"true\", lazy: false, \"flip-rtl\": icon === undefined })), backButtonText && (h(\"span\", { key: '8ee89fb18dfdb5b75948a8b197ff4cdbc008742f', part: \"text\", \"aria-hidden\": \"true\", class: \"button-text\" }, backButtonText))), mode === 'md' && h(\"ion-ripple-effect\", { key: '63803a884998bc73bea5afe0b2a0a14e3fa4d6bf', type: this.rippleType }))));\n    }\n    get el() { return getElement(this); }\n};\nBackButton.style = {\n    ios: backButtonIosCss,\n    md: backButtonMdCss\n};\n\nexport { BackButton as ion_back_button };\n"], "names": ["r", "registerInstance", "l", "config", "e", "getIonMode", "h", "j", "Host", "k", "getElement", "i", "inheritAriaAttributes", "o", "openURL", "c", "createColorClasses", "hostContext", "chevronBack", "a", "arrowBackSharp", "backButtonIosCss", "backButtonMdCss", "BackButton", "constructor", "hostRef", "_this", "inheritedAttributes", "disabled", "type", "onClick", "_ref", "_asyncToGenerator", "ev", "nav", "el", "closest", "preventDefault", "canGoBack", "pop", "animationBuilder", "routerAnimation", "skipIfBusy", "defaultHref", "_x", "apply", "arguments", "componentWillLoad", "undefined", "get", "backButtonIcon", "icon", "backButtonText", "defaultBackButtonText", "text", "hasIconOnly", "rippleType", "render", "color", "showBackButton", "mode", "aria<PERSON><PERSON><PERSON>", "key", "class", "button", "part", "lazy", "style", "ios", "md", "ion_back_button"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}