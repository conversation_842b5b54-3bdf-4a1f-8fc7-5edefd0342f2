"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[1102],{1102:(v,l,s)=>{s.r(l),s.d(l,{ion_tab:()=>b,ion_tabs:()=>d});var n=s(467),i=s(2734),u=s(1653);s(1837);const b=(()=>{let t=class{constructor(e){(0,i.r)(this,e),this.loaded=!1,this.active=!1}componentWillLoad(){var e=this;return(0,n.A)(function*(){e.active&&(yield e.setActive())})()}setActive(){var e=this;return(0,n.A)(function*(){yield e.prepareLazyLoaded(),e.active=!0})()}changeActive(e){e&&this.prepareLazyLoaded()}prepareLazyLoaded(){if(!this.loaded&&null!=this.component){this.loaded=!0;try{return(0,u.a)(this.delegate,this.el,this.component,["ion-page"])}catch(e){(0,i.o)("[ion-tab] - Exception in prepareLazyLoaded:",e)}}return Promise.resolve(void 0)}render(){const{tab:e,active:a,component:o}=this;return(0,i.h)(i.j,{key:"dbad8fe9f1566277d14647626308eaf1601ab01f",role:"tabpanel","aria-hidden":a?null:"true","aria-labelledby":`tab-button-${e}`,class:{"ion-page":void 0===o,"tab-hidden":!a}},(0,i.h)("slot",{key:"3be64f4e7161f6769aaf8e4dcb5293fcaa09af45"}))}get el(){return(0,i.k)(this)}static get watchers(){return{active:["changeActive"]}}};return t.style=":host(.tab-hidden){display:none !important}",t})(),d=class{constructor(t){(0,i.r)(this,t),this.ionNavWillLoad=(0,i.d)(this,"ionNavWillLoad",7),this.ionTabsWillChange=(0,i.d)(this,"ionTabsWillChange",3),this.ionTabsDidChange=(0,i.d)(this,"ionTabsDidChange",3),this.transitioning=!1,this.useRouter=!1,this.onTabClicked=e=>{const{href:a,tab:o}=e.detail;if(this.useRouter&&void 0!==a){const c=document.querySelector("ion-router");c&&c.push(a)}else this.select(o)}}componentWillLoad(){var t=this;return(0,n.A)(function*(){if(t.useRouter||(t.useRouter=!(!t.el.querySelector("ion-router-outlet")&&!document.querySelector("ion-router")||t.el.closest("[no-router]"))),!t.useRouter){const e=t.tabs;e.length>0&&(yield t.select(e[0]))}t.ionNavWillLoad.emit()})()}componentWillRender(){const t=this.el.querySelector("ion-tab-bar");t&&(t.selectedTab=this.selectedTab?this.selectedTab.tab:void 0)}select(t){var e=this;return(0,n.A)(function*(){const a=r(e.tabs,t);return!!e.shouldSwitch(a)&&(yield e.setActive(a),yield e.notifyRouter(),e.tabSwitch(),!0)})()}getTab(t){var e=this;return(0,n.A)(function*(){return r(e.tabs,t)})()}getSelected(){return Promise.resolve(this.selectedTab?this.selectedTab.tab:void 0)}setRouteId(t){var e=this;return(0,n.A)(function*(){const a=r(e.tabs,t);return e.shouldSwitch(a)?(yield e.setActive(a),{changed:!0,element:e.selectedTab,markVisible:()=>e.tabSwitch()}):{changed:!1,element:e.selectedTab}})()}getRouteId(){var t=this;return(0,n.A)(function*(){var e;const a=null===(e=t.selectedTab)||void 0===e?void 0:e.tab;return void 0!==a?{id:a,element:t.selectedTab}:void 0})()}setActive(t){return this.transitioning?Promise.reject("transitioning already happening"):(this.transitioning=!0,this.leavingTab=this.selectedTab,this.selectedTab=t,this.ionTabsWillChange.emit({tab:t.tab}),t.active=!0,Promise.resolve())}tabSwitch(){const t=this.selectedTab,e=this.leavingTab;this.leavingTab=void 0,this.transitioning=!1,t&&e!==t&&(e&&(e.active=!1),this.ionTabsDidChange.emit({tab:t.tab}))}notifyRouter(){if(this.useRouter){const t=document.querySelector("ion-router");if(t)return t.navChanged("forward")}return Promise.resolve(!1)}shouldSwitch(t){return void 0!==t&&t!==this.selectedTab&&!this.transitioning}get tabs(){return Array.from(this.el.querySelectorAll("ion-tab"))}render(){return(0,i.h)(i.j,{key:"73ecd3294ca6c78ce6d8b6a7e5b6ccb11d84ada4",onIonTabButtonClick:this.onTabClicked},(0,i.h)("slot",{key:"09661b26f07a3069a58e76ea4dceb9a6acbf365d",name:"top"}),(0,i.h)("div",{key:"db50d59fad8f9b11873b695fc548f3cfe4aceb6a",class:"tabs-inner"},(0,i.h)("slot",{key:"02694dde2d8381f48fc06dd9e79798c4bd540ccd"})),(0,i.h)("slot",{key:"92c4661a5f3fa1c08c964fab7c422c1a2a03d3d8",name:"bottom"}))}get el(){return(0,i.k)(this)}},r=(t,e)=>{const a="string"==typeof e?t.find(o=>o.tab===e):e;return a||(0,i.o)(`[ion-tabs] - Tab with id: "${a}" does not exist`),a};d.style=":host{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:100%;height:100%;contain:layout size style;z-index:0}.tabs-inner{position:relative;-ms-flex:1;flex:1;contain:layout size style}"}}]);