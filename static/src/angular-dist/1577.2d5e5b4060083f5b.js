"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[1577],{1577:(h,a,n)=>{n.r(a),n.d(a,{ion_text:()=>c});var o=n(2734),u=n(4576);const c=(()=>{let _=class{constructor(s){(0,o.r)(this,s)}render(){const s=(0,o.e)(this);return(0,o.h)(o.j,{key:"361035eae7b92dc109794348d39bad2f596eb6be",class:(0,u.c)(this.color,{[s]:!0})},(0,o.h)("slot",{key:"c7b8835cf485ba9ecd73298f0529276ce1ea0852"}))}};return _.style=":host(.ion-color){color:var(--ion-color-base)}",_})()},4576:(h,a,n)=>{n.d(a,{c:()=>l,g:()=>_,h:()=>u,o:()=>p});var o=n(467);const u=(e,t)=>null!==t.closest(e),l=(e,t)=>"string"==typeof e&&e.length>0?Object.assign({"ion-color":!0,[`ion-color-${e}`]:!0},t):t,_=e=>{const t={};return(e=>void 0!==e?(Array.isArray(e)?e:e.split(" ")).filter(r=>null!=r).map(r=>r.trim()).filter(r=>""!==r):[])(e).forEach(r=>t[r]=!0),t},s=/^[a-z][a-z0-9+\-.]*:/,p=function(){var e=(0,o.A)(function*(t,r,d,f){if(null!=t&&"#"!==t[0]&&!s.test(t)){const i=document.querySelector("ion-router");if(i)return r?.preventDefault(),i.push(t,d,f)}return!1});return function(r,d,f,i){return e.apply(this,arguments)}}()}}]);