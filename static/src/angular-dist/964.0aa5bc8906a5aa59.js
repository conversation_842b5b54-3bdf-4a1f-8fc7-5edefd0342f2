"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[964],{964:(R,d,r)=>{r.r(d),r.d(d,{ion_ripple_effect:()=>u});var b=r(467),n=r(2734);const u=class{constructor(t){(0,n.r)(this,t),this.type="bounded"}addRipple(t,w){var i=this;return(0,b.A)(function*(){return new Promise(k=>{(0,n.f)(()=>{const s=i.el.getBoundingClientRect(),a=s.width,o=s.height,g=Math.sqrt(a*a+o*o),p=Math.max(o,a),A=i.unbounded?p:g+y,l=Math.floor(p*v),I=A/l;let m=t-s.left,f=w-s.top;i.unbounded&&(m=.5*a,f=.5*o);const C=m-.5*l,E=f-.5*l,x=.5*a-m,O=.5*o-f;(0,n.w)(()=>{const c=document.createElement("div");c.classList.add("ripple-effect");const e=c.style;e.top=E+"px",e.left=C+"px",e.width=e.height=l+"px",e.setProperty("--final-scale",`${I}`),e.setProperty("--translate-end",`${x}px, ${O}px`),(i.el.shadowRoot||i.el).appendChild(c),setTimeout(()=>{k(()=>{_(c)})},325)})})})})()}get unbounded(){return"unbounded"===this.type}render(){const t=(0,n.e)(this);return(0,n.h)(n.j,{key:"ae9d3b1ed6773a9b9bb2267129f7e9af23b6c9fc",role:"presentation",class:{[t]:!0,unbounded:this.unbounded}})}get el(){return(0,n.k)(this)}},_=t=>{t.classList.add("fade-out"),setTimeout(()=>{t.remove()},200)},y=10,v=.5;u.style=":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:strict;pointer-events:none}:host(.unbounded){contain:layout size style}.ripple-effect{border-radius:50%;position:absolute;background-color:currentColor;color:inherit;contain:strict;opacity:0;-webkit-animation:225ms rippleAnimation forwards, 75ms fadeInAnimation forwards;animation:225ms rippleAnimation forwards, 75ms fadeInAnimation forwards;will-change:transform, opacity;pointer-events:none}.fade-out{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1));-webkit-animation:150ms fadeOutAnimation forwards;animation:150ms fadeOutAnimation forwards}@-webkit-keyframes rippleAnimation{from{-webkit-animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);-webkit-transform:scale(1);transform:scale(1)}to{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1))}}@keyframes rippleAnimation{from{-webkit-animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);-webkit-transform:scale(1);transform:scale(1)}to{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1))}}@-webkit-keyframes fadeInAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0}to{opacity:0.16}}@keyframes fadeInAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0}to{opacity:0.16}}@-webkit-keyframes fadeOutAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0.16}to{opacity:0}}@keyframes fadeOutAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0.16}to{opacity:0}}"}}]);