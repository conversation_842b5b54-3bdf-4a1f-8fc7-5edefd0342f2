"use strict";
(self["webpackChunkdriver_evaluation_app"] = self["webpackChunkdriver_evaluation_app"] || []).push([["node_modules_ionic_core_dist_esm_ion-text_entry_js"],{

/***/ 198:
/*!*************************************************************!*\
  !*** ./node_modules/@ionic/core/dist/esm/ion-text.entry.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ion_text: () => (/* binding */ Text)
/* harmony export */ });
/* harmony import */ var _index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index-B_U9CtaY.js */ 4917);
/* harmony import */ var _theme_DiVJyqlX_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./theme-DiVJyqlX.js */ 247);
/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */


const textCss = ":host(.ion-color){color:var(--ion-color-base)}";
const Text = class {
  constructor(hostRef) {
    (0,_index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__.r)(this, hostRef);
  }
  render() {
    const mode = (0,_index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__.e)(this);
    return (0,_index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__.h)(_index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__.j, {
      key: '361035eae7b92dc109794348d39bad2f596eb6be',
      class: (0,_theme_DiVJyqlX_js__WEBPACK_IMPORTED_MODULE_1__.c)(this.color, {
        [mode]: true
      })
    }, (0,_index_B_U9CtaY_js__WEBPACK_IMPORTED_MODULE_0__.h)("slot", {
      key: 'c7b8835cf485ba9ecd73298f0529276ce1ea0852'
    }));
  }
};
Text.style = textCss;


/***/ })

}]);
//# sourceMappingURL=node_modules_ionic_core_dist_esm_ion-text_entry_js.js.map