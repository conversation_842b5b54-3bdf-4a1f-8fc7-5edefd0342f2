"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[5671],{5671:(E,_,e)=>{e.r(_),e.d(_,{startStatusTap:()=>i});var r=e(467),o=e(2734),s=e(4657),d=e(1837);const i=()=>{const n=window;n.addEventListener("statusTap",()=>{(0,o.f)(()=>{const a=document.elementFromPoint(n.innerWidth/2,n.innerHeight/2);if(!a)return;const t=(0,s.f)(a);t&&new Promise(u=>(0,d.c)(t,u)).then(()=>{(0,o.w)((0,r.A)(function*(){t.style.setProperty("--overflow","hidden"),yield(0,s.s)(t,300),t.style.removeProperty("--overflow")}))})})})}}}]);