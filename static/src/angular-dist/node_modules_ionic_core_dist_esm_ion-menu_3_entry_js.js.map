{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-menu_3_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACgL;AACtG;AAC0E;AACzE;AACA;AACiC;AAClD;AACsB;AACT;AAC1C;AACa;AACT;AAEjC,MAAM4C,UAAU,GAAG,kxGAAkxG;AAEryG,MAAMC,SAAS,GAAG,g0GAAg0G;AAEl1G,MAAMC,SAAS,GAAG,6BAA6B;AAC/C,MAAMC,QAAQ,GAAG,6BAA6B;AAC9C,MAAMC,gBAAgB,GAAG,gCAAgC;AACzD,MAAMC,eAAe,GAAG,8BAA8B;AACtD,MAAMC,IAAI,GAAG,MAAM;EACfC,WAAWA,CAACC,OAAO,EAAE;IACjBnD,qDAAgB,CAAC,IAAI,EAAEmD,OAAO,CAAC;IAC/B,IAAI,CAACC,WAAW,GAAGlD,qDAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IACtD,IAAI,CAACmD,YAAY,GAAGnD,qDAAW,CAAC,IAAI,EAAE,cAAc,EAAE,CAAC,CAAC;IACxD,IAAI,CAACoD,UAAU,GAAGpD,qDAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IACpD,IAAI,CAACqD,WAAW,GAAGrD,qDAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IACtD,IAAI,CAACsD,aAAa,GAAGtD,qDAAW,CAAC,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC;IAC1D,IAAI,CAACuD,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,OAAO,GAAG/B,8DAAkB,CAACgC,aAAa,CAAC;MAAEC,aAAa,EAAE;IAAK,CAAC,CAAC;IACxE,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,WAAW,GAAIC,EAAE,IAAK;MACvB;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,MAAMC,WAAW,GAAGlD,wDAAmB,CAACmD,QAAQ,CAAC;MACjD,IAAID,WAAW,IAAI,CAACA,WAAW,CAACE,QAAQ,CAAC,IAAI,CAACC,EAAE,CAAC,EAAE;QAC/C;MACJ;MACA,IAAI,CAACC,iBAAiB,CAACL,EAAE,EAAEE,QAAQ,CAAC;IACxC,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACI,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAC5C,SAAS,GAAG,KAAK;IACtB;AACR;AACA;IACQ,IAAI,CAAC6C,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,OAAO;IACnB;AACR;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB;AACR;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,EAAE;EAC1B;EACAC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAE;IACvB,MAAMC,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,IAAIA,SAAS,EAAE;MACX,IAAID,OAAO,KAAKE,SAAS,EAAE;QACvBD,SAAS,CAACE,SAAS,CAACC,MAAM,CAAC,gBAAgBJ,OAAO,EAAE,CAAC;MACzD;MACAC,SAAS,CAACE,SAAS,CAACE,GAAG,CAAC,gBAAgBN,IAAI,EAAE,CAAC;MAC/CE,SAAS,CAACK,eAAe,CAAC,OAAO,CAAC;IACtC;IACA,IAAI,IAAI,CAACC,WAAW,EAAE;MAClB;MACA,IAAI,CAACA,WAAW,CAACD,eAAe,CAAC,OAAO,CAAC;IAC7C;IACA,IAAI,CAACE,SAAS,GAAGN,SAAS;EAC9B;EACAO,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAAClC,aAAa,CAACmC,IAAI,CAAC;MACpBjB,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBkB,IAAI,EAAE,IAAI,CAAC5B;IACf,CAAC,CAAC;EACN;EACA6B,WAAWA,CAAA,EAAG;IACV,IAAI,CAAChE,SAAS,GAAGA,uDAAS,CAAC,IAAI,CAAC8C,IAAI,CAAC;IACrC;AACR;AACA;AACA;IACQ,IAAI,CAACa,SAAS,GAAGN,SAAS;EAC9B;EACAY,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACJ,WAAW,CAAC,CAAC;EACtB;EACMK,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,oMAAA;MACtB;MACA;MACA,IAAI,OAAOC,cAAc,KAAK,WAAW,IAAIA,cAAc,IAAI,IAAI,EAAE;QACjE,MAAMA,cAAc,CAACC,WAAW,CAAC,UAAU,CAAC;MAChD;MACA,IAAIH,KAAI,CAACjB,IAAI,KAAKG,SAAS,EAAE;QACzBc,KAAI,CAACjB,IAAI,GAAG3E,iDAAM,CAACgG,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC;MACjD;MACA,MAAMC,OAAO,GAAGL,KAAI,CAACM,SAAS,KAAKpB,SAAS,GAAGb,QAAQ,CAACkC,cAAc,CAACP,KAAI,CAACM,SAAS,CAAC,GAAG,IAAI;MAC7F,IAAID,OAAO,KAAK,IAAI,EAAE;QAClB/F,qDAAa,CAAC,0EAA0E,CAAC;QACzF;MACJ;MACA,IAAI0F,KAAI,CAACzB,EAAE,CAACD,QAAQ,CAAC+B,OAAO,CAAC,EAAE;QAC3B/F,qDAAa,CAAC,uHAAuH,CAAC;MAC1I;MACA0F,KAAI,CAACf,SAAS,GAAGoB,OAAO;MACxB;MACAA,OAAO,CAAClB,SAAS,CAACE,GAAG,CAAC,cAAc,CAAC;MACrCW,KAAI,CAAClB,WAAW,CAACkB,KAAI,CAACjB,IAAI,EAAEG,SAAS,CAAC;MACtCc,KAAI,CAACH,WAAW,CAAC,CAAC;MAClB;MACA1D,iDAAc,CAACqE,SAAS,CAACR,KAAI,CAAC;MAC9BA,KAAI,CAACS,WAAW,CAAC,CAAC;MAClBT,KAAI,CAACU,OAAO,GAAG,OAAO,qHAA6B,EAAEC,aAAa,CAAC;QAC/DpC,EAAE,EAAEF,QAAQ;QACZuC,WAAW,EAAE,YAAY;QACzBC,eAAe,EAAE,EAAE;QACnBC,SAAS,EAAE,EAAE;QACbC,WAAW,EAAE,IAAI;QACjBC,QAAQ,EAAG7C,EAAE,IAAK6B,KAAI,CAACgB,QAAQ,CAAC7C,EAAE,CAAC;QACnC8C,WAAW,EAAEA,CAAA,KAAMjB,KAAI,CAACiB,WAAW,CAAC,CAAC;QACrCC,OAAO,EAAEA,CAAA,KAAMlB,KAAI,CAACkB,OAAO,CAAC,CAAC;QAC7BC,MAAM,EAAGhD,EAAE,IAAK6B,KAAI,CAACmB,MAAM,CAAChD,EAAE,CAAC;QAC/BiD,KAAK,EAAGjD,EAAE,IAAK6B,KAAI,CAACoB,KAAK,CAACjD,EAAE;MAChC,CAAC,CAAC;MACF6B,KAAI,CAACN,WAAW,CAAC,CAAC;IAAC;EACvB;EACA2B,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACpD,mBAAmB,GAAGlC,uDAAqB,CAAC,IAAI,CAACwC,EAAE,CAAC;EAC7D;EACM+C,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAtB,oMAAA;MACrBsB,MAAI,CAAC1D,OAAO,GAAG,IAAI;MACnB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACQ,MAAM2D,SAAS,GAAGD,MAAI,CAAChD,EAAE,CAACkD,OAAO,CAAC,gBAAgB,CAAC;MACnD,IAAID,SAAS,KAAK,IAAI,EAAE;QACpBD,MAAI,CAAC9C,aAAa,SAAS+C,SAAS,CAACE,SAAS,CAAC,CAAC;MACpD;MACAH,MAAI,CAACd,WAAW,CAAC,CAAC;MAClBc,MAAI,CAAC7B,WAAW,CAAC,CAAC;IAAC;EACvB;EACAe,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;AACA;IACQ,IAAI,IAAI,CAAC5C,OAAO,EAAE;MACd,IAAI,CAACL,aAAa,CAACmC,IAAI,CAAC;QAAEjB,QAAQ,EAAE,IAAI,CAACA,QAAQ;QAAEkB,IAAI,EAAE,IAAI,CAAC5B;MAAQ,CAAC,CAAC;IAC5E;EACJ;EACM2D,oBAAoBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA3B,oMAAA;MACzB;AACR;AACA;AACA;AACA;AACA;AACA;MACQ,MAAM2B,MAAI,CAACC,KAAK,CAAC,KAAK,CAAC;MACvBD,MAAI,CAAClE,OAAO,CAACoE,OAAO,CAAC,CAAC;MACtB3F,iDAAc,CAAC4F,WAAW,CAACH,MAAI,CAAC;MAChC,IAAIA,MAAI,CAACpC,SAAS,EAAE;QAChBoC,MAAI,CAACpC,SAAS,CAACsC,OAAO,CAAC,CAAC;MAC5B;MACA,IAAIF,MAAI,CAAClB,OAAO,EAAE;QACdkB,MAAI,CAAClB,OAAO,CAACoB,OAAO,CAAC,CAAC;QACtBF,MAAI,CAAClB,OAAO,GAAGxB,SAAS;MAC5B;MACA0C,MAAI,CAACpC,SAAS,GAAGN,SAAS;MAC1B0C,MAAI,CAAC3C,SAAS,GAAGC,SAAS;IAAC;EAC/B;EACA8C,kBAAkBA,CAAC7D,EAAE,EAAE;IACnB,MAAM8D,gBAAgB,GAAG,IAAI,CAAC1D,EAAE,CAACkD,OAAO,CAAC,gBAAgB,CAAC;IAC1D,IAAIQ,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK9D,EAAE,CAAC+D,MAAM,EAAE;MAC7D,IAAI,CAACzD,aAAa,GAAGN,EAAE,CAACgE,MAAM,CAACC,OAAO;MACtC,IAAI,CAAC1C,WAAW,CAAC,CAAC;IACtB;EACJ;EACA2C,eAAeA,CAAClE,EAAE,EAAE;IAChB;IACA,IAAI,IAAI,CAACH,OAAO,IAAI,IAAI,CAACP,SAAS,GAAGU,EAAE,CAACmE,SAAS,GAAG,GAAG,EAAE;MACrD,MAAMC,WAAW,GAAGpE,EAAE,CAACqE,YAAY,GAAG,CAACrE,EAAE,CAACqE,YAAY,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAClD,WAAW,CAAC,GAAG,KAAK;MAC3F,IAAIgD,WAAW,EAAE;QACbpE,EAAE,CAACuE,cAAc,CAAC,CAAC;QACnBvE,EAAE,CAACwE,eAAe,CAAC,CAAC;QACpB,IAAI,CAACd,KAAK,CAAC3C,SAAS,EAAE9D,oDAAQ,CAAC;MACnC;IACJ;EACJ;EACAwH,SAASA,CAACzE,EAAE,EAAE;IACV,IAAIA,EAAE,CAAC0E,GAAG,KAAK,QAAQ,EAAE;MACrB,IAAI,CAAChB,KAAK,CAAC3C,SAAS,EAAE9D,oDAAQ,CAAC;IACnC;EACJ;EACA;AACJ;AACA;EACI0H,MAAMA,CAAA,EAAG;IACL,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAI,CAAChF,OAAO,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIiF,QAAQA,CAAA,EAAG;IACP,OAAOF,OAAO,CAACC,OAAO,CAAC,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC;EAC5C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACItD,IAAIA,CAACuD,QAAQ,GAAG,IAAI,EAAE;IAClB,OAAO,IAAI,CAACC,OAAO,CAAC,IAAI,EAAED,QAAQ,CAAC;EACvC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACItB,KAAKA,CAACsB,QAAQ,GAAG,IAAI,EAAEE,IAAI,EAAE;IACzB,OAAO,IAAI,CAACD,OAAO,CAAC,KAAK,EAAED,QAAQ,EAAEE,IAAI,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,MAAMA,CAACH,QAAQ,GAAG,IAAI,EAAE;IACpB,OAAO,IAAI,CAACC,OAAO,CAAC,CAAC,IAAI,CAACpF,OAAO,EAAEmF,QAAQ,CAAC;EAChD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACG,UAAU,EAAEJ,QAAQ,GAAG,IAAI,EAAEE,IAAI,EAAE;IACvC,OAAOlH,iDAAc,CAACqH,QAAQ,CAAC,IAAI,EAAED,UAAU,EAAEJ,QAAQ,EAAEE,IAAI,CAAC;EACpE;EACA7E,iBAAiBA,CAACL,EAAE,EAAEsF,GAAG,EAAE;IACvB,MAAMvB,MAAM,GAAG/D,EAAE,CAAC+D,MAAM;IACxB,IAAI,CAACA,MAAM,EAAE;MACT;IACJ;IACA;AACR;AACA;AACA;IACQ,IAAI,IAAI,CAAC3D,EAAE,CAACD,QAAQ,CAAC4D,MAAM,CAAC,EAAE;MAC1B,IAAI,CAACwB,SAAS,GAAGxB,MAAM;IAC3B,CAAC,MACI;MACD;AACZ;AACA;AACA;MACY,MAAM;QAAE3D;MAAG,CAAC,GAAG,IAAI;MACnB;AACZ;AACA;AACA;AACA;AACA;MACYjD,wDAAoB,CAACiD,EAAE,CAAC;MACxB;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACmF,SAAS,KAAKD,GAAG,CAACE,aAAa,EAAE;QACtCnI,wDAAmB,CAAC+C,EAAE,CAAC;MAC3B;IACJ;EACJ;EACMiF,QAAQA,CAAAI,EAAA,EAAoC;IAAA,IAAAC,MAAA;IAAA,OAAA5D,oMAAA,YAAnCsD,UAAU,EAAEJ,QAAQ,GAAG,IAAI,EAAEE,IAAI;MAC5C;MACA,IAAI,CAACQ,MAAI,CAACX,SAAS,CAAC,CAAC,IAAIW,MAAI,CAAC9F,WAAW,IAAIwF,UAAU,KAAKM,MAAI,CAAC7F,OAAO,EAAE;QACtE,OAAO,KAAK;MAChB;MACA6F,MAAI,CAACC,eAAe,CAACP,UAAU,EAAEF,IAAI,CAAC;MACtC,MAAMQ,MAAI,CAACE,aAAa,CAAC,CAAC;MAC1B,MAAMF,MAAI,CAACG,cAAc,CAACT,UAAU,EAAEJ,QAAQ,CAAC;MAC/C;AACR;AACA;AACA;AACA;MACQ,IAAIU,MAAI,CAAC/F,kBAAkB,EAAE;QACzB+F,MAAI,CAAC/F,kBAAkB,GAAG,KAAK;QAC/B,OAAO,KAAK;MAChB;MACA+F,MAAI,CAACI,cAAc,CAACV,UAAU,EAAEF,IAAI,CAAC;MACrC,OAAO,IAAI;IAAC,GAAAa,KAAA,OAAAC,SAAA;EAChB;EACMJ,aAAaA,CAAA,EAAG;IAAA,IAAAK,MAAA;IAAA,OAAAnE,oMAAA;MAClB;MACA;MACA,MAAMoE,KAAK,GAAGD,MAAI,CAAC7E,WAAW,CAAC+E,WAAW;MAC1C;AACR;AACA;AACA;MACQ,MAAMC,WAAW,GAAG1I,uDAAS,CAACuI,MAAI,CAACzF,IAAI,CAAC;MACxC,IAAI0F,KAAK,KAAKD,MAAI,CAACC,KAAK,IAAID,MAAI,CAAC5E,SAAS,KAAKN,SAAS,IAAIqF,WAAW,KAAKH,MAAI,CAACvI,SAAS,EAAE;QACxF;MACJ;MACAuI,MAAI,CAACC,KAAK,GAAGA,KAAK;MAClBD,MAAI,CAACvI,SAAS,GAAG0I,WAAW;MAC5B;MACA,IAAIH,MAAI,CAAC5E,SAAS,EAAE;QAChB4E,MAAI,CAAC5E,SAAS,CAACsC,OAAO,CAAC,CAAC;QACxBsC,MAAI,CAAC5E,SAAS,GAAGN,SAAS;MAC9B;MACA;MACA,MAAMM,SAAS,GAAI4E,MAAI,CAAC5E,SAAS,SAASrD,iDAAc,CAACqI,gBAAgB,CAACJ,MAAI,CAACrF,IAAI,EAAEqF,MAAI,CAAE;MAC3F,IAAI,CAAChK,iDAAM,CAACqK,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE;QACtCjF,SAAS,CAACkF,QAAQ,CAAC,CAAC,CAAC;MACzB;MACAlF,SAAS,CAACmF,IAAI,CAAC,MAAM,CAAC;IAAC;EAC3B;EACMX,cAAcA,CAACT,UAAU,EAAEJ,QAAQ,EAAE;IAAA,IAAAyB,MAAA;IAAA,OAAA3E,oMAAA;MACvC,MAAM4E,UAAU,GAAG,CAACtB,UAAU;MAC9B,MAAMuB,IAAI,GAAGtK,qDAAU,CAACoK,MAAI,CAAC;MAC7B,MAAMG,MAAM,GAAGD,IAAI,KAAK,KAAK,GAAGjI,SAAS,GAAGC,QAAQ;MACpD,MAAMkI,aAAa,GAAGF,IAAI,KAAK,KAAK,GAAG/H,gBAAgB,GAAGC,eAAe;MACzE,MAAMiI,GAAG,GAAGL,MAAI,CAACpF,SAAS,CACrB0F,SAAS,CAACL,UAAU,GAAG,SAAS,GAAG,QAAQ,CAAC,CAC5CE,MAAM,CAACF,UAAU,GAAGG,aAAa,GAAGD,MAAM,CAAC;MAChD,IAAI5B,QAAQ,EAAE;QACV,MAAM8B,GAAG,CAACE,IAAI,CAAC,CAAC;MACpB,CAAC,MACI;QACDF,GAAG,CAACE,IAAI,CAAC;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;MAC5B;MACA;AACR;AACA;AACA;AACA;AACA;AACA;MACQ,IAAIH,GAAG,CAACI,YAAY,CAAC,CAAC,KAAK,SAAS,EAAE;QAClCJ,GAAG,CAACC,SAAS,CAAC,QAAQ,CAAC;MAC3B;IAAC;EACL;EACAhC,SAASA,CAAA,EAAG;IACR,OAAO,CAAC,IAAI,CAACxE,QAAQ,IAAI,CAAC,IAAI,CAACD,aAAa;EAChD;EACA6G,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC1G,YAAY,IAAI,CAAC,IAAI,CAACb,WAAW,IAAI,IAAI,CAACmF,SAAS,CAAC,CAAC;EACrE;EACAlC,QAAQA,CAACmB,MAAM,EAAE;IACb;IACA,MAAMoD,gBAAgB,GAAG,CAAC,CAAClH,QAAQ,CAACmH,aAAa,CAAC,sBAAsB,CAAC;IACzE,IAAID,gBAAgB,IAAI,CAAC,IAAI,CAACD,QAAQ,CAAC,CAAC,EAAE;MACtC,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAACtH,OAAO,EAAE;MACd,OAAO,IAAI;IACf,CAAC,MACI,IAAI7B,iDAAc,CAACsJ,YAAY,CAAC,CAAC,EAAE;MACpC,OAAO,KAAK;IAChB;IACA,OAAOC,aAAa,CAACC,MAAM,EAAExD,MAAM,CAACyD,QAAQ,EAAE,IAAI,CAAC/J,SAAS,EAAE,IAAI,CAACgD,YAAY,CAAC;EACpF;EACAoC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6C,eAAe,CAAC,CAAC,IAAI,CAAC9F,OAAO,EAAEtC,oDAAO,CAAC;IAC5C,OAAO,IAAI,CAACqI,aAAa,CAAC,CAAC;EAC/B;EACA7C,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACnD,WAAW,IAAI,CAAC,IAAI,CAACyB,SAAS,EAAE;MACtCxD,uDAAM,CAAC,KAAK,EAAE,4BAA4B,CAAC;MAC3C;IACJ;IACA;IACA,IAAI,CAACwD,SAAS,CAACqG,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC7H,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;EAC5D;EACAmD,MAAMA,CAACgB,MAAM,EAAE;IACX,IAAI,CAAC,IAAI,CAACpE,WAAW,IAAI,CAAC,IAAI,CAACyB,SAAS,EAAE;MACtCxD,uDAAM,CAAC,KAAK,EAAE,4BAA4B,CAAC;MAC3C;IACJ;IACA,MAAM8J,KAAK,GAAGC,YAAY,CAAC5D,MAAM,CAAC6D,MAAM,EAAE,IAAI,CAAChI,OAAO,EAAE,IAAI,CAACnC,SAAS,CAAC;IACvE,MAAMoK,SAAS,GAAGH,KAAK,GAAG,IAAI,CAACzB,KAAK;IACpC,IAAI,CAAC7E,SAAS,CAAC0G,YAAY,CAAC,IAAI,CAAClI,OAAO,GAAG,CAAC,GAAGiI,SAAS,GAAGA,SAAS,CAAC;EACzE;EACA7E,KAAKA,CAACe,MAAM,EAAE;IACV,IAAI,CAAC,IAAI,CAACpE,WAAW,IAAI,CAAC,IAAI,CAACyB,SAAS,EAAE;MACtCxD,uDAAM,CAAC,KAAK,EAAE,4BAA4B,CAAC;MAC3C;IACJ;IACA,MAAM8G,MAAM,GAAG,IAAI,CAAC9E,OAAO;IAC3B,MAAMnC,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMiK,KAAK,GAAGC,YAAY,CAAC5D,MAAM,CAAC6D,MAAM,EAAElD,MAAM,EAAEjH,SAAS,CAAC;IAC5D,MAAMwI,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAM4B,SAAS,GAAGH,KAAK,GAAGzB,KAAK;IAC/B,MAAM8B,QAAQ,GAAGhE,MAAM,CAACiE,SAAS;IACjC,MAAMC,CAAC,GAAGhC,KAAK,GAAG,GAAG;IACrB,MAAMiC,mBAAmB,GAAGH,QAAQ,IAAI,CAAC,KAAKA,QAAQ,GAAG,GAAG,IAAIhE,MAAM,CAAC6D,MAAM,GAAGK,CAAC,CAAC;IAClF,MAAME,kBAAkB,GAAGJ,QAAQ,IAAI,CAAC,KAAKA,QAAQ,GAAG,CAAC,GAAG,IAAIhE,MAAM,CAAC6D,MAAM,GAAG,CAACK,CAAC,CAAC;IACnF,MAAMG,cAAc,GAAG1D,MAAM,GACvBjH,SAAS,GACLyK,mBAAmB,GACnBC,kBAAkB,GACtB1K,SAAS,GACL0K,kBAAkB,GAClBD,mBAAmB;IAC7B,IAAI/C,UAAU,GAAG,CAACT,MAAM,IAAI0D,cAAc;IAC1C,IAAI1D,MAAM,IAAI,CAAC0D,cAAc,EAAE;MAC3BjD,UAAU,GAAG,IAAI;IACrB;IACA,IAAI,CAAC9F,SAAS,GAAG0E,MAAM,CAACsE,WAAW;IACnC;IACA,IAAIC,YAAY,GAAGF,cAAc,GAAG,KAAK,GAAG,CAAC,IAAI;IACjD;AACR;AACA;AACA;AACA;IACQ,MAAMG,iBAAiB,GAAGV,SAAS,GAAG,CAAC,GAAG,IAAI,GAAGA,SAAS;IAC1D;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQS,YAAY,IACRzL,4DAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEgB,uDAAK,CAAC,CAAC,EAAE0K,iBAAiB,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC5G,MAAMC,MAAM,GAAG,IAAI,CAAC5I,OAAO,GAAG,CAACwI,cAAc,GAAGA,cAAc;IAC9D,IAAI,CAAChH,SAAS,CACTuF,MAAM,CAAC,gCAAgC,CAAC,CACxC8B,QAAQ,CAAC,MAAM,IAAI,CAAC5C,cAAc,CAACV,UAAU,EAAE7H,oDAAO,CAAC,EAAE;MAAEoL,eAAe,EAAE;IAAK,CAAC,CAAC,CACnFC,WAAW,CAACH,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC5I,OAAO,GAAG,CAAC,GAAG0I,YAAY,GAAGA,YAAY,EAAE,GAAG,CAAC;EACzF;EACA5C,eAAeA,CAACP,UAAU,EAAEF,IAAI,EAAE;IAC9BrH,uDAAM,CAAC,CAAC,IAAI,CAAC+B,WAAW,EAAE,gDAAgD,CAAC;IAC3E;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIrD,qDAAU,CAAC,SAAS,CAAC,EAAE;MACvB,IAAI,CAAC6D,EAAE,CAACyI,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAC/C;IACA;IACA;IACA,IAAI,CAACzI,EAAE,CAACY,SAAS,CAACE,GAAG,CAAC4H,SAAS,CAAC;IAChC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC1I,EAAE,CAACyI,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC;IACrC,IAAI,IAAI,CAACE,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAC/H,SAAS,CAACE,GAAG,CAAC8H,aAAa,CAAC;IAChD;IACA;IACA,IAAI,IAAI,CAAClI,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACE,SAAS,CAACE,GAAG,CAAC+H,iBAAiB,CAAC;MAC/C;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAACnI,SAAS,CAAC+H,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IACtD;IACA,IAAI,CAACtJ,OAAO,CAAC2J,KAAK,CAAC,CAAC;IACpB,IAAI,CAACtJ,WAAW,GAAG,IAAI;IACvB,IAAIwF,UAAU,EAAE;MACZ,IAAI,CAACnG,WAAW,CAACuC,IAAI,CAAC,CAAC;IAC3B,CAAC,MACI;MACD,IAAI,CAACtC,YAAY,CAACsC,IAAI,CAAC;QAAE0D;MAAK,CAAC,CAAC;IACpC;EACJ;EACAY,cAAcA,CAACnB,MAAM,EAAEO,IAAI,EAAE;IACzB,IAAIiE,EAAE;IACN;IACA;IACA;IACA;IACA,IAAI,CAACtJ,OAAO,GAAG8E,MAAM;IACrB,IAAI,CAAC/E,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;MACf,IAAI,CAACN,OAAO,CAAC6J,OAAO,CAAC,CAAC;IAC1B;IACA,IAAIzE,MAAM,EAAE;MACR;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAIpI,qDAAU,CAAC,SAAS,CAAC,EAAE;QACvB,IAAI,CAAC6D,EAAE,CAACe,eAAe,CAAC,aAAa,CAAC;MAC1C;MACA;MACA,IAAI,CAAChC,UAAU,CAACqC,IAAI,CAAC,CAAC;MACtB;AACZ;AACA;AACA;AACA;MACY,MAAM6H,WAAW,GAAG,CAACF,EAAE,GAAGjJ,QAAQ,CAACsF,aAAa,MAAM,IAAI,IAAI2D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC7F,OAAO,CAAC,UAAU,CAAC;MAC7G,IAAI+F,WAAW,KAAK,IAAI,CAACjJ,EAAE,EAAE;QACzB,IAAI,CAACA,EAAE,CAACkJ,KAAK,CAAC,CAAC;MACnB;MACA;MACApJ,QAAQ,CAACqJ,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACxJ,WAAW,EAAE,IAAI,CAAC;IAC9D,CAAC,MACI;MACD,IAAI,CAACK,EAAE,CAACe,eAAe,CAAC,aAAa,CAAC;MACtC;MACA,IAAI,CAACf,EAAE,CAACY,SAAS,CAACC,MAAM,CAAC6H,SAAS,CAAC;MACnC;AACZ;AACA;AACA;MACY,IAAI,CAAC1I,EAAE,CAACe,eAAe,CAAC,UAAU,CAAC;MACnC,IAAI,IAAI,CAACL,SAAS,EAAE;QAChB,IAAI,CAACA,SAAS,CAACE,SAAS,CAACC,MAAM,CAACgI,iBAAiB,CAAC;QAClD;AAChB;AACA;AACA;AACA;QACgB,IAAI,CAACnI,SAAS,CAACK,eAAe,CAAC,aAAa,CAAC;MACjD;MACA,IAAI,IAAI,CAAC4H,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,CAAC/H,SAAS,CAACC,MAAM,CAAC+H,aAAa,CAAC;MACnD;MACA,IAAI,IAAI,CAAC3H,SAAS,EAAE;QAChB,IAAI,CAACA,SAAS,CAACmI,IAAI,CAAC,CAAC;MACzB;MACA;MACA,IAAI,CAACpK,WAAW,CAACoC,IAAI,CAAC;QAAE0D;MAAK,CAAC,CAAC;MAC/B;MACAhF,QAAQ,CAACuJ,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC1J,WAAW,EAAE,IAAI,CAAC;IACjE;EACJ;EACAwB,WAAWA,CAAA,EAAG;IACV,MAAMuD,QAAQ,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IACjC,IAAI,IAAI,CAACxC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACmH,MAAM,CAAC5E,QAAQ,IAAI,IAAI,CAACrE,YAAY,CAAC;IACtD;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACqE,QAAQ,EAAE;MACX;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAAClF,WAAW,EAAE;QAClB,IAAI,CAACD,kBAAkB,GAAG,IAAI;MAClC;MACA;AACZ;AACA;AACA;MACY,IAAI,CAACmG,cAAc,CAAC,KAAK,EAAEvI,oDAAO,CAAC;IACvC;EACJ;EACAoM,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE/I,IAAI;MAAEL,QAAQ;MAAEH,EAAE;MAAEE,aAAa;MAAER,mBAAmB;MAAEU;IAAK,CAAC,GAAG,IAAI;IAC7E,MAAMmG,IAAI,GAAGtK,qDAAU,CAAC,IAAI,CAAC;IAC7B;AACR;AACA;AACA;AACA;IACQ,OAAQG,qDAAC,CAACE,iDAAI,EAAE;MAAEgI,GAAG,EAAE,0CAA0C;MAAEkF,SAAS,EAAEnM,wFAAqB,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAACgH,SAAS;MAAES,IAAI,EAAE,YAAY;MAAE,YAAY,EAAEpF,mBAAmB,CAAC,YAAY,CAAC,IAAI,MAAM;MAAE+J,KAAK,EAAE;QAC7M,CAAClD,IAAI,GAAG,IAAI;QACZ,CAAC,aAAa/F,IAAI,EAAE,GAAG,IAAI;QAC3B,cAAc,EAAE,CAACL,QAAQ;QACzB,CAAC,aAAaC,IAAI,EAAE,GAAG,IAAI;QAC3B,mBAAmB,EAAEF,aAAa;QAClC,iBAAiB,EAAErC,qDAAW,CAAC,gBAAgB,EAAEmC,EAAE;MACvD;IAAE,CAAC,EAAE5D,qDAAC,CAAC,KAAK,EAAE;MAAEkI,GAAG,EAAE,0CAA0C;MAAEmF,KAAK,EAAE,YAAY;MAAEC,IAAI,EAAE,WAAW;MAAEC,GAAG,EAAG3J,EAAE,IAAM,IAAI,CAACgB,WAAW,GAAGhB;IAAI,CAAC,EAAE5D,qDAAC,CAAC,MAAM,EAAE;MAAEkI,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAElI,qDAAC,CAAC,cAAc,EAAE;MAAEkI,GAAG,EAAE,0CAA0C;MAAEqF,GAAG,EAAG3J,EAAE,IAAM,IAAI,CAAC2I,UAAU,GAAG3I,EAAG;MAAEyJ,KAAK,EAAE,eAAe;MAAEG,QAAQ,EAAE,KAAK;MAAExF,eAAe,EAAE,KAAK;MAAEsF,IAAI,EAAE;IAAW,CAAC,CAAC,CAAC;EACzZ;EACA,IAAI1J,EAAEA,CAAA,EAAG;IAAE,OAAOxD,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWqN,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,MAAM,EAAE,CAAC,aAAa,CAAC;MACvB,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,MAAM,EAAE,CAAC,aAAa,CAAC;MACvB,cAAc,EAAE,CAAC,qBAAqB;IAC1C,CAAC;EAAE;AACP,CAAC;AACD,MAAMrC,YAAY,GAAGA,CAACC,MAAM,EAAElD,MAAM,EAAEjH,SAAS,KAAK;EAChD,OAAOwM,IAAI,CAACC,GAAG,CAAC,CAAC,EAAExF,MAAM,KAAKjH,SAAS,GAAG,CAACmK,MAAM,GAAGA,MAAM,CAAC;AAC/D,CAAC;AACD,MAAMN,aAAa,GAAGA,CAAC6C,GAAG,EAAEC,IAAI,EAAE3M,SAAS,EAAEgD,YAAY,KAAK;EAC1D,IAAIhD,SAAS,EAAE;IACX,OAAO2M,IAAI,IAAID,GAAG,CAACE,UAAU,GAAG5J,YAAY;EAChD,CAAC,MACI;IACD,OAAO2J,IAAI,IAAI3J,YAAY;EAC/B;AACJ,CAAC;AACD,MAAMoI,SAAS,GAAG,WAAW;AAC7B,MAAME,aAAa,GAAG,eAAe;AACrC,MAAMC,iBAAiB,GAAG,mBAAmB;AAC7CnK,IAAI,CAACyL,KAAK,GAAG;EACTC,GAAG,EAAEhM,UAAU;EACfiM,EAAE,EAAEhM;AACR,CAAC;;AAED;AACA,MAAMiM,gBAAgB;EAAA,IAAAC,IAAA,GAAA7I,oMAAA,CAAG,WAAO8I,IAAI,EAAK;IACrC,MAAMC,MAAM,SAAS7M,iDAAc,CAACiE,GAAG,CAAC2I,IAAI,CAAC;IAC7C,OAAO,CAAC,EAAEC,MAAM,WAAWA,MAAM,CAAC/F,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,CAAC;EAAA,gBAHK4F,gBAAgBA,CAAAI,GAAA;IAAA,OAAAH,IAAA,CAAA5E,KAAA,OAAAC,SAAA;EAAA;AAAA,GAGrB;AAED,MAAM+E,gBAAgB,GAAG,+2FAA+2F;AAEx4F,MAAMC,eAAe,GAAG,49FAA49F;AAEp/F,MAAMC,UAAU,GAAG,MAAM;EACrBlM,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAkM,MAAA;IACjBrP,qDAAgB,CAAC,IAAI,EAAEmD,OAAO,CAAC;IAC/B,IAAI,CAACc,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACmE,OAAO,GAAG,KAAK;IACpB;AACR;AACA;IACQ,IAAI,CAAC1D,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAAC4K,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;IACQ,IAAI,CAACvK,IAAI,GAAG,QAAQ;IACpB,IAAI,CAACwK,OAAO,gBAAAtJ,oMAAA,CAAG,aAAY;MACvB,OAAO9D,iDAAc,CAACmH,MAAM,CAAC+F,MAAI,CAACN,IAAI,CAAC;IAC3C,CAAC;EACL;EACA1H,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACpD,mBAAmB,GAAGlC,uDAAqB,CAAC,IAAI,CAACwC,EAAE,CAAC;EAC7D;EACA+C,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACkI,iBAAiB,CAAC,CAAC;EAC5B;EACMA,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAxJ,oMAAA;MACtBwJ,MAAI,CAACrH,OAAO,SAASyG,gBAAgB,CAACY,MAAI,CAACV,IAAI,CAAC;IAAC;EACrD;EACAjB,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE4B,KAAK;MAAEhL,QAAQ;MAAET;IAAoB,CAAC,GAAG,IAAI;IACrD,MAAM6G,IAAI,GAAGtK,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAMmP,QAAQ,GAAGvP,iDAAM,CAACgG,GAAG,CAAC,UAAU,EAAE0E,IAAI,KAAK,KAAK,GAAGtI,iDAAW,GAAGE,iDAAS,CAAC;IACjF,MAAMkN,MAAM,GAAG,IAAI,CAACN,QAAQ,IAAI,CAAC,IAAI,CAAClH,OAAO;IAC7C,MAAMyH,KAAK,GAAG;MACV9K,IAAI,EAAE,IAAI,CAACA;IACf,CAAC;IACD,MAAM+K,SAAS,GAAG7L,mBAAmB,CAAC,YAAY,CAAC,IAAI,MAAM;IAC7D,OAAQtD,qDAAC,CAACE,iDAAI,EAAE;MAAEgI,GAAG,EAAE,0CAA0C;MAAE0G,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE,eAAe,EAAE7K,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAE,aAAa,EAAEkL,MAAM,GAAG,MAAM,GAAG,IAAI;MAAE5B,KAAK,EAAE1L,qDAAkB,CAACoN,KAAK,EAAE;QACrM,CAAC5E,IAAI,GAAG,IAAI;QACZiF,MAAM,EAAE,IAAI;QAAE;QACd,oBAAoB,EAAEH,MAAM;QAC5B,sBAAsB,EAAElL,QAAQ;QAChC,YAAY,EAAEtC,qDAAW,CAAC,aAAa,EAAE,IAAI,CAACmC,EAAE,CAAC;QACjD,kBAAkB,EAAEnC,qDAAW,CAAC,oBAAoB,EAAE,IAAI,CAACmC,EAAE,CAAC;QAC9D,iBAAiB,EAAE,IAAI;QACvB,eAAe,EAAE;MACrB,CAAC;IAAE,CAAC,EAAE5D,qDAAC,CAAC,QAAQ,EAAEqP,MAAM,CAACC,MAAM,CAAC;MAAEpH,GAAG,EAAE;IAA2C,CAAC,EAAEgH,KAAK,EAAE;MAAEnL,QAAQ,EAAEA,QAAQ;MAAEsJ,KAAK,EAAE,eAAe;MAAEC,IAAI,EAAE,QAAQ;MAAE,YAAY,EAAE6B;IAAU,CAAC,CAAC,EAAEnP,qDAAC,CAAC,MAAM,EAAE;MAAEkI,GAAG,EAAE,0CAA0C;MAAEmF,KAAK,EAAE;IAAe,CAAC,EAAErN,qDAAC,CAAC,MAAM,EAAE;MAAEkI,GAAG,EAAE;IAA2C,CAAC,EAAElI,qDAAC,CAAC,UAAU,EAAE;MAAEkI,GAAG,EAAE,0CAA0C;MAAEoF,IAAI,EAAE,MAAM;MAAEiC,IAAI,EAAEP,QAAQ;MAAE7E,IAAI,EAAEA,IAAI;MAAEqF,IAAI,EAAE,KAAK;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC,CAAC,CAAC,EAAErF,IAAI,KAAK,IAAI,IAAInK,qDAAC,CAAC,mBAAmB,EAAE;MAAEkI,GAAG,EAAE,0CAA0C;MAAE9D,IAAI,EAAE;IAAY,CAAC,CAAC,CAAC,CAAC;EACxlB;EACA,IAAIR,EAAEA,CAAA,EAAG;IAAE,OAAOxD,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDqO,UAAU,CAACV,KAAK,GAAG;EACfC,GAAG,EAAEO,gBAAgB;EACrBN,EAAE,EAAEO;AACR,CAAC;AAED,MAAMiB,aAAa,GAAG,0CAA0C;AAEhE,MAAMC,UAAU,GAAG,MAAM;EACrBnN,WAAWA,CAACC,OAAO,EAAE;IACjBnD,qDAAgB,CAAC,IAAI,EAAEmD,OAAO,CAAC;IAC/B,IAAI,CAACiF,OAAO,GAAG,KAAK;IACpB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACkH,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,OAAOpN,iDAAc,CAACmH,MAAM,CAAC,IAAI,CAACyF,IAAI,CAAC;IAC3C,CAAC;EACL;EACAhJ,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACyJ,iBAAiB,CAAC,CAAC;EAC5B;EACMA,iBAAiBA,CAAA,EAAG;IAAA,IAAAc,MAAA;IAAA,OAAArK,oMAAA;MACtBqK,MAAI,CAAClI,OAAO,SAASyG,gBAAgB,CAACyB,MAAI,CAACvB,IAAI,CAAC;IAAC;EACrD;EACAjB,MAAMA,CAAA,EAAG;IACL,MAAMhD,IAAI,GAAGtK,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAMoP,MAAM,GAAG,IAAI,CAACN,QAAQ,IAAI,CAAC,IAAI,CAAClH,OAAO;IAC7C,OAAQzH,qDAAC,CAACE,iDAAI,EAAE;MAAEgI,GAAG,EAAE,0CAA0C;MAAE0G,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE,aAAa,EAAEK,MAAM,GAAG,MAAM,GAAG,IAAI;MAAE5B,KAAK,EAAE;QAChI,CAAClD,IAAI,GAAG,IAAI;QACZ,oBAAoB,EAAE8E;MAC1B;IAAE,CAAC,EAAEjP,qDAAC,CAAC,MAAM,EAAE;MAAEkI,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC5E;AACJ,CAAC;AACDwH,UAAU,CAAC3B,KAAK,GAAG0B,aAAa", "sources": ["./node_modules/@ionic/core/dist/esm/ion-menu_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, l as config, o as printIonError, e as getIonMode, a as isPlatform, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-hHmYLOfE.js';\nimport { o as getPresentedOverlay, B as BACKDROP, n as focusFirstDescendant, q as focusLastDescendant, G as GESTURE } from './overlays-8Y2rA-ps.js';\nimport { G as GESTURE_CONTROLLER } from './gesture-controller-BTEOs1at.js';\nimport { shouldUseCloseWatcher } from './hardware-back-button-DcH0BbDp.js';\nimport { o as isEndSide, i as inheritAriaAttributes, l as assert, e as clamp } from './helpers-1O4D2b7y.js';\nimport { m as menuController } from './index-D8sncTHY.js';\nimport { h as hostContext, c as createColorClasses } from './theme-DiVJyqlX.js';\nimport { u as menuOutline, v as menuSharp } from './index-BLV6ykCk.js';\nimport './index-ZjP4CjeZ.js';\nimport './framework-delegate-DxcnWic_.js';\nimport './animation-BWcUKtbn.js';\n\nconst menuIosCss = \":host{--width:304px;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--background:var(--ion-background-color, #fff);left:0;right:0;top:0;bottom:0;display:none;position:absolute;contain:strict}:host(.show-menu){display:block}.menu-inner{-webkit-transform:translateX(-9999px);transform:translateX(-9999px);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:justify;justify-content:space-between;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);contain:strict}:host(.menu-side-start) .menu-inner{--ion-safe-area-right:0px;top:0;bottom:0}:host(.menu-side-start) .menu-inner{inset-inline-start:0;inset-inline-end:auto}:host-context([dir=rtl]):host(.menu-side-start) .menu-inner,:host-context([dir=rtl]).menu-side-start .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}@supports selector(:dir(rtl)){:host(.menu-side-start:dir(rtl)) .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}}:host(.menu-side-end) .menu-inner{--ion-safe-area-left:0px;top:0;bottom:0}:host(.menu-side-end) .menu-inner{inset-inline-start:auto;inset-inline-end:0}:host-context([dir=rtl]):host(.menu-side-end) .menu-inner,:host-context([dir=rtl]).menu-side-end .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}@supports selector(:dir(rtl)){:host(.menu-side-end:dir(rtl)) .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}}ion-backdrop{display:none;opacity:0.01;z-index:-1}@media (max-width: 340px){.menu-inner{--width:264px}}:host(.menu-type-reveal){z-index:0}:host(.menu-type-reveal.show-menu) .menu-inner{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}:host(.menu-type-overlay){z-index:1000}:host(.menu-type-overlay) .show-backdrop{display:block;cursor:pointer}:host(.menu-pane-visible){-ms-flex:0 1 auto;flex:0 1 auto;width:var(--side-width, var(--width));min-width:var(--side-min-width, var(--min-width));max-width:var(--side-max-width, var(--max-width))}:host(.menu-pane-visible.split-pane-side){left:0;right:0;top:0;bottom:0;position:relative;-webkit-box-shadow:none;box-shadow:none;z-index:0}:host(.menu-pane-visible.split-pane-side.menu-enabled){display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0}:host(.menu-pane-visible.split-pane-side){-ms-flex-order:-1;order:-1}:host(.menu-pane-visible.split-pane-side[side=end]){-ms-flex-order:1;order:1}:host(.menu-pane-visible) .menu-inner{left:0;right:0;width:auto;-webkit-transform:none;transform:none;-webkit-box-shadow:none;box-shadow:none}:host(.menu-pane-visible) ion-backdrop{display:hidden !important}:host(.menu-pane-visible.split-pane-side){-webkit-border-start:0;border-inline-start:0;-webkit-border-end:var(--border);border-inline-end:var(--border);border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-pane-visible.split-pane-side[side=end]){-webkit-border-start:var(--border);border-inline-start:var(--border);-webkit-border-end:0;border-inline-end:0;border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-type-push){z-index:1000}:host(.menu-type-push) .show-backdrop{display:block}\";\n\nconst menuMdCss = \":host{--width:304px;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--background:var(--ion-background-color, #fff);left:0;right:0;top:0;bottom:0;display:none;position:absolute;contain:strict}:host(.show-menu){display:block}.menu-inner{-webkit-transform:translateX(-9999px);transform:translateX(-9999px);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:justify;justify-content:space-between;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);contain:strict}:host(.menu-side-start) .menu-inner{--ion-safe-area-right:0px;top:0;bottom:0}:host(.menu-side-start) .menu-inner{inset-inline-start:0;inset-inline-end:auto}:host-context([dir=rtl]):host(.menu-side-start) .menu-inner,:host-context([dir=rtl]).menu-side-start .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}@supports selector(:dir(rtl)){:host(.menu-side-start:dir(rtl)) .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}}:host(.menu-side-end) .menu-inner{--ion-safe-area-left:0px;top:0;bottom:0}:host(.menu-side-end) .menu-inner{inset-inline-start:auto;inset-inline-end:0}:host-context([dir=rtl]):host(.menu-side-end) .menu-inner,:host-context([dir=rtl]).menu-side-end .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}@supports selector(:dir(rtl)){:host(.menu-side-end:dir(rtl)) .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}}ion-backdrop{display:none;opacity:0.01;z-index:-1}@media (max-width: 340px){.menu-inner{--width:264px}}:host(.menu-type-reveal){z-index:0}:host(.menu-type-reveal.show-menu) .menu-inner{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}:host(.menu-type-overlay){z-index:1000}:host(.menu-type-overlay) .show-backdrop{display:block;cursor:pointer}:host(.menu-pane-visible){-ms-flex:0 1 auto;flex:0 1 auto;width:var(--side-width, var(--width));min-width:var(--side-min-width, var(--min-width));max-width:var(--side-max-width, var(--max-width))}:host(.menu-pane-visible.split-pane-side){left:0;right:0;top:0;bottom:0;position:relative;-webkit-box-shadow:none;box-shadow:none;z-index:0}:host(.menu-pane-visible.split-pane-side.menu-enabled){display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0}:host(.menu-pane-visible.split-pane-side){-ms-flex-order:-1;order:-1}:host(.menu-pane-visible.split-pane-side[side=end]){-ms-flex-order:1;order:1}:host(.menu-pane-visible) .menu-inner{left:0;right:0;width:auto;-webkit-transform:none;transform:none;-webkit-box-shadow:none;box-shadow:none}:host(.menu-pane-visible) ion-backdrop{display:hidden !important}:host(.menu-pane-visible.split-pane-side){-webkit-border-start:0;border-inline-start:0;-webkit-border-end:var(--border);border-inline-end:var(--border);border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-pane-visible.split-pane-side[side=end]){-webkit-border-start:var(--border);border-inline-start:var(--border);-webkit-border-end:0;border-inline-end:0;border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-type-overlay) .menu-inner{-webkit-box-shadow:4px 0px 16px rgba(0, 0, 0, 0.18);box-shadow:4px 0px 16px rgba(0, 0, 0, 0.18)}\";\n\nconst iosEasing = 'cubic-bezier(0.32,0.72,0,1)';\nconst mdEasing = 'cubic-bezier(0.0,0.0,0.2,1)';\nconst iosEasingReverse = 'cubic-bezier(1, 0, 0.68, 0.28)';\nconst mdEasingReverse = 'cubic-bezier(0.4, 0, 0.6, 1)';\nconst Menu = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionWillOpen = createEvent(this, \"ionWillOpen\", 7);\n        this.ionWillClose = createEvent(this, \"ionWillClose\", 7);\n        this.ionDidOpen = createEvent(this, \"ionDidOpen\", 7);\n        this.ionDidClose = createEvent(this, \"ionDidClose\", 7);\n        this.ionMenuChange = createEvent(this, \"ionMenuChange\", 7);\n        this.lastOnEnd = 0;\n        this.blocker = GESTURE_CONTROLLER.createBlocker({ disableScroll: true });\n        this.didLoad = false;\n        /**\n         * Flag used to determine if an open/close\n         * operation was cancelled. For example, if\n         * an app calls \"menu.open\" then disables the menu\n         * part way through the animation, then this would\n         * be considered a cancelled operation.\n         */\n        this.operationCancelled = false;\n        this.isAnimating = false;\n        this._isOpen = false;\n        this.inheritedAttributes = {};\n        this.handleFocus = (ev) => {\n            /**\n             * Overlays have their own focus trapping listener\n             * so we do not want the two listeners to conflict\n             * with each other. If the top-most overlay that is\n             * open does not contain this ion-menu, then ion-menu's\n             * focus trapping should not run.\n             */\n            const lastOverlay = getPresentedOverlay(document);\n            if (lastOverlay && !lastOverlay.contains(this.el)) {\n                return;\n            }\n            this.trapKeyboardFocus(ev, document);\n        };\n        /**\n         * If true, then the menu should be\n         * visible within a split pane.\n         * If false, then the menu is hidden.\n         * However, the menu-button/menu-toggle\n         * components can be used to open the\n         * menu.\n         */\n        this.isPaneVisible = false;\n        this.isEndSide = false;\n        /**\n         * If `true`, the menu is disabled.\n         */\n        this.disabled = false;\n        /**\n         * Which side of the view the menu should be placed.\n         */\n        this.side = 'start';\n        /**\n         * If `true`, swiping the menu is enabled.\n         */\n        this.swipeGesture = true;\n        /**\n         * The edge threshold for dragging the menu open.\n         * If a drag/swipe happens over this value, the menu is not triggered.\n         */\n        this.maxEdgeStart = 50;\n    }\n    typeChanged(type, oldType) {\n        const contentEl = this.contentEl;\n        if (contentEl) {\n            if (oldType !== undefined) {\n                contentEl.classList.remove(`menu-content-${oldType}`);\n            }\n            contentEl.classList.add(`menu-content-${type}`);\n            contentEl.removeAttribute('style');\n        }\n        if (this.menuInnerEl) {\n            // Remove effects of previous animations\n            this.menuInnerEl.removeAttribute('style');\n        }\n        this.animation = undefined;\n    }\n    disabledChanged() {\n        this.updateState();\n        this.ionMenuChange.emit({\n            disabled: this.disabled,\n            open: this._isOpen,\n        });\n    }\n    sideChanged() {\n        this.isEndSide = isEndSide(this.side);\n        /**\n         * Menu direction animation is calculated based on the document direction.\n         * If the document direction changes, we need to create a new animation.\n         */\n        this.animation = undefined;\n    }\n    swipeGestureChanged() {\n        this.updateState();\n    }\n    async connectedCallback() {\n        // TODO: connectedCallback is fired in CE build\n        // before WC is defined. This needs to be fixed in Stencil.\n        if (typeof customElements !== 'undefined' && customElements != null) {\n            await customElements.whenDefined('ion-menu');\n        }\n        if (this.type === undefined) {\n            this.type = config.get('menuType', 'overlay');\n        }\n        const content = this.contentId !== undefined ? document.getElementById(this.contentId) : null;\n        if (content === null) {\n            printIonError('[ion-menu] - Must have a \"content\" element to listen for drag events on.');\n            return;\n        }\n        if (this.el.contains(content)) {\n            printIonError(`[ion-menu] - The \"contentId\" should refer to the main view's ion-content, not the ion-content inside of the ion-menu.`);\n        }\n        this.contentEl = content;\n        // add menu's content classes\n        content.classList.add('menu-content');\n        this.typeChanged(this.type, undefined);\n        this.sideChanged();\n        // register this menu with the app's menu controller\n        menuController._register(this);\n        this.menuChanged();\n        this.gesture = (await import('./index-CfgBF1SE.js')).createGesture({\n            el: document,\n            gestureName: 'menu-swipe',\n            gesturePriority: 30,\n            threshold: 10,\n            blurOnStart: true,\n            canStart: (ev) => this.canStart(ev),\n            onWillStart: () => this.onWillStart(),\n            onStart: () => this.onStart(),\n            onMove: (ev) => this.onMove(ev),\n            onEnd: (ev) => this.onEnd(ev),\n        });\n        this.updateState();\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    async componentDidLoad() {\n        this.didLoad = true;\n        /**\n         * A menu inside of a split pane is assumed\n         * to be a side pane.\n         *\n         * When the menu is loaded it needs to\n         * see if it should be considered visible inside\n         * of the split pane. If the split pane is\n         * hidden then the menu should be too.\n         */\n        const splitPane = this.el.closest('ion-split-pane');\n        if (splitPane !== null) {\n            this.isPaneVisible = await splitPane.isVisible();\n        }\n        this.menuChanged();\n        this.updateState();\n    }\n    menuChanged() {\n        /**\n         * Inform dependent components such as ion-menu-button\n         * that the menu is ready. Note that we only want to do this\n         * once the menu has been rendered which is why we check for didLoad.\n         */\n        if (this.didLoad) {\n            this.ionMenuChange.emit({ disabled: this.disabled, open: this._isOpen });\n        }\n    }\n    async disconnectedCallback() {\n        /**\n         * The menu should be closed when it is\n         * unmounted from the DOM.\n         * This is an async call, so we need to wait for\n         * this to finish otherwise contentEl\n         * will not have MENU_CONTENT_OPEN removed.\n         */\n        await this.close(false);\n        this.blocker.destroy();\n        menuController._unregister(this);\n        if (this.animation) {\n            this.animation.destroy();\n        }\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n        this.animation = undefined;\n        this.contentEl = undefined;\n    }\n    onSplitPaneChanged(ev) {\n        const closestSplitPane = this.el.closest('ion-split-pane');\n        if (closestSplitPane !== null && closestSplitPane === ev.target) {\n            this.isPaneVisible = ev.detail.visible;\n            this.updateState();\n        }\n    }\n    onBackdropClick(ev) {\n        // TODO(FW-2832): type (CustomEvent triggers errors which should be sorted)\n        if (this._isOpen && this.lastOnEnd < ev.timeStamp - 100) {\n            const shouldClose = ev.composedPath ? !ev.composedPath().includes(this.menuInnerEl) : false;\n            if (shouldClose) {\n                ev.preventDefault();\n                ev.stopPropagation();\n                this.close(undefined, BACKDROP);\n            }\n        }\n    }\n    onKeydown(ev) {\n        if (ev.key === 'Escape') {\n            this.close(undefined, BACKDROP);\n        }\n    }\n    /**\n     * Returns `true` is the menu is open.\n     */\n    isOpen() {\n        return Promise.resolve(this._isOpen);\n    }\n    /**\n     * Returns `true` if the menu is active.\n     *\n     * A menu is active when it can be opened or closed, meaning it's enabled\n     * and it's not part of a `ion-split-pane`.\n     */\n    isActive() {\n        return Promise.resolve(this._isActive());\n    }\n    /**\n     * Opens the menu. If the menu is already open or it can't be opened,\n     * it returns `false`.\n     *\n     * @param animated If `true`, the menu will animate when opening.\n     * If `false`, the menu will open instantly without animation.\n     * Defaults to `true`.\n     */\n    open(animated = true) {\n        return this.setOpen(true, animated);\n    }\n    /**\n     * Closes the menu. If the menu is already closed or it can't be closed,\n     * it returns `false`.\n     *\n     * @param animated If `true`, the menu will animate when closing. If `false`,\n     * the menu will close instantly without animation. Defaults to `true`.\n     * @param role The role of the element that is closing the menu.\n     * This can be useful in a button handler for determining which button was\n     * clicked to close the menu. Some examples include:\n     * `\"cancel\"`, `\"destructive\"`, `\"selected\"`, and `\"backdrop\"`.\n     */\n    close(animated = true, role) {\n        return this.setOpen(false, animated, role);\n    }\n    /**\n     * Toggles the menu. If the menu is already open, it will try to close,\n     * otherwise it will try to open it.\n     * If the operation can't be completed successfully, it returns `false`.\n     *\n     * @param animated If `true`, the menu will animate when opening/closing.\n     * If `false`, the menu will open/close instantly without animation.\n     * Defaults to `true`.\n     */\n    toggle(animated = true) {\n        return this.setOpen(!this._isOpen, animated);\n    }\n    /**\n     * Opens or closes the menu.\n     * If the operation can't be completed successfully, it returns `false`.\n     *\n     * @param shouldOpen If `true`, the menu will open. If `false`, the menu\n     * will close.\n     * @param animated If `true`, the menu will animate when opening/closing.\n     * If `false`, the menu will open/close instantly without animation.\n     * @param role The role of the element that is closing the menu.\n     */\n    setOpen(shouldOpen, animated = true, role) {\n        return menuController._setOpen(this, shouldOpen, animated, role);\n    }\n    trapKeyboardFocus(ev, doc) {\n        const target = ev.target;\n        if (!target) {\n            return;\n        }\n        /**\n         * If the target is inside the menu contents, let the browser\n         * focus as normal and keep a log of the last focused element.\n         */\n        if (this.el.contains(target)) {\n            this.lastFocus = target;\n        }\n        else {\n            /**\n             * Otherwise, we are about to have focus go out of the menu.\n             * Wrap the focus to either the first or last element.\n             */\n            const { el } = this;\n            /**\n             * Once we call `focusFirstDescendant`, another focus event\n             * will fire, which will cause `lastFocus` to be updated\n             * before we can run the code after that. We cache the value\n             * here to avoid that.\n             */\n            focusFirstDescendant(el);\n            /**\n             * If the cached last focused element is the same as the now-\n             * active element, that means the user was on the first element\n             * already and pressed Shift + Tab, so we need to wrap to the\n             * last descendant.\n             */\n            if (this.lastFocus === doc.activeElement) {\n                focusLastDescendant(el);\n            }\n        }\n    }\n    async _setOpen(shouldOpen, animated = true, role) {\n        // If the menu is disabled or it is currently being animated, let's do nothing\n        if (!this._isActive() || this.isAnimating || shouldOpen === this._isOpen) {\n            return false;\n        }\n        this.beforeAnimation(shouldOpen, role);\n        await this.loadAnimation();\n        await this.startAnimation(shouldOpen, animated);\n        /**\n         * If the animation was cancelled then\n         * return false because the operation\n         * did not succeed.\n         */\n        if (this.operationCancelled) {\n            this.operationCancelled = false;\n            return false;\n        }\n        this.afterAnimation(shouldOpen, role);\n        return true;\n    }\n    async loadAnimation() {\n        // Menu swipe animation takes the menu's inner width as parameter,\n        // If `offsetWidth` changes, we need to create a new animation.\n        const width = this.menuInnerEl.offsetWidth;\n        /**\n         * Menu direction animation is calculated based on the document direction.\n         * If the document direction changes, we need to create a new animation.\n         */\n        const isEndSide$1 = isEndSide(this.side);\n        if (width === this.width && this.animation !== undefined && isEndSide$1 === this.isEndSide) {\n            return;\n        }\n        this.width = width;\n        this.isEndSide = isEndSide$1;\n        // Destroy existing animation\n        if (this.animation) {\n            this.animation.destroy();\n            this.animation = undefined;\n        }\n        // Create new animation\n        const animation = (this.animation = await menuController._createAnimation(this.type, this));\n        if (!config.getBoolean('animated', true)) {\n            animation.duration(0);\n        }\n        animation.fill('both');\n    }\n    async startAnimation(shouldOpen, animated) {\n        const isReversed = !shouldOpen;\n        const mode = getIonMode(this);\n        const easing = mode === 'ios' ? iosEasing : mdEasing;\n        const easingReverse = mode === 'ios' ? iosEasingReverse : mdEasingReverse;\n        const ani = this.animation\n            .direction(isReversed ? 'reverse' : 'normal')\n            .easing(isReversed ? easingReverse : easing);\n        if (animated) {\n            await ani.play();\n        }\n        else {\n            ani.play({ sync: true });\n        }\n        /**\n         * We run this after the play invocation\n         * instead of using ani.onFinish so that\n         * multiple onFinish callbacks do not get\n         * run if an animation is played, stopped,\n         * and then played again.\n         */\n        if (ani.getDirection() === 'reverse') {\n            ani.direction('normal');\n        }\n    }\n    _isActive() {\n        return !this.disabled && !this.isPaneVisible;\n    }\n    canSwipe() {\n        return this.swipeGesture && !this.isAnimating && this._isActive();\n    }\n    canStart(detail) {\n        // Do not allow swipe gesture if a modal is open\n        const isModalPresented = !!document.querySelector('ion-modal.show-modal');\n        if (isModalPresented || !this.canSwipe()) {\n            return false;\n        }\n        if (this._isOpen) {\n            return true;\n        }\n        else if (menuController._getOpenSync()) {\n            return false;\n        }\n        return checkEdgeSide(window, detail.currentX, this.isEndSide, this.maxEdgeStart);\n    }\n    onWillStart() {\n        this.beforeAnimation(!this._isOpen, GESTURE);\n        return this.loadAnimation();\n    }\n    onStart() {\n        if (!this.isAnimating || !this.animation) {\n            assert(false, 'isAnimating has to be true');\n            return;\n        }\n        // the cloned animation should not use an easing curve during seek\n        this.animation.progressStart(true, this._isOpen ? 1 : 0);\n    }\n    onMove(detail) {\n        if (!this.isAnimating || !this.animation) {\n            assert(false, 'isAnimating has to be true');\n            return;\n        }\n        const delta = computeDelta(detail.deltaX, this._isOpen, this.isEndSide);\n        const stepValue = delta / this.width;\n        this.animation.progressStep(this._isOpen ? 1 - stepValue : stepValue);\n    }\n    onEnd(detail) {\n        if (!this.isAnimating || !this.animation) {\n            assert(false, 'isAnimating has to be true');\n            return;\n        }\n        const isOpen = this._isOpen;\n        const isEndSide = this.isEndSide;\n        const delta = computeDelta(detail.deltaX, isOpen, isEndSide);\n        const width = this.width;\n        const stepValue = delta / width;\n        const velocity = detail.velocityX;\n        const z = width / 2.0;\n        const shouldCompleteRight = velocity >= 0 && (velocity > 0.2 || detail.deltaX > z);\n        const shouldCompleteLeft = velocity <= 0 && (velocity < -0.2 || detail.deltaX < -z);\n        const shouldComplete = isOpen\n            ? isEndSide\n                ? shouldCompleteRight\n                : shouldCompleteLeft\n            : isEndSide\n                ? shouldCompleteLeft\n                : shouldCompleteRight;\n        let shouldOpen = !isOpen && shouldComplete;\n        if (isOpen && !shouldComplete) {\n            shouldOpen = true;\n        }\n        this.lastOnEnd = detail.currentTime;\n        // Account for rounding errors in JS\n        let newStepValue = shouldComplete ? 0.001 : -1e-3;\n        /**\n         * stepValue can sometimes return a negative\n         * value, but you can't have a negative time value\n         * for the cubic bezier curve (at least with web animations)\n         */\n        const adjustedStepValue = stepValue < 0 ? 0.01 : stepValue;\n        /**\n         * Animation will be reversed here, so need to\n         * reverse the easing curve as well\n         *\n         * Additionally, we need to account for the time relative\n         * to the new easing curve, as `stepValue` is going to be given\n         * in terms of a linear curve.\n         */\n        newStepValue +=\n            getTimeGivenProgression([0, 0], [0.4, 0], [0.6, 1], [1, 1], clamp(0, adjustedStepValue, 0.9999))[0] || 0;\n        const playTo = this._isOpen ? !shouldComplete : shouldComplete;\n        this.animation\n            .easing('cubic-bezier(0.4, 0.0, 0.6, 1)')\n            .onFinish(() => this.afterAnimation(shouldOpen, GESTURE), { oneTimeCallback: true })\n            .progressEnd(playTo ? 1 : 0, this._isOpen ? 1 - newStepValue : newStepValue, 300);\n    }\n    beforeAnimation(shouldOpen, role) {\n        assert(!this.isAnimating, '_before() should not be called while animating');\n        /**\n         * When the menu is presented on an Android device, TalkBack's focus rings\n         * may appear in the wrong position due to the transition (specifically\n         * `transform` styles). This occurs because the focus rings are initially\n         * displayed at the starting position of the elements before the transition\n         * begins. This workaround ensures the focus rings do not appear in the\n         * incorrect location.\n         *\n         * If this solution is applied to iOS devices, then it leads to a bug where\n         * the overlays cannot be accessed by screen readers. This is due to\n         * VoiceOver not being able to update the accessibility tree when the\n         * `aria-hidden` is removed.\n         */\n        if (isPlatform('android')) {\n            this.el.setAttribute('aria-hidden', 'true');\n        }\n        // this places the menu into the correct location before it animates in\n        // this css class doesn't actually kick off any animations\n        this.el.classList.add(SHOW_MENU);\n        /**\n         * We add a tabindex here so that focus trapping\n         * still works even if the menu does not have\n         * any focusable elements slotted inside. The\n         * focus trapping utility will fallback to focusing\n         * the menu so focus does not leave when the menu\n         * is open.\n         */\n        this.el.setAttribute('tabindex', '0');\n        if (this.backdropEl) {\n            this.backdropEl.classList.add(SHOW_BACKDROP);\n        }\n        // add css class and hide content behind menu from screen readers\n        if (this.contentEl) {\n            this.contentEl.classList.add(MENU_CONTENT_OPEN);\n            /**\n             * When the menu is open and overlaying the main\n             * content, the main content should not be announced\n             * by the screenreader as the menu is the main\n             * focus. This is useful with screenreaders that have\n             * \"read from top\" gestures that read the entire\n             * page from top to bottom when activated.\n             * This should be done before the animation starts\n             * so that users cannot accidentally scroll\n             * the content while dragging a menu open.\n             */\n            this.contentEl.setAttribute('aria-hidden', 'true');\n        }\n        this.blocker.block();\n        this.isAnimating = true;\n        if (shouldOpen) {\n            this.ionWillOpen.emit();\n        }\n        else {\n            this.ionWillClose.emit({ role });\n        }\n    }\n    afterAnimation(isOpen, role) {\n        var _a;\n        // keep opening/closing the menu disabled for a touch more yet\n        // only add listeners/css if it's enabled and isOpen\n        // and only remove listeners/css if it's not open\n        // emit opened/closed events\n        this._isOpen = isOpen;\n        this.isAnimating = false;\n        if (!this._isOpen) {\n            this.blocker.unblock();\n        }\n        if (isOpen) {\n            /**\n             * When the menu is presented on an Android device, TalkBack's focus rings\n             * may appear in the wrong position due to the transition (specifically\n             * `transform` styles). The menu is hidden from screen readers during the\n             * transition to prevent this. Once the transition is complete, the menu\n             * is shown again.\n             */\n            if (isPlatform('android')) {\n                this.el.removeAttribute('aria-hidden');\n            }\n            // emit open event\n            this.ionDidOpen.emit();\n            /**\n             * Move focus to the menu to prepare focus trapping, as long as\n             * it isn't already focused. Use the host element instead of the\n             * first descendant to avoid the scroll position jumping around.\n             */\n            const focusedMenu = (_a = document.activeElement) === null || _a === void 0 ? void 0 : _a.closest('ion-menu');\n            if (focusedMenu !== this.el) {\n                this.el.focus();\n            }\n            // start focus trapping\n            document.addEventListener('focus', this.handleFocus, true);\n        }\n        else {\n            this.el.removeAttribute('aria-hidden');\n            // remove css classes and unhide content from screen readers\n            this.el.classList.remove(SHOW_MENU);\n            /**\n             * Remove tabindex from the menu component\n             * so that is cannot be tabbed to.\n             */\n            this.el.removeAttribute('tabindex');\n            if (this.contentEl) {\n                this.contentEl.classList.remove(MENU_CONTENT_OPEN);\n                /**\n                 * Remove aria-hidden so screen readers\n                 * can announce the main content again\n                 * now that the menu is not the main focus.\n                 */\n                this.contentEl.removeAttribute('aria-hidden');\n            }\n            if (this.backdropEl) {\n                this.backdropEl.classList.remove(SHOW_BACKDROP);\n            }\n            if (this.animation) {\n                this.animation.stop();\n            }\n            // emit close event\n            this.ionDidClose.emit({ role });\n            // undo focus trapping so multiple menus don't collide\n            document.removeEventListener('focus', this.handleFocus, true);\n        }\n    }\n    updateState() {\n        const isActive = this._isActive();\n        if (this.gesture) {\n            this.gesture.enable(isActive && this.swipeGesture);\n        }\n        /**\n         * If the menu is disabled but it is still open\n         * then we should close the menu immediately.\n         * Additionally, if the menu is in the process\n         * of animating {open, close} and the menu is disabled\n         * then it should still be closed immediately.\n         */\n        if (!isActive) {\n            /**\n             * It is possible to disable the menu while\n             * it is mid-animation. When this happens, we\n             * need to set the operationCancelled flag\n             * so that this._setOpen knows to return false\n             * and not run the \"afterAnimation\" callback.\n             */\n            if (this.isAnimating) {\n                this.operationCancelled = true;\n            }\n            /**\n             * If the menu is disabled then we should\n             * forcibly close the menu even if it is open.\n             */\n            this.afterAnimation(false, GESTURE);\n        }\n    }\n    render() {\n        const { type, disabled, el, isPaneVisible, inheritedAttributes, side } = this;\n        const mode = getIonMode(this);\n        /**\n         * If the Close Watcher is enabled then\n         * the ionBackButton listener in the menu controller\n         * will handle closing the menu when Escape is pressed.\n         */\n        return (h(Host, { key: 'a5c75aa40a34530b56ee3b98d706a5ac5ae300de', onKeyDown: shouldUseCloseWatcher() ? null : this.onKeydown, role: \"navigation\", \"aria-label\": inheritedAttributes['aria-label'] || 'menu', class: {\n                [mode]: true,\n                [`menu-type-${type}`]: true,\n                'menu-enabled': !disabled,\n                [`menu-side-${side}`]: true,\n                'menu-pane-visible': isPaneVisible,\n                'split-pane-side': hostContext('ion-split-pane', el),\n            } }, h(\"div\", { key: '3f5f70acd4d3ed6bb445122f4f01d73db738a75f', class: \"menu-inner\", part: \"container\", ref: (el) => (this.menuInnerEl = el) }, h(\"slot\", { key: '3161326c9330e7f7441299c428b87a91b31a83e9' })), h(\"ion-backdrop\", { key: '917b50f38489bdf03d0c642af8b4e4e172c7dc4c', ref: (el) => (this.backdropEl = el), class: \"menu-backdrop\", tappable: false, stopPropagation: false, part: \"backdrop\" })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"type\": [\"typeChanged\"],\n        \"disabled\": [\"disabledChanged\"],\n        \"side\": [\"sideChanged\"],\n        \"swipeGesture\": [\"swipeGestureChanged\"]\n    }; }\n};\nconst computeDelta = (deltaX, isOpen, isEndSide) => {\n    return Math.max(0, isOpen !== isEndSide ? -deltaX : deltaX);\n};\nconst checkEdgeSide = (win, posX, isEndSide, maxEdgeStart) => {\n    if (isEndSide) {\n        return posX >= win.innerWidth - maxEdgeStart;\n    }\n    else {\n        return posX <= maxEdgeStart;\n    }\n};\nconst SHOW_MENU = 'show-menu';\nconst SHOW_BACKDROP = 'show-backdrop';\nconst MENU_CONTENT_OPEN = 'menu-content-open';\nMenu.style = {\n    ios: menuIosCss,\n    md: menuMdCss\n};\n\n// Given a menu, return whether or not the menu toggle should be visible\nconst updateVisibility = async (menu) => {\n    const menuEl = await menuController.get(menu);\n    return !!(menuEl && (await menuEl.isActive()));\n};\n\nconst menuButtonIosCss = \":host{--background:transparent;--color-focused:currentColor;--border-radius:initial;--padding-top:0;--padding-bottom:0;color:var(--color);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;z-index:1}ion-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;pointer-events:none}:host(.menu-button-hidden){display:none}:host(.menu-button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity, 0)}}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-focused:currentColor;--background-focused-opacity:.1;--border-radius:4px;--color:var(--ion-color-primary, #0054e9);--padding-start:5px;--padding-end:5px;min-height:32px;font-size:clamp(31px, 1.9375rem, 38.13px)}:host(.ion-activated){opacity:0.4}@media (any-hover: hover){:host(:hover){opacity:0.6}}\";\n\nconst menuButtonMdCss = \":host{--background:transparent;--color-focused:currentColor;--border-radius:initial;--padding-top:0;--padding-bottom:0;color:var(--color);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;z-index:1}ion-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;pointer-events:none}:host(.menu-button-hidden){display:none}:host(.menu-button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity, 0)}}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-focused:currentColor;--background-focused-opacity:.12;--background-hover:currentColor;--background-hover-opacity:.04;--border-radius:50%;--color:initial;--padding-start:8px;--padding-end:8px;width:3rem;height:3rem;font-size:1.5rem}:host(.ion-color.ion-focused)::after{background:var(--ion-color-base)}@media (any-hover: hover){:host(.ion-color:hover) .button-native::after{background:var(--ion-color-base)}}\";\n\nconst MenuButton = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.inheritedAttributes = {};\n        this.visible = false;\n        /**\n         * If `true`, the user cannot interact with the menu button.\n         */\n        this.disabled = false;\n        /**\n         * Automatically hides the menu button when the corresponding menu is not active\n         */\n        this.autoHide = true;\n        /**\n         * The type of the button.\n         */\n        this.type = 'button';\n        this.onClick = async () => {\n            return menuController.toggle(this.menu);\n        };\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    componentDidLoad() {\n        this.visibilityChanged();\n    }\n    async visibilityChanged() {\n        this.visible = await updateVisibility(this.menu);\n    }\n    render() {\n        const { color, disabled, inheritedAttributes } = this;\n        const mode = getIonMode(this);\n        const menuIcon = config.get('menuIcon', mode === 'ios' ? menuOutline : menuSharp);\n        const hidden = this.autoHide && !this.visible;\n        const attrs = {\n            type: this.type,\n        };\n        const ariaLabel = inheritedAttributes['aria-label'] || 'menu';\n        return (h(Host, { key: '9f0f0e50d39a6872508220c58e64bb2092a0d7ef', onClick: this.onClick, \"aria-disabled\": disabled ? 'true' : null, \"aria-hidden\": hidden ? 'true' : null, class: createColorClasses(color, {\n                [mode]: true,\n                button: true, // ion-buttons target .button\n                'menu-button-hidden': hidden,\n                'menu-button-disabled': disabled,\n                'in-toolbar': hostContext('ion-toolbar', this.el),\n                'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n                'ion-activatable': true,\n                'ion-focusable': true,\n            }) }, h(\"button\", Object.assign({ key: 'ffebf7083d23501839970059ef8e411b571de197' }, attrs, { disabled: disabled, class: \"button-native\", part: \"native\", \"aria-label\": ariaLabel }), h(\"span\", { key: 'cab0c1c763b3ce33ef11dba1d230f66126e59424', class: \"button-inner\" }, h(\"slot\", { key: 'ccfd2be8479b75b5c63e97e1ca7dfe203e9b36ee' }, h(\"ion-icon\", { key: 'ac254fe7f327b08f1ae3fcea89d5cf0e83c9a96c', part: \"icon\", icon: menuIcon, mode: mode, lazy: false, \"aria-hidden\": \"true\" }))), mode === 'md' && h(\"ion-ripple-effect\", { key: 'f0f17c4ca96e3eed3c1727ee00578d40af8f0115', type: \"unbounded\" }))));\n    }\n    get el() { return getElement(this); }\n};\nMenuButton.style = {\n    ios: menuButtonIosCss,\n    md: menuButtonMdCss\n};\n\nconst menuToggleCss = \":host(.menu-toggle-hidden){display:none}\";\n\nconst MenuToggle = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.visible = false;\n        /**\n         * Automatically hides the content when the corresponding menu is not active.\n         *\n         * By default, it's `true`. Change it to `false` in order to\n         * keep `ion-menu-toggle` always visible regardless the state of the menu.\n         */\n        this.autoHide = true;\n        this.onClick = () => {\n            return menuController.toggle(this.menu);\n        };\n    }\n    connectedCallback() {\n        this.visibilityChanged();\n    }\n    async visibilityChanged() {\n        this.visible = await updateVisibility(this.menu);\n    }\n    render() {\n        const mode = getIonMode(this);\n        const hidden = this.autoHide && !this.visible;\n        return (h(Host, { key: 'cd567114769a30bd3871ed5d15bf42aed39956e1', onClick: this.onClick, \"aria-hidden\": hidden ? 'true' : null, class: {\n                [mode]: true,\n                'menu-toggle-hidden': hidden,\n            } }, h(\"slot\", { key: '773d4cff95ca75f23578b1e1dca53c9933f28a33' })));\n    }\n};\nMenuToggle.style = menuToggleCss;\n\nexport { Menu as ion_menu, MenuButton as ion_menu_button, MenuToggle as ion_menu_toggle };\n"], "names": ["r", "registerInstance", "d", "createEvent", "l", "config", "o", "printIonError", "e", "getIonMode", "a", "isPlatform", "h", "j", "Host", "k", "getElement", "g", "getTimeGivenProgression", "getPresentedOverlay", "B", "BACKDROP", "n", "focusFirstDescendant", "q", "focusLastDescendant", "G", "GESTURE", "GESTURE_CONTROLLER", "shouldUseCloseWatcher", "isEndSide", "i", "inheritAriaAttributes", "assert", "clamp", "m", "menuController", "hostContext", "c", "createColorClasses", "u", "menuOutline", "v", "menuSharp", "menuIosCss", "menuMdCss", "iosEasing", "mdEasing", "iosEasingReverse", "mdEasingReverse", "<PERSON><PERSON>", "constructor", "hostRef", "ionWillOpen", "ionWillClose", "ionDidOpen", "ionDidClose", "ionMenuChange", "lastOnEnd", "blocker", "createBlocker", "disableScroll", "didLoad", "operationCancelled", "isAnimating", "_isOpen", "inheritedAttributes", "handleFocus", "ev", "lastOverlay", "document", "contains", "el", "trapKeyboardFocus", "isPaneVisible", "disabled", "side", "swipeGesture", "maxEdgeStart", "typeChanged", "type", "oldType", "contentEl", "undefined", "classList", "remove", "add", "removeAttribute", "menuInnerEl", "animation", "disabled<PERSON><PERSON>ed", "updateState", "emit", "open", "sideChanged", "swipeGestureChanged", "connectedCallback", "_this", "_asyncToGenerator", "customElements", "whenDefined", "get", "content", "contentId", "getElementById", "_register", "menuChanged", "gesture", "createGesture", "<PERSON><PERSON><PERSON>", "gesturePriority", "threshold", "blurOnStart", "canStart", "onWillStart", "onStart", "onMove", "onEnd", "componentWillLoad", "componentDidLoad", "_this2", "splitPane", "closest", "isVisible", "disconnectedCallback", "_this3", "close", "destroy", "_unregister", "onSplitPaneChanged", "closestSplitPane", "target", "detail", "visible", "onBackdropClick", "timeStamp", "shouldClose", "<PERSON><PERSON><PERSON>", "includes", "preventDefault", "stopPropagation", "onKeydown", "key", "isOpen", "Promise", "resolve", "isActive", "_isActive", "animated", "<PERSON><PERSON><PERSON>", "role", "toggle", "shouldOpen", "_setOpen", "doc", "lastFocus", "activeElement", "_x", "_this4", "beforeAnimation", "loadAnimation", "startAnimation", "afterAnimation", "apply", "arguments", "_this5", "width", "offsetWidth", "isEndSide$1", "_createAnimation", "getBoolean", "duration", "fill", "_this6", "isReversed", "mode", "easing", "easingReverse", "ani", "direction", "play", "sync", "getDirection", "canSwipe", "isModalPresented", "querySelector", "_getOpenSync", "checkEdgeSide", "window", "currentX", "progressStart", "delta", "computeDelta", "deltaX", "<PERSON><PERSON><PERSON><PERSON>", "progressStep", "velocity", "velocityX", "z", "shouldCompleteRight", "shouldCompleteLeft", "shouldComplete", "currentTime", "newStepValue", "adjustedStepValue", "playTo", "onFinish", "oneTimeCallback", "progressEnd", "setAttribute", "SHOW_MENU", "backdropEl", "SHOW_BACKDROP", "MENU_CONTENT_OPEN", "block", "_a", "unblock", "focusedMenu", "focus", "addEventListener", "stop", "removeEventListener", "enable", "render", "onKeyDown", "class", "part", "ref", "tappable", "watchers", "Math", "max", "win", "posX", "innerWidth", "style", "ios", "md", "updateVisibility", "_ref", "menu", "menuEl", "_x2", "menuButtonIosCss", "menuButtonMdCss", "MenuButton", "_this7", "autoHide", "onClick", "visibilityChanged", "_this8", "color", "menuIcon", "hidden", "attrs", "aria<PERSON><PERSON><PERSON>", "button", "Object", "assign", "icon", "lazy", "menuToggleCss", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_this9", "ion_menu", "ion_menu_button", "ion_menu_toggle"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}