"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[6521],{6521:(y,a,s)=>{s.r(a),s.d(a,{ion_input_password_toggle:()=>h});var n=s(2734),d=s(4576),l=s(5514);const h=(()=>{let u=class{constructor(t){(0,n.r)(this,t),this.type="password",this.togglePasswordVisibility=()=>{const{inputElRef:e}=this;e&&(e.type="text"===e.type?"password":"text")}}onTypeChange(t){"text"===t||"password"===t||(0,n.m)(`[ion-input-password-toggle] - Only inputs of type "text" or "password" are supported. Input of type "${t}" is not compatible.`,this.el)}connectedCallback(){const{el:t}=this,e=this.inputElRef=t.closest("ion-input");e?this.type=e.type:(0,n.m)("[ion-input-password-toggle] - No ancestor ion-input found. This component must be slotted inside of an ion-input.",t)}disconnectedCallback(){this.inputElRef=null}render(){var t,e;const{color:o,type:c}=this,r=(0,n.e)(this),i=null!==(t=this.showIcon)&&void 0!==t?t:l.y,g=null!==(e=this.hideIcon)&&void 0!==e?e:l.x,f="text"===c;return(0,n.h)(n.j,{key:"91bc55664d496fe457518bd112865dd7811d0c17",class:(0,d.c)(o,{[r]:!0})},(0,n.h)("ion-button",{key:"f3e436422110c9cb4d5c0b83500255b24ab4cdef",mode:r,color:o,fill:"clear",shape:"round","aria-checked":f?"true":"false","aria-label":f?"Hide password":"Show password",role:"switch",type:"button",onPointerDown:b=>{b.preventDefault()},onClick:this.togglePasswordVisibility},(0,n.h)("ion-icon",{key:"5c8b121153f148f92aa7cba0447673a4f6f3ad1e",slot:"icon-only","aria-hidden":"true",icon:f?g:i})))}get el(){return(0,n.k)(this)}static get watchers(){return{type:["onTypeChange"]}}};return u.style={ios:"",md:""},u})()},4576:(y,a,s)=>{s.d(a,{c:()=>l,g:()=>_,h:()=>d,o:()=>u});var n=s(467);const d=(t,e)=>null!==e.closest(t),l=(t,e)=>"string"==typeof t&&t.length>0?Object.assign({"ion-color":!0,[`ion-color-${t}`]:!0},e):e,_=t=>{const e={};return(t=>void 0!==t?(Array.isArray(t)?t:t.split(" ")).filter(o=>null!=o).map(o=>o.trim()).filter(o=>""!==o):[])(t).forEach(o=>e[o]=!0),e},h=/^[a-z][a-z0-9+\-.]*:/,u=function(){var t=(0,n.A)(function*(e,o,c,r){if(null!=e&&"#"!==e[0]&&!h.test(e)){const i=document.querySelector("ion-router");if(i)return o?.preventDefault(),i.push(e,c,r)}return!1});return function(o,c,r,i){return t.apply(this,arguments)}}()}}]);