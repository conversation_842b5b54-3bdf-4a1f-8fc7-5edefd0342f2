{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-item_8_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC6J;AACpF;AACqB;AACpC;AAE1D,MAAMyB,UAAU,GAAG,qjSAAqjS;AAExkS,MAAMC,SAAS,GAAG,0gUAA0gU;AAE5hU,MAAMC,IAAI,GAAG,MAAM;EACfC,WAAWA,CAACC,OAAO,EAAE;IACjB5B,qDAAgB,CAAC,IAAI,EAAE4B,OAAO,CAAC;IAC/B,IAAI,CAACC,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC3B,IAAI,CAACC,uBAAuB,GAAG,CAAC,CAAC;IACjC,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B;AACR;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB;AACR;AACA;IACQ,IAAI,CAACC,UAAU,GAAGd,iDAAc;IAChC;AACR;AACA;IACQ,IAAI,CAACe,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,SAAS;IAChC;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB;IACA,IAAI,CAACC,+BAA+B,GAAG,MAAM;MACzC,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC5B,CAAC;EACL;EACAC,aAAaA,CAAA,EAAG;IACZ;IACA,IAAI,CAACV,SAAS,GAAG,IAAI,CAACW,WAAW,CAAC,CAAC;EACvC;EACAC,iBAAiBA,CAACC,EAAE,EAAE;IAClB,MAAM;MAAEC;IAAM,CAAC,GAAG,IAAI;IACtB;IACA;IACA;IACA,IAAIA,KAAK,KAAKC,SAAS,EAAE;MACrB,IAAI,CAACpB,gBAAgB,GAAGkB,EAAE,CAACG,MAAM;IACrC;EACJ;EACAC,SAASA,CAACJ,EAAE,EAAE;IACVA,EAAE,CAACK,eAAe,CAAC,CAAC;IACpB,MAAMC,OAAO,GAAGN,EAAE,CAACO,MAAM,CAACD,OAAO;IACjC,MAAME,aAAa,GAAGR,EAAE,CAACG,MAAM;IAC/B,MAAMM,SAAS,GAAG,CAAC,CAAC;IACpB,MAAMC,WAAW,GAAG,IAAI,CAAC3B,UAAU,CAAC4B,GAAG,CAACL,OAAO,CAAC,IAAI,CAAC,CAAC;IACtD,IAAIM,cAAc,GAAG,KAAK;IAC1BC,MAAM,CAACC,IAAI,CAACN,aAAa,CAAC,CAACO,OAAO,CAAEC,GAAG,IAAK;MACxC,IAAIR,aAAa,CAACQ,GAAG,CAAC,EAAE;QACpB,MAAMC,OAAO,GAAG,QAAQD,GAAG,EAAE;QAC7B,IAAI,CAACN,WAAW,CAACO,OAAO,CAAC,EAAE;UACvBL,cAAc,GAAG,IAAI;QACzB;QACAH,SAAS,CAACQ,OAAO,CAAC,GAAG,IAAI;MAC7B;IACJ,CAAC,CAAC;IACF,IAAI,CAACL,cAAc,IAAIC,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,CAACS,MAAM,KAAKL,MAAM,CAACC,IAAI,CAACJ,WAAW,CAAC,CAACQ,MAAM,EAAE;MACtFN,cAAc,GAAG,IAAI;IACzB;IACA,IAAIA,cAAc,EAAE;MAChB,IAAI,CAAC7B,UAAU,CAACoC,GAAG,CAACb,OAAO,EAAEG,SAAS,CAAC;MACvCtD,qDAAW,CAAC,IAAI,CAAC;IACrB;EACJ;EACAiE,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACrC,uBAAuB,GAAGjB,uDAAiB,CAAC,IAAI,CAACuD,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC;EAC7E;EACAC,gBAAgBA,CAAA,EAAG;IACfvD,uDAAG,CAAC,MAAM;MACN,IAAI,CAAC2B,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACD,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACR,SAAS,GAAG,IAAI,CAACW,WAAW,CAAC,CAAC;IACvC,CAAC,CAAC;EACN;EACA2B,iBAAiBA,CAAA,EAAG;IAChB;IACA,MAAMC,MAAM,GAAG,IAAI,CAACH,EAAE,CAACI,gBAAgB,CAAC,mDAAmD,CAAC;IAC5F;IACA;IACA;IACA,MAAMC,MAAM,GAAG,IAAI,CAACL,EAAE,CAACI,gBAAgB,CAAC,4EAA4E,CAAC;IACrH;IACA,MAAME,UAAU,GAAG,IAAI,CAACN,EAAE,CAACI,gBAAgB,CAAC,wCAAwC,CAAC;IACrF,OAAO;MACHD,MAAM;MACNE,MAAM;MACNC;IACJ,CAAC;EACL;EACA;EACA;EACA;EACAjC,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAE8B,MAAM;MAAEE,MAAM;MAAEC;IAAW,CAAC,GAAG,IAAI,CAACJ,iBAAiB,CAAC,CAAC;IAC/D;IACA;IACA,IAAI,CAACvC,cAAc,GACfwC,MAAM,CAACR,MAAM,GAAGU,MAAM,CAACV,MAAM,GAAG,CAAC,IAC7BQ,MAAM,CAACR,MAAM,GAAGW,UAAU,CAACX,MAAM,GAAG,CAAC,IACpCQ,MAAM,CAACR,MAAM,GAAG,CAAC,IAAI,IAAI,CAACY,WAAW,CAAC,CAAE;EACrD;EACAnC,gBAAgBA,CAAA,EAAG;IACf;IACA,MAAM;MAAE+B,MAAM;MAAEE,MAAM;MAAEC;IAAW,CAAC,GAAG,IAAI,CAACJ,iBAAiB,CAAC,CAAC;IAC/D,IAAI,CAACrC,aAAa,GAAGsC,MAAM,CAACR,MAAM,GAAG,CAAC,IAAIU,MAAM,CAACV,MAAM,GAAG,CAAC,IAAIW,UAAU,CAACX,MAAM,GAAG,CAAC;EACxF;EACA;EACA;EACA;EACA;EACAa,QAAQA,CAAA,EAAG;IACP,MAAMH,MAAM,GAAG,IAAI,CAACL,EAAE,CAACI,gBAAgB,CAAC,mDAAmD,CAAC;IAC5F,OAAOC,MAAM,CAACV,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAChC,cAAc;EACtD;EACA;EACA;EACA4C,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACE,IAAI,KAAK9B,SAAS,IAAI,IAAI,CAACb,MAAM;EACjD;EACA4C,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACH,WAAW,CAAC,CAAC,IAAI,IAAI,CAACC,QAAQ,CAAC,CAAC;EAChD;EACAjC,WAAWA,CAAA,EAAG;IACV,MAAMoC,cAAc,GAAG,IAAI,CAACX,EAAE,CAACY,aAAa,CAAC,gBAAgB,CAAC;IAC9D,OAAO,IAAI,CAACF,WAAW,CAAC,CAAC,IAAIC,cAAc,KAAK,IAAI;EACxD;EACAb,UAAUA,CAAA,EAAG;IACT,MAAMe,OAAO,GAAG,IAAI,CAACb,EAAE,CAACY,aAAa,CAAC,gBAAgB,CAAC;IACvD,IAAIC,OAAO,KAAK,IAAI,EAAE;MAClB,IAAI,CAACb,EAAE,CAACc,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAChD;EACJ;EACAC,mBAAmBA,CAAA,EAAG;IAClB,MAAMC,QAAQ,GAAG,IAAI,CAACjB,EAAE,CAACI,gBAAgB,CAAC,0KAA0K,CAAC;IACrN,OAAOa,QAAQ,CAAC,CAAC,CAAC;EACtB;EACAC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEtC,MAAM;MAAEb,UAAU;MAAEoD,QAAQ;MAAE5D,gBAAgB;MAAE6D,KAAK;MAAEpD,QAAQ;MAAEyC,IAAI;MAAEY,GAAG;MAAErC,MAAM;MAAEsC,eAAe;MAAErD,eAAe;MAAEP,uBAAuB;MAAEC;IAAgB,CAAC,GAAG,IAAI;IAC/K,MAAMwB,WAAW,GAAG,CAAC,CAAC;IACtB,MAAMoC,IAAI,GAAGzF,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAM0F,SAAS,GAAG,IAAI,CAACjB,WAAW,CAAC,CAAC;IACpC,MAAMG,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC,CAAC;IACtC,MAAMe,OAAO,GAAGD,SAAS,GAAIf,IAAI,KAAK9B,SAAS,GAAG,QAAQ,GAAG,GAAG,GAAI,KAAK;IACzE,MAAM+C,KAAK,GAAGD,OAAO,KAAK,QAAQ,GAC5B;MAAEvD,IAAI,EAAE,IAAI,CAACA;IAAK,CAAC,GACnB;MACEiD,QAAQ;MACRV,IAAI;MACJY,GAAG;MACHrC;IACJ,CAAC;IACL,IAAI2C,OAAO,GAAG,CAAC,CAAC;IAChB,MAAMC,gBAAgB,GAAG,IAAI,CAACZ,mBAAmB,CAAC,CAAC;IACnD;IACA;IACA,IAAIQ,SAAS,IAAKI,gBAAgB,KAAKjD,SAAS,IAAI,CAAChB,cAAe,EAAE;MAClEgE,OAAO,GAAG;QACNE,OAAO,EAAGpD,EAAE,IAAK;UACb,IAAI+C,SAAS,EAAE;YACXzE,qDAAO,CAAC0D,IAAI,EAAEhC,EAAE,EAAER,eAAe,EAAEqD,eAAe,CAAC;UACvD;UACA,IAAIM,gBAAgB,KAAKjD,SAAS,IAAI,CAAChB,cAAc,EAAE;YACnD,MAAMmE,IAAI,GAAGrD,EAAE,CAACsD,YAAY,CAAC,CAAC;YAC9B,MAAM/C,MAAM,GAAG8C,IAAI,CAAC,CAAC,CAAC;YACtB,IAAIrD,EAAE,CAACuD,SAAS,EAAE;cACd;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;cAC4B,MAAMC,uBAAuB,GAAG,IAAI,CAACjC,EAAE,CAACkC,UAAU,CAACC,QAAQ,CAACnD,MAAM,CAAC;cACnE,IAAIiD,uBAAuB,EAAE;gBACzB;AAChC;AACA;AACA;AACA;gBACgC,IAAIL,gBAAgB,CAAC7C,OAAO,KAAK,WAAW,IAAI6C,gBAAgB,CAAC7C,OAAO,KAAK,cAAc,EAAE;kBACzF6C,gBAAgB,CAACQ,QAAQ,CAAC,CAAC;gBAC/B;gBACAR,gBAAgB,CAACS,KAAK,CAAC,CAAC;gBACxB;AAChC;AACA;AACA;AACA;gBACgC5D,EAAE,CAAC6D,wBAAwB,CAAC,CAAC;cACjC;YACJ;UACJ;QACJ;MACJ,CAAC;IACL;IACA,MAAMC,UAAU,GAAG3D,MAAM,KAAKD,SAAS,GAAGC,MAAM,GAAG2C,IAAI,KAAK,KAAK,IAAIC,SAAS;IAC9E,IAAI,CAAChE,UAAU,CAACgC,OAAO,CAAEgD,KAAK,IAAK;MAC/BlD,MAAM,CAACmD,MAAM,CAACtD,WAAW,EAAEqD,KAAK,CAAC;IACrC,CAAC,CAAC;IACF,MAAME,YAAY,GAAG1E,QAAQ,IAAImB,WAAW,CAAC,2BAA2B,CAAC,GAAG,MAAM,GAAG,IAAI;IACzF,MAAMwD,MAAM,GAAGhG,qDAAW,CAAC,UAAU,EAAE,IAAI,CAACqD,EAAE,CAAC,IAAI,CAACrD,qDAAW,CAAC,iBAAiB,EAAE,IAAI,CAACqD,EAAE,CAAC;IAC3F;AACR;AACA;AACA;IACQ,MAAM4C,kCAAkC,GAAGhB,gBAAgB,KAAKjD,SAAS,IAAI,CAAC,CAAC,WAAW,EAAE,cAAc,CAAC,CAACkE,QAAQ,CAACjB,gBAAgB,CAAC7C,OAAO,CAAC;IAC9I,OAAQhD,qDAAC,CAACE,iDAAI,EAAE;MAAEwD,GAAG,EAAE,0CAA0C;MAAE,eAAe,EAAEiD,YAAY;MAAEI,KAAK,EAAExD,MAAM,CAACmD,MAAM,CAACnD,MAAM,CAACmD,MAAM,CAACnD,MAAM,CAACmD,MAAM,CAAC,CAAC,CAAC,EAAEtD,WAAW,CAAC,EAAE5B,gBAAgB,CAAC,EAAEV,qDAAkB,CAAC,IAAI,CAAC6B,KAAK,EAAE;QAC/MqE,IAAI,EAAE,IAAI;QACV,CAACxB,IAAI,GAAG,IAAI;QACZ,oBAAoB,EAAEH,KAAK,KAAKzC,SAAS;QACzC,CAAC,cAAcyC,KAAK,EAAE,GAAGA,KAAK,KAAKzC,SAAS;QAC5C,mCAAmC,EAAEiE,kCAAkC;QACvE,eAAe,EAAE5E,QAAQ;QACzB,SAAS,EAAE2E,MAAM;QACjB,sBAAsB,EAAE,IAAI,CAAChF,cAAc;QAC3C,iBAAiB,EAAE+C,WAAW;QAC9B,eAAe,EAAE,IAAI,CAAC9C,SAAS;QAC/B,UAAU,EAAEoF,QAAQ,CAACC,GAAG,KAAK;MACjC,CAAC,CAAC,CAAC;MAAEC,IAAI,EAAEP,MAAM,GAAG,UAAU,GAAG;IAAK,CAAC,EAAE5G,qDAAC,CAAC0F,OAAO,EAAEnC,MAAM,CAACmD,MAAM,CAAC;MAAEhD,GAAG,EAAE;IAA2C,CAAC,EAAEiC,KAAK,EAAEhE,uBAAuB,EAAE;MAAEoF,KAAK,EAAE,aAAa;MAAEK,IAAI,EAAE,QAAQ;MAAEnF,QAAQ,EAAEA;IAAS,CAAC,EAAE2D,OAAO,CAAC,EAAE5F,qDAAC,CAAC,MAAM,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAE2D,IAAI,EAAE,OAAO;MAAEC,YAAY,EAAE,IAAI,CAAClF;IAAgC,CAAC,CAAC,EAAEpC,qDAAC,CAAC,KAAK,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAEqD,KAAK,EAAE;IAAa,CAAC,EAAE/G,qDAAC,CAAC,KAAK,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAEqD,KAAK,EAAE;IAAgB,CAAC,EAAE/G,qDAAC,CAAC,MAAM,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAE4D,YAAY,EAAE,IAAI,CAAClF;IAAgC,CAAC,CAAC,CAAC,EAAEpC,qDAAC,CAAC,MAAM,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAE2D,IAAI,EAAE,KAAK;MAAEC,YAAY,EAAE,IAAI,CAAClF;IAAgC,CAAC,CAAC,EAAEoE,UAAU,IAAKxG,qDAAC,CAAC,UAAU,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAE6D,IAAI,EAAEvF,UAAU;MAAEwF,IAAI,EAAE,KAAK;MAAET,KAAK,EAAE,kBAAkB;MAAEK,IAAI,EAAE,aAAa;MAAE,aAAa,EAAE,MAAM;MAAE,UAAU,EAAEpF,UAAU,KAAKd,iDAAcA;IAAC,CAAC,CAAE,CAAC,EAAEyD,WAAW,IAAIa,IAAI,KAAK,IAAI,IAAIxF,qDAAC,CAAC,mBAAmB,EAAE;MAAE0D,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,CAAC;EAC5lC;EACA,IAAIO,EAAEA,CAAA,EAAG;IAAE,OAAO7D,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWqH,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,QAAQ,EAAE,CAAC,eAAe;IAC9B,CAAC;EAAE;AACP,CAAC;AACDpG,IAAI,CAACqG,KAAK,GAAG;EACTC,GAAG,EAAExG,UAAU;EACfyG,EAAE,EAAExG;AACR,CAAC;AAED,MAAMyG,iBAAiB,GAAG,wkHAAwkH;AAElmH,MAAMC,gBAAgB,GAAG,o+IAAo+I;AAE7/I,MAAMC,WAAW,GAAG,MAAM;EACtBzG,WAAWA,CAACC,OAAO,EAAE;IACjB5B,qDAAgB,CAAC,IAAI,EAAE4B,OAAO,CAAC;IAC/B;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACyG,MAAM,GAAG,KAAK;EACvB;EACA7C,MAAMA,CAAA,EAAG;IACL,MAAMK,IAAI,GAAGzF,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,qDAAC,CAACE,iDAAI,EAAE;MAAEwD,GAAG,EAAE,0CAA0C;MAAEqD,KAAK,EAAEjG,qDAAkB,CAAC,IAAI,CAAC6B,KAAK,EAAE;QACjG,CAAC6C,IAAI,GAAG,IAAI;QACZ,qBAAqB,EAAE,IAAI,CAACwC,MAAM;QAClChB,IAAI,EAAE;MACV,CAAC;IAAE,CAAC,EAAEhH,qDAAC,CAAC,MAAM,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAE2D,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAErH,qDAAC,CAAC,KAAK,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAEqD,KAAK,EAAE;IAAqB,CAAC,EAAE/G,qDAAC,CAAC,KAAK,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAEqD,KAAK,EAAE;IAAuB,CAAC,EAAE/G,qDAAC,CAAC,MAAM,EAAE;MAAE0D,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAE1D,qDAAC,CAAC,MAAM,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAE2D,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,CAAC;EACna;EACA,IAAIpD,EAAEA,CAAA,EAAG;IAAE,OAAO7D,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD2H,WAAW,CAACL,KAAK,GAAG;EAChBC,GAAG,EAAEE,iBAAiB;EACtBD,EAAE,EAAEE;AACR,CAAC;AAED,MAAMG,eAAe,GAAG,+BAA+B;AAEvD,MAAMC,cAAc,GAAG,+BAA+B;AAEtD,MAAMC,SAAS,GAAG,MAAM;EACpB7G,WAAWA,CAACC,OAAO,EAAE;IACjB5B,qDAAgB,CAAC,IAAI,EAAE4B,OAAO,CAAC;EACnC;EACA4D,MAAMA,CAAA,EAAG;IACL,MAAMK,IAAI,GAAGzF,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,qDAAC,CAACE,iDAAI,EAAE;MAAEwD,GAAG,EAAE,0CAA0C;MAAEyD,IAAI,EAAE,OAAO;MAAEJ,KAAK,EAAE;QACjF,CAACvB,IAAI,GAAG,IAAI;QACZ;QACA,CAAC,cAAcA,IAAI,EAAE,GAAG,IAAI;QAC5BwB,IAAI,EAAE;MACV;IAAE,CAAC,CAAC;EACZ;AACJ,CAAC;AACDmB,SAAS,CAACT,KAAK,GAAG;EACdC,GAAG,EAAEM,eAAe;EACpBL,EAAE,EAAEM;AACR,CAAC;AAED,MAAME,WAAW,GAAG,23HAA23H;AAE/4H,MAAMC,UAAU,GAAG,gvKAAgvK;AAEnwK,MAAMC,KAAK,GAAG,MAAM;EAChBhH,WAAWA,CAACC,OAAO,EAAE;IACjB5B,qDAAgB,CAAC,IAAI,EAAE4B,OAAO,CAAC;IAC/B,IAAI,CAACgH,QAAQ,GAAGjI,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACkI,QAAQ,GAAGlI,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACmI,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,SAAS,GAAG,KAAK;EAC1B;EACA1E,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACyE,OAAO,GAAG,CAAC,CAAC,IAAI,CAACxE,EAAE,CAAC0E,OAAO,CAAC,WAAW,CAAC;IAC7C,IAAI,CAACD,SAAS,GAAG,IAAI,CAACE,QAAQ,KAAK,UAAU;IAC7C,IAAI,CAACC,SAAS,CAAC,CAAC;IAChB,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACA5E,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACwE,SAAS,EAAE;MAChBK,UAAU,CAAC,MAAM;QACb,IAAI,CAACL,SAAS,GAAG,KAAK;MAC1B,CAAC,EAAE,IAAI,CAAC;IACZ;EACJ;EACAM,YAAYA,CAAA,EAAG;IACX,IAAI,CAACF,SAAS,CAAC,CAAC;EACpB;EACAG,eAAeA,CAAA,EAAG;IACd,IAAI,CAACJ,SAAS,CAAC,CAAC;EACpB;EACAC,SAASA,CAAA,EAAG;IACR,MAAM;MAAEnG;IAAM,CAAC,GAAG,IAAI;IACtB,IAAI,CAAC4F,QAAQ,CAACW,IAAI,CAAC;MACf,kBAAkB,EAAEvG,KAAK,KAAKC,SAAS;MACvC,CAAC,aAAaD,KAAK,EAAE,GAAGA,KAAK,KAAKC;IACtC,CAAC,CAAC;EACN;EACAiG,SAASA,CAAA,EAAG;IACR,MAAM;MAAEJ,OAAO;MAAEG;IAAS,CAAC,GAAG,IAAI;IAClC;IACA;IACA;IACA,IAAI,CAACH,OAAO,EAAE;MACV,IAAI,CAACD,QAAQ,CAACU,IAAI,CAAC;QACfC,KAAK,EAAE,IAAI;QACX,CAAC,SAASP,QAAQ,EAAE,GAAGA,QAAQ,KAAKhG;MACxC,CAAC,CAAC;IACN;EACJ;EACAuC,MAAMA,CAAA,EAAG;IACL,MAAMyD,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMpD,IAAI,GAAGzF,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,qDAAC,CAACE,iDAAI,EAAE;MAAEwD,GAAG,EAAE,0CAA0C;MAAEqD,KAAK,EAAEjG,qDAAkB,CAAC,IAAI,CAAC6B,KAAK,EAAE;QACjG,CAAC6C,IAAI,GAAG,IAAI;QACZ,eAAe,EAAE5E,qDAAW,CAAC,oBAAoB,EAAE,IAAI,CAACqD,EAAE,CAAC;QAC3D,CAAC,SAAS2E,QAAQ,EAAE,GAAGA,QAAQ,KAAKhG,SAAS;QAC7C,CAAC,kBAAkB,GAAG,IAAI,CAAC8F,SAAS;QACpC,WAAW,EAAEzB,QAAQ,CAACC,GAAG,KAAK;MAClC,CAAC;IAAE,CAAC,EAAElH,qDAAC,CAAC,MAAM,EAAE;MAAE0D,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC7E;EACA,IAAIO,EAAEA,CAAA,EAAG;IAAE,OAAO7D,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWqH,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,OAAO,EAAE,CAAC,cAAc,CAAC;MACzB,UAAU,EAAE,CAAC,iBAAiB;IAClC,CAAC;EAAE;AACP,CAAC;AACDa,KAAK,CAACZ,KAAK,GAAG;EACVC,GAAG,EAAES,WAAW;EAChBR,EAAE,EAAES;AACR,CAAC;AAED,MAAMe,UAAU,GAAG,knCAAknC;AAEroC,MAAMC,SAAS,GAAG,4zCAA4zC;AAE90C,MAAMC,IAAI,GAAG,MAAM;EACfhI,WAAWA,CAACC,OAAO,EAAE;IACjB5B,qDAAgB,CAAC,IAAI,EAAE4B,OAAO,CAAC;IAC/B;AACR;AACA;IACQ,IAAI,CAACgI,KAAK,GAAG,KAAK;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;EACUC,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,oMAAA;MACtB,MAAM1C,IAAI,GAAGyC,KAAI,CAACxF,EAAE,CAACY,aAAa,CAAC,kBAAkB,CAAC;MACtD,IAAImC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC2C,WAAW,EAAE;QAC9D,OAAO3C,IAAI,CAAC2C,WAAW,CAAC,CAAC;MAC7B;MACA,OAAO,KAAK;IAAC;EACjB;EACAxE,MAAMA,CAAA,EAAG;IACL,MAAMK,IAAI,GAAGzF,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAEsF,KAAK;MAAEkE;IAAM,CAAC,GAAG,IAAI;IAC7B,OAAQvJ,qDAAC,CAACE,iDAAI,EAAE;MAAEwD,GAAG,EAAE,0CAA0C;MAAEyD,IAAI,EAAE,MAAM;MAAEJ,KAAK,EAAE;QAChF,CAACvB,IAAI,GAAG,IAAI;QACZ;QACA,CAAC,QAAQA,IAAI,EAAE,GAAG,IAAI;QACtB,YAAY,EAAE+D,KAAK;QACnB,CAAC,cAAclE,KAAK,EAAE,GAAGA,KAAK,KAAKzC,SAAS;QAC5C,CAAC,QAAQ4C,IAAI,UAAUH,KAAK,EAAE,GAAGA,KAAK,KAAKzC;MAC/C;IAAE,CAAC,CAAC;EACZ;EACA,IAAIqB,EAAEA,CAAA,EAAG;IAAE,OAAO7D,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDkJ,IAAI,CAAC5B,KAAK,GAAG;EACTC,GAAG,EAAEyB,UAAU;EACfxB,EAAE,EAAEyB;AACR,CAAC;AAED,MAAMO,gBAAgB,GAAG,q2EAAq2E;AAE93E,MAAMC,eAAe,GAAG,u/DAAu/D;AAE/gE,MAAMC,UAAU,GAAG,MAAM;EACrBxI,WAAWA,CAACC,OAAO,EAAE;IACjB5B,qDAAgB,CAAC,IAAI,EAAE4B,OAAO,CAAC;EACnC;EACA4D,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEE;IAAM,CAAC,GAAG,IAAI;IACtB,MAAMG,IAAI,GAAGzF,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,qDAAC,CAACE,iDAAI,EAAE;MAAEwD,GAAG,EAAE,0CAA0C;MAAEqD,KAAK,EAAEjG,qDAAkB,CAAC,IAAI,CAAC6B,KAAK,EAAE;QACjG,CAAC6C,IAAI,GAAG,IAAI;QACZ,CAAC,qBAAqBH,KAAK,EAAE,GAAGA,KAAK,KAAKzC;MAC9C,CAAC;IAAE,CAAC,EAAE5C,qDAAC,CAAC,KAAK,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAEqD,KAAK,EAAE;IAAoB,CAAC,EAAE/G,qDAAC,CAAC,MAAM,EAAE;MAAE0D,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,CAAC;EACxK;AACJ,CAAC;AACDoG,UAAU,CAACpC,KAAK,GAAG;EACfC,GAAG,EAAEiC,gBAAgB;EACrBhC,EAAE,EAAEiC;AACR,CAAC;AAED,MAAME,UAAU,GAAG,oRAAoR;AAEvS,MAAMC,SAAS,GAAG,6QAA6Q;AAE/R,MAAMC,IAAI,GAAG,MAAM;EACf3I,WAAWA,CAACC,OAAO,EAAE;IACjB5B,qDAAgB,CAAC,IAAI,EAAE4B,OAAO,CAAC;EACnC;EACA4D,MAAMA,CAAA,EAAG;IACL,MAAMK,IAAI,GAAGzF,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,qDAAC,CAACE,iDAAI,EAAE;MAAEwD,GAAG,EAAE,0CAA0C;MAAEqD,KAAK,EAAEjG,qDAAkB,CAAC,IAAI,CAAC6B,KAAK,EAAE;QACjG,CAAC6C,IAAI,GAAG;MACZ,CAAC;IAAE,CAAC,EAAExF,qDAAC,CAAC,MAAM,EAAE;MAAE0D,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC7E;AACJ,CAAC;AACDuG,IAAI,CAACvC,KAAK,GAAG;EACTC,GAAG,EAAEoC,UAAU;EACfnC,EAAE,EAAEoC;AACR,CAAC;AAED,MAAME,eAAe,GAAG,umDAAumD;AAE/nD,MAAMC,YAAY,GAAG,MAAM;EACvB7I,WAAWA,CAACC,OAAO,EAAE;IACjB5B,qDAAgB,CAAC,IAAI,EAAE4B,OAAO,CAAC;IAC/B,IAAI,CAACiH,QAAQ,GAAGlI,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD;AACR;AACA;IACQ,IAAI,CAAC8J,QAAQ,GAAG,KAAK;EACzB;EACApG,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC6E,SAAS,CAAC,CAAC;EACpB;EACAA,SAASA,CAAA,EAAG;IACR;IACA;IACA;IACA,MAAMnB,KAAK,GAAG;MACV,eAAe,EAAE;IACrB,CAAC;IACD,IAAI,CAACc,QAAQ,CAACU,IAAI,CAACxB,KAAK,CAAC;EAC7B;EACAvC,MAAMA,CAAA,EAAG;IACL,MAAMiF,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI5J,iDAAM,CAAC6J,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC;IACrE,MAAMC,OAAO,GAAG1J,qDAAW,CAAC,YAAY,EAAE,IAAI,CAACqD,EAAE,CAAC,IAAIrD,qDAAW,CAAC,eAAe,EAAE,IAAI,CAACqD,EAAE,CAAC;IAC3F,MAAMuB,IAAI,GAAGzF,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,qDAAC,CAACE,iDAAI,EAAE;MAAEwD,GAAG,EAAE,0CAA0C;MAAEqD,KAAK,EAAE;QAClE,CAACvB,IAAI,GAAG,IAAI;QACZ,wBAAwB,EAAE4E,QAAQ;QAClC,UAAU,EAAEE;MAChB;IAAE,CAAC,EAAEtK,qDAAC,CAAC,MAAM,EAAE;MAAE0D,GAAG,EAAE;IAA2C,CAAC,EAAE,QAAQ,CAAC,CAAC;EACtF;EACA,IAAIO,EAAEA,CAAA,EAAG;IAAE,OAAO7D,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD+J,YAAY,CAACzC,KAAK,GAAGwC,eAAe", "sources": ["./node_modules/@ionic/core/dist/esm/ion-item_8.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, n as forceUpdate, e as getIonMode, h, j as Host, k as getElement, d as createEvent, l as config } from './index-B_U9CtaY.js';\nimport { b as inheritAttributes, r as raf } from './helpers-1O4D2b7y.js';\nimport { h as hostContext, c as createColorClasses, o as openURL } from './theme-DiVJyqlX.js';\nimport { p as chevronForward } from './index-BLV6ykCk.js';\n\nconst itemIosCss = \":host{--border-radius:0px;--border-width:0px;--border-style:solid;--padding-top:0px;--padding-bottom:0px;--padding-end:0px;--padding-start:0px;--inner-border-width:0px;--inner-padding-top:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;--inner-padding-end:0px;--inner-box-shadow:none;--detail-icon-color:initial;--detail-icon-font-size:1.25em;--detail-icon-opacity:0.25;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;outline:none;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:initial;text-decoration:none;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color) .item-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.ion-color) .item-native,:host(.ion-color) .item-inner{border-color:var(--ion-color-shade)}:host(.ion-activated) .item-native{color:var(--color-activated)}:host(.ion-activated) .item-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.ion-color.ion-activated) .item-native{color:var(--ion-color-contrast)}:host(.ion-focused) .item-native{color:var(--color-focused)}:host(.ion-focused) .item-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(.ion-color.ion-focused) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-focused) .item-native::after{background:var(--ion-color-contrast)}@media (any-hover: hover){:host(.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--color-hover)}:host(.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--ion-color-contrast)}}:host(.item-control-needs-pointer-cursor){cursor:pointer}:host(.item-interactive-disabled:not(.item-multiple-inputs)){cursor:default;pointer-events:none}:host(.item-disabled){cursor:default;opacity:0.3;pointer-events:none}.item-native{border-radius:var(--border-radius);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;min-height:var(--min-height);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);overflow:inherit;z-index:1;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}[dir=rtl] .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){.item-native:dir(rtl){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}.item-native::-moz-focus-inner{border:0}.item-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;-webkit-transition:var(--transition);transition:var(--transition);z-index:-1}button,a{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-user-drag:none}.item-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);-webkit-box-shadow:var(--inner-box-shadow);box-shadow:var(--inner-box-shadow);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-detail-icon{-webkit-margin-start:calc(var(--inner-padding-end) / 2);margin-inline-start:calc(var(--inner-padding-end) / 2);-webkit-margin-end:-6px;margin-inline-end:-6px;color:var(--detail-icon-color);font-size:var(--detail-icon-font-size);opacity:var(--detail-icon-opacity)}::slotted(ion-icon){font-size:1.6em}::slotted(ion-button){--margin-top:0;--margin-bottom:0;--margin-start:0;--margin-end:0;z-index:1}::slotted(ion-label:not([slot=end])){-ms-flex:1;flex:1;width:-webkit-min-content;width:-moz-min-content;width:min-content;max-width:100%}:host(.item-input){-ms-flex-align:center;align-items:center}.input-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.item-label-stacked),:host(.item-label-floating){-ms-flex-align:start;align-items:start}:host(.item-label-stacked) .input-wrapper,:host(.item-label-floating) .input-wrapper{-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column}:host(.item-multiple-inputs) ::slotted(ion-checkbox),:host(.item-multiple-inputs) ::slotted(ion-datetime),:host(.item-multiple-inputs) ::slotted(ion-radio){position:relative}:host(.item-textarea){-ms-flex-align:stretch;align-items:stretch}::slotted(ion-reorder[slot]){margin-top:0;margin-bottom:0}ion-ripple-effect{color:var(--ripple-color)}:host{--min-height:44px;--transition:background-color 200ms linear, opacity 200ms linear;--padding-start:16px;--inner-padding-end:16px;--inner-border-width:0px 0px 0.55px 0px;--background:var(--ion-item-background, var(--ion-background-color, #fff));--background-activated:var(--ion-text-color, #000);--background-focused:var(--ion-text-color, #000);--background-hover:currentColor;--background-activated-opacity:.12;--background-focused-opacity:.15;--background-hover-opacity:.04;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--color:var(--ion-item-color, var(--ion-text-color, #000));font-size:1rem}:host(.ion-activated){--transition:none}:host(.ion-color.ion-focused) .item-native::after{background:#000;opacity:0.15}:host(.ion-color.ion-activated) .item-native::after{background:#000;opacity:0.12}:host(.item-lines-full){--border-width:0px 0px 0.55px 0px}:host(.item-lines-inset){--inner-border-width:0px 0px 0.55px 0px}:host(.item-lines-inset),:host(.item-lines-none){--border-width:0px}:host(.item-lines-full),:host(.item-lines-none){--inner-border-width:0px}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:2px;margin-bottom:2px}::slotted(ion-icon[slot=start]),::slotted(ion-icon[slot=end]){margin-top:7px;margin-bottom:7px}::slotted(ion-toggle[slot=start]),::slotted(ion-toggle[slot=end]){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}:host(.item-label-stacked) ::slotted([slot=end]),:host(.item-label-floating) ::slotted([slot=end]){margin-top:7px;margin-bottom:7px}::slotted(.button-small){--padding-top:1px;--padding-bottom:1px;--padding-start:.5em;--padding-end:.5em;min-height:24px;font-size:0.8125rem}::slotted(ion-avatar){width:36px;height:36px}::slotted(ion-thumbnail){--size:56px}::slotted(ion-avatar[slot=end]),::slotted(ion-thumbnail[slot=end]){-webkit-margin-start:8px;margin-inline-start:8px;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:8px;margin-bottom:8px}:host(.item-radio) ::slotted(ion-label),:host(.item-toggle) ::slotted(ion-label){-webkit-margin-start:0px;margin-inline-start:0px}::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:10px;margin-bottom:10px}:host(.item-label-floating),:host(.item-label-stacked){--min-height:68px}\";\n\nconst itemMdCss = \":host{--border-radius:0px;--border-width:0px;--border-style:solid;--padding-top:0px;--padding-bottom:0px;--padding-end:0px;--padding-start:0px;--inner-border-width:0px;--inner-padding-top:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;--inner-padding-end:0px;--inner-box-shadow:none;--detail-icon-color:initial;--detail-icon-font-size:1.25em;--detail-icon-opacity:0.25;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;outline:none;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:initial;text-decoration:none;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color) .item-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.ion-color) .item-native,:host(.ion-color) .item-inner{border-color:var(--ion-color-shade)}:host(.ion-activated) .item-native{color:var(--color-activated)}:host(.ion-activated) .item-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.ion-color.ion-activated) .item-native{color:var(--ion-color-contrast)}:host(.ion-focused) .item-native{color:var(--color-focused)}:host(.ion-focused) .item-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(.ion-color.ion-focused) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-focused) .item-native::after{background:var(--ion-color-contrast)}@media (any-hover: hover){:host(.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--color-hover)}:host(.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--ion-color-contrast)}}:host(.item-control-needs-pointer-cursor){cursor:pointer}:host(.item-interactive-disabled:not(.item-multiple-inputs)){cursor:default;pointer-events:none}:host(.item-disabled){cursor:default;opacity:0.3;pointer-events:none}.item-native{border-radius:var(--border-radius);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;min-height:var(--min-height);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);overflow:inherit;z-index:1;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}[dir=rtl] .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){.item-native:dir(rtl){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}.item-native::-moz-focus-inner{border:0}.item-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;-webkit-transition:var(--transition);transition:var(--transition);z-index:-1}button,a{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-user-drag:none}.item-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);-webkit-box-shadow:var(--inner-box-shadow);box-shadow:var(--inner-box-shadow);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-detail-icon{-webkit-margin-start:calc(var(--inner-padding-end) / 2);margin-inline-start:calc(var(--inner-padding-end) / 2);-webkit-margin-end:-6px;margin-inline-end:-6px;color:var(--detail-icon-color);font-size:var(--detail-icon-font-size);opacity:var(--detail-icon-opacity)}::slotted(ion-icon){font-size:1.6em}::slotted(ion-button){--margin-top:0;--margin-bottom:0;--margin-start:0;--margin-end:0;z-index:1}::slotted(ion-label:not([slot=end])){-ms-flex:1;flex:1;width:-webkit-min-content;width:-moz-min-content;width:min-content;max-width:100%}:host(.item-input){-ms-flex-align:center;align-items:center}.input-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.item-label-stacked),:host(.item-label-floating){-ms-flex-align:start;align-items:start}:host(.item-label-stacked) .input-wrapper,:host(.item-label-floating) .input-wrapper{-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column}:host(.item-multiple-inputs) ::slotted(ion-checkbox),:host(.item-multiple-inputs) ::slotted(ion-datetime),:host(.item-multiple-inputs) ::slotted(ion-radio){position:relative}:host(.item-textarea){-ms-flex-align:stretch;align-items:stretch}::slotted(ion-reorder[slot]){margin-top:0;margin-bottom:0}ion-ripple-effect{color:var(--ripple-color)}:host{--min-height:48px;--background:var(--ion-item-background, var(--ion-background-color, #fff));--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor;--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--color:var(--ion-item-color, var(--ion-text-color, #000));--transition:opacity 15ms linear, background-color 15ms linear;--padding-start:16px;--inner-padding-end:16px;--inner-border-width:0 0 1px 0;font-size:1rem;font-weight:normal;text-transform:none}:host(.ion-color.ion-activated) .item-native::after{background:transparent}:host(.item-interactive){--border-width:0 0 1px 0;--inner-border-width:0}:host(.item-lines-full){--border-width:0 0 1px 0}:host(.item-lines-inset){--inner-border-width:0 0 1px 0}:host(.item-lines-inset),:host(.item-lines-none){--border-width:0}:host(.item-lines-full),:host(.item-lines-none){--inner-border-width:0}:host(.item-multi-line) ::slotted([slot=start]),:host(.item-multi-line) ::slotted([slot=end]){margin-top:16px;margin-bottom:16px;-ms-flex-item-align:start;align-self:flex-start}::slotted([slot=start]){-webkit-margin-end:16px;margin-inline-end:16px}::slotted([slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-icon){color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54);font-size:1.5em}:host(.ion-color) ::slotted(ion-icon){color:var(--ion-color-contrast)}::slotted(ion-icon[slot]){margin-top:12px;margin-bottom:12px}::slotted(ion-icon[slot=start]){-webkit-margin-end:32px;margin-inline-end:32px}::slotted(ion-icon[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-toggle[slot=start]),::slotted(ion-toggle[slot=end]){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}::slotted(ion-note){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-ms-flex-item-align:start;align-self:flex-start;font-size:0.6875rem}::slotted(ion-note[slot]){padding-left:0;padding-right:0;padding-top:18px;padding-bottom:10px}::slotted(ion-avatar){width:40px;height:40px}::slotted(ion-thumbnail){--size:56px}::slotted(ion-avatar),::slotted(ion-thumbnail){margin-top:8px;margin-bottom:8px}::slotted(ion-avatar[slot=start]),::slotted(ion-thumbnail[slot=start]){-webkit-margin-end:16px;margin-inline-end:16px}::slotted(ion-avatar[slot=end]),::slotted(ion-thumbnail[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-label){margin-left:0;margin-right:0;margin-top:10px;margin-bottom:10px}:host(.item-label-stacked) ::slotted([slot=end]),:host(.item-label-floating) ::slotted([slot=end]){margin-top:7px;margin-bottom:7px}:host(.item-toggle) ::slotted(ion-label),:host(.item-radio) ::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0}::slotted(.button-small){--padding-top:2px;--padding-bottom:2px;--padding-start:.6em;--padding-end:.6em;min-height:25px;font-size:0.75rem}:host(.item-label-floating),:host(.item-label-stacked){--min-height:55px}:host(.ion-focused:not(.ion-color)) ::slotted(.label-stacked),:host(.ion-focused:not(.ion-color)) ::slotted(.label-floating),:host(.item-has-focus:not(.ion-color)) ::slotted(.label-stacked),:host(.item-has-focus:not(.ion-color)) ::slotted(.label-floating){color:var(--ion-color-primary, #0054e9)}\";\n\nconst Item = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.labelColorStyles = {};\n        this.itemStyles = new Map();\n        this.inheritedAriaAttributes = {};\n        this.multipleInputs = false;\n        this.focusable = true;\n        this.isInteractive = false;\n        /**\n         * If `true`, a button tag will be rendered and the item will be tappable.\n         */\n        this.button = false;\n        /**\n         * The icon to use when `detail` is set to `true`.\n         */\n        this.detailIcon = chevronForward;\n        /**\n         * If `true`, the user cannot interact with the item.\n         */\n        this.disabled = false;\n        /**\n         * When using a router, it specifies the transition direction when navigating to\n         * another page using `href`.\n         */\n        this.routerDirection = 'forward';\n        /**\n         * The type of the button. Only used when an `onclick` or `button` property is present.\n         */\n        this.type = 'button';\n        // slot change listener updates state to reflect how/if item should be interactive\n        this.updateInteractivityOnSlotChange = () => {\n            this.setIsInteractive();\n            this.setMultipleInputs();\n        };\n    }\n    buttonChanged() {\n        // Update the focusable option when the button option is changed\n        this.focusable = this.isFocusable();\n    }\n    labelColorChanged(ev) {\n        const { color } = this;\n        // There will be a conflict with item color if\n        // we apply the label color to item, so we ignore\n        // the label color if the user sets a color on item\n        if (color === undefined) {\n            this.labelColorStyles = ev.detail;\n        }\n    }\n    itemStyle(ev) {\n        ev.stopPropagation();\n        const tagName = ev.target.tagName;\n        const updatedStyles = ev.detail;\n        const newStyles = {};\n        const childStyles = this.itemStyles.get(tagName) || {};\n        let hasStyleChange = false;\n        Object.keys(updatedStyles).forEach((key) => {\n            if (updatedStyles[key]) {\n                const itemKey = `item-${key}`;\n                if (!childStyles[itemKey]) {\n                    hasStyleChange = true;\n                }\n                newStyles[itemKey] = true;\n            }\n        });\n        if (!hasStyleChange && Object.keys(newStyles).length !== Object.keys(childStyles).length) {\n            hasStyleChange = true;\n        }\n        if (hasStyleChange) {\n            this.itemStyles.set(tagName, newStyles);\n            forceUpdate(this);\n        }\n    }\n    connectedCallback() {\n        this.hasStartEl();\n    }\n    componentWillLoad() {\n        this.inheritedAriaAttributes = inheritAttributes(this.el, ['aria-label']);\n    }\n    componentDidLoad() {\n        raf(() => {\n            this.setMultipleInputs();\n            this.setIsInteractive();\n            this.focusable = this.isFocusable();\n        });\n    }\n    totalNestedInputs() {\n        // The following elements have a clickable cover that is relative to the entire item\n        const covers = this.el.querySelectorAll('ion-checkbox, ion-datetime, ion-select, ion-radio');\n        // The following elements can accept focus alongside the previous elements\n        // therefore if these elements are also a child of item, we don't want the\n        // input cover on top of those interfering with their clicks\n        const inputs = this.el.querySelectorAll('ion-input, ion-range, ion-searchbar, ion-segment, ion-textarea, ion-toggle');\n        // The following elements should also stay clickable when an input with cover is present\n        const clickables = this.el.querySelectorAll('ion-router-link, ion-button, a, button');\n        return {\n            covers,\n            inputs,\n            clickables,\n        };\n    }\n    // If the item contains multiple clickable elements and/or inputs, then the item\n    // should not have a clickable input cover over the entire item to prevent\n    // interfering with their individual click events\n    setMultipleInputs() {\n        const { covers, inputs, clickables } = this.totalNestedInputs();\n        // Check for multiple inputs to change the position of the input cover to relative\n        // for all of the covered inputs above\n        this.multipleInputs =\n            covers.length + inputs.length > 1 ||\n                covers.length + clickables.length > 1 ||\n                (covers.length > 0 && this.isClickable());\n    }\n    setIsInteractive() {\n        // If item contains any interactive children, set isInteractive to `true`\n        const { covers, inputs, clickables } = this.totalNestedInputs();\n        this.isInteractive = covers.length > 0 || inputs.length > 0 || clickables.length > 0;\n    }\n    // If the item contains an input including a checkbox, datetime, select, or radio\n    // then the item will have a clickable input cover that covers the item\n    // that should get the hover, focused and activated states UNLESS it has multiple\n    // inputs, then those need to individually get each click\n    hasCover() {\n        const inputs = this.el.querySelectorAll('ion-checkbox, ion-datetime, ion-select, ion-radio');\n        return inputs.length === 1 && !this.multipleInputs;\n    }\n    // If the item has an href or button property it will render a native\n    // anchor or button that is clickable\n    isClickable() {\n        return this.href !== undefined || this.button;\n    }\n    canActivate() {\n        return this.isClickable() || this.hasCover();\n    }\n    isFocusable() {\n        const focusableChild = this.el.querySelector('.ion-focusable');\n        return this.canActivate() || focusableChild !== null;\n    }\n    hasStartEl() {\n        const startEl = this.el.querySelector('[slot=\"start\"]');\n        if (startEl !== null) {\n            this.el.classList.add('item-has-start-slot');\n        }\n    }\n    getFirstInteractive() {\n        const controls = this.el.querySelectorAll('ion-toggle:not([disabled]), ion-checkbox:not([disabled]), ion-radio:not([disabled]), ion-select:not([disabled]), ion-input:not([disabled]), ion-textarea:not([disabled])');\n        return controls[0];\n    }\n    render() {\n        const { detail, detailIcon, download, labelColorStyles, lines, disabled, href, rel, target, routerAnimation, routerDirection, inheritedAriaAttributes, multipleInputs, } = this;\n        const childStyles = {};\n        const mode = getIonMode(this);\n        const clickable = this.isClickable();\n        const canActivate = this.canActivate();\n        const TagType = clickable ? (href === undefined ? 'button' : 'a') : 'div';\n        const attrs = TagType === 'button'\n            ? { type: this.type }\n            : {\n                download,\n                href,\n                rel,\n                target,\n            };\n        let clickFn = {};\n        const firstInteractive = this.getFirstInteractive();\n        // Only set onClick if the item is clickable to prevent screen\n        // readers from reading all items as clickable\n        if (clickable || (firstInteractive !== undefined && !multipleInputs)) {\n            clickFn = {\n                onClick: (ev) => {\n                    if (clickable) {\n                        openURL(href, ev, routerDirection, routerAnimation);\n                    }\n                    if (firstInteractive !== undefined && !multipleInputs) {\n                        const path = ev.composedPath();\n                        const target = path[0];\n                        if (ev.isTrusted) {\n                            /**\n                             * Dispatches a click event to the first interactive element,\n                             * when it is the result of a user clicking on the item.\n                             *\n                             * We check if the click target is in the shadow root,\n                             * which means the user clicked on the .item-native or\n                             * .item-inner padding.\n                             */\n                            const clickedWithinShadowRoot = this.el.shadowRoot.contains(target);\n                            if (clickedWithinShadowRoot) {\n                                /**\n                                 * For input/textarea clicking the padding should focus the\n                                 * text field (thus making it editable). For everything else,\n                                 * we want to click the control so it activates.\n                                 */\n                                if (firstInteractive.tagName === 'ION-INPUT' || firstInteractive.tagName === 'ION-TEXTAREA') {\n                                    firstInteractive.setFocus();\n                                }\n                                firstInteractive.click();\n                                /**\n                                 * Stop the item event from being triggered\n                                 * as the firstInteractive click event will also\n                                 * trigger the item click event.\n                                 */\n                                ev.stopImmediatePropagation();\n                            }\n                        }\n                    }\n                },\n            };\n        }\n        const showDetail = detail !== undefined ? detail : mode === 'ios' && clickable;\n        this.itemStyles.forEach((value) => {\n            Object.assign(childStyles, value);\n        });\n        const ariaDisabled = disabled || childStyles['item-interactive-disabled'] ? 'true' : null;\n        const inList = hostContext('ion-list', this.el) && !hostContext('ion-radio-group', this.el);\n        /**\n         * Inputs and textareas do not need to show a cursor pointer.\n         * However, other form controls such as checkboxes and radios do.\n         */\n        const firstInteractiveNeedsPointerCursor = firstInteractive !== undefined && !['ION-INPUT', 'ION-TEXTAREA'].includes(firstInteractive.tagName);\n        return (h(Host, { key: '24b59935bd8db8b0b7f940582455a42b82cbf762', \"aria-disabled\": ariaDisabled, class: Object.assign(Object.assign(Object.assign({}, childStyles), labelColorStyles), createColorClasses(this.color, {\n                item: true,\n                [mode]: true,\n                'item-lines-default': lines === undefined,\n                [`item-lines-${lines}`]: lines !== undefined,\n                'item-control-needs-pointer-cursor': firstInteractiveNeedsPointerCursor,\n                'item-disabled': disabled,\n                'in-list': inList,\n                'item-multiple-inputs': this.multipleInputs,\n                'ion-activatable': canActivate,\n                'ion-focusable': this.focusable,\n                'item-rtl': document.dir === 'rtl',\n            })), role: inList ? 'listitem' : null }, h(TagType, Object.assign({ key: 'fd77b6e5f3eb2e1857a0cdd45562d71eabd30255' }, attrs, inheritedAriaAttributes, { class: \"item-native\", part: \"native\", disabled: disabled }, clickFn), h(\"slot\", { key: '8824ac8395aafa3d63c92f2128e947cac8393ac4', name: \"start\", onSlotchange: this.updateInteractivityOnSlotChange }), h(\"div\", { key: '5c9127e388a432687766d86a9db91fd1663abf03', class: \"item-inner\" }, h(\"div\", { key: '9dc2d2f58c4067c0143b3963334c346c3c7f77df', class: \"input-wrapper\" }, h(\"slot\", { key: '8377d9e56dc4b1913f1346111b706e7f14c24d30', onSlotchange: this.updateInteractivityOnSlotChange })), h(\"slot\", { key: 'bc771e106174f4a84ee12e92d14df81ad7ed177d', name: \"end\", onSlotchange: this.updateInteractivityOnSlotChange }), showDetail && (h(\"ion-icon\", { key: '45336d121a097cbf71ee8a3f6b554745ba5e0bbf', icon: detailIcon, lazy: false, class: \"item-detail-icon\", part: \"detail-icon\", \"aria-hidden\": \"true\", \"flip-rtl\": detailIcon === chevronForward }))), canActivate && mode === 'md' && h(\"ion-ripple-effect\", { key: '197e244ae3bffebfa6ac9bfe7658d12e1af0ecb1' }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"button\": [\"buttonChanged\"]\n    }; }\n};\nItem.style = {\n    ios: itemIosCss,\n    md: itemMdCss\n};\n\nconst itemDividerIosCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--inner-padding-top:0px;--inner-padding-end:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);overflow:hidden;z-index:100;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){:host(:dir(rtl)){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.item-divider-sticky){position:-webkit-sticky;position:sticky;top:0}.item-divider-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border:0;overflow:hidden}:host-context([dir=rtl]) .item-divider-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-divider-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-divider-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-divider-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:hidden}:host{--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));--padding-start:16px;--inner-padding-end:8px;border-radius:0;position:relative;min-height:28px;font-size:1.0625rem;font-weight:600}:host([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:2px;margin-bottom:2px}::slotted(ion-icon[slot=start]),::slotted(ion-icon[slot=end]){margin-top:7px;margin-bottom:7px}::slotted(h1){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px}::slotted(h2){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px}::slotted(h3),::slotted(h4),::slotted(h5),::slotted(h6){margin-left:0;margin-right:0;margin-top:0;margin-bottom:3px}::slotted(p){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;color:var(--ion-text-color-step-550, #a3a3a3);font-size:0.875rem;line-height:normal;text-overflow:inherit;overflow:inherit}::slotted(h2:last-child) ::slotted(h3:last-child),::slotted(h4:last-child),::slotted(h5:last-child),::slotted(h6:last-child),::slotted(p:last-child){margin-bottom:0}\";\n\nconst itemDividerMdCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--inner-padding-top:0px;--inner-padding-end:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);overflow:hidden;z-index:100;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){:host(:dir(rtl)){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.item-divider-sticky){position:-webkit-sticky;position:sticky;top:0}.item-divider-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border:0;overflow:hidden}:host-context([dir=rtl]) .item-divider-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-divider-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-divider-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-divider-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:hidden}:host{--background:var(--ion-background-color, #fff);--color:var(--ion-color-step-400, var(--ion-text-color-step-600, #999999));--padding-start:16px;--inner-padding-end:16px;min-height:30px;border-bottom:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));font-size:0.875rem}::slotted([slot=start]){-webkit-margin-end:16px;margin-inline-end:16px}::slotted([slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-label){margin-left:0;margin-right:0;margin-top:13px;margin-bottom:10px}::slotted(ion-icon){color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54);font-size:1.7142857143em}:host(.ion-color) ::slotted(ion-icon){color:var(--ion-color-contrast)}::slotted(ion-icon[slot]){margin-top:12px;margin-bottom:12px}::slotted(ion-icon[slot=start]){-webkit-margin-end:32px;margin-inline-end:32px}::slotted(ion-icon[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-note){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-ms-flex-item-align:start;align-self:flex-start;font-size:0.6875rem}::slotted(ion-note[slot]){padding-left:0;padding-right:0;padding-top:18px;padding-bottom:10px}::slotted(ion-avatar){width:40px;height:40px}::slotted(ion-thumbnail){--size:56px}::slotted(ion-avatar),::slotted(ion-thumbnail){margin-top:8px;margin-bottom:8px}::slotted(ion-avatar[slot=start]),::slotted(ion-thumbnail[slot=start]){-webkit-margin-end:16px;margin-inline-end:16px}::slotted(ion-avatar[slot=end]),::slotted(ion-thumbnail[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(h1){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px}::slotted(h2){margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px}::slotted(h3,h4,h5,h6){margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px}::slotted(p){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));font-size:0.875rem;line-height:normal;text-overflow:inherit;overflow:inherit}\";\n\nconst ItemDivider = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        /**\n         * When it's set to `true`, the item-divider will stay visible when it reaches the top\n         * of the viewport until the next `ion-item-divider` replaces it.\n         *\n         * This feature relies in `position:sticky`:\n         * https://caniuse.com/#feat=css-sticky\n         */\n        this.sticky = false;\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '1523095ce4af3f2611512ff0948ead659959ee4a', class: createColorClasses(this.color, {\n                [mode]: true,\n                'item-divider-sticky': this.sticky,\n                item: true,\n            }) }, h(\"slot\", { key: '39105d888e115416c3a3fe588da44b4c61f4e5fe', name: \"start\" }), h(\"div\", { key: '67e16f1056bd39187f3629c1bb383b7abbda829b', class: \"item-divider-inner\" }, h(\"div\", { key: 'b3a218fdcc7b9aeab6e0155340152d39fa0b6329', class: \"item-divider-wrapper\" }, h(\"slot\", { key: '69d8587533b387869d34b075d02f61396858fc90' })), h(\"slot\", { key: 'b91c654699b3b26d0012ea0c719c4a07d1fcfbaa', name: \"end\" }))));\n    }\n    get el() { return getElement(this); }\n};\nItemDivider.style = {\n    ios: itemDividerIosCss,\n    md: itemDividerMdCss\n};\n\nconst itemGroupIosCss = \"ion-item-group{display:block}\";\n\nconst itemGroupMdCss = \"ion-item-group{display:block}\";\n\nconst ItemGroup = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'e49dc8f99247d2431d7c6db01b6e021a0f5b1c37', role: \"group\", class: {\n                [mode]: true,\n                // Used internally for styling\n                [`item-group-${mode}`]: true,\n                item: true,\n            } }));\n    }\n};\nItemGroup.style = {\n    ios: itemGroupIosCss,\n    md: itemGroupMdCss\n};\n\nconst labelIosCss = \".item.sc-ion-label-ios-h,.item .sc-ion-label-ios-h{--color:initial;display:block;color:var(--color);font-family:var(--ion-font-family, inherit);font-size:inherit;text-overflow:ellipsis;-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-label-ios-h{color:var(--ion-color-base)}.ion-text-nowrap.sc-ion-label-ios-h{overflow:hidden}.item-interactive-disabled.sc-ion-label-ios-h:not(.item-multiple-inputs),.item-interactive-disabled:not(.item-multiple-inputs) .sc-ion-label-ios-h{cursor:default;opacity:0.3;pointer-events:none}.item-input.sc-ion-label-ios-h,.item-input .sc-ion-label-ios-h{-ms-flex:initial;flex:initial;max-width:200px;pointer-events:none}.item-textarea.sc-ion-label-ios-h,.item-textarea .sc-ion-label-ios-h{-ms-flex-item-align:baseline;align-self:baseline}.item-skeleton-text.sc-ion-label-ios-h,.item-skeleton-text .sc-ion-label-ios-h{overflow:hidden}.label-fixed.sc-ion-label-ios-h{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.label-stacked.sc-ion-label-ios-h,.label-floating.sc-ion-label-ios-h{margin-bottom:0;-ms-flex-item-align:stretch;align-self:stretch;width:auto;max-width:100%}.label-no-animate.label-floating.sc-ion-label-ios-h{-webkit-transition:none;transition:none}.sc-ion-label-ios-s h1,.sc-ion-label-ios-s h2,.sc-ion-label-ios-s h3,.sc-ion-label-ios-s h4,.sc-ion-label-ios-s h5,.sc-ion-label-ios-s h6{text-overflow:inherit;overflow:inherit}.ion-text-wrap.sc-ion-label-ios-h{font-size:0.875rem;line-height:1.5}.label-stacked.sc-ion-label-ios-h{margin-bottom:4px;font-size:0.875rem}.label-floating.sc-ion-label-ios-h{margin-bottom:0;-webkit-transform:translate(0, 29px);transform:translate(0, 29px);-webkit-transform-origin:left top;transform-origin:left top;-webkit-transition:-webkit-transform 150ms ease-in-out;transition:-webkit-transform 150ms ease-in-out;transition:transform 150ms ease-in-out;transition:transform 150ms ease-in-out, -webkit-transform 150ms ease-in-out}[dir=rtl].sc-ion-label-ios-h -no-combinator.label-floating.sc-ion-label-ios-h,[dir=rtl] .sc-ion-label-ios-h -no-combinator.label-floating.sc-ion-label-ios-h,[dir=rtl].label-floating.sc-ion-label-ios-h,[dir=rtl] .label-floating.sc-ion-label-ios-h{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.label-floating.sc-ion-label-ios-h:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}.item-textarea.label-floating.sc-ion-label-ios-h,.item-textarea .label-floating.sc-ion-label-ios-h{-webkit-transform:translate(0, 28px);transform:translate(0, 28px)}.item-has-focus.label-floating.sc-ion-label-ios-h,.item-has-focus .label-floating.sc-ion-label-ios-h,.item-has-placeholder.sc-ion-label-ios-h:not(.item-input).label-floating,.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-ios-h,.item-has-value.label-floating.sc-ion-label-ios-h,.item-has-value .label-floating.sc-ion-label-ios-h{-webkit-transform:scale(0.82);transform:scale(0.82)}.sc-ion-label-ios-s h1{margin-left:0;margin-right:0;margin-top:3px;margin-bottom:2px;font-size:1.375rem;font-weight:normal}.sc-ion-label-ios-s h2{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:1.0625rem;font-weight:normal}.sc-ion-label-ios-s h3,.sc-ion-label-ios-s h4,.sc-ion-label-ios-s h5,.sc-ion-label-ios-s h6{margin-left:0;margin-right:0;margin-top:0;margin-bottom:3px;font-size:0.875rem;font-weight:normal;line-height:normal}.sc-ion-label-ios-s p{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:0.875rem;line-height:normal;text-overflow:inherit;overflow:inherit}.sc-ion-label-ios-s>p{color:var(--ion-color-step-400, var(--ion-text-color-step-600, #999999))}.sc-ion-label-ios-h.in-item-color.sc-ion-label-ios-s>p{color:inherit}.sc-ion-label-ios-s h2:last-child,.sc-ion-label-ios-s h3:last-child,.sc-ion-label-ios-s h4:last-child,.sc-ion-label-ios-s h5:last-child,.sc-ion-label-ios-s h6:last-child,.sc-ion-label-ios-s p:last-child{margin-bottom:0}\";\n\nconst labelMdCss = \".item.sc-ion-label-md-h,.item .sc-ion-label-md-h{--color:initial;display:block;color:var(--color);font-family:var(--ion-font-family, inherit);font-size:inherit;text-overflow:ellipsis;-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-label-md-h{color:var(--ion-color-base)}.ion-text-nowrap.sc-ion-label-md-h{overflow:hidden}.item-interactive-disabled.sc-ion-label-md-h:not(.item-multiple-inputs),.item-interactive-disabled:not(.item-multiple-inputs) .sc-ion-label-md-h{cursor:default;opacity:0.3;pointer-events:none}.item-input.sc-ion-label-md-h,.item-input .sc-ion-label-md-h{-ms-flex:initial;flex:initial;max-width:200px;pointer-events:none}.item-textarea.sc-ion-label-md-h,.item-textarea .sc-ion-label-md-h{-ms-flex-item-align:baseline;align-self:baseline}.item-skeleton-text.sc-ion-label-md-h,.item-skeleton-text .sc-ion-label-md-h{overflow:hidden}.label-fixed.sc-ion-label-md-h{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.label-stacked.sc-ion-label-md-h,.label-floating.sc-ion-label-md-h{margin-bottom:0;-ms-flex-item-align:stretch;align-self:stretch;width:auto;max-width:100%}.label-no-animate.label-floating.sc-ion-label-md-h{-webkit-transition:none;transition:none}.sc-ion-label-md-s h1,.sc-ion-label-md-s h2,.sc-ion-label-md-s h3,.sc-ion-label-md-s h4,.sc-ion-label-md-s h5,.sc-ion-label-md-s h6{text-overflow:inherit;overflow:inherit}.ion-text-wrap.sc-ion-label-md-h{line-height:1.5}.label-stacked.sc-ion-label-md-h,.label-floating.sc-ion-label-md-h{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-transform-origin:top left;transform-origin:top left}.label-stacked.label-rtl.sc-ion-label-md-h,.label-floating.label-rtl.sc-ion-label-md-h{-webkit-transform-origin:top right;transform-origin:top right}.label-stacked.sc-ion-label-md-h{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.label-floating.sc-ion-label-md-h{-webkit-transform:translateY(96%);transform:translateY(96%);-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1)}.ion-focused.label-floating.sc-ion-label-md-h,.ion-focused .label-floating.sc-ion-label-md-h,.item-has-focus.label-floating.sc-ion-label-md-h,.item-has-focus .label-floating.sc-ion-label-md-h,.item-has-placeholder.sc-ion-label-md-h:not(.item-input).label-floating,.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-md-h,.item-has-value.label-floating.sc-ion-label-md-h,.item-has-value .label-floating.sc-ion-label-md-h{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75)}.ion-focused.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-focused .label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--ion-color-primary, #0054e9)}.ion-focused.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--ion-color-contrast)}.ion-invalid.ion-touched.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--highlight-color-invalid)}.sc-ion-label-md-s h1{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:1.5rem;font-weight:normal}.sc-ion-label-md-s h2{margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:1rem;font-weight:normal}.sc-ion-label-md-s h3,.sc-ion-label-md-s h4,.sc-ion-label-md-s h5,.sc-ion-label-md-s h6{margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:0.875rem;font-weight:normal;line-height:normal}.sc-ion-label-md-s p{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:0.875rem;line-height:1.25rem;text-overflow:inherit;overflow:inherit}.sc-ion-label-md-s>p{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}.sc-ion-label-md-h.in-item-color.sc-ion-label-md-s>p{color:inherit}\";\n\nconst Label = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionColor = createEvent(this, \"ionColor\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.inRange = false;\n        this.noAnimate = false;\n    }\n    componentWillLoad() {\n        this.inRange = !!this.el.closest('ion-range');\n        this.noAnimate = this.position === 'floating';\n        this.emitStyle();\n        this.emitColor();\n    }\n    componentDidLoad() {\n        if (this.noAnimate) {\n            setTimeout(() => {\n                this.noAnimate = false;\n            }, 1000);\n        }\n    }\n    colorChanged() {\n        this.emitColor();\n    }\n    positionChanged() {\n        this.emitStyle();\n    }\n    emitColor() {\n        const { color } = this;\n        this.ionColor.emit({\n            'item-label-color': color !== undefined,\n            [`ion-color-${color}`]: color !== undefined,\n        });\n    }\n    emitStyle() {\n        const { inRange, position } = this;\n        // If the label is inside of a range we don't want\n        // to override the classes added by the label that\n        // is a direct child of the item\n        if (!inRange) {\n            this.ionStyle.emit({\n                label: true,\n                [`label-${position}`]: position !== undefined,\n            });\n        }\n    }\n    render() {\n        const position = this.position;\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'd6fba1a97189acc8ddfd64a2f009954a3e46e188', class: createColorClasses(this.color, {\n                [mode]: true,\n                'in-item-color': hostContext('ion-item.ion-color', this.el),\n                [`label-${position}`]: position !== undefined,\n                [`label-no-animate`]: this.noAnimate,\n                'label-rtl': document.dir === 'rtl',\n            }) }, h(\"slot\", { key: 'ce0ab50b5700398fdf50f36d02b7ad287eb71481' })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"color\": [\"colorChanged\"],\n        \"position\": [\"positionChanged\"]\n    }; }\n};\nLabel.style = {\n    ios: labelIosCss,\n    md: labelMdCss\n};\n\nconst listIosCss = \"ion-list{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:block;contain:content;list-style-type:none}ion-list.list-inset{-webkit-transform:translateZ(0);transform:translateZ(0);overflow:hidden}.list-ios{background:var(--ion-item-background, var(--ion-background-color, #fff))}.list-ios.list-inset{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px;border-radius:10px}.list-ios.list-inset ion-item:only-child,.list-ios.list-inset ion-item:not(:only-of-type):last-of-type,.list-ios.list-inset ion-item-sliding:last-of-type ion-item{--border-width:0;--inner-border-width:0}.list-ios.list-inset+ion-list.list-inset{margin-top:0}.list-ios-lines-none .item-lines-default{--inner-border-width:0px;--border-width:0px}.list-ios-lines-full .item-lines-default{--inner-border-width:0px;--border-width:0 0 0.55px 0}.list-ios-lines-inset .item-lines-default{--inner-border-width:0 0 0.55px 0;--border-width:0px}ion-card .list-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}\";\n\nconst listMdCss = \"ion-list{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:block;contain:content;list-style-type:none}ion-list.list-inset{-webkit-transform:translateZ(0);transform:translateZ(0);overflow:hidden}.list-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:8px;padding-bottom:8px;background:var(--ion-item-background, var(--ion-background-color, #fff))}.list-md>.input:last-child::after{inset-inline-start:0}.list-md.list-inset{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px;border-radius:2px}.list-md.list-inset ion-item:not(:only-of-type):last-of-type,.list-md.list-inset ion-item-sliding:last-of-type ion-item{--border-width:0;--inner-border-width:0}.list-md.list-inset ion-item:only-child{--border-width:0;--inner-border-width:0}.list-md.list-inset+ion-list.list-inset{margin-top:0}.list-md-lines-none .item-lines-default{--inner-border-width:0px;--border-width:0px}.list-md-lines-full .item-lines-default{--inner-border-width:0px;--border-width:0 0 1px 0}.list-md-lines-inset .item-lines-default{--inner-border-width:0 0 1px 0;--border-width:0px}ion-card .list-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}\";\n\nconst List = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        /**\n         * If `true`, the list will have margin around it and rounded corners.\n         */\n        this.inset = false;\n    }\n    /**\n     * If `ion-item-sliding` are used inside the list, this method closes\n     * any open sliding item.\n     *\n     * Returns `true` if an actual `ion-item-sliding` is closed.\n     */\n    async closeSlidingItems() {\n        const item = this.el.querySelector('ion-item-sliding');\n        if (item === null || item === void 0 ? void 0 : item.closeOpened) {\n            return item.closeOpened();\n        }\n        return false;\n    }\n    render() {\n        const mode = getIonMode(this);\n        const { lines, inset } = this;\n        return (h(Host, { key: '7f9943751542d2cbd49a4ad3f28e16d9949f70d4', role: \"list\", class: {\n                [mode]: true,\n                // Used internally for styling\n                [`list-${mode}`]: true,\n                'list-inset': inset,\n                [`list-lines-${lines}`]: lines !== undefined,\n                [`list-${mode}-lines-${lines}`]: lines !== undefined,\n            } }));\n    }\n    get el() { return getElement(this); }\n};\nList.style = {\n    ios: listIosCss,\n    md: listMdCss\n};\n\nconst listHeaderIosCss = \":host{--border-style:solid;--border-width:0;--inner-border-width:0;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:40px;border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);color:var(--color);overflow:hidden}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.list-header-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-label){-ms-flex:1 1 auto;flex:1 1 auto}:host(.list-header-lines-inset),:host(.list-header-lines-none){--border-width:0}:host(.list-header-lines-full),:host(.list-header-lines-none){--inner-border-width:0}:host{--background:transparent;--color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));padding-right:var(--ion-safe-area-right);padding-left:calc(var(--ion-safe-area-left, 0px) + 16px);position:relative;-ms-flex-align:end;align-items:flex-end;font-size:min(1.375rem, 56.1px);font-weight:700;letter-spacing:0}:host-context([dir=rtl]){padding-right:calc(var(--ion-safe-area-right, 0px) + 16px);padding-left:var(--ion-safe-area-left)}@supports selector(:dir(rtl)){:host(:dir(rtl)){padding-right:calc(var(--ion-safe-area-right, 0px) + 16px);padding-left:var(--ion-safe-area-left)}}::slotted(ion-button),::slotted(ion-label){margin-top:29px;margin-bottom:6px}::slotted(ion-button){--padding-top:0;--padding-bottom:0;-webkit-margin-start:3px;margin-inline-start:3px;-webkit-margin-end:3px;margin-inline-end:3px;min-height:1.4em}:host(.list-header-lines-full){--border-width:0 0 0.55px 0}:host(.list-header-lines-inset){--inner-border-width:0 0 0.55px 0}\";\n\nconst listHeaderMdCss = \":host{--border-style:solid;--border-width:0;--inner-border-width:0;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:40px;border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);color:var(--color);overflow:hidden}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.list-header-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-label){-ms-flex:1 1 auto;flex:1 1 auto}:host(.list-header-lines-inset),:host(.list-header-lines-none){--border-width:0}:host(.list-header-lines-full),:host(.list-header-lines-none){--inner-border-width:0}:host{--background:transparent;--color:var(--ion-text-color, #000);--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));padding-right:var(--ion-safe-area-right);padding-left:calc(var(--ion-safe-area-left, 0px) + 16px);min-height:45px;font-size:0.875rem}:host-context([dir=rtl]){padding-right:calc(var(--ion-safe-area-right, 0px) + 16px);padding-left:var(--ion-safe-area-left)}@supports selector(:dir(rtl)){:host(:dir(rtl)){padding-right:calc(var(--ion-safe-area-right, 0px) + 16px);padding-left:var(--ion-safe-area-left)}}:host(.list-header-lines-full){--border-width:0 0 1px 0}:host(.list-header-lines-inset){--inner-border-width:0 0 1px 0}\";\n\nconst ListHeader = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    render() {\n        const { lines } = this;\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'd9bc827ad8cc77231efddc2435831a7fc080f77d', class: createColorClasses(this.color, {\n                [mode]: true,\n                [`list-header-lines-${lines}`]: lines !== undefined,\n            }) }, h(\"div\", { key: '02dd9698304a7b2997ea1487e2f308bebea2b44c', class: \"list-header-inner\" }, h(\"slot\", { key: '01d63a572c003286ae467a1ab23631e37e695042' }))));\n    }\n};\nListHeader.style = {\n    ios: listHeaderIosCss,\n    md: listHeaderMdCss\n};\n\nconst noteIosCss = \":host{color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-base)}:host{--color:var(--ion-color-step-350, var(--ion-text-color-step-650, #a6a6a6));font-size:max(14px, 1rem)}\";\n\nconst noteMdCss = \":host{color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-base)}:host{--color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));font-size:0.875rem}\";\n\nconst Note = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '0ec2ef7367d867fd7588611953f696eecdf3221e', class: createColorClasses(this.color, {\n                [mode]: true,\n            }) }, h(\"slot\", { key: 'a200b94ddffb29cf6dabe6e984220930ea7efdef' })));\n    }\n};\nNote.style = {\n    ios: noteIosCss,\n    md: noteMdCss\n};\n\nconst skeletonTextCss = \":host{--background:rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065);border-radius:var(--border-radius, inherit);display:block;width:100%;height:inherit;margin-top:4px;margin-bottom:4px;background:var(--background);line-height:10px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;pointer-events:none}span{display:inline-block}:host(.in-media){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;height:100%}:host(.skeleton-text-animated){position:relative;background:-webkit-gradient(linear, left top, right top, color-stop(8%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065)), color-stop(18%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.135)), color-stop(33%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065)));background:linear-gradient(to right, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065) 8%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.135) 18%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065) 33%);background-size:800px 104px;-webkit-animation-duration:1s;animation-duration:1s;-webkit-animation-fill-mode:forwards;animation-fill-mode:forwards;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;-webkit-animation-name:shimmer;animation-name:shimmer;-webkit-animation-timing-function:linear;animation-timing-function:linear}@-webkit-keyframes shimmer{0%{background-position:-400px 0}100%{background-position:400px 0}}@keyframes shimmer{0%{background-position:-400px 0}100%{background-position:400px 0}}\";\n\nconst SkeletonText = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        /**\n         * If `true`, the skeleton text will animate.\n         */\n        this.animated = false;\n    }\n    componentWillLoad() {\n        this.emitStyle();\n    }\n    emitStyle() {\n        // The emitted property is used by item in order\n        // to add the item-skeleton-text class which applies\n        // overflow: hidden to its label\n        const style = {\n            'skeleton-text': true,\n        };\n        this.ionStyle.emit(style);\n    }\n    render() {\n        const animated = this.animated && config.getBoolean('animated', true);\n        const inMedia = hostContext('ion-avatar', this.el) || hostContext('ion-thumbnail', this.el);\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'd86ef7392507cdbf48dfd3a71f02d7a83eda4aae', class: {\n                [mode]: true,\n                'skeleton-text-animated': animated,\n                'in-media': inMedia,\n            } }, h(\"span\", { key: '8e8b5a232a6396d2bba691b05f9de4da44b2965c' }, \"\\u00A0\")));\n    }\n    get el() { return getElement(this); }\n};\nSkeletonText.style = skeletonTextCss;\n\nexport { Item as ion_item, ItemDivider as ion_item_divider, ItemGroup as ion_item_group, Label as ion_label, List as ion_list, ListHeader as ion_list_header, Note as ion_note, SkeletonText as ion_skeleton_text };\n"], "names": ["r", "registerInstance", "n", "forceUpdate", "e", "getIonMode", "h", "j", "Host", "k", "getElement", "d", "createEvent", "l", "config", "b", "inheritAttributes", "raf", "hostContext", "c", "createColorClasses", "o", "openURL", "p", "chevronForward", "itemIosCss", "itemMdCss", "<PERSON><PERSON>", "constructor", "hostRef", "labelColorStyles", "itemStyles", "Map", "inheritedAriaAttributes", "multipleInputs", "focusable", "isInteractive", "button", "detailIcon", "disabled", "routerDirection", "type", "updateInteractivityOnSlotChange", "setIsInteractive", "setMultipleInputs", "buttonChanged", "isFocusable", "labelColorChanged", "ev", "color", "undefined", "detail", "itemStyle", "stopPropagation", "tagName", "target", "updatedStyles", "newStyles", "childStyles", "get", "hasStyleChange", "Object", "keys", "for<PERSON>ach", "key", "itemKey", "length", "set", "connectedCallback", "hasStartEl", "componentWillLoad", "el", "componentDidLoad", "totalNestedInputs", "covers", "querySelectorAll", "inputs", "clickables", "isClickable", "hasCover", "href", "canActivate", "focus<PERSON><PERSON><PERSON><PERSON>", "querySelector", "startEl", "classList", "add", "getFirstInteractive", "controls", "render", "download", "lines", "rel", "routerAnimation", "mode", "clickable", "TagType", "attrs", "clickFn", "firstInteractive", "onClick", "path", "<PERSON><PERSON><PERSON>", "isTrusted", "clickedWithinShadowRoot", "shadowRoot", "contains", "setFocus", "click", "stopImmediatePropagation", "showDetail", "value", "assign", "ariaDisabled", "inList", "firstInteractiveNeedsPointerCursor", "includes", "class", "item", "document", "dir", "role", "part", "name", "onSlotchange", "icon", "lazy", "watchers", "style", "ios", "md", "itemDividerIosCss", "itemDividerMdCss", "ItemDivider", "sticky", "itemGroupIosCss", "itemGroupMdCss", "ItemGroup", "labelIosCss", "labelMdCss", "Label", "ionColor", "ionStyle", "inRange", "noAnimate", "closest", "position", "emitStyle", "emitColor", "setTimeout", "colorChanged", "positionChanged", "emit", "label", "listIosCss", "listMdCss", "List", "inset", "closeSlidingItems", "_this", "_asyncToGenerator", "closeOpened", "listHeaderIosCss", "listHeaderMdCss", "ListHeader", "noteIosCss", "noteMdCss", "Note", "skeletonTextCss", "SkeletonText", "animated", "getBoolean", "inMedia", "ion_item", "ion_item_divider", "ion_item_group", "ion_label", "ion_list", "ion_list_header", "ion_note", "ion_skeleton_text"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}