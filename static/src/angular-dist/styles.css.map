{"version": 3, "file": "styles.css", "mappings": ";;;AAMA,MAOI,6BACA,oCACA,mCACA,gDACA,mCACA,kCALA,+BACA,sCACA,qCACA,kDACA,qCACA,oCALA,8BACA,sCACA,oCACA,iDACA,oCACA,mCALA,6BACA,qCACA,mCACA,0CACA,mCACA,kCALA,6BACA,qCACA,mCACA,0CACA,mCACA,kCALA,4BACA,mCACA,kCACA,+CACA,kCACA,iCALA,2BACA,qCACA,iCACA,wCACA,iCACA,gCALA,4BACA,qCACA,kCACA,+CACA,kCACA,iCALA,0BACA,iCACA,gCACA,6CACA,gCACA,+BAOJ,SACE,8FAEF,QACE,2DAGF,KACE,uCACA,2CAGF,KACE,uCACA,4BAGF,wBACE,gBAYF,iQAIE,eChC+B,CDwCjC,qIAEE,kBCvC+B,CD+CjC,yEAEE,qDACA,mDAOF,qCACE,4CACE,0BA+BJ,2DACE,sBACA,mBAaF,wCACE,4CAyBA,mBATA,+DACA,0EACA,yEACA,0FACA,sEACA,oEAIA,qBATA,iEACA,4EACA,2EACA,4FACA,wEACA,sEAIA,oBATA,gEACA,4EACA,0EACA,2FACA,uEACA,qEAIA,mBATA,+DACA,2EACA,yEACA,oFACA,sEACA,oEAIA,mBATA,+DACA,2EACA,yEACA,oFACA,sEACA,oEAIA,kBATA,8DACA,yEACA,wEACA,yFACA,qEACA,mEAIA,iBATA,6DACA,2EACA,uEACA,kFACA,oEACA,kEAIA,kBATA,8DACA,2EACA,wEACA,yFACA,qEACA,mEAIA,gBATA,4DACA,uEACA,sEACA,uFACA,mEACA,iEAaF,UEmOM,MFlOuB,CEmOvB,OFnOiB,CEuPrB,KFvPkB,CEwPlB,QFxPwB,CAExB,aACA,kBAEA,sBACA,8BAEA,0BACA,SG3H+B,CHsIjC,oBACE,kBAEA,qBAEA,YAGF,8CACE,kBAGF,iRAeE,wBAGF,oBACE,UAGF,wCACE,cAOF,6CACE,8BAGF,6BACE,KACE,mDAIJ,iDACE,KACE,8CACA,oDACA,gDACA,mDAQJ,mFAEE,cAOF,cE2TM,iCFvTN,mBACE,eACA,0BAUA,oBAMA,kBAUF,+BACE,mBAGF,4CACE,gBAGF,0BACE,sCIzS+B,CJ4SjC,mCACE,qCI1S+B,CJiTjC,yBACE,uCKxT8B,CL2ThC,uBACE,uCK5T8B,CLgUhC,6EACE,2BACA,4BAEF,4EACE,8BACA,+BAEF,qEACE,oBAGF,0EACE,2DAGF,uCACE,yCAEE,4BASJ,qJAEE,yBAGF,2GACE,oBACA,0BAGF,uMAEE,aAMF,6CACE,iBASF,6BACE,qBACA,sBAUF,wDACE,YACA,iBAUF,uNAOE,aAaF,oCACE,gBAUF,4CACE,kBACE;;;;;AMhbJ,4BAIE,wBAKF,sBACE,aAEA,SAQF,SAEE,iBAOF,IACE,eAMF,GACE,WAEA,eAEA,uBAIF,IACE,cAIF,kBAIE,gCACA,cAgBF,4BAIE,oBACA,mBAGF,SACE,cAEA,YAEA,aACA,cAGF,sBACE,iBAGF,2BAIE,SAEA,aACA,cAQF,6DAGE,eAEA,0BAIF,qNAkBE,0BAGF,6BAEE,oBAGF,OACE,UACA,SACA,gBACA,oBACA,mBACA,qBACA,cACA,oBACA,eAEA,0BAGF,WACE,eAIF,kDAGE,eAIF,iDAEE,UAEA,SAMF,4FAEE,YAMF,+FAEE,wBAQF,MACE,yBACA,iBAGF,MAEE;;;;;AC1MF,EACE,sBAEA,0CACA,wCACA,2BAGF,KACE,WACA,YACA,8BAEA,sBAGF,iBACE,cAGF,aACE,aAGF,KL8EE,kCACA,mCA0NE,aKvSc,CLwSd,cKxSc,CL4ThB,YK5TgB,CL6ThB,eK7TgB,CLuSd,cKtSe,CLuSf,eKvSe,CL2TjB,aK3TiB,CL4TjB,gBK5TiB,CAEjB,eAEA,WACA,eACA,YACA,gBAsBA,wBAEA,kCAEA,gBAEA,0BAEA,uBAEA,yBAEA,qBAEA,2BACA,8BAEA;;;;;ACnDF,KACE,mCAOF,uCACE,KAIE,2DAIJ,EACE,6BACA,wCAGF,kBN0SE,eMpSgB,CNqShB,kBMrS4B,CAE5B,eArD6B,CAuD7B,eApD6B,CAuD/B,GN6RE,eM5RgB,CAEhB,kBAvD6B,CA0D/B,GNuRE,eMtRgB,CAEhB,gBA1D6B,CA6D/B,GACE,kBA3D6B,CA8D/B,GACE,iBA5D6B,CA+D/B,GACE,kBA7D6B,CAgE/B,GACE,cA9D6B,CAiE/B,MACE,cAGF,QAEE,kBAEA,cAEA,cAEA,wBAGF,IACE,WAGF,IACE;;;;;AC1GF,UACE,wBAUE,aACE,wBAOF,eACE,wBPsIF,yBO/IA,gBACE,yBPuLF,4BOhLA,kBACE,yBPsIF,yBO/IA,gBACE,yBPuLF,4BOhLA,kBACE,yBPsIF,yBO/IA,gBACE,yBPuLF,4BOhLA,kBACE,yBPsIF,0BO/IA,gBACE,yBPuLF,6BOhLA,kBACE;;;;;ACZN,gBACE,mBACA,iBACA,iBACA,oBRsTE,cQpTe,CRqTf,eQrTe,CRyUjB,aQzUiB,CR0UjB,gBQ1UiB,CAGnB,aACE,0CACA,wCACA,wCACA,2CRkTE,6CQrUM,CRuUN,2CQvUM,CRqVR,oCQrVQ,CRsVR,uCQtVQ,CAwBV,iBACE,wCR4TA,oCQrVQ,CA8BV,mBACE,0CRsSE,6CQrUM,CAoCV,iBACE,wCRkSE,2CQvUM,CA0CV,oBACE,2CR2SA,uCQtVQ,CAgDV,sBACE,wCACA,2CRmSA,oCQrVQ,CRsVR,uCQtVQ,CAuDV,wBACE,0CACA,wCR4QE,6CQrUM,CRuUN,2CQvUM,CAkEV,eACE,kBACA,gBACA,gBACA,mBR0PE,aQxPc,CRyPd,cQzPc,CR6QhB,YQ7QgB,CR8QhB,eQ9QgB,CAGlB,YACE,wCACA,sCACA,sCACA,yCRsPE,2CQpUK,CRsUL,yCQtUK,CRoVP,kCQpVO,CRqVP,qCQrVO,CAmFT,gBACE,sCRgQA,kCQpVO,CAyFT,kBACE,wCR0OE,2CQpUK,CA+FT,gBACE,sCRsOE,yCQtUK,CAqGT,mBACE,yCR+OA,qCQrVO,CA2GT,qBACE,sCACA,yCRuOA,kCQpVO,CRqVP,qCQrVO,CAkHT,uBACE,wCACA,sCRgNE,2CQpUK,CRsUL,yCQtUK;;;;;ACGL,gBT2dE,sBSvdF,iBTudE,uBSndF,iBTqcE,sBAzNO,0CA4NP,uBArNO,2BAqNP,uBA/MJ,8BAcW,0BAiMP,wBSpcF,eTwcE,uBAhOO,wCAmOP,sBA5NO,yBA4NP,sBAtNJ,8BAcW,wBAwMP,uBAnUF,yBSpJA,mBT2dE,sBSvdF,oBTudE,uBSndF,oBTqcE,sBAzNO,6CA4NP,uBArNO,8BAqNP,uBA/MJ,8BAcW,6BAiMP,wBSpcF,kBTwcE,uBAhOO,2CAmOP,sBA5NO,4BA4NP,sBAtNJ,8BAcW,2BAwMP,wBAnUF,yBSpJA,mBT2dE,sBSvdF,oBTudE,uBSndF,oBTqcE,sBAzNO,6CA4NP,uBArNO,8BAqNP,uBA/MJ,8BAcW,6BAiMP,wBSpcF,kBTwcE,uBAhOO,2CAmOP,sBA5NO,4BA4NP,sBAtNJ,8BAcW,2BAwMP,wBAnUF,yBSpJA,mBT2dE,sBSvdF,oBTudE,uBSndF,oBTqcE,sBAzNO,6CA4NP,uBArNO,8BAqNP,uBA/MJ,8BAcW,6BAiMP,wBSpcF,kBTwcE,uBAhOO,2CAmOP,sBA5NO,4BA4NP,sBAtNJ,8BAcW,2BAwMP,wBAnUF,0BSpJA,mBT2dE,sBSvdF,oBTudE,uBSndF,oBTqcE,sBAzNO,6CA4NP,uBArNO,8BAqNP,uBA/MJ,8BAcW,6BAiMP,wBSpcF,kBTwcE,uBAhOO,2CAmOP,sBA5NO,4BA4NP,sBAtNJ,8BAcW,2BAwMP;;;;;AUvdF,iBACE,6BAGF,kBACE,8BAGF,gBACE,4BAGF,cACE,0BAGF,eACE,2BAGF,gBACE,4BAGF,iBACE,8BAGF,eACE,8BVuHF,yBUpJA,oBACE,6BAGF,qBACE,8BAGF,mBACE,4BAGF,iBACE,0BAGF,kBACE,2BAGF,mBACE,4BAGF,oBACE,8BAGF,kBACE,+BVuHF,yBUpJA,oBACE,6BAGF,qBACE,8BAGF,mBACE,4BAGF,iBACE,0BAGF,kBACE,2BAGF,mBACE,4BAGF,oBACE,8BAGF,kBACE,+BVuHF,yBUpJA,oBACE,6BAGF,qBACE,8BAGF,mBACE,4BAGF,iBACE,0BAGF,kBACE,2BAGF,mBACE,4BAGF,oBACE,8BAGF,kBACE,+BVuHF,0BUpJA,oBACE,6BAGF,qBACE,8BAGF,mBACE,4BAGF,iBACE,0BAGF,kBACE,2BAGF,mBACE,4BAGF,oBACE,8BAGF,kBACE;;;;;AC7BF,oBAEE,oCAGF,oBAEE,oCAGF,qBAEE,qCXwIF,yBWpJA,uBAEE,oCAGF,uBAEE,oCAGF,wBAEE,sCXwIF,yBWpJA,uBAEE,oCAGF,uBAEE,oCAGF,wBAEE,sCXwIF,yBWpJA,uBAEE,oCAGF,uBAEE,oCAGF,wBAEE,sCXwIF,0BWpJA,uBAEE,oCAGF,uBAEE,oCAGF,wBAEE;;;;;ACjBN,sBACE,iCAGF,oBACE,+BAGF,uBACE,6BAGF,wBACE,8BAGF,yBACE,+BAGF,qBACE,2BAOF,UACE,0BAGF,YACE,4BAGF,kBACE,kCAOF,2BACE,sCAGF,4BACE,kCAGF,yBACE,oCAGF,4BACE,wCAGF,6BACE,yCAGF,4BACE,wCAOF,uBACE,kCAGF,wBACE,8BAGF,qBACE,gCAGF,yBACE,+BAGF,0BACE;;;;;ACjGF;AAYA;AACA;EACE;EACA;EACA;EACA;AAAF;;AAGA;EACE;EACA;EACA;EACA;EACA;AAAF;AAEE;EACE;EACA;EACA;AAAJ;AAGE;EACE;EACA;AADJ;;AAKA;AACA;EACE;EACA;EACA;EACA;EACA;EACA;AAFF;;AAKA;EACE;EACA;EACA;EACA;AAFF;;AAKA;AACA;EACE;AAFF;;AAKA;EACE;IAAO;IAAY;EAAnB;EACA;IAAK;IAAY;EAGjB;AACF;AADA;AACA;EACE;EACA;AAGF;AADE;EACE;EACA;EACA;AAGJ;;AACA;EACE;EACA;EACA;EACA;AAEF;;AACA;EACE;EACA;EACA;EACA;AAEF;AAAE;EACE;EACA;AAEJ;AACE;EACE;AACJ;;AAGA;AACA;EACE;EACA;EACA;AAAF;AAEE;EACE;EACA;EACA;AAAJ;;AAIA;EACE;EACA;EACA;EACA;AADF;AAGE;EACE;EACA;EACA;EACA;AADJ;;AAKA;AACA;EACE;EACA;EACA;EACA;EACA;AAFF;AAIE;EACE;EACA;EACA;AAFJ;;AAMA;AACA;EACE;EACA;AAHF;AAKE;EACE;EACA;EACA;AAHJ;AAME;EACE;EACA;AAJJ;;AAQA;AACA;EACE;EACA;EACA;EACA;EACA;EACA;AALF;;AAQA;EACE;EACA;EACA;EACA;EACA;EACA;AALF;;AAQA;AACA;EACE;IACE;EALF;EAQA;IACE;IACA;EANF;EAQE;IACE;EANJ;EAUA;IACE;EARF;EAUE;IACE;EARJ;EAYA;IACE;IACA;EAVF;EAaA;IACE;EAXF;AACF;AAcA;EACE;IACE;EAZF;EAeA;IACE;EAbF;AACF,C", "sources": ["./node_modules/@ionic/angular/src/css/core.scss", "./node_modules/@ionic/angular/src/components/modal/modal.vars.scss", "./node_modules/@ionic/angular/src/themes/ionic.mixins.scss", "./node_modules/@ionic/angular/src/themes/ionic.globals.scss", "./node_modules/@ionic/angular/src/components/menu/menu.ios.vars.scss", "./node_modules/@ionic/angular/src/components/menu/menu.md.vars.scss", "./node_modules/@ionic/angular/src/css/normalize.scss", "./node_modules/@ionic/angular/src/css/structure.scss", "./node_modules/@ionic/angular/src/css/typography.scss", "./node_modules/@ionic/angular/src/css/display.scss", "./node_modules/@ionic/angular/src/css/padding.scss", "./node_modules/@ionic/angular/src/css/float-elements.scss", "./node_modules/@ionic/angular/src/css/text-alignment.scss", "./node_modules/@ionic/angular/src/css/text-transformation.scss", "./node_modules/@ionic/angular/src/css/flex-utils.scss", "./src/styles.scss"], "sourcesContent": ["@use \"sass:map\";\n@import \"../themes/ionic.globals\";\n@import \"../components/menu/menu.ios.vars\";\n@import \"../components/menu/menu.md.vars\";\n@import \"../components/modal/modal.vars\";\n\n:root {\n  /**\n   * Loop through each color object from the\n   * `ionic.theme.default.scss` file\n   * and generate CSS Variables for each color.\n   */\n  @each $color-name, $value in $colors {\n    --ion-color-#{$color-name}: #{map.get($value, base)};\n    --ion-color-#{$color-name}-rgb: #{color-to-rgb-list(map.get($value, base))};\n    --ion-color-#{$color-name}-contrast: #{map.get($value, contrast)};\n    --ion-color-#{$color-name}-contrast-rgb: #{color-to-rgb-list(map.get($value, contrast))};\n    --ion-color-#{$color-name}-shade: #{map.get($value, shade)};\n    --ion-color-#{$color-name}-tint: #{map.get($value, tint)};\n  }\n}\n\n// Ionic Font Family\n// --------------------------------------------------\n\nhtml.ios {\n  --ion-default-font: -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Roboto\", sans-serif;\n}\nhtml.md {\n  --ion-default-font: \"Roboto\", \"Helvetica Neue\", sans-serif;\n}\n\nhtml {\n  --ion-dynamic-font: -apple-system-body;\n  --ion-font-family: var(--ion-default-font);\n}\n\nbody {\n  background: var(--ion-background-color);\n  color: var(--ion-text-color);\n}\n\nbody.backdrop-no-scroll {\n  overflow: hidden;\n}\n\n// Modal - Card Style\n// --------------------------------------------------\n/**\n * Card style modal needs additional padding on the\n * top of the header. We accomplish this by targeting\n * the first toolbar in the header.\n * Footer also needs this. We do not adjust the bottom\n * padding though because of the safe area.\n */\nhtml.ios ion-modal.modal-card ion-header ion-toolbar:first-of-type,\nhtml.ios ion-modal.modal-sheet ion-header ion-toolbar:first-of-type,\nhtml.ios ion-modal ion-footer ion-toolbar:first-of-type,\nhtml.ios ion-footer.modal-footer-moving ion-toolbar:first-of-type {\n  padding-top: $modal-sheet-padding-top;\n}\n\n/**\n* Card style modal needs additional padding on the\n* bottom of the header. We accomplish this by targeting\n* the last toolbar in the header.\n*/\nhtml.ios ion-modal.modal-card ion-header ion-toolbar:last-of-type,\nhtml.ios ion-modal.modal-sheet ion-header ion-toolbar:last-of-type {\n  padding-bottom: $modal-sheet-padding-bottom;\n}\n\n/**\n* Add padding on the left and right\n* of toolbars while accounting for\n* safe area values when in landscape.\n*/\nhtml.ios ion-modal ion-toolbar,\nhtml.ios .modal-footer-moving ion-toolbar {\n  padding-right: calc(var(--ion-safe-area-right) + 8px);\n  padding-left: calc(var(--ion-safe-area-left) + 8px);\n}\n\n/**\n * Card style modal on iPadOS\n * should only have backdrop on first instance.\n */\n@media screen and (min-width: 768px) {\n  html.ios ion-modal.modal-card:first-of-type {\n    --backdrop-opacity: 0.18;\n  }\n}\n\n/**\n * Subsequent modals should not have a backdrop/box shadow\n * as it will cause the screen to appear to get progressively\n * darker. With Ionic 6, declarative modals made it\n * possible to have multiple non-presented modals in the DOM,\n * so we could no longer rely on ion-modal:first-of-type.\n * Here we disable the opacity/box-shadow for every modal\n * that comes after the first presented modal.\n *\n * Note: ion-modal:not(.overlay-hidden):first-of-type\n * does not match the first modal to not have\n * the .overlay-hidden class, it will match the\n * first modal in general only if it does not\n * have the .overlay-hidden class.\n * The :nth-child() pseudo-class has support\n * for selectors which would help us here. At the\n * time of writing it does not have great cross browser\n * support.\n *\n * Note 2: This should only apply to non-card and\n * non-sheet modals. Card and sheet modals have their\n * own criteria for displaying backdrops/box shadows.\n *\n * Do not use :not(.overlay-hidden) in place of\n * .show-modal because that triggers a memory\n * leak in Blink: https://bugs.chromium.org/p/chromium/issues/detail?id=1418768\n */\nion-modal.modal-default.show-modal ~ ion-modal.modal-default {\n  --backdrop-opacity: 0;\n  --box-shadow: none;\n}\n\n/**\n * This works around a bug in WebKit where the\n * content will overflow outside of the bottom border\n * radius when re-painting. As long as a single\n * border radius value is set on .ion-page, this\n * issue does not happen. We set the top left radius\n * here because the top left corner will always have a\n * radius no matter the platform.\n * This behavior only applies to card modals.\n */\nhtml.ios ion-modal.modal-card .ion-page {\n  border-top-left-radius: var(--border-radius);\n}\n\n// Ionic Colors\n// --------------------------------------------------\n// Generates the color classes and variables based on the\n// colors map\n\n@mixin generate-color($color-name) {\n  $value: map-get($colors, $color-name);\n\n  $base: map-get($value, base);\n  $contrast: map-get($value, contrast);\n  $shade: map-get($value, shade);\n  $tint: map-get($value, tint);\n\n  --ion-color-base: var(--ion-color-#{$color-name}, #{$base}) !important;\n  --ion-color-base-rgb: var(--ion-color-#{$color-name}-rgb, #{color-to-rgb-list($base)}) !important;\n  --ion-color-contrast: var(--ion-color-#{$color-name}-contrast, #{$contrast}) !important;\n  --ion-color-contrast-rgb: var(--ion-color-#{$color-name}-contrast-rgb, #{color-to-rgb-list($contrast)}) !important;\n  --ion-color-shade: var(--ion-color-#{$color-name}-shade, #{$shade}) !important;\n  --ion-color-tint: var(--ion-color-#{$color-name}-tint, #{$tint}) !important;\n}\n\n@each $color-name, $value in $colors {\n  .ion-color-#{$color-name} {\n    @include generate-color($color-name);\n  }\n}\n\n\n// Page Container Structure\n// --------------------------------------------------\n\n.ion-page {\n  @include position(0, 0, 0, 0);\n\n  display: flex;\n  position: absolute;\n\n  flex-direction: column;\n  justify-content: space-between;\n\n  contain: layout size style;\n  z-index: $z-index-page-container;\n}\n\n/**\n * When making custom dialogs, using\n * ion-content is not required. As a result,\n * some developers may wish to have dialogs\n * that are automatically sized by the browser.\n * These changes allow certain dimension values\n * such as fit-content to work correctly.\n */\nion-modal > .ion-page {\n  position: relative;\n\n  contain: layout style;\n\n  height: 100%;\n}\n\n.split-pane-visible > .ion-page.split-pane-main {\n  position: relative;\n}\n\nion-route,\nion-route-redirect,\nion-router,\nion-select-option,\nion-nav-controller,\nion-menu-controller,\nion-action-sheet-controller,\nion-alert-controller,\nion-loading-controller,\nion-modal-controller,\nion-picker-controller,\nion-popover-controller,\nion-toast-controller,\n.ion-page-hidden {\n  /* stylelint-disable-next-line declaration-no-important */\n  display: none !important;\n}\n\n.ion-page-invisible {\n  opacity: 0;\n}\n\n.can-go-back > ion-header ion-back-button {\n  display: block;\n}\n\n\n// Ionic Safe Margins\n// --------------------------------------------------\n\nhtml.plt-ios.plt-hybrid, html.plt-ios.plt-pwa {\n  --ion-statusbar-padding: 20px;\n}\n\n@supports (padding-top: 20px) {\n  html {\n    --ion-safe-area-top: var(--ion-statusbar-padding);\n  }\n}\n\n@supports (padding-top: env(safe-area-inset-top)) {\n  html {\n    --ion-safe-area-top: env(safe-area-inset-top);\n    --ion-safe-area-bottom: env(safe-area-inset-bottom);\n    --ion-safe-area-left: env(safe-area-inset-left);\n    --ion-safe-area-right: env(safe-area-inset-right);\n  }\n}\n\n\n// Global Card Styles\n// --------------------------------------------------\n\nion-card.ion-color .ion-inherit-color,\nion-card-header.ion-color .ion-inherit-color {\n  color: inherit;\n}\n\n\n// Menu Styles\n// --------------------------------------------------\n\n.menu-content {\n  @include transform(translate3d(0, 0, 0));\n}\n\n.menu-content-open {\n  cursor: pointer;\n  touch-action: manipulation;\n\n  /**\n   * The containing element itself should be clickable but\n   * everything inside of it should not clickable when menu is open\n   *\n   * Setting pointer-events after scrolling has already started\n   * will not cancel scrolling which is why we also set\n   * overflow-y below.\n   */\n  pointer-events: none;\n\n  /**\n   * This accounts for scenarios where the main content itself\n   * is scrollable.\n   */\n  overflow-y: hidden;\n}\n\n/**\n * Setting overflow cancels any in-progress scrolling\n * when the menu opens. This prevents users from accidentally\n * scrolling the main content while also dragging the menu open.\n * The code below accounts for both ion-content and then custom\n * scroll containers within ion-content (such as virtual scroll)\n */\n.menu-content-open ion-content {\n  --overflow: hidden;\n}\n\n.menu-content-open .ion-content-scroll-host {\n  overflow: hidden;\n}\n\n.ios .menu-content-reveal {\n  box-shadow: $menu-ios-box-shadow-reveal;\n}\n\n[dir=rtl].ios .menu-content-reveal {\n  box-shadow: $menu-ios-box-shadow-reveal-rtl;\n}\n\n.ios .menu-content-push {\n  box-shadow: $menu-ios-box-shadow-push;\n}\n\n.md .menu-content-reveal {\n  box-shadow: $menu-md-box-shadow;\n}\n\n.md .menu-content-push {\n  box-shadow: $menu-md-box-shadow;\n}\n\n// Accordion Styles\nion-accordion-group.accordion-group-expand-inset > ion-accordion:first-of-type {\n  border-top-left-radius: 8px;\n  border-top-right-radius: 8px;\n}\nion-accordion-group.accordion-group-expand-inset > ion-accordion:last-of-type {\n  border-bottom-left-radius: 8px;\n  border-bottom-right-radius: 8px;\n}\nion-accordion-group > ion-accordion:last-of-type ion-item[slot=\"header\"] {\n  --border-width: 0px;\n}\n\nion-accordion.accordion-animated > [slot=\"header\"] .ion-accordion-toggle-icon {\n  transition: 300ms transform cubic-bezier(0.25, 0.8, 0.5, 1);\n}\n\n@media (prefers-reduced-motion: reduce) {\n  ion-accordion .ion-accordion-toggle-icon {\n    /* stylelint-disable declaration-no-important */\n    transition: none !important;\n  }\n}\n/**\n * The > [slot=\"header\"] selector ensures that we do\n * not modify toggle icons for any nested accordions. The state\n * of one accordion should not affect any accordions inside\n * of a nested accordion group.\n */\nion-accordion.accordion-expanding > [slot=\"header\"] .ion-accordion-toggle-icon,\nion-accordion.accordion-expanded > [slot=\"header\"] .ion-accordion-toggle-icon {\n  transform: rotate(180deg);\n}\n\nion-accordion-group.accordion-group-expand-inset.md > ion-accordion.accordion-previous ion-item[slot=\"header\"] {\n  --border-width: 0px;\n  --inner-border-width: 0px;\n}\n\nion-accordion-group.accordion-group-expand-inset.md > ion-accordion.accordion-expanding:first-of-type,\nion-accordion-group.accordion-group-expand-inset.md > ion-accordion.accordion-expanded:first-of-type {\n  margin-top: 0;\n}\n\n// Safari/iOS 15 changes the appearance of input[type=\"date\"].\n// For backwards compatibility from Ionic 5/Safari 14 designs,\n// we override the appearance only when using within an ion-input.\nion-input input::-webkit-date-and-time-value {\n  text-align: start;\n}\n\n/**\n * The .ion-datetime-button-overlay class contains\n * styles that allow any modal/popover to be\n * sized according to the dimensions of the datetime\n * when used with ion-datetime-button.\n */\n.ion-datetime-button-overlay {\n  --width: fit-content;\n  --height: fit-content;\n}\n\n/**\n * The grid variant can scale down when inline.\n * When used in a `fit-content` overlay, this causes\n * the overlay to shrink when the month/year picker is open.\n * Explicitly setting the dimensions lets us have a consistently\n * sized grid interface.\n */\n.ion-datetime-button-overlay ion-datetime.datetime-grid {\n  width: 320px;\n  min-height: 320px;\n}\n\n/**\n * When moving focus on page transitions we call .focus() on an element which can\n * add an undesired outline ring. This CSS removes the outline ring.\n * We also remove the outline ring from elements that are actively being focused\n * by the focus manager. We are intentionally selective about which elements this\n * applies to so we do not accidentally override outlines set by the developer.\n */\n[ion-last-focus],\nheader[tabindex=\"-1\"]:focus,\n[role=\"banner\"][tabindex=\"-1\"]:focus,\nmain[tabindex=\"-1\"]:focus,\n[role=\"main\"][tabindex=\"-1\"]:focus,\nh1[tabindex=\"-1\"]:focus,\n[role=\"heading\"][aria-level=\"1\"][tabindex=\"-1\"]:focus {\n  outline: none;\n}\n\n/*\n * If a popover has a child ion-content (or class equivalent) then the .popover-viewport element\n * should not be scrollable to ensure the inner content does scroll. However, if the popover\n * does not have a child ion-content (or class equivalent) then the .popover-viewport element\n * should remain scrollable. This code exists globally because popover targets\n * .popover-viewport using ::slotted which only supports simple selectors.\n *\n * Note that we do not need to account for .ion-content-scroll-host here because that\n * class should always be placed within ion-content even if ion-content is not scrollable.\n */\n.popover-viewport:has(> ion-content) {\n  overflow: hidden;\n}\n\n/**\n * :has has cross-browser support, but it is still relatively new. As a result,\n * we should fallback to the old behavior for environments that do not support :has.\n * Developers can explicitly enable this behavior by setting overflow: visible\n * on .popover-viewport if they know they are not going to use an ion-content.\n * TODO FW-6106 Remove this\n */\n@supports not selector(:has(> ion-content)) {\n  .popover-viewport {\n    overflow: hidden;\n  }\n}\n", "@import \"../../themes/ionic.globals\";\n\n// Modals\n// --------------------------------------------------\n\n/// @prop - Min width of the modal inset\n$modal-inset-min-width:         768px;\n\n/// @prop - Minimum height of the small modal inset\n$modal-inset-min-height-small:  600px;\n\n/// @prop - Minimum height of the large modal inset\n$modal-inset-min-height-large:  768px;\n\n/// @prop - Width of the large modal inset\n$modal-inset-width:             600px;\n\n/// @prop - Height of the small modal inset\n$modal-inset-height-small:      500px;\n\n/// @prop - Height of the large modal inset\n$modal-inset-height-large:      600px;\n\n/// @prop - Text color of the modal content\n$modal-text-color:              $text-color;\n\n/// @prop - Padding top of the sheet modal\n$modal-sheet-padding-top:        6px;\n\n/// @prop - Padding bottom of the sheet modal\n$modal-sheet-padding-bottom:     6px;\n", "\n/**\n * A heuristic that applies CSS to tablet\n * viewports.\n *\n * Usage:\n * @include tablet-viewport() {\n *   :host {\n *     background-color: green;\n *   }\n * }\n */\n@mixin tablet-viewport() {\n  @media screen and (min-width: 768px) {\n    @content;\n  }\n}\n\n/**\n * A heuristic that applies CSS to mobile\n * viewports (i.e. phones, not tablets).\n *\n * Usage:\n * @include mobile-viewport() {\n *   :host {\n *     background-color: blue;\n *   }\n * }\n */\n@mixin mobile-viewport() {\n  @media screen and (max-width: 767px) {\n    @content;\n  }\n}\n\n@mixin input-cover() {\n  @include position(0, null, null, 0);\n  @include margin(0);\n\n  position: absolute;\n\n  width: 100%;\n  height: 100%;\n\n  border: 0;\n  background: transparent;\n  cursor: pointer;\n\n  appearance: none;\n  outline: none;\n\n  &::-moz-focus-inner {\n    border: 0;\n  }\n}\n\n@mixin visually-hidden() {\n  position: absolute;\n\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n\n  width: 100%;\n  height: 100%;\n\n  margin: 0;\n  padding: 0;\n\n  border: 0;\n  outline: 0;\n  clip: rect(0 0 0 0);\n\n  opacity: 0;\n  overflow: hidden;\n\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n@mixin text-inherit() {\n  font-family: inherit;\n  font-size: inherit;\n  font-style: inherit;\n  font-weight: inherit;\n  letter-spacing: inherit;\n  text-decoration: inherit;\n  text-indent: inherit;\n  text-overflow: inherit;\n  text-transform: inherit;\n  text-align: inherit;\n  white-space: inherit;\n  color: inherit;\n}\n\n@mixin button-state() {\n  @include position(0, 0, 0, 0);\n\n  position: absolute;\n\n  content: \"\";\n\n  opacity: 0;\n}\n\n// Font smoothing\n// --------------------------------------------------\n\n@mixin font-smoothing() {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n}\n\n// Get the key from a map based on the index\n@function index-to-key($map, $index) {\n  $keys: map-keys($map);\n\n  @return nth($keys, $index);\n}\n\n\n// Breakpoint Mixins\n// ---------------------------------------------------------------------------------\n\n// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$screen-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// ---------------------------------------------------------------------------------\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $screen-breakpoints) {\n  $min: map-get($breakpoints, $name);\n\n  @return if($name != index-to-key($breakpoints, 1), $min, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash infront.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $screen-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $screen-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $screen-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Maximum breakpoint width. Null for the smallest (first) breakpoint.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n//\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\t// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\t// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $screen-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $screen-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n\n// Text Direction - ltr / rtl\n//\n// CSS defaults to use the ltr css, and adds [dir=rtl] selectors\n// to override ltr defaults.\n// ----------------------------------------------------------\n\n@mixin multi-dir() {\n  @content;\n\n  // $root: #{&};\n  // @at-root [dir] {\n  //   #{$root} {\n  //     @content;\n  //   }\n  // }\n}\n\n@mixin rtl() {\n  $root: #{&};\n\n  $rootSplit: str-split($root, \",\");\n  $selectors: #{add-root-selector($root, \"[dir=rtl]\")};\n  $selectorsSplit: str-split($selectors, \",\");\n\n  $hostContextSelectors: ();\n  $restSelectors: ();\n  $dirSelectors: ();\n\n  // Selectors must be split into individual selectors in case the browser\n  // doesn't support a specific selector.\n  // For example, Firefox and Safari doesn't support `:host-context()`.\n  // If an invalid selector is used, then the entire group of selectors\n  // will be ignored.\n  // @link https://www.w3.org/TR/selectors-3/#grouping\n  @each $selector in $selectorsSplit {\n    // Group the selectors back into a single selector to optimize the output.\n    @if str-index($selector, \":host-context\") {\n      $hostContextSelectors: append($hostContextSelectors, $selector, comma);\n    } @else {\n      // Group the selectors back into a single selector to optimize the output.\n      $restSelectors: append($restSelectors, $selector, comma);\n    }\n  }\n\n  // Supported by Chrome.\n  @if length($hostContextSelectors) > 0 {\n    @at-root #{$hostContextSelectors} {\n      @content;\n    }\n  }\n\n  // Supported by all browsers.\n  @if length($restSelectors) > 0 {\n    @at-root #{$restSelectors} {\n      @content;\n    }\n  }\n\n  // If browser can support `:dir()`, then add the `:dir()` selectors.\n  @supports selector(:dir(rtl)) {\n    // Adding :dir() in case the browser doesn't support `:host-context()` and does support `:dir()`.\n    // `:host-context()` is added:\n    // - through the `add-root-selector()` function.\n    // - first so that it takes precedence over `:dir()`.\n    // For example,\n    // - Firefox doesn't support `:host-context()`, but does support `:dir()`.\n    // - Safari doesn't support `:host-context()`, but Safari 16.4+ supports `:dir()`\n    // @link https://webkit.org/blog/13966/webkit-features-in-safari-16-4/\n    // -- However, there is a Webkit bug on v16 that prevents `:dir()` from working when\n    // -- the app direction is changed dynamically. v17+ works fine.\n    // -- @link https://bugs.webkit.org/show_bug.cgi?id=257133\n\n    // Supported by Firefox.\n    @at-root #{add-root-selector($root, \":dir(rtl)\", false)} {\n      @content;\n    }\n  }\n}\n\n@mixin ltr() {\n  @content;\n}\n\n\n// SVG Background Image Mixin\n// @param {string} $svg\n// ----------------------------------------------------------\n@mixin svg-background-image($svg, $flip-rtl: false) {\n  $url: url-encode($svg);\n  $viewBox: str-split(str-extract($svg, \"viewBox='\", \"'\"), \" \");\n\n  @if $flip-rtl != true or $viewBox == null {\n    @include multi-dir() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n  } @else {\n    $transform: \"transform='translate(#{nth($viewBox, 3)}, 0) scale(-1, 1)'\";\n    $flipped-url: $svg;\n    $flipped-url: str-replace($flipped-url, \"<path\", \"<path #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<line\", \"<line #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<polygon\", \"<polygon #{$transform}\");\n    $flipped-url: url-encode($flipped-url);\n\n    @include ltr () {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n    @include rtl() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$flipped-url}\");\n    }\n  }\n}\n\n// Add property horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin property-horizontal($prop, $start, $end: $start) {\n  @if $start == 0 and $end == 0 {\n    #{$prop}-left: $start;\n    #{$prop}-right: $end;\n\n  } @else {\n    -webkit-#{$prop}-start: $start;\n    #{$prop}-inline-start: $start;\n    -webkit-#{$prop}-end: $end;\n    #{$prop}-inline-end: $end;\n  }\n}\n\n// Add property for all directions\n// @param {string} $prop\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// @param {boolean} $content include content or use default\n// ----------------------------------------------------------\n@mixin property($prop, $top, $end: $top, $bottom: $top, $start: $end) {\n  @include property-horizontal($prop, $start, $end);\n  #{$prop}-top: $top;\n  #{$prop}-bottom: $bottom;\n}\n\n// Add padding horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin padding-horizontal($start, $end: $start) {\n  @include property-horizontal(padding, $start, $end);\n}\n\n// Add padding for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin padding($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(padding, $top, $end, $bottom, $start);\n}\n\n// Add margin horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin margin-horizontal($start, $end: $start) {\n  @include property-horizontal(margin, $start, $end);\n}\n\n// Add margin for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin margin($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(margin, $top, $end, $bottom, $start);\n}\n\n// Add position horizontal\n// @param {string} $start - amount to position start\n// @param {string} $end - amount to left: 0; end\n// ----------------------------------------------------------\n@mixin position-horizontal($start: null, $end: null) {\n  @if $start == $end {\n    @include multi-dir() {\n      left: $start;\n      right: $end;\n    }\n  } @else {\n    @at-root {\n      & {\n        inset-inline-start: $start;\n        inset-inline-end: $end;\n      }\n    }\n  }\n}\n\n// Add position for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin position($top: null, $end: null, $bottom: null, $start: null) {\n  @include position-horizontal($start, $end);\n  top: $top;\n  bottom: $bottom;\n}\n\n// Add border for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin border($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(border, $top, $end, $bottom, $start);\n}\n\n// Add border radius for all directions\n// @param {string} $top-start\n// @param {string} $top-end\n// @param {string} $bottom-end\n// @param {string} $bottom-start\n// ----------------------------------------------------------\n@mixin border-radius($top-start, $top-end: $top-start, $bottom-end: $top-start, $bottom-start: $top-end) {\n  @if $top-start == $top-end and $top-start == $bottom-end and $top-start == $bottom-start {\n    border-radius: $top-start;\n  } @else {\n    border-start-start-radius: $top-start;\n    border-start-end-radius: $top-end;\n    border-end-end-radius: $bottom-end;\n    border-end-start-radius: $bottom-start;\n  }\n}\n\n// Add direction for all directions\n// @param {string} $dir - Direction on LTR\n@mixin direction($dir) {\n  $other-dir: null;\n\n  @if $dir == ltr {\n    $other-dir: rtl;\n  } @else {\n    $other-dir: ltr;\n  }\n\n  @include ltr() {\n    direction: $dir;\n  }\n  @include rtl() {\n    direction: $other-dir;\n  }\n}\n\n// Add float for all directions\n// @param {string} $side\n// @param {string} $decorator - !important\n@mixin float($side, $decorator: null) {\n  @if $side == start {\n    @include ltr() {\n      float: left $decorator;\n    }\n    @include rtl() {\n      float: right $decorator;\n    }\n  } @else if $side == end {\n    @include ltr() {\n      float: right $decorator;\n    }\n    @include rtl() {\n      float: left $decorator;\n    }\n  } @else {\n    @include multi-dir() {\n      float: $side $decorator;\n    }\n  }\n}\n\n@mixin background-position($horizontal, $horizontal-amount: null, $vertical: null, $vertical-amount: null) {\n  @if $horizontal == start or $horizontal == end {\n    $horizontal-ltr: null;\n    $horizontal-rtl: null;\n    @if $horizontal == start {\n      $horizontal-ltr: left;\n      $horizontal-rtl: right;\n    } @else {\n      $horizontal-ltr: right;\n      $horizontal-rtl: left;\n    }\n\n    @include ltr() {\n      background-position: $horizontal-ltr $horizontal-amount $vertical $vertical-amount;\n    }\n    @include rtl() {\n      background-position: $horizontal-rtl $horizontal-amount $vertical $vertical-amount;\n    }\n  } @else {\n    @include multi-dir() {\n      background-position: $horizontal $horizontal-amount $vertical $vertical-amount;\n    }\n  }\n}\n\n@mixin transform-origin($x-axis, $y-axis: null) {\n  @if $x-axis == start {\n    @include ltr() {\n      transform-origin: left $y-axis;\n    }\n    @include rtl() {\n      transform-origin: right $y-axis;\n    }\n  } @else if $x-axis == end {\n    @include ltr() {\n      transform-origin: right $y-axis;\n    }\n    @include rtl() {\n      transform-origin: left $y-axis;\n    }\n  } @else if $x-axis == left or $x-axis == right {\n    @include multi-dir() {\n      transform-origin: $x-axis $y-axis;\n    }\n  } @else {\n    @include ltr() {\n      transform-origin: $x-axis $y-axis;\n    }\n    @include rtl() {\n      transform-origin: calc(100% - #{$x-axis}) $y-axis;\n    }\n  }\n}\n\n// Add transform for all directions\n// @param {string} $transforms - comma separated list of transforms\n@mixin transform($transforms...) {\n  $extra: null;\n\n  $x: null;\n  $ltr-translate: null;\n  $rtl-translate: null;\n\n  @each $transform in $transforms {\n    @if (str-index($transform, translate3d)) {\n      $transform: str-replace($transform, 'translate3d(');\n      $transform: str-replace($transform, ')');\n\n      $coordinates: str-split($transform, ',');\n\n      $x: nth($coordinates, 1);\n      $y: nth($coordinates, 2);\n      $z: nth($coordinates, 3);\n\n      $ltr-translate: translate3d($x, $y, $z);\n      $rtl-translate: translate3d(calc(-1 * #{$x}), $y, $z);\n    } @else {\n      @if $extra == null {\n        $extra: $transform;\n      } @else {\n        $extra: $extra $transform;\n      }\n    }\n  }\n\n  @if $x == '0' or $x == null {\n    @include multi-dir() {\n      transform: $ltr-translate $extra;\n    }\n  } @else {\n    @include ltr() {\n      transform: $ltr-translate $extra;\n    }\n\n    @include rtl() {\n      transform: $rtl-translate $extra;\n    }\n  }\n}\n", "\n// Global Utility Functions\n@import \"./ionic.functions.string\";\n\n// Global Color Functions\n@import \"./ionic.functions.color\";\n\n// Global Font Functions\n@import \"./ionic.functions.font\";\n\n// Global Mixins\n@import \"./ionic.mixins\";\n\n// Default Theme\n@import \"./ionic.theme.default\";\n\n\n// Default General\n// --------------------------------------------------\n$font-family-base:                  var(--ion-font-family, inherit);\n\n// Hairlines width\n$hairlines-width: .55px;\n\n// The minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries\n$screen-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px\n);\n\n// Input placeholder opacity\n// Ensures that the placeholder has the\n// correct color contrast against the background.\n$placeholder-opacity: var(--ion-placeholder-opacity, 0.6);\n\n$form-control-label-margin: 16px;\n\n// How much the stacked labels should be scaled by\n/// The value 0.75 is used to match the MD spec.\n/// iOS does not have a floating label design spec, so we standardize on 0.75.\n$form-control-label-stacked-scale: 0.75;\n\n\n// Z-Index\n// --------------------------------------------------\n// Grouped by elements which would be siblings\n\n$z-index-menu-overlay:           1000;\n$z-index-overlay:                1001;\n\n$z-index-fixed-content:          999;\n$z-index-refresher:              -1;\n\n$z-index-page-container:         0;\n$z-index-toolbar:                10;\n$z-index-toolbar-background:     -1;\n$z-index-toolbar-buttons:        99;\n\n$z-index-backdrop:               2;\n$z-index-overlay-wrapper:        10;\n\n$z-index-item-options:           1;\n$z-index-item-input:             2;\n$z-index-item-divider:           100;\n\n$z-index-reorder-selected:       100;\n", "@import \"../../themes/ionic.globals.ios\";\n\n// iOS Menu\n// --------------------------------------------------\n\n/// @prop - Box shadow color of the menu\n$menu-ios-box-shadow-color:      rgba(0, 0, 0, .08);\n\n/// @prop - Box shadow of the menu\n$menu-ios-box-shadow:            -8px 0 42px $menu-ios-box-shadow-color;\n\n/// @prop - Box shadow of the menu in rtl mode\n$menu-ios-box-shadow-rtl:        8px 0 42px $menu-ios-box-shadow-color;\n\n/// @prop - Box shadow of the reveal menu\n$menu-ios-box-shadow-reveal:     $menu-ios-box-shadow;\n\n/// @prop - Box shadow of the reveal menu\n$menu-ios-box-shadow-reveal-rtl: $menu-ios-box-shadow-rtl;\n\n/// @prop - Box shadow of the push menu\n$menu-ios-box-shadow-push:       null;\n\n/// @prop - Box shadow of the overlay menu\n$menu-ios-box-shadow-overlay:    null;\n", "@import \"../../themes/ionic.globals.md\";\n\n// Material Design Menu\n// --------------------------------------------------\n\n/// @prop - Box shadow of the menu\n$menu-md-box-shadow:            4px 0px 16px rgba(0, 0, 0, 0.18);\n", "// ! normalize.css v3.0.2 | MIT License | github.com/necolas/normalize.css\n\n\n// HTML5 display definitions\n// ==========================================================================\n\n// 1. Normalize vertical alignment of `progress` in Chrome, Firefox, and Opera.\naudio,\ncanvas,\nprogress,\nvideo {\n  vertical-align: baseline; // 1\n}\n\n// Prevent modern browsers from displaying `audio` without controls.\n// Remove excess height in iOS 5 devices.\naudio:not([controls]) {\n  display: none;\n\n  height: 0;\n}\n\n\n// Text-level semantics\n// ==========================================================================\n\n// Address style set to `bolder` in Firefox 4+, Safari, and Chrome.\nb,\nstrong {\n  font-weight: bold;\n}\n\n// Embedded content\n// ==========================================================================\n\n// Makes it so the img does not flow outside container\nimg {\n  max-width: 100%;\n}\n\n// Grouping content\n// ==========================================================================\n\nhr {\n  height: 1px;\n\n  border-width: 0;\n\n  box-sizing: content-box;\n}\n\n// Contain overflow in all browsers.\npre {\n  overflow: auto;\n}\n\n// Address odd `em`-unit font size rendering in all browsers.\ncode,\nkbd,\npre,\nsamp {\n  font-family: monospace, monospace;\n  font-size: 1em;\n}\n\n\n// Forms\n// ==========================================================================\n\n// Known limitation: by default, Chrome and Safari on OS X allow very limited\n// styling of `select`, unless a `border` property is set.\n\n// 1. Correct color not being inherited.\n//    Known issue: affects color of disabled elements.\n// 2. Correct font properties not being inherited.\n// 3. Address margins set differently in Firefox 4+, Safari, and Chrome.\n//\n\nlabel,\ninput,\nselect,\ntextarea {\n  font-family: inherit;\n  line-height: normal;\n}\n\ntextarea {\n  overflow: auto;\n\n  height: auto;\n\n  font: inherit;\n  color: inherit;\n}\n\ntextarea::placeholder {\n  padding-left: 2px;\n}\n\nform,\ninput,\noptgroup,\nselect {\n  margin: 0; // 3\n\n  font: inherit; // 2\n  color: inherit; // 1\n}\n\n// 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`\n//    and `video` controls.\n// 2. Correct inability to style clickable `input` types in iOS.\n// 3. Improve usability and consistency of cursor style between image-type\n//    `input` and others.\nhtml input[type=\"button\"], // 1\ninput[type=\"reset\"],\ninput[type=\"submit\"] {\n  cursor: pointer; // 3\n\n  -webkit-appearance: button; // 2\n}\n\n// remove 300ms delay\na,\na div,\na span,\na ion-icon,\na ion-label,\nbutton,\nbutton div,\nbutton span,\nbutton ion-icon,\nbutton ion-label,\n.ion-tappable,\n[tappable],\n[tappable] div,\n[tappable] span,\n[tappable] ion-icon,\n[tappable] ion-label,\ninput,\ntextarea {\n  touch-action: manipulation;\n}\n\na ion-label,\nbutton ion-label {\n  pointer-events: none;\n}\n\nbutton {\n  padding: 0;\n  border: 0;\n  border-radius: 0;\n  font-family: inherit;\n  font-style: inherit;\n  font-variant: inherit;\n  line-height: 1;\n  text-transform: none;\n  cursor: pointer;\n\n  -webkit-appearance: button;\n}\n\n[tappable] {\n  cursor: pointer;\n}\n\n// Re-set default cursor for disabled elements.\na[disabled],\nbutton[disabled],\nhtml input[disabled] {\n  cursor: default;\n}\n\n// Remove inner padding and border in Firefox 4+.\nbutton::-moz-focus-inner,\ninput::-moz-focus-inner {\n  padding: 0;\n\n  border: 0;\n}\n\n// Fix the cursor style for Chrome's increment/decrement buttons. For certain\n// `font-size` values of the `input`, it causes the cursor style of the\n// decrement button to change from `default` to `text`.\ninput[type=\"number\"]::-webkit-inner-spin-button,\ninput[type=\"number\"]::-webkit-outer-spin-button {\n  height: auto;\n}\n\n// Remove inner padding and search cancel button in Safari and Chrome on OS X.\n// Safari (but not Chrome) clips the cancel button when the search input has\n// padding (and `textfield` appearance).\ninput[type=\"search\"]::-webkit-search-cancel-button,\ninput[type=\"search\"]::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n\n// Tables\n// ==========================================================================//\n\n// Remove most spacing between table cells.\ntable {\n  border-collapse: collapse;\n  border-spacing: 0;\n}\n\ntd,\nth {\n  padding: 0;\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n\n// Structure\n// --------------------------------------------------\n// Adds structural css to the native html elements\n\n* {\n  box-sizing: border-box;\n\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n  -webkit-tap-highlight-color: transparent;\n  -webkit-touch-callout: none;\n}\n\nhtml {\n  width: 100%;\n  height: 100%;\n  -webkit-text-size-adjust: 100%;\n\n  text-size-adjust: 100%;\n}\n\nhtml.ion-ce body {\n  display: block;\n}\n\nhtml.plt-pwa {\n  height: 100vh;\n}\n\nbody {\n  @include font-smoothing();\n  @include margin(0);\n  @include padding(0);\n\n  position: fixed;\n\n  width: 100%;\n  max-width: 100%;\n  height: 100%;\n  max-height: 100%;\n\n  /**\n   * Because body has position: fixed,\n   * it should be promoted to its own\n   * layer.\n   *\n   * WebKit does not always promote\n   * the body to its own layer on page\n   * load in Ionic apps. Once scrolling on\n   * ion-content starts, WebKit will promote\n   * body. Unfortunately, this causes a re-paint\n   * which results in scrolling being halted\n   * until the next user gesture.\n   *\n   * This impacts the Custom Elements build.\n   * The lazy loaded build causes the browser to\n   * re-paint during hydration which causes WebKit\n   * to promote body to its own layer.\n   * In the CE Build, this hydration does not\n   * happen, so the additional re-paint does not occur.\n   */\n  transform: translateZ(0);\n\n  text-rendering: optimizeLegibility;\n\n  overflow: hidden;\n\n  touch-action: manipulation;\n\n  -webkit-user-drag: none;\n\n  -ms-content-zooming: none;\n\n  word-wrap: break-word;\n\n  overscroll-behavior-y: none;\n  -webkit-text-size-adjust: none;\n\n  text-size-adjust: none;\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n// Typography\n// --------------------------------------------------\n\n/// @prop - Font weight of all headings\n$headings-font-weight:         500;\n\n/// @prop - Line height of all headings\n$headings-line-height:         1.2;\n\n/// @prop - Font size of heading level 1\n$h1-font-size:                 dynamic-font(26px);\n\n/// @prop - Font size of heading level 2\n$h2-font-size:                 dynamic-font(24px);\n\n/// @prop - Font size of heading level 3\n$h3-font-size:                 dynamic-font(22px);\n\n/// @prop - Font size of heading level 4\n$h4-font-size:                 dynamic-font(20px);\n\n/// @prop - Font size of heading level 5\n$h5-font-size:                 dynamic-font(18px);\n\n/// @prop - Font size of heading level 6\n$h6-font-size:                 dynamic-font(16px);\n\nhtml {\n  font-family: var(--ion-font-family);\n}\n\n/**\n * Dynamic Type is an iOS-only feature, so\n * this should only be enabled on iOS devices.\n */\n@supports (-webkit-touch-callout: none) {\n  html {\n    /**\n     * Includes fallback if Dynamic Type is not enabled.\n     */\n    font: var(--ion-dynamic-font, 16px var(--ion-font-family));\n  }\n}\n\na {\n  background-color: transparent;\n  color: ion-color(primary, base);\n}\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  @include margin(16px, null, 10px, null);\n\n  font-weight: $headings-font-weight;\n\n  line-height: $headings-line-height;\n}\n\nh1 {\n  @include margin(20px, null, null, null);\n\n  font-size: $h1-font-size;\n}\n\nh2 {\n  @include margin(18px, null, null, null);\n\n  font-size: $h2-font-size;\n}\n\nh3 {\n  font-size: $h3-font-size;\n}\n\nh4 {\n  font-size: $h4-font-size;\n}\n\nh5 {\n  font-size: $h5-font-size;\n}\n\nh6 {\n  font-size: $h6-font-size;\n}\n\nsmall {\n  font-size: 75%;\n}\n\nsub,\nsup {\n  position: relative;\n\n  font-size: 75%;\n\n  line-height: 0;\n\n  vertical-align: baseline;\n}\n\nsup {\n  top: -.5em;\n}\n\nsub {\n  bottom: -.25em;\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n// Display\n// --------------------------------------------------\n// Modifies display of a particular element based on the given classes\n\n.ion-hide {\n  display: none !important;\n}\n\n// Adds hidden classes\n@each $breakpoint in map-keys($screen-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $screen-breakpoints);\n\n  @include media-breakpoint-up($breakpoint, $screen-breakpoints) {\n    // Provide `ion-hide-{bp}-up` classes for hiding the element based\n    // on the breakpoint\n    .ion-hide#{$infix}-up {\n      display: none !important;\n    }\n  }\n\n  @include media-breakpoint-down($breakpoint, $screen-breakpoints) {\n    // Provide `ion-hide-{bp}-down` classes for hiding the element based\n    // on the breakpoint\n    .ion-hide#{$infix}-down {\n      display: none !important;\n    }\n  }\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n\n// Element Space\n// --------------------------------------------------\n// Creates padding and margin attributes to be used on\n// any element\n\n$padding: var(--ion-padding, 16px);\n$margin: var(--ion-margin, 16px);\n\n// Padding\n// --------------------------------------------------\n\n.ion-no-padding {\n  --padding-start: 0;\n  --padding-end: 0;\n  --padding-top: 0;\n  --padding-bottom: 0;\n\n  @include padding(0);\n}\n\n.ion-padding {\n  --padding-start: #{$padding};\n  --padding-end: #{$padding};\n  --padding-top: #{$padding};\n  --padding-bottom: #{$padding};\n\n  @include padding($padding);\n}\n\n.ion-padding-top {\n  --padding-top: #{$padding};\n\n  @include padding($padding, null, null, null);\n}\n\n.ion-padding-start {\n  --padding-start: #{$padding};\n\n  @include padding-horizontal($padding, null);\n}\n\n.ion-padding-end {\n  --padding-end: #{$padding};\n\n  @include padding-horizontal(null, $padding);\n}\n\n.ion-padding-bottom {\n  --padding-bottom: #{$padding};\n\n  @include padding(null, null, $padding, null);\n}\n\n.ion-padding-vertical {\n  --padding-top: #{$padding};\n  --padding-bottom: #{$padding};\n\n  @include padding($padding, null, $padding, null);\n}\n\n.ion-padding-horizontal {\n  --padding-start: #{$padding};\n  --padding-end: #{$padding};\n\n  @include padding-horizontal($padding);\n}\n\n\n// Margin\n// --------------------------------------------------\n\n.ion-no-margin {\n  --margin-start: 0;\n  --margin-end: 0;\n  --margin-top: 0;\n  --margin-bottom: 0;\n\n  @include margin(0);\n}\n\n.ion-margin {\n  --margin-start: #{$margin};\n  --margin-end: #{$margin};\n  --margin-top: #{$margin};\n  --margin-bottom: #{$margin};\n\n  @include margin($margin);\n}\n\n.ion-margin-top {\n  --margin-top: #{$margin};\n\n  @include margin($margin, null, null, null);\n}\n\n.ion-margin-start {\n  --margin-start: #{$margin};\n\n  @include margin-horizontal($margin, null);\n}\n\n.ion-margin-end {\n  --margin-end: #{$margin};\n\n  @include margin-horizontal(null, $margin);\n}\n\n.ion-margin-bottom {\n  --margin-bottom: #{$margin};\n\n  @include margin(null, null, $margin, null);\n}\n\n.ion-margin-vertical {\n  --margin-top: #{$margin};\n  --margin-bottom: #{$margin};\n\n  @include margin($margin, null, $margin, null);\n}\n\n.ion-margin-horizontal {\n  --margin-start: #{$margin};\n  --margin-end: #{$margin};\n\n  @include margin-horizontal($margin);\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n// Float Elements\n// --------------------------------------------------\n// Creates float classes based on screen size\n\n@each $breakpoint in map-keys($screen-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $screen-breakpoints);\n\n  @include media-breakpoint-up($breakpoint, $screen-breakpoints) {\n    // Provide `.ion-float-{bp}-{side}` classes for floating the element based\n    // on the breakpoint and side\n    .ion-float#{$infix}-left {\n      @include float(left, !important);\n    }\n\n    .ion-float#{$infix}-right {\n      @include float(right, !important);\n    }\n\n    .ion-float#{$infix}-start {\n      @include float(start, !important);\n    }\n\n    .ion-float#{$infix}-end {\n      @include float(end, !important);\n    }\n  }\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n// Text Alignment\n// --------------------------------------------------\n// Creates text alignment attributes based on screen size\n\n@each $breakpoint in map-keys($screen-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $screen-breakpoints);\n\n  @include media-breakpoint-up($breakpoint, $screen-breakpoints) {\n    // Provide `.ion-text-{bp}` classes for aligning the text based\n    // on the breakpoint\n    .ion-text#{$infix}-center {\n      text-align: center !important;\n    }\n\n    .ion-text#{$infix}-justify {\n      text-align: justify !important;\n    }\n\n    .ion-text#{$infix}-start {\n      text-align: start !important;\n    }\n\n    .ion-text#{$infix}-end {\n      text-align: end !important;\n    }\n\n    .ion-text#{$infix}-left {\n      text-align: left !important;\n    }\n\n    .ion-text#{$infix}-right {\n      text-align: right !important;\n    }\n\n    .ion-text#{$infix}-nowrap {\n      white-space: nowrap !important;\n    }\n\n    .ion-text#{$infix}-wrap {\n      white-space: normal !important;\n    }\n  }\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n// Text Transformation\n// --------------------------------------------------\n// Creates text transform attributes based on screen size\n\n@each $breakpoint in map-keys($screen-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $screen-breakpoints);\n\n  @include media-breakpoint-up($breakpoint, $screen-breakpoints) {\n    // Provide `.ion-text-{bp}` classes for transforming the text based\n    // on the breakpoint\n    .ion-text#{$infix}-uppercase {\n      /* stylelint-disable-next-line declaration-no-important */\n      text-transform: uppercase !important;\n    }\n\n    .ion-text#{$infix}-lowercase {\n      /* stylelint-disable-next-line declaration-no-important */\n      text-transform: lowercase !important;\n    }\n\n    .ion-text#{$infix}-capitalize {\n      /* stylelint-disable-next-line declaration-no-important */\n      text-transform: capitalize !important;\n    }\n  }\n}\n", "// Flex Utilities\n// --------------------------------------------------\n// Creates flex classes to align flex containers\n// and items\n\n// Align Self\n// --------------------------------------------------\n\n.ion-align-self-start {\n  align-self: flex-start !important;\n}\n\n.ion-align-self-end {\n  align-self: flex-end !important;\n}\n\n.ion-align-self-center {\n  align-self: center !important;\n}\n\n.ion-align-self-stretch {\n  align-self: stretch !important;\n}\n\n.ion-align-self-baseline {\n  align-self: baseline !important;\n}\n\n.ion-align-self-auto {\n  align-self: auto !important;\n}\n\n\n// Flex Wrap\n// --------------------------------------------------\n\n.ion-wrap {\n  flex-wrap: wrap !important;\n}\n\n.ion-nowrap {\n  flex-wrap: nowrap !important;\n}\n\n.ion-wrap-reverse {\n  flex-wrap: wrap-reverse !important;\n}\n\n\n// Justify Content\n// --------------------------------------------------\n\n.ion-justify-content-start {\n  justify-content: flex-start !important;\n}\n\n.ion-justify-content-center {\n  justify-content: center !important;\n}\n\n.ion-justify-content-end {\n  justify-content: flex-end !important;\n}\n\n.ion-justify-content-around {\n  justify-content: space-around !important;\n}\n\n.ion-justify-content-between {\n  justify-content: space-between !important;\n}\n\n.ion-justify-content-evenly {\n  justify-content: space-evenly !important;\n}\n\n\n// Align Items\n// --------------------------------------------------\n\n.ion-align-items-start {\n  align-items: flex-start !important;\n}\n\n.ion-align-items-center {\n  align-items: center !important;\n}\n\n.ion-align-items-end {\n  align-items: flex-end !important;\n}\n\n.ion-align-items-stretch {\n  align-items: stretch !important;\n}\n\n.ion-align-items-baseline {\n  align-items: baseline !important;\n}\n", "/* Global Styles */\n@import '~@ionic/angular/css/core.css';\n@import '~@ionic/angular/css/normalize.css';\n@import '~@ionic/angular/css/structure.css';\n@import '~@ionic/angular/css/typography.css';\n@import '~@ionic/angular/css/display.css';\n@import '~@ionic/angular/css/padding.css';\n@import '~@ionic/angular/css/float-elements.css';\n@import '~@ionic/angular/css/text-alignment.css';\n@import '~@ionic/angular/css/text-transformation.css';\n@import '~@ionic/angular/css/flex-utils.css';\n\n/* Driver Evaluation Styles */\n.evaluation-container {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 20px;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n}\n\n.evaluation-header {\n  text-align: center;\n  margin-bottom: 30px;\n  padding: 20px;\n  color: white;\n  border-radius: 10px;\n\n  h1 {\n    margin: 0 0 10px 0;\n    font-size: 2.5rem;\n    font-weight: 300;\n  }\n\n  p {\n    margin: 5px 0;\n    opacity: 0.9;\n  }\n}\n\n/* Progress Bar */\n.progress-container {\n  width: 100%;\n  height: 6px;\n  background-color: #e0e0e0;\n  border-radius: 3px;\n  margin-bottom: 30px;\n  overflow: hidden;\n}\n\n.progress-bar {\n  height: 100%;\n  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\n  border-radius: 3px;\n  transition: width 0.3s ease;\n}\n\n/* Evaluation Steps */\n.evaluation-step {\n  animation: fadeIn 0.3s ease-in;\n}\n\n@keyframes fadeIn {\n  from { opacity: 0; transform: translateY(20px); }\n  to { opacity: 1; transform: translateY(0); }\n}\n\n/* Rating System */\n.category-rating {\n  margin-bottom: 30px;\n  text-align: center;\n\n  h3 {\n    margin-bottom: 15px;\n    color: #333;\n    font-weight: 600;\n  }\n}\n\n.rating-container {\n  display: flex;\n  justify-content: center;\n  gap: 5px;\n  margin: 15px 0;\n}\n\n.rating-star {\n  color: #ddd;\n  font-size: 2rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    color: #ffc107;\n    transform: scale(1.1);\n  }\n\n  &.selected {\n    color: #ffc107;\n  }\n}\n\n/* Questions */\n.detailed-questions {\n  margin-top: 30px;\n  padding-top: 20px;\n  border-top: 1px solid #e0e0e0;\n\n  h3 {\n    color: #333;\n    font-weight: 600;\n    margin-bottom: 20px;\n  }\n}\n\n.question-item {\n  margin-bottom: 25px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 8px;\n\n  h4 {\n    margin: 0 0 15px 0;\n    color: #333;\n    font-weight: 500;\n    font-size: 1.1rem;\n  }\n}\n\n/* Step Actions */\n.step-actions {\n  display: flex;\n  justify-content: space-between;\n  gap: 15px;\n  margin-top: 30px;\n  padding: 20px 0;\n\n  ion-button {\n    flex: 1;\n    height: 50px;\n    font-weight: 600;\n  }\n}\n\n/* Loading States */\n.loading-container {\n  text-align: center;\n  padding: 60px 20px;\n\n  ion-spinner {\n    width: 50px;\n    height: 50px;\n    margin-bottom: 20px;\n  }\n\n  p {\n    color: #666;\n    font-size: 1.1rem;\n  }\n}\n\n/* Success/Error States */\n.alert-success {\n  background-color: #d4edda;\n  color: #155724;\n  border: 1px solid #c3e6cb;\n  padding: 15px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n}\n\n.alert-danger {\n  background-color: #f8d7da;\n  color: #721c24;\n  border: 1px solid #f5c6cb;\n  padding: 15px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .evaluation-container {\n    padding: 10px;\n  }\n  \n  .evaluation-header {\n    padding: 15px;\n    margin-bottom: 20px;\n\n    h1 {\n      font-size: 2rem;\n    }\n  }\n  \n  .step-actions {\n    flex-direction: column;\n\n    ion-button {\n      margin-bottom: 10px;\n    }\n  }\n  \n  .rating-container {\n    flex-wrap: wrap;\n    gap: 10px;\n  }\n  \n  .rating-star {\n    font-size: 1.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .evaluation-header h1 {\n    font-size: 1.5rem;\n  }\n  \n  .question-item {\n    padding: 10px;\n  }\n}\n"], "names": [], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]}