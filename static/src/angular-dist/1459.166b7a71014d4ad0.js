"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[1459],{1459:(A,b,c)=>{c.r(b),c.d(b,{ion_picker_column:()=>a});var f=c(467),o=c(2734),g=c(9596),m=c(1837),v=c(906),x=c(4576);c(6780);const a=class{constructor(s){(0,o.r)(this,s),this.ionChange=(0,o.d)(this,"ionChange",7),this.isScrolling=!1,this.isColumnVisible=!1,this.canExitInputMode=!0,this.updateValueTextOnScroll=!1,this.ariaLabel=null,this.isActive=!1,this.disabled=!1,this.color="primary",this.numericInput=!1,this.centerPickerItemInView=(e,t=!0,i=!0)=>{const{isColumnVisible:n,scrollEl:d}=this;if(n&&d){const h=e.offsetTop-3*e.clientHeight+e.clientHeight/2;d.scrollTop!==h&&(this.canExitInputMode=i,this.updateValueTextOnScroll=!1,d.scroll({top:h,left:0,behavior:t?"smooth":void 0}))}},this.setPickerItemActiveState=(e,t)=>{t?e.classList.add(r):e.classList.remove(r)},this.inputModeChange=e=>{if(!this.numericInput)return;const{useInputMode:t,inputModeColumn:i}=e.detail;this.setInputModeActive(!(!t||void 0!==i&&i!==this.el))},this.setInputModeActive=e=>{this.isScrolling?this.scrollEndCallback=()=>{this.isActive=e}:this.isActive=e},this.initializeScrollListener=()=>{const e=(0,o.a)("ios"),{el:t,scrollEl:i}=this;let n,d=this.activeItem;const h=()=>{(0,m.r)(()=>{var k;if(!i)return;n&&(clearTimeout(n),n=void 0),this.isScrolling||(e&&(0,v.b)(),this.isScrolling=!0);const _=i.getBoundingClientRect(),E=_.x+_.width/2,C=_.y+_.height/2,O=t.getRootNode(),y=O instanceof ShadowRoot?O:g.d;if(void 0===y)return;let u=y.elementsFromPoint(E,C).find(p=>"ION-PICKER-COLUMN-OPTION"===p.tagName);if(void 0===u){const p=y.elementFromPoint(E,C);"ION-PICKER-COLUMN-OPTION"===p?.tagName&&(u=p)}void 0!==d&&this.setPickerItemActiveState(d,!1),void 0!==u&&!u.disabled&&(u!==d&&(e&&(0,v.a)(),this.canExitInputMode&&this.exitInputMode()),d=u,this.setPickerItemActiveState(u,!0),this.updateValueTextOnScroll&&(null===(k=this.assistiveFocusable)||void 0===k||k.setAttribute("aria-valuetext",this.getOptionValueText(u))),n=setTimeout(()=>{this.isScrolling=!1,this.updateValueTextOnScroll=!0,e&&(0,v.h)();const{scrollEndCallback:p}=this;p&&(p(),this.scrollEndCallback=void 0),this.canExitInputMode=!0,this.setValue(u.value)},250))})};(0,m.r)(()=>{i&&(i.addEventListener("scroll",h),this.destroyScrollListener=()=>{i.removeEventListener("scroll",h)})})},this.exitInputMode=()=>{const{parentEl:e}=this;null!=e&&(e.exitInputMode(),this.el.classList.remove("picker-column-active"))},this.findNextOption=(e=1)=>{const{activeItem:t}=this;if(!t)return null;let i=t,n=t.nextElementSibling;for(;null!=n;){if(e>0&&e--,"ION-PICKER-COLUMN-OPTION"===n.tagName&&!n.disabled&&0===e)return n;i=n,n=n.nextElementSibling}return i},this.findPreviousOption=(e=1)=>{const{activeItem:t}=this;if(!t)return null;let i=t,n=t.previousElementSibling;for(;null!=n;){if(e>0&&e--,"ION-PICKER-COLUMN-OPTION"===n.tagName&&!n.disabled&&0===e)return n;i=n,n=n.previousElementSibling}return i},this.onKeyDown=e=>{const t=(0,o.a)("mobile");let i=null;switch(e.key){case"ArrowDown":i=t?this.findPreviousOption():this.findNextOption();break;case"ArrowUp":i=t?this.findNextOption():this.findPreviousOption();break;case"PageUp":i=t?this.findNextOption(5):this.findPreviousOption(5);break;case"PageDown":i=t?this.findPreviousOption(5):this.findNextOption(5);break;case"Home":i=this.el.querySelector("ion-picker-column-option:first-of-type");break;case"End":i=this.el.querySelector("ion-picker-column-option:last-of-type")}null!==i&&(this.setValue(i.value),e.preventDefault())},this.getOptionValueText=e=>{var t;return e?null!==(t=e.getAttribute("aria-label"))&&void 0!==t?t:e.innerText:""},this.renderAssistiveFocusable=()=>{const{activeItem:e}=this,t=this.getOptionValueText(e);return(0,o.h)("div",{ref:i=>this.assistiveFocusable=i,class:"assistive-focusable",role:"slider",tabindex:this.disabled?void 0:0,"aria-label":this.ariaLabel,"aria-valuemin":0,"aria-valuemax":0,"aria-valuenow":0,"aria-valuetext":t,"aria-orientation":"vertical",onKeyDown:i=>this.onKeyDown(i)})}}ariaLabelChanged(s){this.ariaLabel=s}valueChange(){this.isColumnVisible&&this.scrollActiveItemIntoView(!0)}componentWillLoad(){const s=this.parentEl=this.el.closest("ion-picker");new IntersectionObserver(t=>{if(t[t.length-1].isIntersecting){const{activeItem:n,el:d}=this;this.isColumnVisible=!0;const h=(0,m.g)(d).querySelector(`.${r}`);h&&this.setPickerItemActiveState(h,!1),this.scrollActiveItemIntoView(),n&&this.setPickerItemActiveState(n,!0),this.initializeScrollListener()}else this.isColumnVisible=!1,this.destroyScrollListener&&(this.destroyScrollListener(),this.destroyScrollListener=void 0)},{threshold:.001,root:this.parentEl}).observe(this.el),null!==s&&s.addEventListener("ionInputModeChange",t=>this.inputModeChange(t))}componentDidRender(){const{el:s,activeItem:e,isColumnVisible:t,value:i}=this;if(t&&!e){const n=s.querySelector("ion-picker-column-option");null!==n&&n.value!==i&&this.setValue(n.value)}}scrollActiveItemIntoView(){var s=this;return(0,f.A)(function*(e=!1){const t=s.activeItem;t&&s.centerPickerItemInView(t,e,!1)}).apply(this,arguments)}setValue(s){var e=this;return(0,f.A)(function*(){!0===e.disabled||e.value===s||(e.value=s,e.ionChange.emit({value:s}))})()}setFocus(){var s=this;return(0,f.A)(function*(){s.assistiveFocusable&&s.assistiveFocusable.focus()})()}connectedCallback(){var s;this.ariaLabel=null!==(s=this.el.getAttribute("aria-label"))&&void 0!==s?s:"Select a value"}get activeItem(){const{value:s}=this;return Array.from(this.el.querySelectorAll("ion-picker-column-option")).find(t=>!(!this.disabled&&t.disabled)&&t.value===s)}render(){const{color:s,disabled:e,isActive:t,numericInput:i}=this,n=(0,o.e)(this);return(0,o.h)(o.j,{key:"ea0280355b2f87895bf7dddd289ccf473aa759f3",class:(0,x.c)(s,{[n]:!0,"picker-column-active":t,"picker-column-numeric-input":i,"picker-column-disabled":e})},this.renderAssistiveFocusable(),(0,o.h)("slot",{key:"482992131cdeb85b1f61430d7fe1322a16345769",name:"prefix"}),(0,o.h)("div",{key:"43f7f80d621d411ef366b3ca1396299e8c9a0c97","aria-hidden":"true",class:"picker-opts",ref:d=>{this.scrollEl=d},tabIndex:-1},(0,o.h)("div",{key:"13a9ee686132af32240710730765de4c0003a9e8",class:"picker-item-empty","aria-hidden":"true"},"\xa0"),(0,o.h)("div",{key:"dbccba4920833cfcebe9b0fc763458ec3053705a",class:"picker-item-empty","aria-hidden":"true"},"\xa0"),(0,o.h)("div",{key:"682b43f83a5ea2e46067457f3af118535e111edb",class:"picker-item-empty","aria-hidden":"true"},"\xa0"),(0,o.h)("slot",{key:"d27e1e1dc0504b2f4627a29912a05bb91e8e413a"}),(0,o.h)("div",{key:"61c948dbb9cf7469aed3018542bc0954211585ba",class:"picker-item-empty","aria-hidden":"true"},"\xa0"),(0,o.h)("div",{key:"cf46c277fbee65e35ff44ce0d53ce12aa9cbf9db",class:"picker-item-empty","aria-hidden":"true"},"\xa0"),(0,o.h)("div",{key:"bbc0e2d491d3f836ab849493ade2f7fa6ad9244e",class:"picker-item-empty","aria-hidden":"true"},"\xa0")),(0,o.h)("slot",{key:"d25cbbe14b2914fe7b878d43b4e3f4a8c8177d24",name:"suffix"}))}get el(){return(0,o.k)(this)}static get watchers(){return{"aria-label":["ariaLabelChanged"],value:["valueChange"]}}},r="option-active";a.style=":host{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;max-width:100%;height:200px;font-size:22px;text-align:center}.assistive-focusable{left:0;right:0;top:0;bottom:0;position:absolute;z-index:1;pointer-events:none}.assistive-focusable:focus{outline:none}.picker-opts{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0px;padding-bottom:0px;min-width:26px;max-height:200px;outline:none;text-align:inherit;-webkit-scroll-snap-type:y mandatory;-ms-scroll-snap-type:y mandatory;scroll-snap-type:y mandatory;overflow-x:hidden;overflow-y:scroll;scrollbar-width:none}.picker-item-empty{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.picker-opts::-webkit-scrollbar{display:none}::slotted(ion-picker-column-option){display:block;scroll-snap-align:center}.picker-item-empty,:host(:not([disabled])) ::slotted(ion-picker-column-option.option-disabled){scroll-snap-align:none}::slotted([slot=prefix]),::slotted([slot=suffix]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}::slotted([slot=prefix]){-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0;padding-bottom:0;-ms-flex-pack:end;justify-content:end}::slotted([slot=suffix]){-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0;padding-bottom:0;-ms-flex-pack:start;justify-content:start}:host(.picker-column-disabled) .picker-opts{overflow-y:hidden}:host(.picker-column-disabled) ::slotted(ion-picker-column-option){cursor:default;opacity:0.4;pointer-events:none}@media (any-hover: hover){:host(:focus) .picker-opts{outline:none;background:rgba(var(--ion-color-base-rgb), 0.2)}}"},4576:(A,b,c)=>{c.d(b,{c:()=>g,g:()=>v,h:()=>o,o:()=>I});var f=c(467);const o=(l,a)=>null!==a.closest(l),g=(l,a)=>"string"==typeof l&&l.length>0?Object.assign({"ion-color":!0,[`ion-color-${l}`]:!0},a):a,v=l=>{const a={};return(l=>void 0!==l?(Array.isArray(l)?l:l.split(" ")).filter(r=>null!=r).map(r=>r.trim()).filter(r=>""!==r):[])(l).forEach(r=>a[r]=!0),a},x=/^[a-z][a-z0-9+\-.]*:/,I=function(){var l=(0,f.A)(function*(a,r,s,e){if(null!=a&&"#"!==a[0]&&!x.test(a)){const t=document.querySelector("ion-router");if(t)return r?.preventDefault(),t.push(a,s,e)}return!1});return function(r,s,e,t){return l.apply(this,arguments)}}()}}]);