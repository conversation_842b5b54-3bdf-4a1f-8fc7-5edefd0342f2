{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-input_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACgJ;AACpE;AAC0D;AACnC;AACnB;AACR;AAC3C;AAE7B,MAAM4B,WAAW,GAAG,k3XAAk3X;AAEt4X,MAAMC,UAAU,GAAG,2hoBAA2hoB;AAE9ioB,MAAMC,KAAK,GAAG,MAAM;EAChBC,WAAWA,CAACC,OAAO,EAAE;IACjB/B,qDAAgB,CAAC,IAAI,EAAE+B,OAAO,CAAC;IAC/B,IAAI,CAACC,QAAQ,GAAG9B,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC+B,SAAS,GAAG/B,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACgC,OAAO,GAAGhC,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACiC,QAAQ,GAAGjC,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACkC,OAAO,GAAG,aAAaC,QAAQ,EAAE,EAAE;IACxC,IAAI,CAACC,YAAY,GAAG,GAAG,IAAI,CAACF,OAAO,cAAc;IACjD,IAAI,CAACG,WAAW,GAAG,GAAG,IAAI,CAACH,OAAO,aAAa;IAC/C,IAAI,CAACI,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B;AACR;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB;AACR;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;AACR;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;AACR;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,OAAO;IAC7B;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,IAAI,CAAChB,OAAO;IACxB;AACR;AACA;IACQ,IAAI,CAACiB,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,MAAM;IAClB;AACR;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,OAAO,GAAIC,EAAE,IAAK;MACnB,MAAMC,KAAK,GAAGD,EAAE,CAACE,MAAM;MACvB,IAAID,KAAK,EAAE;QACP,IAAI,CAACH,KAAK,GAAGG,KAAK,CAACH,KAAK,IAAI,EAAE;MAClC;MACA,IAAI,CAACK,eAAe,CAACH,EAAE,CAAC;IAC5B,CAAC;IACD,IAAI,CAACI,QAAQ,GAAIJ,EAAE,IAAK;MACpB,IAAI,CAACK,eAAe,CAACL,EAAE,CAAC;IAC5B,CAAC;IACD,IAAI,CAACM,MAAM,GAAIN,EAAE,IAAK;MAClB,IAAI,CAAChB,QAAQ,GAAG,KAAK;MACrB,IAAI,IAAI,CAACuB,YAAY,KAAK,IAAI,CAACT,KAAK,EAAE;QAClC;AAChB;AACA;AACA;QACgB,IAAI,CAACO,eAAe,CAACL,EAAE,CAAC;MAC5B;MACA,IAAI,CAACjB,mBAAmB,GAAG,KAAK;MAChC,IAAI,CAACR,OAAO,CAACiC,IAAI,CAACR,EAAE,CAAC;IACzB,CAAC;IACD,IAAI,CAACS,OAAO,GAAIT,EAAE,IAAK;MACnB,IAAI,CAAChB,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACuB,YAAY,GAAG,IAAI,CAACT,KAAK;MAC9B,IAAI,CAACtB,QAAQ,CAACgC,IAAI,CAACR,EAAE,CAAC;IAC1B,CAAC;IACD,IAAI,CAACU,SAAS,GAAIV,EAAE,IAAK;MACrB,IAAI,CAACW,gBAAgB,CAACX,EAAE,CAAC;IAC7B,CAAC;IACD,IAAI,CAACY,kBAAkB,GAAG,MAAM;MAC5B,IAAI,CAAC9B,WAAW,GAAG,IAAI;IAC3B,CAAC;IACD,IAAI,CAAC+B,gBAAgB,GAAG,MAAM;MAC1B,IAAI,CAAC/B,WAAW,GAAG,KAAK;IAC5B,CAAC;IACD,IAAI,CAACgC,cAAc,GAAId,EAAE,IAAK;MAC1B,IAAI,IAAI,CAACX,UAAU,IAAI,CAAC,IAAI,CAACK,QAAQ,IAAI,CAAC,IAAI,CAACH,QAAQ,IAAIS,EAAE,EAAE;QAC3DA,EAAE,CAACe,cAAc,CAAC,CAAC;QACnBf,EAAE,CAACgB,eAAe,CAAC,CAAC;QACpB;QACA,IAAI,CAACC,QAAQ,CAAC,CAAC;MACnB;MACA,IAAI,CAACnB,KAAK,GAAG,EAAE;MACf,IAAI,CAACK,eAAe,CAACH,EAAE,CAAC;IAC5B,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACkB,YAAY,GAAIlB,EAAE,IAAK;MACxB;MACA;MACA,IAAIA,EAAE,CAACE,MAAM,KAAKF,EAAE,CAACmB,aAAa,EAAE;QAChCnB,EAAE,CAACgB,eAAe,CAAC,CAAC;MACxB;IACJ,CAAC;EACL;EACAI,eAAeA,CAAA,EAAG;IACd,MAAM;MAAE/C,QAAQ;MAAEgD,QAAQ;MAAEC;IAAiB,CAAC,GAAG,IAAI;IACrD;AACR;AACA;AACA;IACQ,IAAI,CAACjD,QAAQ,GAAGgD,QAAQ,KAAKE,SAAS,GAAGD,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGjD,QAAQ,GAAGlB,uDAAa,CAACkB,QAAQ,EAAEgD,QAAQ,CAAC;EACvK;EACA;AACJ;AACA;AACA;AACA;EACIG,YAAYA,CAAA,EAAG;IACX,MAAMC,cAAc,GAAG,IAAI,CAACC,EAAE,CAACC,aAAa,CAAC,2BAA2B,CAAC;IACzE,IAAIF,cAAc,EAAE;MAChBA,cAAc,CAAC5B,IAAI,GAAG,IAAI,CAACA,IAAI;IACnC;EACJ;EACA;AACJ;AACA;EACI+B,YAAYA,CAAA,EAAG;IACX,MAAMC,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,MAAM/B,KAAK,GAAG,IAAI,CAACgC,QAAQ,CAAC,CAAC;IAC7B,IAAID,WAAW,IAAIA,WAAW,CAAC/B,KAAK,KAAKA,KAAK,IAAI,CAAC,IAAI,CAAChB,WAAW,EAAE;MACjE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY+C,WAAW,CAAC/B,KAAK,GAAGA,KAAK;IAC7B;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIiC,YAAYA,CAACC,QAAQ,EAAE;IACnB,IAAI,CAACnD,mBAAmB,GAAGoD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACrD,mBAAmB,CAAC,EAAE;MAAEsD,GAAG,EAAEH;IAAS,CAAC,CAAC;IACxGvF,qDAAW,CAAC,IAAI,CAAC;EACrB;EACA;AACJ;AACA;AACA;EACI2F,cAAcA,CAACpC,EAAE,EAAE;IACf,MAAM6B,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,IAAIA,WAAW,IAAI7B,EAAE,CAACE,MAAM,KAAK2B,WAAW,EAAE;MAC1C7B,EAAE,CAACgB,eAAe,CAAC,CAAC;MACpB,IAAI,CAACU,EAAE,CAACW,KAAK,CAAC,CAAC;IACnB;EACJ;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACzD,mBAAmB,GAAGoD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE7E,uDAAqB,CAAC,IAAI,CAACqE,EAAE,CAAC,CAAC,EAAEnE,uDAAiB,CAAC,IAAI,CAACmE,EAAE,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC,CAAC;EAC3K;EACAa,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAEb;IAAG,CAAC,GAAG,IAAI;IACnB,IAAI,CAACc,sBAAsB,GAAG/E,2DAA4B,CAACiE,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,MAAMjF,qDAAW,CAAC,IAAI,CAAC,CAAC;IAClH,IAAI,CAACgG,eAAe,GAAGvF,gEAAqB,CAACwE,EAAE,EAAE,MAAM,IAAI,CAACgB,aAAa,EAAE,MAAM,IAAI,CAACC,SAAS,CAAC;IAChG,IAAI,CAACvB,eAAe,CAAC,CAAC;IACtB;MACIwB,QAAQ,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,iBAAiB,EAAE;QACtDC,MAAM,EAAE,IAAI,CAACrB;MACjB,CAAC,CAAC,CAAC;IACP;EACJ;EACAsB,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC1B,gBAAgB,GAAG,IAAI,CAACjD,QAAQ;IACrC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACmD,YAAY,CAAC,CAAC;IACnB,IAAI,CAACJ,eAAe,CAAC,CAAC;EAC1B;EACA6B,kBAAkBA,CAAA,EAAG;IACjB,IAAIC,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAACT,eAAe,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,mBAAmB,CAAC,CAAC;EAC7F;EACAC,oBAAoBA,CAAA,EAAG;IACnB;MACIR,QAAQ,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,mBAAmB,EAAE;QACxDC,MAAM,EAAE,IAAI,CAACrB;MACjB,CAAC,CAAC,CAAC;IACP;IACA,IAAI,IAAI,CAACc,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAACa,OAAO,CAAC,CAAC;MACrC,IAAI,CAACb,sBAAsB,GAAGjB,SAAS;IAC3C;IACA,IAAI,IAAI,CAACkB,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACY,OAAO,CAAC,CAAC;MAC9B,IAAI,CAACZ,eAAe,GAAGlB,SAAS;IACpC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUN,QAAQA,CAAA,EAAG;IAAA,IAAAqC,KAAA;IAAA,OAAAC,oMAAA;MACb,IAAID,KAAI,CAACzB,WAAW,EAAE;QAClByB,KAAI,CAACzB,WAAW,CAAC2B,KAAK,CAAC,CAAC;MAC5B;IAAC;EACL;EACA;AACJ;AACA;EACUC,eAAeA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAH,oMAAA;MACpB;AACR;AACA;AACA;MACQ,IAAI,CAACG,MAAI,CAAC7B,WAAW,EAAE;QACnB,MAAM,IAAI8B,OAAO,CAAEC,OAAO,IAAKpG,uDAAgB,CAACkG,MAAI,CAAChC,EAAE,EAAEkC,OAAO,CAAC,CAAC;MACtE;MACA,OAAOD,OAAO,CAACC,OAAO,CAACF,MAAI,CAAC7B,WAAW,CAAC;IAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIxB,eAAeA,CAACwD,KAAK,EAAE;IACnB,MAAM;MAAE/D;IAAM,CAAC,GAAG,IAAI;IACtB;IACA,MAAMkC,QAAQ,GAAGlC,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGA,KAAK,CAACgE,QAAQ,CAAC,CAAC;IACzD;IACA,IAAI,CAACvD,YAAY,GAAGyB,QAAQ;IAC5B,IAAI,CAAC1D,SAAS,CAACkC,IAAI,CAAC;MAAEV,KAAK,EAAEkC,QAAQ;MAAE6B;IAAM,CAAC,CAAC;EACnD;EACA;AACJ;AACA;EACI1D,eAAeA,CAAC0D,KAAK,EAAE;IACnB,MAAM;MAAE/D;IAAM,CAAC,GAAG,IAAI;IACtB;IACA,MAAMkC,QAAQ,GAAGlC,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGA,KAAK,CAACgE,QAAQ,CAAC,CAAC;IACzD,IAAI,CAACzF,QAAQ,CAACmC,IAAI,CAAC;MAAEV,KAAK,EAAEkC,QAAQ;MAAE6B;IAAM,CAAC,CAAC;EAClD;EACAE,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAElE,IAAI;MAAEmE;IAAY,CAAC,GAAG,IAAI;IAClC,OAAOA,WAAW,KAAKzC,SAAS,GAAG1B,IAAI,KAAK,UAAU,GAAGmE,WAAW;EACxE;EACAlC,QAAQA,CAAA,EAAG;IACP,OAAO,OAAO,IAAI,CAAChC,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACA,KAAK,CAACgE,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAChE,KAAK,IAAI,EAAE,EAAEgE,QAAQ,CAAC,CAAC;EACjG;EACAnD,gBAAgBA,CAACX,EAAE,EAAE;IACjB,IAAI,CAAC,IAAI,CAAC+D,iBAAiB,CAAC,CAAC,EAAE;MAC3B;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAME,YAAY,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC;IACxE,MAAMC,iBAAiB,GAAGD,YAAY,CAACE,QAAQ,CAACnE,EAAE,CAACoE,GAAG,CAAC;IACvD;AACR;AACA;AACA;IACQ,IAAI,CAAC,IAAI,CAACrF,mBAAmB,IAAI,IAAI,CAACsF,QAAQ,CAAC,CAAC,IAAI,CAACH,iBAAiB,EAAE;MACpE,IAAI,CAACpE,KAAK,GAAG,EAAE;MACf,IAAI,CAACK,eAAe,CAACH,EAAE,CAAC;IAC5B;IACA;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACkE,iBAAiB,EAAE;MACpB,IAAI,CAACnF,mBAAmB,GAAG,IAAI;IACnC;EACJ;EACAsF,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACvC,QAAQ,CAAC,CAAC,CAACwC,MAAM,GAAG,CAAC;EACrC;EACA;AACJ;AACA;EACIC,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEC,UAAU;MAAEC,SAAS;MAAE9F,YAAY;MAAEC;IAAY,CAAC,GAAG,IAAI;IACjE,OAAO,CACHlC,qDAAC,CAAC,KAAK,EAAE;MAAEgI,EAAE,EAAE/F,YAAY;MAAEgG,KAAK,EAAE;IAAc,CAAC,EAAEH,UAAU,CAAC,EAChE9H,qDAAC,CAAC,KAAK,EAAE;MAAEgI,EAAE,EAAE9F,WAAW;MAAE+F,KAAK,EAAE;IAAa,CAAC,EAAEF,SAAS,CAAC,CAChE;EACL;EACAG,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAElD,EAAE;MAAE8C,UAAU;MAAEC,SAAS;MAAE9F,YAAY;MAAEC;IAAY,CAAC,GAAG,IAAI;IACrE,IAAI8C,EAAE,CAACmD,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAIpD,EAAE,CAACmD,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAIL,SAAS,EAAE;MAC3F,OAAO7F,WAAW;IACtB;IACA,IAAI4F,UAAU,EAAE;MACZ,OAAO7F,YAAY;IACvB;IACA,OAAO4C,SAAS;EACpB;EACAwD,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAEzF,OAAO;MAAE0F,SAAS;MAAEC,gBAAgB;MAAEnF;IAAM,CAAC,GAAG,IAAI;IAC5D,IAAIR,OAAO,KAAK,IAAI,IAAI0F,SAAS,KAAKzD,SAAS,EAAE;MAC7C;IACJ;IACA,OAAO7E,qDAAC,CAAC,KAAK,EAAE;MAAEiI,KAAK,EAAE;IAAU,CAAC,EAAEhH,2DAAc,CAACmC,KAAK,EAAEkF,SAAS,EAAEC,gBAAgB,CAAC,CAAC;EAC7F;EACA;AACJ;AACA;AACA;AACA;EACIC,mBAAmBA,CAAA,EAAG;IAClB,MAAM;MAAE5F,OAAO;MAAEkF,UAAU;MAAEC,SAAS;MAAEO;IAAU,CAAC,GAAG,IAAI;IAC1D;AACR;AACA;AACA;IACQ,MAAMG,WAAW,GAAG,CAAC,CAACX,UAAU,IAAI,CAAC,CAACC,SAAS;IAC/C,MAAMW,UAAU,GAAG9F,OAAO,KAAK,IAAI,IAAI0F,SAAS,KAAKzD,SAAS;IAC9D,IAAI,CAAC4D,WAAW,IAAI,CAACC,UAAU,EAAE;MAC7B;IACJ;IACA,OAAQ1I,qDAAC,CAAC,KAAK,EAAE;MAAEiI,KAAK,EAAE;IAAe,CAAC,EAAE,IAAI,CAACJ,cAAc,CAAC,CAAC,EAAE,IAAI,CAACQ,aAAa,CAAC,CAAC,CAAC;EAC5F;EACAM,WAAWA,CAAA,EAAG;IACV,MAAM;MAAEC;IAAM,CAAC,GAAG,IAAI;IACtB,OAAQ5I,qDAAC,CAAC,KAAK,EAAE;MAAEiI,KAAK,EAAE;QAClB,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAAC,IAAI,CAACY;MACvC;IAAE,CAAC,EAAED,KAAK,KAAK/D,SAAS,GAAG7E,qDAAC,CAAC,MAAM,EAAE;MAAE+C,IAAI,EAAE;IAAQ,CAAC,CAAC,GAAG/C,qDAAC,CAAC,KAAK,EAAE;MAAEiI,KAAK,EAAE;IAAa,CAAC,EAAEW,KAAK,CAAC,CAAC;EAC3G;EACA;AACJ;AACA;AACA;EACI,IAAI3C,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACjB,EAAE,CAACC,aAAa,CAAC,gBAAgB,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAI4D,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACD,KAAK,KAAK/D,SAAS,IAAI,IAAI,CAACoB,SAAS,KAAK,IAAI;EAC9D;EACA;AACJ;AACA;AACA;EACI6C,oBAAoBA,CAAA,EAAG;IACnB,MAAMC,IAAI,GAAG7I,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAM8I,cAAc,GAAGD,IAAI,KAAK,IAAI,IAAI,IAAI,CAACE,IAAI,KAAK,SAAS;IAC/D,IAAID,cAAc,EAAE;MAChB;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,OAAO,CACHhJ,qDAAC,CAAC,KAAK,EAAE;QAAEiI,KAAK,EAAE;MAA0B,CAAC,EAAEjI,qDAAC,CAAC,KAAK,EAAE;QAAEiI,KAAK,EAAE;MAAsB,CAAC,CAAC,EAAEjI,qDAAC,CAAC,KAAK,EAAE;QAAEiI,KAAK,EAAE;UACrG,qBAAqB,EAAE,IAAI;UAC3B,4BAA4B,EAAE,CAAC,IAAI,CAACY;QACxC;MAAE,CAAC,EAAE7I,qDAAC,CAAC,KAAK,EAAE;QAAEiI,KAAK,EAAE,cAAc;QAAE,aAAa,EAAE,MAAM;QAAEiB,GAAG,EAAGlE,EAAE,IAAM,IAAI,CAACgB,aAAa,GAAGhB;MAAI,CAAC,EAAE,IAAI,CAAC4D,KAAK,CAAC,CAAC,EAAE5I,qDAAC,CAAC,KAAK,EAAE;QAAEiI,KAAK,EAAE;MAAoB,CAAC,CAAC,CAAC,EACnK,IAAI,CAACU,WAAW,CAAC,CAAC,CACrB;IACL;IACA;AACR;AACA;AACA;IACQ,OAAO,IAAI,CAACA,WAAW,CAAC,CAAC;EAC7B;EACAQ,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEtG,QAAQ;MAAEoG,IAAI;MAAEjG,QAAQ;MAAEoG,KAAK;MAAErH,OAAO;MAAEe,cAAc;MAAEkC,EAAE;MAAE1C,QAAQ;MAAE+G;IAAe,CAAC,GAAG,IAAI;IACvG,MAAMN,IAAI,GAAG7I,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAMkD,KAAK,GAAG,IAAI,CAACgC,QAAQ,CAAC,CAAC;IAC7B,MAAMkE,MAAM,GAAGpI,qDAAW,CAAC,UAAU,EAAE,IAAI,CAAC8D,EAAE,CAAC;IAC/C,MAAMuE,qBAAqB,GAAGR,IAAI,KAAK,IAAI,IAAIE,IAAI,KAAK,SAAS,IAAI,CAACK,MAAM;IAC5E,MAAME,gBAAgB,GAAGT,IAAI,KAAK,KAAK,GAAG3H,iDAAW,GAAGC,iDAAU;IAClE,MAAMoI,aAAa,GAAGJ,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGG,gBAAgB;IAC9G,MAAM7B,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;IAChC,MAAM+B,gBAAgB,GAAG1E,EAAE,CAACC,aAAa,CAAC,8BAA8B,CAAC,KAAK,IAAI;IAClF;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM0E,gBAAgB,GAAG7G,cAAc,KAAK,SAAS,IAAKA,cAAc,KAAK,UAAU,KAAK6E,QAAQ,IAAIrF,QAAQ,IAAIoH,gBAAgB,CAAE;IACtI,OAAQ1J,qDAAC,CAACI,iDAAI,EAAE;MAAEsH,GAAG,EAAE,0CAA0C;MAAEO,KAAK,EAAE9G,qDAAkB,CAAC,IAAI,CAACyI,KAAK,EAAE;QACjG,CAACb,IAAI,GAAG,IAAI;QACZ,WAAW,EAAEpB,QAAQ;QACrB,WAAW,EAAErF,QAAQ;QACrB,gBAAgB,EAAEqH,gBAAgB;QAClC,CAAC,cAAcV,IAAI,EAAE,GAAGA,IAAI,KAAKpE,SAAS;QAC1C,CAAC,eAAeuE,KAAK,EAAE,GAAGA,KAAK,KAAKvE,SAAS;QAC7C,CAAC,yBAAyB/B,cAAc,EAAE,GAAG,IAAI;QACjD,SAAS,EAAEwG,MAAM;QACjB,eAAe,EAAEpI,qDAAW,CAAC,oBAAoB,EAAE,IAAI,CAAC8D,EAAE,CAAC;QAC3D,gBAAgB,EAAEnC;MACtB,CAAC;IAAE,CAAC,EAAE7C,qDAAC,CAAC,OAAO,EAAE;MAAE0H,GAAG,EAAE,0CAA0C;MAAEO,KAAK,EAAE,eAAe;MAAE4B,OAAO,EAAE9H,OAAO;MAAE+H,OAAO,EAAE,IAAI,CAACtF;IAAa,CAAC,EAAE,IAAI,CAACsE,oBAAoB,CAAC,CAAC,EAAE9I,qDAAC,CAAC,KAAK,EAAE;MAAE0H,GAAG,EAAE,0CAA0C;MAAEO,KAAK,EAAE,gBAAgB;MAAE6B,OAAO,EAAE,IAAI,CAACtF;IAAa,CAAC,EAAExE,qDAAC,CAAC,MAAM,EAAE;MAAE0H,GAAG,EAAE,0CAA0C;MAAE3E,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAE/C,qDAAC,CAAC,OAAO,EAAEuF,MAAM,CAACC,MAAM,CAAC;MAAEkC,GAAG,EAAE,0CAA0C;MAAEO,KAAK,EAAE,cAAc;MAAEiB,GAAG,EAAG3F,KAAK,IAAM,IAAI,CAAC4B,WAAW,GAAG5B,KAAM;MAAEyE,EAAE,EAAEjG,OAAO;MAAEc,QAAQ,EAAEA,QAAQ;MAAEkH,cAAc,EAAE,IAAI,CAACxH,cAAc;MAAEyH,YAAY,EAAE,IAAI,CAACxH,YAAY;MAAEyH,WAAW,EAAE,IAAI,CAACxH,WAAW;MAAEyH,SAAS,EAAE,IAAI,CAACxH,SAAS;MAAEyH,YAAY,EAAE,IAAI,CAACC,YAAY;MAAEC,SAAS,EAAE,IAAI,CAACC,SAAS;MAAEC,GAAG,EAAE,IAAI,CAACA,GAAG;MAAEC,GAAG,EAAE,IAAI,CAACA,GAAG;MAAEC,SAAS,EAAE,IAAI,CAACC,SAAS;MAAEC,SAAS,EAAE,IAAI,CAACrC,SAAS;MAAEsC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAE7H,IAAI,EAAE,IAAI,CAACA,IAAI;MAAE8H,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEC,WAAW,EAAE,IAAI,CAACA,WAAW,IAAI,EAAE;MAAEC,QAAQ,EAAE/H,QAAQ;MAAEC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEC,UAAU,EAAE,IAAI,CAACA,UAAU;MAAE8H,IAAI,EAAE,IAAI,CAACA,IAAI;MAAE7H,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,KAAK,EAAEA,KAAK;MAAEC,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEK,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEE,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEG,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEkH,SAAS,EAAE,IAAI,CAACjH,SAAS;MAAEkH,kBAAkB,EAAE,IAAI,CAAChH,kBAAkB;MAAEiH,gBAAgB,EAAE,IAAI,CAAChH,gBAAgB;MAAE,kBAAkB,EAAE,IAAI,CAAC+D,aAAa,CAAC,CAAC;MAAE,cAAc,EAAE,IAAI,CAACA,aAAa,CAAC,CAAC,KAAK,IAAI,CAAChG;IAAY,CAAC,EAAE,IAAI,CAACC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAACQ,UAAU,IAAI,CAACK,QAAQ,IAAI,CAACH,QAAQ,IAAK7C,qDAAC,CAAC,QAAQ,EAAE;MAAE0H,GAAG,EAAE,0CAA0C;MAAE,YAAY,EAAE,OAAO;MAAEvE,IAAI,EAAE,QAAQ;MAAE8E,KAAK,EAAE,kBAAkB;MAAEmD,aAAa,EAAG9H,EAAE,IAAK;QAC7hD;AAChB;AACA;AACA;AACA;QACgBA,EAAE,CAACe,cAAc,CAAC,CAAC;MACvB,CAAC;MAAEyF,OAAO,EAAE,IAAI,CAAC1F;IAAe,CAAC,EAAEpE,qDAAC,CAAC,UAAU,EAAE;MAAE0H,GAAG,EAAE,0CAA0C;MAAE,aAAa,EAAE,MAAM;MAAE2D,IAAI,EAAE5B;IAAc,CAAC,CAAC,CAAE,EAAEzJ,qDAAC,CAAC,MAAM,EAAE;MAAE0H,GAAG,EAAE,0CAA0C;MAAE3E,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,EAAEwG,qBAAqB,IAAIvJ,qDAAC,CAAC,KAAK,EAAE;MAAE0H,GAAG,EAAE,0CAA0C;MAAEO,KAAK,EAAE;IAAkB,CAAC,CAAC,CAAC,EAAE,IAAI,CAACO,mBAAmB,CAAC,CAAC,CAAC;EACzX;EACA,IAAIxD,EAAEA,CAAA,EAAG;IAAE,OAAO1E,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWgL,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,MAAM,EAAE,CAAC,cAAc,CAAC;MACxB,OAAO,EAAE,CAAC,cAAc,CAAC;MACzB,KAAK,EAAE,CAAC,cAAc;IAC1B,CAAC;EAAE;AACP,CAAC;AACD,IAAItJ,QAAQ,GAAG,CAAC;AAChBR,KAAK,CAAC+J,KAAK,GAAG;EACVC,GAAG,EAAElK,WAAW;EAChBmK,EAAE,EAAElK;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-input.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, n as forceUpdate, h, e as getIonMode, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { c as createNotchController } from './notch-controller-C5LPspO8.js';\nimport { d as debounceEvent, i as inheritAriaAttributes, b as inheritAttributes, c as componentOnReady } from './helpers-1O4D2b7y.js';\nimport { c as createSlotMutationController, g as getCounterText } from './input.utils-zWijNCrx.js';\nimport { h as hostContext, c as createColorClasses } from './theme-DiVJyqlX.js';\nimport { b as closeCircle, d as closeSharp } from './index-BLV6ykCk.js';\nimport './index-ZjP4CjeZ.js';\n\nconst inputIosCss = \".sc-ion-input-ios-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--background:transparent;--color:initial;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;padding:0 !important;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2}ion-item[slot=start].sc-ion-input-ios-h,ion-item [slot=start].sc-ion-input-ios-h,ion-item[slot=end].sc-ion-input-ios-h,ion-item [slot=end].sc-ion-input-ios-h{width:auto}.ion-color.sc-ion-input-ios-h{--highlight-color-focused:var(--ion-color-base)}.input-label-placement-floating.sc-ion-input-ios-h,.input-label-placement-stacked.sc-ion-input-ios-h{min-height:56px}.native-input.sc-ion-input-ios{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:inline-block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;height:100%;max-height:100%;border:0;outline:none;background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:1}.native-input.sc-ion-input-ios::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios:-webkit-autofill{background-color:transparent}.native-input.sc-ion-input-ios:invalid{-webkit-box-shadow:none;box-shadow:none}.native-input.sc-ion-input-ios::-ms-clear{display:none}.cloned-input.sc-ion-input-ios{top:0;bottom:0;position:absolute;pointer-events:none}.cloned-input.sc-ion-input-ios{inset-inline-start:0}.cloned-input.sc-ion-input-ios:disabled{opacity:1}.input-clear-icon.sc-ion-input-ios{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;background-position:center;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:30px;height:30px;border:0;outline:none;background-color:transparent;background-repeat:no-repeat;color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));visibility:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}.in-item-color.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{color:inherit}.input-clear-icon.sc-ion-input-ios:focus{opacity:0.5}.has-value.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{visibility:visible}.input-wrapper.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:stretch;align-items:stretch;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-input-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;width:100%}.ion-touched.ion-invalid.sc-ion-input-ios-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-ios-h{--highlight-color:var(--highlight-color-valid)}.input-bottom.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}.has-focus.ion-valid.sc-ion-input-ios-h,.ion-touched.ion-invalid.sc-ion-input-ios-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios{display:none;color:var(--highlight-color-invalid)}.input-bottom.sc-ion-input-ios .helper-text.sc-ion-input-ios{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}.ion-touched.ion-invalid.sc-ion-input-ios-h .input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios{display:block}.ion-touched.ion-invalid.sc-ion-input-ios-h .input-bottom.sc-ion-input-ios .helper-text.sc-ion-input-ios{display:none}.input-bottom.sc-ion-input-ios .counter.sc-ion-input-ios{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.has-focus.sc-ion-input-ios-h input.sc-ion-input-ios{caret-color:var(--highlight-color)}.label-text-wrapper.sc-ion-input-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-input-ios,.sc-ion-input-ios-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-input-ios,.input-outline-notch-hidden.sc-ion-input-ios{display:none}.input-wrapper.sc-ion-input-ios input.sc-ion-input-ios{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.input-label-placement-start.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:row;flex-direction:row}.input-label-placement-start.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-end.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.input-label-placement-end.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-ios-h .label-text.sc-ion-input-ios{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.input-label-placement-stacked.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:left top;transform-origin:left top;max-width:100%;z-index:2}[dir=rtl].sc-ion-input-ios-h -no-combinator.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h -no-combinator.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].sc-ion-input-ios-h -no-combinator.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h -no-combinator.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-label-placement-stacked.sc-ion-input-ios-h:dir(rtl) .label-text-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h:dir(rtl) .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:right top;transform-origin:right top}}.input-label-placement-stacked.sc-ion-input-ios-h input.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0}.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{opacity:0}.has-focus.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios,.has-value.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{opacity:1}.label-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.sc-ion-input-ios-s>[slot=start]:last-of-type{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-input-ios-s>[slot=end]:first-of-type{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.sc-ion-input-ios-h[disabled].sc-ion-input-ios-s>ion-input-password-toggle,.sc-ion-input-ios-h[disabled] .sc-ion-input-ios-s>ion-input-password-toggle,.sc-ion-input-ios-h[readonly].sc-ion-input-ios-s>ion-input-password-toggle,.sc-ion-input-ios-h[readonly] .sc-ion-input-ios-s>ion-input-password-toggle{visibility:hidden}.sc-ion-input-ios-h{--border-width:0.55px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--highlight-height:0px;font-size:inherit}.input-clear-icon.sc-ion-input-ios ion-icon.sc-ion-input-ios{width:18px;height:18px}.input-disabled.sc-ion-input-ios-h{opacity:0.3}.sc-ion-input-ios-s>ion-button[slot=start].button-has-icon-only,.sc-ion-input-ios-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\n\nconst inputMdCss = \".sc-ion-input-md-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--background:transparent;--color:initial;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;padding:0 !important;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2}ion-item[slot=start].sc-ion-input-md-h,ion-item [slot=start].sc-ion-input-md-h,ion-item[slot=end].sc-ion-input-md-h,ion-item [slot=end].sc-ion-input-md-h{width:auto}.ion-color.sc-ion-input-md-h{--highlight-color-focused:var(--ion-color-base)}.input-label-placement-floating.sc-ion-input-md-h,.input-label-placement-stacked.sc-ion-input-md-h{min-height:56px}.native-input.sc-ion-input-md{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:inline-block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;height:100%;max-height:100%;border:0;outline:none;background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:1}.native-input.sc-ion-input-md::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md:-webkit-autofill{background-color:transparent}.native-input.sc-ion-input-md:invalid{-webkit-box-shadow:none;box-shadow:none}.native-input.sc-ion-input-md::-ms-clear{display:none}.cloned-input.sc-ion-input-md{top:0;bottom:0;position:absolute;pointer-events:none}.cloned-input.sc-ion-input-md{inset-inline-start:0}.cloned-input.sc-ion-input-md:disabled{opacity:1}.input-clear-icon.sc-ion-input-md{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;background-position:center;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:30px;height:30px;border:0;outline:none;background-color:transparent;background-repeat:no-repeat;color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));visibility:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}.in-item-color.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{color:inherit}.input-clear-icon.sc-ion-input-md:focus{opacity:0.5}.has-value.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{visibility:visible}.input-wrapper.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:stretch;align-items:stretch;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-input-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;width:100%}.ion-touched.ion-invalid.sc-ion-input-md-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-md-h{--highlight-color:var(--highlight-color-valid)}.input-bottom.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}.has-focus.ion-valid.sc-ion-input-md-h,.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-md .error-text.sc-ion-input-md{display:none;color:var(--highlight-color-invalid)}.input-bottom.sc-ion-input-md .helper-text.sc-ion-input-md{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}.ion-touched.ion-invalid.sc-ion-input-md-h .input-bottom.sc-ion-input-md .error-text.sc-ion-input-md{display:block}.ion-touched.ion-invalid.sc-ion-input-md-h .input-bottom.sc-ion-input-md .helper-text.sc-ion-input-md{display:none}.input-bottom.sc-ion-input-md .counter.sc-ion-input-md{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.has-focus.sc-ion-input-md-h input.sc-ion-input-md{caret-color:var(--highlight-color)}.label-text-wrapper.sc-ion-input-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-input-md,.sc-ion-input-md-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-input-md,.input-outline-notch-hidden.sc-ion-input-md{display:none}.input-wrapper.sc-ion-input-md input.sc-ion-input-md{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.input-label-placement-start.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:row;flex-direction:row}.input-label-placement-start.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-end.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.input-label-placement-end.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-md-h .label-text.sc-ion-input-md{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.input-label-placement-stacked.sc-ion-input-md-h .input-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:left top;transform-origin:left top;max-width:100%;z-index:2}[dir=rtl].sc-ion-input-md-h -no-combinator.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].sc-ion-input-md-h -no-combinator.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-label-placement-stacked.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}}.input-label-placement-stacked.sc-ion-input-md-h input.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0}.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{opacity:0}.has-focus.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md,.has-value.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{opacity:1}.label-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.sc-ion-input-md-s>[slot=start]:last-of-type{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-input-md-s>[slot=end]:first-of-type{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.sc-ion-input-md-h[disabled].sc-ion-input-md-s>ion-input-password-toggle,.sc-ion-input-md-h[disabled] .sc-ion-input-md-s>ion-input-password-toggle,.sc-ion-input-md-h[readonly].sc-ion-input-md-s>ion-input-password-toggle,.sc-ion-input-md-h[readonly] .sc-ion-input-md-s>ion-input-password-toggle{visibility:hidden}.input-fill-solid.sc-ion-input-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-500, var(--ion-background-color-step-500, gray));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-bottom:var(--border-width) var(--border-style) var(--border-color)}.has-focus.input-fill-solid.ion-valid.sc-ion-input-md-h,.input-fill-solid.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-fill-solid.sc-ion-input-md-h .input-bottom.sc-ion-input-md{border-top:none}@media (any-hover: hover){.input-fill-solid.sc-ion-input-md-h:hover{--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}.input-fill-solid.has-focus.sc-ion-input-md-h{--background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0px;border-end-start-radius:0px}.label-floating.input-fill-solid.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{max-width:calc(100% / 0.75)}.input-fill-outline.sc-ion-input-md-h{--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.input-fill-outline.input-shape-round.sc-ion-input-md-h{--border-radius:28px;--padding-start:32px;--padding-end:32px}.has-focus.input-fill-outline.ion-valid.sc-ion-input-md-h,.input-fill-outline.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}@media (any-hover: hover){.input-fill-outline.sc-ion-input-md-h:hover{--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}.input-fill-outline.has-focus.sc-ion-input-md-h{--border-width:var(--highlight-height);--border-color:var(--highlight-color)}.input-fill-outline.sc-ion-input-md-h .input-bottom.sc-ion-input-md{border-top:none}.input-fill-outline.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-bottom:none}.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:left top;transform-origin:left top;position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}}.input-fill-outline.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{position:relative}.label-floating.input-fill-outline.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h input.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}.input-fill-outline.sc-ion-input-md-h .input-outline-container.sc-ion-input-md{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{pointer-events:none}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color)}.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md{max-width:calc(100% - var(--padding-start) - var(--padding-end))}.input-fill-outline.sc-ion-input-md-h .notch-spacer.sc-ion-input-md{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none;-webkit-box-sizing:content-box;box-sizing:content-box}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md{border-start-start-radius:var(--border-radius);border-start-end-radius:0px;border-end-end-radius:0px;border-end-start-radius:var(--border-radius);-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color);width:calc(var(--padding-start) - 4px)}.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color);border-start-start-radius:0px;border-start-end-radius:var(--border-radius);border-end-end-radius:var(--border-radius);border-end-start-radius:0px;-ms-flex-positive:1;flex-grow:1}.label-floating.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md{border-top:none}.sc-ion-input-md-h{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--highlight-height:2px;font-size:inherit}.input-clear-icon.sc-ion-input-md ion-icon.sc-ion-input-md{width:22px;height:22px}.input-disabled.sc-ion-input-md-h{opacity:0.38}.has-focus.ion-valid.sc-ion-input-md-h,.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-md .counter.sc-ion-input-md{letter-spacing:0.0333333333em}.input-label-placement-floating.has-focus.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-stacked.has-focus.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{color:var(--highlight-color)}.has-focus.input-label-placement-floating.ion-valid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.ion-touched.ion-invalid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.has-focus.input-label-placement-stacked.ion-valid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-stacked.ion-touched.ion-invalid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{color:var(--highlight-color)}.input-highlight.sc-ion-input-md{bottom:-1px;position:absolute;width:100%;height:var(--highlight-height);-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}.input-highlight.sc-ion-input-md{inset-inline-start:0}.has-focus.sc-ion-input-md-h .input-highlight.sc-ion-input-md{-webkit-transform:scale(1);transform:scale(1)}.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{bottom:0}.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{inset-inline-start:0}.input-shape-round.sc-ion-input-md-h{--border-radius:16px}.sc-ion-input-md-s>ion-button[slot=start].button-has-icon-only,.sc-ion-input-md-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\n\nconst Input = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionInput = createEvent(this, \"ionInput\", 7);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.inputId = `ion-input-${inputIds++}`;\n        this.helperTextId = `${this.inputId}-helper-text`;\n        this.errorTextId = `${this.inputId}-error-text`;\n        this.inheritedAttributes = {};\n        this.isComposing = false;\n        /**\n         * `true` if the input was cleared as a result of the user typing\n         * with `clearOnEdit` enabled.\n         *\n         * Resets when the input loses focus.\n         */\n        this.didInputClearOnEdit = false;\n        /**\n         * The `hasFocus` state ensures the focus class is\n         * added regardless of how the element is focused.\n         * The `ion-focused` class only applies when focused\n         * via tabbing, not by clicking.\n         * The `has-focus` logic was added to ensure the class\n         * is applied in both cases.\n         */\n        this.hasFocus = false;\n        /**\n         * Indicates whether and how the text value should be automatically capitalized as it is entered/edited by the user.\n         * Available options: `\"off\"`, `\"none\"`, `\"on\"`, `\"sentences\"`, `\"words\"`, `\"characters\"`.\n         */\n        this.autocapitalize = 'off';\n        /**\n         * Indicates whether the value of the control can be automatically completed by the browser.\n         */\n        this.autocomplete = 'off';\n        /**\n         * Whether auto correction should be enabled when the user is entering/editing the text value.\n         */\n        this.autocorrect = 'off';\n        /**\n         * Sets the [`autofocus` attribute](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/autofocus) on the native input element.\n         *\n         * This may not be sufficient for the element to be focused on page load. See [managing focus](/docs/developing/managing-focus) for more information.\n         */\n        this.autofocus = false;\n        /**\n         * If `true`, a clear icon will appear in the input when there is a value. Clicking it clears the input.\n         */\n        this.clearInput = false;\n        /**\n         * If `true`, a character counter will display the ratio of characters used and the total character limit. Developers must also set the `maxlength` property for the counter to be calculated correctly.\n         */\n        this.counter = false;\n        /**\n         * If `true`, the user cannot interact with the input.\n         */\n        this.disabled = false;\n        /**\n         * Where to place the label relative to the input.\n         * `\"start\"`: The label will appear to the left of the input in LTR and to the right in RTL.\n         * `\"end\"`: The label will appear to the right of the input in LTR and to the left in RTL.\n         * `\"floating\"`: The label will appear smaller and above the input when the input is focused or it has a value. Otherwise it will appear on top of the input.\n         * `\"stacked\"`: The label will appear smaller and above the input regardless even when the input is blurred or has no value.\n         * `\"fixed\"`: The label has the same behavior as `\"start\"` except it also has a fixed width. Long text will be truncated with ellipses (\"...\").\n         */\n        this.labelPlacement = 'start';\n        /**\n         * The name of the control, which is submitted with the form data.\n         */\n        this.name = this.inputId;\n        /**\n         * If `true`, the user cannot modify the value.\n         */\n        this.readonly = false;\n        /**\n         * If `true`, the user must fill in a value before submitting a form.\n         */\n        this.required = false;\n        /**\n         * If `true`, the element will have its spelling and grammar checked.\n         */\n        this.spellcheck = false;\n        /**\n         * The type of control to display. The default type is text.\n         */\n        this.type = 'text';\n        /**\n         * The value of the input.\n         */\n        this.value = '';\n        this.onInput = (ev) => {\n            const input = ev.target;\n            if (input) {\n                this.value = input.value || '';\n            }\n            this.emitInputChange(ev);\n        };\n        this.onChange = (ev) => {\n            this.emitValueChange(ev);\n        };\n        this.onBlur = (ev) => {\n            this.hasFocus = false;\n            if (this.focusedValue !== this.value) {\n                /**\n                 * Emits the `ionChange` event when the input value\n                 * is different than the value when the input was focused.\n                 */\n                this.emitValueChange(ev);\n            }\n            this.didInputClearOnEdit = false;\n            this.ionBlur.emit(ev);\n        };\n        this.onFocus = (ev) => {\n            this.hasFocus = true;\n            this.focusedValue = this.value;\n            this.ionFocus.emit(ev);\n        };\n        this.onKeydown = (ev) => {\n            this.checkClearOnEdit(ev);\n        };\n        this.onCompositionStart = () => {\n            this.isComposing = true;\n        };\n        this.onCompositionEnd = () => {\n            this.isComposing = false;\n        };\n        this.clearTextInput = (ev) => {\n            if (this.clearInput && !this.readonly && !this.disabled && ev) {\n                ev.preventDefault();\n                ev.stopPropagation();\n                // Attempt to focus input again after pressing clear button\n                this.setFocus();\n            }\n            this.value = '';\n            this.emitInputChange(ev);\n        };\n        /**\n         * Stops propagation when the label is clicked,\n         * otherwise, two clicks will be triggered.\n         */\n        this.onLabelClick = (ev) => {\n            // Only stop propagation if the click was directly on the label\n            // and not on the input or other child elements\n            if (ev.target === ev.currentTarget) {\n                ev.stopPropagation();\n            }\n        };\n    }\n    debounceChanged() {\n        const { ionInput, debounce, originalIonInput } = this;\n        /**\n         * If debounce is undefined, we have to manually revert the ionInput emitter in case\n         * debounce used to be set to a number. Otherwise, the event would stay debounced.\n         */\n        this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n    }\n    /**\n     * Whenever the type on the input changes we need\n     * to update the internal type prop on the password\n     * toggle so that that correct icon is shown.\n     */\n    onTypeChange() {\n        const passwordToggle = this.el.querySelector('ion-input-password-toggle');\n        if (passwordToggle) {\n            passwordToggle.type = this.type;\n        }\n    }\n    /**\n     * Update the native input element when the value changes\n     */\n    valueChanged() {\n        const nativeInput = this.nativeInput;\n        const value = this.getValue();\n        if (nativeInput && nativeInput.value !== value && !this.isComposing) {\n            /**\n             * Assigning the native input's value on attribute\n             * value change, allows `ionInput` implementations\n             * to override the control's value.\n             *\n             * Used for patterns such as input trimming (removing whitespace),\n             * or input masking.\n             */\n            nativeInput.value = value;\n        }\n    }\n    /**\n     * dir is a globally enumerated attribute.\n     * As a result, creating these as properties\n     * can have unintended side effects. Instead, we\n     * listen for attribute changes and inherit them\n     * to the inner `<input>` element.\n     */\n    onDirChanged(newValue) {\n        this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), { dir: newValue });\n        forceUpdate(this);\n    }\n    /**\n     * This prevents the native input from emitting the click event.\n     * Instead, the click event from the ion-input is emitted.\n     */\n    onClickCapture(ev) {\n        const nativeInput = this.nativeInput;\n        if (nativeInput && ev.target === nativeInput) {\n            ev.stopPropagation();\n            this.el.click();\n        }\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = Object.assign(Object.assign({}, inheritAriaAttributes(this.el)), inheritAttributes(this.el, ['tabindex', 'title', 'data-form-type', 'dir']));\n    }\n    connectedCallback() {\n        const { el } = this;\n        this.slotMutationController = createSlotMutationController(el, ['label', 'start', 'end'], () => forceUpdate(this));\n        this.notchController = createNotchController(el, () => this.notchSpacerEl, () => this.labelSlot);\n        this.debounceChanged();\n        {\n            document.dispatchEvent(new CustomEvent('ionInputDidLoad', {\n                detail: this.el,\n            }));\n        }\n    }\n    componentDidLoad() {\n        this.originalIonInput = this.ionInput;\n        /**\n         * Set the type on the password toggle in the event that this input's\n         * type was set async and does not match the default type for the password toggle.\n         * This can happen when the type is bound using a JS framework binding syntax\n         * such as [type] in Angular.\n         */\n        this.onTypeChange();\n        this.debounceChanged();\n    }\n    componentDidRender() {\n        var _a;\n        (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n    }\n    disconnectedCallback() {\n        {\n            document.dispatchEvent(new CustomEvent('ionInputDidUnload', {\n                detail: this.el,\n            }));\n        }\n        if (this.slotMutationController) {\n            this.slotMutationController.destroy();\n            this.slotMutationController = undefined;\n        }\n        if (this.notchController) {\n            this.notchController.destroy();\n            this.notchController = undefined;\n        }\n    }\n    /**\n     * Sets focus on the native `input` in `ion-input`. Use this method instead of the global\n     * `input.focus()`.\n     *\n     * Developers who wish to focus an input when a page enters\n     * should call `setFocus()` in the `ionViewDidEnter()` lifecycle method.\n     *\n     * Developers who wish to focus an input when an overlay is presented\n     * should call `setFocus` after `didPresent` has resolved.\n     *\n     * See [managing focus](/docs/developing/managing-focus) for more information.\n     */\n    async setFocus() {\n        if (this.nativeInput) {\n            this.nativeInput.focus();\n        }\n    }\n    /**\n     * Returns the native `<input>` element used under the hood.\n     */\n    async getInputElement() {\n        /**\n         * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n         * nativeInput won't be defined yet with the custom elements build, so wait for it to load in.\n         */\n        if (!this.nativeInput) {\n            await new Promise((resolve) => componentOnReady(this.el, resolve));\n        }\n        return Promise.resolve(this.nativeInput);\n    }\n    /**\n     * Emits an `ionChange` event.\n     *\n     * This API should be called for user committed changes.\n     * This API should not be used for external value changes.\n     */\n    emitValueChange(event) {\n        const { value } = this;\n        // Checks for both null and undefined values\n        const newValue = value == null ? value : value.toString();\n        // Emitting a value change should update the internal state for tracking the focused value\n        this.focusedValue = newValue;\n        this.ionChange.emit({ value: newValue, event });\n    }\n    /**\n     * Emits an `ionInput` event.\n     */\n    emitInputChange(event) {\n        const { value } = this;\n        // Checks for both null and undefined values\n        const newValue = value == null ? value : value.toString();\n        this.ionInput.emit({ value: newValue, event });\n    }\n    shouldClearOnEdit() {\n        const { type, clearOnEdit } = this;\n        return clearOnEdit === undefined ? type === 'password' : clearOnEdit;\n    }\n    getValue() {\n        return typeof this.value === 'number' ? this.value.toString() : (this.value || '').toString();\n    }\n    checkClearOnEdit(ev) {\n        if (!this.shouldClearOnEdit()) {\n            return;\n        }\n        /**\n         * The following keys do not modify the\n         * contents of the input. As a result, pressing\n         * them should not edit the input.\n         *\n         * We can't check to see if the value of the input\n         * was changed because we call checkClearOnEdit\n         * in a keydown listener, and the key has not yet\n         * been added to the input.\n         */\n        const IGNORED_KEYS = ['Enter', 'Tab', 'Shift', 'Meta', 'Alt', 'Control'];\n        const pressedIgnoredKey = IGNORED_KEYS.includes(ev.key);\n        /**\n         * Clear the input if the control has not been previously cleared during focus.\n         * Do not clear if the user hitting enter to submit a form.\n         */\n        if (!this.didInputClearOnEdit && this.hasValue() && !pressedIgnoredKey) {\n            this.value = '';\n            this.emitInputChange(ev);\n        }\n        /**\n         * Pressing an IGNORED_KEYS first and\n         * then an allowed key will cause the input to not\n         * be cleared.\n         */\n        if (!pressedIgnoredKey) {\n            this.didInputClearOnEdit = true;\n        }\n    }\n    hasValue() {\n        return this.getValue().length > 0;\n    }\n    /**\n     * Renders the helper text or error text values\n     */\n    renderHintText() {\n        const { helperText, errorText, helperTextId, errorTextId } = this;\n        return [\n            h(\"div\", { id: helperTextId, class: \"helper-text\" }, helperText),\n            h(\"div\", { id: errorTextId, class: \"error-text\" }, errorText),\n        ];\n    }\n    getHintTextID() {\n        const { el, helperText, errorText, helperTextId, errorTextId } = this;\n        if (el.classList.contains('ion-touched') && el.classList.contains('ion-invalid') && errorText) {\n            return errorTextId;\n        }\n        if (helperText) {\n            return helperTextId;\n        }\n        return undefined;\n    }\n    renderCounter() {\n        const { counter, maxlength, counterFormatter, value } = this;\n        if (counter !== true || maxlength === undefined) {\n            return;\n        }\n        return h(\"div\", { class: \"counter\" }, getCounterText(value, maxlength, counterFormatter));\n    }\n    /**\n     * Responsible for rendering helper text,\n     * error text, and counter. This element should only\n     * be rendered if hint text is set or counter is enabled.\n     */\n    renderBottomContent() {\n        const { counter, helperText, errorText, maxlength } = this;\n        /**\n         * undefined and empty string values should\n         * be treated as not having helper/error text.\n         */\n        const hasHintText = !!helperText || !!errorText;\n        const hasCounter = counter === true && maxlength !== undefined;\n        if (!hasHintText && !hasCounter) {\n            return;\n        }\n        return (h(\"div\", { class: \"input-bottom\" }, this.renderHintText(), this.renderCounter()));\n    }\n    renderLabel() {\n        const { label } = this;\n        return (h(\"div\", { class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !this.hasLabel,\n            } }, label === undefined ? h(\"slot\", { name: \"label\" }) : h(\"div\", { class: \"label-text\" }, label)));\n    }\n    /**\n     * Gets any content passed into the `label` slot,\n     * not the <slot> definition.\n     */\n    get labelSlot() {\n        return this.el.querySelector('[slot=\"label\"]');\n    }\n    /**\n     * Returns `true` if label content is provided\n     * either by a prop or a content. If you want\n     * to get the plaintext value of the label use\n     * the `labelText` getter instead.\n     */\n    get hasLabel() {\n        return this.label !== undefined || this.labelSlot !== null;\n    }\n    /**\n     * Renders the border container\n     * when fill=\"outline\".\n     */\n    renderLabelContainer() {\n        const mode = getIonMode(this);\n        const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n        if (hasOutlineFill) {\n            /**\n             * The outline fill has a special outline\n             * that appears around the input and the label.\n             * Certain stacked and floating label placements cause the\n             * label to translate up and create a \"cut out\"\n             * inside of that border by using the notch-spacer element.\n             */\n            return [\n                h(\"div\", { class: \"input-outline-container\" }, h(\"div\", { class: \"input-outline-start\" }), h(\"div\", { class: {\n                        'input-outline-notch': true,\n                        'input-outline-notch-hidden': !this.hasLabel,\n                    } }, h(\"div\", { class: \"notch-spacer\", \"aria-hidden\": \"true\", ref: (el) => (this.notchSpacerEl = el) }, this.label)), h(\"div\", { class: \"input-outline-end\" })),\n                this.renderLabel(),\n            ];\n        }\n        /**\n         * If not using the outline style,\n         * we can render just the label.\n         */\n        return this.renderLabel();\n    }\n    render() {\n        const { disabled, fill, readonly, shape, inputId, labelPlacement, el, hasFocus, clearInputIcon } = this;\n        const mode = getIonMode(this);\n        const value = this.getValue();\n        const inItem = hostContext('ion-item', this.el);\n        const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n        const defaultClearIcon = mode === 'ios' ? closeCircle : closeSharp;\n        const clearIconData = clearInputIcon !== null && clearInputIcon !== void 0 ? clearInputIcon : defaultClearIcon;\n        const hasValue = this.hasValue();\n        const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n        /**\n         * If the label is stacked, it should always sit above the input.\n         * For floating labels, the label should move above the input if\n         * the input has a value, is focused, or has anything in either\n         * the start or end slot.\n         *\n         * If there is content in the start slot, the label would overlap\n         * it if not forced to float. This is also applied to the end slot\n         * because with the default or solid fills, the input is not\n         * vertically centered in the container, but the label is. This\n         * causes the slots and label to appear vertically offset from each\n         * other when the label isn't floating above the input. This doesn't\n         * apply to the outline fill, but this was not accounted for to keep\n         * things consistent.\n         *\n         * TODO(FW-5592): Remove hasStartEndSlots condition\n         */\n        const labelShouldFloat = labelPlacement === 'stacked' || (labelPlacement === 'floating' && (hasValue || hasFocus || hasStartEndSlots));\n        return (h(Host, { key: '41b2526627e7d2773a80f011b123284203a71ca0', class: createColorClasses(this.color, {\n                [mode]: true,\n                'has-value': hasValue,\n                'has-focus': hasFocus,\n                'label-floating': labelShouldFloat,\n                [`input-fill-${fill}`]: fill !== undefined,\n                [`input-shape-${shape}`]: shape !== undefined,\n                [`input-label-placement-${labelPlacement}`]: true,\n                'in-item': inItem,\n                'in-item-color': hostContext('ion-item.ion-color', this.el),\n                'input-disabled': disabled,\n            }) }, h(\"label\", { key: '9ab078363e32528102b441ad1791d83f86fdcbdc', class: \"input-wrapper\", htmlFor: inputId, onClick: this.onLabelClick }, this.renderLabelContainer(), h(\"div\", { key: 'e34b594980ec62e4c618e827fadf7669a39ad0d8', class: \"native-wrapper\", onClick: this.onLabelClick }, h(\"slot\", { key: '12dc04ead5502e9e5736240e918bf9331bf7b5d9', name: \"start\" }), h(\"input\", Object.assign({ key: 'df356eb4ced23109b2c0242f36dc043aba8782d6', class: \"native-input\", ref: (input) => (this.nativeInput = input), id: inputId, disabled: disabled, autoCapitalize: this.autocapitalize, autoComplete: this.autocomplete, autoCorrect: this.autocorrect, autoFocus: this.autofocus, enterKeyHint: this.enterkeyhint, inputMode: this.inputmode, min: this.min, max: this.max, minLength: this.minlength, maxLength: this.maxlength, multiple: this.multiple, name: this.name, pattern: this.pattern, placeholder: this.placeholder || '', readOnly: readonly, required: this.required, spellcheck: this.spellcheck, step: this.step, type: this.type, value: value, onInput: this.onInput, onChange: this.onChange, onBlur: this.onBlur, onFocus: this.onFocus, onKeyDown: this.onKeydown, onCompositionstart: this.onCompositionStart, onCompositionend: this.onCompositionEnd, \"aria-describedby\": this.getHintTextID(), \"aria-invalid\": this.getHintTextID() === this.errorTextId }, this.inheritedAttributes)), this.clearInput && !readonly && !disabled && (h(\"button\", { key: 'f79f68cabcd4ea99419331174a377827db0c0741', \"aria-label\": \"reset\", type: \"button\", class: \"input-clear-icon\", onPointerDown: (ev) => {\n                /**\n                 * This prevents mobile browsers from\n                 * blurring the input when the clear\n                 * button is activated.\n                 */\n                ev.preventDefault();\n            }, onClick: this.clearTextInput }, h(\"ion-icon\", { key: '237ec07ec2e10f08818a332bb596578c2c49f770', \"aria-hidden\": \"true\", icon: clearIconData }))), h(\"slot\", { key: '1f0a3624aa3e8dc3c307a6762230ab698768a5e5', name: \"end\" })), shouldRenderHighlight && h(\"div\", { key: '8a8cbb82695a722a0010b53dd0b1f1f97534a20b', class: \"input-highlight\" })), this.renderBottomContent()));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"debounce\": [\"debounceChanged\"],\n        \"type\": [\"onTypeChange\"],\n        \"value\": [\"valueChanged\"],\n        \"dir\": [\"onDirChanged\"]\n    }; }\n};\nlet inputIds = 0;\nInput.style = {\n    ios: inputIosCss,\n    md: inputMdCss\n};\n\nexport { Input as ion_input };\n"], "names": ["r", "registerInstance", "d", "createEvent", "n", "forceUpdate", "h", "e", "getIonMode", "j", "Host", "k", "getElement", "c", "createNotchController", "debounceEvent", "i", "inheritAriaAttributes", "b", "inheritAttributes", "componentOnReady", "createSlotMutationController", "g", "getCounterText", "hostContext", "createColorClasses", "closeCircle", "closeSharp", "inputIosCss", "inputMdCss", "Input", "constructor", "hostRef", "ionInput", "ionChange", "ionBlur", "ionFocus", "inputId", "inputIds", "helperTextId", "errorTextId", "inheritedAttributes", "isComposing", "didInputClearOnEdit", "hasFocus", "autocapitalize", "autocomplete", "autocorrect", "autofocus", "clearInput", "counter", "disabled", "labelPlacement", "name", "readonly", "required", "spellcheck", "type", "value", "onInput", "ev", "input", "target", "emitInputChange", "onChange", "emitValueChange", "onBlur", "focusedValue", "emit", "onFocus", "onKeydown", "checkClearOnEdit", "onCompositionStart", "onCompositionEnd", "clearTextInput", "preventDefault", "stopPropagation", "setFocus", "onLabelClick", "currentTarget", "debounce<PERSON><PERSON>ed", "debounce", "originalIonInput", "undefined", "onTypeChange", "passwordToggle", "el", "querySelector", "valueChanged", "nativeInput", "getValue", "onDirChanged", "newValue", "Object", "assign", "dir", "onClickCapture", "click", "componentWillLoad", "connectedCallback", "slotMutationController", "notchController", "notchSpacerEl", "labelSlot", "document", "dispatchEvent", "CustomEvent", "detail", "componentDidLoad", "componentDidRender", "_a", "calculateNotchWidth", "disconnectedCallback", "destroy", "_this", "_asyncToGenerator", "focus", "getInputElement", "_this2", "Promise", "resolve", "event", "toString", "shouldClearOnEdit", "clearOnEdit", "IGNORED_KEYS", "pressedIgnoredKey", "includes", "key", "hasValue", "length", "renderHintText", "helperText", "errorText", "id", "class", "getHintTextID", "classList", "contains", "renderCounter", "maxlength", "counterFormatter", "renderBottomContent", "hasHintText", "<PERSON><PERSON><PERSON><PERSON>", "renderLabel", "label", "<PERSON><PERSON><PERSON><PERSON>", "renderLabelContainer", "mode", "hasOutlineFill", "fill", "ref", "render", "shape", "clearInputIcon", "inItem", "should<PERSON>ender<PERSON>ighlight", "defaultClearIcon", "clearIconData", "hasStartEndSlots", "labelShouldFloat", "color", "htmlFor", "onClick", "autoCapitalize", "autoComplete", "autoCorrect", "autoFocus", "enterKeyHint", "enterkeyhint", "inputMode", "inputmode", "min", "max", "<PERSON><PERSON><PERSON><PERSON>", "minlength", "max<PERSON><PERSON><PERSON>", "multiple", "pattern", "placeholder", "readOnly", "step", "onKeyDown", "onCompositionstart", "onCompositionend", "onPointerDown", "icon", "watchers", "style", "ios", "md", "ion_input"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}