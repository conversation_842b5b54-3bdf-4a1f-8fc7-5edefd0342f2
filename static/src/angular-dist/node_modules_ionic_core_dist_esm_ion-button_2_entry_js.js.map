{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-button_2_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC2L;AACrG;AACU;AAEhG,IAAIyB,UAAU;AACd,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACjC,OAAO,IAAIC,GAAG,CAAC,CAAC;EAClB,CAAC,MACI;IACH,IAAI,CAACH,UAAU,EAAE;MACf,MAAMI,GAAG,GAAGF,MAAM;MAClBE,GAAG,CAACC,QAAQ,GAAGD,GAAG,CAACC,QAAQ,IAAI,CAAC,CAAC;MACjCL,UAAU,GAAGI,GAAG,CAACC,QAAQ,CAACC,GAAG,GAAGF,GAAG,CAACC,QAAQ,CAACC,GAAG,IAAI,IAAIH,GAAG,CAAC,CAAC;IAC/D;IACA,OAAOH,UAAU;EACnB;AACF,CAAC;AACD,MAAMO,MAAM,GAAId,CAAC,IAAK;EACpB,IAAIe,GAAG,GAAGC,MAAM,CAAChB,CAAC,CAACiB,GAAG,CAAC;EACvB,IAAIF,GAAG,EAAE;IACP,OAAOA,GAAG;EACZ;EACAA,GAAG,GAAGG,OAAO,CAAClB,CAAC,CAACmB,IAAI,EAAEnB,CAAC,CAACoB,IAAI,EAAEpB,CAAC,CAACqB,IAAI,EAAErB,CAAC,CAACsB,GAAG,EAAEtB,CAAC,CAACuB,EAAE,CAAC;EAClD,IAAIR,GAAG,EAAE;IACP,OAAOS,WAAW,CAACT,GAAG,EAAEf,CAAC,CAAC;EAC5B;EACA,IAAIA,CAAC,CAACoB,IAAI,EAAE;IACVL,GAAG,GAAGC,MAAM,CAAChB,CAAC,CAACoB,IAAI,CAAC;IACpB,IAAIL,GAAG,EAAE;MACP,OAAOA,GAAG;IACZ;IACAA,GAAG,GAAGC,MAAM,CAAChB,CAAC,CAACoB,IAAI,CAACpB,CAAC,CAACqB,IAAI,CAAC,CAAC;IAC5B,IAAIN,GAAG,EAAE;MACP,OAAOA,GAAG;IACZ;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,MAAMS,WAAW,GAAGA,CAACC,QAAQ,EAAEC,MAAM,KAAK;EACxC,MAAMX,GAAG,GAAGP,UAAU,CAAC,CAAC,CAACmB,GAAG,CAACF,QAAQ,CAAC;EACtC,IAAIV,GAAG,EAAE;IACP,OAAOA,GAAG;EACZ;EACA,IAAI;IACF,OAAOhC,qDAAY,CAAC,OAAO0C,QAAQ,MAAM,CAAC;EAC5C,CAAC,CACD,OAAOjC,CAAC,EAAE;IACR;AACJ;AACA;AACA;AACA;AACA;AACA;IACIoC,OAAO,CAACC,IAAI,CAAC,sDAAsDJ,QAAQ,0HAA0H,EAAEC,MAAM,CAAC;EAChN;AACF,CAAC;AACD,MAAMR,OAAO,GAAGA,CAACO,QAAQ,EAAEL,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,EAAE,KAAK;EACjD;EACAF,IAAI,GAAG,CAACA,IAAI,IAAIS,OAAO,CAACT,IAAI,CAAC,MAAM,KAAK,GAAG,KAAK,GAAG,IAAI;EACvD;EACA;EACA,IAAIC,GAAG,IAAID,IAAI,KAAK,KAAK,EAAE;IACzBI,QAAQ,GAAGK,OAAO,CAACR,GAAG,CAAC;EACzB,CAAC,MACI,IAAIC,EAAE,IAAIF,IAAI,KAAK,IAAI,EAAE;IAC5BI,QAAQ,GAAGK,OAAO,CAACP,EAAE,CAAC;EACxB,CAAC,MACI;IACH,IAAI,CAACE,QAAQ,IAAIL,IAAI,IAAI,CAACW,KAAK,CAACX,IAAI,CAAC,EAAE;MACrCK,QAAQ,GAAGL,IAAI;IACjB;IACA,IAAIY,KAAK,CAACP,QAAQ,CAAC,EAAE;MACnBA,QAAQ,GAAGK,OAAO,CAACL,QAAQ,CAAC;IAC9B;EACF;EACA,IAAI,CAACO,KAAK,CAACP,QAAQ,CAAC,IAAIA,QAAQ,CAACQ,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IAC9C,OAAO,IAAI;EACb;EACA;EACA,MAAMC,YAAY,GAAGT,QAAQ,CAACU,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;EACzD,IAAID,YAAY,KAAK,EAAE,EAAE;IACvB,OAAO,IAAI;EACb;EACA,OAAOT,QAAQ;AACjB,CAAC;AACD,MAAMT,MAAM,GAAIC,GAAG,IAAK;EACtB,IAAIe,KAAK,CAACf,GAAG,CAAC,EAAE;IACdA,GAAG,GAAGA,GAAG,CAACgB,IAAI,CAAC,CAAC;IAChB,IAAIF,KAAK,CAACd,GAAG,CAAC,EAAE;MACd,OAAOA,GAAG;IACZ;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,MAAMc,KAAK,GAAIK,GAAG,IAAKA,GAAG,CAACC,MAAM,GAAG,CAAC,IAAI,SAAS,CAACC,IAAI,CAACF,GAAG,CAAC;AAC5D,MAAMJ,KAAK,GAAIO,GAAG,IAAK,OAAOA,GAAG,KAAK,QAAQ;AAC9C,MAAMT,OAAO,GAAIS,GAAG,IAAKA,GAAG,CAACC,WAAW,CAAC,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAGA,CAACC,EAAE,EAAEC,UAAU,GAAG,EAAE,KAAK;EACjD,MAAMC,eAAe,GAAG,CAAC,CAAC;EAC1BD,UAAU,CAACE,OAAO,CAACC,IAAI,IAAI;IACzB,IAAIJ,EAAE,CAACK,YAAY,CAACD,IAAI,CAAC,EAAE;MACzB,MAAME,KAAK,GAAGN,EAAE,CAACO,YAAY,CAACH,IAAI,CAAC;MACnC,IAAIE,KAAK,KAAK,IAAI,EAAE;QAClBJ,eAAe,CAACE,IAAI,CAAC,GAAGJ,EAAE,CAACO,YAAY,CAACH,IAAI,CAAC;MAC/C;MACAJ,EAAE,CAACQ,eAAe,CAACJ,IAAI,CAAC;IAC1B;EACF,CAAC,CAAC;EACF,OAAOF,eAAe;AACxB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMO,KAAK,GAAIC,MAAM,IAAK;EACxB,IAAIA,MAAM,EAAE;IACV,IAAIA,MAAM,CAACC,GAAG,KAAK,EAAE,EAAE;MACrB,OAAOD,MAAM,CAACC,GAAG,CAACb,WAAW,CAAC,CAAC,KAAK,KAAK;IAC3C;EACF;EACA,OAAO,CAACc,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACD,GAAG,CAACb,WAAW,CAAC,CAAC,MAAM,KAAK;AACnG,CAAC;AAED,MAAMe,YAAY,GAAG,i7RAAi7R;AAEt8R,MAAMC,WAAW,GAAG,o4QAAo4Q;AAEx5Q,MAAMC,MAAM,GAAG,MAAM;EACjBC,WAAWA,CAACC,OAAO,EAAE;IACjB1E,qDAAgB,CAAC,IAAI,EAAE0E,OAAO,CAAC;IAC/B,IAAI,CAACC,QAAQ,GAAGzE,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC0E,OAAO,GAAG1E,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC2E,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,QAAQ;IAC1B;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,SAAS;IAChC;AACR;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB,IAAI,CAACC,WAAW,GAAIC,EAAE,IAAK;MACvB,MAAM;QAAEjC;MAAG,CAAC,GAAG,IAAI;MACnB,IAAI,IAAI,CAAC+B,IAAI,KAAK,QAAQ,EAAE;QACxBtE,qDAAO,CAAC,IAAI,CAACyE,IAAI,EAAED,EAAE,EAAE,IAAI,CAACJ,eAAe,EAAE,IAAI,CAACM,eAAe,CAAC;MACtE,CAAC,MACI,IAAI9E,uDAAY,CAAC2C,EAAE,CAAC,EAAE;QACvB,IAAI,CAACoC,UAAU,CAACH,EAAE,CAAC;MACvB;IACJ,CAAC;IACD,IAAI,CAACI,OAAO,GAAG,MAAM;MACjB,IAAI,CAACnB,QAAQ,CAACoB,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAACpB,OAAO,CAACmB,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACE,WAAW,GAAG,MAAM;MACrB;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAACd,QAAQ,GAAG,IAAI,CAACe,WAAW;IACpC,CAAC;EACL;EACAC,eAAeA,CAAA,EAAG;IACd,MAAM;MAAEd;IAAS,CAAC,GAAG,IAAI;IACzB,IAAI,IAAI,CAACL,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACK,QAAQ,GAAGA,QAAQ;IACzC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIe,aAAaA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAE;IACzC,IAAI,CAACrB,mBAAmB,GAAGsB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACvB,mBAAmB,CAAC,EAAE;MAAE,CAACqB,QAAQ,GAAGF;IAAS,CAAC,CAAC;IAC/GjG,qDAAW,CAAC,IAAI,CAAC;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIsG,kBAAkBA,CAAA,EAAG;IACjB,MAAMzB,MAAM,GAAI,IAAI,CAACA,MAAM,GAAG,IAAI,CAAC0B,QAAQ,CAAC,CAAE;IAC9C,IAAI1B,MAAM,EAAE;MACR,MAAM;QAAED;MAAa,CAAC,GAAG,IAAI;MAC7B;AACZ;AACA;AACA;MACY,IAAIA,YAAY,KAAK,IAAI,IAAIC,MAAM,CAAC2B,QAAQ,CAAC5B,YAAY,CAAC,EAAE;QACxD;MACJ;MACA;MACA,MAAM6B,eAAe,GAAI,IAAI,CAAC7B,YAAY,GAAGX,QAAQ,CAACyC,aAAa,CAAC,QAAQ,CAAE;MAC9ED,eAAe,CAACrB,IAAI,GAAG,IAAI,CAACA,IAAI;MAChCqB,eAAe,CAACE,KAAK,CAACC,OAAO,GAAG,MAAM;MACtC;MACAH,eAAe,CAACxB,QAAQ,GAAG,IAAI,CAACA,QAAQ;MACxCJ,MAAM,CAACgC,WAAW,CAACJ,eAAe,CAAC;IACvC;EACJ;EACAK,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACnC,SAAS,GAAG,CAAC,CAAC,IAAI,CAACtB,EAAE,CAAC0D,OAAO,CAAC,aAAa,CAAC;IACjD,IAAI,CAACrC,YAAY,GAAG,CAAC,CAAC,IAAI,CAACrB,EAAE,CAAC0D,OAAO,CAAC,iBAAiB,CAAC;IACxD,IAAI,CAACtC,MAAM,GAAG,CAAC,CAAC,IAAI,CAACpB,EAAE,CAAC0D,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC1D,EAAE,CAAC0D,OAAO,CAAC,kBAAkB,CAAC;IACpF,IAAI,CAACjC,mBAAmB,GAAGlE,uDAAqB,CAAC,IAAI,CAACyC,EAAE,CAAC;EAC7D;EACA,IAAIyC,WAAWA,CAAA,EAAG;IACd,OAAO,CAAC,CAAC,IAAI,CAACzC,EAAE,CAAC2D,aAAa,CAAC,oBAAoB,CAAC;EACxD;EACA,IAAIC,UAAUA,CAAA,EAAG;IACb,MAAMC,YAAY,GAAG,IAAI,CAACC,IAAI,KAAKC,SAAS,IAAI,IAAI,CAACD,IAAI,KAAK,OAAO;IACrE;IACA;IACA,IAAID,YAAY,IAAI,IAAI,CAACpB,WAAW,IAAI,IAAI,CAACnB,SAAS,EAAE;MACpD,OAAO,WAAW;IACtB;IACA,OAAO,SAAS;EACpB;EACA;AACJ;AACA;AACA;EACI4B,QAAQA,CAAA,EAAG;IACP,MAAM;MAAEc;IAAK,CAAC,GAAG,IAAI;IACrB,IAAIA,IAAI,YAAYC,eAAe,EAAE;MACjC,OAAOD,IAAI;IACf;IACA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC1B;MACA,MAAMhE,EAAE,GAAGY,QAAQ,CAACsD,cAAc,CAACF,IAAI,CAAC;MACxC,IAAIhE,EAAE,EAAE;QACJ,IAAIA,EAAE,YAAYiE,eAAe,EAAE;UAC/B,OAAOjE,EAAE;QACb,CAAC,MACI;UACD;AACpB;AACA;AACA;UACoBnD,qDAAe,CAAC,wCAAwCmH,IAAI,2EAA2E,EAAE,IAAI,CAAChE,EAAE,CAAC;UACjJ,OAAO,IAAI;QACf;MACJ,CAAC,MACI;QACD;AAChB;AACA;AACA;QACgBnD,qDAAe,CAAC,wCAAwCmH,IAAI,0FAA0F,EAAE,IAAI,CAAChE,EAAE,CAAC;QAChK,OAAO,IAAI;MACf;IACJ;IACA,IAAIgE,IAAI,KAAKD,SAAS,EAAE;MACpB;AACZ;AACA;AACA;AACA;AACA;MACYlH,qDAAe,CAAC,2HAA2H,EAAE,IAAI,CAACmD,EAAE,CAAC;MACrJ,OAAO,IAAI;IACf;IACA;AACR;AACA;AACA;IACQ,OAAO,IAAI,CAACA,EAAE,CAAC0D,OAAO,CAAC,MAAM,CAAC;EAClC;EACAtB,UAAUA,CAACH,EAAE,EAAE;IACX;IACA;IACA;IACA,IAAI,IAAI,CAACT,MAAM,IAAI,IAAI,CAACD,YAAY,EAAE;MAClCU,EAAE,CAACkC,cAAc,CAAC,CAAC;MACnB,IAAI,CAAC5C,YAAY,CAAC6C,KAAK,CAAC,CAAC;IAC7B;EACJ;EACAC,MAAMA,CAAA,EAAG;IACL,MAAM1F,IAAI,GAAG5B,qDAAY,CAAC,IAAI,CAAC;IAC/B,MAAM;MAAE4E,UAAU;MAAEI,IAAI;MAAEH,QAAQ;MAAE0C,GAAG;MAAEC,MAAM;MAAEC,IAAI;MAAEtC,IAAI;MAAEuC,KAAK;MAAEC,MAAM;MAAEjC,WAAW;MAAEkC,KAAK;MAAE7C,MAAM;MAAEL;IAAqB,CAAC,GAAG,IAAI;IACrI,MAAMmD,SAAS,GAAGJ,IAAI,KAAKT,SAAS,IAAI,IAAI,CAAC3C,MAAM,GAAG,OAAO,GAAGoD,IAAI;IACpE,MAAMK,OAAO,GAAG3C,IAAI,KAAK6B,SAAS,GAAG,QAAQ,GAAG,GAAG;IACnD,MAAMe,KAAK,GAAGD,OAAO,KAAK,QAAQ,GAC5B;MAAE9C;IAAK,CAAC,GACR;MACEgD,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvB7C,IAAI;MACJoC,GAAG;MACHC;IACJ,CAAC;IACL,IAAIT,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB;AACR;AACA;AACA;IACQ,IAAIA,IAAI,IAAI,IAAI,EAAE;MACdA,IAAI,GAAG,IAAI,CAACxC,SAAS,IAAI,IAAI,CAACD,YAAY,GAAG,OAAO,GAAG,OAAO;IAClE;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ;MACIU,IAAI,KAAK,QAAQ,IAAI,IAAI,CAACkB,kBAAkB,CAAC,CAAC;IAClD;IACA,OAAQjG,qDAAC,CAACE,iDAAI,EAAE;MAAE8H,GAAG,EAAE,0CAA0C;MAAEC,OAAO,EAAE,IAAI,CAACjD,WAAW;MAAE,eAAe,EAAEJ,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAEsD,KAAK,EAAEvH,qDAAoB,CAAC8G,KAAK,EAAE;QACpK,CAAC9F,IAAI,GAAG,IAAI;QACZ,CAACgD,UAAU,GAAG,IAAI;QAClB,CAAC,GAAGA,UAAU,IAAI+C,MAAM,EAAE,GAAGA,MAAM,KAAKX,SAAS;QACjD,CAAC,GAAGpC,UAAU,IAAIiD,SAAS,EAAE,GAAGA,SAAS,KAAKb,SAAS;QACvD,CAAC,GAAGpC,UAAU,IAAIgD,KAAK,EAAE,GAAGA,KAAK,KAAKZ,SAAS;QAC/C,CAAC,GAAGpC,UAAU,IAAImC,IAAI,EAAE,GAAG,IAAI;QAC/B,CAAC,GAAGnC,UAAU,SAAS,GAAGG,MAAM;QAChC,YAAY,EAAElE,qDAAW,CAAC,aAAa,EAAE,IAAI,CAACoC,EAAE,CAAC;QACjD,kBAAkB,EAAEpC,qDAAW,CAAC,oBAAoB,EAAE,IAAI,CAACoC,EAAE,CAAC;QAC9D,YAAY,EAAEpC,qDAAW,CAAC,aAAa,EAAE,IAAI,CAACoC,EAAE,CAAC;QACjD,sBAAsB,EAAEyC,WAAW;QACnC,iBAAiB,EAAEb,QAAQ;QAC3B,iBAAiB,EAAE,IAAI;QACvB,eAAe,EAAE;MACrB,CAAC;IAAE,CAAC,EAAE5E,qDAAC,CAAC6H,OAAO,EAAE9B,MAAM,CAACC,MAAM,CAAC;MAAEgC,GAAG,EAAE;IAA2C,CAAC,EAAEF,KAAK,EAAE;MAAEI,KAAK,EAAE,eAAe;MAAEC,IAAI,EAAE,QAAQ;MAAEvD,QAAQ,EAAEA,QAAQ;MAAES,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEE,MAAM,EAAE,IAAI,CAACA;IAAO,CAAC,EAAEd,mBAAmB,CAAC,EAAEzE,qDAAC,CAAC,MAAM,EAAE;MAAEgI,GAAG,EAAE,0CAA0C;MAAEE,KAAK,EAAE;IAAe,CAAC,EAAElI,qDAAC,CAAC,MAAM,EAAE;MAAEgI,GAAG,EAAE,0CAA0C;MAAEvG,IAAI,EAAE,WAAW;MAAE2G,YAAY,EAAE,IAAI,CAAC5C;IAAY,CAAC,CAAC,EAAExF,qDAAC,CAAC,MAAM,EAAE;MAAEgI,GAAG,EAAE,0CAA0C;MAAEvG,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAEzB,qDAAC,CAAC,MAAM,EAAE;MAAEgI,GAAG,EAAE;IAA2C,CAAC,CAAC,EAAEhI,qDAAC,CAAC,MAAM,EAAE;MAAEgI,GAAG,EAAE,0CAA0C;MAAEvG,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,EAAEE,IAAI,KAAK,IAAI,IAAI3B,qDAAC,CAAC,mBAAmB,EAAE;MAAEgI,GAAG,EAAE,0CAA0C;MAAEjD,IAAI,EAAE,IAAI,CAAC6B;IAAW,CAAC,CAAC,CAAC,CAAC;EAChwB;EACA,IAAI5D,EAAEA,CAAA,EAAG;IAAE,OAAO5C,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWiI,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,cAAc,EAAE,CAAC,eAAe,CAAC;MACjC,YAAY,EAAE,CAAC,eAAe;IAClC,CAAC;EAAE;AACP,CAAC;AACDtE,MAAM,CAACuC,KAAK,GAAG;EACX1E,GAAG,EAAEiC,YAAY;EACjBhC,EAAE,EAAEiC;AACR,CAAC;AAED,MAAMwE,eAAe,GAAIC,UAAU,IAAK;EACtC,MAAMC,GAAG,GAAG5E,QAAQ,CAACyC,aAAa,CAAC,KAAK,CAAC;EACzCmC,GAAG,CAACC,SAAS,GAAGF,UAAU;EAC1B;EACA,KAAK,IAAIjI,CAAC,GAAGkI,GAAG,CAACE,UAAU,CAAC/F,MAAM,GAAG,CAAC,EAAErC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACnD,IAAIkI,GAAG,CAACE,UAAU,CAACpI,CAAC,CAAC,CAACqI,QAAQ,CAAC7F,WAAW,CAAC,CAAC,KAAK,KAAK,EAAE;MACtD0F,GAAG,CAACI,WAAW,CAACJ,GAAG,CAACE,UAAU,CAACpI,CAAC,CAAC,CAAC;IACpC;EACF;EACA;EACA,MAAMuI,MAAM,GAAGL,GAAG,CAACM,iBAAiB;EACpC,IAAID,MAAM,IAAIA,MAAM,CAACF,QAAQ,CAAC7F,WAAW,CAAC,CAAC,KAAK,KAAK,EAAE;IACrD,MAAMiG,QAAQ,GAAGF,MAAM,CAACtF,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE;IACnDsF,MAAM,CAACG,YAAY,CAAC,OAAO,EAAE,CAACD,QAAQ,GAAG,aAAa,EAAExG,IAAI,CAAC,CAAC,CAAC;IAC/D;IACA;IACA;IACA,IAAI0G,OAAO,CAACJ,MAAM,CAAC,EAAE;MACnB,OAAOL,GAAG,CAACC,SAAS;IACtB;EACF;EACA,OAAO,EAAE;AACX,CAAC;AACD,MAAMQ,OAAO,GAAIC,GAAG,IAAK;EACvB,IAAIA,GAAG,CAACC,QAAQ,KAAK,CAAC,EAAE;IACtB,IAAID,GAAG,CAACP,QAAQ,CAAC7F,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE;MAC3C,OAAO,KAAK;IACd;IACA,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4I,GAAG,CAACjG,UAAU,CAACN,MAAM,EAAErC,CAAC,EAAE,EAAE;MAC9C,MAAMmB,IAAI,GAAGyH,GAAG,CAACjG,UAAU,CAAC3C,CAAC,CAAC,CAACmB,IAAI;MACnC,IAAIa,KAAK,CAACb,IAAI,CAAC,IAAIA,IAAI,CAACqB,WAAW,CAAC,CAAC,CAACsG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QACzD,OAAO,KAAK;MACd;IACF;IACA,KAAK,IAAI9I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4I,GAAG,CAACR,UAAU,CAAC/F,MAAM,EAAErC,CAAC,EAAE,EAAE;MAC9C,IAAI,CAAC2I,OAAO,CAACC,GAAG,CAACR,UAAU,CAACpI,CAAC,CAAC,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;IACF;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,MAAM+I,YAAY,GAAIhI,GAAG,IAAKA,GAAG,CAACiI,UAAU,CAAC,oBAAoB,CAAC;AAClE,MAAMC,gBAAgB,GAAIlI,GAAG,IAAKA,GAAG,CAAC+H,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAE9D,MAAMI,cAAc,GAAG,IAAIxI,GAAG,CAAC,CAAC;AAChC,MAAMyI,QAAQ,GAAG,IAAIzI,GAAG,CAAC,CAAC;AAC1B,IAAI0I,MAAM;AACV,MAAMC,aAAa,GAAGA,CAACtI,GAAG,EAAEuI,QAAQ,KAAK;EACvC;EACA,IAAIC,GAAG,GAAGJ,QAAQ,CAACxH,GAAG,CAACZ,GAAG,CAAC;EAC3B,IAAI,CAACwI,GAAG,EAAE;IACR,IAAI,OAAOC,KAAK,KAAK,WAAW,IAAI,OAAOlG,QAAQ,KAAK,WAAW,EAAE;MACnE;AACN;AACA;AACA;MACM,IAAIyF,YAAY,CAAChI,GAAG,CAAC,IAAIkI,gBAAgB,CAAClI,GAAG,CAAC,EAAE;QAC9C,IAAI,CAACqI,MAAM,EAAE;UACX;AACV;AACA;AACA;UACUA,MAAM,GAAG,IAAIK,SAAS,CAAC,CAAC;QAC1B;QACA,MAAMC,GAAG,GAAGN,MAAM,CAACO,eAAe,CAAC5I,GAAG,EAAE,WAAW,CAAC;QACpD,MAAM6I,GAAG,GAAGF,GAAG,CAACrD,aAAa,CAAC,KAAK,CAAC;QACpC,IAAIuD,GAAG,EAAE;UACPV,cAAc,CAACW,GAAG,CAAC9I,GAAG,EAAE6I,GAAG,CAACE,SAAS,CAAC;QACxC;QACA,OAAOC,OAAO,CAACC,OAAO,CAAC,CAAC;MAC1B,CAAC,MACI;QACH;QACAT,GAAG,GAAGC,KAAK,CAACzI,GAAG,CAAC,CAACkJ,IAAI,CAAEC,GAAG,IAAK;UAC7B,IAAIA,GAAG,CAACC,EAAE,EAAE;YACV,OAAOD,GAAG,CAACE,IAAI,CAAC,CAAC,CAACH,IAAI,CAAEhC,UAAU,IAAK;cACrC,IAAIA,UAAU,IAAIqB,QAAQ,KAAK,KAAK,EAAE;gBACpCrB,UAAU,GAAGD,eAAe,CAACC,UAAU,CAAC;cAC1C;cACAiB,cAAc,CAACW,GAAG,CAAC9I,GAAG,EAAEkH,UAAU,IAAI,EAAE,CAAC;YAC3C,CAAC,CAAC;UACJ;UACAiB,cAAc,CAACW,GAAG,CAAC9I,GAAG,EAAE,EAAE,CAAC;QAC7B,CAAC,CAAC;QACF;QACAoI,QAAQ,CAACU,GAAG,CAAC9I,GAAG,EAAEwI,GAAG,CAAC;MACxB;IACF,CAAC,MACI;MACH;MACAL,cAAc,CAACW,GAAG,CAAC9I,GAAG,EAAE,EAAE,CAAC;MAC3B,OAAOgJ,OAAO,CAACC,OAAO,CAAC,CAAC;IAC1B;EACF;EACA,OAAOT,GAAG;AACZ,CAAC;AAED,MAAMc,OAAO,GAAG,wwDAAwwD;AAExxD,MAAMC,IAAI,GAAG,MAAM;EACf5G,WAAWA,CAACC,OAAO,EAAE;IACjB1E,qDAAgB,CAAC,IAAI,EAAE0E,OAAO,CAAC;IAC/B,IAAI,CAAClC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC0C,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACoG,WAAW,GAAG,KAAK;IACxB,IAAI,CAACtC,UAAU,GAAGxB,SAAS;IAC3B,IAAI,CAAC+D,SAAS,GAAG,KAAK;IACtB,IAAI,CAACnJ,IAAI,GAAGoJ,UAAU,CAAC,CAAC;IACxB,IAAI,CAACtD,KAAK,GAAGV,SAAS;IACtB,IAAI,CAACnF,GAAG,GAAGmF,SAAS;IACpB,IAAI,CAAClF,EAAE,GAAGkF,SAAS;IACnB,IAAI,CAACiE,OAAO,GAAGjE,SAAS;IACxB,IAAI,CAACtF,IAAI,GAAGsF,SAAS;IACrB,IAAI,CAACxF,GAAG,GAAGwF,SAAS;IACpB,IAAI,CAACrF,IAAI,GAAGqF,SAAS;IACrB,IAAI,CAACS,IAAI,GAAGT,SAAS;IACrB,IAAI,CAACkE,IAAI,GAAG,KAAK;IACjB,IAAI,CAACrB,QAAQ,GAAG,IAAI;EACxB;EACAnD,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAChC,mBAAmB,GAAG1B,iBAAiB,CAAC,IAAI,CAACC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC;EACzE;EACAkI,iBAAiBA,CAAA,EAAG;IAChB;IACA;IACA;IACA,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACnI,EAAE,EAAE,MAAM,EAAE,MAAM;MACzC,IAAI,CAAC8H,SAAS,GAAG,IAAI;MACrB,IAAI,CAACM,QAAQ,CAAC,CAAC;IACnB,CAAC,CAAC;EACN;EACAC,gBAAgBA,CAAA,EAAG;IACf;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC,IAAI,CAACR,WAAW,EAAE;MACnB,IAAI,CAACO,QAAQ,CAAC,CAAC;IACnB;EACJ;EACAE,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACC,EAAE,EAAE;MACT,IAAI,CAACA,EAAE,CAACC,UAAU,CAAC,CAAC;MACpB,IAAI,CAACD,EAAE,GAAGxE,SAAS;IACvB;EACJ;EACAoE,gBAAgBA,CAACnI,EAAE,EAAEyI,UAAU,EAAEC,EAAE,EAAE;IACjC,IAAI,IAAI,CAACT,IAAI,IAAI,OAAOlK,MAAM,KAAK,WAAW,IAAIA,MAAM,CAAC4K,oBAAoB,EAAE;MAC3E,MAAMJ,EAAE,GAAI,IAAI,CAACA,EAAE,GAAG,IAAIxK,MAAM,CAAC4K,oBAAoB,CAAEC,IAAI,IAAK;QAC5D,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACC,cAAc,EAAE;UACxBN,EAAE,CAACC,UAAU,CAAC,CAAC;UACf,IAAI,CAACD,EAAE,GAAGxE,SAAS;UACnB2E,EAAE,CAAC,CAAC;QACR;MACJ,CAAC,EAAE;QAAED;MAAW,CAAC,CAAE;MACnBF,EAAE,CAACO,OAAO,CAAC9I,EAAE,CAAC;IAClB,CAAC,MACI;MACD;MACA;MACA0I,EAAE,CAAC,CAAC;IACR;EACJ;EACAN,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACN,SAAS,EAAE;MAChB,MAAMzJ,GAAG,GAAGD,MAAM,CAAC,IAAI,CAAC;MACxB,IAAIC,GAAG,EAAE;QACL,IAAImI,cAAc,CAACuC,GAAG,CAAC1K,GAAG,CAAC,EAAE;UACzB;UACA,IAAI,CAACkH,UAAU,GAAGiB,cAAc,CAACvH,GAAG,CAACZ,GAAG,CAAC;QAC7C,CAAC,MACI;UACD;UACAsI,aAAa,CAACtI,GAAG,EAAE,IAAI,CAACuI,QAAQ,CAAC,CAACW,IAAI,CAAC,MAAO,IAAI,CAAChC,UAAU,GAAGiB,cAAc,CAACvH,GAAG,CAACZ,GAAG,CAAE,CAAC;QAC7F;QACA,IAAI,CAACwJ,WAAW,GAAG,IAAI;MAC3B;IACJ;IACA,IAAI,CAAC9I,QAAQ,GAAGP,OAAO,CAAC,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,EAAE,CAAC;EAC/E;EACAwF,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE2D,OAAO;MAAEjJ,QAAQ;MAAE0C,mBAAmB;MAAEzB;IAAG,CAAC,GAAG,IAAI;IAC3D,MAAMrB,IAAI,GAAG,IAAI,CAACA,IAAI,IAAI,IAAI;IAC9B;IACA,MAAMqK,cAAc,GAAGjK,QAAQ,GACzB,CAACA,QAAQ,CAACkK,QAAQ,CAAC,OAAO,CAAC,IAAIlK,QAAQ,CAACkK,QAAQ,CAAC,SAAS,CAAC,KAAKjB,OAAO,KAAK,KAAK,GACjF,KAAK;IACX;IACA,MAAMkB,iBAAiB,GAAGlB,OAAO,IAAIgB,cAAc;IACnD,OAAQhM,qDAAC,CAACE,iDAAI,EAAE6F,MAAM,CAACC,MAAM,CAAC;MAAEmG,IAAI,EAAE,KAAK;MAAEjE,KAAK,EAAEnC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAE,CAACrE,IAAI,GAAG;MAAK,CAAC,EAAEyK,kBAAkB,CAAC,IAAI,CAAC3E,KAAK,CAAC,CAAC,EAAE;QAAE,CAAC,QAAQ,IAAI,CAACD,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,CAACA,IAAI;QAAE,UAAU,EAAE0E,iBAAiB;QAAE,UAAU,EAAEA,iBAAiB,IAAIzI,KAAK,CAACT,EAAE;MAAE,CAAC;IAAE,CAAC,EAAEyB,mBAAmB,CAAC,EAAE,IAAI,CAAC8D,UAAU,GAAIvI,qDAAC,CAAC,KAAK,EAAE;MAAEkI,KAAK,EAAE,YAAY;MAAEO,SAAS,EAAE,IAAI,CAACF;IAAW,CAAC,CAAC,GAAKvI,qDAAC,CAAC,KAAK,EAAE;MAAEkI,KAAK,EAAE;IAAa,CAAC,CAAE,CAAC;EAC3Y;EACA,WAAWmE,UAAUA,CAAA,EAAG;IAAE,OAAO,CAAC,KAAK,CAAC;EAAE;EAC1C,IAAIrJ,EAAEA,CAAA,EAAG;IAAE,OAAO5C,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWiI,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,MAAM,EAAE,CAAC,UAAU,CAAC;MACpB,KAAK,EAAE,CAAC,UAAU,CAAC;MACnB,MAAM,EAAE,CAAC,UAAU,CAAC;MACpB,KAAK,EAAE,CAAC,UAAU,CAAC;MACnB,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE;AACP,CAAC;AACD,MAAM0C,UAAU,GAAGA,CAAA,KAAO,OAAOnH,QAAQ,KAAK,WAAW,IAAIA,QAAQ,CAAC0I,eAAe,CAAC/I,YAAY,CAAC,MAAM,CAAC,IAAK,IAAI;AACnH,MAAM6I,kBAAkB,GAAI3E,KAAK,IAAK;EAClC,OAAOA,KAAK,GACN;IACE,WAAW,EAAE,IAAI;IACjB,CAAC,aAAaA,KAAK,EAAE,GAAG;EAC5B,CAAC,GACC,IAAI;AACd,CAAC;AACDmD,IAAI,CAACtE,KAAK,GAAGqE,OAAO", "sources": ["./node_modules/@ionic/core/dist/esm/ion-button_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { q as getAssetPath, r as registerInstance, d as createEvent, n as forceUpdate, m as printIonWarning, e as getIonMode$1, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { k as hasShadowDom, i as inheritAriaAttributes } from './helpers-1O4D2b7y.js';\nimport { o as openURL, c as createColorClasses$1, h as hostContext } from './theme-DiVJyqlX.js';\n\nlet CACHED_MAP;\nconst getIconMap = () => {\n  if (typeof window === 'undefined') {\n    return new Map();\n  }\n  else {\n    if (!CACHED_MAP) {\n      const win = window;\n      win.Ionicons = win.Ionicons || {};\n      CACHED_MAP = win.Ionicons.map = win.Ionicons.map || new Map();\n    }\n    return CACHED_MAP;\n  }\n};\nconst getUrl = (i) => {\n  let url = getSrc(i.src);\n  if (url) {\n    return url;\n  }\n  url = getName(i.name, i.icon, i.mode, i.ios, i.md);\n  if (url) {\n    return getNamedUrl(url, i);\n  }\n  if (i.icon) {\n    url = getSrc(i.icon);\n    if (url) {\n      return url;\n    }\n    url = getSrc(i.icon[i.mode]);\n    if (url) {\n      return url;\n    }\n  }\n  return null;\n};\nconst getNamedUrl = (iconName, iconEl) => {\n  const url = getIconMap().get(iconName);\n  if (url) {\n    return url;\n  }\n  try {\n    return getAssetPath(`svg/${iconName}.svg`);\n  }\n  catch (e) {\n    /**\n     * In the custom elements build version of ionicons, referencing an icon\n     * by name will throw an invalid URL error because the asset path is not defined.\n     * This catches that error and logs something that is more developer-friendly.\n     * We also include a reference to the ion-icon element so developers can\n     * figure out which instance of ion-icon needs to be updated.\n     */\n    console.warn(`[Ionicons Warning]: Could not load icon with name \"${iconName}\". Ensure that the icon is registered using addIcons or that the icon SVG data is passed directly to the icon component.`, iconEl);\n  }\n};\nconst getName = (iconName, icon, mode, ios, md) => {\n  // default to \"md\" if somehow the mode wasn't set\n  mode = (mode && toLower(mode)) === 'ios' ? 'ios' : 'md';\n  // if an icon was passed in using the ios or md attributes\n  // set the iconName to whatever was passed in\n  if (ios && mode === 'ios') {\n    iconName = toLower(ios);\n  }\n  else if (md && mode === 'md') {\n    iconName = toLower(md);\n  }\n  else {\n    if (!iconName && icon && !isSrc(icon)) {\n      iconName = icon;\n    }\n    if (isStr(iconName)) {\n      iconName = toLower(iconName);\n    }\n  }\n  if (!isStr(iconName) || iconName.trim() === '') {\n    return null;\n  }\n  // only allow alpha characters and dash\n  const invalidChars = iconName.replace(/[a-z]|-|\\d/gi, '');\n  if (invalidChars !== '') {\n    return null;\n  }\n  return iconName;\n};\nconst getSrc = (src) => {\n  if (isStr(src)) {\n    src = src.trim();\n    if (isSrc(src)) {\n      return src;\n    }\n  }\n  return null;\n};\nconst isSrc = (str) => str.length > 0 && /(\\/|\\.)/.test(str);\nconst isStr = (val) => typeof val === 'string';\nconst toLower = (val) => val.toLowerCase();\n/**\n * Elements inside of web components sometimes need to inherit global attributes\n * set on the host. For example, the inner input in `ion-input` should inherit\n * the `title` attribute that developers set directly on `ion-input`. This\n * helper function should be called in componentWillLoad and assigned to a variable\n * that is later used in the render function.\n *\n * This does not need to be reactive as changing attributes on the host element\n * does not trigger a re-render.\n */\nconst inheritAttributes = (el, attributes = []) => {\n  const attributeObject = {};\n  attributes.forEach(attr => {\n    if (el.hasAttribute(attr)) {\n      const value = el.getAttribute(attr);\n      if (value !== null) {\n        attributeObject[attr] = el.getAttribute(attr);\n      }\n      el.removeAttribute(attr);\n    }\n  });\n  return attributeObject;\n};\n/**\n * Returns `true` if the document or host element\n * has a `dir` set to `rtl`. The host value will always\n * take priority over the root document value.\n */\nconst isRTL = (hostEl) => {\n  if (hostEl) {\n    if (hostEl.dir !== '') {\n      return hostEl.dir.toLowerCase() === 'rtl';\n    }\n  }\n  return (document === null || document === void 0 ? void 0 : document.dir.toLowerCase()) === 'rtl';\n};\n\nconst buttonIosCss = \":host{--overflow:hidden;--ripple-color:currentColor;--border-width:initial;--border-color:initial;--border-style:initial;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--box-shadow:none;display:inline-block;width:auto;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;white-space:normal;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;vertical-align:top;vertical-align:-webkit-baseline-middle;-webkit-font-kerning:none;font-kerning:none}:host(.button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.button-solid){--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff)}:host(.button-outline){--border-color:var(--ion-color-primary, #0054e9);--background:transparent;--color:var(--ion-color-primary, #0054e9)}:host(.button-clear){--border-width:0;--background:transparent;--color:var(--ion-color-primary, #0054e9)}:host(.button-block){display:block}:host(.button-block) .button-native{margin-left:0;margin-right:0;width:100%;clear:both;contain:content}:host(.button-block) .button-native::after{clear:both}:host(.button-full){display:block}:host(.button-full) .button-native{margin-left:0;margin-right:0;width:100%;contain:content}:host(.button-full:not(.button-round)) .button-native{border-radius:0;border-right-width:0;border-left-width:0}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);line-height:1;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);contain:layout style;cursor:pointer;opacity:var(--opacity);overflow:var(--overflow);z-index:0;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-native::-moz-focus-inner{border:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted(ion-icon){font-size:1.35em;pointer-events:none}::slotted(ion-icon[slot=start]){-webkit-margin-start:-0.3em;margin-inline-start:-0.3em;-webkit-margin-end:0.3em;margin-inline-end:0.3em;margin-top:0;margin-bottom:0}::slotted(ion-icon[slot=end]){-webkit-margin-start:0.3em;margin-inline-start:0.3em;-webkit-margin-end:-0.2em;margin-inline-end:-0.2em;margin-top:0;margin-bottom:0}ion-ripple-effect{color:var(--ripple-color)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){:host(:hover){color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-activated){color:var(--color-activated)}:host(.ion-activated) .button-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.button-solid.ion-color) .button-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.button-outline.ion-color) .button-native{border-color:var(--ion-color-base);background:transparent;color:var(--ion-color-base)}:host(.button-clear.ion-color) .button-native{background:transparent;color:var(--ion-color-base)}:host(.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{color:var(--ion-toolbar-color, var(--color))}:host(.button-outline.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{border-color:var(--ion-toolbar-color, var(--color, var(--border-color)))}:host(.button-solid.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-color, var(--background));color:var(--ion-toolbar-background, var(--color))}:host{--border-radius:14px;--padding-top:13px;--padding-bottom:13px;--padding-start:1em;--padding-end:1em;--transition:background-color, opacity 100ms linear;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:4px;margin-bottom:4px;min-height:3.1em;font-size:min(1rem, 48px);font-weight:500;letter-spacing:0}:host(.button-solid){--background-activated:var(--ion-color-primary-shade, #004acd);--background-focused:var(--ion-color-primary-shade, #004acd);--background-hover:var(--ion-color-primary-tint, #1a65eb);--background-activated-opacity:1;--background-focused-opacity:1;--background-hover-opacity:1}:host(.button-outline){--border-radius:14px;--border-width:1px;--border-style:solid;--background-activated:var(--ion-color-primary, #0054e9);--background-focused:var(--ion-color-primary, #0054e9);--background-hover:transparent;--background-focused-opacity:.1;--color-activated:var(--ion-color-primary-contrast, #fff)}:host(.button-clear){--background-activated:transparent;--background-activated-opacity:0;--background-focused:var(--ion-color-primary, #0054e9);--background-hover:transparent;--background-focused-opacity:.1;font-size:min(1.0625rem, 51px);font-weight:normal}:host(.in-buttons){font-size:clamp(17px, 1.0625rem, 21.08px);font-weight:400}:host(.button-large){--border-radius:16px;--padding-top:17px;--padding-start:1em;--padding-end:1em;--padding-bottom:17px;min-height:3.1em;font-size:min(1.25rem, 60px)}:host(.button-small){--border-radius:6px;--padding-top:4px;--padding-start:0.9em;--padding-end:0.9em;--padding-bottom:4px;min-height:2.1em;font-size:min(0.8125rem, 39px)}:host(.button-round){--border-radius:999px;--padding-top:0;--padding-start:26px;--padding-end:26px;--padding-bottom:0}:host(.button-strong){font-weight:600}:host(.button-has-icon-only){--padding-top:0;--padding-bottom:var(--padding-top);--padding-end:var(--padding-top);--padding-start:var(--padding-end);min-width:clamp(30px, 2.125em, 60px);min-height:clamp(30px, 2.125em, 60px)}::slotted(ion-icon[slot=icon-only]){font-size:clamp(15.12px, 1.125em, 43.02px)}:host(.button-small.button-has-icon-only){min-width:clamp(23px, 2.16em, 54px);min-height:clamp(23px, 2.16em, 54px)}:host(.button-small) ::slotted(ion-icon[slot=icon-only]){font-size:clamp(12.1394px, 1.308125em, 40.1856px)}:host(.button-large.button-has-icon-only){min-width:clamp(46px, 2.5em, 78px);min-height:clamp(46px, 2.5em, 78px)}:host(.button-large) ::slotted(ion-icon[slot=icon-only]){font-size:clamp(15.12px, 0.9em, 43.056px)}:host(.button-outline.ion-focused.ion-color) .button-native,:host(.button-clear.ion-focused.ion-color) .button-native{color:var(--ion-color-base)}:host(.button-outline.ion-focused.ion-color) .button-native::after,:host(.button-clear.ion-focused.ion-color) .button-native::after{background:var(--ion-color-base)}:host(.button-solid.ion-color.ion-focused) .button-native::after{background:var(--ion-color-shade)}@media (any-hover: hover){:host(.button-clear:not(.ion-activated):hover),:host(.button-outline:not(.ion-activated):hover){opacity:0.6}:host(.button-clear.ion-color:hover) .button-native,:host(.button-outline.ion-color:hover) .button-native{color:var(--ion-color-base)}:host(.button-clear.ion-color:hover) .button-native::after,:host(.button-outline.ion-color:hover) .button-native::after{background:transparent}:host(.button-solid.ion-color:hover) .button-native::after{background:var(--ion-color-tint)}:host(:hover.button-solid.in-toolbar:not(.ion-color):not(.in-toolbar-color):not(.ion-activated)) .button-native::after{background:#fff;opacity:0.1}}:host(.button-clear.ion-activated){opacity:0.4}:host(.button-outline.ion-activated.ion-color) .button-native{color:var(--ion-color-contrast)}:host(.button-outline.ion-activated.ion-color) .button-native::after{background:var(--ion-color-base)}:host(.button-solid.ion-color.ion-activated) .button-native::after{background:var(--ion-color-shade)}:host(.button-outline.ion-activated.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-color, var(--color));color:var(--ion-toolbar-background, var(--background), var(--ion-color-primary-contrast, #fff))}\";\n\nconst buttonMdCss = \":host{--overflow:hidden;--ripple-color:currentColor;--border-width:initial;--border-color:initial;--border-style:initial;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--box-shadow:none;display:inline-block;width:auto;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;white-space:normal;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;vertical-align:top;vertical-align:-webkit-baseline-middle;-webkit-font-kerning:none;font-kerning:none}:host(.button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.button-solid){--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff)}:host(.button-outline){--border-color:var(--ion-color-primary, #0054e9);--background:transparent;--color:var(--ion-color-primary, #0054e9)}:host(.button-clear){--border-width:0;--background:transparent;--color:var(--ion-color-primary, #0054e9)}:host(.button-block){display:block}:host(.button-block) .button-native{margin-left:0;margin-right:0;width:100%;clear:both;contain:content}:host(.button-block) .button-native::after{clear:both}:host(.button-full){display:block}:host(.button-full) .button-native{margin-left:0;margin-right:0;width:100%;contain:content}:host(.button-full:not(.button-round)) .button-native{border-radius:0;border-right-width:0;border-left-width:0}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);line-height:1;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);contain:layout style;cursor:pointer;opacity:var(--opacity);overflow:var(--overflow);z-index:0;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-native::-moz-focus-inner{border:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted(ion-icon){font-size:1.35em;pointer-events:none}::slotted(ion-icon[slot=start]){-webkit-margin-start:-0.3em;margin-inline-start:-0.3em;-webkit-margin-end:0.3em;margin-inline-end:0.3em;margin-top:0;margin-bottom:0}::slotted(ion-icon[slot=end]){-webkit-margin-start:0.3em;margin-inline-start:0.3em;-webkit-margin-end:-0.2em;margin-inline-end:-0.2em;margin-top:0;margin-bottom:0}ion-ripple-effect{color:var(--ripple-color)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){:host(:hover){color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-activated){color:var(--color-activated)}:host(.ion-activated) .button-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.button-solid.ion-color) .button-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.button-outline.ion-color) .button-native{border-color:var(--ion-color-base);background:transparent;color:var(--ion-color-base)}:host(.button-clear.ion-color) .button-native{background:transparent;color:var(--ion-color-base)}:host(.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{color:var(--ion-toolbar-color, var(--color))}:host(.button-outline.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{border-color:var(--ion-toolbar-color, var(--color, var(--border-color)))}:host(.button-solid.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-color, var(--background));color:var(--ion-toolbar-background, var(--color))}:host{--border-radius:4px;--padding-top:8px;--padding-bottom:8px;--padding-start:1.1em;--padding-end:1.1em;--transition:box-shadow 280ms cubic-bezier(.4, 0, .2, 1),\\n                background-color 15ms linear,\\n                color 15ms linear;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:4px;margin-bottom:4px;min-height:36px;font-size:0.875rem;font-weight:500;letter-spacing:0.06em;text-transform:uppercase}:host(.button-solid){--background-activated:transparent;--background-hover:var(--ion-color-primary-contrast, #fff);--background-focused:var(--ion-color-primary-contrast, #fff);--background-activated-opacity:0;--background-focused-opacity:.24;--background-hover-opacity:.08;--box-shadow:0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12)}:host(.button-solid.ion-activated){--box-shadow:0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12)}:host(.button-outline){--border-width:2px;--border-style:solid;--box-shadow:none;--background-activated:transparent;--background-focused:var(--ion-color-primary, #0054e9);--background-hover:var(--ion-color-primary, #0054e9);--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04}:host(.button-outline.ion-activated.ion-color) .button-native{background:transparent}:host(.button-clear){--background-activated:transparent;--background-focused:var(--ion-color-primary, #0054e9);--background-hover:var(--ion-color-primary, #0054e9);--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04}:host(.button-round){--border-radius:999px;--padding-top:0;--padding-start:26px;--padding-end:26px;--padding-bottom:0}:host(.button-large){--padding-top:14px;--padding-start:1em;--padding-end:1em;--padding-bottom:14px;min-height:2.8em;font-size:1.25rem}:host(.button-small){--padding-top:4px;--padding-start:0.9em;--padding-end:0.9em;--padding-bottom:4px;min-height:2.1em;font-size:0.8125rem}:host(.button-strong){font-weight:bold}:host(.button-has-icon-only){--padding-top:0;--padding-bottom:var(--padding-top);--padding-end:var(--padding-top);--padding-start:var(--padding-end);min-width:clamp(30px, 2.86em, 60px);min-height:clamp(30px, 2.86em, 60px)}::slotted(ion-icon[slot=icon-only]){font-size:clamp(15.104px, 1.6em, 43.008px)}:host(.button-small.button-has-icon-only){min-width:clamp(23px, 2.16em, 54px);min-height:clamp(23px, 2.16em, 54px)}:host(.button-small) ::slotted(ion-icon[slot=icon-only]){font-size:clamp(13.002px, 1.23125em, 40.385px)}:host(.button-large.button-has-icon-only){min-width:clamp(46px, 2.5em, 78px);min-height:clamp(46px, 2.5em, 78px)}:host(.button-large) ::slotted(ion-icon[slot=icon-only]){font-size:clamp(15.008px, 1.4em, 43.008px)}:host(.button-solid.ion-color.ion-focused) .button-native::after{background:var(--ion-color-contrast)}:host(.button-clear.ion-color.ion-focused) .button-native::after,:host(.button-outline.ion-color.ion-focused) .button-native::after{background:var(--ion-color-base)}@media (any-hover: hover){:host(.button-solid.ion-color:hover) .button-native::after{background:var(--ion-color-contrast)}:host(.button-clear.ion-color:hover) .button-native::after,:host(.button-outline.ion-color:hover) .button-native::after{background:var(--ion-color-base)}}:host(.button-outline.ion-activated.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-background, var(--color));color:var(--ion-toolbar-color, var(--background), var(--ion-color-primary-contrast, #fff))}\";\n\nconst Button = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.inItem = false;\n        this.inListHeader = false;\n        this.inToolbar = false;\n        this.formButtonEl = null;\n        this.formEl = null;\n        this.inheritedAttributes = {};\n        /**\n         * If `true`, the button only has an icon.\n         */\n        this.isCircle = false;\n        /**\n         * The type of button.\n         */\n        this.buttonType = 'button';\n        /**\n         * If `true`, the user cannot interact with the button.\n         */\n        this.disabled = false;\n        /**\n         * When using a router, it specifies the transition direction when navigating to\n         * another page using `href`.\n         */\n        this.routerDirection = 'forward';\n        /**\n         * If `true`, activates a button with a heavier font weight.\n         */\n        this.strong = false;\n        /**\n         * The type of the button.\n         */\n        this.type = 'button';\n        this.handleClick = (ev) => {\n            const { el } = this;\n            if (this.type === 'button') {\n                openURL(this.href, ev, this.routerDirection, this.routerAnimation);\n            }\n            else if (hasShadowDom(el)) {\n                this.submitForm(ev);\n            }\n        };\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.slotChanged = () => {\n            /**\n             * Ensures that the 'has-icon-only' class is properly added\n             * or removed from `ion-button` when manipulating the\n             * `icon-only` slot.\n             *\n             * Without this, the 'has-icon-only' class is only checked\n             * or added when `ion-button` component first renders.\n             */\n            this.isCircle = this.hasIconOnly;\n        };\n    }\n    disabledChanged() {\n        const { disabled } = this;\n        if (this.formButtonEl) {\n            this.formButtonEl.disabled = disabled;\n        }\n    }\n    /**\n     * This component is used within the `ion-input-password-toggle` component\n     * to toggle the visibility of the password input.\n     * These attributes need to update based on the state of the password input.\n     * Otherwise, the values will be stale.\n     *\n     * @param newValue\n     * @param _oldValue\n     * @param propName\n     */\n    onAriaChanged(newValue, _oldValue, propName) {\n        this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), { [propName]: newValue });\n        forceUpdate(this);\n    }\n    /**\n     * This is responsible for rendering a hidden native\n     * button element inside the associated form. This allows\n     * users to submit a form by pressing \"Enter\" when a text\n     * field inside of the form is focused. The native button\n     * rendered inside of `ion-button` is in the Shadow DOM\n     * and therefore does not participate in form submission\n     * which is why the following code is necessary.\n     */\n    renderHiddenButton() {\n        const formEl = (this.formEl = this.findForm());\n        if (formEl) {\n            const { formButtonEl } = this;\n            /**\n             * If the form already has a rendered form button\n             * then do not append a new one again.\n             */\n            if (formButtonEl !== null && formEl.contains(formButtonEl)) {\n                return;\n            }\n            // Create a hidden native button inside of the form\n            const newFormButtonEl = (this.formButtonEl = document.createElement('button'));\n            newFormButtonEl.type = this.type;\n            newFormButtonEl.style.display = 'none';\n            // Only submit if the button is not disabled.\n            newFormButtonEl.disabled = this.disabled;\n            formEl.appendChild(newFormButtonEl);\n        }\n    }\n    componentWillLoad() {\n        this.inToolbar = !!this.el.closest('ion-buttons');\n        this.inListHeader = !!this.el.closest('ion-list-header');\n        this.inItem = !!this.el.closest('ion-item') || !!this.el.closest('ion-item-divider');\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    get hasIconOnly() {\n        return !!this.el.querySelector('[slot=\"icon-only\"]');\n    }\n    get rippleType() {\n        const hasClearFill = this.fill === undefined || this.fill === 'clear';\n        // If the button is in a toolbar, has a clear fill (which is the default)\n        // and only has an icon we use the unbounded \"circular\" ripple effect\n        if (hasClearFill && this.hasIconOnly && this.inToolbar) {\n            return 'unbounded';\n        }\n        return 'bounded';\n    }\n    /**\n     * Finds the form element based on the provided `form` selector\n     * or element reference provided.\n     */\n    findForm() {\n        const { form } = this;\n        if (form instanceof HTMLFormElement) {\n            return form;\n        }\n        if (typeof form === 'string') {\n            // Check if the string provided is a form id.\n            const el = document.getElementById(form);\n            if (el) {\n                if (el instanceof HTMLFormElement) {\n                    return el;\n                }\n                else {\n                    /**\n                     * The developer specified a string for the form attribute, but the\n                     * element with that id is not a form element.\n                     */\n                    printIonWarning(`[ion-button] - Form with selector: \"#${form}\" could not be found. Verify that the id is attached to a <form> element.`, this.el);\n                    return null;\n                }\n            }\n            else {\n                /**\n                 * The developer specified a string for the form attribute, but the\n                 * element with that id could not be found in the DOM.\n                 */\n                printIonWarning(`[ion-button] - Form with selector: \"#${form}\" could not be found. Verify that the id is correct and the form is rendered in the DOM.`, this.el);\n                return null;\n            }\n        }\n        if (form !== undefined) {\n            /**\n             * The developer specified a HTMLElement for the form attribute,\n             * but the element is not a HTMLFormElement.\n             * This will also catch if the developer tries to pass in null\n             * as the form attribute.\n             */\n            printIonWarning(`[ion-button] - The provided \"form\" element is invalid. Verify that the form is a HTMLFormElement and rendered in the DOM.`, this.el);\n            return null;\n        }\n        /**\n         * If the form element is not set, the button may be inside\n         * of a form element. Query the closest form element to the button.\n         */\n        return this.el.closest('form');\n    }\n    submitForm(ev) {\n        // this button wants to specifically submit a form\n        // climb up the dom to see if we're in a <form>\n        // and if so, then use JS to submit it\n        if (this.formEl && this.formButtonEl) {\n            ev.preventDefault();\n            this.formButtonEl.click();\n        }\n    }\n    render() {\n        const mode = getIonMode$1(this);\n        const { buttonType, type, disabled, rel, target, size, href, color, expand, hasIconOnly, shape, strong, inheritedAttributes, } = this;\n        const finalSize = size === undefined && this.inItem ? 'small' : size;\n        const TagType = href === undefined ? 'button' : 'a';\n        const attrs = TagType === 'button'\n            ? { type }\n            : {\n                download: this.download,\n                href,\n                rel,\n                target,\n            };\n        let fill = this.fill;\n        /**\n         * We check both undefined and null to\n         * work around https://github.com/ionic-team/stencil/issues/3586.\n         */\n        if (fill == null) {\n            fill = this.inToolbar || this.inListHeader ? 'clear' : 'solid';\n        }\n        /**\n         * We call renderHiddenButton in the render function to account\n         * for any properties being set async. For example, changing the\n         * \"type\" prop from \"button\" to \"submit\" after the component has\n         * loaded would warrant the hidden button being added to the\n         * associated form.\n         */\n        {\n            type !== 'button' && this.renderHiddenButton();\n        }\n        return (h(Host, { key: 'b105ad09215adb3ca2298acdadf0dc9154bbb9b0', onClick: this.handleClick, \"aria-disabled\": disabled ? 'true' : null, class: createColorClasses$1(color, {\n                [mode]: true,\n                [buttonType]: true,\n                [`${buttonType}-${expand}`]: expand !== undefined,\n                [`${buttonType}-${finalSize}`]: finalSize !== undefined,\n                [`${buttonType}-${shape}`]: shape !== undefined,\n                [`${buttonType}-${fill}`]: true,\n                [`${buttonType}-strong`]: strong,\n                'in-toolbar': hostContext('ion-toolbar', this.el),\n                'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n                'in-buttons': hostContext('ion-buttons', this.el),\n                'button-has-icon-only': hasIconOnly,\n                'button-disabled': disabled,\n                'ion-activatable': true,\n                'ion-focusable': true,\n            }) }, h(TagType, Object.assign({ key: '66b4e7112bcb9e41d5a723fbbadb0a3104f9ee1d' }, attrs, { class: \"button-native\", part: \"native\", disabled: disabled, onFocus: this.onFocus, onBlur: this.onBlur }, inheritedAttributes), h(\"span\", { key: '1439fc3da280221028dcf7ce8ec9dab273c4d4bb', class: \"button-inner\" }, h(\"slot\", { key: 'd5269ae1afc87ec7b99746032f59cbae93720a9f', name: \"icon-only\", onSlotchange: this.slotChanged }), h(\"slot\", { key: '461c83e97aa246aa86d83e14f1e15a288d35041e', name: \"start\" }), h(\"slot\", { key: '807170d47101f9f6a333dd4ff489c89284f306fe' }), h(\"slot\", { key: 'e67f116dd0349a0d27893e4f3ff0ccef1d402f80', name: \"end\" })), mode === 'md' && h(\"ion-ripple-effect\", { key: '273f0bd9645a36c1bfd18a5c2ab4f81e22b7b989', type: this.rippleType }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"disabled\": [\"disabledChanged\"],\n        \"aria-checked\": [\"onAriaChanged\"],\n        \"aria-label\": [\"onAriaChanged\"]\n    }; }\n};\nButton.style = {\n    ios: buttonIosCss,\n    md: buttonMdCss\n};\n\nconst validateContent = (svgContent) => {\n  const div = document.createElement('div');\n  div.innerHTML = svgContent;\n  // setup this way to ensure it works on our buddy IE\n  for (let i = div.childNodes.length - 1; i >= 0; i--) {\n    if (div.childNodes[i].nodeName.toLowerCase() !== 'svg') {\n      div.removeChild(div.childNodes[i]);\n    }\n  }\n  // must only have 1 root element\n  const svgElm = div.firstElementChild;\n  if (svgElm && svgElm.nodeName.toLowerCase() === 'svg') {\n    const svgClass = svgElm.getAttribute('class') || '';\n    svgElm.setAttribute('class', (svgClass + ' s-ion-icon').trim());\n    // root element must be an svg\n    // lets double check we've got valid elements\n    // do not allow scripts\n    if (isValid(svgElm)) {\n      return div.innerHTML;\n    }\n  }\n  return '';\n};\nconst isValid = (elm) => {\n  if (elm.nodeType === 1) {\n    if (elm.nodeName.toLowerCase() === 'script') {\n      return false;\n    }\n    for (let i = 0; i < elm.attributes.length; i++) {\n      const name = elm.attributes[i].name;\n      if (isStr(name) && name.toLowerCase().indexOf('on') === 0) {\n        return false;\n      }\n    }\n    for (let i = 0; i < elm.childNodes.length; i++) {\n      if (!isValid(elm.childNodes[i])) {\n        return false;\n      }\n    }\n  }\n  return true;\n};\nconst isSvgDataUrl = (url) => url.startsWith('data:image/svg+xml');\nconst isEncodedDataUrl = (url) => url.indexOf(';utf8,') !== -1;\n\nconst ioniconContent = new Map();\nconst requests = new Map();\nlet parser;\nconst getSvgContent = (url, sanitize) => {\n  // see if we already have a request for this url\n  let req = requests.get(url);\n  if (!req) {\n    if (typeof fetch !== 'undefined' && typeof document !== 'undefined') {\n      /**\n       * If the url is a data url of an svg, then try to parse it\n       * with the DOMParser. This works with content security policies enabled.\n       */\n      if (isSvgDataUrl(url) && isEncodedDataUrl(url)) {\n        if (!parser) {\n          /**\n           * Create an instance of the DOM parser. This creates a single\n           * parser instance for the entire app, which is more efficient.\n           */\n          parser = new DOMParser();\n        }\n        const doc = parser.parseFromString(url, 'text/html');\n        const svg = doc.querySelector('svg');\n        if (svg) {\n          ioniconContent.set(url, svg.outerHTML);\n        }\n        return Promise.resolve();\n      }\n      else {\n        // we don't already have a request\n        req = fetch(url).then((rsp) => {\n          if (rsp.ok) {\n            return rsp.text().then((svgContent) => {\n              if (svgContent && sanitize !== false) {\n                svgContent = validateContent(svgContent);\n              }\n              ioniconContent.set(url, svgContent || '');\n            });\n          }\n          ioniconContent.set(url, '');\n        });\n        // cache for the same requests\n        requests.set(url, req);\n      }\n    }\n    else {\n      // set to empty for ssr scenarios and resolve promise\n      ioniconContent.set(url, '');\n      return Promise.resolve();\n    }\n  }\n  return req;\n};\n\nconst iconCss = \":host{display:inline-block;width:1em;height:1em;contain:strict;fill:currentColor;-webkit-box-sizing:content-box !important;box-sizing:content-box !important}:host .ionicon{stroke:currentColor}.ionicon-fill-none{fill:none}.ionicon-stroke-width{stroke-width:32px;stroke-width:var(--ionicon-stroke-width, 32px)}.icon-inner,.ionicon,svg{display:block;height:100%;width:100%}@supports (background: -webkit-named-image(i)){:host(.icon-rtl) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}}@supports not selector(:dir(rtl)) and selector(:host-context([dir='rtl'])){:host(.icon-rtl) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}}:host(.flip-rtl):host-context([dir='rtl']) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}@supports selector(:dir(rtl)){:host(.flip-rtl:dir(rtl)) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}:host(.flip-rtl:dir(ltr)) .icon-inner{-webkit-transform:scaleX(1);transform:scaleX(1)}}:host(.icon-small){font-size:1.125rem !important}:host(.icon-large){font-size:2rem !important}:host(.ion-color){color:var(--ion-color-base) !important}:host(.ion-color-primary){--ion-color-base:var(--ion-color-primary, #3880ff)}:host(.ion-color-secondary){--ion-color-base:var(--ion-color-secondary, #0cd1e8)}:host(.ion-color-tertiary){--ion-color-base:var(--ion-color-tertiary, #f4a942)}:host(.ion-color-success){--ion-color-base:var(--ion-color-success, #10dc60)}:host(.ion-color-warning){--ion-color-base:var(--ion-color-warning, #ffce00)}:host(.ion-color-danger){--ion-color-base:var(--ion-color-danger, #f14141)}:host(.ion-color-light){--ion-color-base:var(--ion-color-light, #f4f5f8)}:host(.ion-color-medium){--ion-color-base:var(--ion-color-medium, #989aa2)}:host(.ion-color-dark){--ion-color-base:var(--ion-color-dark, #222428)}\";\n\nconst Icon = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.iconName = null;\n        this.inheritedAttributes = {};\n        this.didLoadIcon = false;\n        this.svgContent = undefined;\n        this.isVisible = false;\n        this.mode = getIonMode();\n        this.color = undefined;\n        this.ios = undefined;\n        this.md = undefined;\n        this.flipRtl = undefined;\n        this.name = undefined;\n        this.src = undefined;\n        this.icon = undefined;\n        this.size = undefined;\n        this.lazy = false;\n        this.sanitize = true;\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAttributes(this.el, ['aria-label']);\n    }\n    connectedCallback() {\n        // purposely do not return the promise here because loading\n        // the svg file should not hold up loading the app\n        // only load the svg if it's visible\n        this.waitUntilVisible(this.el, '50px', () => {\n            this.isVisible = true;\n            this.loadIcon();\n        });\n    }\n    componentDidLoad() {\n        /**\n         * Addresses an Angular issue where property values are assigned after the 'connectedCallback' but prior to the registration of watchers.\n         * This enhancement ensures the loading of an icon when the component has finished rendering and the icon has yet to apply the SVG data.\n         * This modification pertains to the usage of Angular's binding syntax:\n         * `<ion-icon [name]=\"myIconName\"></ion-icon>`\n         */\n        if (!this.didLoadIcon) {\n            this.loadIcon();\n        }\n    }\n    disconnectedCallback() {\n        if (this.io) {\n            this.io.disconnect();\n            this.io = undefined;\n        }\n    }\n    waitUntilVisible(el, rootMargin, cb) {\n        if (this.lazy && typeof window !== 'undefined' && window.IntersectionObserver) {\n            const io = (this.io = new window.IntersectionObserver((data) => {\n                if (data[0].isIntersecting) {\n                    io.disconnect();\n                    this.io = undefined;\n                    cb();\n                }\n            }, { rootMargin }));\n            io.observe(el);\n        }\n        else {\n            // browser doesn't support IntersectionObserver\n            // so just fallback to always show it\n            cb();\n        }\n    }\n    loadIcon() {\n        if (this.isVisible) {\n            const url = getUrl(this);\n            if (url) {\n                if (ioniconContent.has(url)) {\n                    // sync if it's already loaded\n                    this.svgContent = ioniconContent.get(url);\n                }\n                else {\n                    // async if it hasn't been loaded\n                    getSvgContent(url, this.sanitize).then(() => (this.svgContent = ioniconContent.get(url)));\n                }\n                this.didLoadIcon = true;\n            }\n        }\n        this.iconName = getName(this.name, this.icon, this.mode, this.ios, this.md);\n    }\n    render() {\n        const { flipRtl, iconName, inheritedAttributes, el } = this;\n        const mode = this.mode || 'md';\n        // we have designated that arrows & chevrons should automatically flip (unless flip-rtl is set to false) because \"back\" is left in ltr and right in rtl, and \"forward\" is the opposite\n        const shouldAutoFlip = iconName\n            ? (iconName.includes('arrow') || iconName.includes('chevron')) && flipRtl !== false\n            : false;\n        // if shouldBeFlippable is true, the icon should change direction when `dir` changes\n        const shouldBeFlippable = flipRtl || shouldAutoFlip;\n        return (h(Host, Object.assign({ role: \"img\", class: Object.assign(Object.assign({ [mode]: true }, createColorClasses(this.color)), { [`icon-${this.size}`]: !!this.size, 'flip-rtl': shouldBeFlippable, 'icon-rtl': shouldBeFlippable && isRTL(el) }) }, inheritedAttributes), this.svgContent ? (h(\"div\", { class: \"icon-inner\", innerHTML: this.svgContent })) : (h(\"div\", { class: \"icon-inner\" }))));\n    }\n    static get assetsDirs() { return [\"svg\"]; }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"name\": [\"loadIcon\"],\n        \"src\": [\"loadIcon\"],\n        \"icon\": [\"loadIcon\"],\n        \"ios\": [\"loadIcon\"],\n        \"md\": [\"loadIcon\"]\n    }; }\n};\nconst getIonMode = () => (typeof document !== 'undefined' && document.documentElement.getAttribute('mode')) || 'md';\nconst createColorClasses = (color) => {\n    return color\n        ? {\n            'ion-color': true,\n            [`ion-color-${color}`]: true,\n        }\n        : null;\n};\nIcon.style = iconCss;\n\nexport { Button as ion_button, Icon as ion_icon };\n"], "names": ["q", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "r", "registerInstance", "d", "createEvent", "n", "forceUpdate", "m", "printIonWarning", "e", "getIonMode$1", "h", "j", "Host", "k", "getElement", "hasShadowDom", "i", "inheritAriaAttributes", "o", "openURL", "c", "createColorClasses$1", "hostContext", "CACHED_MAP", "getIconMap", "window", "Map", "win", "Ionicons", "map", "getUrl", "url", "getSrc", "src", "getName", "name", "icon", "mode", "ios", "md", "getNamedUrl", "iconName", "iconEl", "get", "console", "warn", "<PERSON><PERSON><PERSON><PERSON>", "isSrc", "isStr", "trim", "invalid<PERSON>hars", "replace", "str", "length", "test", "val", "toLowerCase", "inheritAttributes", "el", "attributes", "attributeObject", "for<PERSON>ach", "attr", "hasAttribute", "value", "getAttribute", "removeAttribute", "isRTL", "hostEl", "dir", "document", "buttonIosCss", "buttonMdCss", "<PERSON><PERSON>", "constructor", "hostRef", "ionFocus", "ionBlur", "inItem", "inList<PERSON><PERSON>er", "inToolbar", "formButtonEl", "formEl", "inheritedAttributes", "isCircle", "buttonType", "disabled", "routerDirection", "strong", "type", "handleClick", "ev", "href", "routerAnimation", "submitForm", "onFocus", "emit", "onBlur", "slotChanged", "hasIconOnly", "disabled<PERSON><PERSON>ed", "onAriaChanged", "newValue", "_old<PERSON><PERSON><PERSON>", "propName", "Object", "assign", "renderHiddenButton", "findForm", "contains", "newFormButtonEl", "createElement", "style", "display", "append<PERSON><PERSON><PERSON>", "componentWillLoad", "closest", "querySelector", "rippleType", "hasClearFill", "fill", "undefined", "form", "HTMLFormElement", "getElementById", "preventDefault", "click", "render", "rel", "target", "size", "color", "expand", "shape", "finalSize", "TagType", "attrs", "download", "key", "onClick", "class", "part", "onSlotchange", "watchers", "validateContent", "svgContent", "div", "innerHTML", "childNodes", "nodeName", "<PERSON><PERSON><PERSON><PERSON>", "svgElm", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "svgClass", "setAttribute", "<PERSON><PERSON><PERSON><PERSON>", "elm", "nodeType", "indexOf", "isSvgDataUrl", "startsWith", "isEncodedDataUrl", "ioniconContent", "requests", "parser", "getSvgContent", "sanitize", "req", "fetch", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doc", "parseFromString", "svg", "set", "outerHTML", "Promise", "resolve", "then", "rsp", "ok", "text", "iconCss", "Icon", "didLoadIcon", "isVisible", "getIonMode", "flipRtl", "lazy", "connectedCallback", "waitUntilVisible", "loadIcon", "componentDidLoad", "disconnectedCallback", "io", "disconnect", "rootMargin", "cb", "IntersectionObserver", "data", "isIntersecting", "observe", "has", "shouldAutoFlip", "includes", "shouldBeFlippable", "role", "createColorClasses", "assetsDirs", "documentElement", "ion_button", "ion_icon"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}