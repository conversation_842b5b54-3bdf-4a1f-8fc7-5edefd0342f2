"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[7428],{7428:(L,v,u)=>{u.r(v),u.d(v,{ion_datetime_button:()=>P});var b=u(467),s=u(2734),f=u(1837),D=u(4576),l=u(5386);const P=(()=>{let x=class{constructor(r){var d=this;(0,s.r)(this,r),this.datetimeEl=null,this.overlayEl=null,this.datetimePresentation="date-time",this.datetimeActive=!1,this.color="primary",this.disabled=!1,this.getParsedDateValues=e=>null==e?[]:Array.isArray(e)?e:[e],this.setDateTimeText=()=>{var e,n,a,i,o;const{datetimeEl:c,datetimePresentation:O}=this;if(!c)return;const{value:B,locale:m,formatOptions:t,hourCycle:I,preferWheel:A,multiple:M,titleSelectedDatesFormatter:g}=c,h=this.getParsedDateValues(B),_=(0,l.s)(h.length>0?h:[(0,l.z)()]);if(!_)return;const p=_[0],y=(0,l.O)(m,I);switch(this.dateText=this.timeText=void 0,O){case"date-time":case"time-date":const k=(0,l.Q)(m,p,null!==(e=t?.date)&&void 0!==e?e:{month:"short",day:"numeric",year:"numeric"}),T=(0,l.P)(m,p,y,t?.time);A?this.dateText=`${k} ${T}`:(this.dateText=k,this.timeText=T);break;case"date":if(M&&1!==h.length){let E=`${h.length} days`;if(void 0!==g)try{E=g(h)}catch(j){(0,s.o)("[ion-datetime-button] - Exception in provided `titleSelectedDatesFormatter`:",j)}this.dateText=E}else this.dateText=(0,l.Q)(m,p,null!==(n=t?.date)&&void 0!==n?n:{month:"short",day:"numeric",year:"numeric"});break;case"time":this.timeText=(0,l.P)(m,p,y,t?.time);break;case"month-year":this.dateText=(0,l.Q)(m,p,null!==(a=t?.date)&&void 0!==a?a:{month:"long",year:"numeric"});break;case"month":this.dateText=(0,l.Q)(m,p,null!==(i=t?.time)&&void 0!==i?i:{month:"long"});break;case"year":this.dateText=(0,l.Q)(m,p,null!==(o=t?.time)&&void 0!==o?o:{year:"numeric"})}},this.waitForDatetimeChanges=(0,b.A)(function*(){const{datetimeEl:e}=d;return e?new Promise(n=>{(0,f.f)(e,"ionRender",n,{once:!0})}):Promise.resolve()}),this.handleDateClick=function(){var e=(0,b.A)(function*(n){const{datetimeEl:a,datetimePresentation:i}=d;if(!a)return;let o=!1;switch(i){case"date-time":case"time-date":!a.preferWheel&&"date"!==a.presentation&&(a.presentation="date",o=!0)}d.selectedButton="date",d.presentOverlay(n,o,d.dateTargetEl)});return function(n){return e.apply(this,arguments)}}(),this.handleTimeClick=e=>{const{datetimeEl:n,datetimePresentation:a}=this;if(!n)return;let i=!1;switch(a){case"date-time":case"time-date":"time"!==n.presentation&&(n.presentation="time",i=!0)}this.selectedButton="time",this.presentOverlay(e,i,this.timeTargetEl)},this.presentOverlay=function(){var e=(0,b.A)(function*(n,a,i){const{overlayEl:o}=d;o&&("ION-POPOVER"===o.tagName?(a&&(yield d.waitForDatetimeChanges()),o.present(Object.assign(Object.assign({},n),{detail:{ionShadowTarget:i}}))):o.present())});return function(n,a,i){return e.apply(this,arguments)}}()}componentWillLoad(){var r=this;return(0,b.A)(function*(){const{datetime:d}=r;if(!d)return void(0,s.o)("[ion-datetime-button] - An ID associated with an ion-datetime instance is required to function properly.",r.el);const e=r.datetimeEl=document.getElementById(d);if(!e)return void(0,s.o)(`[ion-datetime-button] - No ion-datetime instance found for ID '${d}'.`,r.el);if("ION-DATETIME"!==e.tagName)return void(0,s.o)(`[ion-datetime-button] - Expected an ion-datetime instance for ID '${d}' but received '${e.tagName.toLowerCase()}' instead.`,e);new IntersectionObserver(i=>{r.datetimeActive=i[0].isIntersecting},{threshold:.01}).observe(e);const a=r.overlayEl=e.closest("ion-modal, ion-popover");a&&a.classList.add("ion-datetime-button-overlay"),(0,f.c)(e,()=>{const i=r.datetimePresentation=e.presentation||"date-time";switch(r.setDateTimeText(),(0,f.f)(e,"ionValueChange",r.setDateTimeText),i){case"date-time":case"date":case"month-year":case"month":case"year":r.selectedButton="date";break;case"time-date":case"time":r.selectedButton="time"}})})()}render(){const{color:r,dateText:d,timeText:e,selectedButton:n,datetimeActive:a,disabled:i}=this,o=(0,s.e)(this);return(0,s.h)(s.j,{key:"11d037e6ab061e5116842970760b04850b42f2c7",class:(0,D.c)(r,{[o]:!0,[`${n}-active`]:a,"datetime-button-disabled":i})},d&&(0,s.h)("button",{key:"08ecb62da0fcbf7466a1f2403276712a3ff17fbc",class:"ion-activatable",id:"date-button","aria-expanded":a?"true":"false",onClick:this.handleDateClick,disabled:i,part:"native",ref:c=>this.dateTargetEl=c},(0,s.h)("slot",{key:"1c04853d4d23c0f1a594602bde44511c98355644",name:"date-target"},d),"md"===o&&(0,s.h)("ion-ripple-effect",{key:"5fc566cd4bc885bcf983ce99e3dc65d7f485bf9b"})),e&&(0,s.h)("button",{key:"c9c5c34ac338badf8659da22bea5829d62c51169",class:"ion-activatable",id:"time-button","aria-expanded":a?"true":"false",onClick:this.handleTimeClick,disabled:i,part:"native",ref:c=>this.timeTargetEl=c},(0,s.h)("slot",{key:"147a9d2069dbf737f6fc64787823d6d5af5aa653",name:"time-target"},e),"md"===o&&(0,s.h)("ion-ripple-effect",{key:"70a5e25b75ed90ac6bba003468435f67aa9d8f0a"})))}get el(){return(0,s.k)(this)}};return x.style={ios:":host{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}:host button{border-radius:8px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:0px;margin-bottom:0px;position:relative;-webkit-transition:150ms color ease-in-out;transition:150ms color ease-in-out;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:1rem;cursor:pointer;overflow:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host(.time-active) #time-button,:host(.date-active) #date-button{color:var(--ion-color-base)}:host(.datetime-button-disabled){pointer-events:none}:host(.datetime-button-disabled) button{opacity:0.4}:host button{-webkit-padding-start:13px;padding-inline-start:13px;-webkit-padding-end:13px;padding-inline-end:13px;padding-top:7px;padding-bottom:7px}:host button.ion-activated{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}",md:":host{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}:host button{border-radius:8px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:0px;margin-bottom:0px;position:relative;-webkit-transition:150ms color ease-in-out;transition:150ms color ease-in-out;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:1rem;cursor:pointer;overflow:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host(.time-active) #time-button,:host(.date-active) #date-button{color:var(--ion-color-base)}:host(.datetime-button-disabled){pointer-events:none}:host(.datetime-button-disabled) button{opacity:0.4}:host button{-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px}"},x})()}}]);