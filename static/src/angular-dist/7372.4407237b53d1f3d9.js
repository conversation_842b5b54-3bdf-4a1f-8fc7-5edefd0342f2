"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[7372],{7372:(k,h,r)=>{r.r(h),r.d(h,{ion_loading:()=>L});var p=r(467),e=r(2734),m=r(6640),u=r(1837),f=r(7930),l=r(3217),_=r(4576),n=r(5756);r(9596),r(1906),r(1653),r(8607);const c=a=>{const i=(0,n.c)(),t=(0,n.c)(),o=(0,n.c)();return t.addElement(a.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),o.addElement(a.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.01,transform:"scale(1.1)"},{offset:1,opacity:1,transform:"scale(1)"}]),i.addElement(a).easing("ease-in-out").duration(200).addAnimation([t,o])},D=a=>{const i=(0,n.c)(),t=(0,n.c)(),o=(0,n.c)();return t.addElement(a.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),o.addElement(a.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.99,transform:"scale(1)"},{offset:1,opacity:0,transform:"scale(0.9)"}]),i.addElement(a).easing("ease-in-out").duration(200).addAnimation([t,o])},E=a=>{const i=(0,n.c)(),t=(0,n.c)(),o=(0,n.c)();return t.addElement(a.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),o.addElement(a.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.01,transform:"scale(1.1)"},{offset:1,opacity:1,transform:"scale(1)"}]),i.addElement(a).easing("ease-in-out").duration(200).addAnimation([t,o])},w=a=>{const i=(0,n.c)(),t=(0,n.c)(),o=(0,n.c)();return t.addElement(a.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),o.addElement(a.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.99,transform:"scale(1)"},{offset:1,opacity:0,transform:"scale(0.9)"}]),i.addElement(a).easing("ease-in-out").duration(200).addAnimation([t,o])},L=(()=>{let a=class{constructor(i){(0,e.r)(this,i),this.didPresent=(0,e.d)(this,"ionLoadingDidPresent",7),this.willPresent=(0,e.d)(this,"ionLoadingWillPresent",7),this.willDismiss=(0,e.d)(this,"ionLoadingWillDismiss",7),this.didDismiss=(0,e.d)(this,"ionLoadingDidDismiss",7),this.didPresentShorthand=(0,e.d)(this,"didPresent",7),this.willPresentShorthand=(0,e.d)(this,"willPresent",7),this.willDismissShorthand=(0,e.d)(this,"willDismiss",7),this.didDismissShorthand=(0,e.d)(this,"didDismiss",7),this.delegateController=(0,l.d)(this),this.lockController=(0,f.c)(),this.triggerController=(0,l.e)(),this.customHTMLEnabled=e.l.get("innerHTMLTemplatesEnabled",m.E),this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.duration=0,this.backdropDismiss=!1,this.showBackdrop=!0,this.translucent=!1,this.animated=!0,this.isOpen=!1,this.onBackdropTap=()=>{this.dismiss(void 0,l.B)}}onIsOpenChange(i,t){!0===i&&!1===t?this.present():!1===i&&!0===t&&this.dismiss()}triggerChanged(){const{trigger:i,el:t,triggerController:o}=this;i&&o.addClickListener(t,i)}connectedCallback(){(0,l.j)(this.el),this.triggerChanged()}componentWillLoad(){var i;if(void 0===this.spinner){const t=(0,e.e)(this);this.spinner=e.l.get("loadingSpinner",e.l.get("spinner","ios"===t?"lines":"crescent"))}null!==(i=this.htmlAttributes)&&void 0!==i&&i.id||(0,l.k)(this.el)}componentDidLoad(){!0===this.isOpen&&(0,u.r)(()=>this.present()),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener()}present(){var i=this;return(0,p.A)(function*(){const t=yield i.lockController.lock();yield i.delegateController.attachViewToDom(),yield(0,l.f)(i,"loadingEnter",c,E),i.duration>0&&(i.durationTimeout=setTimeout(()=>i.dismiss(),i.duration+10)),t()})()}dismiss(i,t){var o=this;return(0,p.A)(function*(){const v=yield o.lockController.lock();o.durationTimeout&&clearTimeout(o.durationTimeout);const g=yield(0,l.g)(o,i,t,"loadingLeave",D,w);return g&&o.delegateController.removeViewFromDom(),v(),g})()}onDidDismiss(){return(0,l.h)(this.el,"ionLoadingDidDismiss")}onWillDismiss(){return(0,l.h)(this.el,"ionLoadingWillDismiss")}renderLoadingMessage(i){const{customHTMLEnabled:t,message:o}=this;return t?(0,e.h)("div",{class:"loading-content",id:i,innerHTML:(0,m.a)(o)}):(0,e.h)("div",{class:"loading-content",id:i},o)}render(){const{message:i,spinner:t,htmlAttributes:o,overlayIndex:v}=this,g=(0,e.e)(this),x=`loading-${v}-msg`;return(0,e.h)(e.j,Object.assign({key:"4497183ce220242abe19ae15f328f9a92ccafbbc",role:"dialog","aria-modal":"true","aria-labelledby":void 0!==i?x:null,tabindex:"-1"},o,{style:{zIndex:`${4e4+this.overlayIndex}`},onIonBackdropTap:this.onBackdropTap,class:Object.assign(Object.assign({},(0,_.g)(this.cssClass)),{[g]:!0,"overlay-hidden":!0,"loading-translucent":this.translucent})}),(0,e.h)("ion-backdrop",{key:"231dec84e424a2dc358ce95b84d6035cf43e4dea",visible:this.showBackdrop,tappable:this.backdropDismiss}),(0,e.h)("div",{key:"c9af29b6e6bb49a217396a5c874bbfb8835a926c",tabindex:"0","aria-hidden":"true"}),(0,e.h)("div",{key:"a8659863743cdeccbe1ba810eaabfd3ebfcb86f3",class:"loading-wrapper ion-overlay-wrapper"},t&&(0,e.h)("div",{key:"3b346f39bc71691bd8686556a1e142198a7b12fa",class:"loading-spinner"},(0,e.h)("ion-spinner",{key:"8dc2bf1556e5138e262827f1516c59ecd09f3520",name:t,"aria-hidden":"true"})),void 0!==i&&this.renderLoadingMessage(x)),(0,e.h)("div",{key:"054164c0dbae9a0e0973dd3c8e28f5b771820310",tabindex:"0","aria-hidden":"true"}))}get el(){return(0,e.k)(this)}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}};return a.style={ios:".sc-ion-loading-ios-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-ios-h{display:none}.loading-wrapper.sc-ion-loading-ios{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-ios{color:var(--spinner-color)}.sc-ion-loading-ios-h{--background:var(--ion-overlay-background-color, var(--ion-color-step-100, var(--ion-background-color-step-100, #f9f9f9)));--max-width:270px;--max-height:90%;--spinner-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));--backdrop-opacity:var(--ion-backdrop-opacity, 0.3);color:var(--ion-text-color, #000);font-size:0.875rem}.loading-wrapper.sc-ion-loading-ios{border-radius:8px;-webkit-padding-start:34px;padding-inline-start:34px;-webkit-padding-end:34px;padding-inline-end:34px;padding-top:24px;padding-bottom:24px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.loading-translucent.sc-ion-loading-ios-h .loading-wrapper.sc-ion-loading-ios{background-color:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}.loading-content.sc-ion-loading-ios{font-weight:bold}.loading-spinner.sc-ion-loading-ios+.loading-content.sc-ion-loading-ios{-webkit-margin-start:16px;margin-inline-start:16px}",md:".sc-ion-loading-md-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-md-h{display:none}.loading-wrapper.sc-ion-loading-md{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-md{color:var(--spinner-color)}.sc-ion-loading-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--max-width:280px;--max-height:90%;--spinner-color:var(--ion-color-primary, #0054e9);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32);color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));font-size:0.875rem}.loading-wrapper.sc-ion-loading-md{border-radius:2px;-webkit-padding-start:24px;padding-inline-start:24px;-webkit-padding-end:24px;padding-inline-end:24px;padding-top:24px;padding-bottom:24px;-webkit-box-shadow:0 16px 20px rgba(0, 0, 0, 0.4);box-shadow:0 16px 20px rgba(0, 0, 0, 0.4)}.loading-spinner.sc-ion-loading-md+.loading-content.sc-ion-loading-md{-webkit-margin-start:16px;margin-inline-start:16px}"},a})()},4576:(k,h,r)=>{r.d(h,{c:()=>m,g:()=>f,h:()=>e,o:()=>_});var p=r(467);const e=(n,s)=>null!==s.closest(n),m=(n,s)=>"string"==typeof n&&n.length>0?Object.assign({"ion-color":!0,[`ion-color-${n}`]:!0},s):s,f=n=>{const s={};return(n=>void 0!==n?(Array.isArray(n)?n:n.split(" ")).filter(d=>null!=d).map(d=>d.trim()).filter(d=>""!==d):[])(n).forEach(d=>s[d]=!0),s},l=/^[a-z][a-z0-9+\-.]*:/,_=function(){var n=(0,p.A)(function*(s,d,b,y){if(null!=s&&"#"!==s[0]&&!l.test(s)){const c=document.querySelector("ion-router");if(c)return d?.preventDefault(),c.push(s,b,y)}return!1});return function(d,b,y,c){return n.apply(this,arguments)}}()}}]);