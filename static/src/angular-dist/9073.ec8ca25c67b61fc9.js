"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[9073],{9073:(r,t,n)=>{n.r(t),n.d(t,{ion_segment_content:()=>l});var e=n(2734);const l=(()=>{let s=class{constructor(o){(0,e.r)(this,o)}render(){return(0,e.h)(e.j,{key:"db6876f2aee7afa1ea8bc147337670faa68fae1c"},(0,e.h)("slot",{key:"bc05714a973a5655668679033f5809a1da6db8cc"}))}};return s.style=":host{scroll-snap-align:center;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%;min-height:1px;overflow-y:scroll;scrollbar-width:none;-ms-overflow-style:none;}:host::-webkit-scrollbar{display:none}",s})()}}]);