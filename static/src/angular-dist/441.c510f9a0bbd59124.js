"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[441],{441:(ke,Q,k)=>{k.r(Q),k.d(Q,{ion_modal:()=>be});var M=k(467),c=k(2734),T=k(4657),V=k(1653),m=k(1837),oe=k(7930),re=k(6780),p=k(3217),A=k(4576),B=k(4590),q=k(3224),l=k(5756),G=k(9166),ce=k(6011),ee=k(9596),F=(k(1906),k(8607),k(9043),function(e){return e.Dark="DARK",e.Light="LIGHT",e.Default="DEFAULT",e}(F||{}));const ie={getEngine(){const e=(0,re.g)();if(e?.isPluginAvailable("StatusBar"))return e.Plugins.StatusBar},setStyle(e){const t=this.getEngine();t&&t.setStyle(e)},getStyle:(e=(0,M.A)(function*(){const t=this.getEngine();if(!t)return F.Default;const{style:n}=yield t.getInfo();return n}),function(){return e.apply(this,arguments)})},se=(e,t)=>{if(1===t)return 0;const n=1/(1-t);return e*n+-t*n},pe=()=>{!ee.w||ee.w.innerWidth>=768||ie.setStyle({style:F.Dark})},ae=(e=F.Default)=>{!ee.w||ee.w.innerWidth>=768||ie.setStyle({style:e})},fe=function(){var e=(0,M.A)(function*(t,n){"function"!=typeof t.canDismiss||!(yield t.canDismiss(void 0,p.G))||(n.isRunning()?n.onFinish(()=>{t.dismiss(void 0,"handler")},{oneTimeCallback:!0}):t.dismiss(void 0,"handler"))});return function(n,o){return e.apply(this,arguments)}}(),de=e=>.00255275*2.71828**(-14.9619*e)-1.00255*2.71828**(-.0380968*e)+1,he=(e,t)=>(0,m.e)(400,e/Math.abs(1.1*t),500),me=e=>{const{currentBreakpoint:t,backdropBreakpoint:n,expandToScroll:o}=e,s=void 0===n||n<t,i=s?`calc(var(--backdrop-opacity) * ${t})`:"0",a=(0,l.c)("backdropAnimation").fromTo("opacity",0,i);return s&&a.beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),{wrapperAnimation:(0,l.c)("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:"translateY(100%)"},{offset:1,opacity:1,transform:`translateY(${100-100*t}%)`}]),backdropAnimation:a,contentAnimation:o?void 0:(0,l.c)("contentAnimation").keyframes([{offset:0,opacity:1,maxHeight:100*(1-t)+"%"},{offset:1,opacity:1,maxHeight:100*t+"%"}])}},ue=e=>{const{currentBreakpoint:t,backdropBreakpoint:n}=e,o=`calc(var(--backdrop-opacity) * ${se(t,n)})`,s=[{offset:0,opacity:o},{offset:1,opacity:0}],i=[{offset:0,opacity:o},{offset:n,opacity:0},{offset:1,opacity:0}],a=(0,l.c)("backdropAnimation").keyframes(0!==n?i:s);return{wrapperAnimation:(0,l.c)("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:`translateY(${100-100*t}%)`},{offset:1,opacity:1,transform:"translateY(100%)"}]),backdropAnimation:a}},ge=(e,t)=>{const{presentingEl:n,currentBreakpoint:o,expandToScroll:s}=t,i=(0,m.g)(e),{wrapperAnimation:a,backdropAnimation:r,contentAnimation:d}=void 0!==o?me(t):{backdropAnimation:(0,l.c)().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),wrapperAnimation:(0,l.c)().fromTo("transform","translateY(100vh)","translateY(0vh)"),contentAnimation:void 0};r.addElement(i.querySelector("ion-backdrop")),a.addElement(i.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1}),!s&&d?.addElement(e.querySelector(".ion-page"));const y=(0,l.c)("entering-base").addElement(e).easing("cubic-bezier(0.32,0.72,0,1)").duration(500).addAnimation([a]);if(d&&y.addAnimation(d),n){const w=window.innerWidth<768,h="ION-MODAL"===n.tagName&&void 0!==n.presentingElement,S=(0,m.g)(n),b=(0,l.c)().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"}),E=document.body;if(w){const R=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",$=`translateY(${h?"-10px":R}) scale(0.915)`;b.afterStyles({transform:$}).beforeAddWrite(()=>E.style.setProperty("background-color","black")).addElement(n).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"},{offset:1,filter:"contrast(0.85)",transform:$,borderRadius:"10px 10px 0 0"}]),y.addAnimation(b)}else if(y.addAnimation(r),h){const u=`translateY(-10px) scale(${h?.915:1})`;b.afterStyles({transform:u}).addElement(S.querySelector(".modal-wrapper")).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0) scale(1)"},{offset:1,filter:"contrast(0.85)",transform:u}]);const Y=(0,l.c)().afterStyles({transform:u}).addElement(S.querySelector(".modal-shadow")).keyframes([{offset:0,opacity:"1",transform:"translateY(0) scale(1)"},{offset:1,opacity:"0",transform:u}]);y.addAnimation([b,Y])}else a.fromTo("opacity","0","1")}else y.addAnimation(r);return y},ye=(e,t,n=500)=>{const{presentingEl:o,currentBreakpoint:s}=t,i=(0,m.g)(e),{wrapperAnimation:a,backdropAnimation:r}=void 0!==s?ue(t):{backdropAnimation:(0,l.c)().fromTo("opacity","var(--backdrop-opacity)",0),wrapperAnimation:(0,l.c)().fromTo("transform","translateY(0vh)","translateY(100vh)")};r.addElement(i.querySelector("ion-backdrop")),a.addElement(i.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1});const d=(0,l.c)("leaving-base").addElement(e).easing("cubic-bezier(0.32,0.72,0,1)").duration(n).addAnimation(a);if(o){const y=window.innerWidth<768,w="ION-MODAL"===o.tagName&&void 0!==o.presentingElement,h=(0,m.g)(o),S=(0,l.c)().beforeClearStyles(["transform"]).afterClearStyles(["transform"]).onFinish(E=>{1===E&&(o.style.setProperty("overflow",""),Array.from(b.querySelectorAll("ion-modal:not(.overlay-hidden)")).filter(u=>void 0!==u.presentingElement).length<=1&&b.style.setProperty("background-color",""))}),b=document.body;if(y){const E=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",Y=`translateY(${w?"-10px":E}) scale(0.915)`;S.addElement(o).keyframes([{offset:0,filter:"contrast(0.85)",transform:Y,borderRadius:"10px 10px 0 0"},{offset:1,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"}]),d.addAnimation(S)}else if(d.addAnimation(r),w){const R=`translateY(-10px) scale(${w?.915:1})`;S.addElement(h.querySelector(".modal-wrapper")).afterStyles({transform:"translate3d(0, 0, 0)"}).keyframes([{offset:0,filter:"contrast(0.85)",transform:R},{offset:1,filter:"contrast(1)",transform:"translateY(0) scale(1)"}]);const u=(0,l.c)().addElement(h.querySelector(".modal-shadow")).afterStyles({transform:"translateY(0) scale(1)"}).keyframes([{offset:0,opacity:"0",transform:R},{offset:1,opacity:"1",transform:"translateY(0) scale(1)"}]);d.addAnimation([S,u])}else a.fromTo("opacity","1","0")}else d.addAnimation(r);return d},Te=(e,t)=>{const{currentBreakpoint:n,expandToScroll:o}=t,s=(0,m.g)(e),{wrapperAnimation:i,backdropAnimation:a,contentAnimation:r}=void 0!==n?me(t):{backdropAnimation:(0,l.c)().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),wrapperAnimation:(0,l.c)().keyframes([{offset:0,opacity:.01,transform:"translateY(40px)"},{offset:1,opacity:1,transform:"translateY(0px)"}]),contentAnimation:void 0};a.addElement(s.querySelector("ion-backdrop")),i.addElement(s.querySelector(".modal-wrapper")),!o&&r?.addElement(e.querySelector(".ion-page"));const d=(0,l.c)().addElement(e).easing("cubic-bezier(0.36,0.66,0.04,1)").duration(280).addAnimation([a,i]);return r&&d.addAnimation(r),d},Pe=(e,t)=>{const{currentBreakpoint:n}=t,o=(0,m.g)(e),{wrapperAnimation:s,backdropAnimation:i}=void 0!==n?ue(t):{backdropAnimation:(0,l.c)().fromTo("opacity","var(--backdrop-opacity)",0),wrapperAnimation:(0,l.c)().keyframes([{offset:0,opacity:.99,transform:"translateY(0px)"},{offset:1,opacity:0,transform:"translateY(40px)"}])};return i.addElement(o.querySelector("ion-backdrop")),s.addElement(o.querySelector(".modal-wrapper")),(0,l.c)().easing("cubic-bezier(0.47,0,0.745,0.715)").duration(200).addAnimation([i,s])},be=class{constructor(e){(0,c.r)(this,e),this.didPresent=(0,c.d)(this,"ionModalDidPresent",7),this.willPresent=(0,c.d)(this,"ionModalWillPresent",7),this.willDismiss=(0,c.d)(this,"ionModalWillDismiss",7),this.didDismiss=(0,c.d)(this,"ionModalDidDismiss",7),this.ionBreakpointDidChange=(0,c.d)(this,"ionBreakpointDidChange",7),this.didPresentShorthand=(0,c.d)(this,"didPresent",7),this.willPresentShorthand=(0,c.d)(this,"willPresent",7),this.willDismissShorthand=(0,c.d)(this,"willDismiss",7),this.didDismissShorthand=(0,c.d)(this,"didDismiss",7),this.ionMount=(0,c.d)(this,"ionMount",7),this.lockController=(0,oe.c)(),this.triggerController=(0,p.e)(),this.coreDelegate=(0,V.C)(),this.isSheetModal=!1,this.inheritedAttributes={},this.inline=!1,this.gestureAnimationDismissing=!1,this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.expandToScroll=!0,this.backdropBreakpoint=0,this.handleBehavior="none",this.backdropDismiss=!0,this.showBackdrop=!0,this.animated=!0,this.isOpen=!1,this.keepContentsMounted=!1,this.focusTrap=!0,this.canDismiss=!0,this.onHandleClick=()=>{const{sheetTransition:t,handleBehavior:n}=this;"cycle"!==n||void 0!==t||this.moveToNextBreakpoint()},this.onBackdropTap=()=>{const{sheetTransition:t}=this;void 0===t&&this.dismiss(void 0,p.B)},this.onLifecycle=t=>{const n=this.usersElement,o=Re[t.type];if(n&&o){const s=new CustomEvent(o,{bubbles:!1,cancelable:!1,detail:t.detail});n.dispatchEvent(s)}},this.onModalFocus=t=>{const{dragHandleEl:n,el:o}=this;t.target===o&&n&&-1!==n.tabIndex&&n.focus()},this.onSlotChange=({target:t})=>{t.assignedElements().forEach(o=>{o.querySelectorAll("ion-modal").forEach(s=>{null===s.getAttribute("data-parent-ion-modal")&&s.setAttribute("data-parent-ion-modal",this.el.id)})})}}onIsOpenChange(e,t){!0===e&&!1===t?this.present():!1===e&&!0===t&&this.dismiss()}triggerChanged(){const{trigger:e,el:t,triggerController:n}=this;e&&n.addClickListener(t,e)}onWindowResize(){"ios"!==(0,c.e)(this)||!this.presentingElement||this.enterAnimation||this.leaveAnimation||(clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout(()=>{this.handleViewTransition()},50))}breakpointsChanged(e){void 0!==e&&(this.sortedBreakpoints=e.sort((t,n)=>t-n))}connectedCallback(){const{el:e}=this;(0,p.j)(e),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener(),this.cleanupViewTransitionListener(),this.cleanupParentRemovalObserver()}componentWillLoad(){var e;const{breakpoints:t,initialBreakpoint:n,el:o,htmlAttributes:s}=this,i=this.isSheetModal=void 0!==t&&void 0!==n,a=["aria-label","role"];this.inheritedAttributes=(0,m.b)(o,a),o.parentNode&&(this.cachedOriginalParent=o.parentNode),void 0!==s&&a.forEach(r=>{s[r]&&(this.inheritedAttributes=Object.assign(Object.assign({},this.inheritedAttributes),{[r]:s[r]}),delete s[r])}),i&&(this.currentBreakpoint=this.initialBreakpoint),void 0!==t&&void 0!==n&&!t.includes(n)&&(0,c.m)("[ion-modal] - Your breakpoints array must include the initialBreakpoint value."),null!==(e=this.htmlAttributes)&&void 0!==e&&e.id||(0,p.k)(this.el)}componentDidLoad(){!0===this.isOpen&&(0,m.r)(()=>this.present()),this.breakpointsChanged(this.breakpoints),this.triggerChanged()}getDelegate(e=!1){if(this.workingDelegate&&!e)return{delegate:this.workingDelegate,inline:this.inline};const n=this.inline=null!==this.el.parentNode&&!this.hasController;return{inline:n,delegate:this.workingDelegate=n?this.delegate||this.coreDelegate:this.delegate}}checkCanDismiss(e,t){var n=this;return(0,M.A)(function*(){const{canDismiss:o}=n;return"function"==typeof o?o(e,t):o})()}present(){var e=this;return(0,M.A)(function*(){const t=yield e.lockController.lock();if(e.presented)return void t();const{presentingElement:n,el:o}=e;e.currentBreakpoint=e.initialBreakpoint;const{inline:s,delegate:i}=e.getDelegate(!0);e.ionMount.emit(),e.usersElement=yield(0,V.a)(i,o,e.component,["ion-page"],e.componentProps,s),(0,m.h)(o)?yield(0,B.e)(e.usersElement):e.keepContentsMounted||(yield(0,B.w)()),(0,c.w)(()=>e.el.classList.add("show-modal"));const a=void 0!==n;a&&"ios"===(0,c.e)(e)&&(e.statusBarStyle=yield ie.getStyle(),pe()),yield(0,p.f)(e,"modalEnter",ge,Te,{presentingEl:n,currentBreakpoint:e.initialBreakpoint,backdropBreakpoint:e.backdropBreakpoint,expandToScroll:e.expandToScroll}),typeof window<"u"&&(e.keyboardOpenCallback=()=>{e.gesture&&(e.gesture.enable(!1),(0,m.r)(()=>{e.gesture&&e.gesture.enable(!0)}))},window.addEventListener(q.KEYBOARD_DID_OPEN,e.keyboardOpenCallback)),e.isSheetModal?e.initSheetGesture():a&&e.initSwipeToClose(),e.initViewTransitionListener(),e.initParentRemovalObserver(),t()})()}initSwipeToClose(){var t,e=this;if("ios"!==(0,c.e)(this))return;const{el:n}=this,o=this.leaveAnimation||c.l.get("modalLeave",ye),s=this.animation=o(n,{presentingEl:this.presentingElement,expandToScroll:this.expandToScroll});if(!(0,T.a)(n))return void(0,T.p)(n);const a=null!==(t=this.statusBarStyle)&&void 0!==t?t:F.Default;this.gesture=((e,t,n,o)=>{const i=e.offsetHeight;let a=!1,r=!1,d=null,y=null,h=!0,S=0;const $=(0,ce.createGesture)({el:e,gestureName:"modalSwipeToClose",gesturePriority:p.O,direction:"y",threshold:10,canStart:C=>{const _=C.event.target;return null===_||!_.closest||(d=(0,T.f)(_),d?(y=(0,T.i)(d)?(0,m.g)(d).querySelector(".inner-scroll"):d,!d.querySelector("ion-refresher")&&0===y.scrollTop):null===_.closest("ion-footer"))},onStart:C=>{const{deltaY:_}=C;h=!d||!(0,T.i)(d)||d.scrollY,r=void 0!==e.canDismiss&&!0!==e.canDismiss,_>0&&d&&(0,T.d)(d),t.progressStart(!0,a?1:0)},onMove:C=>{const{deltaY:_}=C;_>0&&d&&(0,T.d)(d);const P=C.deltaY/i,I=P>=0&&r,K=I?.2:.9999,Z=I?de(P/K):P,N=(0,m.e)(1e-4,Z,K);t.progressStep(N),N>=.5&&S<.5?ae(n):N<.5&&S>=.5&&pe(),S=N},onEnd:C=>{const _=C.velocityY,P=C.deltaY/i,I=P>=0&&r,K=I?.2:.9999,Z=I?de(P/K):P,N=(0,m.e)(1e-4,Z,K),O=!I&&(C.deltaY+1e3*_)/i>=.5;let L=O?-.001:.001;O?(t.easing("cubic-bezier(0.32, 0.72, 0, 1)"),L+=(0,G.g)([0,0],[.32,.72],[0,1],[1,1],N)[0]):(t.easing("cubic-bezier(1, 0, 0.68, 0.28)"),L+=(0,G.g)([0,0],[1,0],[.68,.28],[1,1],N)[0]);const te=he(O?P*i:(1-N)*i,_);a=O,$.enable(!1),d&&(0,T.r)(d,h),t.onFinish(()=>{O||$.enable(!0)}).progressEnd(O?1:0,L,te),I&&N>K/4?fe(e,t):O&&o()}});return $})(n,s,a,()=>{this.gestureAnimationDismissing=!0,ae(this.statusBarStyle),this.animation.onFinish((0,M.A)(function*(){yield e.dismiss(void 0,p.G),e.gestureAnimationDismissing=!1}))}),this.gesture.enable(!0)}initSheetGesture(){const{wrapperEl:e,initialBreakpoint:t,backdropBreakpoint:n}=this;if(!e||void 0===t)return;const o=this.enterAnimation||c.l.get("modalEnter",ge),s=this.animation=o(this.el,{presentingEl:this.presentingElement,currentBreakpoint:t,backdropBreakpoint:n,expandToScroll:this.expandToScroll});s.progressStart(!0,1);const{gesture:i,moveSheetToBreakpoint:a}=((e,t,n,o,s,i,a=[],r,d,y,w)=>{const b={WRAPPER_KEYFRAMES:[{offset:0,transform:"translateY(0%)"},{offset:1,transform:"translateY(100%)"}],BACKDROP_KEYFRAMES:0!==s?[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1-s,opacity:0},{offset:1,opacity:0}]:[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1,opacity:.01}],CONTENT_KEYFRAMES:[{offset:0,maxHeight:"100%"},{offset:1,maxHeight:"0%"}]},E=e.querySelector("ion-content"),R=n.clientHeight;let u=o,Y=0,$=!1,C=null,_=null,P=null,I=null;const Z=a[a.length-1],N=a[0],j=i.childAnimations.find(v=>"wrapperAnimation"===v.id),O=i.childAnimations.find(v=>"backdropAnimation"===v.id),L=i.childAnimations.find(v=>"contentAnimation"===v.id),te=()=>{e.style.setProperty("pointer-events","auto"),t.style.setProperty("pointer-events","auto"),e.classList.remove(p.F)},ve=()=>{e.style.setProperty("pointer-events","none"),t.style.setProperty("pointer-events","none"),e.classList.add(p.F)},z=v=>{if(!_&&(_=Array.from(e.querySelectorAll("ion-footer")),!_.length))return;const f=e.querySelector(".ion-page");if(I=v,"stationary"===v)_.forEach(x=>{x.classList.remove("modal-footer-moving"),x.style.removeProperty("position"),x.style.removeProperty("width"),x.style.removeProperty("height"),x.style.removeProperty("top"),x.style.removeProperty("left"),f?.style.removeProperty("padding-bottom"),f?.appendChild(x)});else{let x=0;_.forEach((g,U)=>{const W=g.getBoundingClientRect(),D=document.body.getBoundingClientRect();x+=g.clientHeight;const J=W.top-D.top,X=W.left-D.left;if(g.style.setProperty("--pinned-width",`${g.clientWidth}px`),g.style.setProperty("--pinned-height",`${g.clientHeight}px`),g.style.setProperty("--pinned-top",`${J}px`),g.style.setProperty("--pinned-left",`${X}px`),0===U){P=J;const le=e.querySelector("ion-header");le&&(P-=le.clientHeight)}}),_.forEach(g=>{f?.style.setProperty("padding-bottom",`${x}px`),g.classList.add("modal-footer-moving"),g.style.setProperty("position","absolute"),g.style.setProperty("width","var(--pinned-width)"),g.style.setProperty("height","var(--pinned-height)"),g.style.setProperty("top","var(--pinned-top)"),g.style.setProperty("left","var(--pinned-left)"),document.body.appendChild(g)})}};j&&O&&(j.keyframes([...b.WRAPPER_KEYFRAMES]),O.keyframes([...b.BACKDROP_KEYFRAMES]),L?.keyframes([...b.CONTENT_KEYFRAMES]),i.progressStart(!0,1-u),u>s?te():ve()),E&&u!==Z&&r&&(E.scrollY=!1);const xe=v=>{const{breakpoint:f,canDismiss:x,breakpointOffset:g,animated:U}=v,W=x&&0===f,D=W?u:f,J=0!==D;return u=0,j&&O&&(j.keyframes([{offset:0,transform:`translateY(${100*g}%)`},{offset:1,transform:`translateY(${100*(1-D)}%)`}]),O.keyframes([{offset:0,opacity:`calc(var(--backdrop-opacity) * ${se(1-g,s)})`},{offset:1,opacity:`calc(var(--backdrop-opacity) * ${se(D,s)})`}]),L&&L.keyframes([{offset:0,maxHeight:100*(1-g)+"%"},{offset:1,maxHeight:100*D+"%"}]),i.progressStep(0)),ne.enable(!1),W?fe(e,i):J||y(),E&&(D===a[a.length-1]||!r)&&(E.scrollY=!0),!r&&0===D&&z("stationary"),new Promise(X=>{i.onFinish(()=>{J?(r||z("stationary"),j&&O?(0,m.r)(()=>{j.keyframes([...b.WRAPPER_KEYFRAMES]),O.keyframes([...b.BACKDROP_KEYFRAMES]),L?.keyframes([...b.CONTENT_KEYFRAMES]),i.progressStart(!0,1-D),u=D,w(u),u>s?te():ve(),ne.enable(!0),X()}):(ne.enable(!0),X())):X()},{oneTimeCallback:!0}).progressEnd(1,0,U?500:0)})},ne=(0,ce.createGesture)({el:n,gestureName:"modalSheet",gesturePriority:40,direction:"y",threshold:10,canStart:v=>{const f=(0,T.f)(v.event.target);if(u=d(),!r&&f)return 0===((0,T.i)(f)?(0,m.g)(f).querySelector(".inner-scroll"):f).scrollTop;if(1===u&&f){const x=(0,T.i)(f)?(0,m.g)(f).querySelector(".inner-scroll"):f;return!f.querySelector("ion-refresher")&&0===x.scrollTop}return!0},onStart:v=>{if($=void 0!==e.canDismiss&&!0!==e.canDismiss&&0===N,!r){const f=(0,T.f)(v.event.target);C=f&&(0,T.i)(f)?(0,m.g)(f).querySelector(".inner-scroll"):f}r||z("moving"),v.deltaY>0&&E&&(E.scrollY=!1),(0,m.r)(()=>{e.focus()}),i.progressStart(!0,1-u)},onMove:v=>{if(!r&&null!==P&&null!==I&&(v.currentY>=P&&"moving"===I?z("stationary"):v.currentY<P&&"stationary"===I&&z("moving")),!r&&v.deltaY<=0&&C)return;v.deltaY>0&&E&&(E.scrollY=!1);const x=a.length>1?1-a[1]:void 0,g=1-u+v.deltaY/R,U=void 0!==x&&g>=x&&$,W=U?.95:.9999,D=U&&void 0!==x?x+de((g-x)/(W-x)):g;Y=(0,m.e)(1e-4,D,W),i.progressStep(Y)},onEnd:v=>{if(!r&&v.deltaY<=0&&C&&C.scrollTop>0)return void z("stationary");const g=u-(v.deltaY+350*v.velocityY)/R,U=a.reduce((W,D)=>Math.abs(D-g)<Math.abs(W-g)?D:W);xe({breakpoint:U,breakpointOffset:Y,canDismiss:$,animated:!0})}});return{gesture:ne,moveSheetToBreakpoint:xe}})(this.el,this.backdropEl,e,t,n,s,this.sortedBreakpoints,this.expandToScroll,()=>{var r;return null!==(r=this.currentBreakpoint)&&void 0!==r?r:0},()=>this.sheetOnDismiss(),r=>{this.currentBreakpoint!==r&&(this.currentBreakpoint=r,this.ionBreakpointDidChange.emit({breakpoint:r}))});this.gesture=i,this.moveSheetToBreakpoint=a,this.gesture.enable(!0)}sheetOnDismiss(){var e=this;this.gestureAnimationDismissing=!0,this.animation.onFinish((0,M.A)(function*(){e.currentBreakpoint=0,e.ionBreakpointDidChange.emit({breakpoint:e.currentBreakpoint}),yield e.dismiss(void 0,p.G),e.gestureAnimationDismissing=!1}))}dismiss(e,t){var n=this;return(0,M.A)(function*(){var o;if(n.gestureAnimationDismissing&&t!==p.G)return!1;const s=yield n.lockController.lock();if(yield n.dismissNestedModals(),"handler"!==t&&!(yield n.checkCanDismiss(e,t)))return s(),!1;const{presentingElement:i}=n;void 0!==i&&"ios"===(0,c.e)(n)&&ae(n.statusBarStyle),typeof window<"u"&&n.keyboardOpenCallback&&(window.removeEventListener(q.KEYBOARD_DID_OPEN,n.keyboardOpenCallback),n.keyboardOpenCallback=void 0);const r=yield(0,p.g)(n,e,t,"modalLeave",ye,Pe,{presentingEl:i,currentBreakpoint:null!==(o=n.currentBreakpoint)&&void 0!==o?o:n.initialBreakpoint,backdropBreakpoint:n.backdropBreakpoint,expandToScroll:n.expandToScroll});if(r){const{delegate:d}=n.getDelegate();yield(0,V.d)(d,n.usersElement),(0,c.w)(()=>n.el.classList.remove("show-modal")),n.animation&&n.animation.destroy(),n.gesture&&n.gesture.destroy(),n.cleanupViewTransitionListener(),n.cleanupParentRemovalObserver()}return n.currentBreakpoint=void 0,n.animation=void 0,s(),r})()}onDidDismiss(){return(0,p.h)(this.el,"ionModalDidDismiss")}onWillDismiss(){return(0,p.h)(this.el,"ionModalWillDismiss")}setCurrentBreakpoint(e){var t=this;return(0,M.A)(function*(){if(!t.isSheetModal)return void(0,c.m)("[ion-modal] - setCurrentBreakpoint is only supported on sheet modals.");if(!t.breakpoints.includes(e))return void(0,c.m)(`[ion-modal] - Attempted to set invalid breakpoint value ${e}. Please double check that the breakpoint value is part of your defined breakpoints.`);const{currentBreakpoint:n,moveSheetToBreakpoint:o,canDismiss:s,breakpoints:i,animated:a}=t;n!==e&&o&&(t.sheetTransition=o({breakpoint:e,breakpointOffset:1-n,canDismiss:void 0!==s&&!0!==s&&0===i[0],animated:a}),yield t.sheetTransition,t.sheetTransition=void 0)})()}getCurrentBreakpoint(){var e=this;return(0,M.A)(function*(){return e.currentBreakpoint})()}moveToNextBreakpoint(){var e=this;return(0,M.A)(function*(){const{breakpoints:t,currentBreakpoint:n}=e;if(!t||null==n)return!1;const o=t.filter(r=>0!==r),i=(o.indexOf(n)+1)%o.length,a=o[i];return yield e.setCurrentBreakpoint(a),!0})()}initViewTransitionListener(){"ios"!==(0,c.e)(this)||!this.presentingElement||this.enterAnimation||this.leaveAnimation||(this.currentViewIsPortrait=window.innerWidth<768)}handleViewTransition(){const e=window.innerWidth<768;if(this.currentViewIsPortrait===e)return;this.viewTransitionAnimation&&(this.viewTransitionAnimation.destroy(),this.viewTransitionAnimation=void 0);const{presentingElement:t}=this;if(!t)return;let n;n=this.currentViewIsPortrait&&!e?((e,t,n=300)=>{const{presentingEl:o}=t;if(!o)return(0,l.c)("portrait-to-landscape-transition");const s="ION-MODAL"===o.tagName&&void 0!==o.presentingElement,i=(0,m.g)(o),a=document.body,r=(0,l.c)("portrait-to-landscape-transition").addElement(e).easing("cubic-bezier(0.32,0.72,0,1)").duration(n),d=(0,l.c)().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"});if(s){const w="translateY(-10px) scale(0.915)",h="translateY(0px) scale(1)";d.addElement(o).afterStyles({transform:h}).fromTo("transform",w,h).fromTo("filter","contrast(0.85)","contrast(1)");const S=(0,l.c)().addElement(i.querySelector(".modal-shadow")).afterStyles({transform:h,opacity:"0"}).fromTo("transform",w,h);r.addAnimation([d,S])}else{const y=(0,m.g)(e),w=(0,l.c)().addElement(y.querySelectorAll(".modal-wrapper, .modal-shadow")).fromTo("opacity","1","1"),h=(0,l.c)().addElement(y.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)","var(--backdrop-opacity)"),E=`translateY(${CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px"}) scale(0.915)`;d.addElement(o).afterStyles({transform:"translateY(0px) scale(1)","border-radius":"0px"}).beforeAddWrite(()=>a.style.setProperty("background-color","")).fromTo("transform",E,"translateY(0px) scale(1)").fromTo("filter","contrast(0.85)","contrast(1)").fromTo("border-radius","10px 10px 0 0","0px"),r.addAnimation([d,w,h])}return r})(this.el,{presentingEl:t}):((e,t,n=300)=>{const{presentingEl:o}=t;if(!o)return(0,l.c)("landscape-to-portrait-transition");const s="ION-MODAL"===o.tagName&&void 0!==o.presentingElement,i=(0,m.g)(o),a=document.body,r=(0,l.c)("landscape-to-portrait-transition").addElement(e).easing("cubic-bezier(0.32,0.72,0,1)").duration(n),d=(0,l.c)().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"});if(s){const w="translateY(-10px) scale(0.915)",h="translateY(0) scale(1)";d.addElement(o).afterStyles({transform:h}).fromTo("transform",w,h);const S=(0,l.c)().addElement(i.querySelector(".modal-shadow")).afterStyles({transform:h,opacity:"0"}).fromTo("transform",w,h);r.addAnimation([d,S])}else{const y=(0,m.g)(e),w=(0,l.c)().addElement(y.querySelectorAll(".modal-wrapper, .modal-shadow")).fromTo("opacity","1","1"),h=(0,l.c)().addElement(y.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)","var(--backdrop-opacity)"),E=`translateY(${CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px"}) scale(0.915)`;d.addElement(o).afterStyles({transform:E}).beforeAddWrite(()=>a.style.setProperty("background-color","black")).keyframes([{offset:0,transform:"translateY(0px) scale(1)",filter:"contrast(1)",borderRadius:"0px"},{offset:.2,transform:"translateY(0px) scale(1)",filter:"contrast(1)",borderRadius:"10px 10px 0 0"},{offset:1,transform:E,filter:"contrast(0.85)",borderRadius:"10px 10px 0 0"}]),r.addAnimation([d,w,h])}return r})(this.el,{presentingEl:t}),this.currentViewIsPortrait=e,this.viewTransitionAnimation=n,n.play().then(()=>{this.viewTransitionAnimation=void 0,this.reinitSwipeToClose()})}cleanupViewTransitionListener(){this.resizeTimeout&&(clearTimeout(this.resizeTimeout),this.resizeTimeout=void 0),this.viewTransitionAnimation&&(this.viewTransitionAnimation.destroy(),this.viewTransitionAnimation=void 0)}reinitSwipeToClose(){"ios"!==(0,c.e)(this)||!this.presentingElement||(this.gesture&&(this.gesture.destroy(),this.gesture=void 0),this.animation&&(this.animation.progressEnd(0,0,0),this.animation.destroy(),this.animation=void 0),(0,m.r)(()=>{this.ensureCorrectModalPosition(),this.initSwipeToClose()}))}ensureCorrectModalPosition(){const{el:e,presentingElement:t}=this,o=(0,m.g)(e).querySelector(".modal-wrapper");if(o&&(o.style.transform="translateY(0vh)",o.style.opacity="1"),"ION-MODAL"===t?.tagName)if(window.innerWidth<768){const i=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px";t.style.transform=`translateY(${i}) scale(0.915)`}else t.style.transform="translateY(0px) scale(1)"}dismissNestedModals(){var e=this;return(0,M.A)(function*(){const t=document.querySelectorAll(`ion-modal[data-parent-ion-modal="${e.el.id}"]`);t?.forEach(function(){var n=(0,M.A)(function*(o){yield o.dismiss(void 0,"parent-dismissed")});return function(o){return n.apply(this,arguments)}}())})()}initParentRemovalObserver(){typeof MutationObserver>"u"||typeof window>"u"||!this.cachedOriginalParent||this.cachedOriginalParent.nodeType===Node.DOCUMENT_NODE||this.cachedOriginalParent.nodeType===Node.DOCUMENT_FRAGMENT_NODE||(this.parentRemovalObserver=new MutationObserver(e=>{e.forEach(t=>{"childList"===t.type&&t.removedNodes.length>0&&(Array.from(t.removedNodes).some(s=>{var i,a;const r=s===this.cachedOriginalParent,d=!!this.cachedOriginalParent&&(null===(a=(i=s).contains)||void 0===a?void 0:a.call(i,this.cachedOriginalParent));return r||d})||this.cachedOriginalParent&&!this.cachedOriginalParent.isConnected)&&(this.dismiss(void 0,"parent-removed"),this.cachedOriginalParent=void 0)})}),this.parentRemovalObserver.observe(document.body,{childList:!0,subtree:!0}))}cleanupParentRemovalObserver(){var e;null===(e=this.parentRemovalObserver)||void 0===e||e.disconnect(),this.parentRemovalObserver=void 0}render(){const{handle:e,isSheetModal:t,presentingElement:n,htmlAttributes:o,handleBehavior:s,inheritedAttributes:i,focusTrap:a,expandToScroll:r}=this,d=!1!==e&&t,y=(0,c.e)(this),w=void 0!==n&&"ios"===y,h="cycle"===s;return(0,c.h)(c.j,Object.assign({key:"9e9a7bd591eb17a225a00b4fa2e379e94601d17f","no-router":!0,tabIndex:h&&t&&d?0:-1},o,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign({[y]:!0,"modal-default":!w&&!t,"modal-card":w,"modal-sheet":t,"modal-no-expand-scroll":t&&!r,"overlay-hidden":!0,[p.F]:!1===a},(0,A.g)(this.cssClass)),onIonBackdropTap:this.onBackdropTap,onIonModalDidPresent:this.onLifecycle,onIonModalWillPresent:this.onLifecycle,onIonModalWillDismiss:this.onLifecycle,onIonModalDidDismiss:this.onLifecycle,onFocus:this.onModalFocus}),(0,c.h)("ion-backdrop",{key:"e5eae2c14f830f75e308fcd7f4c10c86fac5b962",ref:b=>this.backdropEl=b,visible:this.showBackdrop,tappable:this.backdropDismiss,part:"backdrop"}),"ios"===y&&(0,c.h)("div",{key:"e268f9cd310c3cf4e051b5b92524ce4fb70d005e",class:"modal-shadow"}),(0,c.h)("div",Object.assign({key:"9c380f36c18144c153077b15744d1c3346bce63e",role:"dialog"},i,{"aria-modal":"true",class:"modal-wrapper ion-overlay-wrapper",part:"content",ref:b=>this.wrapperEl=b}),d&&(0,c.h)("button",{key:"2d5ee6d5959d97309c306e8ce72eb0f2c19be144",class:"modal-handle",tabIndex:h?0:-1,"aria-label":"Activate to adjust the size of the dialog overlaying the screen",onClick:h?this.onHandleClick:void 0,part:"handle",ref:b=>this.dragHandleEl=b}),(0,c.h)("slot",{key:"5590434c35ea04c42fc006498bc189038e15a298",onSlotchange:this.onSlotChange})))}get el(){return(0,c.k)(this)}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}},Re={ionModalDidPresent:"ionViewDidEnter",ionModalWillPresent:"ionViewWillEnter",ionModalWillDismiss:"ionViewWillLeave",ionModalDidDismiss:"ionViewDidLeave"};var e;be.style={ios:':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.4)}:host(.modal-card),:host(.modal-sheet){--border-radius:10px}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:10px}}.modal-wrapper{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0)}@media screen and (max-width: 767px){@supports (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - max(30px, var(--ion-safe-area-top)) - 10px)}}@supports not (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - 40px)}}:host(.modal-card) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}:host(.modal-card){--backdrop-opacity:0;--width:100%;-ms-flex-align:end;align-items:flex-end}:host(.modal-card) .modal-shadow{display:none}:host(.modal-card) ion-backdrop{pointer-events:none}}@media screen and (min-width: 768px){:host(.modal-card){--width:calc(100% - 120px);--height:calc(100% - (120px + var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));--max-width:720px;--max-height:1000px;--backdrop-opacity:0;--box-shadow:0px 0px 30px 10px rgba(0, 0, 0, 0.1);-webkit-transition:all 0.5s ease-in-out;transition:all 0.5s ease-in-out}:host(.modal-card) .modal-wrapper{-webkit-box-shadow:none;box-shadow:none}:host(.modal-card) .modal-shadow{-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow)}}:host(.modal-sheet) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}',md:':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:2px;--box-shadow:0 28px 48px rgba(0, 0, 0, 0.4)}}.modal-wrapper{-webkit-transform:translate3d(0,  40px,  0);transform:translate3d(0,  40px,  0);opacity:0.01}'}},4576:(ke,Q,k)=>{k.d(Q,{c:()=>T,g:()=>m,h:()=>c,o:()=>re});var M=k(467);const c=(p,A)=>null!==A.closest(p),T=(p,A)=>"string"==typeof p&&p.length>0?Object.assign({"ion-color":!0,[`ion-color-${p}`]:!0},A):A,m=p=>{const A={};return(p=>void 0!==p?(Array.isArray(p)?p:p.split(" ")).filter(B=>null!=B).map(B=>B.trim()).filter(B=>""!==B):[])(p).forEach(B=>A[B]=!0),A},oe=/^[a-z][a-z0-9+\-.]*:/,re=function(){var p=(0,M.A)(function*(A,B,q,l){if(null!=A&&"#"!==A[0]&&!oe.test(A)){const G=document.querySelector("ion-router");if(G)return B?.preventDefault(),G.push(A,q,l)}return!1});return function(B,q,l,G){return p.apply(this,arguments)}}()}}]);