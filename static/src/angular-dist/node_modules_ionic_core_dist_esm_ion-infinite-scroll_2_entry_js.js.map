{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-infinite-scroll_2_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC0K;AACpD;AACtB;AACjE;AAE/B,MAAM0B,iBAAiB,GAAG,qFAAqF;AAE/G,MAAMC,cAAc,GAAG,MAAM;EACzBC,WAAWA,CAACC,OAAO,EAAE;IACjB5B,qDAAgB,CAAC,IAAI,EAAE4B,OAAO,CAAC;IAC/B,IAAI,CAACC,WAAW,GAAG3B,qDAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IACtD,IAAI,CAAC4B,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,KAAK,GAAG,CAAC;IACd;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAG,MAAM;MAClB,MAAMC,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC9B,IAAI,CAACA,QAAQ,IAAI,CAAC,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE;QAC/B,OAAO,CAAC;MACZ;MACA,MAAMC,cAAc,GAAG,IAAI,CAACC,EAAE,CAACC,YAAY;MAC3C,IAAIF,cAAc,KAAK,CAAC,EAAE;QACtB;QACA,OAAO,CAAC;MACZ;MACA,MAAMG,SAAS,GAAGL,QAAQ,CAACK,SAAS;MACpC,MAAMC,YAAY,GAAGN,QAAQ,CAACM,YAAY;MAC1C,MAAMC,MAAM,GAAGP,QAAQ,CAACI,YAAY;MACpC,MAAMR,SAAS,GAAG,IAAI,CAACJ,KAAK,KAAK,CAAC,GAAGe,MAAM,GAAG,IAAI,CAACf,KAAK,GAAG,IAAI,CAACD,KAAK;MACrE,MAAMiB,oBAAoB,GAAG,IAAI,CAACV,QAAQ,KAAK,QAAQ,GACjDQ,YAAY,GAAGJ,cAAc,GAAGG,SAAS,GAAGT,SAAS,GAAGW,MAAM,GAC9DF,SAAS,GAAGH,cAAc,GAAGN,SAAS;MAC5C,IAAIY,oBAAoB,GAAG,CAAC,EAAE;QAC1B,IAAI,CAAC,IAAI,CAACf,OAAO,EAAE;UACf,IAAI,CAACE,SAAS,GAAG,IAAI;UACrB,IAAI,CAACF,OAAO,GAAG,IAAI;UACnB,IAAI,CAACH,WAAW,CAACmB,IAAI,CAAC,CAAC;UACvB,OAAO,CAAC;QACZ;MACJ;MACA,OAAO,CAAC;IACZ,CAAC;EACL;EACAC,gBAAgBA,CAAA,EAAG;IACf,MAAMC,GAAG,GAAG,IAAI,CAACf,SAAS;IAC1B,IAAIe,GAAG,CAACC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MAC3B,IAAI,CAACrB,KAAK,GAAG,CAAC;MACd,IAAI,CAACC,KAAK,GAAGqB,UAAU,CAACF,GAAG,CAAC,GAAG,GAAG;IACtC,CAAC,MACI;MACD,IAAI,CAACpB,KAAK,GAAGsB,UAAU,CAACF,GAAG,CAAC;MAC5B,IAAI,CAACnB,KAAK,GAAG,CAAC;IAClB;EACJ;EACAsB,eAAeA,CAAA,EAAG;IACd,MAAMjB,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAIA,QAAQ,EAAE;MACV,IAAI,CAACF,SAAS,GAAG,KAAK;MACtB,IAAI,CAACD,MAAM,GAAG,KAAK;IACvB;IACA,IAAI,CAACqB,kBAAkB,CAAC,CAAClB,QAAQ,CAAC;EACtC;EACMmB,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,oMAAA;MACtB,MAAMC,SAAS,GAAG1C,qDAAqB,CAACwC,KAAI,CAACd,EAAE,CAAC;MAChD,IAAI,CAACgB,SAAS,EAAE;QACZxC,qDAAuB,CAACsC,KAAI,CAACd,EAAE,CAAC;QAChC;MACJ;MACAc,KAAI,CAACjB,QAAQ,SAASnB,qDAAgB,CAACsC,SAAS,CAAC;MACjDF,KAAI,CAACP,gBAAgB,CAAC,CAAC;MACvBO,KAAI,CAACH,eAAe,CAAC,CAAC;MACtB,IAAIG,KAAI,CAACnB,QAAQ,KAAK,KAAK,EAAE;QACzBjC,qDAAS,CAAC,MAAM;UACZ,IAAIoD,KAAI,CAACjB,QAAQ,EAAE;YACfiB,KAAI,CAACjB,QAAQ,CAACK,SAAS,GAAGY,KAAI,CAACjB,QAAQ,CAACM,YAAY,GAAGW,KAAI,CAACjB,QAAQ,CAACoB,YAAY;UACrF;QACJ,CAAC,CAAC;MACN;IAAC;EACL;EACAC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACN,kBAAkB,CAAC,KAAK,CAAC;IAC9B,IAAI,CAACf,QAAQ,GAAGsB,SAAS;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUC,QAAQA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAN,oMAAA;MACb,MAAMlB,QAAQ,GAAGwB,MAAI,CAACxB,QAAQ;MAC9B,IAAI,CAACwB,MAAI,CAAC7B,SAAS,IAAI,CAACK,QAAQ,EAAE;QAC9B;MACJ;MACAwB,MAAI,CAAC7B,SAAS,GAAG,KAAK;MACtB,IAAI6B,MAAI,CAAC1B,QAAQ,KAAK,KAAK,EAAE;QACzB;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACY0B,MAAI,CAAC9B,MAAM,GAAG,IAAI;QAClB;QACA;QACA,MAAM+B,IAAI,GAAGzB,QAAQ,CAACM,YAAY,GAAGN,QAAQ,CAACK,SAAS;QACvD;QACAqB,qBAAqB,CAAC,MAAM;UACxB3D,qDAAQ,CAAC,MAAM;YACX;YACA,MAAMuC,YAAY,GAAGN,QAAQ,CAACM,YAAY;YAC1C;YACA,MAAMqB,YAAY,GAAGrB,YAAY,GAAGmB,IAAI;YACxC;YACAC,qBAAqB,CAAC,MAAM;cACxB7D,qDAAS,CAAC,MAAM;gBACZmC,QAAQ,CAACK,SAAS,GAAGsB,YAAY;gBACjCH,MAAI,CAAC9B,MAAM,GAAG,KAAK;gBACnB8B,MAAI,CAAC/B,OAAO,GAAG,KAAK;cACxB,CAAC,CAAC;YACN,CAAC,CAAC;UACN,CAAC,CAAC;QACN,CAAC,CAAC;MACN,CAAC,MACI;QACD+B,MAAI,CAAC/B,OAAO,GAAG,KAAK;MACxB;IAAC;EACL;EACAQ,QAAQA,CAAA,EAAG;IACP,OAAO,CAAC,IAAI,CAACJ,QAAQ,IAAI,CAAC,IAAI,CAACH,MAAM,IAAI,CAAC,CAAC,IAAI,CAACM,QAAQ,IAAI,CAAC,IAAI,CAACL,SAAS;EAC/E;EACAoB,kBAAkBA,CAACa,YAAY,EAAE;IAC7B,IAAI,IAAI,CAAC5B,QAAQ,EAAE;MACf,IAAI4B,YAAY,EAAE;QACd,IAAI,CAAC5B,QAAQ,CAAC6B,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC9B,QAAQ,CAAC;MAC3D,CAAC,MACI;QACD,IAAI,CAACC,QAAQ,CAAC8B,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC/B,QAAQ,CAAC;MAC9D;IACJ;EACJ;EACAgC,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAG/D,qDAAU,CAAC,IAAI,CAAC;IAC7B,MAAM4B,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,OAAQ3B,qDAAC,CAACE,iDAAI,EAAE;MAAE6D,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACF,IAAI,GAAG,IAAI;QACZ,yBAAyB,EAAE,IAAI,CAACrC,SAAS;QACzC,yBAAyB,EAAE,CAACE;MAChC;IAAE,CAAC,CAAC;EACZ;EACA,IAAIM,EAAEA,CAAA,EAAG;IAAE,OAAO7B,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW6D,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,WAAW,EAAE,CAAC,kBAAkB,CAAC;MACjC,UAAU,EAAE,CAAC,iBAAiB;IAClC,CAAC;EAAE;AACP,CAAC;AACDhD,cAAc,CAACiD,KAAK,GAAGlD,iBAAiB;AAExC,MAAMmD,2BAA2B,GAAG,m1CAAm1C;AAEv3C,MAAMC,0BAA0B,GAAG,00CAA00C;AAE72C,MAAMC,qBAAqB,GAAG,MAAM;EAChCnD,WAAWA,CAACC,OAAO,EAAE;IACjB5B,qDAAgB,CAAC,IAAI,EAAE4B,OAAO,CAAC;IAC/B,IAAI,CAACmD,iBAAiB,GAAGhE,iDAAM,CAACiE,GAAG,CAAC,2BAA2B,EAAE1D,kDAA2B,CAAC;EACjG;EACA2D,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACC,cAAc,KAAKrB,SAAS,EAAE;MACnC,MAAMU,IAAI,GAAG/D,qDAAU,CAAC,IAAI,CAAC;MAC7B,IAAI,CAAC0E,cAAc,GAAGnE,iDAAM,CAACiE,GAAG,CAAC,wBAAwB,EAAEjE,iDAAM,CAACiE,GAAG,CAAC,SAAS,EAAET,IAAI,KAAK,KAAK,GAAG,OAAO,GAAG,UAAU,CAAC,CAAC;IAC5H;EACJ;EACAY,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAEJ,iBAAiB;MAAEK;IAAY,CAAC,GAAG,IAAI;IAC/C,IAAIL,iBAAiB,EAAE;MACnB,OAAOtE,qDAAC,CAAC,KAAK,EAAE;QAAEgE,KAAK,EAAE,uBAAuB;QAAEY,SAAS,EAAE7D,sDAAiB,CAAC4D,WAAW;MAAE,CAAC,CAAC;IAClG;IACA,OAAO3E,qDAAC,CAAC,KAAK,EAAE;MAAEgE,KAAK,EAAE;IAAwB,CAAC,EAAE,IAAI,CAACW,WAAW,CAAC;EACzE;EACAd,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAG/D,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,qDAAC,CAACE,iDAAI,EAAE;MAAE6D,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACF,IAAI,GAAG,IAAI;QACZ;QACA,CAAC,2BAA2BA,IAAI,EAAE,GAAG;MACzC;IAAE,CAAC,EAAE9D,qDAAC,CAAC,KAAK,EAAE;MAAE+D,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAmB,CAAC,EAAE,IAAI,CAACS,cAAc,IAAKzE,qDAAC,CAAC,KAAK,EAAE;MAAE+D,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAA2B,CAAC,EAAEhE,qDAAC,CAAC,aAAa,EAAE;MAAE+D,GAAG,EAAE,0CAA0C;MAAEc,IAAI,EAAE,IAAI,CAACJ;IAAe,CAAC,CAAC,CAAE,EAAE,IAAI,CAACE,WAAW,KAAKvB,SAAS,IAAI,IAAI,CAACsB,iBAAiB,CAAC,CAAC,CAAC,CAAC;EAC/X;AACJ,CAAC;AACDL,qBAAqB,CAACH,KAAK,GAAG;EAC1BY,GAAG,EAAEX,2BAA2B;EAChCY,EAAE,EAAEX;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-infinite-scroll_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, w as writeTask, f as readTask, e as getIonMode, h, j as Host, k as getElement, l as config } from './index-B_U9CtaY.js';\nimport { f as findClosestIonContent, p as printIonContentErrorMsg, g as getScrollElement } from './index-BlJTBdxG.js';\nimport { E as ENABLE_HTML_CONTENT_DEFAULT, a as sanitizeDOMString } from './config-AaTyISnm.js';\nimport './helpers-1O4D2b7y.js';\n\nconst infiniteScrollCss = \"ion-infinite-scroll{display:none;width:100%}.infinite-scroll-enabled{display:block}\";\n\nconst InfiniteScroll = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionInfinite = createEvent(this, \"ionInfinite\", 7);\n        this.thrPx = 0;\n        this.thrPc = 0;\n        /**\n         * didFire exists so that ionInfinite\n         * does not fire multiple times if\n         * users continue to scroll after\n         * scrolling into the infinite\n         * scroll threshold.\n         */\n        this.didFire = false;\n        this.isBusy = false;\n        this.isLoading = false;\n        /**\n         * The threshold distance from the bottom\n         * of the content to call the `infinite` output event when scrolled.\n         * The threshold value can be either a percent, or\n         * in pixels. For example, use the value of `10%` for the `infinite`\n         * output event to get called when the user has scrolled 10%\n         * from the bottom of the page. Use the value `100px` when the\n         * scroll is within 100 pixels from the bottom of the page.\n         */\n        this.threshold = '15%';\n        /**\n         * If `true`, the infinite scroll will be hidden and scroll event listeners\n         * will be removed.\n         *\n         * Set this to true to disable the infinite scroll from actively\n         * trying to receive new data while scrolling. This is useful\n         * when it is known that there is no more data that can be added, and\n         * the infinite scroll is no longer needed.\n         */\n        this.disabled = false;\n        /**\n         * The position of the infinite scroll element.\n         * The value can be either `top` or `bottom`.\n         */\n        this.position = 'bottom';\n        this.onScroll = () => {\n            const scrollEl = this.scrollEl;\n            if (!scrollEl || !this.canStart()) {\n                return 1;\n            }\n            const infiniteHeight = this.el.offsetHeight;\n            if (infiniteHeight === 0) {\n                // if there is no height of this element then do nothing\n                return 2;\n            }\n            const scrollTop = scrollEl.scrollTop;\n            const scrollHeight = scrollEl.scrollHeight;\n            const height = scrollEl.offsetHeight;\n            const threshold = this.thrPc !== 0 ? height * this.thrPc : this.thrPx;\n            const distanceFromInfinite = this.position === 'bottom'\n                ? scrollHeight - infiniteHeight - scrollTop - threshold - height\n                : scrollTop - infiniteHeight - threshold;\n            if (distanceFromInfinite < 0) {\n                if (!this.didFire) {\n                    this.isLoading = true;\n                    this.didFire = true;\n                    this.ionInfinite.emit();\n                    return 3;\n                }\n            }\n            return 4;\n        };\n    }\n    thresholdChanged() {\n        const val = this.threshold;\n        if (val.lastIndexOf('%') > -1) {\n            this.thrPx = 0;\n            this.thrPc = parseFloat(val) / 100;\n        }\n        else {\n            this.thrPx = parseFloat(val);\n            this.thrPc = 0;\n        }\n    }\n    disabledChanged() {\n        const disabled = this.disabled;\n        if (disabled) {\n            this.isLoading = false;\n            this.isBusy = false;\n        }\n        this.enableScrollEvents(!disabled);\n    }\n    async connectedCallback() {\n        const contentEl = findClosestIonContent(this.el);\n        if (!contentEl) {\n            printIonContentErrorMsg(this.el);\n            return;\n        }\n        this.scrollEl = await getScrollElement(contentEl);\n        this.thresholdChanged();\n        this.disabledChanged();\n        if (this.position === 'top') {\n            writeTask(() => {\n                if (this.scrollEl) {\n                    this.scrollEl.scrollTop = this.scrollEl.scrollHeight - this.scrollEl.clientHeight;\n                }\n            });\n        }\n    }\n    disconnectedCallback() {\n        this.enableScrollEvents(false);\n        this.scrollEl = undefined;\n    }\n    /**\n     * Call `complete()` within the `ionInfinite` output event handler when\n     * your async operation has completed. For example, the `loading`\n     * state is while the app is performing an asynchronous operation,\n     * such as receiving more data from an AJAX request to add more items\n     * to a data list. Once the data has been received and UI updated, you\n     * then call this method to signify that the loading has completed.\n     * This method will change the infinite scroll's state from `loading`\n     * to `enabled`.\n     */\n    async complete() {\n        const scrollEl = this.scrollEl;\n        if (!this.isLoading || !scrollEl) {\n            return;\n        }\n        this.isLoading = false;\n        if (this.position === 'top') {\n            /**\n             * New content is being added at the top, but the scrollTop position stays the same,\n             * which causes a scroll jump visually. This algorithm makes sure to prevent this.\n             * (Frame 1)\n             *    - complete() is called, but the UI hasn't had time to update yet.\n             *    - Save the current content dimensions.\n             *    - Wait for the next frame using _dom.read, so the UI will be updated.\n             * (Frame 2)\n             *    - Read the new content dimensions.\n             *    - Calculate the height difference and the new scroll position.\n             *    - Delay the scroll position change until other possible dom reads are done using _dom.write to be performant.\n             * (Still frame 2, if I'm correct)\n             *    - Change the scroll position (= visually maintain the scroll position).\n             *    - Change the state to re-enable the InfiniteScroll.\n             *    - This should be after changing the scroll position, or it could\n             *    cause the InfiniteScroll to be triggered again immediately.\n             * (Frame 3)\n             *    Done.\n             */\n            this.isBusy = true;\n            // ******** DOM READ ****************\n            // Save the current content dimensions before the UI updates\n            const prev = scrollEl.scrollHeight - scrollEl.scrollTop;\n            // ******** DOM READ ****************\n            requestAnimationFrame(() => {\n                readTask(() => {\n                    // UI has updated, save the new content dimensions\n                    const scrollHeight = scrollEl.scrollHeight;\n                    // New content was added on top, so the scroll position should be changed immediately to prevent it from jumping around\n                    const newScrollTop = scrollHeight - prev;\n                    // ******** DOM WRITE ****************\n                    requestAnimationFrame(() => {\n                        writeTask(() => {\n                            scrollEl.scrollTop = newScrollTop;\n                            this.isBusy = false;\n                            this.didFire = false;\n                        });\n                    });\n                });\n            });\n        }\n        else {\n            this.didFire = false;\n        }\n    }\n    canStart() {\n        return !this.disabled && !this.isBusy && !!this.scrollEl && !this.isLoading;\n    }\n    enableScrollEvents(shouldListen) {\n        if (this.scrollEl) {\n            if (shouldListen) {\n                this.scrollEl.addEventListener('scroll', this.onScroll);\n            }\n            else {\n                this.scrollEl.removeEventListener('scroll', this.onScroll);\n            }\n        }\n    }\n    render() {\n        const mode = getIonMode(this);\n        const disabled = this.disabled;\n        return (h(Host, { key: 'e844956795f69be33396ce4480aa7a54ad01b28c', class: {\n                [mode]: true,\n                'infinite-scroll-loading': this.isLoading,\n                'infinite-scroll-enabled': !disabled,\n            } }));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"threshold\": [\"thresholdChanged\"],\n        \"disabled\": [\"disabledChanged\"]\n    }; }\n};\nInfiniteScroll.style = infiniteScrollCss;\n\nconst infiniteScrollContentIosCss = \"ion-infinite-scroll-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;min-height:84px;text-align:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.infinite-loading{margin-left:0;margin-right:0;margin-top:0;margin-bottom:32px;display:none;width:100%}.infinite-loading-text{-webkit-margin-start:32px;margin-inline-start:32px;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:4px;margin-bottom:0}.infinite-scroll-loading ion-infinite-scroll-content>.infinite-loading{display:block}.infinite-scroll-content-ios .infinite-loading-text{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}.infinite-scroll-content-ios .infinite-loading-spinner .spinner-lines-ios line,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-lines-small-ios line,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-crescent circle{stroke:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}.infinite-scroll-content-ios .infinite-loading-spinner .spinner-bubbles circle,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-circles circle,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-dots circle{fill:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}\";\n\nconst infiniteScrollContentMdCss = \"ion-infinite-scroll-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;min-height:84px;text-align:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.infinite-loading{margin-left:0;margin-right:0;margin-top:0;margin-bottom:32px;display:none;width:100%}.infinite-loading-text{-webkit-margin-start:32px;margin-inline-start:32px;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:4px;margin-bottom:0}.infinite-scroll-loading ion-infinite-scroll-content>.infinite-loading{display:block}.infinite-scroll-content-md .infinite-loading-text{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}.infinite-scroll-content-md .infinite-loading-spinner .spinner-lines-md line,.infinite-scroll-content-md .infinite-loading-spinner .spinner-lines-small-md line,.infinite-scroll-content-md .infinite-loading-spinner .spinner-crescent circle{stroke:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}.infinite-scroll-content-md .infinite-loading-spinner .spinner-bubbles circle,.infinite-scroll-content-md .infinite-loading-spinner .spinner-circles circle,.infinite-scroll-content-md .infinite-loading-spinner .spinner-dots circle{fill:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}\";\n\nconst InfiniteScrollContent = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.customHTMLEnabled = config.get('innerHTMLTemplatesEnabled', ENABLE_HTML_CONTENT_DEFAULT);\n    }\n    componentDidLoad() {\n        if (this.loadingSpinner === undefined) {\n            const mode = getIonMode(this);\n            this.loadingSpinner = config.get('infiniteLoadingSpinner', config.get('spinner', mode === 'ios' ? 'lines' : 'crescent'));\n        }\n    }\n    renderLoadingText() {\n        const { customHTMLEnabled, loadingText } = this;\n        if (customHTMLEnabled) {\n            return h(\"div\", { class: \"infinite-loading-text\", innerHTML: sanitizeDOMString(loadingText) });\n        }\n        return h(\"div\", { class: \"infinite-loading-text\" }, this.loadingText);\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '7c16060dcfe2a0b0fb3e2f8f4c449589a76f1baa', class: {\n                [mode]: true,\n                // Used internally for styling\n                [`infinite-scroll-content-${mode}`]: true,\n            } }, h(\"div\", { key: 'a94f4d8746e053dc718f97520bd7e48cb316443a', class: \"infinite-loading\" }, this.loadingSpinner && (h(\"div\", { key: '10143d5d2a50a2a2bc5de1cee8e7ab51263bcf23', class: \"infinite-loading-spinner\" }, h(\"ion-spinner\", { key: '8846e88191690d9c61a0b462889ed56fbfed8b0d', name: this.loadingSpinner }))), this.loadingText !== undefined && this.renderLoadingText())));\n    }\n};\nInfiniteScrollContent.style = {\n    ios: infiniteScrollContentIosCss,\n    md: infiniteScrollContentMdCss\n};\n\nexport { InfiniteScroll as ion_infinite_scroll, InfiniteScrollContent as ion_infinite_scroll_content };\n"], "names": ["r", "registerInstance", "d", "createEvent", "w", "writeTask", "f", "readTask", "e", "getIonMode", "h", "j", "Host", "k", "getElement", "l", "config", "findClosestIonContent", "p", "printIonContentErrorMsg", "g", "getScrollElement", "E", "ENABLE_HTML_CONTENT_DEFAULT", "a", "sanitizeDOMString", "infiniteScrollCss", "InfiniteScroll", "constructor", "hostRef", "ionInfinite", "thrPx", "thrPc", "<PERSON><PERSON><PERSON>", "isBusy", "isLoading", "threshold", "disabled", "position", "onScroll", "scrollEl", "canStart", "infiniteHeight", "el", "offsetHeight", "scrollTop", "scrollHeight", "height", "distanceFromInfinite", "emit", "thresholdChanged", "val", "lastIndexOf", "parseFloat", "disabled<PERSON><PERSON>ed", "enableScrollEvents", "connectedCallback", "_this", "_asyncToGenerator", "contentEl", "clientHeight", "disconnectedCallback", "undefined", "complete", "_this2", "prev", "requestAnimationFrame", "newScrollTop", "<PERSON><PERSON><PERSON><PERSON>", "addEventListener", "removeEventListener", "render", "mode", "key", "class", "watchers", "style", "infiniteScrollContentIosCss", "infiniteScrollContentMdCss", "InfiniteScrollContent", "customHTMLEnabled", "get", "componentDidLoad", "loadingSpinner", "renderLoadingText", "loadingText", "innerHTML", "name", "ios", "md", "ion_infinite_scroll", "ion_infinite_scroll_content"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}