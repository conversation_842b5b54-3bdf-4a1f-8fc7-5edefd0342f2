"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[7676],{7676:(P,x,g)=>{g.r(x),g.d(x,{ion_input_otp:()=>d});var k=g(467),p=g(2734),I=g(1837),E=g(647),C=g(4576);const d=class{constructor(i){(0,p.r)(this,i),this.ionInput=(0,p.d)(this,"ionInput",7),this.ionChange=(0,p.d)(this,"ionChange",7),this.ionComplete=(0,p.d)(this,"ionComplete",7),this.ionBlur=(0,p.d)(this,"ionBlur",7),this.ionFocus=(0,p.d)(this,"ionFocus",7),this.inheritedAttributes={},this.inputRefs=[],this.inputId="ion-input-otp-"+c++,this.parsedSeparators=[],this.isKeyboardNavigation=!1,this.inputValues=[],this.hasFocus=!1,this.previousInputValues=[],this.autocapitalize="off",this.disabled=!1,this.fill="outline",this.length=4,this.readonly=!1,this.shape="round",this.size="medium",this.type="number",this.value="",this.onFocus=t=>o=>{var n;const{inputRefs:r}=this;this.hasFocus||(this.ionFocus.emit(o),this.focusedValue=this.value),this.hasFocus=!0;let e=t;if(!this.isKeyboardNavigation){const l=this.inputValues[t]?t:this.getFirstEmptyIndex();e=-1===l?this.length-1:l,null===(n=this.inputRefs[e])||void 0===n||n.focus()}r.forEach((l,u)=>{l.tabIndex=u===e?0:-1}),this.isKeyboardNavigation=!1},this.onBlur=t=>{const{inputRefs:o}=this,n=t.relatedTarget;null!=n&&o.includes(n)||(this.hasFocus=!1,this.updateTabIndexes(),this.ionBlur.emit(t),this.focusedValue!==this.value&&this.emitIonChange(t))},this.onKeyDown=t=>o=>{const{length:n}=this,r=(0,E.i)(this.el),e=o.target;if(!(e.selectionStart!==e.selectionEnd||(o.metaKey||o.ctrlKey)&&["a","c","v","x","r","z","y"].includes(o.key.toLowerCase())))if("Backspace"===o.key)if(this.inputValues[t]){for(let s=t;s<n-1;s++)this.inputValues[s]=this.inputValues[s+1];this.inputValues[n-1]="";for(let s=0;s<n;s++)this.inputRefs[s].value=this.inputValues[s]||"";this.updateValue(o),o.preventDefault()}else!this.inputValues[t]&&t>0&&this.focusPrevious(t);else if("ArrowLeft"===o.key||"ArrowRight"===o.key){this.isKeyboardNavigation=!0,o.preventDefault();const s="ArrowLeft"===o.key;s&&r||!s&&!r?this.inputValues[t]&&t<n-1&&this.focusNext(t):this.focusPrevious(t)}else if("Tab"===o.key)return void(this.isKeyboardNavigation=!0)},this.onInput=t=>o=>{var n,r;const{length:e,validKeyPattern:l}=this,u=o.target,s=u.value,f=this.previousInputValues[t]||"";if(s.length-f.length>1){const h=s.split("").filter(a=>l.test(a)).slice(0,e);0===h.length&&requestAnimationFrame(()=>{this.inputRefs.forEach(a=>{a.value=""})});for(let a=0;a<e;a++)this.inputValues[a]=h[a]||"",this.inputRefs[a].value=h[a]||"";return this.updateValue(o),setTimeout(()=>{var a;null===(a=this.inputRefs[h.length<e?h.length:e-1])||void 0===a||a.focus()},20),void(this.previousInputValues=[...this.inputValues])}if(s.length>0&&!l.test(s[s.length-1]))return u.value=this.inputValues[t]||"",void(this.previousInputValues=[...this.inputValues]);if(0===u.selectionStart&&u.selectionEnd===s.length||!this.inputValues[t])return this.inputValues[t]=s,u.value=s,this.updateValue(o),this.focusNext(t),void(this.previousInputValues=[...this.inputValues]);if(this.inputValues[t]&&""===this.inputValues[this.inputValues.length-1]&&2===s.length){let h=o.data;if(h||(h=s.split("").find((a,y)=>a!==f[y])||s[s.length-1]),!l.test(h))return u.value=this.inputValues[t]||"",void(this.previousInputValues=[...this.inputValues]);for(let a=this.inputValues.length-1;a>t;a--)this.inputValues[a]=this.inputValues[a-1],this.inputRefs[a].value=this.inputValues[a]||"";return this.inputValues[t]=h,this.inputRefs[t].value=h,this.updateValue(o),void(this.previousInputValues=[...this.inputValues])}const _=null!==(r=s[(null!==(n=u.selectionStart)&&void 0!==n?n:s.length)-1])&&void 0!==r?r:s[0];if(!l.test(_))return u.value=this.inputValues[t]||"",void(this.previousInputValues=[...this.inputValues]);this.inputValues[t]=_,u.value=_,this.updateValue(o),this.previousInputValues=[...this.inputValues]},this.onPaste=t=>{var o,n;const{inputRefs:r,length:e,validKeyPattern:l}=this;t.preventDefault();const u=null===(o=t.clipboardData)||void 0===o?void 0:o.getData("text");if(!u)return void this.emitIonInput(t);const s=u.split("").filter(m=>l.test(m)).slice(0,e);s.forEach((m,b)=>{b<e&&(this.inputRefs[b].value=m,this.inputValues[b]=m)}),this.value=s.join(""),this.updateValue(t),null===(n=r[s.length<e?s.length:e-1])||void 0===n||n.focus()}}setFocus(i){var t=this;return(0,k.A)(function*(){var o,n;if("number"==typeof i){const r=Math.max(0,Math.min(i,t.length-1));null===(o=t.inputRefs[r])||void 0===o||o.focus()}else{const r=t.getTabbableIndex();null===(n=t.inputRefs[r])||void 0===n||n.focus()}})()}valueChanged(){this.initializeValues(),this.updateTabIndexes()}processSeparators(){const{separators:i,length:t}=this;if(void 0===i)return void(this.parsedSeparators=[]);if("string"==typeof i&&"all"!==i&&!/^(\d+)(,\d+)*$/.test(i))return(0,p.m)(`[ion-input-otp] - Invalid separators format. Expected a comma-separated list of numbers, an array of numbers, or "all". Received: ${i}`,this.el),void(this.parsedSeparators=[]);let o;o="all"===i?Array.from({length:t-1},(e,l)=>l+1):Array.isArray(i)?i:i.split(",").map(e=>parseInt(e,10)).filter(e=>!isNaN(e)),o.filter((e,l)=>o.indexOf(e)!==l).length>0&&(0,p.m)(`[ion-input-otp] - Duplicate separator positions are not allowed. Received: ${i}`,this.el);const r=o.filter(e=>e>t);r.length>0&&(0,p.m)(`[ion-input-otp] - The following separator positions are greater than the input length (${t}): ${r.join(", ")}. These separators will be ignored.`,this.el),this.parsedSeparators=o.filter(e=>e<=t)}componentWillLoad(){this.inheritedAttributes=(0,I.i)(this.el),this.processSeparators(),this.initializeValues()}componentDidLoad(){this.updateTabIndexes()}get validKeyPattern(){return new RegExp(`^${this.getPattern()}$`,"u")}getPattern(){const{pattern:i,type:t}=this;return i||("number"===t?"[\\p{N}]":"[\\p{L}\\p{N}]")}getInputmode(){const{inputmode:i}=this;return i||("number"==this.type?"numeric":"text")}initializeValues(){this.inputValues=Array(this.length).fill(""),null!=this.value&&0!==String(this.value).length&&(String(this.value).split("").slice(0,this.length).forEach((t,o)=>{this.validKeyPattern.test(t)&&(this.inputValues[o]=t)}),this.value=this.inputValues.join(""),this.previousInputValues=[...this.inputValues])}updateValue(i){const{inputValues:t,length:o}=this,n=t.join("");this.value=n,this.emitIonInput(i),n.length===o&&this.ionComplete.emit({value:n})}emitIonChange(i){const{value:t}=this,o=null==t?t:t.toString();this.ionChange.emit({value:o,event:i})}emitIonInput(i){const{value:t}=this,o=null==t?t:t.toString();this.ionInput.emit({value:o,event:i})}focusNext(i){var t;const{inputRefs:o,length:n}=this;i<n-1&&(null===(t=o[i+1])||void 0===t||t.focus())}focusPrevious(i){var t;const{inputRefs:o}=this;i>0&&(null===(t=o[i-1])||void 0===t||t.focus())}getFirstEmptyIndex(){var i;const{inputValues:t,length:o}=this;return null!==(i=Array.from({length:o},(r,e)=>t[e]||"").findIndex(r=>!r||""===r))&&void 0!==i?i:-1}getTabbableIndex(){const{length:i}=this,t=this.getFirstEmptyIndex();return-1===t?i-1:t}updateTabIndexes(){const{inputRefs:i,inputValues:t,length:o}=this;let n=-1;for(let r=0;r<o;r++)if(!t[r]||""===t[r]){n=r;break}i.forEach((r,e)=>{const l=-1===n?e===o-1:n===e;r.tabIndex=l?0:-1,r.setAttribute("aria-hidden",t[e]&&""!==t[e]||l?"false":"true")})}showSeparator(i){const{length:t}=this;return this.parsedSeparators.includes(i+1)&&i<t-1}render(){var i,t;const{autocapitalize:o,color:n,disabled:r,el:e,fill:l,hasFocus:u,inheritedAttributes:s,inputId:f,inputRefs:m,inputValues:b,length:V,readonly:w,shape:D,size:A}=this,_=(0,p.e)(this),h=this.getInputmode(),a=this.getTabbableIndex(),y=this.getPattern(),S=""!==(null===(t=null===(i=e.querySelector(".input-otp-description"))||void 0===i?void 0:i.textContent)||void 0===t?void 0:t.trim());return(0,p.h)(p.j,{key:"f15a29fb17b681ef55885ca36d3d5f899cbaca83",class:(0,C.c)(n,{[_]:!0,"has-focus":u,[`input-otp-size-${A}`]:!0,[`input-otp-shape-${D}`]:!0,[`input-otp-fill-${l}`]:!0,"input-otp-disabled":r,"input-otp-readonly":w})},(0,p.h)("div",Object.assign({key:"d7e1d4edd8aafcf2ed4313301287282e90fc7e82",role:"group","aria-label":"One-time password input",class:"input-otp-group"},s),Array.from({length:V}).map((F,v)=>(0,p.h)(p.F,null,(0,p.h)("div",{class:"native-wrapper"},(0,p.h)("input",{class:"native-input",id:`${f}-${v}`,"aria-label":`Input ${v+1} of ${V}`,type:"text",autoCapitalize:o,inputmode:h,pattern:y,disabled:r,readOnly:w,tabIndex:v===a?0:-1,value:b[v]||"",autocomplete:"one-time-code",ref:T=>m[v]=T,onInput:this.onInput(v),onBlur:this.onBlur,onFocus:this.onFocus(v),onKeyDown:this.onKeyDown(v),onPaste:this.onPaste})),this.showSeparator(v)&&(0,p.h)("div",{class:"input-otp-separator"})))),(0,p.h)("div",{key:"3724a3159d02860971879a906092f9965f5a7c47",class:{"input-otp-description":!0,"input-otp-description-hidden":!S}},(0,p.h)("slot",{key:"11baa2624926a08274508afe0833d9237a8dc35c"})))}get el(){return(0,p.k)(this)}static get watchers(){return{value:["valueChanged"],separators:["processSeparators"],length:["processSeparators"]}}};let c=0;d.style={ios:".sc-ion-input-otp-ios-h{--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--padding-top:16px;--padding-end:0;--padding-bottom:16px;--padding-start:0;--color:initial;--min-width:40px;--separator-width:8px;--separator-height:var(--separator-width);--separator-border-radius:999px;--separator-color:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-size:0.875rem}.input-otp-group.sc-ion-input-otp-ios{-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.native-wrapper.sc-ion-input-otp-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:var(--min-width)}.native-input.sc-ion-input-otp-ios{border-radius:var(--border-radius);width:var(--width);min-width:inherit;height:var(--height);border-width:var(--border-width);border-style:solid;border-color:var(--border-color);background:var(--background);color:var(--color);font-size:inherit;text-align:center;-webkit-appearance:none;-moz-appearance:none;appearance:none}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{caret-color:var(--highlight-color)}.input-otp-description.sc-ion-input-otp-ios{color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));font-size:0.75rem;line-height:1.25rem;text-align:center}.input-otp-description-hidden.sc-ion-input-otp-ios{display:none}.input-otp-separator.sc-ion-input-otp-ios{border-radius:var(--separator-border-radius);-ms-flex-negative:0;flex-shrink:0;width:var(--separator-width);height:var(--separator-height);background:var(--separator-color)}.input-otp-size-small.sc-ion-input-otp-ios-h{--width:40px;--height:40px}.input-otp-size-small.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios{gap:8px}.input-otp-size-medium.sc-ion-input-otp-ios-h{--width:48px;--height:48px}.input-otp-size-large.sc-ion-input-otp-ios-h{--width:56px;--height:56px}.input-otp-size-medium.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios,.input-otp-size-large.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios{gap:12px}.input-otp-shape-round.sc-ion-input-otp-ios-h{--border-radius:16px}.input-otp-shape-soft.sc-ion-input-otp-ios-h{--border-radius:8px}.input-otp-shape-rectangular.sc-ion-input-otp-ios-h{--border-radius:0}.input-otp-fill-outline.sc-ion-input-otp-ios-h{--background:none}.input-otp-fill-solid.sc-ion-input-otp-ios-h{--border-color:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-disabled.sc-ion-input-otp-ios-h{--color:var(--ion-color-step-350, var(--ion-text-color-step-650, #a6a6a6))}.input-otp-fill-outline.input-otp-disabled.sc-ion-input-otp-ios-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.input-otp-disabled.sc-ion-input-otp-ios-h,.input-otp-disabled.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:disabled{cursor:not-allowed}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{--border-color:var(--highlight-color);outline:none}.input-otp-fill-outline.input-otp-readonly.sc-ion-input-otp-ios-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-fill-solid.input-otp-disabled.sc-ion-input-otp-ios-h,.input-otp-fill-solid.input-otp-readonly.sc-ion-input-otp-ios-h{--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.ion-touched.ion-invalid.sc-ion-input-otp-ios-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-otp-ios-h{--highlight-color:var(--highlight-color-valid)}.has-focus.ion-valid.sc-ion-input-otp-ios-h,.ion-touched.ion-invalid.sc-ion-input-otp-ios-h{--border-color:var(--highlight-color)}.ion-color.sc-ion-input-otp-ios-h{--highlight-color-focused:var(--ion-color-base)}.input-otp-fill-outline.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{border-color:rgba(var(--ion-color-base-rgb), 0.6)}.input-otp-fill-outline.ion-color.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-outline.ion-color.has-focus.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.has-focus.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:var(--ion-color-danger, #c5000f)}.input-otp-fill-outline.ion-color.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-outline.ion-color.has-focus.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.has-focus.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:var(--ion-color-success, #2dd55b)}.input-otp-fill-outline.input-otp-disabled.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:rgba(var(--ion-color-base-rgb), 0.3)}.sc-ion-input-otp-ios-h{--border-width:0.55px}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{--border-width:1px}.input-otp-fill-outline.sc-ion-input-otp-ios-h{--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))))}",md:".sc-ion-input-otp-md-h{--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--padding-top:16px;--padding-end:0;--padding-bottom:16px;--padding-start:0;--color:initial;--min-width:40px;--separator-width:8px;--separator-height:var(--separator-width);--separator-border-radius:999px;--separator-color:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-size:0.875rem}.input-otp-group.sc-ion-input-otp-md{-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.native-wrapper.sc-ion-input-otp-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:var(--min-width)}.native-input.sc-ion-input-otp-md{border-radius:var(--border-radius);width:var(--width);min-width:inherit;height:var(--height);border-width:var(--border-width);border-style:solid;border-color:var(--border-color);background:var(--background);color:var(--color);font-size:inherit;text-align:center;-webkit-appearance:none;-moz-appearance:none;appearance:none}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{caret-color:var(--highlight-color)}.input-otp-description.sc-ion-input-otp-md{color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));font-size:0.75rem;line-height:1.25rem;text-align:center}.input-otp-description-hidden.sc-ion-input-otp-md{display:none}.input-otp-separator.sc-ion-input-otp-md{border-radius:var(--separator-border-radius);-ms-flex-negative:0;flex-shrink:0;width:var(--separator-width);height:var(--separator-height);background:var(--separator-color)}.input-otp-size-small.sc-ion-input-otp-md-h{--width:40px;--height:40px}.input-otp-size-small.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md{gap:8px}.input-otp-size-medium.sc-ion-input-otp-md-h{--width:48px;--height:48px}.input-otp-size-large.sc-ion-input-otp-md-h{--width:56px;--height:56px}.input-otp-size-medium.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md,.input-otp-size-large.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md{gap:12px}.input-otp-shape-round.sc-ion-input-otp-md-h{--border-radius:16px}.input-otp-shape-soft.sc-ion-input-otp-md-h{--border-radius:8px}.input-otp-shape-rectangular.sc-ion-input-otp-md-h{--border-radius:0}.input-otp-fill-outline.sc-ion-input-otp-md-h{--background:none}.input-otp-fill-solid.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-disabled.sc-ion-input-otp-md-h{--color:var(--ion-color-step-350, var(--ion-text-color-step-650, #a6a6a6))}.input-otp-fill-outline.input-otp-disabled.sc-ion-input-otp-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.input-otp-disabled.sc-ion-input-otp-md-h,.input-otp-disabled.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:disabled{cursor:not-allowed}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{--border-color:var(--highlight-color);outline:none}.input-otp-fill-outline.input-otp-readonly.sc-ion-input-otp-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-fill-solid.input-otp-disabled.sc-ion-input-otp-md-h,.input-otp-fill-solid.input-otp-readonly.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.ion-touched.ion-invalid.sc-ion-input-otp-md-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-otp-md-h{--highlight-color:var(--highlight-color-valid)}.has-focus.ion-valid.sc-ion-input-otp-md-h,.ion-touched.ion-invalid.sc-ion-input-otp-md-h{--border-color:var(--highlight-color)}.ion-color.sc-ion-input-otp-md-h{--highlight-color-focused:var(--ion-color-base)}.input-otp-fill-outline.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{border-color:rgba(var(--ion-color-base-rgb), 0.6)}.input-otp-fill-outline.ion-color.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-outline.ion-color.has-focus.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.has-focus.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:var(--ion-color-danger, #c5000f)}.input-otp-fill-outline.ion-color.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-outline.ion-color.has-focus.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.has-focus.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:var(--ion-color-success, #2dd55b)}.input-otp-fill-outline.input-otp-disabled.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:rgba(var(--ion-color-base-rgb), 0.3)}.sc-ion-input-otp-md-h{--border-width:1px}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{--border-width:2px}.input-otp-fill-outline.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3))}"}},4576:(P,x,g)=>{g.d(x,{c:()=>I,g:()=>C,h:()=>p,o:()=>R});var k=g(467);const p=(d,c)=>null!==c.closest(d),I=(d,c)=>"string"==typeof d&&d.length>0?Object.assign({"ion-color":!0,[`ion-color-${d}`]:!0},c):c,C=d=>{const c={};return(d=>void 0!==d?(Array.isArray(d)?d:d.split(" ")).filter(i=>null!=i).map(i=>i.trim()).filter(i=>""!==i):[])(d).forEach(i=>c[i]=!0),c},z=/^[a-z][a-z0-9+\-.]*:/,R=function(){var d=(0,k.A)(function*(c,i,t,o){if(null!=c&&"#"!==c[0]&&!z.test(c)){const n=document.querySelector("ion-router");if(n)return i?.preventDefault(),n.push(c,t,o)}return!1});return function(i,t,o,n){return d.apply(this,arguments)}}()}}]);