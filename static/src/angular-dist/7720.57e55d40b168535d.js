"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[7720],{7720:(W,C,l)=>{l.r(C),l.d(C,{ion_app:()=>B,ion_buttons:()=>Z,ion_content:()=>M,ion_footer:()=>q,ion_header:()=>at,ion_router_outlet:()=>ct,ion_title:()=>ht,ion_toolbar:()=>ft});var b=l(467),n=l(2734),E=l(1906),m=l(1837),_=l(647),y=l(4576),v=l(4657),p=l(1148),h=l(9166),f=l(1653),S=l(7930),T=l(4590);l(9596),l(9043),l(6780);const B=class{constructor(o){(0,n.r)(this,o)}componentDidLoad(){var o=this;X((0,b.A)(function*(){const t=(0,n.a)(window,"hybrid");if(n.l.getBoolean("_testing")||l.e(7948).then(l.bind(l,7948)).then(r=>r.startTapClick(n.l)),n.l.getBoolean("statusTap",t)&&l.e(5671).then(l.bind(l,5671)).then(r=>r.startStatusTap()),n.l.getBoolean("inputShims",U())){const r=(0,n.a)(window,"ios")?"ios":"android";l.e(530).then(l.bind(l,530)).then(s=>s.startInputShims(n.l,r))}const i=yield Promise.resolve().then(l.bind(l,1906)),e=t||(0,E.shouldUseCloseWatcher)();n.l.getBoolean("hardwareBackButton",e)?i.startHardwareBackButton():((0,E.shouldUseCloseWatcher)()&&(0,n.m)("[ion-app] - experimentalCloseWatcher was set to `true`, but hardwareBackButton was set to `false`. Both config options must be `true` for the Close Watcher API to be used."),i.blockHardwareBackButton()),typeof window<"u"&&l.e(2076).then(l.bind(l,3224)).then(r=>r.startKeyboardAssist(window)),l.e(2076).then(l.bind(l,6090)).then(r=>o.focusVisible=r.startFocusVisible())}))}setFocus(o){var t=this;return(0,b.A)(function*(){t.focusVisible&&t.focusVisible.setFocus(o)})()}render(){const o=(0,n.e)(this);return(0,n.h)(n.j,{key:"9be440c65819e4fa67c2c3c6477ab40b3ad3eed3",class:{[o]:!0,"ion-page":!0,"force-statusbar-padding":n.l.getBoolean("_forceStatusbarPadding")}})}get el(){return(0,n.k)(this)}},U=()=>!!((0,n.a)(window,"ios")&&(0,n.a)(window,"mobile")||(0,n.a)(window,"android")&&(0,n.a)(window,"mobileweb")),X=o=>{"requestIdleCallback"in window?window.requestIdleCallback(o):setTimeout(o,32)};B.style="html.plt-mobile ion-app{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}html.plt-mobile ion-app [contenteditable]{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}ion-app.force-statusbar-padding{--ion-safe-area-top:20px}";const Z=(()=>{let o=class{constructor(t){(0,n.r)(this,t),this.collapse=!1}render(){const t=(0,n.e)(this);return(0,n.h)(n.j,{key:"58c1fc5eb867d0731c63549b1ccb3ec3bbbe6e1b",class:{[t]:!0,"buttons-collapse":this.collapse}},(0,n.h)("slot",{key:"0c8f95b9840c8fa0c4e50be84c5159620a3eb5c8"}))}};return o.style={ios:".sc-ion-buttons-ios-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-ios-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-ios-s ion-button{--padding-top:3px;--padding-bottom:3px;--padding-start:5px;--padding-end:5px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;min-height:32px}.sc-ion-buttons-ios-s .button-has-icon-only{--padding-top:0;--padding-bottom:0}.sc-ion-buttons-ios-s ion-button:not(.button-round){--border-radius:4px}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button{--color:initial;--border-color:initial;--background-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-solid,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-solid{--background:var(--ion-color-contrast);--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12;--background-hover:var(--ion-color-base);--background-hover-opacity:0.45;--color:var(--ion-color-base);--color-focused:var(--ion-color-base)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-clear,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-clear{--color-activated:var(--ion-color-contrast);--color-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-outline,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-outline{--color-activated:var(--ion-color-base);--color-focused:var(--ion-color-contrast);--background-activated:var(--ion-color-contrast)}.sc-ion-buttons-ios-s .button-clear,.sc-ion-buttons-ios-s .button-outline{--background-activated:transparent;--background-focused:currentColor;--background-hover:transparent}.sc-ion-buttons-ios-s .button-solid:not(.ion-color){--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12}.sc-ion-buttons-ios-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:1.41em;line-height:0.67}.sc-ion-buttons-ios-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-start:0.4em;margin-inline-start:0.4em;font-size:1.41em;line-height:0.67}.sc-ion-buttons-ios-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.65em;line-height:0.67}",md:".sc-ion-buttons-md-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-md-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-md-s ion-button{--padding-top:3px;--padding-bottom:3px;--padding-start:8px;--padding-end:8px;--box-shadow:none;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;min-height:32px}.sc-ion-buttons-md-s .button-has-icon-only{--padding-top:0;--padding-bottom:0}.sc-ion-buttons-md-s ion-button:not(.button-round){--border-radius:2px}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button{--color:initial;--color-focused:var(--ion-color-contrast);--color-hover:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-contrast);--background-hover:var(--ion-color-contrast)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-solid,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-solid{--background:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-shade);--background-hover:var(--ion-color-base);--color:var(--ion-color-base);--color-focused:var(--ion-color-base);--color-hover:var(--ion-color-base)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-outline,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-outline{--border-color:var(--ion-color-contrast)}.sc-ion-buttons-md-s .button-has-icon-only.button-clear{--padding-top:12px;--padding-end:12px;--padding-bottom:12px;--padding-start:12px;--border-radius:50%;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:3rem;height:3rem}.sc-ion-buttons-md-s .button{--background-hover:currentColor}.sc-ion-buttons-md-s .button-solid{--color:var(--ion-toolbar-background, var(--ion-background-color, #fff));--background:var(--ion-toolbar-color, var(--ion-text-color, #424242));--background-activated:transparent;--background-focused:currentColor}.sc-ion-buttons-md-s .button-outline{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor;--border-color:currentColor}.sc-ion-buttons-md-s .button-clear{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor}.sc-ion-buttons-md-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:1.4em}.sc-ion-buttons-md-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-start:0.4em;margin-inline-start:0.4em;font-size:1.4em}.sc-ion-buttons-md-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.8em}"},o})(),M=class{constructor(o){(0,n.r)(this,o),this.ionScrollStart=(0,n.d)(this,"ionScrollStart",7),this.ionScroll=(0,n.d)(this,"ionScroll",7),this.ionScrollEnd=(0,n.d)(this,"ionScrollEnd",7),this.watchDog=null,this.isScrolling=!1,this.lastScroll=0,this.queued=!1,this.cTop=-1,this.cBottom=-1,this.isMainContent=!0,this.resizeTimeout=null,this.inheritedAttributes={},this.tabsElement=null,this.detail={scrollTop:0,scrollLeft:0,type:"scroll",event:void 0,startX:0,startY:0,startTime:0,currentX:0,currentY:0,velocityX:0,velocityY:0,deltaX:0,deltaY:0,currentTime:0,data:void 0,isScrolling:!0},this.fullscreen=!1,this.fixedSlotPlacement="after",this.scrollX=!1,this.scrollY=!0,this.scrollEvents=!1}componentWillLoad(){this.inheritedAttributes=(0,m.i)(this.el)}connectedCallback(){if(this.isMainContent=null===this.el.closest("ion-menu, ion-popover, ion-modal"),(0,m.h)(this.el)){const o=this.tabsElement=this.el.closest("ion-tabs");null!==o&&(this.tabsLoadCallback=()=>this.resize(),o.addEventListener("ionTabBarLoaded",this.tabsLoadCallback))}}disconnectedCallback(){if(this.onScrollEnd(),(0,m.h)(this.el)){const{tabsElement:o,tabsLoadCallback:t}=this;null!==o&&void 0!==t&&o.removeEventListener("ionTabBarLoaded",t),this.tabsElement=null,this.tabsLoadCallback=void 0}}onResize(){this.resizeTimeout&&(clearTimeout(this.resizeTimeout),this.resizeTimeout=null),this.resizeTimeout=setTimeout(()=>{null!==this.el.offsetParent&&this.resize()},100)}shouldForceOverscroll(){const{forceOverscroll:o}=this,t=(0,n.e)(this);return void 0===o?"ios"===t&&(0,n.a)("ios"):o}resize(){this.fullscreen?(0,n.f)(()=>this.readDimensions()):(0!==this.cTop||0!==this.cBottom)&&(this.cTop=this.cBottom=0,(0,n.n)(this))}readDimensions(){const o=V(this.el),t=Math.max(this.el.offsetTop,0),i=Math.max(o.offsetHeight-t-this.el.offsetHeight,0);(t!==this.cTop||i!==this.cBottom)&&(this.cTop=t,this.cBottom=i,(0,n.n)(this))}onScroll(o){const t=Date.now(),i=!this.isScrolling;this.lastScroll=t,i&&this.onScrollStart(),!this.queued&&this.scrollEvents&&(this.queued=!0,(0,n.f)(e=>{this.queued=!1,this.detail.event=o,G(this.detail,this.scrollEl,e,i),this.ionScroll.emit(this.detail)}))}getScrollElement(){var o=this;return(0,b.A)(function*(){return o.scrollEl||(yield new Promise(t=>(0,m.c)(o.el,t))),Promise.resolve(o.scrollEl)})()}getBackgroundElement(){var o=this;return(0,b.A)(function*(){return o.backgroundContentEl||(yield new Promise(t=>(0,m.c)(o.el,t))),Promise.resolve(o.backgroundContentEl)})()}scrollToTop(o=0){return this.scrollToPoint(void 0,0,o)}scrollToBottom(){var o=this;return(0,b.A)(function*(t=0){const i=yield o.getScrollElement();return o.scrollToPoint(void 0,i.scrollHeight-i.clientHeight,t)}).apply(this,arguments)}scrollByPoint(o,t,i){var e=this;return(0,b.A)(function*(){const r=yield e.getScrollElement();return e.scrollToPoint(o+r.scrollLeft,t+r.scrollTop,i)})()}scrollToPoint(o,t){var i=this;return(0,b.A)(function*(e,r,s=0){const a=yield i.getScrollElement();if(s<32)return null!=r&&(a.scrollTop=r),void(null!=e&&(a.scrollLeft=e));let c,d=0;const u=new Promise(k=>c=k),g=a.scrollTop,x=a.scrollLeft,A=null!=r?r-g:0,D=null!=e?e-x:0,R=k=>{const gt=Math.min(1,(k-d)/s)-1,O=Math.pow(gt,3)+1;0!==A&&(a.scrollTop=Math.floor(O*A+g)),0!==D&&(a.scrollLeft=Math.floor(O*D+x)),O<1?requestAnimationFrame(R):c()};return requestAnimationFrame(k=>{d=k,R(k)}),u}).apply(this,arguments)}onScrollStart(){this.isScrolling=!0,this.ionScrollStart.emit({isScrolling:!0}),this.watchDog&&clearInterval(this.watchDog),this.watchDog=setInterval(()=>{this.lastScroll<Date.now()-120&&this.onScrollEnd()},100)}onScrollEnd(){this.watchDog&&clearInterval(this.watchDog),this.watchDog=null,this.isScrolling&&(this.isScrolling=!1,this.ionScrollEnd.emit({isScrolling:!1}))}render(){const{fixedSlotPlacement:o,inheritedAttributes:t,isMainContent:i,scrollX:e,scrollY:r,el:s}=this,a=(0,_.i)(s)?"rtl":"ltr",c=(0,n.e)(this),d=this.shouldForceOverscroll(),u="ios"===c;return this.resize(),(0,n.h)(n.j,Object.assign({key:"f2a24aa66dbf5c76f9d4b06f708eb73cadc239df",role:i?"main":void 0,class:(0,y.c)(this.color,{[c]:!0,"content-sizing":(0,y.h)("ion-popover",this.el),overscroll:d,[`content-${a}`]:!0}),style:{"--offset-top":`${this.cTop}px`,"--offset-bottom":`${this.cBottom}px`}},t),(0,n.h)("div",{key:"6480ca7648b278abb36477b3838bccbcd4995e2a",ref:g=>this.backgroundContentEl=g,id:"background-content",part:"background"}),"before"===o?(0,n.h)("slot",{name:"fixed"}):null,(0,n.h)("div",{key:"29a23b663f5f0215bb000820c01e1814c0d55985",class:{"inner-scroll":!0,"scroll-x":e,"scroll-y":r,overscroll:(e||r)&&d},ref:g=>this.scrollEl=g,onScroll:this.scrollEvents?g=>this.onScroll(g):void 0,part:"scroll"},(0,n.h)("slot",{key:"0fe1bd05609a4b88ae2ce9addf5d5dc5dc1806f0"})),u?(0,n.h)("div",{class:"transition-effect"},(0,n.h)("div",{class:"transition-cover"}),(0,n.h)("div",{class:"transition-shadow"})):null,"after"===o?(0,n.h)("slot",{name:"fixed"}):null)}get el(){return(0,n.k)(this)}},V=o=>{const t=o.closest("ion-tabs");return t||(o.closest("ion-app, ion-page, .ion-page, page-inner, .popover-content")||(o=>{var t;return o.parentElement?o.parentElement:null!==(t=o.parentNode)&&void 0!==t&&t.host?o.parentNode.host:null})(o))},G=(o,t,i,e)=>{const r=o.currentX,s=o.currentY,c=t.scrollLeft,d=t.scrollTop,u=i-o.currentTime;if(e&&(o.startTime=i,o.startX=c,o.startY=d,o.velocityX=o.velocityY=0),o.currentTime=i,o.currentX=o.scrollLeft=c,o.currentY=o.scrollTop=d,o.deltaX=c-o.startX,o.deltaY=d-o.startY,u>0&&u<100){const x=(d-s)/u;o.velocityX=(c-r)/u*.7+.3*o.velocityX,o.velocityY=.7*x+.3*o.velocityY}};M.style=':host{--background:var(--ion-background-color, #fff);--color:var(--ion-text-color, #000);--padding-top:0px;--padding-bottom:0px;--padding-start:0px;--padding-end:0px;--keyboard-offset:0px;--offset-top:0px;--offset-bottom:0px;--overflow:auto;display:block;position:relative;-ms-flex:1;flex:1;width:100%;height:100%;margin:0 !important;padding:0 !important;font-family:var(--ion-font-family, inherit);contain:size style}:host(.ion-color) .inner-scroll{background:var(--ion-color-base);color:var(--ion-color-contrast)}#background-content{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);position:absolute;background:var(--background)}.inner-scroll{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:calc(var(--padding-top) + var(--offset-top));padding-bottom:calc(var(--padding-bottom) + var(--keyboard-offset) + var(--offset-bottom));position:absolute;color:var(--color);-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;-ms-touch-action:pan-x pan-y pinch-zoom;touch-action:pan-x pan-y pinch-zoom}.scroll-y,.scroll-x{-webkit-overflow-scrolling:touch;z-index:0;will-change:scroll-position}.scroll-y{overflow-y:var(--overflow);overscroll-behavior-y:contain}.scroll-x{overflow-x:var(--overflow);overscroll-behavior-x:contain}.overscroll::before,.overscroll::after{position:absolute;width:1px;height:1px;content:""}.overscroll::before{bottom:-1px}.overscroll::after{top:-1px}:host(.content-sizing){display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;min-height:0;contain:none}:host(.content-sizing) .inner-scroll{position:relative;top:0;bottom:0;margin-top:calc(var(--offset-top) * -1);margin-bottom:calc(var(--offset-bottom) * -1)}.transition-effect{display:none;position:absolute;width:100%;height:100vh;opacity:0;pointer-events:none}:host(.content-ltr) .transition-effect{left:-100%;}:host(.content-rtl) .transition-effect{right:-100%;}.transition-cover{position:absolute;right:0;width:100%;height:100%;background:black;opacity:0.1}.transition-shadow{display:block;position:absolute;width:100%;height:100%;-webkit-box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03);box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03)}:host(.content-ltr) .transition-shadow{right:0;}:host(.content-rtl) .transition-shadow{left:0;-webkit-transform:scaleX(-1);transform:scaleX(-1)}::slotted([slot=fixed]){position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0)}';const L=(o,t)=>{(0,n.f)(()=>{const c=(0,m.e)(0,1-(o.scrollTop-(o.scrollHeight-o.clientHeight-10))/10,1);(0,n.w)(()=>{t.style.setProperty("--opacity-scale",c.toString())})})},q=(()=>{let o=class{constructor(t){var i=this;(0,n.r)(this,t),this.keyboardCtrl=null,this.keyboardVisible=!1,this.translucent=!1,this.checkCollapsibleFooter=()=>{if("ios"!==(0,n.e)(this))return;const{collapse:r}=this,s="fade"===r;if(this.destroyCollapsibleFooter(),s){const a=this.el.closest("ion-app,ion-page,.ion-page,page-inner"),c=a?(0,v.a)(a):null;if(!c)return void(0,v.p)(this.el);this.setupFadeFooter(c)}},this.setupFadeFooter=function(){var e=(0,b.A)(function*(r){const s=i.scrollEl=yield(0,v.g)(r);i.contentScrollCallback=()=>{L(s,i.el)},s.addEventListener("scroll",i.contentScrollCallback),L(s,i.el)});return function(r){return e.apply(this,arguments)}}()}componentDidLoad(){this.checkCollapsibleFooter()}componentDidUpdate(){this.checkCollapsibleFooter()}connectedCallback(){var t=this;return(0,b.A)(function*(){t.keyboardCtrl=yield(0,p.c)(function(){var i=(0,b.A)(function*(e,r){!1===e&&void 0!==r&&(yield r),t.keyboardVisible=e});return function(e,r){return i.apply(this,arguments)}}())})()}disconnectedCallback(){this.keyboardCtrl&&this.keyboardCtrl.destroy()}destroyCollapsibleFooter(){this.scrollEl&&this.contentScrollCallback&&(this.scrollEl.removeEventListener("scroll",this.contentScrollCallback),this.contentScrollCallback=void 0)}render(){const{translucent:t,collapse:i}=this,e=(0,n.e)(this),r=this.el.closest("ion-tabs"),s=r?.querySelector(":scope > ion-tab-bar");return(0,n.h)(n.j,{key:"ddc228f1a1e7fa4f707dccf74db2490ca3241137",role:"contentinfo",class:{[e]:!0,[`footer-${e}`]:!0,"footer-translucent":t,[`footer-translucent-${e}`]:t,"footer-toolbar-padding":!(this.keyboardVisible||s&&"bottom"===s.slot),[`footer-collapse-${i}`]:void 0!==i}},"ios"===e&&t&&(0,n.h)("div",{key:"e16ed4963ff94e06de77eb8038201820af73937c",class:"footer-background"}),(0,n.h)("slot",{key:"f186934febf85d37133d9351a96c1a64b0a4b203"}))}get el(){return(0,n.k)(this)}};return o.style={ios:"ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-ios ion-toolbar:first-of-type{--border-width:0.55px 0 0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.footer-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.footer-translucent-ios ion-toolbar{--opacity:.8}}.footer-ios.ion-no-border ion-toolbar:first-of-type{--border-width:0}.footer-collapse-fade ion-toolbar{--opacity-scale:inherit}",md:"ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.footer-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}"},o})(),I=o=>{const t=document.querySelector(`${o}.ion-cloned-element`);if(null!==t)return t;const i=document.createElement(o);return i.classList.add("ion-cloned-element"),i.style.setProperty("display","none"),document.body.appendChild(i),i},H=o=>{if(!o)return;const t=o.querySelectorAll("ion-toolbar");return{el:o,toolbars:Array.from(t).map(i=>{const e=i.querySelector("ion-title");return{el:i,background:i.shadowRoot.querySelector(".toolbar-background"),ionTitleEl:e,innerTitleEl:e?e.shadowRoot.querySelector(".toolbar-title"):null,ionButtonsEl:Array.from(i.querySelectorAll("ion-buttons"))}})}},P=(o,t)=>{"fade"!==o.collapse&&(void 0===t?o.style.removeProperty("--opacity-scale"):o.style.setProperty("--opacity-scale",t.toString()))},w=(o,t=!0)=>{const i=o.el,r=o.toolbars.map(s=>s.ionTitleEl);t?(i.classList.remove("header-collapse-condense-inactive"),r.forEach(s=>{s&&s.removeAttribute("aria-hidden")})):(i.classList.add("header-collapse-condense-inactive"),r.forEach(s=>{s&&s.setAttribute("aria-hidden","true")}))},j=(o,t,i)=>{(0,n.f)(()=>{const e=o.scrollTop,r=t.clientHeight,s=i?i.clientHeight:0;if(null!==i&&e<s)return t.style.setProperty("--opacity-scale","0"),void o.style.setProperty("clip-path",`inset(${r}px 0px 0px 0px)`);const d=(0,m.e)(0,(e-s)/10,1);(0,n.w)(()=>{o.style.removeProperty("clip-path"),t.style.setProperty("--opacity-scale",d.toString())})})},at=(()=>{let o=class{constructor(t){var i=this;(0,n.r)(this,t),this.inheritedAttributes={},this.translucent=!1,this.setupFadeHeader=function(){var e=(0,b.A)(function*(r,s){const a=i.scrollEl=yield(0,v.g)(r);i.contentScrollCallback=()=>{j(i.scrollEl,i.el,s)},a.addEventListener("scroll",i.contentScrollCallback),j(i.scrollEl,i.el,s)});return function(r,s){return e.apply(this,arguments)}}()}componentWillLoad(){this.inheritedAttributes=(0,m.i)(this.el)}componentDidLoad(){this.checkCollapsibleHeader()}componentDidUpdate(){this.checkCollapsibleHeader()}disconnectedCallback(){this.destroyCollapsibleHeader()}checkCollapsibleHeader(){var t=this;return(0,b.A)(function*(){if("ios"!==(0,n.e)(t))return;const{collapse:e}=t,r="condense"===e,s="fade"===e;if(t.destroyCollapsibleHeader(),r){const a=t.el.closest("ion-app,ion-page,.ion-page,page-inner"),c=a?(0,v.a)(a):null;(0,n.w)(()=>{I("ion-title").size="large",I("ion-back-button")}),yield t.setupCondenseHeader(c,a)}else if(s){const a=t.el.closest("ion-app,ion-page,.ion-page,page-inner"),c=a?(0,v.a)(a):null;if(!c)return void(0,v.p)(t.el);const d=c.querySelector('ion-header[collapse="condense"]');yield t.setupFadeHeader(c,d)}})()}destroyCollapsibleHeader(){this.intersectionObserver&&(this.intersectionObserver.disconnect(),this.intersectionObserver=void 0),this.scrollEl&&this.contentScrollCallback&&(this.scrollEl.removeEventListener("scroll",this.contentScrollCallback),this.contentScrollCallback=void 0),this.collapsibleMainHeader&&(this.collapsibleMainHeader.classList.remove("header-collapse-main"),this.collapsibleMainHeader=void 0)}setupCondenseHeader(t,i){var e=this;return(0,b.A)(function*(){if(!t||!i)return void(0,v.p)(e.el);if(typeof IntersectionObserver>"u")return;e.scrollEl=yield(0,v.g)(t);const r=i.querySelectorAll("ion-header");if(e.collapsibleMainHeader=Array.from(r).find(d=>"condense"!==d.collapse),!e.collapsibleMainHeader)return;const s=H(e.collapsibleMainHeader),a=H(e.el);s&&a&&(w(s,!1),P(s.el,0),e.intersectionObserver=new IntersectionObserver(d=>{((o,t,i,e)=>{(0,n.w)(()=>{const r=e.scrollTop;((o,t,i)=>{if(!o[0].isIntersecting)return;const e=o[0].intersectionRatio>.9||i<=0?0:100*(1-o[0].intersectionRatio)/75;P(t.el,1===e?void 0:e)})(o,t,r);const s=o[0],a=s.intersectionRect,c=a.width*a.height,u=0===c&&0==s.rootBounds.width*s.rootBounds.height,g=Math.abs(a.left-s.boundingClientRect.left),x=Math.abs(a.right-s.boundingClientRect.right);u||c>0&&(g>=5||x>=5)||(s.isIntersecting?(w(t,!1),w(i)):(0===a.x&&0===a.y||0!==a.width&&0!==a.height)&&r>0&&(w(t),w(i,!1),P(t.el)))})})(d,s,a,e.scrollEl)},{root:t,threshold:[.25,.3,.4,.5,.6,.7,.8,.9,1]}),e.intersectionObserver.observe(a.toolbars[a.toolbars.length-1].el),e.contentScrollCallback=()=>{((o,t,i)=>{(0,n.f)(()=>{const r=(0,m.e)(1,1+-o.scrollTop/500,1.1);null===i.querySelector("ion-refresher.refresher-native")&&(0,n.w)(()=>{((o=[],t=1,i=!1)=>{o.forEach(e=>{const r=e.ionTitleEl,s=e.innerTitleEl;!r||"large"!==r.size||(s.style.transition=i?"all 0.2s ease-in-out":"",s.style.transform=`scale3d(${t}, ${t}, 1)`)})})(t.toolbars,r)})})})(e.scrollEl,a,t)},e.scrollEl.addEventListener("scroll",e.contentScrollCallback),(0,n.w)(()=>{void 0!==e.collapsibleMainHeader&&e.collapsibleMainHeader.classList.add("header-collapse-main")}))})()}render(){const{translucent:t,inheritedAttributes:i}=this,e=(0,n.e)(this),r=this.collapse||"none",s=(0,y.h)("ion-menu",this.el)?"none":"banner";return(0,n.h)(n.j,Object.assign({key:"b6cc27f0b08afc9fcc889683525da765d80ba672",role:s,class:{[e]:!0,[`header-${e}`]:!0,"header-translucent":this.translucent,[`header-collapse-${r}`]:!0,[`header-translucent-${e}`]:this.translucent}},i),"ios"===e&&t&&(0,n.h)("div",{key:"395766d4dcee3398bc91960db21f922095292f14",class:"header-background"}),(0,n.h)("slot",{key:"09a67ece27b258ff1248805d43d92a49b2c6859a"}))}get el(){return(0,n.k)(this)}};return o.style={ios:"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-ios ion-toolbar:last-of-type{--border-width:0 0 0.55px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.header-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.header-translucent-ios ion-toolbar{--opacity:.8}.header-collapse-condense-inactive .header-background{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}}.header-ios.ion-no-border ion-toolbar:last-of-type{--border-width:0}.header-collapse-fade ion-toolbar{--opacity-scale:inherit}.header-collapse-condense{z-index:9}.header-collapse-condense ion-toolbar{position:-webkit-sticky;position:sticky;top:0}.header-collapse-condense ion-toolbar:first-of-type{padding-top:0px;z-index:1}.header-collapse-condense ion-toolbar{--background:var(--ion-background-color, #fff);z-index:0}.header-collapse-condense ion-toolbar:last-of-type{--border-width:0px}.header-collapse-condense ion-toolbar ion-searchbar{padding-top:0px;padding-bottom:13px}.header-collapse-main{--opacity-scale:1}.header-collapse-main ion-toolbar{--opacity-scale:inherit}.header-collapse-main ion-toolbar.in-toolbar ion-title,.header-collapse-main ion-toolbar.in-toolbar ion-buttons{-webkit-transition:all 0.2s ease-in-out;transition:all 0.2s ease-in-out}.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-buttons.buttons-collapse{opacity:0;pointer-events:none}.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-buttons.buttons-collapse{visibility:hidden}ion-header.header-ios:not(.header-collapse-main):has(~ion-content ion-header.header-ios[collapse=condense],~ion-content ion-header.header-ios.header-collapse-condense){opacity:0}",md:"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.header-collapse-condense{display:none}.header-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}"},o})(),ct=(()=>{let o=class{constructor(t){(0,n.r)(this,t),this.ionNavWillLoad=(0,n.d)(this,"ionNavWillLoad",7),this.ionNavWillChange=(0,n.d)(this,"ionNavWillChange",3),this.ionNavDidChange=(0,n.d)(this,"ionNavDidChange",3),this.lockController=(0,S.c)(),this.gestureOrAnimationInProgress=!1,this.mode=(0,n.e)(this),this.animated=!0}swipeHandlerChanged(){this.gesture&&this.gesture.enable(void 0!==this.swipeHandler)}connectedCallback(){var t=this;return(0,b.A)(function*(){t.gesture=(yield l.e(2076).then(l.bind(l,6438))).createSwipeBackGesture(t.el,()=>!t.gestureOrAnimationInProgress&&!!t.swipeHandler&&t.swipeHandler.canStart(),()=>(t.gestureOrAnimationInProgress=!0,void(t.swipeHandler&&t.swipeHandler.onStart())),e=>{var r;return null===(r=t.ani)||void 0===r?void 0:r.progressStep(e)},(e,r,s)=>{if(t.ani){t.ani.onFinish(()=>{t.gestureOrAnimationInProgress=!1,t.swipeHandler&&t.swipeHandler.onEnd(e)},{oneTimeCallback:!0});let a=e?-.001:.001;e?a+=(0,h.g)([0,0],[.32,.72],[0,1],[1,1],r)[0]:(t.ani.easing("cubic-bezier(1, 0, 0.68, 0.28)"),a+=(0,h.g)([0,0],[1,0],[.68,.28],[1,1],r)[0]),t.ani.progressEnd(e?1:0,a,s)}else t.gestureOrAnimationInProgress=!1}),t.swipeHandlerChanged()})()}componentWillLoad(){this.ionNavWillLoad.emit()}disconnectedCallback(){this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}commit(t,i,e){var r=this;return(0,b.A)(function*(){const s=yield r.lockController.lock();let a=!1;try{a=yield r.transition(t,i,e)}catch(c){(0,n.o)("[ion-router-outlet] - Exception in commit:",c)}return s(),a})()}setRouteId(t,i,e,r){var s=this;return(0,b.A)(function*(){return{changed:yield s.setRoot(t,i,{duration:"root"===e?0:void 0,direction:"back"===e?"back":"forward",animationBuilder:r}),element:s.activeEl}})()}getRouteId(){var t=this;return(0,b.A)(function*(){const i=t.activeEl;return i?{id:i.tagName,element:i,params:t.activeParams}:void 0})()}setRoot(t,i,e){var r=this;return(0,b.A)(function*(){if(r.activeComponent===t&&(0,m.s)(i,r.activeParams))return!1;const s=r.activeEl,a=yield(0,f.a)(r.delegate,r.el,t,["ion-page","ion-page-invisible"],i);return r.activeComponent=t,r.activeEl=a,r.activeParams=i,yield r.commit(a,s,e),yield(0,f.d)(r.delegate,s),!0})()}transition(t,i){var e=this;return(0,b.A)(function*(r,s,a={}){if(s===r)return!1;e.ionNavWillChange.emit();const{el:c,mode:d}=e,u=e.animated&&n.l.getBoolean("animated",!0),g=a.animationBuilder||e.animation||n.l.get("navAnimation");return yield(0,T.t)(Object.assign(Object.assign({mode:d,animated:u,enteringEl:r,leavingEl:s,baseEl:c,deepWait:(0,m.h)(c),progressCallback:a.progressAnimation?x=>{void 0===x||e.gestureOrAnimationInProgress?e.ani=x:(e.gestureOrAnimationInProgress=!0,x.onFinish(()=>{e.gestureOrAnimationInProgress=!1,e.swipeHandler&&e.swipeHandler.onEnd(!1)},{oneTimeCallback:!0}),x.progressEnd(0,0,0))}:void 0},a),{animationBuilder:g})),e.ionNavDidChange.emit(),!0}).apply(this,arguments)}render(){return(0,n.h)("slot",{key:"84b50f1155b0d780dff802ee13223287259fd525"})}get el(){return(0,n.k)(this)}static get watchers(){return{swipeHandler:["swipeHandlerChanged"]}}};return o.style=":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}",o})(),ht=(()=>{let o=class{constructor(t){(0,n.r)(this,t),this.ionStyle=(0,n.d)(this,"ionStyle",7)}sizeChanged(){this.emitStyle()}connectedCallback(){this.emitStyle()}emitStyle(){const t=this.getSize();this.ionStyle.emit({[`title-${t}`]:!0})}getSize(){return void 0!==this.size?this.size:"default"}render(){const t=(0,n.e)(this),i=this.getSize();return(0,n.h)(n.j,{key:"e599c0bf1b0817df3fa8360bdcd6d787f751c371",class:(0,y.c)(this.color,{[t]:!0,[`title-${i}`]:!0,"title-rtl":"rtl"===document.dir})},(0,n.h)("div",{key:"6e7eee9047d6759876bb31d7305b76efc7c4338c",class:"toolbar-title"},(0,n.h)("slot",{key:"bf790eb4c83dd0af4f2fd1f85ab4af5819f46ff4"})))}get el(){return(0,n.k)(this)}static get watchers(){return{size:["sizeChanged"]}}};return o.style={ios:":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{top:0;-webkit-padding-start:90px;padding-inline-start:90px;-webkit-padding-end:90px;padding-inline-end:90px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);position:absolute;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0);font-size:min(1.0625rem, 20.4px);font-weight:600;text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host{inset-inline-start:0}:host(.title-small){-webkit-padding-start:9px;padding-inline-start:9px;-webkit-padding-end:9px;padding-inline-end:9px;padding-top:6px;padding-bottom:16px;position:relative;font-size:min(0.8125rem, 23.4px);font-weight:normal}:host(.title-large){-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:2px;padding-bottom:4px;-webkit-transform-origin:left center;transform-origin:left center;position:static;-ms-flex-align:end;align-items:flex-end;min-width:100%;font-size:min(2.125rem, 61.2px);font-weight:700;text-align:start}:host(.title-large.title-rtl){-webkit-transform-origin:right center;transform-origin:right center}:host(.title-large.ion-cloned-element){--color:var(--ion-text-color, #000);font-family:var(--ion-font-family)}:host(.title-large) .toolbar-title{-webkit-transform-origin:inherit;transform-origin:inherit;width:auto}:host-context([dir=rtl]):host(.title-large) .toolbar-title,:host-context([dir=rtl]).title-large .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}@supports selector(:dir(rtl)){:host(.title-large:dir(rtl)) .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}}",md:":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;font-size:1.25rem;font-weight:500;letter-spacing:0.0125em}:host(.title-small){width:100%;height:100%;font-size:0.9375rem;font-weight:normal}"},o})(),ft=(()=>{let o=class{constructor(t){(0,n.r)(this,t),this.childrenStyles=new Map}componentWillLoad(){const t=Array.from(this.el.querySelectorAll("ion-buttons")),i=t.find(s=>"start"===s.slot);i&&i.classList.add("buttons-first-slot");const e=t.reverse(),r=e.find(s=>"end"===s.slot)||e.find(s=>"primary"===s.slot)||e.find(s=>"secondary"===s.slot);r&&r.classList.add("buttons-last-slot")}childrenStyle(t){t.stopPropagation();const i=t.target.tagName,e=t.detail,r={},s=this.childrenStyles.get(i)||{};let a=!1;Object.keys(e).forEach(c=>{const d=`toolbar-${c}`,u=e[c];u!==s[d]&&(a=!0),u&&(r[d]=!0)}),a&&(this.childrenStyles.set(i,r),(0,n.n)(this))}render(){const t=(0,n.e)(this),i={};return this.childrenStyles.forEach(e=>{Object.assign(i,e)}),(0,n.h)(n.j,{key:"f6c4f669a6a61c5eac4cbb5ea0aa97c48ae5bd46",class:Object.assign(Object.assign({},i),(0,y.c)(this.color,{[t]:!0,"in-toolbar":(0,y.h)("ion-toolbar",this.el)}))},(0,n.h)("div",{key:"9c81742ffa02de9ba7417025b077d05e67305074",class:"toolbar-background",part:"background"}),(0,n.h)("div",{key:"5fc96d166fa47894a062e41541a9beee38078a36",class:"toolbar-container",part:"container"},(0,n.h)("slot",{key:"b62c0d9d59a70176bdbf769aec6090d7a166853b",name:"start"}),(0,n.h)("slot",{key:"d01d3cc2c50e5aaa49c345b209fe8dbdf3d48131",name:"secondary"}),(0,n.h)("div",{key:"3aaa3a2810aedd38c37eb616158ec7b9638528fc",class:"toolbar-content",part:"content"},(0,n.h)("slot",{key:"357246690f8d5e1cc3ca369611d4845a79edf610"})),(0,n.h)("slot",{key:"06ed3cca4f7ebff4a54cd877dad3cc925ccf9f75",name:"primary"}),(0,n.h)("slot",{key:"e453d43d14a26b0d72f41e1b81a554bab8ece811",name:"end"})))}get el(){return(0,n.k)(this)}};return o.style={ios:":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-color-step-50, var(--ion-background-color-step-50, #f7f7f7)));--color:var(--ion-toolbar-color, var(--ion-text-color, #000));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.2)))));--padding-top:3px;--padding-bottom:3px;--padding-start:4px;--padding-end:4px;--min-height:44px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:4;order:4;min-width:0}:host(.toolbar-segment) .toolbar-content{display:-ms-inline-flexbox;display:inline-flex}:host(.toolbar-searchbar) .toolbar-container{padding-top:0;padding-bottom:0}:host(.toolbar-searchbar) ::slotted(*){-ms-flex-item-align:start;align-self:start}:host(.toolbar-searchbar) ::slotted(ion-chip){margin-top:3px}::slotted(ion-buttons){min-height:38px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:3;order:3}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}:host(.toolbar-title-large) .toolbar-container{-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:start;align-items:flex-start}:host(.toolbar-title-large) .toolbar-content ion-title{-ms-flex:1;flex:1;-ms-flex-order:8;order:8;min-width:100%}",md:":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-background-color, #fff));--color:var(--ion-toolbar-color, var(--ion-text-color, #424242));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, #c1c4cd))));--padding-top:0;--padding-bottom:0;--padding-start:0;--padding-end:0;--min-height:56px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:3;order:3;min-width:0;max-width:100%}::slotted(.buttons-first-slot){-webkit-margin-start:4px;margin-inline-start:4px}::slotted(.buttons-last-slot){-webkit-margin-end:4px;margin-inline-end:4px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:4;order:4}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}"},o})()},4576:(W,C,l)=>{l.d(C,{c:()=>E,g:()=>_,h:()=>n,o:()=>v});var b=l(467);const n=(p,h)=>null!==h.closest(p),E=(p,h)=>"string"==typeof p&&p.length>0?Object.assign({"ion-color":!0,[`ion-color-${p}`]:!0},h):h,_=p=>{const h={};return(p=>void 0!==p?(Array.isArray(p)?p:p.split(" ")).filter(f=>null!=f).map(f=>f.trim()).filter(f=>""!==f):[])(p).forEach(f=>h[f]=!0),h},y=/^[a-z][a-z0-9+\-.]*:/,v=function(){var p=(0,b.A)(function*(h,f,S,T){if(null!=h&&"#"!==h[0]&&!y.test(h)){const z=document.querySelector("ion-router");if(z)return f?.preventDefault(),z.push(h,S,T)}return!1});return function(f,S,T,z){return p.apply(this,arguments)}}()}}]);