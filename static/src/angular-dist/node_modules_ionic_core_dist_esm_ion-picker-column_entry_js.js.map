{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-picker-column_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC+I;AAChG;AACuB;AACiD;AACzD;AAC7B;AAEjC,MAAMuB,eAAe,GAAG,4mEAA4mE;AAEpoE,MAAMC,YAAY,GAAG,MAAM;EACvBC,WAAWA,CAACC,OAAO,EAAE;IACjBzB,qDAAgB,CAAC,IAAI,EAAEyB,OAAO,CAAC;IAC/B,IAAI,CAACC,SAAS,GAAGxB,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACyB,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,uBAAuB,GAAG,KAAK;IACpC,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,SAAS;IACtB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,sBAAsB,GAAG,CAACC,MAAM,EAAEC,MAAM,GAAG,IAAI,EAAET,gBAAgB,GAAG,IAAI,KAAK;MAC9E,MAAM;QAAED,eAAe;QAAEW;MAAS,CAAC,GAAG,IAAI;MAC1C,IAAIX,eAAe,IAAIW,QAAQ,EAAE;QAC7B;QACA,MAAMC,GAAG,GAAGH,MAAM,CAACI,SAAS,GAAG,CAAC,GAAGJ,MAAM,CAACK,YAAY,GAAGL,MAAM,CAACK,YAAY,GAAG,CAAC;QAChF,IAAIH,QAAQ,CAACI,SAAS,KAAKH,GAAG,EAAE;UAC5B;AACpB;AACA;AACA;AACA;AACA;AACA;UACoB,IAAI,CAACX,gBAAgB,GAAGA,gBAAgB;UACxC,IAAI,CAACC,uBAAuB,GAAG,KAAK;UACpCS,QAAQ,CAACK,MAAM,CAAC;YACZJ,GAAG;YACHK,IAAI,EAAE,CAAC;YACPC,QAAQ,EAAER,MAAM,GAAG,QAAQ,GAAGS;UAClC,CAAC,CAAC;QACN;MACJ;IACJ,CAAC;IACD,IAAI,CAACC,wBAAwB,GAAG,CAACC,IAAI,EAAEjB,QAAQ,KAAK;MAChD,IAAIA,QAAQ,EAAE;QACViB,IAAI,CAACC,SAAS,CAACC,GAAG,CAACC,wBAAwB,CAAC;MAChD,CAAC,MACI;QACDH,IAAI,CAACC,SAAS,CAACG,MAAM,CAACD,wBAAwB,CAAC;MACnD;IACJ,CAAC;IACD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACE,eAAe,GAAIC,EAAE,IAAK;MAC3B,IAAI,CAAC,IAAI,CAACpB,YAAY,EAAE;QACpB;MACJ;MACA,MAAM;QAAEqB,YAAY;QAAEC;MAAgB,CAAC,GAAGF,EAAE,CAACG,MAAM;MACnD;AACZ;AACA;AACA;MACY,MAAMC,cAAc,GAAGF,eAAe,KAAKV,SAAS,IAAIU,eAAe,KAAK,IAAI,CAACG,EAAE;MACnF,IAAI,CAACJ,YAAY,IAAI,CAACG,cAAc,EAAE;QAClC,IAAI,CAACE,kBAAkB,CAAC,KAAK,CAAC;QAC9B;MACJ;MACA,IAAI,CAACA,kBAAkB,CAAC,IAAI,CAAC;IACjC,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACA,kBAAkB,GAAIC,KAAK,IAAK;MACjC,IAAI,IAAI,CAACnC,WAAW,EAAE;QAClB,IAAI,CAACoC,iBAAiB,GAAG,MAAM;UAC3B,IAAI,CAAC/B,QAAQ,GAAG8B,KAAK;QACzB,CAAC;QACD;MACJ;MACA,IAAI,CAAC9B,QAAQ,GAAG8B,KAAK;IACzB,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACE,wBAAwB,GAAG,MAAM;MAClC;AACZ;AACA;AACA;AACA;MACY,MAAMC,aAAa,GAAG7D,qDAAU,CAAC,KAAK,CAAC;MACvC,MAAM;QAAEwD,EAAE;QAAErB;MAAS,CAAC,GAAG,IAAI;MAC7B,IAAI2B,OAAO;MACX,IAAIC,QAAQ,GAAG,IAAI,CAACC,UAAU;MAC9B,MAAMC,cAAc,GAAGA,CAAA,KAAM;QACzBxD,uDAAG,CAAC,MAAM;UACN,IAAIyD,EAAE;UACN,IAAI,CAAC/B,QAAQ,EACT;UACJ,IAAI2B,OAAO,EAAE;YACTK,YAAY,CAACL,OAAO,CAAC;YACrBA,OAAO,GAAGnB,SAAS;UACvB;UACA,IAAI,CAAC,IAAI,CAACpB,WAAW,EAAE;YACnBsC,aAAa,IAAIhD,sDAAoB,CAAC,CAAC;YACvC,IAAI,CAACU,WAAW,GAAG,IAAI;UAC3B;UACA;AACpB;AACA;AACA;UACoB,MAAM6C,IAAI,GAAGjC,QAAQ,CAACkC,qBAAqB,CAAC,CAAC;UAC7C,MAAMC,OAAO,GAAGF,IAAI,CAACG,CAAC,GAAGH,IAAI,CAACI,KAAK,GAAG,CAAC;UACvC,MAAMC,OAAO,GAAGL,IAAI,CAACM,CAAC,GAAGN,IAAI,CAACO,MAAM,GAAG,CAAC;UACxC;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoB,MAAMC,QAAQ,GAAGpB,EAAE,CAACqB,WAAW,CAAC,CAAC;UACjC,MAAMC,eAAe,GAAGF,QAAQ,YAAYG,UAAU;UACtD,MAAMC,aAAa,GAAGF,eAAe,GAAGF,QAAQ,GAAGpE,iDAAG;UACtD;AACpB;AACA;AACA;AACA;UACoB,IAAIwE,aAAa,KAAKrC,SAAS,EAAE;YAC7B;UACJ;UACA,MAAMsC,eAAe,GAAGD,aAAa,CAACE,iBAAiB,CAACZ,OAAO,EAAEG,OAAO,CAAC;UACzE;AACpB;AACA;AACA;UACoB,IAAIU,gBAAgB,GAAGF,eAAe,CAACG,IAAI,CAAE5B,EAAE,IAAKA,EAAE,CAAC6B,OAAO,KAAK,0BAA0B,CAAC;UAC9F;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoB,IAAIF,gBAAgB,KAAKxC,SAAS,EAAE;YAChC,MAAM2C,qBAAqB,GAAGN,aAAa,CAACO,gBAAgB,CAACjB,OAAO,EAAEG,OAAO,CAAC;YAC9E,IAAI,CAACa,qBAAqB,KAAK,IAAI,IAAIA,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACD,OAAO,MAAM,0BAA0B,EAAE;cAC9IF,gBAAgB,GAAGG,qBAAqB;YAC5C;UACJ;UACA,IAAIvB,QAAQ,KAAKpB,SAAS,EAAE;YACxB,IAAI,CAACC,wBAAwB,CAACmB,QAAQ,EAAE,KAAK,CAAC;UAClD;UACA,IAAIoB,gBAAgB,KAAKxC,SAAS,IAAIwC,gBAAgB,CAACtD,QAAQ,EAAE;YAC7D;UACJ;UACA;AACpB;AACA;AACA;UACoB,IAAIsD,gBAAgB,KAAKpB,QAAQ,EAAE;YAC/BF,aAAa,IAAI/C,sDAAsB,CAAC,CAAC;YACzC,IAAI,IAAI,CAACW,gBAAgB,EAAE;cACvB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;cAC4B,IAAI,CAAC+D,aAAa,CAAC,CAAC;YACxB;UACJ;UACAzB,QAAQ,GAAGoB,gBAAgB;UAC3B,IAAI,CAACvC,wBAAwB,CAACuC,gBAAgB,EAAE,IAAI,CAAC;UACrD;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoB,IAAI,IAAI,CAACzD,uBAAuB,EAAE;YAC9B,CAACwC,EAAE,GAAG,IAAI,CAACuB,kBAAkB,MAAM,IAAI,IAAIvB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwB,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAACC,kBAAkB,CAACR,gBAAgB,CAAC,CAAC;UACpJ;UACArB,OAAO,GAAG8B,UAAU,CAAC,MAAM;YACvB,IAAI,CAACrE,WAAW,GAAG,KAAK;YACxB,IAAI,CAACG,uBAAuB,GAAG,IAAI;YACnCmC,aAAa,IAAI9C,sDAAkB,CAAC,CAAC;YACrC;AACxB;AACA;AACA;AACA;AACA;YACwB,MAAM;cAAE4C;YAAkB,CAAC,GAAG,IAAI;YAClC,IAAIA,iBAAiB,EAAE;cACnBA,iBAAiB,CAAC,CAAC;cACnB,IAAI,CAACA,iBAAiB,GAAGhB,SAAS;YACtC;YACA;AACxB;AACA;AACA;AACA;AACA;YACwB,IAAI,CAAClB,gBAAgB,GAAG,IAAI;YAC5B,IAAI,CAACoE,QAAQ,CAACV,gBAAgB,CAACW,KAAK,CAAC;UACzC,CAAC,EAAE,GAAG,CAAC;QACX,CAAC,CAAC;MACN,CAAC;MACD;AACZ;AACA;AACA;MACYrF,uDAAG,CAAC,MAAM;QACN,IAAI,CAAC0B,QAAQ,EACT;QACJA,QAAQ,CAAC4D,gBAAgB,CAAC,QAAQ,EAAE9B,cAAc,CAAC;QACnD,IAAI,CAAC+B,qBAAqB,GAAG,MAAM;UAC/B7D,QAAQ,CAAC8D,mBAAmB,CAAC,QAAQ,EAAEhC,cAAc,CAAC;QAC1D,CAAC;MACL,CAAC,CAAC;IACN,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACuB,aAAa,GAAG,MAAM;MACvB,MAAM;QAAEU;MAAS,CAAC,GAAG,IAAI;MACzB,IAAIA,QAAQ,IAAI,IAAI,EAChB;MACJA,QAAQ,CAACV,aAAa,CAAC,CAAC;MACxB;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAAChC,EAAE,CAACV,SAAS,CAACG,MAAM,CAAC,sBAAsB,CAAC;IACpD,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACkD,cAAc,GAAG,CAACC,MAAM,GAAG,CAAC,KAAK;MAClC,MAAM;QAAEpC;MAAW,CAAC,GAAG,IAAI;MAC3B,IAAI,CAACA,UAAU,EACX,OAAO,IAAI;MACf,IAAIqC,QAAQ,GAAGrC,UAAU;MACzB,IAAIsC,IAAI,GAAGtC,UAAU,CAACuC,kBAAkB;MACxC,OAAOD,IAAI,IAAI,IAAI,EAAE;QACjB,IAAIF,MAAM,GAAG,CAAC,EAAE;UACZA,MAAM,EAAE;QACZ;QACA,IAAIE,IAAI,CAACjB,OAAO,KAAK,0BAA0B,IAAI,CAACiB,IAAI,CAACzE,QAAQ,IAAIuE,MAAM,KAAK,CAAC,EAAE;UAC/E,OAAOE,IAAI;QACf;QACAD,QAAQ,GAAGC,IAAI;QACf;QACAA,IAAI,GAAGA,IAAI,CAACC,kBAAkB;MAClC;MACA,OAAOF,QAAQ;IACnB,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACG,kBAAkB,GAAG,CAACJ,MAAM,GAAG,CAAC,KAAK;MACtC,MAAM;QAAEpC;MAAW,CAAC,GAAG,IAAI;MAC3B,IAAI,CAACA,UAAU,EACX,OAAO,IAAI;MACf,IAAIyC,QAAQ,GAAGzC,UAAU;MACzB,IAAIsC,IAAI,GAAGtC,UAAU,CAAC0C,sBAAsB;MAC5C,OAAOJ,IAAI,IAAI,IAAI,EAAE;QACjB,IAAIF,MAAM,GAAG,CAAC,EAAE;UACZA,MAAM,EAAE;QACZ;QACA,IAAIE,IAAI,CAACjB,OAAO,KAAK,0BAA0B,IAAI,CAACiB,IAAI,CAACzE,QAAQ,IAAIuE,MAAM,KAAK,CAAC,EAAE;UAC/E,OAAOE,IAAI;QACf;QACAG,QAAQ,GAAGH,IAAI;QACf;QACAA,IAAI,GAAGA,IAAI,CAACI,sBAAsB;MACtC;MACA,OAAOD,QAAQ;IACnB,CAAC;IACD,IAAI,CAACE,SAAS,GAAIxD,EAAE,IAAK;MACrB;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAMyD,MAAM,GAAG5G,qDAAU,CAAC,QAAQ,CAAC;MACnC,IAAI6G,SAAS,GAAG,IAAI;MACpB,QAAQ1D,EAAE,CAAC2D,GAAG;QACV,KAAK,WAAW;UACZD,SAAS,GAAGD,MAAM,GAAG,IAAI,CAACJ,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACL,cAAc,CAAC,CAAC;UACtE;QACJ,KAAK,SAAS;UACVU,SAAS,GAAGD,MAAM,GAAG,IAAI,CAACT,cAAc,CAAC,CAAC,GAAG,IAAI,CAACK,kBAAkB,CAAC,CAAC;UACtE;QACJ,KAAK,QAAQ;UACTK,SAAS,GAAGD,MAAM,GAAG,IAAI,CAACT,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAACK,kBAAkB,CAAC,CAAC,CAAC;UACxE;QACJ,KAAK,UAAU;UACXK,SAAS,GAAGD,MAAM,GAAG,IAAI,CAACJ,kBAAkB,CAAC,CAAC,CAAC,GAAG,IAAI,CAACL,cAAc,CAAC,CAAC,CAAC;UACxE;QACJ,KAAK,MAAM;UACP;AACpB;AACA;AACA;UACoBU,SAAS,GAAG,IAAI,CAACrD,EAAE,CAACuD,aAAa,CAAC,wCAAwC,CAAC;UAC3E;QACJ,KAAK,KAAK;UACN;AACpB;AACA;AACA;UACoBF,SAAS,GAAG,IAAI,CAACrD,EAAE,CAACuD,aAAa,CAAC,uCAAuC,CAAC;UAC1E;MACR;MACA,IAAIF,SAAS,KAAK,IAAI,EAAE;QACpB,IAAI,CAAChB,QAAQ,CAACgB,SAAS,CAACf,KAAK,CAAC;QAC9B;QACA3C,EAAE,CAAC6D,cAAc,CAAC,CAAC;MACvB;IACJ,CAAC;IACD;AACR;AACA;IACQ,IAAI,CAACrB,kBAAkB,GAAInC,EAAE,IAAK;MAC9B,IAAIU,EAAE;MACN,OAAOV,EAAE,GAAG,CAACU,EAAE,GAAGV,EAAE,CAACyD,YAAY,CAAC,YAAY,CAAC,MAAM,IAAI,IAAI/C,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGV,EAAE,CAAC0D,SAAS,GAAG,EAAE;IACvG,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,wBAAwB,GAAG,MAAM;MAClC,MAAM;QAAEnD;MAAW,CAAC,GAAG,IAAI;MAC3B,MAAMoD,SAAS,GAAG,IAAI,CAACzB,kBAAkB,CAAC3B,UAAU,CAAC;MACrD;AACZ;AACA;AACA;AACA;AACA;MACY,OAAQ/D,qDAAC,CAAC,KAAK,EAAE;QAAEoH,GAAG,EAAG7D,EAAE,IAAM,IAAI,CAACiC,kBAAkB,GAAGjC,EAAG;QAAE8D,KAAK,EAAE,qBAAqB;QAAEC,IAAI,EAAE,QAAQ;QAAEC,QAAQ,EAAE,IAAI,CAAC3F,QAAQ,GAAGc,SAAS,GAAG,CAAC;QAAE,YAAY,EAAE,IAAI,CAAChB,SAAS;QAAE,eAAe,EAAE,CAAC;QAAE,eAAe,EAAE,CAAC;QAAE,eAAe,EAAE,CAAC;QAAE,gBAAgB,EAAEyF,SAAS;QAAE,kBAAkB,EAAE,UAAU;QAAET,SAAS,EAAGxD,EAAE,IAAK,IAAI,CAACwD,SAAS,CAACxD,EAAE;MAAE,CAAC,CAAC;IAC1V,CAAC;EACL;EACAsE,gBAAgBA,CAACC,QAAQ,EAAE;IACvB,IAAI,CAAC/F,SAAS,GAAG+F,QAAQ;EAC7B;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACnG,eAAe,EAAE;MACtB;AACZ;AACA;AACA;MACY,IAAI,CAACoG,wBAAwB,CAAC,IAAI,CAAC;IACvC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,iBAAiBA,CAAA,EAAG;IAChB;AACR;AACA;AACA;AACA;AACA;IACQ,MAAM3B,QAAQ,GAAI,IAAI,CAACA,QAAQ,GAAG,IAAI,CAAC1C,EAAE,CAACsE,OAAO,CAAC,YAAY,CAAE;IAChE,MAAMC,eAAe,GAAIC,OAAO,IAAK;MACjC;AACZ;AACA;AACA;MACY,MAAM7E,EAAE,GAAG6E,OAAO,CAACA,OAAO,CAACC,MAAM,GAAG,CAAC,CAAC;MACtC,IAAI9E,EAAE,CAAC+E,cAAc,EAAE;QACnB,MAAM;UAAElE,UAAU;UAAER;QAAG,CAAC,GAAG,IAAI;QAC/B,IAAI,CAAChC,eAAe,GAAG,IAAI;QAC3B;AAChB;AACA;AACA;QACgB,MAAM2G,SAAS,GAAGxH,uDAAc,CAAC6C,EAAE,CAAC,CAACuD,aAAa,CAAC,IAAI/D,wBAAwB,EAAE,CAAC;QAClF,IAAImF,SAAS,EAAE;UACX,IAAI,CAACvF,wBAAwB,CAACuF,SAAS,EAAE,KAAK,CAAC;QACnD;QACA,IAAI,CAACP,wBAAwB,CAAC,CAAC;QAC/B,IAAI5D,UAAU,EAAE;UACZ,IAAI,CAACpB,wBAAwB,CAACoB,UAAU,EAAE,IAAI,CAAC;QACnD;QACA,IAAI,CAACJ,wBAAwB,CAAC,CAAC;MACnC,CAAC,MACI;QACD,IAAI,CAACpC,eAAe,GAAG,KAAK;QAC5B,IAAI,IAAI,CAACwE,qBAAqB,EAAE;UAC5B,IAAI,CAACA,qBAAqB,CAAC,CAAC;UAC5B,IAAI,CAACA,qBAAqB,GAAGrD,SAAS;QAC1C;MACJ;IACJ,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIyF,oBAAoB,CAACL,eAAe,EAAE;MAAEM,SAAS,EAAE,KAAK;MAAEC,IAAI,EAAE,IAAI,CAACpC;IAAS,CAAC,CAAC,CAACqC,OAAO,CAAC,IAAI,CAAC/E,EAAE,CAAC;IACrG,IAAI0C,QAAQ,KAAK,IAAI,EAAE;MACnB;MACAA,QAAQ,CAACH,gBAAgB,CAAC,oBAAoB,EAAG5C,EAAE,IAAK,IAAI,CAACD,eAAe,CAACC,EAAE,CAAC,CAAC;IACrF;EACJ;EACAqF,kBAAkBA,CAAA,EAAG;IACjB,MAAM;MAAEhF,EAAE;MAAEQ,UAAU;MAAExC,eAAe;MAAEsE;IAAM,CAAC,GAAG,IAAI;IACvD,IAAItE,eAAe,IAAI,CAACwC,UAAU,EAAE;MAChC,MAAMyE,WAAW,GAAGjF,EAAE,CAACuD,aAAa,CAAC,0BAA0B,CAAC;MAChE;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI0B,WAAW,KAAK,IAAI,IAAIA,WAAW,CAAC3C,KAAK,KAAKA,KAAK,EAAE;QACrD,IAAI,CAACD,QAAQ,CAAC4C,WAAW,CAAC3C,KAAK,CAAC;MACpC;IACJ;EACJ;EACA;EACM8B,wBAAwBA,CAAA,EAAiB;IAAA,IAAAc,KAAA;IAAA,OAAAC,oMAAA,YAAhBzG,MAAM,GAAG,KAAK;MACzC,MAAM6B,QAAQ,GAAG2E,KAAI,CAAC1E,UAAU;MAChC,IAAID,QAAQ,EAAE;QACV2E,KAAI,CAAC1G,sBAAsB,CAAC+B,QAAQ,EAAE7B,MAAM,EAAE,KAAK,CAAC;MACxD;IAAC,GAAA0G,KAAA,OAAAC,SAAA;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUhD,QAAQA,CAACC,KAAK,EAAE;IAAA,IAAAgD,MAAA;IAAA,OAAAH,oMAAA;MAClB,IAAIG,MAAI,CAACjH,QAAQ,KAAK,IAAI,IAAIiH,MAAI,CAAChD,KAAK,KAAKA,KAAK,EAAE;QAChD;MACJ;MACAgD,MAAI,CAAChD,KAAK,GAAGA,KAAK;MAClBgD,MAAI,CAACxH,SAAS,CAACyH,IAAI,CAAC;QAAEjD;MAAM,CAAC,CAAC;IAAC;EACnC;EACA;AACJ;AACA;AACA;EACUkD,QAAQA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAN,oMAAA;MACb,IAAIM,MAAI,CAACxD,kBAAkB,EAAE;QACzBwD,MAAI,CAACxD,kBAAkB,CAACyD,KAAK,CAAC,CAAC;MACnC;IAAC;EACL;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAIjF,EAAE;IACN,IAAI,CAACvC,SAAS,GAAG,CAACuC,EAAE,GAAG,IAAI,CAACV,EAAE,CAACyD,YAAY,CAAC,YAAY,CAAC,MAAM,IAAI,IAAI/C,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,gBAAgB;EAChH;EACA,IAAIF,UAAUA,CAAA,EAAG;IACb,MAAM;MAAE8B;IAAM,CAAC,GAAG,IAAI;IACtB,MAAMsD,OAAO,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC9F,EAAE,CAAC+F,gBAAgB,CAAC,0BAA0B,CAAC,CAAC;IAChF,OAAOH,OAAO,CAAChE,IAAI,CAAEoE,MAAM,IAAK;MAC5B;AACZ;AACA;AACA;MACY,IAAI,CAAC,IAAI,CAAC3H,QAAQ,IAAI2H,MAAM,CAAC3H,QAAQ,EAAE;QACnC,OAAO,KAAK;MAChB;MACA,OAAO2H,MAAM,CAAC1D,KAAK,KAAKA,KAAK;IACjC,CAAC,CAAC;EACN;EACA2D,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE3H,KAAK;MAAED,QAAQ;MAAED,QAAQ;MAAEG;IAAa,CAAC,GAAG,IAAI;IACxD,MAAM2H,IAAI,GAAGvJ,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQF,qDAAC,CAACI,iDAAI,EAAE;MAAEyG,GAAG,EAAE,0CAA0C;MAAEQ,KAAK,EAAErG,qDAAkB,CAACa,KAAK,EAAE;QAC5F,CAAC4H,IAAI,GAAG,IAAI;QACZ,CAAC,sBAAsB,GAAG9H,QAAQ;QAClC,CAAC,6BAA6B,GAAGG,YAAY;QAC7C,CAAC,wBAAwB,GAAGF;MAChC,CAAC;IAAE,CAAC,EAAE,IAAI,CAACsF,wBAAwB,CAAC,CAAC,EAAElH,qDAAC,CAAC,MAAM,EAAE;MAAE6G,GAAG,EAAE,0CAA0C;MAAE6C,IAAI,EAAE;IAAS,CAAC,CAAC,EAAE1J,qDAAC,CAAC,KAAK,EAAE;MAAE6G,GAAG,EAAE,0CAA0C;MAAE,aAAa,EAAE,MAAM;MAAEQ,KAAK,EAAE,aAAa;MAAED,GAAG,EAAG7D,EAAE,IAAK;QACzO,IAAI,CAACrB,QAAQ,GAAGqB,EAAE;MACtB,CAAC;MACD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACYoG,QAAQ,EAAE,CAAC;IAAE,CAAC,EAAE3J,qDAAC,CAAC,KAAK,EAAE;MAAE6G,GAAG,EAAE,0CAA0C;MAAEQ,KAAK,EAAE,mBAAmB;MAAE,aAAa,EAAE;IAAO,CAAC,EAAE,QAAQ,CAAC,EAAErH,qDAAC,CAAC,KAAK,EAAE;MAAE6G,GAAG,EAAE,0CAA0C;MAAEQ,KAAK,EAAE,mBAAmB;MAAE,aAAa,EAAE;IAAO,CAAC,EAAE,QAAQ,CAAC,EAAErH,qDAAC,CAAC,KAAK,EAAE;MAAE6G,GAAG,EAAE,0CAA0C;MAAEQ,KAAK,EAAE,mBAAmB;MAAE,aAAa,EAAE;IAAO,CAAC,EAAE,QAAQ,CAAC,EAAErH,qDAAC,CAAC,MAAM,EAAE;MAAE6G,GAAG,EAAE;IAA2C,CAAC,CAAC,EAAE7G,qDAAC,CAAC,KAAK,EAAE;MAAE6G,GAAG,EAAE,0CAA0C;MAAEQ,KAAK,EAAE,mBAAmB;MAAE,aAAa,EAAE;IAAO,CAAC,EAAE,QAAQ,CAAC,EAAErH,qDAAC,CAAC,KAAK,EAAE;MAAE6G,GAAG,EAAE,0CAA0C;MAAEQ,KAAK,EAAE,mBAAmB;MAAE,aAAa,EAAE;IAAO,CAAC,EAAE,QAAQ,CAAC,EAAErH,qDAAC,CAAC,KAAK,EAAE;MAAE6G,GAAG,EAAE,0CAA0C;MAAEQ,KAAK,EAAE,mBAAmB;MAAE,aAAa,EAAE;IAAO,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAErH,qDAAC,CAAC,MAAM,EAAE;MAAE6G,GAAG,EAAE,0CAA0C;MAAE6C,IAAI,EAAE;IAAS,CAAC,CAAC,CAAC;EACh5B;EACA,IAAInG,EAAEA,CAAA,EAAG;IAAE,OAAOjD,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWsJ,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,YAAY,EAAE,CAAC,kBAAkB,CAAC;MAClC,OAAO,EAAE,CAAC,aAAa;IAC3B,CAAC;EAAE;AACP,CAAC;AACD,MAAM7G,wBAAwB,GAAG,eAAe;AAChD7B,YAAY,CAAC2I,KAAK,GAAG5I,eAAe", "sources": ["./node_modules/@ionic/core/dist/esm/ion-picker-column.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, a as isPlatform, h, e as getIonMode, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { d as doc } from './index-ZjP4CjeZ.js';\nimport { r as raf, g as getElementRoot } from './helpers-1O4D2b7y.js';\nimport { b as hapticSelectionStart, a as hapticSelectionChanged, h as hapticSelectionEnd } from './haptic-DzAMWJuk.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\nimport './capacitor-CFERIeaU.js';\n\nconst pickerColumnCss = \":host{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;max-width:100%;height:200px;font-size:22px;text-align:center}.assistive-focusable{left:0;right:0;top:0;bottom:0;position:absolute;z-index:1;pointer-events:none}.assistive-focusable:focus{outline:none}.picker-opts{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0px;padding-bottom:0px;min-width:26px;max-height:200px;outline:none;text-align:inherit;-webkit-scroll-snap-type:y mandatory;-ms-scroll-snap-type:y mandatory;scroll-snap-type:y mandatory;overflow-x:hidden;overflow-y:scroll;scrollbar-width:none}.picker-item-empty{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.picker-opts::-webkit-scrollbar{display:none}::slotted(ion-picker-column-option){display:block;scroll-snap-align:center}.picker-item-empty,:host(:not([disabled])) ::slotted(ion-picker-column-option.option-disabled){scroll-snap-align:none}::slotted([slot=prefix]),::slotted([slot=suffix]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}::slotted([slot=prefix]){-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0;padding-bottom:0;-ms-flex-pack:end;justify-content:end}::slotted([slot=suffix]){-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0;padding-bottom:0;-ms-flex-pack:start;justify-content:start}:host(.picker-column-disabled) .picker-opts{overflow-y:hidden}:host(.picker-column-disabled) ::slotted(ion-picker-column-option){cursor:default;opacity:0.4;pointer-events:none}@media (any-hover: hover){:host(:focus) .picker-opts{outline:none;background:rgba(var(--ion-color-base-rgb), 0.2)}}\";\n\nconst PickerColumn = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.isScrolling = false;\n        this.isColumnVisible = false;\n        this.canExitInputMode = true;\n        this.updateValueTextOnScroll = false;\n        this.ariaLabel = null;\n        this.isActive = false;\n        /**\n         * If `true`, the user cannot interact with the picker.\n         */\n        this.disabled = false;\n        /**\n         * The color to use from your application's color palette.\n         * Default options are: `\"primary\"`, `\"secondary\"`, `\"tertiary\"`, `\"success\"`, `\"warning\"`, `\"danger\"`, `\"light\"`, `\"medium\"`, and `\"dark\"`.\n         * For more information on colors, see [theming](/docs/theming/basics).\n         */\n        this.color = 'primary';\n        /**\n         * If `true`, tapping the picker will\n         * reveal a number input keyboard that lets\n         * the user type in values for each picker\n         * column. This is useful when working\n         * with time pickers.\n         *\n         * @internal\n         */\n        this.numericInput = false;\n        this.centerPickerItemInView = (target, smooth = true, canExitInputMode = true) => {\n            const { isColumnVisible, scrollEl } = this;\n            if (isColumnVisible && scrollEl) {\n                // (Vertical offset from parent) - (three empty picker rows) + (half the height of the target to ensure the scroll triggers)\n                const top = target.offsetTop - 3 * target.clientHeight + target.clientHeight / 2;\n                if (scrollEl.scrollTop !== top) {\n                    /**\n                     * Setting this flag prevents input\n                     * mode from exiting in the picker column's\n                     * scroll callback. This is useful when the user manually\n                     * taps an item or types on the keyboard as both\n                     * of these can cause a scroll to occur.\n                     */\n                    this.canExitInputMode = canExitInputMode;\n                    this.updateValueTextOnScroll = false;\n                    scrollEl.scroll({\n                        top,\n                        left: 0,\n                        behavior: smooth ? 'smooth' : undefined,\n                    });\n                }\n            }\n        };\n        this.setPickerItemActiveState = (item, isActive) => {\n            if (isActive) {\n                item.classList.add(PICKER_ITEM_ACTIVE_CLASS);\n            }\n            else {\n                item.classList.remove(PICKER_ITEM_ACTIVE_CLASS);\n            }\n        };\n        /**\n         * When ionInputModeChange is emitted, each column\n         * needs to check if it is the one being made available\n         * for text entry.\n         */\n        this.inputModeChange = (ev) => {\n            if (!this.numericInput) {\n                return;\n            }\n            const { useInputMode, inputModeColumn } = ev.detail;\n            /**\n             * If inputModeColumn is undefined then this means\n             * all numericInput columns are being selected.\n             */\n            const isColumnActive = inputModeColumn === undefined || inputModeColumn === this.el;\n            if (!useInputMode || !isColumnActive) {\n                this.setInputModeActive(false);\n                return;\n            }\n            this.setInputModeActive(true);\n        };\n        /**\n         * Setting isActive will cause a re-render.\n         * As a result, we do not want to cause the\n         * re-render mid scroll as this will cause\n         * the picker column to jump back to\n         * whatever value was selected at the\n         * start of the scroll interaction.\n         */\n        this.setInputModeActive = (state) => {\n            if (this.isScrolling) {\n                this.scrollEndCallback = () => {\n                    this.isActive = state;\n                };\n                return;\n            }\n            this.isActive = state;\n        };\n        /**\n         * When the column scrolls, the component\n         * needs to determine which item is centered\n         * in the view and will emit an ionChange with\n         * the item object.\n         */\n        this.initializeScrollListener = () => {\n            /**\n             * The haptics for the wheel picker are\n             * an iOS-only feature. As a result, they should\n             * be disabled on Android.\n             */\n            const enableHaptics = isPlatform('ios');\n            const { el, scrollEl } = this;\n            let timeout;\n            let activeEl = this.activeItem;\n            const scrollCallback = () => {\n                raf(() => {\n                    var _a;\n                    if (!scrollEl)\n                        return;\n                    if (timeout) {\n                        clearTimeout(timeout);\n                        timeout = undefined;\n                    }\n                    if (!this.isScrolling) {\n                        enableHaptics && hapticSelectionStart();\n                        this.isScrolling = true;\n                    }\n                    /**\n                     * Select item in the center of the column\n                     * which is the month/year that we want to select\n                     */\n                    const bbox = scrollEl.getBoundingClientRect();\n                    const centerX = bbox.x + bbox.width / 2;\n                    const centerY = bbox.y + bbox.height / 2;\n                    /**\n                     * elementFromPoint returns the top-most element.\n                     * This means that if an ion-backdrop is overlaying the\n                     * picker then the appropriate picker column option will\n                     * not be selected. To account for this, we use elementsFromPoint\n                     * and use an Array.find to find the appropriate column option\n                     * at that point.\n                     *\n                     * Additionally, the picker column could be used in the\n                     * Shadow DOM (i.e. in ion-datetime) so we need to make\n                     * sure we are choosing the correct host otherwise\n                     * the elements returns by elementsFromPoint will be\n                     * retargeted. To account for this, we check to see\n                     * if the picker column has a parent shadow root. If\n                     * so, we use that shadow root when doing elementsFromPoint.\n                     * Otherwise, we just use the document.\n                     */\n                    const rootNode = el.getRootNode();\n                    const hasParentShadow = rootNode instanceof ShadowRoot;\n                    const referenceNode = hasParentShadow ? rootNode : doc;\n                    /**\n                     * If the reference node is undefined\n                     * then it's likely that doc is undefined\n                     * due to being in an SSR environment.\n                     */\n                    if (referenceNode === undefined) {\n                        return;\n                    }\n                    const elementsAtPoint = referenceNode.elementsFromPoint(centerX, centerY);\n                    /**\n                     * elementsFromPoint can returns multiple elements\n                     * so find the relevant picker column option if one exists.\n                     */\n                    let newActiveElement = elementsAtPoint.find((el) => el.tagName === 'ION-PICKER-COLUMN-OPTION');\n                    /**\n                     * TODO(FW-6594): Remove this workaround when iOS 16 is no longer\n                     * supported.\n                     *\n                     * If `elementsFromPoint` failed to find the active element (a known\n                     * issue on iOS 16 when elements are in a Shadow DOM and the\n                     * referenceNode is the document), a fallback to `elementFromPoint`\n                     * is used. While `elementsFromPoint` returns all elements,\n                     * `elementFromPoint` returns only the top-most, which is sufficient\n                     * for this use case and appears to handle Shadow DOM retargeting\n                     * more reliably in this specific iOS bug.\n                     */\n                    if (newActiveElement === undefined) {\n                        const fallbackActiveElement = referenceNode.elementFromPoint(centerX, centerY);\n                        if ((fallbackActiveElement === null || fallbackActiveElement === void 0 ? void 0 : fallbackActiveElement.tagName) === 'ION-PICKER-COLUMN-OPTION') {\n                            newActiveElement = fallbackActiveElement;\n                        }\n                    }\n                    if (activeEl !== undefined) {\n                        this.setPickerItemActiveState(activeEl, false);\n                    }\n                    if (newActiveElement === undefined || newActiveElement.disabled) {\n                        return;\n                    }\n                    /**\n                     * If we are selecting a new value,\n                     * we need to run haptics again.\n                     */\n                    if (newActiveElement !== activeEl) {\n                        enableHaptics && hapticSelectionChanged();\n                        if (this.canExitInputMode) {\n                            /**\n                             * The native iOS wheel picker\n                             * only dismisses the keyboard\n                             * once the selected item has changed\n                             * as a result of a swipe\n                             * from the user. If `canExitInputMode` is\n                             * `false` then this means that the\n                             * scroll is happening as a result of\n                             * the `value` property programmatically changing\n                             * either by an application or by the user via the keyboard.\n                             */\n                            this.exitInputMode();\n                        }\n                    }\n                    activeEl = newActiveElement;\n                    this.setPickerItemActiveState(newActiveElement, true);\n                    /**\n                     * Set the aria-valuetext even though the value prop has not been updated yet.\n                     * This enables some screen readers to announce the value as the users drag\n                     * as opposed to when their release their pointer from the screen.\n                     *\n                     * When the value is programmatically updated, we will smoothly scroll\n                     * to the new option. However, we do not want to update aria-valuetext mid-scroll\n                     * as that can cause the old value to be briefly set before being set to the\n                     * correct option. This will cause some screen readers to announce the old value\n                     * again before announcing the new value. The correct valuetext will be set on render.\n                     */\n                    if (this.updateValueTextOnScroll) {\n                        (_a = this.assistiveFocusable) === null || _a === void 0 ? void 0 : _a.setAttribute('aria-valuetext', this.getOptionValueText(newActiveElement));\n                    }\n                    timeout = setTimeout(() => {\n                        this.isScrolling = false;\n                        this.updateValueTextOnScroll = true;\n                        enableHaptics && hapticSelectionEnd();\n                        /**\n                         * Certain tasks (such as those that\n                         * cause re-renders) should only be done\n                         * once scrolling has finished, otherwise\n                         * flickering may occur.\n                         */\n                        const { scrollEndCallback } = this;\n                        if (scrollEndCallback) {\n                            scrollEndCallback();\n                            this.scrollEndCallback = undefined;\n                        }\n                        /**\n                         * Reset this flag as the\n                         * next scroll interaction could\n                         * be a scroll from the user. In this\n                         * case, we should exit input mode.\n                         */\n                        this.canExitInputMode = true;\n                        this.setValue(newActiveElement.value);\n                    }, 250);\n                });\n            };\n            /**\n             * Wrap this in an raf so that the scroll callback\n             * does not fire when component is initially shown.\n             */\n            raf(() => {\n                if (!scrollEl)\n                    return;\n                scrollEl.addEventListener('scroll', scrollCallback);\n                this.destroyScrollListener = () => {\n                    scrollEl.removeEventListener('scroll', scrollCallback);\n                };\n            });\n        };\n        /**\n         * Tells the parent picker to\n         * exit text entry mode. This is only called\n         * when the selected item changes during scroll, so\n         * we know that the user likely wants to scroll\n         * instead of type.\n         */\n        this.exitInputMode = () => {\n            const { parentEl } = this;\n            if (parentEl == null)\n                return;\n            parentEl.exitInputMode();\n            /**\n             * setInputModeActive only takes\n             * effect once scrolling stops to avoid\n             * a component re-render while scrolling.\n             * However, we want the visual active\n             * indicator to go away immediately, so\n             * we call classList.remove here.\n             */\n            this.el.classList.remove('picker-column-active');\n        };\n        /**\n         * Find the next enabled option after the active option.\n         * @param stride - How many options to \"jump\" over in order to select the next option.\n         * This can be used to implement PageUp/PageDown behaviors where pressing these keys\n         * scrolls the picker by more than 1 option. For example, a stride of 5 means select\n         * the enabled option 5 options after the active one. Note that the actual option selected\n         * may be past the stride if the option at the stride is disabled.\n         */\n        this.findNextOption = (stride = 1) => {\n            const { activeItem } = this;\n            if (!activeItem)\n                return null;\n            let prevNode = activeItem;\n            let node = activeItem.nextElementSibling;\n            while (node != null) {\n                if (stride > 0) {\n                    stride--;\n                }\n                if (node.tagName === 'ION-PICKER-COLUMN-OPTION' && !node.disabled && stride === 0) {\n                    return node;\n                }\n                prevNode = node;\n                // Use nextElementSibling instead of nextSibling to avoid text/comment nodes\n                node = node.nextElementSibling;\n            }\n            return prevNode;\n        };\n        /**\n         * Find the next enabled option after the active option.\n         * @param stride - How many options to \"jump\" over in order to select the next option.\n         * This can be used to implement PageUp/PageDown behaviors where pressing these keys\n         * scrolls the picker by more than 1 option. For example, a stride of 5 means select\n         * the enabled option 5 options before the active one. Note that the actual option selected\n         *  may be past the stride if the option at the stride is disabled.\n         */\n        this.findPreviousOption = (stride = 1) => {\n            const { activeItem } = this;\n            if (!activeItem)\n                return null;\n            let nextNode = activeItem;\n            let node = activeItem.previousElementSibling;\n            while (node != null) {\n                if (stride > 0) {\n                    stride--;\n                }\n                if (node.tagName === 'ION-PICKER-COLUMN-OPTION' && !node.disabled && stride === 0) {\n                    return node;\n                }\n                nextNode = node;\n                // Use previousElementSibling instead of previousSibling to avoid text/comment nodes\n                node = node.previousElementSibling;\n            }\n            return nextNode;\n        };\n        this.onKeyDown = (ev) => {\n            /**\n             * The below operations should be inverted when running on a mobile device.\n             * For example, swiping up will dispatch an \"ArrowUp\" event. On desktop,\n             * this should cause the previous option to be selected. On mobile, swiping\n             * up causes a view to scroll down. As a result, swiping up on mobile should\n             * cause the next option to be selected. The Home/End operations remain\n             * unchanged because those always represent the first/last options, respectively.\n             */\n            const mobile = isPlatform('mobile');\n            let newOption = null;\n            switch (ev.key) {\n                case 'ArrowDown':\n                    newOption = mobile ? this.findPreviousOption() : this.findNextOption();\n                    break;\n                case 'ArrowUp':\n                    newOption = mobile ? this.findNextOption() : this.findPreviousOption();\n                    break;\n                case 'PageUp':\n                    newOption = mobile ? this.findNextOption(5) : this.findPreviousOption(5);\n                    break;\n                case 'PageDown':\n                    newOption = mobile ? this.findPreviousOption(5) : this.findNextOption(5);\n                    break;\n                case 'Home':\n                    /**\n                     * There is no guarantee that the first child will be an ion-picker-column-option,\n                     * so we do not use firstElementChild.\n                     */\n                    newOption = this.el.querySelector('ion-picker-column-option:first-of-type');\n                    break;\n                case 'End':\n                    /**\n                     * There is no guarantee that the last child will be an ion-picker-column-option,\n                     * so we do not use lastElementChild.\n                     */\n                    newOption = this.el.querySelector('ion-picker-column-option:last-of-type');\n                    break;\n            }\n            if (newOption !== null) {\n                this.setValue(newOption.value);\n                // This stops any default browser behavior such as scrolling\n                ev.preventDefault();\n            }\n        };\n        /**\n         * Utility to generate the correct text for aria-valuetext.\n         */\n        this.getOptionValueText = (el) => {\n            var _a;\n            return el ? (_a = el.getAttribute('aria-label')) !== null && _a !== void 0 ? _a : el.innerText : '';\n        };\n        /**\n         * Render an element that overlays the column. This element is for assistive\n         * tech to allow users to navigate the column up/down. This element should receive\n         * focus as it listens for synthesized keyboard events as required by the\n         * slider role: https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/slider_role\n         */\n        this.renderAssistiveFocusable = () => {\n            const { activeItem } = this;\n            const valueText = this.getOptionValueText(activeItem);\n            /**\n             * When using the picker, the valuetext provides important context that valuenow\n             * does not. Additionally, using non-zero valuemin/valuemax values can cause\n             * WebKit to incorrectly announce numeric valuetext values (such as a year\n             * like \"2024\") as percentages: https://bugs.webkit.org/show_bug.cgi?id=273126\n             */\n            return (h(\"div\", { ref: (el) => (this.assistiveFocusable = el), class: \"assistive-focusable\", role: \"slider\", tabindex: this.disabled ? undefined : 0, \"aria-label\": this.ariaLabel, \"aria-valuemin\": 0, \"aria-valuemax\": 0, \"aria-valuenow\": 0, \"aria-valuetext\": valueText, \"aria-orientation\": \"vertical\", onKeyDown: (ev) => this.onKeyDown(ev) }));\n        };\n    }\n    ariaLabelChanged(newValue) {\n        this.ariaLabel = newValue;\n    }\n    valueChange() {\n        if (this.isColumnVisible) {\n            /**\n             * Only scroll the active item into view when the picker column\n             * is actively visible to the user.\n             */\n            this.scrollActiveItemIntoView(true);\n        }\n    }\n    /**\n     * Only setup scroll listeners\n     * when the picker is visible, otherwise\n     * the container will have a scroll\n     * height of 0px.\n     */\n    componentWillLoad() {\n        /**\n         * We cache parentEl in a local variable\n         * so we don't need to keep accessing\n         * the class variable (which comes with\n         * a small performance hit)\n         */\n        const parentEl = (this.parentEl = this.el.closest('ion-picker'));\n        const visibleCallback = (entries) => {\n            /**\n             * Browsers will sometimes group multiple IO events into a single callback.\n             * As a result, we want to grab the last/most recent event in case there are multiple events.\n             */\n            const ev = entries[entries.length - 1];\n            if (ev.isIntersecting) {\n                const { activeItem, el } = this;\n                this.isColumnVisible = true;\n                /**\n                 * Because this initial call to scrollActiveItemIntoView has to fire before\n                 * the scroll listener is set up, we need to manage the active class manually.\n                 */\n                const oldActive = getElementRoot(el).querySelector(`.${PICKER_ITEM_ACTIVE_CLASS}`);\n                if (oldActive) {\n                    this.setPickerItemActiveState(oldActive, false);\n                }\n                this.scrollActiveItemIntoView();\n                if (activeItem) {\n                    this.setPickerItemActiveState(activeItem, true);\n                }\n                this.initializeScrollListener();\n            }\n            else {\n                this.isColumnVisible = false;\n                if (this.destroyScrollListener) {\n                    this.destroyScrollListener();\n                    this.destroyScrollListener = undefined;\n                }\n            }\n        };\n        /**\n         * Set the root to be the parent picker element\n         * This causes the IO callback\n         * to be fired in WebKit as soon as the element\n         * is visible. If we used the default root value\n         * then WebKit would only fire the IO callback\n         * after any animations (such as a modal transition)\n         * finished, and there would potentially be a flicker.\n         */\n        new IntersectionObserver(visibleCallback, { threshold: 0.001, root: this.parentEl }).observe(this.el);\n        if (parentEl !== null) {\n            // TODO(FW-2832): type\n            parentEl.addEventListener('ionInputModeChange', (ev) => this.inputModeChange(ev));\n        }\n    }\n    componentDidRender() {\n        const { el, activeItem, isColumnVisible, value } = this;\n        if (isColumnVisible && !activeItem) {\n            const firstOption = el.querySelector('ion-picker-column-option');\n            /**\n             * If the picker column does not have an active item and the current value\n             * does not match the first item in the picker column, that means\n             * the value is out of bounds. In this case, we assign the value to the\n             * first item to match the scroll position of the column.\n             *\n             */\n            if (firstOption !== null && firstOption.value !== value) {\n                this.setValue(firstOption.value);\n            }\n        }\n    }\n    /** @internal  */\n    async scrollActiveItemIntoView(smooth = false) {\n        const activeEl = this.activeItem;\n        if (activeEl) {\n            this.centerPickerItemInView(activeEl, smooth, false);\n        }\n    }\n    /**\n     * Sets the value prop and fires the ionChange event.\n     * This is used when we need to fire ionChange from\n     * user-generated events that cannot be caught with normal\n     * input/change event listeners.\n     * @internal\n     */\n    async setValue(value) {\n        if (this.disabled === true || this.value === value) {\n            return;\n        }\n        this.value = value;\n        this.ionChange.emit({ value });\n    }\n    /**\n     * Sets focus on the scrollable container within the picker column.\n     * Use this method instead of the global `pickerColumn.focus()`.\n     */\n    async setFocus() {\n        if (this.assistiveFocusable) {\n            this.assistiveFocusable.focus();\n        }\n    }\n    connectedCallback() {\n        var _a;\n        this.ariaLabel = (_a = this.el.getAttribute('aria-label')) !== null && _a !== void 0 ? _a : 'Select a value';\n    }\n    get activeItem() {\n        const { value } = this;\n        const options = Array.from(this.el.querySelectorAll('ion-picker-column-option'));\n        return options.find((option) => {\n            /**\n             * If the whole picker column is disabled, the current value should appear active\n             * If the current value item is specifically disabled, it should not appear active\n             */\n            if (!this.disabled && option.disabled) {\n                return false;\n            }\n            return option.value === value;\n        });\n    }\n    render() {\n        const { color, disabled, isActive, numericInput } = this;\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'ea0280355b2f87895bf7dddd289ccf473aa759f3', class: createColorClasses(color, {\n                [mode]: true,\n                ['picker-column-active']: isActive,\n                ['picker-column-numeric-input']: numericInput,\n                ['picker-column-disabled']: disabled,\n            }) }, this.renderAssistiveFocusable(), h(\"slot\", { key: '482992131cdeb85b1f61430d7fe1322a16345769', name: \"prefix\" }), h(\"div\", { key: '43f7f80d621d411ef366b3ca1396299e8c9a0c97', \"aria-hidden\": \"true\", class: \"picker-opts\", ref: (el) => {\n                this.scrollEl = el;\n            },\n            /**\n             * When an element has an overlay scroll style and\n             * a fixed height, Firefox will focus the scrollable\n             * container if the content exceeds the container's\n             * dimensions.\n             *\n             * This causes keyboard navigation to focus to this\n             * element instead of going to the next element in\n             * the tab order.\n             *\n             * The desired behavior is for the user to be able to\n             * focus the assistive focusable element and tab to\n             * the next element in the tab order. Instead of tabbing\n             * to this element.\n             *\n             * To prevent this, we set the tabIndex to -1. This\n             * will match the behavior of the other browsers.\n             */\n            tabIndex: -1 }, h(\"div\", { key: '13a9ee686132af32240710730765de4c0003a9e8', class: \"picker-item-empty\", \"aria-hidden\": \"true\" }, \"\\u00A0\"), h(\"div\", { key: 'dbccba4920833cfcebe9b0fc763458ec3053705a', class: \"picker-item-empty\", \"aria-hidden\": \"true\" }, \"\\u00A0\"), h(\"div\", { key: '682b43f83a5ea2e46067457f3af118535e111edb', class: \"picker-item-empty\", \"aria-hidden\": \"true\" }, \"\\u00A0\"), h(\"slot\", { key: 'd27e1e1dc0504b2f4627a29912a05bb91e8e413a' }), h(\"div\", { key: '61c948dbb9cf7469aed3018542bc0954211585ba', class: \"picker-item-empty\", \"aria-hidden\": \"true\" }, \"\\u00A0\"), h(\"div\", { key: 'cf46c277fbee65e35ff44ce0d53ce12aa9cbf9db', class: \"picker-item-empty\", \"aria-hidden\": \"true\" }, \"\\u00A0\"), h(\"div\", { key: 'bbc0e2d491d3f836ab849493ade2f7fa6ad9244e', class: \"picker-item-empty\", \"aria-hidden\": \"true\" }, \"\\u00A0\")), h(\"slot\", { key: 'd25cbbe14b2914fe7b878d43b4e3f4a8c8177d24', name: \"suffix\" })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"aria-label\": [\"ariaLabelChanged\"],\n        \"value\": [\"valueChange\"]\n    }; }\n};\nconst PICKER_ITEM_ACTIVE_CLASS = 'option-active';\nPickerColumn.style = pickerColumnCss;\n\nexport { PickerColumn as ion_picker_column };\n"], "names": ["r", "registerInstance", "d", "createEvent", "a", "isPlatform", "h", "e", "getIonMode", "j", "Host", "k", "getElement", "doc", "raf", "g", "getElementRoot", "b", "hapticSelectionStart", "hapticSelectionChanged", "hapticSelectionEnd", "c", "createColorClasses", "pickerColumnCss", "PickerColumn", "constructor", "hostRef", "ionChange", "isScrolling", "isColumnVisible", "canExitInputMode", "updateValueTextOnScroll", "aria<PERSON><PERSON><PERSON>", "isActive", "disabled", "color", "numericInput", "centerPickerItemInView", "target", "smooth", "scrollEl", "top", "offsetTop", "clientHeight", "scrollTop", "scroll", "left", "behavior", "undefined", "setPickerItemActiveState", "item", "classList", "add", "PICKER_ITEM_ACTIVE_CLASS", "remove", "inputModeChange", "ev", "useInputMode", "inputModeColumn", "detail", "isColumnActive", "el", "setInputModeActive", "state", "scrollEndCallback", "initializeScrollListener", "enableHaptics", "timeout", "activeEl", "activeItem", "scrollCallback", "_a", "clearTimeout", "bbox", "getBoundingClientRect", "centerX", "x", "width", "centerY", "y", "height", "rootNode", "getRootNode", "has<PERSON>arentShadow", "ShadowRoot", "referenceNode", "elementsAtPoint", "elementsFromPoint", "newActiveElement", "find", "tagName", "fallbackActiveElement", "elementFromPoint", "exitInputMode", "assistiveFocusable", "setAttribute", "getOptionValueText", "setTimeout", "setValue", "value", "addEventListener", "destroyScrollListener", "removeEventListener", "parentEl", "findNextOption", "stride", "prevNode", "node", "nextElement<PERSON><PERSON>ling", "findPreviousOption", "nextNode", "previousElementSibling", "onKeyDown", "mobile", "newOption", "key", "querySelector", "preventDefault", "getAttribute", "innerText", "renderAssistiveFocusable", "valueText", "ref", "class", "role", "tabindex", "aria<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newValue", "valueChange", "scrollActiveItemIntoView", "componentWillLoad", "closest", "visibleCallback", "entries", "length", "isIntersecting", "oldActive", "IntersectionObserver", "threshold", "root", "observe", "componentDidRender", "firstOption", "_this", "_asyncToGenerator", "apply", "arguments", "_this2", "emit", "setFocus", "_this3", "focus", "connectedCallback", "options", "Array", "from", "querySelectorAll", "option", "render", "mode", "name", "tabIndex", "watchers", "style", "ion_picker_column"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}