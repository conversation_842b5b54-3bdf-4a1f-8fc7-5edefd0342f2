(self["webpackChunkdriver_evaluation_app"] = self["webpackChunkdriver_evaluation_app"] || []).push([["main"],{

/***/ 92:
/*!**********************************!*\
  !*** ./src/app/app.component.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppComponent: () => (/* binding */ AppComponent)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ionic/angular */ 1507);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 7580);




class AppComponent {
  constructor() {
    this.title = 'driver-evaluation-app';
  }
  static {
    this.ɵfac = function AppComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || AppComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: AppComponent,
      selectors: [["app-root"]],
      standalone: true,
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵStandaloneFeature"]],
      decls: 3,
      vars: 0,
      template: function AppComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "ion-app")(1, "ion-content");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](2, "router-outlet");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
        }
      },
      dependencies: [_angular_router__WEBPACK_IMPORTED_MODULE_1__.RouterOutlet, _ionic_angular__WEBPACK_IMPORTED_MODULE_2__.IonicModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_2__.IonApp, _ionic_angular__WEBPACK_IMPORTED_MODULE_2__.IonContent],
      encapsulation: 2
    });
  }
}

/***/ }),

/***/ 2181:
/*!*******************************!*\
  !*** ./src/app/app.routes.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   routes: () => (/* binding */ routes)
/* harmony export */ });
/* harmony import */ var _components_evaluation_evaluation_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./components/evaluation/evaluation.component */ 2380);

const routes = [{
  path: '',
  component: _components_evaluation_evaluation_component__WEBPACK_IMPORTED_MODULE_0__.EvaluationComponent
}, {
  path: '**',
  redirectTo: ''
}];

/***/ }),

/***/ 6113:
/*!*************************************************************************!*\
  !*** ./src/app/components/evaluation-card/evaluation-card.component.ts ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EvaluationCardComponent: () => (/* binding */ EvaluationCardComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ionic/angular */ 1507);
/* harmony import */ var _ngx_translate_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ngx-translate/core */ 852);








function EvaluationCardComponent_div_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](1, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](2, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function EvaluationCardComponent_div_7_Template_div_click_0_listener() {
      const option_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵrestoreView"](_r1).$implicit;
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵresetView"](ctx_r2.setRating(option_r2.value));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](3, "div", 5)(4, "span", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](6, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](7, "div", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](8, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const option_r2 = ctx.$implicit;
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵclassProp"]("selected", ctx_r2.currentRating === option_r2.value);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵattribute"]("aria-label", _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind1"](1, 6, option_r2.label) + ": " + _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind1"](2, 8, option_r2.description));
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind1"](6, 10, option_r2.label));
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵclassProp"]("selected", ctx_r2.currentRating === option_r2.value);
  }
}
class EvaluationCardComponent {
  constructor() {
    this.currentRating = 0;
    this.ratingChange = new _angular_core__WEBPACK_IMPORTED_MODULE_0__.EventEmitter();
    // Rating options with 4-point scale
    this.ratingOptions = [{
      value: 4,
      label: 'RATING.VERY_GOOD',
      description: 'RATING.VERY_GOOD_DESC'
    }, {
      value: 3,
      label: 'RATING.GOOD',
      description: 'RATING.GOOD_DESC'
    }, {
      value: 2,
      label: 'RATING.BAD',
      description: 'RATING.BAD_DESC'
    }, {
      value: 1,
      label: 'RATING.VERY_BAD',
      description: 'RATING.VERY_BAD_DESC'
    }];
  }
  setRating(score) {
    this.ratingChange.emit(score);
  }
  static {
    this.ɵfac = function EvaluationCardComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || EvaluationCardComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: EvaluationCardComponent,
      selectors: [["app-evaluation-card"]],
      inputs: {
        category: "category",
        currentRating: "currentRating"
      },
      outputs: {
        ratingChange: "ratingChange"
      },
      standalone: true,
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵStandaloneFeature"]],
      decls: 8,
      vars: 3,
      consts: [[1, "evaluation-card"], [3, "title"], [1, "rating-options"], ["class", "rating-option", 3, "selected", "click", 4, "ngFor", "ngForOf"], [1, "rating-option", 3, "click"], [1, "option-content"], [1, "option-label"], [1, "option-circle"], [1, "circle-inner"]],
      template: function EvaluationCardComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 0)(1, "ion-card")(2, "ion-card-header")(3, "ion-card-title", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](5, "ion-card-content")(6, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](7, EvaluationCardComponent_div_7_Template, 9, 12, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("title", ctx.category.description);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx.category.name);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx.ratingOptions);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_1__.CommonModule, _angular_common__WEBPACK_IMPORTED_MODULE_1__.NgForOf, _ionic_angular__WEBPACK_IMPORTED_MODULE_2__.IonicModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_2__.IonCard, _ionic_angular__WEBPACK_IMPORTED_MODULE_2__.IonCardContent, _ionic_angular__WEBPACK_IMPORTED_MODULE_2__.IonCardHeader, _ionic_angular__WEBPACK_IMPORTED_MODULE_2__.IonCardTitle, _ngx_translate_core__WEBPACK_IMPORTED_MODULE_3__.TranslateModule, _ngx_translate_core__WEBPACK_IMPORTED_MODULE_3__.TranslatePipe],
      styles: [".evaluation-card[_ngcontent-%COMP%] {\n  margin-bottom: 20px;\n  font-family: \"Cairo\", sans-serif;\n}\n.evaluation-card[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%] {\n  margin: 0;\n  border-radius: 12px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n}\n.evaluation-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%] {\n  padding-bottom: 10px;\n}\n.evaluation-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%] {\n  font-family: \"Cairo\", sans-serif;\n  font-weight: 500;\n  cursor: help;\n}\n.evaluation-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]:hover {\n  color: #EBC940;\n}\n.evaluation-card[_ngcontent-%COMP%]   .rating-options[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n  margin: 20px 0;\n}\n.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 56pt;\n  background-color: #ffffff;\n  border: 1px solid #DFDFDF;\n  border-radius: 16pt;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  position: relative;\n}\n.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]:hover {\n  background-color: #fafafa;\n}\n.evaluation-card[_ngcontent-%COMP%]   .rating-option.selected[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  border: 1px solid #EBC940;\n  box-shadow: 0 0 0 4px rgba(235, 201, 64, 0.1607843137);\n}\n.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-content[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 24px;\n  height: 100%;\n  font-family: \"Cairo\", sans-serif;\n  font-weight: 500;\n}\n.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-label[_ngcontent-%COMP%] {\n  font-size: 16px;\n  color: #333;\n}\n.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-circle[_ngcontent-%COMP%] {\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  border: 2px solid #ccc;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.2s ease;\n}\n.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-circle[_ngcontent-%COMP%]   .circle-inner[_ngcontent-%COMP%] {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  background-color: #ccc;\n  transition: all 0.2s ease;\n}\n.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-circle[_ngcontent-%COMP%]   .circle-inner.selected[_ngcontent-%COMP%] {\n  background-color: #EBC940;\n}\n.evaluation-card[_ngcontent-%COMP%]   .rating-option.selected[_ngcontent-%COMP%]   .option-circle[_ngcontent-%COMP%] {\n  border-color: #EBC940;\n  box-shadow: 0 0 0 2px rgba(235, 201, 64, 0.1607843137);\n}\n.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]:not(.selected)   .option-circle[_ngcontent-%COMP%] {\n  box-shadow: none;\n}\n\n@media (max-width: 768px) {\n  .evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%] {\n    height: 48pt;\n  }\n  .evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-content[_ngcontent-%COMP%] {\n    padding: 0 20px;\n  }\n  .evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-label[_ngcontent-%COMP%] {\n    font-size: 14px;\n  }\n}\n@media (max-width: 480px) {\n  .evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%] {\n    height: 44pt;\n  }\n  .evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-content[_ngcontent-%COMP%] {\n    padding: 0 16px;\n  }\n  .evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-label[_ngcontent-%COMP%] {\n    font-size: 13px;\n  }\n  .evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-circle[_ngcontent-%COMP%] {\n    width: 20px;\n    height: 20px;\n  }\n  .evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-circle[_ngcontent-%COMP%]   .circle-inner[_ngcontent-%COMP%] {\n    width: 10px;\n    height: 10px;\n  }\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 2380:
/*!***************************************************************!*\
  !*** ./src/app/components/evaluation/evaluation.component.ts ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EvaluationComponent: () => (/* binding */ EvaluationComponent)
/* harmony export */ });
/* harmony import */ var _Users_macbook_Desktop_olivery_web_odoo_12_extra_addons_olivery_driver_evaluation_angular_app_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 9204);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @ionic/angular */ 1507);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rxjs */ 819);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rxjs */ 3900);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rxjs */ 6196);
/* harmony import */ var _ngx_translate_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @ngx-translate/core */ 852);
/* harmony import */ var _evaluation_card_evaluation_card_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../evaluation-card/evaluation-card.component */ 6113);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _services_evaluation_api_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../services/evaluation-api.service */ 1877);
/* harmony import */ var _services_iframe_communication_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../services/iframe-communication.service */ 2519);
/* harmony import */ var _services_evaluation_data_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../services/evaluation-data.service */ 1856);















function EvaluationComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 9)(1, "ion-card")(2, "ion-card-content")(3, "h2");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](7, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](8, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](ctx_r0.evaluationTitle);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate2"]("", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind1"](7, 3, "EXPIRES"), ": ", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind2"](8, 5, ctx_r0.evaluationData.expiryDate, "medium"), "");
  }
}
function EvaluationComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "ion-spinner", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](4, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind1"](4, 1, "LOADING_EVALUATION_FORM"));
  }
}
function EvaluationComponent_div_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 12)(1, "h4");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](3, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind1"](3, 2, "ERROR"));
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](ctx_r0.error);
  }
}
function EvaluationComponent_div_11_app_evaluation_card_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "app-evaluation-card", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("ratingChange", function EvaluationComponent_div_11_app_evaluation_card_2_Template_app_evaluation_card_ratingChange_0_listener($event) {
      const category_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r3).$implicit;
      const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r0.setRating(category_r4.id, $event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const category_r4 = ctx.$implicit;
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("category", category_r4)("currentRating", ctx_r0.getRating(category_r4.id));
  }
}
function EvaluationComponent_div_11_ion_spinner_17_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](0, "ion-spinner", 11);
  }
}
function EvaluationComponent_div_11_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 13)(1, "div", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](2, EvaluationComponent_div_11_app_evaluation_card_2_Template, 1, 2, "app-evaluation-card", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "div", 16)(4, "ion-card")(5, "ion-card-header")(6, "ion-card-title", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](7, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](9, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](10, "ion-card-content")(11, "ion-item")(12, "ion-textarea", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](13, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayListener"]("ngModelChange", function EvaluationComponent_div_11_Template_ion_textarea_ngModelChange_12_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r2);
      const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayBindingSet"](ctx_r0.feedback, $event) || (ctx_r0.feedback = $event);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"]($event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](14, "div", 19)(15, "div", 20)(16, "ion-button", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function EvaluationComponent_div_11_Template_ion_button_click_16_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r2);
      const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r0.submitEvaluation());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](17, EvaluationComponent_div_11_ion_spinner_17_Template, 1, 0, "ion-spinner", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](18);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](19, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](20, "translate");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx_r0.config.criteria.categories);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("title", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind1"](7, 8, "FEEDBACK.FEEDBACK_DESCRIPTION"));
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind1"](9, 10, "FEEDBACK.ADDITIONAL_FEEDBACK"), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayProperty"]("ngModel", ctx_r0.feedback);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("placeholder", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind1"](13, 12, "FEEDBACK.FEEDBACK_PLACEHOLDER"));
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("disabled", ctx_r0.isSubmitting || !ctx_r0.hasValidRatings());
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r0.isSubmitting);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx_r0.isSubmitting ? _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind1"](19, 14, "BUTTONS.SUBMITTING") : _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind1"](20, 16, "BUTTONS.SUBMIT_EVALUATION"), " ");
  }
}
class EvaluationComponent {
  constructor(apiService, iframeService, dataService, translate) {
    this.apiService = apiService;
    this.iframeService = iframeService;
    this.dataService = dataService;
    this.translate = translate;
    this.destroy$ = new rxjs__WEBPACK_IMPORTED_MODULE_6__.Subject();
    // State management
    this.isLoading = true;
    this.error = null;
    // Data
    this.evaluationData = null;
    this.config = null;
    this.scores = {};
    this.feedback = '';
    this.evaluationTitle = 'Driver Evaluation';
    // UI state
    this.isSubmitting = false;
    this.currentLanguage = 'ar';
    this.isArabic = true;
    // Set Arabic as default language
    this.translate.setDefaultLang('ar');
    this.translate.use('ar');
  }
  ngOnInit() {
    this.iframeService.evaluationData$.pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_7__.takeUntil)(this.destroy$)).subscribe(data => {
      if (data) {
        this.evaluationData = data;
        this.initializeEvaluation();
      }
    });
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  initializeEvaluation() {
    var _this = this;
    return (0,_Users_macbook_Desktop_olivery_web_odoo_12_extra_addons_olivery_driver_evaluation_angular_app_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (!_this.evaluationData) return;
      try {
        _this.isLoading = true;
        _this.error = null;
        // Validate token
        const validation = yield (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.firstValueFrom)(_this.apiService.validateToken(_this.evaluationData.token));
        if (!validation?.success) {
          const errorMessage = validation?.error || _this.translate.instant('MESSAGES.INVALID_OR_EXPIRED_LINK');
          _this.error = errorMessage;
          _this.iframeService.notifyEvaluationError({
            error: errorMessage,
            isArabic: true
          });
          return;
        }
        // Update evaluation data with validated information
        if (validation.data) {
          _this.evaluationData = {
            ..._this.evaluationData,
            driverName: validation.data.driver_name,
            driverId: validation.data.driver_id,
            linkId: validation.data.link_id,
            expiryDate: validation.data.expiry_date
          };
        }
        // Get configuration - try API first, then fallback to static data
        try {
          console.log('🔄 Requesting config from API with token:', _this.evaluationData.token);
          const configResponse = yield (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.firstValueFrom)(_this.apiService.getConfig(_this.evaluationData.token));
          console.log('📡 API Response received:', configResponse);
          if (configResponse?.success && configResponse.data?.criteria?.categories && configResponse.data.criteria.categories.length > 0) {
            _this.config = configResponse.data;
            _this.evaluationTitle = _this.config.evaluation_title || 'Driver Evaluation';
            console.log('✅ Using dynamic configuration from API:');
            console.log('📋 Categories:', _this.config.criteria.categories);
            console.log('🔢 Number of categories:', _this.config.criteria.categories.length);
            console.log('📝 Evaluation Title:', _this.evaluationTitle);
          } else {
            // Fallback to static configuration only if API fails completely
            console.log('⚠️ API config failed or returned empty categories');
            console.log('📊 Response success:', configResponse?.success);
            console.log('📊 Response data:', configResponse?.data);
            console.log('📊 Categories:', configResponse?.data?.criteria?.categories);
            _this.config = yield (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.firstValueFrom)(_this.dataService.getEvaluationConfig());
            console.log('📁 Using static configuration:', _this.config.criteria.categories);
          }
        } catch (apiError) {
          console.log('❌ API config error, using static configuration...', apiError);
          _this.config = yield (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.firstValueFrom)(_this.dataService.getEvaluationConfig());
          console.log('📁 Using static configuration due to error:', _this.config.criteria.categories);
        }
        _this.isLoading = false;
      } catch (error) {
        console.error('Initialization error:', error);
        const errorMessage = _this.translate.instant('MESSAGES.FAILED_TO_LOAD');
        _this.error = errorMessage;
        _this.iframeService.notifyEvaluationError({
          error: errorMessage,
          isArabic: true
        });
      }
    })();
  }
  setRating(categoryId, score) {
    this.scores[categoryId] = score;
  }
  getRating(categoryId) {
    return this.scores[categoryId] || 0;
  }
  getRatingLabel(score) {
    switch (score) {
      case 4:
        return this.translate.instant('RATING.VERY_GOOD');
      case 3:
        return this.translate.instant('RATING.GOOD');
      case 2:
        return this.translate.instant('RATING.BAD');
      case 1:
        return this.translate.instant('RATING.VERY_BAD');
      default:
        return this.translate.instant('RATING.NOT_RATED');
    }
  }
  toggleLanguage() {
    this.currentLanguage = this.isArabic ? 'ar' : 'en';
    this.translate.use(this.currentLanguage);
    // Update document direction
    const container = document.querySelector('.evaluation-container');
    if (container) {
      container.setAttribute('dir', this.currentLanguage === 'ar' ? 'rtl' : 'ltr');
    }
  }
  submitEvaluation() {
    var _this2 = this;
    return (0,_Users_macbook_Desktop_olivery_web_odoo_12_extra_addons_olivery_driver_evaluation_angular_app_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (_this2.isSubmitting || !_this2.evaluationData) return;
      try {
        _this2.isSubmitting = true;
        const submission = {
          scores: _this2.scores,
          evaluator: {
            name: 'Anonymous',
            email: '<EMAIL>',
            phone: ''
          },
          feedback: _this2.feedback
        };
        const response = yield (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.firstValueFrom)(_this2.apiService.submitEvaluation(_this2.evaluationData.token, submission));
        if (response?.success) {
          // Send translated success message
          const translatedMessage = _this2.translate.instant('MESSAGES.EVALUATION_COMPLETED_SUCCESSFULLY');
          const translatedThankYou = _this2.translate.instant('MESSAGES.THANK_YOU_FEEDBACK');
          const translatedOverallScore = _this2.translate.instant('MESSAGES.OVERALL_SCORE');
          const translatedLinkInactive = _this2.translate.instant('MESSAGES.LINK_NOW_INACTIVE');
          _this2.iframeService.notifyEvaluationComplete({
            success: true,
            message: translatedMessage,
            thankYou: translatedThankYou,
            overallScore: response.data?.overall_score || 0,
            overallScoreLabel: translatedOverallScore,
            linkInactiveMessage: translatedLinkInactive,
            isArabic: true
          });
        } else {
          const errorMessage = response?.error || _this2.translate.instant('MESSAGES.SUBMISSION_ERROR');
          _this2.error = errorMessage;
          _this2.iframeService.notifyEvaluationError(errorMessage);
        }
      } catch (error) {
        console.error('Submission error:', error);
        const errorMessage = _this2.translate.instant('MESSAGES.SUBMISSION_ERROR');
        _this2.error = errorMessage;
        _this2.iframeService.notifyEvaluationError({
          error: errorMessage,
          isArabic: true
        });
      } finally {
        _this2.isSubmitting = false;
      }
    })();
  }
  getStarArray(maxScore) {
    return Array.from({
      length: maxScore
    }, (_, i) => i + 1);
  }
  getCategoryName(categoryId) {
    if (!this.config) return categoryId;
    const category = this.config.criteria.categories.find(c => c.id === categoryId);
    // For dynamic questions, use the actual name from config
    if (category && category.name) {
      return category.name;
    }
    // Fallback to translation for legacy categories
    return this.translate.instant('CATEGORIES.' + categoryId.toUpperCase()) || categoryId;
  }
  hasValidRatings() {
    if (!this.config) return false;
    // Check if all categories have been rated
    for (const category of this.config.criteria.categories) {
      if (!this.scores[category.id] || this.scores[category.id] === 0) {
        return false;
      }
    }
    return true;
  }
  static {
    this.ɵfac = function EvaluationComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || EvaluationComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_evaluation_api_service__WEBPACK_IMPORTED_MODULE_2__.EvaluationApiService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_iframe_communication_service__WEBPACK_IMPORTED_MODULE_3__.IframeCommunicationService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_evaluation_data_service__WEBPACK_IMPORTED_MODULE_4__.EvaluationDataService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_ngx_translate_core__WEBPACK_IMPORTED_MODULE_9__.TranslateService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
      type: EvaluationComponent,
      selectors: [["app-evaluation"]],
      standalone: true,
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵStandaloneFeature"]],
      decls: 12,
      vars: 9,
      consts: [["dir", "rtl", 1, "evaluation-container"], [1, "language-switch"], [1, "lang-toggle"], [1, "lang-label"], [1, "lang-toggle-switch", 3, "ngModelChange", "ionChange", "ngModel"], ["class", "evaluation-header", 4, "ngIf"], ["class", "loading-container", 4, "ngIf"], ["class", "alert-danger", 4, "ngIf"], ["class", "evaluation-form", 4, "ngIf"], [1, "evaluation-header"], [1, "loading-container"], ["name", "crescent"], [1, "alert-danger"], [1, "evaluation-form"], [1, "categories-section"], [3, "category", "currentRating", "ratingChange", 4, "ngFor", "ngForOf"], [1, "feedback-section"], [3, "title"], ["rows", "4", 3, "ngModelChange", "ngModel", "placeholder"], [1, "submit-section"], [1, "submit-actions"], ["expand", "block", "size", "large", 1, "submit-button", 3, "click", "disabled"], ["name", "crescent", 4, "ngIf"], [3, "ratingChange", "category", "currentRating"]],
      template: function EvaluationComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "span", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](4, "AR");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "ion-toggle", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayListener"]("ngModelChange", function EvaluationComponent_Template_ion_toggle_ngModelChange_5_listener($event) {
            _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayBindingSet"](ctx.isArabic, $event) || (ctx.isArabic = $event);
            return $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("ionChange", function EvaluationComponent_Template_ion_toggle_ionChange_5_listener() {
            return ctx.toggleLanguage();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](6, "span", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](7, "EN");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](8, EvaluationComponent_div_8_Template, 9, 8, "div", 5)(9, EvaluationComponent_div_9_Template, 5, 3, "div", 6)(10, EvaluationComponent_div_10_Template, 6, 4, "div", 7)(11, EvaluationComponent_div_11_Template, 21, 18, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵclassProp"]("active", ctx.currentLanguage === "ar");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayProperty"]("ngModel", ctx.isArabic);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵclassProp"]("active", ctx.currentLanguage === "en");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.evaluationData);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.error);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", !ctx.isLoading && !ctx.error && ctx.config);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_10__.CommonModule, _angular_common__WEBPACK_IMPORTED_MODULE_10__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_10__.NgIf, _angular_common__WEBPACK_IMPORTED_MODULE_10__.DatePipe, _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_11__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_11__.NgModel, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonicModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonButton, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonCard, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonCardContent, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonCardHeader, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonCardTitle, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonItem, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonSpinner, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonTextarea, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonToggle, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.BooleanValueAccessor, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.TextValueAccessor, _ngx_translate_core__WEBPACK_IMPORTED_MODULE_9__.TranslateModule, _ngx_translate_core__WEBPACK_IMPORTED_MODULE_9__.TranslatePipe, _evaluation_card_evaluation_card_component__WEBPACK_IMPORTED_MODULE_1__.EvaluationCardComponent],
      styles: [".evaluation-container[_ngcontent-%COMP%] {\n  max-width: 900px;\n  margin: 0 auto;\n  padding: 20px;\n  font-family: \"Cairo\", sans-serif;\n  font-weight: 500;\n  direction: rtl;\n  text-align: right;\n  min-height: 100vh;\n  box-sizing: border-box;\n}\n.evaluation-container[dir=rtl][_ngcontent-%COMP%]   *[_ngcontent-%COMP%] {\n  text-align: right;\n}\n.evaluation-container[dir=ltr][_ngcontent-%COMP%]   *[_ngcontent-%COMP%] {\n  text-align: left;\n}\n\n.language-switch[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  margin-bottom: 20px;\n}\n.language-switch[_ngcontent-%COMP%]   .lang-toggle[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-family: \"Cairo\", sans-serif;\n  font-weight: 500;\n}\n.language-switch[_ngcontent-%COMP%]   .lang-toggle[_ngcontent-%COMP%]   .lang-label[_ngcontent-%COMP%] {\n  font-size: 14px;\n  color: #666;\n  transition: color 0.2s ease;\n}\n.language-switch[_ngcontent-%COMP%]   .lang-toggle[_ngcontent-%COMP%]   .lang-label.active[_ngcontent-%COMP%] {\n  color: #EBC940;\n  font-weight: 600;\n}\n.language-switch[_ngcontent-%COMP%]   .lang-toggle[_ngcontent-%COMP%]   .lang-toggle-switch[_ngcontent-%COMP%] {\n  --color: #EBC940;\n  --color-checked: #EBC940;\n  --handle-background: #fff;\n  --handle-background-checked: #fff;\n  --background: #ddd;\n  --background-checked: #EBC940;\n}\n\n.evaluation-header[_ngcontent-%COMP%] {\n  margin-bottom: 30px;\n}\n.evaluation-header[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%] {\n  margin: 0;\n  border-radius: 12px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n}\n.evaluation-header[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%] {\n  text-align: center;\n  font-family: \"Cairo\", sans-serif;\n}\n.evaluation-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  font-size: 1.8rem;\n  margin-bottom: 15px;\n  font-weight: 600;\n  color: #333;\n  font-family: \"Cairo\", sans-serif;\n}\n.evaluation-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  margin-bottom: 8px;\n  color: #666;\n  font-family: \"Cairo\", sans-serif;\n  font-weight: 500;\n}\n.evaluation-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #EBC940;\n}\n\n.evaluation-form[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_fadeIn 0.5s ease-in;\n}\n\n@keyframes _ngcontent-%COMP%_fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n.categories-section[_ngcontent-%COMP%] {\n  margin-bottom: 30px;\n}\n\n.feedback-section[_ngcontent-%COMP%] {\n  margin-bottom: 30px;\n}\n.feedback-section[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%] {\n  margin: 0;\n  border-radius: 12px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n}\n.feedback-section[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%] {\n  padding-bottom: 10px;\n}\n.feedback-section[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%] {\n  font-family: \"Cairo\", sans-serif;\n  font-weight: 500;\n  cursor: help;\n}\n.feedback-section[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]:hover {\n  color: #EBC940;\n}\n.feedback-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%] {\n  --background: transparent;\n  --border-color: transparent;\n  font-family: \"Cairo\", sans-serif;\n  --inner-border-width: 0;\n}\n.feedback-section[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%] {\n  font-family: \"Cairo\", sans-serif;\n  font-weight: 500;\n}\n.feedback-section[_ngcontent-%COMP%]   ion-textarea[_ngcontent-%COMP%] {\n  font-family: \"Cairo\", sans-serif;\n  --color: #333;\n  border: 1px #DFDFDF solid;\n  border-radius: 15px;\n  --padding-start: 12px;\n  --padding-end: 12px;\n  --padding-top: 12px;\n  --padding-bottom: 12px;\n  margin-top: 8px;\n}\n\n.submit-section[_ngcontent-%COMP%] {\n  margin-bottom: 20px;\n}\n\n.submit-actions[_ngcontent-%COMP%] {\n  margin-top: 20px;\n  padding: 20px 0 60px 0;\n  margin-bottom: env(safe-area-inset-bottom, 20px);\n}\n.submit-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%] {\n  --background: #EBC940;\n  --background-activated: #d4b235;\n  --background-hover: #d4b235;\n  --color: #333;\n  --border-radius: 16px;\n  font-family: \"Cairo\", sans-serif;\n  font-weight: 600;\n  height: 56px;\n  font-size: 16px;\n}\n.submit-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]:disabled {\n  --background: #ccc;\n  --color: #666;\n}\n\n.detailed-questions[_ngcontent-%COMP%] {\n  margin-top: 30px;\n  padding-top: 20px;\n  border-top: 1px solid #e0e0e0;\n}\n.detailed-questions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  color: #333;\n  font-weight: 600;\n  margin-bottom: 20px;\n}\n\n.question-item[_ngcontent-%COMP%] {\n  margin-bottom: 25px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 8px;\n}\n.question-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0 0 15px 0;\n  color: #333;\n  font-weight: 500;\n  font-size: 1.1rem;\n}\n\n.step-actions[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  gap: 15px;\n  margin-top: 30px;\n  padding: 20px 0;\n}\n.step-actions[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\n  flex: 1;\n  height: 50px;\n  font-weight: 600;\n}\n\n.loading-container[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 60px 20px;\n}\n.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%] {\n  width: 50px;\n  height: 50px;\n  margin-bottom: 20px;\n}\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 1.1rem;\n}\n\n.alert-success[_ngcontent-%COMP%] {\n  background-color: #d4edda;\n  color: #155724;\n  border: 1px solid #c3e6cb;\n  padding: 15px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n}\n\n.alert-danger[_ngcontent-%COMP%] {\n  background-color: #f8d7da;\n  color: #721c24;\n  border: 1px solid #f5c6cb;\n  padding: 15px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n}\n.alert-danger[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0 0 10px 0;\n  font-weight: 600;\n}\n.alert-danger[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0;\n}\n\n.review-summary[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  color: #333;\n  font-weight: 600;\n  margin: 20px 0 15px 0;\n}\n\n.ratings-summary[_ngcontent-%COMP%] {\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n}\n\n.rating-item[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 0;\n  border-bottom: 1px solid #e9ecef;\n}\n.rating-item[_ngcontent-%COMP%]:last-child {\n  border-bottom: none;\n}\n.rating-item[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%] {\n  font-weight: 500;\n  color: #333;\n}\n.rating-item[_ngcontent-%COMP%]   .score-value[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #EBC940;\n  font-size: 1.1rem;\n}\n\n.feedback-summary[_ngcontent-%COMP%] {\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 8px;\n  margin-top: 15px;\n}\n.feedback-summary[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 10px 0 0 0;\n  color: #555;\n  line-height: 1.5;\n}\n\n.rating-display[_ngcontent-%COMP%] {\n  margin-top: 10px;\n  font-weight: 500;\n  color: #EBC940;\n}\n\n@media (max-width: 768px) {\n  .evaluation-container[_ngcontent-%COMP%] {\n    padding: 10px;\n    padding-bottom: 80px;\n  }\n  .evaluation-header[_ngcontent-%COMP%] {\n    padding: 15px;\n    margin-bottom: 20px;\n  }\n  .evaluation-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n    font-size: 2rem;\n  }\n  .step-actions[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .step-actions[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\n    margin-bottom: 10px;\n  }\n  .submit-actions[_ngcontent-%COMP%] {\n    padding-bottom: 100px;\n    margin-bottom: max(env(safe-area-inset-bottom), 40px);\n  }\n  .submit-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%] {\n    position: relative;\n    z-index: 10;\n  }\n}\n@media (max-width: 480px) {\n  .evaluation-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n    font-size: 1.5rem;\n  }\n  .question-item[_ngcontent-%COMP%] {\n    padding: 10px;\n  }\n  .submit-actions[_ngcontent-%COMP%] {\n    padding-bottom: 120px;\n    margin-bottom: max(env(safe-area-inset-bottom), 60px);\n  }\n}\nion-card[_ngcontent-%COMP%] {\n  margin: 0 0 20px 0;\n  border-radius: 12px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  font-family: \"Cairo\", sans-serif;\n}\n\nion-card-header[_ngcontent-%COMP%] {\n  padding-bottom: 10px;\n}\n\nion-card-title[_ngcontent-%COMP%] {\n  font-size: 1.3rem;\n  font-weight: 600;\n  color: #333;\n  font-family: \"Cairo\", sans-serif;\n}\n\nion-card-subtitle[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.95rem;\n  font-family: \"Cairo\", sans-serif;\n}\n\nion-item[_ngcontent-%COMP%] {\n  --border-color: #e0e0e0;\n  --background: transparent;\n  margin-bottom: 15px;\n  font-family: \"Cairo\", sans-serif;\n}\n\nion-label[_ngcontent-%COMP%] {\n  font-weight: 500;\n  font-family: \"Cairo\", sans-serif;\n  color: #333;\n}\n\nion-input[_ngcontent-%COMP%], ion-textarea[_ngcontent-%COMP%] {\n  --color: #333;\n  --placeholder-color: #999;\n}\n\nion-button[_ngcontent-%COMP%] {\n  --border-radius: 8px;\n  font-weight: 600;\n  text-transform: none;\n}\n\nion-button[fill=outline][_ngcontent-%COMP%] {\n  --color: #EBC940;\n  --border-color: #EBC940;\n}\n\nion-button[color=success][_ngcontent-%COMP%] {\n  --background: #28a745;\n  --background-activated: #218838;\n}\n\nion-list[_ngcontent-%COMP%] {\n  background: transparent;\n  padding: 0;\n}\n\nion-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%] {\n  --background: #f8f9fa;\n  --border-color: #e9ecef;\n  border-radius: 6px;\n  margin-bottom: 8px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 1877:
/*!****************************************************!*\
  !*** ./src/app/services/evaluation-api.service.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EvaluationApiService: () => (/* binding */ EvaluationApiService)
/* harmony export */ });
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/common/http */ 6443);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rxjs/operators */ 271);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);




class EvaluationApiService {
  constructor(http) {
    this.http = http;
    this.baseUrl = '/api/evaluation';
    this.httpOptions = {
      headers: new _angular_common_http__WEBPACK_IMPORTED_MODULE_0__.HttpHeaders({
        'Content-Type': 'application/json'
      })
    };
  }
  validateToken(token) {
    return this.http.post(`${this.baseUrl}/validate`, {
      'params': {
        'token': token
      }
    }, this.httpOptions).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_1__.map)(response => {
      if (response.error) {
        return {
          success: false,
          error: response.error.message,
          code: response.error.code?.toString()
        };
      }
      return response.result || {
        success: false,
        error: 'No result in response'
      };
    }));
  }
  getConfig(token) {
    return this.http.post(`${this.baseUrl}/config`, {
      'params': {
        'token': token
      }
    }, this.httpOptions).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_1__.map)(response => {
      if (response.error) {
        return {
          success: false,
          error: response.error.message,
          code: response.error.code?.toString()
        };
      }
      return response.result || {
        success: false,
        error: 'No result in response'
      };
    }));
  }
  submitEvaluation(token, evaluationData) {
    return this.http.post(`${this.baseUrl}/submit`, {
      params: {
        'token': token,
        evaluation_data: evaluationData
      }
    }, this.httpOptions).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_1__.map)(response => {
      if (response.error) {
        return {
          success: false,
          error: response.error.message,
          code: response.error.code?.toString()
        };
      }
      return response.result || {
        success: false,
        error: 'No result in response'
      };
    }));
  }
  static {
    this.ɵfac = function EvaluationApiService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || EvaluationApiService)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_0__.HttpClient));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjectable"]({
      token: EvaluationApiService,
      factory: EvaluationApiService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 1856:
/*!*****************************************************!*\
  !*** ./src/app/services/evaluation-data.service.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EvaluationDataService: () => (/* binding */ EvaluationDataService)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rxjs */ 9452);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rxjs/operators */ 1318);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common/http */ 6443);




class EvaluationDataService {
  constructor(http) {
    this.http = http;
  }
  /**
   * Load evaluation configuration from static JSON file
   * This serves as a fallback when API is not available
   */
  loadStaticConfig() {
    return this.http.get('./assets/evaluation-config.json').pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_0__.catchError)(error => {
      console.error('Failed to load static evaluation config:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(this.getDefaultConfig());
    }));
  }
  /**
   * Load evaluation data from XML file (if needed for parsing)
   */
  loadEvaluationDataXml() {
    return this.http.get('./assets/evaluation_data.xml', {
      responseType: 'text'
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_0__.catchError)(error => {
      console.error('Failed to load evaluation data XML:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)('');
    }));
  }
  /**
   * Parse XML evaluation data and convert to JSON format
   */
  parseEvaluationXml(xmlContent) {
    try {
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(xmlContent, 'text/xml');
      // Check for parsing errors
      const parserError = xmlDoc.querySelector('parsererror');
      if (parserError) {
        console.error('XML parsing error:', parserError.textContent);
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(null);
      }
      // Extract evaluation criteria from XML
      const criteriaField = xmlDoc.querySelector('field[name="evaluation_criteria"]');
      if (!criteriaField) {
        console.error('No evaluation criteria found in XML');
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(null);
      }
      const criteriaJson = criteriaField.textContent?.trim();
      if (!criteriaJson) {
        console.error('Empty evaluation criteria in XML');
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(null);
      }
      // Parse the JSON content
      const criteria = JSON.parse(criteriaJson);
      // Extract other fields
      const durationField = xmlDoc.querySelector('field[name="evaluation_duration"]');
      const config = {
        evaluation_duration: durationField ? parseInt(durationField.textContent || '7') : 7,
        criteria: criteria
      };
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(config);
    } catch (error) {
      console.error('Error parsing evaluation XML:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(null);
    }
  }
  /**
   * Get evaluation configuration with fallback strategy:
   * 1. Try to load from static JSON
   * 2. If that fails, use default config
   */
  getEvaluationConfig() {
    return this.loadStaticConfig().pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_0__.catchError)(() => {
      console.log('Static JSON config failed, using default config...');
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(this.getDefaultConfig());
    }));
  }
  /**
   * Minimal default configuration as emergency fallback only
   * This should only be used if both API and static file fail
   */
  getDefaultConfig() {
    return {
      evaluation_duration: 7,
      max_attempts: 1,
      criteria: {
        categories: [{
          id: 'question_1',
          name: 'General Evaluation',
          description: 'Please rate the overall performance',
          max_score: 10,
          questions: [{
            id: 'general_rating',
            text: 'How would you rate the overall performance?',
            type: 'rating',
            scale: 10
          }]
        }]
      }
    };
  }
  static {
    this.ɵfac = function EvaluationDataService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || EvaluationDataService)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_3__.HttpClient));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjectable"]({
      token: EvaluationDataService,
      factory: EvaluationDataService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 2519:
/*!**********************************************************!*\
  !*** ./src/app/services/iframe-communication.service.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   IframeCommunicationService: () => (/* binding */ IframeCommunicationService)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rxjs */ 5797);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);


class IframeCommunicationService {
  constructor() {
    this.evaluationDataSubject = new rxjs__WEBPACK_IMPORTED_MODULE_0__.BehaviorSubject(null);
    this.evaluationData$ = this.evaluationDataSubject.asObservable();
    this.initializeMessageListener();
    this.requestEvaluationData();
  }
  initializeMessageListener() {
    window.addEventListener('message', event => {
      // Verify origin for security (adjust as needed)
      if (event.origin !== window.location.origin && !event.origin.includes('localhost')) {
        return;
      }
      if (event.data && event.data.type === 'EVALUATION_DATA') {
        this.evaluationDataSubject.next(event.data.payload);
      }
    });
  }
  requestEvaluationData() {
    // Try to get data from URL parameters first
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    const driverName = urlParams.get('driverName');
    const driverId = urlParams.get('driverId');
    const linkId = urlParams.get('linkId');
    const expiryDate = urlParams.get('expiryDate');
    if (token && driverName && driverId && linkId && expiryDate) {
      const evaluationData = {
        token,
        driverName,
        driverId: parseInt(driverId),
        linkId: parseInt(linkId),
        expiryDate
      };
      this.evaluationDataSubject.next(evaluationData);
    } else {
      // Request data from parent window
      if (window.parent !== window) {
        window.parent.postMessage({
          type: 'REQUEST_EVALUATION_DATA'
        }, '*');
      }
    }
  }
  sendMessage(type, payload) {
    if (window.parent !== window) {
      window.parent.postMessage({
        type,
        payload
      }, '*');
    }
  }
  notifyEvaluationComplete(result) {
    this.sendMessage('EVALUATION_COMPLETE', result);
  }
  notifyEvaluationError(error) {
    const errorPayload = typeof error === 'string' ? {
      error
    } : error;
    this.sendMessage('EVALUATION_ERROR', errorPayload);
  }
  static {
    this.ɵfac = function IframeCommunicationService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || IframeCommunicationService)();
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
      token: IframeCommunicationService,
      factory: IframeCommunicationService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 4429:
/*!*********************!*\
  !*** ./src/main.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HttpLoaderFactory: () => (/* binding */ HttpLoaderFactory)
/* harmony export */ });
/* harmony import */ var _angular_platform_browser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/platform-browser */ 436);
/* harmony import */ var _app_app_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app/app.component */ 92);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common/http */ 6443);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @ionic/angular */ 1507);
/* harmony import */ var _ngx_translate_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ngx-translate/core */ 852);
/* harmony import */ var _ngx_translate_http_loader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ngx-translate/http-loader */ 8952);
/* harmony import */ var _app_app_routes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./app/app.routes */ 2181);









// Translation loader factory
function HttpLoaderFactory(http) {
  return new _ngx_translate_http_loader__WEBPACK_IMPORTED_MODULE_2__.TranslateHttpLoader(http, './assets/i18n/', '.json');
}
(0,_angular_platform_browser__WEBPACK_IMPORTED_MODULE_3__.bootstrapApplication)(_app_app_component__WEBPACK_IMPORTED_MODULE_0__.AppComponent, {
  providers: [(0,_angular_router__WEBPACK_IMPORTED_MODULE_4__.provideRouter)(_app_app_routes__WEBPACK_IMPORTED_MODULE_1__.routes), (0,_angular_common_http__WEBPACK_IMPORTED_MODULE_5__.provideHttpClient)(), (0,_angular_core__WEBPACK_IMPORTED_MODULE_6__.importProvidersFrom)(_ionic_angular__WEBPACK_IMPORTED_MODULE_7__.IonicModule.forRoot(), _ngx_translate_core__WEBPACK_IMPORTED_MODULE_8__.TranslateModule.forRoot({
    loader: {
      provide: _ngx_translate_core__WEBPACK_IMPORTED_MODULE_8__.TranslateLoader,
      useFactory: HttpLoaderFactory,
      deps: [_angular_common_http__WEBPACK_IMPORTED_MODULE_5__.HttpClient]
    },
    defaultLanguage: 'ar'
  }))]
}).catch(err => console.error(err));

/***/ }),

/***/ 8996:
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/@ionic/core/dist/esm/ lazy ^\.\/.*\.entry\.js$ include: \.entry\.js$ exclude: \.system\.entry\.js$ namespace object ***!
  \******************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./ion-accordion_2.entry.js": [
		7518,
		"common",
		"node_modules_ionic_core_dist_esm_ion-accordion_2_entry_js"
	],
	"./ion-action-sheet.entry.js": [
		1981,
		"common",
		"node_modules_ionic_core_dist_esm_ion-action-sheet_entry_js"
	],
	"./ion-alert.entry.js": [
		1603,
		"common",
		"node_modules_ionic_core_dist_esm_ion-alert_entry_js"
	],
	"./ion-app_8.entry.js": [
		2273,
		"common",
		"node_modules_ionic_core_dist_esm_ion-app_8_entry_js"
	],
	"./ion-avatar_3.entry.js": [
		9642,
		"node_modules_ionic_core_dist_esm_ion-avatar_3_entry_js"
	],
	"./ion-back-button.entry.js": [
		2095,
		"common",
		"node_modules_ionic_core_dist_esm_ion-back-button_entry_js"
	],
	"./ion-backdrop.entry.js": [
		2335,
		"node_modules_ionic_core_dist_esm_ion-backdrop_entry_js"
	],
	"./ion-breadcrumb_2.entry.js": [
		8221,
		"common",
		"node_modules_ionic_core_dist_esm_ion-breadcrumb_2_entry_js"
	],
	"./ion-button_2.entry.js": [
		7184,
		"node_modules_ionic_core_dist_esm_ion-button_2_entry_js"
	],
	"./ion-card_5.entry.js": [
		8759,
		"node_modules_ionic_core_dist_esm_ion-card_5_entry_js"
	],
	"./ion-checkbox.entry.js": [
		4248,
		"node_modules_ionic_core_dist_esm_ion-checkbox_entry_js"
	],
	"./ion-chip.entry.js": [
		9863,
		"node_modules_ionic_core_dist_esm_ion-chip_entry_js"
	],
	"./ion-col_3.entry.js": [
		1769,
		"node_modules_ionic_core_dist_esm_ion-col_3_entry_js"
	],
	"./ion-datetime-button.entry.js": [
		2569,
		"default-node_modules_ionic_core_dist_esm_data-GIsHsYIB_js",
		"node_modules_ionic_core_dist_esm_ion-datetime-button_entry_js"
	],
	"./ion-datetime_3.entry.js": [
		6534,
		"default-node_modules_ionic_core_dist_esm_data-GIsHsYIB_js",
		"common",
		"node_modules_ionic_core_dist_esm_ion-datetime_3_entry_js"
	],
	"./ion-fab_3.entry.js": [
		5458,
		"common",
		"node_modules_ionic_core_dist_esm_ion-fab_3_entry_js"
	],
	"./ion-img.entry.js": [
		654,
		"node_modules_ionic_core_dist_esm_ion-img_entry_js"
	],
	"./ion-infinite-scroll_2.entry.js": [
		6034,
		"common",
		"node_modules_ionic_core_dist_esm_ion-infinite-scroll_2_entry_js"
	],
	"./ion-input-otp.entry.js": [
		381,
		"common",
		"node_modules_ionic_core_dist_esm_ion-input-otp_entry_js"
	],
	"./ion-input-password-toggle.entry.js": [
		5196,
		"common",
		"node_modules_ionic_core_dist_esm_ion-input-password-toggle_entry_js"
	],
	"./ion-input.entry.js": [
		761,
		"default-node_modules_ionic_core_dist_esm_input_utils-zWijNCrx_js-node_modules_ionic_core_dist-2e0994",
		"common",
		"node_modules_ionic_core_dist_esm_ion-input_entry_js"
	],
	"./ion-item-option_3.entry.js": [
		6492,
		"common",
		"node_modules_ionic_core_dist_esm_ion-item-option_3_entry_js"
	],
	"./ion-item_8.entry.js": [
		9557,
		"common",
		"node_modules_ionic_core_dist_esm_ion-item_8_entry_js"
	],
	"./ion-loading.entry.js": [
		8353,
		"common",
		"node_modules_ionic_core_dist_esm_ion-loading_entry_js"
	],
	"./ion-menu_3.entry.js": [
		1024,
		"common",
		"node_modules_ionic_core_dist_esm_ion-menu_3_entry_js"
	],
	"./ion-modal.entry.js": [
		9160,
		"common",
		"node_modules_ionic_core_dist_esm_ion-modal_entry_js"
	],
	"./ion-nav_2.entry.js": [
		393,
		"node_modules_ionic_core_dist_esm_ion-nav_2_entry_js"
	],
	"./ion-picker-column-option.entry.js": [
		8442,
		"node_modules_ionic_core_dist_esm_ion-picker-column-option_entry_js"
	],
	"./ion-picker-column.entry.js": [
		3110,
		"common",
		"node_modules_ionic_core_dist_esm_ion-picker-column_entry_js"
	],
	"./ion-picker.entry.js": [
		5575,
		"node_modules_ionic_core_dist_esm_ion-picker_entry_js"
	],
	"./ion-popover.entry.js": [
		6772,
		"common",
		"node_modules_ionic_core_dist_esm_ion-popover_entry_js"
	],
	"./ion-progress-bar.entry.js": [
		4810,
		"node_modules_ionic_core_dist_esm_ion-progress-bar_entry_js"
	],
	"./ion-radio_2.entry.js": [
		4639,
		"common",
		"node_modules_ionic_core_dist_esm_ion-radio_2_entry_js"
	],
	"./ion-range.entry.js": [
		628,
		"common",
		"node_modules_ionic_core_dist_esm_ion-range_entry_js"
	],
	"./ion-refresher_2.entry.js": [
		8471,
		"common",
		"node_modules_ionic_core_dist_esm_ion-refresher_2_entry_js"
	],
	"./ion-reorder_2.entry.js": [
		1479,
		"common",
		"node_modules_ionic_core_dist_esm_ion-reorder_2_entry_js"
	],
	"./ion-ripple-effect.entry.js": [
		4065,
		"node_modules_ionic_core_dist_esm_ion-ripple-effect_entry_js"
	],
	"./ion-route_4.entry.js": [
		7971,
		"node_modules_ionic_core_dist_esm_ion-route_4_entry_js"
	],
	"./ion-searchbar.entry.js": [
		3184,
		"common",
		"node_modules_ionic_core_dist_esm_ion-searchbar_entry_js"
	],
	"./ion-segment-content.entry.js": [
		4312,
		"node_modules_ionic_core_dist_esm_ion-segment-content_entry_js"
	],
	"./ion-segment-view.entry.js": [
		4540,
		"node_modules_ionic_core_dist_esm_ion-segment-view_entry_js"
	],
	"./ion-segment_2.entry.js": [
		469,
		"common",
		"node_modules_ionic_core_dist_esm_ion-segment_2_entry_js"
	],
	"./ion-select-modal.entry.js": [
		7101,
		"node_modules_ionic_core_dist_esm_ion-select-modal_entry_js"
	],
	"./ion-select_3.entry.js": [
		3709,
		"common",
		"node_modules_ionic_core_dist_esm_ion-select_3_entry_js"
	],
	"./ion-spinner.entry.js": [
		388,
		"common",
		"node_modules_ionic_core_dist_esm_ion-spinner_entry_js"
	],
	"./ion-split-pane.entry.js": [
		2392,
		"node_modules_ionic_core_dist_esm_ion-split-pane_entry_js"
	],
	"./ion-tab-bar_2.entry.js": [
		6059,
		"common",
		"node_modules_ionic_core_dist_esm_ion-tab-bar_2_entry_js"
	],
	"./ion-tab_2.entry.js": [
		5427,
		"node_modules_ionic_core_dist_esm_ion-tab_2_entry_js"
	],
	"./ion-text.entry.js": [
		198,
		"node_modules_ionic_core_dist_esm_ion-text_entry_js"
	],
	"./ion-textarea.entry.js": [
		1735,
		"default-node_modules_ionic_core_dist_esm_input_utils-zWijNCrx_js-node_modules_ionic_core_dist-2e0994",
		"node_modules_ionic_core_dist_esm_ion-textarea_entry_js"
	],
	"./ion-toast.entry.js": [
		7510,
		"common",
		"node_modules_ionic_core_dist_esm_ion-toast_entry_js"
	],
	"./ion-toggle.entry.js": [
		5297,
		"common",
		"node_modules_ionic_core_dist_esm_ion-toggle_entry_js"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return Promise.all(ids.slice(1).map(__webpack_require__.e)).then(() => {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = 8996;
module.exports = webpackAsyncContext;

/***/ }),

/***/ 4140:
/*!************************************************************************************************************************************************************!*\
  !*** ./node_modules/@stencil/core/internal/client/ lazy ^\.\/.*\.entry\.js.*$ include: \.entry\.js$ exclude: \.system\.entry\.js$ strict namespace object ***!
  \************************************************************************************************************************************************************/
/***/ ((module) => {

function webpackEmptyAsyncContext(req) {
	// Here Promise.resolve().then() is used instead of new Promise() to prevent
	// uncaught exception popping up in devtools
	return Promise.resolve().then(() => {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	});
}
webpackEmptyAsyncContext.keys = () => ([]);
webpackEmptyAsyncContext.resolve = webpackEmptyAsyncContext;
webpackEmptyAsyncContext.id = 4140;
module.exports = webpackEmptyAsyncContext;

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendor"], () => (__webpack_exec__(4429)));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=main.js.map