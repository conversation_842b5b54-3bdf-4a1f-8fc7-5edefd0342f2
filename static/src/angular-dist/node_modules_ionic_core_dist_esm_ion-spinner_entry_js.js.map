{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-spinner_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACwG;AAC1C;AACA;AAE9D,MAAMa,UAAU,GAAG,svIAAsvI;AAEzwI,MAAMC,OAAO,GAAG,MAAM;EAClBC,WAAWA,CAACC,OAAO,EAAE;IACjBf,qDAAgB,CAAC,IAAI,EAAEe,OAAO,CAAC;IAC/B;AACR;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,KAAK;EACvB;EACAC,OAAOA,CAAA,EAAG;IACN,MAAMC,WAAW,GAAG,IAAI,CAACC,IAAI,IAAIjB,iDAAM,CAACkB,GAAG,CAAC,SAAS,CAAC;IACtD,MAAMC,IAAI,GAAGjB,qDAAU,CAAC,IAAI,CAAC;IAC7B,IAAIc,WAAW,EAAE;MACb,OAAOA,WAAW;IACtB;IACA,OAAOG,IAAI,KAAK,KAAK,GAAG,OAAO,GAAG,UAAU;EAChD;EACAC,MAAMA,CAAA,EAAG;IACL,IAAIC,EAAE;IACN,MAAMC,IAAI,GAAG,IAAI;IACjB,MAAMH,IAAI,GAAGjB,qDAAU,CAACoB,IAAI,CAAC;IAC7B,MAAMN,WAAW,GAAGM,IAAI,CAACP,OAAO,CAAC,CAAC;IAClC,MAAMQ,OAAO,GAAG,CAACF,EAAE,GAAGZ,2DAAQ,CAACO,WAAW,CAAC,MAAM,IAAI,IAAIK,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGZ,2DAAQ,CAAC,OAAO,CAAC;IAC/F,MAAMe,QAAQ,GAAG,OAAOF,IAAI,CAACE,QAAQ,KAAK,QAAQ,IAAIF,IAAI,CAACE,QAAQ,GAAG,EAAE,GAAGF,IAAI,CAACE,QAAQ,GAAGD,OAAO,CAACE,GAAG;IACtG,MAAMC,IAAI,GAAG,EAAE;IACf,IAAIH,OAAO,CAACI,OAAO,KAAKC,SAAS,EAAE;MAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,OAAO,CAACI,OAAO,EAAEE,CAAC,EAAE,EAAE;QACtCH,IAAI,CAACI,IAAI,CAACC,WAAW,CAACR,OAAO,EAAEC,QAAQ,EAAEK,CAAC,EAAEN,OAAO,CAACI,OAAO,CAAC,CAAC;MACjE;IACJ,CAAC,MACI,IAAIJ,OAAO,CAACS,KAAK,KAAKJ,SAAS,EAAE;MAClC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,OAAO,CAACS,KAAK,EAAEH,CAAC,EAAE,EAAE;QACpCH,IAAI,CAACI,IAAI,CAACG,SAAS,CAACV,OAAO,EAAEC,QAAQ,EAAEK,CAAC,EAAEN,OAAO,CAACS,KAAK,CAAC,CAAC;MAC7D;IACJ;IACA,OAAQ7B,qDAAC,CAACE,iDAAI,EAAE;MAAE6B,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE5B,qDAAkB,CAACe,IAAI,CAACc,KAAK,EAAE;QACjG,CAACjB,IAAI,GAAG,IAAI;QACZ,CAAC,WAAWH,WAAW,EAAE,GAAG,IAAI;QAChC,gBAAgB,EAAEM,IAAI,CAACR,MAAM,IAAId,iDAAM,CAACqC,UAAU,CAAC,UAAU;MACjE,CAAC,CAAC;MAAEC,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAEhB,OAAO,CAACiB,WAAW,GAAG;QAAEC,iBAAiB,EAAEjB,QAAQ,GAAG;MAAK,CAAC,GAAG,CAAC;IAAE,CAAC,EAAEE,IAAI,CAAC;EAClH;AACJ,CAAC;AACD,MAAMK,WAAW,GAAGA,CAACR,OAAO,EAAEC,QAAQ,EAAEkB,KAAK,EAAEC,KAAK,KAAK;EACrD,MAAMC,IAAI,GAAGrB,OAAO,CAACsB,EAAE,CAACrB,QAAQ,EAAEkB,KAAK,EAAEC,KAAK,CAAC;EAC/CC,IAAI,CAACL,KAAK,CAAC,oBAAoB,CAAC,GAAGf,QAAQ,GAAG,IAAI;EAClD,OAAQrB,qDAAC,CAAC,KAAK,EAAE;IAAE2C,OAAO,EAAEF,IAAI,CAACE,OAAO,IAAI,WAAW;IAAEP,KAAK,EAAEK,IAAI,CAACL;EAAM,CAAC,EAAEpC,qDAAC,CAAC,QAAQ,EAAE;IAAE4C,SAAS,EAAEH,IAAI,CAACG,SAAS,IAAI,kBAAkB;IAAEC,EAAE,EAAEJ,IAAI,CAACI,EAAE;IAAEC,EAAE,EAAEL,IAAI,CAACK,EAAE;IAAEpD,CAAC,EAAE+C,IAAI,CAAC/C,CAAC;IAAE0C,KAAK,EAAEhB,OAAO,CAACiB,WAAW,GAAG;MAAEC,iBAAiB,EAAEjB,QAAQ,GAAG;IAAK,CAAC,GAAG,CAAC;EAAE,CAAC,CAAC,CAAC;AAClQ,CAAC;AACD,MAAMS,SAAS,GAAGA,CAACV,OAAO,EAAEC,QAAQ,EAAEkB,KAAK,EAAEC,KAAK,KAAK;EACnD,MAAMC,IAAI,GAAGrB,OAAO,CAACsB,EAAE,CAACrB,QAAQ,EAAEkB,KAAK,EAAEC,KAAK,CAAC;EAC/CC,IAAI,CAACL,KAAK,CAAC,oBAAoB,CAAC,GAAGf,QAAQ,GAAG,IAAI;EAClD,OAAQrB,qDAAC,CAAC,KAAK,EAAE;IAAE2C,OAAO,EAAEF,IAAI,CAACE,OAAO,IAAI,WAAW;IAAEP,KAAK,EAAEK,IAAI,CAACL;EAAM,CAAC,EAAEpC,qDAAC,CAAC,MAAM,EAAE;IAAE4C,SAAS,EAAE,kBAAkB;IAAEG,EAAE,EAAEN,IAAI,CAACM,EAAE;IAAEC,EAAE,EAAEP,IAAI,CAACO;EAAG,CAAC,CAAC,CAAC;AACzJ,CAAC;AACDxC,OAAO,CAAC4B,KAAK,GAAG7B,UAAU", "sources": ["./node_modules/@ionic/core/dist/esm/ion-spinner.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, l as config, e as getIonMode, h, j as Host } from './index-B_U9CtaY.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\nimport { S as SPINNERS } from './spinner-configs-D4RIp70E.js';\n\nconst spinnerCss = \":host{display:inline-block;position:relative;width:28px;height:28px;color:var(--color);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}:host(.ion-color){color:var(--ion-color-base)}svg{-webkit-transform-origin:center;transform-origin:center;position:absolute;top:0;left:0;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0)}:host-context([dir=rtl]) svg{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}[dir=rtl] svg{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}@supports selector(:dir(rtl)){svg:dir(rtl){-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}}:host(.spinner-lines) line,:host(.spinner-lines-small) line{stroke-width:7px}:host(.spinner-lines-sharp) line,:host(.spinner-lines-sharp-small) line{stroke-width:4px}:host(.spinner-lines) line,:host(.spinner-lines-small) line,:host(.spinner-lines-sharp) line,:host(.spinner-lines-sharp-small) line{stroke-linecap:round;stroke:currentColor}:host(.spinner-lines) svg,:host(.spinner-lines-small) svg,:host(.spinner-lines-sharp) svg,:host(.spinner-lines-sharp-small) svg{-webkit-animation:spinner-fade-out 1s linear infinite;animation:spinner-fade-out 1s linear infinite}:host(.spinner-bubbles) svg{-webkit-animation:spinner-scale-out 1s linear infinite;animation:spinner-scale-out 1s linear infinite;fill:currentColor}:host(.spinner-circles) svg{-webkit-animation:spinner-fade-out 1s linear infinite;animation:spinner-fade-out 1s linear infinite;fill:currentColor}:host(.spinner-crescent) circle{fill:transparent;stroke-width:4px;stroke-dasharray:128px;stroke-dashoffset:82px;stroke:currentColor}:host(.spinner-crescent) svg{-webkit-animation:spinner-rotate 1s linear infinite;animation:spinner-rotate 1s linear infinite}:host(.spinner-dots) circle{stroke-width:0;fill:currentColor}:host(.spinner-dots) svg{-webkit-animation:spinner-dots 1s linear infinite;animation:spinner-dots 1s linear infinite}:host(.spinner-circular) svg{-webkit-animation:spinner-circular linear infinite;animation:spinner-circular linear infinite}:host(.spinner-circular) circle{-webkit-animation:spinner-circular-inner ease-in-out infinite;animation:spinner-circular-inner ease-in-out infinite;stroke:currentColor;stroke-dasharray:80px, 200px;stroke-dashoffset:0px;stroke-width:5.6;fill:none}:host(.spinner-paused),:host(.spinner-paused) svg,:host(.spinner-paused) circle{-webkit-animation-play-state:paused;animation-play-state:paused}@-webkit-keyframes spinner-fade-out{0%{opacity:1}100%{opacity:0}}@keyframes spinner-fade-out{0%{opacity:1}100%{opacity:0}}@-webkit-keyframes spinner-scale-out{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1)}100%{-webkit-transform:scale(0, 0);transform:scale(0, 0)}}@keyframes spinner-scale-out{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1)}100%{-webkit-transform:scale(0, 0);transform:scale(0, 0)}}@-webkit-keyframes spinner-rotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spinner-rotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes spinner-dots{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}50%{-webkit-transform:scale(0.4, 0.4);transform:scale(0.4, 0.4);opacity:0.3}100%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}}@keyframes spinner-dots{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}50%{-webkit-transform:scale(0.4, 0.4);transform:scale(0.4, 0.4);opacity:0.3}100%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}}@-webkit-keyframes spinner-circular{100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spinner-circular{100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes spinner-circular-inner{0%{stroke-dasharray:1px, 200px;stroke-dashoffset:0px}50%{stroke-dasharray:100px, 200px;stroke-dashoffset:-15px}100%{stroke-dasharray:100px, 200px;stroke-dashoffset:-125px}}@keyframes spinner-circular-inner{0%{stroke-dasharray:1px, 200px;stroke-dashoffset:0px}50%{stroke-dasharray:100px, 200px;stroke-dashoffset:-15px}100%{stroke-dasharray:100px, 200px;stroke-dashoffset:-125px}}\";\n\nconst Spinner = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        /**\n         * If `true`, the spinner's animation will be paused.\n         */\n        this.paused = false;\n    }\n    getName() {\n        const spinnerName = this.name || config.get('spinner');\n        const mode = getIonMode(this);\n        if (spinnerName) {\n            return spinnerName;\n        }\n        return mode === 'ios' ? 'lines' : 'circular';\n    }\n    render() {\n        var _a;\n        const self = this;\n        const mode = getIonMode(self);\n        const spinnerName = self.getName();\n        const spinner = (_a = SPINNERS[spinnerName]) !== null && _a !== void 0 ? _a : SPINNERS['lines'];\n        const duration = typeof self.duration === 'number' && self.duration > 10 ? self.duration : spinner.dur;\n        const svgs = [];\n        if (spinner.circles !== undefined) {\n            for (let i = 0; i < spinner.circles; i++) {\n                svgs.push(buildCircle(spinner, duration, i, spinner.circles));\n            }\n        }\n        else if (spinner.lines !== undefined) {\n            for (let i = 0; i < spinner.lines; i++) {\n                svgs.push(buildLine(spinner, duration, i, spinner.lines));\n            }\n        }\n        return (h(Host, { key: 'a33d6421fcc885995fbc7a348516525f68ca496c', class: createColorClasses(self.color, {\n                [mode]: true,\n                [`spinner-${spinnerName}`]: true,\n                'spinner-paused': self.paused || config.getBoolean('_testing'),\n            }), role: \"progressbar\", style: spinner.elmDuration ? { animationDuration: duration + 'ms' } : {} }, svgs));\n    }\n};\nconst buildCircle = (spinner, duration, index, total) => {\n    const data = spinner.fn(duration, index, total);\n    data.style['animation-duration'] = duration + 'ms';\n    return (h(\"svg\", { viewBox: data.viewBox || '0 0 64 64', style: data.style }, h(\"circle\", { transform: data.transform || 'translate(32,32)', cx: data.cx, cy: data.cy, r: data.r, style: spinner.elmDuration ? { animationDuration: duration + 'ms' } : {} })));\n};\nconst buildLine = (spinner, duration, index, total) => {\n    const data = spinner.fn(duration, index, total);\n    data.style['animation-duration'] = duration + 'ms';\n    return (h(\"svg\", { viewBox: data.viewBox || '0 0 64 64', style: data.style }, h(\"line\", { transform: \"translate(32,32)\", y1: data.y1, y2: data.y2 })));\n};\nSpinner.style = spinnerCss;\n\nexport { Spinner as ion_spinner };\n"], "names": ["r", "registerInstance", "l", "config", "e", "getIonMode", "h", "j", "Host", "c", "createColorClasses", "S", "SPINNERS", "spinnerCss", "Spinner", "constructor", "hostRef", "paused", "getName", "spinnerName", "name", "get", "mode", "render", "_a", "self", "spinner", "duration", "dur", "svgs", "circles", "undefined", "i", "push", "buildCircle", "lines", "buildLine", "key", "class", "color", "getBoolean", "role", "style", "elmDuration", "animationDuration", "index", "total", "data", "fn", "viewBox", "transform", "cx", "cy", "y1", "y2", "ion_spinner"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}