(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[8792],{6031:(pn,pt,Y)=>{"use strict";Y.d(pt,{L:()=>be,a:()=>Ue,b:()=>Je,c:()=>ke,d:()=>Z,g:()=>at}),Y(7837);const be="ionViewWillEnter",Ue="ionViewDidEnter",Je="ionViewWillLeave",ke="ionViewDidLeave",Z="ionViewWillUnload",at=re=>re.classList.contains("ion-page")?re:re.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")||re},7837:(pn,pt,Y)=>{"use strict";Y.d(pt,{a:()=>He,c:()=>be,p:()=>Ae});class oe{constructor(){this.m=new Map}reset(P){this.m=new Map(Object.entries(P))}get(P,K){const q=this.m.get(P);return void 0!==q?q:K}getBoolean(P,K=!1){const q=this.m.get(P);return void 0===q?K:"string"==typeof q?"true"===q:!!q}getNumber(P,K){const q=parseFloat(this.m.get(P));return isNaN(q)?void 0!==K?K:NaN:q}set(P,K){this.m.set(P,K)}}const be=new oe;var De=function(I){return I.OFF="OFF",I.ERROR="ERROR",I.WARN="WARN",I}(De||{});const Ae=(I,...P)=>{const K=be.get("logLevel",De.WARN);if([De.WARN].includes(K))return console.warn(`[Ionic Warning]: ${I}`,...P)},He=(I,...P)=>{const K=be.get("logLevel",De.ERROR);if([De.ERROR,De.WARN].includes(K))return console.error(`[Ionic Error]: ${I}`,...P)}},5756:(pn,pt,Y)=>{"use strict";Y.d(pt,{c:()=>ie});var oe=Y(2734),be=Y(9596);let Ue;const ke=(se,De,Ae)=>{const He=De.startsWith("animation")?(se=>(void 0===Ue&&(Ue=void 0===se.style.animationName&&void 0!==se.style.webkitAnimationName?"-webkit-":""),Ue))(se):"";se.style.setProperty(He+De,Ae)},Z=(se=[],De)=>{if(void 0!==De){const Ae=Array.isArray(De)?De:[De];return[...se,...Ae]}return se},ie=se=>{let De,Ae,He,ge,I,P,ee,Ne,de,Oe,V,K=[],q=[],J=[],X=!1,G={},Re=[],Te=[],Le={},k=0,ye=!1,we=!1,We=!0,at=!1,re=!0,R=!1;const B=se,x=[],te=[],ne=[],$e=[],ze=[],qe=[],Ke=[],St=[],gn=[],Jt=[],xt=[],cn="function"==typeof AnimationEffect||void 0!==be.w&&"function"==typeof be.w.AnimationEffect,Gt="function"==typeof Element&&"function"==typeof Element.prototype.animate&&cn,nt=()=>xt,Q=(S,xe)=>{const gt=xe.findIndex(Qe=>Qe.c===S);gt>-1&&xe.splice(gt,1)},H=(S,xe)=>((xe?.oneTimeCallback?te:x).push({c:S,o:xe}),V),lt=()=>{Gt&&(xt.forEach(S=>{S.cancel()}),xt.length=0)},qt=()=>{qe.forEach(S=>{S?.parentNode&&S.parentNode.removeChild(S)}),qe.length=0},Rr=()=>void 0!==I?I:ee?ee.getFill():"both",ht=()=>void 0!==Ne?Ne:void 0!==P?P:ee?ee.getDirection():"normal",Ys=()=>ye?"linear":void 0!==He?He:ee?ee.getEasing():"linear",it=()=>we?0:void 0!==de?de:void 0!==Ae?Ae:ee?ee.getDuration():0,qi=()=>void 0!==ge?ge:ee?ee.getIterations():1,Yo=()=>void 0!==Oe?Oe:void 0!==De?De:ee?ee.getDelay():0,Zo=()=>{0!==k&&(k--,0===k&&((()=>{gn.forEach(On=>On()),Jt.forEach(On=>On());const S=We?1:0,xe=Re,gt=Te,Qe=Le;$e.forEach(On=>{const At=On.classList;xe.forEach(Pr=>At.add(Pr)),gt.forEach(Pr=>At.remove(Pr));for(const Pr in Qe)Qe.hasOwnProperty(Pr)&&ke(On,Pr,Qe[Pr])}),de=void 0,Ne=void 0,Oe=void 0,x.forEach(On=>On.c(S,V)),te.forEach(On=>On.c(S,V)),te.length=0,re=!0,We&&(at=!0),We=!0})(),ee&&ee.animationFinish()))},xl=()=>{(()=>{Ke.forEach(Qe=>Qe()),St.forEach(Qe=>Qe());const S=q,xe=J,gt=G;$e.forEach(Qe=>{const On=Qe.classList;S.forEach(At=>On.add(At)),xe.forEach(At=>On.remove(At));for(const At in gt)gt.hasOwnProperty(At)&&ke(Qe,At,gt[At])})})(),K.length>0&&Gt&&($e.forEach(S=>{const xe=S.animate(K,{id:B,delay:Yo(),duration:it(),easing:Ys(),iterations:qi(),fill:Rr(),direction:ht()});xe.pause(),xt.push(xe)}),xt.length>0&&(xt[0].onfinish=()=>{Zo()})),X=!0},Eo=S=>{S=Math.min(Math.max(S,0),.9999),Gt&&xt.forEach(xe=>{xe.currentTime=xe.effect.getComputedTiming().delay+it()*S,xe.pause()})},z=S=>{xt.forEach(xe=>{xe.effect.updateTiming({delay:Yo(),duration:it(),easing:Ys(),iterations:qi(),fill:Rr(),direction:ht()})}),void 0!==S&&Eo(S)},mn=(S=!1,xe=!0,gt)=>(S&&ze.forEach(Qe=>{Qe.update(S,xe,gt)}),Gt&&z(gt),V),Ol=()=>{X&&(Gt?xt.forEach(S=>{S.pause()}):$e.forEach(S=>{ke(S,"animation-play-state","paused")}),R=!0)},Nr=S=>new Promise(xe=>{S?.sync&&(we=!0,H(()=>we=!1,{oneTimeCallback:!0})),X||xl(),at&&(Gt&&(Eo(0),z()),at=!1),re&&(k=ze.length+1,re=!1);const gt=()=>{Q(Qe,te),xe()},Qe=()=>{Q(gt,ne),xe()};H(Qe,{oneTimeCallback:!0}),(S=>{ne.push({c:S,o:{oneTimeCallback:!0}})})(gt),ze.forEach(On=>{On.play()}),Gt?(xt.forEach(S=>{S.play()}),(0===K.length||0===$e.length)&&Zo()):Zo(),R=!1}),oo=(S,xe)=>{const gt=K[0];return void 0===gt||void 0!==gt.offset&&0!==gt.offset?K=[{offset:0,[S]:xe},...K]:gt[S]=xe,V};return V={parentAnimation:ee,elements:$e,childAnimations:ze,id:B,animationFinish:Zo,from:oo,to:(S,xe)=>{const gt=K[K.length-1];return void 0===gt||void 0!==gt.offset&&1!==gt.offset?K=[...K,{offset:1,[S]:xe}]:gt[S]=xe,V},fromTo:(S,xe,gt)=>oo(S,xe).to(S,gt),parent:S=>(ee=S,V),play:Nr,pause:()=>(ze.forEach(S=>{S.pause()}),Ol(),V),stop:()=>{ze.forEach(S=>{S.stop()}),X&&(lt(),X=!1),ye=!1,we=!1,re=!0,Ne=void 0,de=void 0,Oe=void 0,k=0,at=!1,We=!0,R=!1,ne.forEach(S=>S.c(0,V)),ne.length=0},destroy:S=>(ze.forEach(xe=>{xe.destroy(S)}),(S=>{lt(),S&&qt()})(S),$e.length=0,ze.length=0,K.length=0,x.length=0,te.length=0,X=!1,re=!0,V),keyframes:S=>{const xe=K!==S;return K=S,xe&&(S=>{Gt&&nt().forEach(xe=>{const gt=xe.effect;if(gt.setKeyframes)gt.setKeyframes(S);else{const Qe=new KeyframeEffect(gt.target,S,gt.getTiming());xe.effect=Qe}})})(K),V},addAnimation:S=>{if(null!=S)if(Array.isArray(S))for(const xe of S)xe.parent(V),ze.push(xe);else S.parent(V),ze.push(S);return V},addElement:S=>{if(null!=S)if(1===S.nodeType)$e.push(S);else if(S.length>=0)for(let xe=0;xe<S.length;xe++)$e.push(S[xe]);else(0,oe.o)("createAnimation - Invalid addElement value.");return V},update:mn,fill:S=>(I=S,mn(!0),V),direction:S=>(P=S,mn(!0),V),iterations:S=>(ge=S,mn(!0),V),duration:S=>(!Gt&&0===S&&(S=1),Ae=S,mn(!0),V),easing:S=>(He=S,mn(!0),V),delay:S=>(De=S,mn(!0),V),getWebAnimations:nt,getKeyframes:()=>K,getFill:Rr,getDirection:ht,getDelay:Yo,getIterations:qi,getEasing:Ys,getDuration:it,afterAddRead:S=>(gn.push(S),V),afterAddWrite:S=>(Jt.push(S),V),afterClearStyles:(S=[])=>{for(const xe of S)Le[xe]="";return V},afterStyles:(S={})=>(Le=S,V),afterRemoveClass:S=>(Te=Z(Te,S),V),afterAddClass:S=>(Re=Z(Re,S),V),beforeAddRead:S=>(Ke.push(S),V),beforeAddWrite:S=>(St.push(S),V),beforeClearStyles:(S=[])=>{for(const xe of S)G[xe]="";return V},beforeStyles:(S={})=>(G=S,V),beforeRemoveClass:S=>(J=Z(J,S),V),beforeAddClass:S=>(q=Z(q,S),V),onFinish:H,isRunning:()=>0!==k&&!R,progressStart:(S=!1,xe)=>(ze.forEach(gt=>{gt.progressStart(S,xe)}),Ol(),ye=S,X||xl(),mn(!1,!0,xe),V),progressStep:S=>(ze.forEach(xe=>{xe.progressStep(S)}),Eo(S),V),progressEnd:(S,xe,gt)=>(ye=!1,ze.forEach(Qe=>{Qe.progressEnd(S,xe,gt)}),void 0!==gt&&(de=gt),at=!1,We=!0,0===S?(Ne="reverse"===ht()?"normal":"reverse","reverse"===Ne&&(We=!1),Gt?(mn(),Eo(1-xe)):(Oe=(1-xe)*it()*-1,mn(!1,!1))):1===S&&(Gt?(mn(),Eo(xe)):(Oe=xe*it()*-1,mn(!1,!1))),void 0!==S&&!ee&&Nr(),V)}}},6640:(pn,pt,Y)=>{"use strict";Y.d(pt,{E:()=>He,a:()=>be,s:()=>De});var oe=Y(2734);const be=ge=>{try{if(ge instanceof se)return ge.value;if(!ke()||"string"!=typeof ge||""===ge)return ge;if(ge.includes("onload="))return"";const I=document.createDocumentFragment(),P=document.createElement("div");I.appendChild(P),P.innerHTML=ge,ie.forEach(X=>{const ee=I.querySelectorAll(X);for(let G=ee.length-1;G>=0;G--){const Re=ee[G];Re.parentNode?Re.parentNode.removeChild(Re):I.removeChild(Re);const Te=Je(Re);for(let Le=0;Le<Te.length;Le++)Ue(Te[Le])}});const K=Je(I);for(let X=0;X<K.length;X++)Ue(K[X]);const q=document.createElement("div");q.appendChild(I);const J=q.querySelector("div");return null!==J?J.innerHTML:q.innerHTML}catch(I){return(0,oe.o)("sanitizeDOMString",I),""}},Ue=ge=>{if(ge.nodeType&&1!==ge.nodeType)return;if(typeof NamedNodeMap<"u"&&!(ge.attributes instanceof NamedNodeMap))return void ge.remove();for(let P=ge.attributes.length-1;P>=0;P--){const K=ge.attributes.item(P),q=K.name;if(!Z.includes(q.toLowerCase())){ge.removeAttribute(q);continue}const J=K.value,X=ge[q];(null!=J&&J.toLowerCase().includes("javascript:")||null!=X&&X.toLowerCase().includes("javascript:"))&&ge.removeAttribute(q)}const I=Je(ge);for(let P=0;P<I.length;P++)Ue(I[P])},Je=ge=>null!=ge.children?ge.children:ge.childNodes,ke=()=>{var ge;const I=window,P=null===(ge=I?.Ionic)||void 0===ge?void 0:ge.config;return!P||(P.get?P.get("sanitizerEnabled",!0):!0===P.sanitizerEnabled||void 0===P.sanitizerEnabled)},Z=["class","id","href","src","name","slot"],ie=["script","style","iframe","meta","link","object","embed"];class se{constructor(I){this.value=I}}const De=ge=>{const I=window,P=I.Ionic;if(!P||!P.config||"Object"===P.config.constructor.name)return I.Ionic=I.Ionic||{},I.Ionic.config=Object.assign(Object.assign({},I.Ionic.config),ge),I.Ionic.config},He=!1},1653:(pn,pt,Y)=>{"use strict";Y.d(pt,{C:()=>ke,a:()=>Ue,d:()=>Je});var oe=Y(467),be=Y(1837);const Ue=function(){var Z=(0,oe.A)(function*(ie,se,De,Ae,He,ge){var I;if(ie)return ie.attachViewToDom(se,De,He,Ae);if(!(ge||"string"==typeof De||De instanceof HTMLElement))throw new Error("framework delegate is missing");const P="string"==typeof De?null===(I=se.ownerDocument)||void 0===I?void 0:I.createElement(De):De;return Ae&&Ae.forEach(K=>P.classList.add(K)),He&&Object.assign(P,He),se.appendChild(P),yield new Promise(K=>(0,be.c)(P,K)),P});return function(se,De,Ae,He,ge,I){return Z.apply(this,arguments)}}(),Je=(Z,ie)=>{if(ie){if(Z)return Z.removeViewFromDom(ie.parentElement,ie);ie.remove()}return Promise.resolve()},ke=()=>{let Z,ie;return{attachViewToDom:function(){var Ae=(0,oe.A)(function*(He,ge,I={},P=[]){var K,q;let J;if(Z=He,ge){const ee="string"==typeof ge?null===(K=Z.ownerDocument)||void 0===K?void 0:K.createElement(ge):ge;P.forEach(G=>ee.classList.add(G)),Object.assign(ee,I),Z.appendChild(ee),J=ee,yield new Promise(G=>(0,be.c)(ee,G))}else if(Z.children.length>0&&("ION-MODAL"===Z.tagName||"ION-POPOVER"===Z.tagName)&&!(J=Z.children[0]).classList.contains("ion-delegate-host")){const G=null===(q=Z.ownerDocument)||void 0===q?void 0:q.createElement("div");G.classList.add("ion-delegate-host"),P.forEach(Re=>G.classList.add(Re)),G.append(...Z.children),Z.appendChild(G),J=G}const X=document.querySelector("ion-app")||document.body;return ie=document.createComment("ionic teleport"),Z.parentNode.insertBefore(ie,Z),X.appendChild(Z),J??Z});return function(ge,I){return Ae.apply(this,arguments)}}(),removeViewFromDom:()=>(Z&&ie&&(ie.parentNode.insertBefore(Z,ie),ie.remove()),Promise.resolve())}}},8607:(pn,pt,Y)=>{"use strict";Y.d(pt,{B:()=>Je,G:()=>ke});class be{constructor(ie,se,De,Ae,He){this.id=se,this.name=De,this.disableScroll=He,this.priority=1e6*Ae+se,this.ctrl=ie}canStart(){return!!this.ctrl&&this.ctrl.canStart(this.name)}start(){return!!this.ctrl&&this.ctrl.start(this.name,this.id,this.priority)}capture(){if(!this.ctrl)return!1;const ie=this.ctrl.capture(this.name,this.id,this.priority);return ie&&this.disableScroll&&this.ctrl.disableScroll(this.id),ie}release(){this.ctrl&&(this.ctrl.release(this.id),this.disableScroll&&this.ctrl.enableScroll(this.id))}destroy(){this.release(),this.ctrl=void 0}}class Ue{constructor(ie,se,De,Ae){this.id=se,this.disable=De,this.disableScroll=Ae,this.ctrl=ie}block(){if(this.ctrl){if(this.disable)for(const ie of this.disable)this.ctrl.disableGesture(ie,this.id);this.disableScroll&&this.ctrl.disableScroll(this.id)}}unblock(){if(this.ctrl){if(this.disable)for(const ie of this.disable)this.ctrl.enableGesture(ie,this.id);this.disableScroll&&this.ctrl.enableScroll(this.id)}}destroy(){this.unblock(),this.ctrl=void 0}}const Je="backdrop-no-scroll",ke=new class oe{constructor(){this.gestureId=0,this.requestedStart=new Map,this.disabledGestures=new Map,this.disabledScroll=new Set}createGesture(ie){var se;return new be(this,this.newID(),ie.name,null!==(se=ie.priority)&&void 0!==se?se:0,!!ie.disableScroll)}createBlocker(ie={}){return new Ue(this,this.newID(),ie.disable,!!ie.disableScroll)}start(ie,se,De){return this.canStart(ie)?(this.requestedStart.set(se,De),!0):(this.requestedStart.delete(se),!1)}capture(ie,se,De){if(!this.start(ie,se,De))return!1;const Ae=this.requestedStart;let He=-1e4;if(Ae.forEach(ge=>{He=Math.max(He,ge)}),He===De){this.capturedId=se,Ae.clear();const ge=new CustomEvent("ionGestureCaptured",{detail:{gestureName:ie}});return document.dispatchEvent(ge),!0}return Ae.delete(se),!1}release(ie){this.requestedStart.delete(ie),this.capturedId===ie&&(this.capturedId=void 0)}disableGesture(ie,se){let De=this.disabledGestures.get(ie);void 0===De&&(De=new Set,this.disabledGestures.set(ie,De)),De.add(se)}enableGesture(ie,se){const De=this.disabledGestures.get(ie);void 0!==De&&De.delete(se)}disableScroll(ie){this.disabledScroll.add(ie),1===this.disabledScroll.size&&document.body.classList.add(Je)}enableScroll(ie){this.disabledScroll.delete(ie),0===this.disabledScroll.size&&document.body.classList.remove(Je)}canStart(ie){return!(void 0!==this.capturedId||this.isDisabled(ie))}isCaptured(){return void 0!==this.capturedId}isScrollDisabled(){return this.disabledScroll.size>0}isDisabled(ie){const se=this.disabledGestures.get(ie);return!!(se&&se.size>0)}newID(){return this.gestureId++,this.gestureId}}},1906:(pn,pt,Y)=>{"use strict";Y.r(pt),Y.d(pt,{MENU_BACK_BUTTON_PRIORITY:()=>se,OVERLAY_BACK_BUTTON_PRIORITY:()=>ie,blockHardwareBackButton:()=>ke,shouldUseCloseWatcher:()=>Je,startHardwareBackButton:()=>Z});var oe=Y(467),be=Y(9596),Ue=Y(2734);const Je=()=>Ue.l.get("experimentalCloseWatcher",!1)&&void 0!==be.w&&"CloseWatcher"in be.w,ke=()=>{document.addEventListener("backbutton",()=>{})},Z=()=>{const De=document;let Ae=!1;const He=()=>{if(Ae)return;let ge=0,I=[];const P=new CustomEvent("ionBackButton",{bubbles:!1,detail:{register(J,X){I.push({priority:J,handler:X,id:ge++})}}});De.dispatchEvent(P);const K=function(){var J=(0,oe.A)(function*(X){try{if(X?.handler){const ee=X.handler(q);null!=ee&&(yield ee)}}catch(ee){(0,Ue.o)("[ion-app] - Exception in startHardwareBackButton:",ee)}});return function(ee){return J.apply(this,arguments)}}(),q=()=>{if(I.length>0){let J={priority:Number.MIN_SAFE_INTEGER,handler:()=>{},id:-1};I.forEach(X=>{X.priority>=J.priority&&(J=X)}),Ae=!0,I=I.filter(X=>X.id!==J.id),K(J).then(()=>Ae=!1)}};q()};if(Je()){let ge;const I=()=>{ge?.destroy(),ge=new be.w.CloseWatcher,ge.onclose=()=>{He(),I()}};I()}else De.addEventListener("backbutton",He)},ie=100,se=99},1837:(pn,pt,Y)=>{"use strict";Y.d(pt,{a:()=>K,b:()=>Z,c:()=>Je,d:()=>G,e:()=>q,f:()=>De,g:()=>He,h:()=>ke,i:()=>se,j:()=>Le,k:()=>I,l:()=>J,m:()=>Ae,n:()=>P,o:()=>ee,p:()=>Re,q:()=>X,r:()=>ge,s:()=>Te,t:()=>be});var oe=Y(2734);const be=(k,ye=0)=>new Promise(we=>{Ue(k,ye,we)}),Ue=(k,ye=0,we)=>{let Ne,de;const Oe={passive:!0},at=()=>{Ne&&Ne()},re=V=>{(void 0===V||k===V.target)&&(at(),we(V))};return k&&(k.addEventListener("webkitTransitionEnd",re,Oe),k.addEventListener("transitionend",re,Oe),de=setTimeout(re,ye+500),Ne=()=>{void 0!==de&&(clearTimeout(de),de=void 0),k.removeEventListener("webkitTransitionEnd",re,Oe),k.removeEventListener("transitionend",re,Oe)}),at},Je=(k,ye)=>{k.componentOnReady?k.componentOnReady().then(we=>ye(we)):ge(()=>ye(k))},ke=k=>void 0!==k.componentOnReady,Z=(k,ye=[])=>{const we={};return ye.forEach(Ne=>{k.hasAttribute(Ne)&&(null!==k.getAttribute(Ne)&&(we[Ne]=k.getAttribute(Ne)),k.removeAttribute(Ne))}),we},ie=["role","aria-activedescendant","aria-atomic","aria-autocomplete","aria-braillelabel","aria-brailleroledescription","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colindextext","aria-colspan","aria-controls","aria-current","aria-describedby","aria-description","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowindextext","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext"],se=(k,ye)=>Z(k,ie),De=(k,ye,we,Ne)=>k.addEventListener(ye,we,Ne),Ae=(k,ye,we,Ne)=>k.removeEventListener(ye,we,Ne),He=(k,ye=k)=>k.shadowRoot||ye,ge=k=>"function"==typeof __zone_symbol__requestAnimationFrame?__zone_symbol__requestAnimationFrame(k):"function"==typeof requestAnimationFrame?requestAnimationFrame(k):setTimeout(k),I=k=>!!k.shadowRoot&&!!k.attachShadow,P=k=>{if(k.focus(),k.classList.contains("ion-focusable")){const ye=k.closest("ion-app");ye&&ye.setFocus([k])}},K=(k,ye,we,Ne,de)=>{{let Oe=ye.querySelector("input.aux-input");Oe||(Oe=ye.ownerDocument.createElement("input"),Oe.type="hidden",Oe.classList.add("aux-input"),ye.appendChild(Oe)),Oe.disabled=de,Oe.name=we,Oe.value=Ne||""}},q=(k,ye,we)=>Math.max(k,Math.min(ye,we)),J=(k,ye)=>{if(!k){const we="ASSERT: "+ye;throw(0,oe.o)(we),new Error(we)}},X=k=>{if(k){const ye=k.changedTouches;if(ye&&ye.length>0){const we=ye[0];return{x:we.clientX,y:we.clientY}}if(void 0!==k.pageX)return{x:k.pageX,y:k.pageY}}return{x:0,y:0}},ee=k=>{const ye="rtl"===document.dir;switch(k){case"start":return ye;case"end":return!ye;default:throw new Error(`"${k}" is not a valid value for [side]. Use "start" or "end" instead.`)}},G=(k,ye)=>{const we=k._original||k;return{_original:k,emit:Re(we.emit.bind(we),ye)}},Re=(k,ye=0)=>{let we;return(...Ne)=>{clearTimeout(we),we=setTimeout(k,ye,...Ne)}},Te=(k,ye)=>{if(k??(k={}),ye??(ye={}),k===ye)return!0;const we=Object.keys(k);if(we.length!==Object.keys(ye).length)return!1;for(const Ne of we)if(!(Ne in ye)||k[Ne]!==ye[Ne])return!1;return!0},Le=k=>"number"==typeof k&&!isNaN(k)&&isFinite(k)},2734:(pn,pt,Y)=>{"use strict";Y.d(pt,{B:()=>Jt,F:()=>yi,a:()=>J,b:()=>Xn,d:()=>Pl,e:()=>$e,f:()=>Qs,g:()=>qe,h:()=>Xe,j:()=>ep,k:()=>ra,l:()=>ke,m:()=>I,n:()=>ga,o:()=>P,q:()=>Js,r:()=>Q,t:()=>K,w:()=>wn});var oe=Y(467);class Je{constructor(){this.m=new Map}reset(f){this.m=new Map(Object.entries(f))}get(f,p){const m=this.m.get(f);return void 0!==m?m:p}getBoolean(f,p=!1){const m=this.m.get(f);return void 0===m?p:"string"==typeof m?"true"===m:!!m}getNumber(f,p){const m=parseFloat(this.m.get(f));return isNaN(m)?void 0!==p?p:NaN:m}set(f,p){this.m.set(f,p)}}const ke=new Je,He="ionic-persist-config";var ge=function(u){return u.OFF="OFF",u.ERROR="ERROR",u.WARN="WARN",u}(ge||{});const I=(u,...f)=>{const p=ke.get("logLevel",ge.WARN);if([ge.WARN].includes(p))return console.warn(`[Ionic Warning]: ${u}`,...f)},P=(u,...f)=>{const p=ke.get("logLevel",ge.ERROR);if([ge.ERROR,ge.WARN].includes(p))return console.error(`[Ionic Error]: ${u}`,...f)},K=(u,...f)=>console.error(`<${u.tagName.toLowerCase()}> must be used inside ${f.join(" or ")}.`),J=(u,f)=>("string"==typeof u&&(f=u,u=void 0),(u=>X(u))(u).includes(f)),X=(u=window)=>{if(typeof u>"u")return[];u.Ionic=u.Ionic||{};let f=u.Ionic.platforms;return null==f&&(f=u.Ionic.platforms=ee(u),f.forEach(p=>u.document.documentElement.classList.add(`plt-${p}`))),f},ee=u=>{const f=ke.get("platform");return Object.keys(te).filter(p=>{const m=f?.[p];return"function"==typeof m?m(u):te[p](u)})},Re=u=>!!(B(u,/iPad/i)||B(u,/Macintosh/i)&&de(u)),k=u=>B(u,/android|sink/i),de=u=>x(u,"(any-pointer:coarse)"),We=u=>at(u)||re(u),at=u=>!!(u.cordova||u.phonegap||u.PhoneGap),re=u=>{const f=u.Capacitor;return!!(f?.isNative||f?.isNativePlatform&&f.isNativePlatform())},B=(u,f)=>f.test(u.navigator.userAgent),x=(u,f)=>{var p;return null===(p=u.matchMedia)||void 0===p?void 0:p.call(u,f).matches},te={ipad:Re,iphone:u=>B(u,/iPhone/i),ios:u=>B(u,/iPhone|iPod/i)||Re(u),android:k,phablet:u=>{const f=u.innerWidth,p=u.innerHeight,m=Math.min(f,p),_=Math.max(f,p);return m>390&&m<520&&_>620&&_<800},tablet:u=>{const f=u.innerWidth,p=u.innerHeight,m=Math.min(f,p),_=Math.max(f,p);return Re(u)||(u=>k(u)&&!B(u,/mobile/i))(u)||m>460&&m<820&&_>780&&_<1400},cordova:at,capacitor:re,electron:u=>B(u,/electron/i),pwa:u=>{var f;return!!(null!==(f=u.matchMedia)&&void 0!==f&&f.call(u,"(display-mode: standalone)").matches||u.navigator.standalone)},mobile:de,mobileweb:u=>de(u)&&!We(u),desktop:u=>!de(u),hybrid:We};let ne;const $e=u=>u&&np(u)||ne,qe=(u={})=>{if(typeof window>"u")return;const f=window.document,p=window,m=p.Ionic=p.Ionic||{},_=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(u=>{try{const f=u.sessionStorage.getItem(He);return null!==f?JSON.parse(f):{}}catch{return{}}})(p)),{persistConfig:!1}),m.config),(u=>{const f={};return u.location.search.slice(1).split("&").map(p=>p.split("=")).map(([p,m])=>{try{return[decodeURIComponent(p),decodeURIComponent(m)]}catch{return["",""]}}).filter(([p])=>((u,f)=>u.substr(0,f.length)===f)(p,"ionic:")).map(([p,m])=>[p.slice(6),m]).forEach(([p,m])=>{f[p]=m}),f})(p)),u);ke.reset(_),ke.getBoolean("persistConfig")&&((u,f)=>{try{u.sessionStorage.setItem(He,JSON.stringify(f))}catch{return}})(p,_),X(p),m.config=ke,m.mode=ne=ke.get("mode",f.documentElement.getAttribute("mode")||(J(p,"ios")?"ios":"md")),ke.set("mode",ne),f.documentElement.setAttribute("mode",ne),f.documentElement.classList.add(ne),ke.getBoolean("_testing")&&ke.set("animated",!1);const D=E=>{var T;return null===(T=E.tagName)||void 0===T?void 0:T.startsWith("ION-")},N=E=>["ios","md"].includes(E);Uu(E=>{for(;E;){const T=E.mode||E.getAttribute("mode");if(T){if(N(T))return T;D(E)&&I('Invalid ionic mode: "'+T+'", expected: "ios" or "md"')}E=E.parentElement}return ne})};var St=Object.defineProperty,Jt={isBrowser:!0},Gt=(u=>(u.Undefined="undefined",u.Null="null",u.String="string",u.Number="number",u.SpecialNumber="number",u.Boolean="boolean",u.BigInt="bigint",u))(Gt||{}),nt=(u=>(u.Array="array",u.Date="date",u.Map="map",u.Object="object",u.RegularExpression="regexp",u.Set="set",u.Channel="channel",u.Symbol="symbol",u))(nt||{}),Wt="type",un="value",yn="serialized:",Ze=u=>{if(u.__stencil__getHostRef)return u.__stencil__getHostRef()},Q=(u,f)=>{u.__stencil__getHostRef=()=>f,f.$lazyInstance$=u},H=(u,f)=>f in u,Ie=(u,f)=>(0,console.error)(u,f),lt=new Map,Ot=new Map,Pt=[],tr="s-id",Xr="sty-id",Xs="slot-fb{display:contents}slot-fb[hidden]{display:none}",Rr="http://www.w3.org/1999/xlink",ht=typeof window<"u"?window:{},it={$flags$:0,$resourcesUrl$:"",jmp:u=>u(),raf:u=>requestAnimationFrame(u),ael:(u,f,p,m)=>u.addEventListener(f,p,m),rel:(u,f,p,m)=>u.removeEventListener(f,p,m),ce:(u,f)=>new CustomEvent(u,f)},Yo=(()=>{var u;let f=!1;try{null==(u=ht.document)||u.addEventListener("e",null,Object.defineProperty({},"passive",{get(){f=!0}}))}catch{}return f})(),Xi=(()=>{try{return new CSSStyleSheet,"function"==typeof(new CSSStyleSheet).replaceSync}catch{}return!1})(),hi=!1,Yt=[],Yi=[],Zs=(u,f)=>p=>{u.push(p),hi||(hi=!0,f&&4&it.$flags$?jt(Zi):it.raf(Zi))},Ks=u=>{for(let f=0;f<u.length;f++)try{u[f](performance.now())}catch(p){Ie(p)}u.length=0},Zi=()=>{Ks(Yt),Ks(Yi),(hi=Yt.length>0)&&it.raf(Zi)},jt=u=>Promise.resolve(void 0).then(u),Qs=Zs(Yt,!1),wn=Zs(Yi,!0),Js=u=>{const f=new URL(u,it.$resourcesUrl$);return f.origin!==ht.location.origin?f.href:f.pathname},en=u=>"object"==(u=typeof u)||"function"===u;function Zo(u){var f,p,m;return null!=(m=null==(p=null==(f=u.head)?void 0:f.querySelector('meta[name="csp-nonce"]'))?void 0:p.getAttribute("content"))?m:void 0}var xl=class qs{static fromLocalValue(f){const p=f[Wt],m=un in f?f[un]:void 0;switch(p){case"string":case"boolean":return m;case"bigint":return BigInt(m);case"undefined":return;case"null":return null;case"number":return"NaN"===m?NaN:"-0"===m?-0:"Infinity"===m?1/0:"-Infinity"===m?-1/0:m;case"array":return m.map(w=>qs.fromLocalValue(w));case"date":return new Date(m);case"map":const _=new Map;for(const[w,F]of m){const fe="object"==typeof w&&null!==w?qs.fromLocalValue(w):w,ue=qs.fromLocalValue(F);_.set(fe,ue)}return _;case"object":const D={};for(const[w,F]of m)D[w]=qs.fromLocalValue(F);return D;case"regexp":const{pattern:N,flags:E}=m;return new RegExp(N,E);case"set":const T=new Set;for(const w of m)T.add(qs.fromLocalValue(w));return T;case"symbol":return Symbol(m);default:throw new Error(`Unsupported type: ${p}`)}}static fromLocalValueArray(f){return f.map(p=>qs.fromLocalValue(p))}static isLocalValueObject(f){if("object"!=typeof f||null===f||!f.hasOwnProperty(Wt))return!1;const p=f[Wt];return!!Object.values({...Gt,...nt}).includes(p)&&("null"===p||"undefined"===p||f.hasOwnProperty(un))}};((u,f)=>{for(var p in f)St(u,p,{get:f[p],enumerable:!0})})({},{err:()=>mn,map:()=>pi,ok:()=>z,unwrap:()=>ea,unwrapErr:()=>$u});var z=u=>({isOk:!0,isErr:!1,value:u}),mn=u=>({isOk:!1,isErr:!0,value:u});function pi(u,f){if(u.isOk){const p=f(u.value);return p instanceof Promise?p.then(m=>z(m)):z(p)}if(u.isErr)return mn(u.value);throw"should never get here"}var ea=u=>{if(u.isOk)return u.value;throw u.value},$u=u=>{if(u.isErr)return u.value;throw u.value};function ta(u){const f=this.attachShadow({mode:"open",delegatesFocus:!!(16&u.$flags$)});if(Xi){const p=new CSSStyleSheet;p.replaceSync(""),f.adoptedStyleSheets.push(p)}}var Gn=u=>{const f=Wn(u,"childNodes");u.tagName&&u.tagName.includes("-")&&u["s-cr"]&&"SLOT-FB"!==u.tagName&&Dr(f,u.tagName).forEach(m=>{1===m.nodeType&&"SLOT-FB"===m.tagName&&(m.hidden=!!Nr(m,br(m),!1).length)});let p=0;for(p=0;p<f.length;p++){const m=f[p];1===m.nodeType&&Wn(m,"childNodes").length&&Gn(m)}},_r=u=>{const f=[];for(let p=0;p<u.length;p++){const m=u[p]["s-nr"]||void 0;m&&m.isConnected&&f.push(m)}return f};function Dr(u,f,p){let D,m=0,_=[];for(;m<u.length;m++){if(D=u[m],D["s-sr"]&&(!f||D["s-hn"]===f)&&(void 0===p||br(D)===p)&&(_.push(D),typeof p<"u"))return _;_=[..._,...Dr(D.childNodes,f,p)]}return _}var Nr=(u,f,p=!0)=>{const m=[];(p&&u["s-sr"]||!u["s-sr"])&&m.push(u);let _=u;for(;_=_.nextSibling;)br(_)===f&&(p||!_["s-sr"])&&m.push(_);return m},Ki=(u,f)=>1===u.nodeType?null===u.getAttribute("slot")&&""===f||u.getAttribute("slot")===f:u["s-sn"]===f||""===f,oo=(u,f,p,m)=>{if(u["s-ol"]&&u["s-ol"].isConnected)return;const _=document.createTextNode("");if(_["s-nr"]=u,!f["s-cr"]||!f["s-cr"].parentNode)return;const D=f["s-cr"].parentNode,N=Wn(D,p?"prepend":"appendChild");if(typeof m<"u"){_["s-oo"]=m;const E=Wn(D,"childNodes"),T=[_];E.forEach(w=>{w["s-nr"]&&T.push(w)}),T.sort((w,F)=>!w["s-oo"]||w["s-oo"]<(F["s-oo"]||0)?-1:!F["s-oo"]||F["s-oo"]<w["s-oo"]?1:0),T.forEach(w=>N.call(D,w))}else N.call(D,_);u["s-ol"]=_,u["s-sh"]=f["s-hn"]},br=u=>"string"==typeof u["s-sn"]?u["s-sn"]:1===u.nodeType&&u.getAttribute("slot")||void 0;function Vu(u){if(u.assignedElements||u.assignedNodes||!u["s-sr"])return;const f=p=>function(m){const _=[],D=this["s-sn"];m?.flatten&&console.error("\n          Flattening is not supported for Stencil non-shadow slots. \n          You can use `.childNodes` to nested slot fallback content.\n          If you have a particular use case, please open an issue on the Stencil repo.\n        ");const N=this["s-cr"].parentElement;return(N.__childNodes?N.childNodes:_r(N.childNodes)).forEach(T=>{D===br(T)&&_.push(T)}),p?_.filter(T=>1===T.nodeType):_}.bind(u);u.assignedElements=f(!0),u.assignedNodes=f(!1)}function S(u){u.dispatchEvent(new CustomEvent("slotchange",{bubbles:!1,cancelable:!1,composed:!1}))}function xe(u,f){var p;if(!(f=f||(null==(p=u["s-ol"])?void 0:p.parentElement)))return{slotNode:null,slotName:""};const m=u["s-sn"]=br(u)||"";return{slotNode:Dr(Wn(f,"childNodes"),f.tagName,m)[0],slotName:m}}var Qe=u=>{const f=u.cloneNode;u.cloneNode=function(p){const _=this.shadowRoot&&true,D=f.call(this,!!_&&p);if(!_&&p){let E,T,N=0;const w=["s-id","s-cr","s-lr","s-rc","s-sc","s-p","s-cn","s-sr","s-sn","s-hn","s-ol","s-nr","s-si","s-rf","s-scs"],F=this.__childNodes||this.childNodes;for(;N<F.length;N++)E=F[N]["s-nr"],T=w.every(fe=>!F[N][fe]),E&&(D.__appendChild?D.__appendChild(E.cloneNode(!0)):D.appendChild(E.cloneNode(!0))),T&&D.appendChild(F[N].cloneNode(!0))}return D}},On=u=>{u.__appendChild=u.appendChild,u.appendChild=function(f){const{slotName:p,slotNode:m}=xe(f,this);if(m){oo(f,m);const _=Nr(m,p),D=_[_.length-1],N=Wn(D,"parentNode"),E=Wn(N,"insertBefore")(f,D.nextSibling);return S(m),Gn(this),E}return this.__appendChild(f)}},At=u=>{u.__removeChild=u.removeChild,u.removeChild=function(f){return f&&typeof f["s-sn"]<"u"&&Dr(this.__childNodes||this.childNodes,this.tagName,f["s-sn"])&&f.isConnected?(f.remove(),void Gn(this)):this.__removeChild(f)}},Pr=u=>{u.__prepend=u.prepend,u.prepend=function(...f){f.forEach(p=>{"string"==typeof p&&(p=this.ownerDocument.createTextNode(p));const m=(p["s-sn"]=br(p))||"",D=Dr(Wn(this,"childNodes"),this.tagName,m)[0];if(D){oo(p,D,!0);const E=Nr(D,m)[0],T=Wn(E,"parentNode"),w=Wn(T,"insertBefore")(p,Wn(E,"nextSibling"));return S(D),w}return 1===p.nodeType&&p.getAttribute("slot")&&(p.hidden=!0),u.__prepend(p)})}},oD=u=>{u.__append=u.append,u.append=function(...f){f.forEach(p=>{"string"==typeof p&&(p=this.ownerDocument.createTextNode(p)),this.appendChild(p)})}},Zh=u=>{const f=u.insertAdjacentHTML;u.insertAdjacentHTML=function(p,m){if("afterbegin"!==p&&"beforeend"!==p)return f.call(this,p,m);const _=this.ownerDocument.createElement("_");let D;if(_.innerHTML=m,"afterbegin"===p)for(;D=_.firstChild;)this.prepend(D);else if("beforeend"===p)for(;D=_.firstChild;)this.append(D)}},kt=u=>{u.insertAdjacentText=function(f,p){this.insertAdjacentHTML(f,p)}},et=u=>{u.__insertBefore||(u.__insertBefore=u.insertBefore,u.insertBefore=function(p,m){const{slotName:_,slotNode:D}=xe(p,this),N=this.__childNodes?this.childNodes:_r(this.childNodes);if(D){let T=!1;if(N.forEach(w=>{if(w!==m&&null!==m);else{if(T=!0,null===m||_!==m["s-sn"])return void this.appendChild(p);if(_===m["s-sn"]){oo(p,D);const F=Wn(m,"parentNode");Wn(F,"insertBefore")(p,m),S(D)}}}),T)return p}const E=m?.__parentNode;return E&&!this.isSameNode(E)?this.appendChild(p):this.__insertBefore(p,m)})},na=u=>{const f=u.insertAdjacentElement;u.insertAdjacentElement=function(p,m){return"afterbegin"!==p&&"beforeend"!==p?f.call(this,p,m):"afterbegin"===p?(this.prepend(m),m):("beforeend"===p&&this.append(m),m)}},nr=u=>{io("textContent",u),Object.defineProperty(u,"textContent",{get:function(){let f="";return(this.__childNodes?this.childNodes:_r(this.childNodes)).forEach(m=>f+=m.textContent||""),f},set:function(f){(this.__childNodes?this.childNodes:_r(this.childNodes)).forEach(m=>{m["s-ol"]&&m["s-ol"].remove(),m.remove()}),this.insertAdjacentHTML("beforeend",f)}})},iD=u=>{class f extends Array{item(m){return this[m]}}io("children",u),Object.defineProperty(u,"children",{get(){return this.childNodes.filter(p=>1===p.nodeType)}}),Object.defineProperty(u,"childElementCount",{get(){return this.children.length}}),io("firstChild",u),Object.defineProperty(u,"firstChild",{get(){return this.childNodes[0]}}),io("lastChild",u),Object.defineProperty(u,"lastChild",{get(){return this.childNodes[this.childNodes.length-1]}}),io("childNodes",u),Object.defineProperty(u,"childNodes",{get(){const p=new f;return p.push(..._r(this.__childNodes)),p}})},Kh=u=>{!u||void 0!==u.__nextSibling||!globalThis.Node||(sD(u),aD(u),Qh(u),u.nodeType===Node.ELEMENT_NODE&&(wo(u),lD(u)))},sD=u=>{!u||u.__nextSibling||(io("nextSibling",u),Object.defineProperty(u,"nextSibling",{get:function(){var f;const p=null==(f=this["s-ol"])?void 0:f.parentNode.childNodes,m=p?.indexOf(this);return p&&m>-1?p[m+1]:this.__nextSibling}}))},wo=u=>{!u||u.__nextElementSibling||(io("nextElementSibling",u),Object.defineProperty(u,"nextElementSibling",{get:function(){var f;const p=null==(f=this["s-ol"])?void 0:f.parentNode.children,m=p?.indexOf(this);return p&&m>-1?p[m+1]:this.__nextElementSibling}}))},aD=u=>{!u||u.__previousSibling||(io("previousSibling",u),Object.defineProperty(u,"previousSibling",{get:function(){var f;const p=null==(f=this["s-ol"])?void 0:f.parentNode.childNodes,m=p?.indexOf(this);return p&&m>-1?p[m-1]:this.__previousSibling}}))},lD=u=>{!u||u.__previousElementSibling||(io("previousElementSibling",u),Object.defineProperty(u,"previousElementSibling",{get:function(){var f;const p=null==(f=this["s-ol"])?void 0:f.parentNode.children,m=p?.indexOf(this);return p&&m>-1?p[m-1]:this.__previousElementSibling}}))},Qh=u=>{!u||u.__parentNode||(io("parentNode",u),Object.defineProperty(u,"parentNode",{get:function(){var f;return(null==(f=this["s-ol"])?void 0:f.parentNode)||this.__parentNode},set:function(f){this.__parentNode=f}}))},cD=["children","nextElementSibling","previousElementSibling"],Jh=["childNodes","firstChild","lastChild","nextSibling","previousSibling","textContent","parentNode"];function io(u,f){let p;cD.includes(u)?p=Object.getOwnPropertyDescriptor(Element.prototype,u):Jh.includes(u)&&(p=Object.getOwnPropertyDescriptor(Node.prototype,u)),p||(p=Object.getOwnPropertyDescriptor(f,u)),p&&Object.defineProperty(f,"__"+u,p)}function Wn(u,f){if("__"+f in u){const p=u["__"+f];return"function"!=typeof p?p:p.bind(u)}return"function"!=typeof u[f]?u[f]:u[f].bind(u)}var Xe=(u,f,...p)=>{let m=null,_=null,D=null,N=!1,E=!1;const T=[],w=fe=>{for(let ue=0;ue<fe.length;ue++)m=fe[ue],Array.isArray(m)?w(m):null!=m&&"boolean"!=typeof m&&((N="function"!=typeof u&&!en(m))&&(m=String(m)),N&&E?T[T.length-1].$text$+=m:T.push(N?Qi(null,m):m),E=N)};if(w(p),f){f.key&&(_=f.key),f.name&&(D=f.name);{const fe=f.className||f.class;fe&&(f.class="object"!=typeof fe?fe:Object.keys(fe).filter(ue=>fe[ue]).join(" "))}}if("function"==typeof u)return u(null===f?{}:f,T,dD);const F=Qi(u,null);return F.$attrs$=f,T.length>0&&(F.$children$=T),F.$key$=_,F.$name$=D,F},Qi=(u,f)=>({$flags$:0,$tag$:u,$text$:f,$elm$:null,$children$:null,$attrs$:null,$key$:null,$name$:null}),ep={},dD={forEach:(u,f)=>u.map(tp).forEach(f),map:(u,f)=>u.map(tp).map(f).map(Me)},tp=u=>({vattrs:u.$attrs$,vchildren:u.$children$,vkey:u.$key$,vname:u.$name$,vtag:u.$tag$,vtext:u.$text$}),Me=u=>{if("function"==typeof u.vtag){const p={...u.vattrs};return u.vkey&&(p.key=u.vkey),u.vname&&(p.name=u.vname),Xe(u.vtag,p,...u.vchildren||[])}const f=Qi(u.vtag,u.vtext);return f.$attrs$=u.vattrs,f.$children$=u.vchildren,f.$key$=u.vkey,f.$name$=u.vname,f},Zr=(u,f,p,m,_,D,N,E=[])=>{let T,w,F,fe;const ue=_["s-sc"];if(1===D.nodeType){if(T=D.getAttribute("c-id"),T&&(w=T.split("."),w[0]===N||"0"===w[0])){F=Bu({$flags$:0,$hostId$:w[0],$nodeId$:w[1],$depth$:w[2],$index$:w[3],$tag$:D.tagName.toLowerCase(),$elm$:D,$attrs$:{class:D.className||""}}),f.push(F),D.removeAttribute("c-id"),u.$children$||(u.$children$=[]),ue&&(D["s-si"]=ue,F.$attrs$.class+=" "+ue);const Se=F.$elm$.getAttribute("s-sn");"string"==typeof Se&&("slot-fb"===F.$tag$&&(Rl(Se,w[2],F,D,u,f,p,m,E),ue&&D.classList.add(ue)),F.$elm$["s-sn"]=Se,F.$elm$.removeAttribute("s-sn")),void 0!==F.$index$&&(u.$children$[F.$index$]=F),u=F,m&&"0"===F.$depth$&&(m[F.$index$]=F.$elm$)}if(D.shadowRoot)for(fe=D.shadowRoot.childNodes.length-1;fe>=0;fe--)Zr(u,f,p,m,_,D.shadowRoot.childNodes[fe],N,E);const Ve=D.__childNodes||D.childNodes;for(fe=Ve.length-1;fe>=0;fe--)Zr(u,f,p,m,_,Ve[fe],N,E)}else if(8===D.nodeType)w=D.nodeValue.split("."),(w[1]===N||"0"===w[1])&&(T=w[0],F=Bu({$hostId$:w[1],$nodeId$:w[2],$depth$:w[3],$index$:w[4]||"0",$elm$:D,$attrs$:null,$children$:null,$key$:null,$name$:null,$tag$:null,$text$:null}),"t"===T?(F.$elm$=Ji(D,3),F.$elm$&&3===F.$elm$.nodeType&&(F.$text$=F.$elm$.textContent,f.push(F),D.remove(),N===F.$hostId$&&(u.$children$||(u.$children$=[]),u.$children$[F.$index$]=F),m&&"0"===F.$depth$&&(m[F.$index$]=F.$elm$))):"c"===T?(F.$elm$=Ji(D,8),F.$elm$&&8===F.$elm$.nodeType&&(f.push(F),D.remove())):F.$hostId$===N&&("s"===T?Rl(D["s-sn"]=w[5]||"",w[2],F,D,u,f,p,m,E):"r"===T&&(m?D.remove():(_["s-cr"]=D,D["s-cn"]=!0))));else if(u&&"style"===u.$tag$){const Ve=Qi(null,D.textContent);Ve.$elm$=D,Ve.$index$="0",u.$children$=[Ve]}else 3===D.nodeType&&!D.wholeText.trim()&&D.remove();return u},gi=(u,f)=>{if(1===u.nodeType){const p=u[tr]||u.getAttribute(tr);p&&f.set(p,u);let m=0;if(u.shadowRoot)for(;m<u.shadowRoot.childNodes.length;m++)gi(u.shadowRoot.childNodes[m],f);const _=u.__childNodes||u.childNodes;for(m=0;m<_.length;m++)gi(_[m],f)}else if(8===u.nodeType){const p=u.nodeValue.split(".");"o"===p[0]&&(f.set(p[1]+"."+p[2],u),u.nodeValue="",u["s-en"]=p[3])}},Bu=u=>({$flags$:0,$hostId$:null,$nodeId$:null,$depth$:null,$index$:"0",$elm$:null,$attrs$:null,$children$:null,$key$:null,$name$:null,$tag$:null,$text$:null,...u});function Rl(u,f,p,m,_,D,N,E,T){m["s-sr"]=!0,p.$name$=u||null,p.$tag$="slot";const w=_?.$elm$?_.$elm$["s-id"]||_.$elm$.getAttribute("s-id"):"";if(E&&ht.document){const F=p.$elm$=ht.document.createElement(p.$tag$);p.$name$&&p.$elm$.setAttribute("name",u),w&&w!==p.$hostId$?_.$elm$.insertBefore(F,_.$elm$.children[0]):m.parentNode.insertBefore(p.$elm$,m),ju(T,f,u,m,p.$hostId$),m.remove(),"0"===p.$depth$&&(E[p.$index$]=p.$elm$)}else{const F=p.$elm$,fe=w&&w!==p.$hostId$&&_.$elm$.shadowRoot;ju(T,f,u,m,fe?w:p.$hostId$),Vu(m),fe&&_.$elm$.insertBefore(F,_.$elm$.children[0]),D.push(p)}N.push(p),_.$children$||(_.$children$=[]),_.$children$[p.$index$]=p}var ju=(u,f,p,m,_)=>{let D=m.nextSibling;for(u[f]=u[f]||[];D&&((D.getAttribute&&D.getAttribute("slot")||D["s-sn"])===p||""===p&&!D["s-sn"]&&(8===D.nodeType&&1!==D.nodeValue.indexOf(".")||3===D.nodeType));)D["s-sn"]=p,u[f].push({slot:m,node:D,hostId:_}),D=D.nextSibling},Ji=(u,f)=>{let p=u;do{p=p.nextSibling}while(p&&(p.nodeType!==f||!p.nodeValue));return p},mi=u=>{const f=(u=>u.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))(u);return new RegExp(`(^|[^@]|@(?!supports\\s+selector\\s*\\([^{]*?${f}))(${f}\\b)`,"g")};mi("::slotted"),mi(":host"),mi(":host-context");var Uu=u=>Pt.push(u),np=u=>Ze(u).$modeName$,ce=(u,f)=>{if("string"==typeof u&&(u.startsWith("{")&&u.endsWith("}")||u.startsWith("[")&&u.endsWith("]")))try{return JSON.parse(u)}catch{}return"string"==typeof u&&u.startsWith(yn)?u=function Ol(u){return"string"==typeof u&&u.startsWith(yn)?xl.fromLocalValue(JSON.parse(atob(u.slice(11)))):u}(u):null==u||en(u)?u:4&f?"false"!==u&&(""===u||!!u):2&f?"string"==typeof u?parseFloat(u):"number"==typeof u?u:NaN:1&f?String(u):u},ra=u=>Ze(u).$hostElement$,Pl=(u,f,p)=>{const m=ra(u);return{emit:_=>kl(m,f,{bubbles:!!(4&p),composed:!!(2&p),cancelable:!!(1&p),detail:_})}},kl=(u,f,p)=>{const m=it.ce(f,p);return u.dispatchEvent(m),m},Io=new WeakMap,Hu=(u,f,p)=>{let m=Ot.get(u);Xi&&p?(m=m||new CSSStyleSheet,"string"==typeof m?m=f:m.replaceSync(f)):m=f,Ot.set(u,m)},oa=(u,f,p)=>{var m;const _=zu(f,p),D=Ot.get(_);if(!ht.document)return _;if(u=11===u.nodeType?u:ht.document,D)if("string"==typeof D){let E,N=Io.get(u=u.head||u);if(N||Io.set(u,N=new Set),!N.has(_)){if(u.host&&(E=u.querySelector(`[${Xr}="${_}"]`)))E.innerHTML=D;else{E=document.querySelector(`[${Xr}="${_}"]`)||ht.document.createElement("style"),E.innerHTML=D;const T=null!=(m=it.$nonce$)?m:Zo(ht.document);if(null!=T&&E.setAttribute("nonce",T),!(1&f.$flags$))if("HEAD"===u.nodeName){const w=u.querySelectorAll("link[rel=preconnect]"),F=w.length>0?w[w.length-1].nextSibling:u.querySelector("style");u.insertBefore(E,F?.parentNode===u?F:null)}else if("host"in u)if(Xi){const w=new CSSStyleSheet;w.replaceSync(D),u.adoptedStyleSheets=[w,...u.adoptedStyleSheets]}else{const w=u.querySelector("style");w?w.innerHTML=D+w.innerHTML:u.prepend(E)}else u.append(E);1&f.$flags$&&u.insertBefore(E,null)}4&f.$flags$&&(E.innerHTML+=Xs),N&&N.add(_)}}else u.adoptedStyleSheets.includes(D)||(u.adoptedStyleSheets=[...u.adoptedStyleSheets,D]);return _},zu=(u,f)=>"sc-"+(f&&32&u.$flags$?u.$tagName$+"-"+f:u.$tagName$),pD=u=>u.replace(/\/\*!@([^\/]+)\*\/[^\{]+\{/g,"$1{"),ia=(u,f,p,m,_,D,N)=>{if(p===m)return;let E=H(u,f),T=f.toLowerCase();if("class"===f){const w=u.classList,F=sa(p);let fe=sa(m);u["s-si"]&&N?(fe.push(u["s-si"]),F.forEach(ue=>{ue.startsWith(u["s-si"])&&fe.push(ue)}),fe=[...new Set(fe)],w.add(...fe)):(w.remove(...F.filter(ue=>ue&&!fe.includes(ue))),w.add(...fe.filter(ue=>ue&&!F.includes(ue))))}else if("style"===f){for(const w in p)(!m||null==m[w])&&(w.includes("-")?u.style.removeProperty(w):u.style[w]="");for(const w in m)(!p||m[w]!==p[w])&&(w.includes("-")?u.style.setProperty(w,m[w]):u.style[w]=m[w])}else if("key"!==f)if("ref"===f)m&&m(u);else if(E||"o"!==f[0]||"n"!==f[1]){const w=en(m);if((E||w&&null!==m)&&!_)try{if(u.tagName.includes("-"))u[f]!==m&&(u[f]=m);else{const fe=m??"";"list"===f?E=!1:(null==p||u[f]!=fe)&&("function"==typeof u.__lookupSetter__(f)?u[f]=fe:u.setAttribute(f,fe))}}catch{}let F=!1;T!==(T=T.replace(/^xlink\:?/,""))&&(f=T,F=!0),null==m||!1===m?(!1!==m||""===u.getAttribute(f))&&(F?u.removeAttributeNS(Rr,f):u.removeAttribute(f)):(!E||4&D||_)&&!w&&1===u.nodeType&&(m=!0===m?"":m,F?u.setAttributeNS(Rr,f,m):u.setAttribute(f,m))}else if(f="-"===f[2]?f.slice(3):H(ht,T)?T.slice(2):T[2]+f.slice(3),p||m){const w=f.endsWith(aa);f=f.replace(Gu,""),p&&it.rel(u,f,p,w),m&&it.ael(u,f,m,w)}},es=/\s/,sa=u=>("object"==typeof u&&u&&"baseVal"in u&&(u=u.baseVal),u&&"string"==typeof u?u.split(es):[]),aa="Capture",Gu=new RegExp(aa+"$"),kr=(u,f,p,m)=>{const _=11===f.$elm$.nodeType&&f.$elm$.host?f.$elm$.host:f.$elm$,D=u&&u.$attrs$||{},N=f.$attrs$||{};for(const E of vi(Object.keys(D)))E in N||ia(_,E,D[E],void 0,p,f.$flags$,m);for(const E of vi(Object.keys(N)))ia(_,E,D[E],N[E],p,f.$flags$,m)};function vi(u){return u.includes("ref")?[...u.filter(f=>"ref"!==f),"ref"]:u}var ts,tt,yt,Fl=!1,la=!1,ca=!1,or=!1,ua=(u,f,p)=>{var m;const _=f.$children$[p];let N,E,T,D=0;if(Fl||(ca=!0,"slot"===_.$tag$&&(_.$flags$|=_.$children$?2:1)),null!==_.$text$)N=_.$elm$=ht.document.createTextNode(_.$text$);else if(1&_.$flags$)N=_.$elm$=ht.document.createTextNode(""),kr(null,_,or);else{if(or||(or="svg"===_.$tag$),!ht.document)throw new Error("You are trying to render a Stencil component in an environment that doesn't support the DOM. Make sure to populate the [`window`](https://developer.mozilla.org/en-US/docs/Web/API/Window/window) object before rendering a component.");if(N=_.$elm$=ht.document.createElementNS(or?"http://www.w3.org/2000/svg":"http://www.w3.org/1999/xhtml",!Fl&&2&_.$flags$?"slot-fb":_.$tag$),or&&"foreignObject"===_.$tag$&&(or=!1),kr(null,_,or),(u=>null!=u&&void 0!==u)(ts)&&N["s-si"]!==ts&&N.classList.add(N["s-si"]=ts),_.$children$)for(D=0;D<_.$children$.length;++D)E=ua(u,_,D),E&&N.appendChild(E);"svg"===_.$tag$?or=!1:"foreignObject"===N.tagName&&(or=!0)}return N["s-hn"]=yt,3&_.$flags$&&(N["s-sr"]=!0,N["s-cr"]=tt,N["s-sn"]=_.$name$||"",N["s-rf"]=null==(m=_.$attrs$)?void 0:m.ref,Vu(N),T=u&&u.$children$&&u.$children$[p],T&&T.$tag$===_.$tag$&&u.$elm$&&da(u.$elm$),Wu(tt,N,f.$elm$,u?.$elm$)),N},da=u=>{it.$flags$|=1;const f=u.closest(yt.toLowerCase());if(null!=f){const p=Array.from(f.__childNodes||f.childNodes).find(_=>_["s-cr"]),m=Array.from(u.__childNodes||u.childNodes);for(const _ of p?m.reverse():m)null!=_["s-sh"]&&(ao(f,_,p??null),_["s-sh"]=void 0,ca=!0)}it.$flags$&=-2},ut=(u,f)=>{it.$flags$|=1;const p=Array.from(u.__childNodes||u.childNodes);if(u["s-sr"]){let m=u;for(;m=m.nextSibling;)m&&m["s-sn"]===u["s-sn"]&&m["s-sh"]===yt&&p.push(m)}for(let m=p.length-1;m>=0;m--){const _=p[m];_["s-hn"]!==yt&&_["s-ol"]&&(ao(ha(_).parentNode,_,ha(_)),_["s-ol"].remove(),_["s-ol"]=void 0,_["s-sh"]=void 0,ca=!0),f&&ut(_,f)}it.$flags$&=-2},fa=(u,f,p,m,_,D)=>{let E,N=u["s-cr"]&&u["s-cr"].parentNode||u;for(N.shadowRoot&&N.tagName===yt&&(N=N.shadowRoot);_<=D;++_)m[_]&&(E=ua(null,p,_),E&&(m[_].$elm$=E,ao(N,E,ha(f))))},Ll=(u,f,p)=>{for(let m=f;m<=p;++m){const _=u[m];if(_){const D=_.$elm$;os(_),D&&(la=!0,D["s-ol"]?D["s-ol"].remove():ut(D,!0),D.remove())}}},ns=(u,f,p=!1)=>u.$tag$===f.$tag$&&("slot"===u.$tag$?u.$name$===f.$name$:p?(p&&!u.$key$&&f.$key$&&(u.$key$=f.$key$),!0):u.$key$===f.$key$),ha=u=>u&&u["s-ol"]||u,rs=(u,f,p=!1)=>{const m=f.$elm$=u.$elm$,_=u.$children$,D=f.$children$,N=f.$tag$,E=f.$text$;let T;null===E?(or="svg"===N||"foreignObject"!==N&&or,"slot"===N&&!Fl&&u.$name$!==f.$name$&&(f.$elm$["s-sn"]=f.$name$||"",da(f.$elm$.parentElement)),kr(u,f,or,p),null!==_&&null!==D?((u,f,p,m,_=!1)=>{let _t,Et,D=0,N=0,E=0,T=0,w=f.length-1,F=f[0],fe=f[w],ue=m.length-1,Ve=m[0],Se=m[ue];for(;D<=w&&N<=ue;)if(null==F)F=f[++D];else if(null==fe)fe=f[--w];else if(null==Ve)Ve=m[++N];else if(null==Se)Se=m[--ue];else if(ns(F,Ve,_))rs(F,Ve,_),F=f[++D],Ve=m[++N];else if(ns(fe,Se,_))rs(fe,Se,_),fe=f[--w],Se=m[--ue];else if(ns(F,Se,_))("slot"===F.$tag$||"slot"===Se.$tag$)&&ut(F.$elm$.parentNode,!1),rs(F,Se,_),ao(u,F.$elm$,fe.$elm$.nextSibling),F=f[++D],Se=m[--ue];else if(ns(fe,Ve,_))("slot"===F.$tag$||"slot"===Se.$tag$)&&ut(fe.$elm$.parentNode,!1),rs(fe,Ve,_),ao(u,fe.$elm$,F.$elm$),fe=f[--w],Ve=m[++N];else{for(E=-1,T=D;T<=w;++T)if(f[T]&&null!==f[T].$key$&&f[T].$key$===Ve.$key$){E=T;break}E>=0?(Et=f[E],Et.$tag$!==Ve.$tag$?_t=ua(f&&f[N],p,E):(rs(Et,Ve,_),f[E]=void 0,_t=Et.$elm$),Ve=m[++N]):(_t=ua(f&&f[N],p,N),Ve=m[++N]),_t&&ao(ha(F.$elm$).parentNode,_t,ha(F.$elm$))}D>w?fa(u,null==m[ue+1]?null:m[ue+1].$elm$,p,m,N,ue):N>ue&&Ll(f,D,w)})(m,_,f,D,p):null!==D?(null!==u.$text$&&(m.textContent=""),fa(m,null,f,D,0,D.length-1)):!p&&null!==_&&Ll(_,0,_.length-1),or&&"svg"===N&&(or=!1)):(T=m["s-cr"])?T.parentNode.textContent=E:u.$text$!==E&&(m.data=E)},ir=[],pa=u=>{let f,p,m;const _=u.__childNodes||u.childNodes;for(const D of _){if(D["s-sr"]&&(f=D["s-cr"])&&f.parentNode){p=f.parentNode.__childNodes||f.parentNode.childNodes;const N=D["s-sn"];for(m=p.length-1;m>=0;m--)if(f=p[m],!(f["s-cn"]||f["s-nr"]||f["s-hn"]===D["s-hn"]||f["s-sh"]&&f["s-sh"]===D["s-hn"]))if(Ki(f,N)){let E=ir.find(T=>T.$nodeToRelocate$===f);la=!0,f["s-sn"]=f["s-sn"]||N,E?(E.$nodeToRelocate$["s-sh"]=D["s-hn"],E.$slotRefNode$=D):(f["s-sh"]=D["s-hn"],ir.push({$slotRefNode$:D,$nodeToRelocate$:f})),f["s-sr"]&&ir.map(T=>{Ki(T.$nodeToRelocate$,f["s-sn"])&&(E=ir.find(w=>w.$nodeToRelocate$===f),E&&!T.$slotRefNode$&&(T.$slotRefNode$=E.$slotRefNode$))})}else ir.some(E=>E.$nodeToRelocate$===f)||ir.push({$nodeToRelocate$:f})}1===D.nodeType&&pa(D)}},os=u=>{u.$attrs$&&u.$attrs$.ref&&u.$attrs$.ref(null),u.$children$&&u.$children$.map(os)},ao=(u,f,p)=>{if("string"==typeof f["s-sn"]&&f["s-sr"]&&f["s-cr"])Wu(f["s-cr"],f,u,f.parentElement);else if("string"==typeof f["s-sn"]){11!==u.getRootNode().nodeType&&Qh(f),u.insertBefore(f,p);const{slotNode:m}=xe(f);return m&&S(m),f}return u.__insertBefore?u.__insertBefore(f,p):u?.insertBefore(f,p)};function Wu(u,f,p,m){var _,D;let N;if(u&&"string"==typeof f["s-sn"]&&f["s-sr"]&&u.parentNode&&u.parentNode["s-sc"]&&(N=f["s-si"]||u.parentNode["s-sc"])){const E=f["s-sn"],T=f["s-hn"];if(null==(_=p.classList)||_.add(N+"-s"),m&&null!=(D=m.classList)&&D.contains(N+"-s")){let w=(m.__childNodes||m.childNodes)[0],F=!1;for(;w;){if(w["s-sn"]!==E&&w["s-hn"]===T&&w["s-sr"]){F=!0;break}w=w.nextSibling}F||m.classList.remove(N+"-s")}}}var $l=(u,f)=>{if(f&&!u.$onRenderResolve$&&f["s-p"]){const p=f["s-p"].push(new Promise(m=>u.$onRenderResolve$=()=>{f["s-p"].splice(p-1,1),m()}))}},lo=(u,f)=>{if(u.$flags$|=16,!(4&u.$flags$))return $l(u,u.$ancestorComponent$),wn(()=>op(u,f));u.$flags$|=512},op=(u,f)=>{const p=u.$hostElement$,_=u.$lazyInstance$;if(!_)throw new Error(`Can't render component <${p.tagName.toLowerCase()} /> with invalid Stencil runtime! Make sure this imported component is compiled with a \`externalRuntime: true\` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime`);let D;return f?(u.$flags$|=256,u.$queuedListeners$&&(u.$queuedListeners$.map(([N,E])=>sr(_,N,E,p)),u.$queuedListeners$=void 0),D=sr(_,"componentWillLoad",void 0,p)):D=sr(_,"componentWillUpdate",void 0,p),D=co(D,()=>sr(_,"componentWillRender",void 0,p)),co(D,()=>Pe(u,_,f))},co=(u,f)=>ip(u)?u.then(f).catch(p=>{console.error(p),f()}):f(),ip=u=>u instanceof Promise||u&&u.then&&"function"==typeof u.then,Pe=function(){var u=(0,oe.A)(function*(f,p,m){var _;const D=f.$hostElement$,E=D["s-rc"];m&&(u=>{const f=u.$cmpMeta$,p=u.$hostElement$,m=f.$flags$,D=oa(p.shadowRoot?p.shadowRoot:p.getRootNode(),f,u.$modeName$);10&m&&(p["s-sc"]=D,p.classList.add(D+"-h"))})(f);qu(f,p,D,m),E&&(E.map(w=>w()),D["s-rc"]=void 0);{const w=null!=(_=D["s-p"])?_:[],F=()=>U(f);0===w.length?F():(Promise.all(w).then(F),f.$flags$|=4,w.length=0)}});return function(p,m,_){return u.apply(this,arguments)}}(),qu=(u,f,p,m)=>{try{f=f.render&&f.render(),u.$flags$&=-17,u.$flags$|=2,((u,f,p=!1)=>{var m,_,D,N,E;const T=u.$hostElement$,w=u.$cmpMeta$,F=u.$vnode$||Qi(null,null),ue=(u=>u&&u.$tag$===ep)(f)?f:Xe(null,null,f);if(yt=T.tagName,w.$attrsToReflect$&&(ue.$attrs$=ue.$attrs$||{},w.$attrsToReflect$.map(([Ve,Se])=>ue.$attrs$[Se]=T[Ve])),p&&ue.$attrs$)for(const Ve of Object.keys(ue.$attrs$))T.hasAttribute(Ve)&&!["key","ref","style","class"].includes(Ve)&&(ue.$attrs$[Ve]=T[Ve]);if(ue.$tag$=null,ue.$flags$|=4,u.$vnode$=ue,ue.$elm$=F.$elm$=T.shadowRoot||T,ts=T["s-sc"],Fl=!(!(1&w.$flags$)||128&w.$flags$),tt=T["s-cr"],la=!1,rs(F,ue,p),it.$flags$|=1,ca){pa(ue.$elm$);for(const Ve of ir){const Se=Ve.$nodeToRelocate$;if(!Se["s-ol"]&&ht.document){const _t=ht.document.createTextNode("");_t["s-nr"]=Se,ao(Se.parentNode,Se["s-ol"]=_t,Se)}}for(const Ve of ir){const Se=Ve.$nodeToRelocate$,_t=Ve.$slotRefNode$;if(_t){const Et=_t.parentNode;let wt=_t.nextSibling;if(wt&&1===wt.nodeType){let Lt=null==(m=Se["s-ol"])?void 0:m.previousSibling;for(;Lt;){let $t=null!=(_=Lt["s-nr"])?_:null;if($t&&$t["s-sn"]===Se["s-sn"]&&Et===($t.__parentNode||$t.parentNode)){for($t=$t.nextSibling;$t===Se||$t?.["s-sr"];)$t=$t?.nextSibling;if(!$t||!$t["s-nr"]){wt=$t;break}}Lt=Lt.previousSibling}}(!wt&&Et!==(Se.__parentNode||Se.parentNode)||(Se.__nextSibling||Se.nextSibling)!==wt)&&Se!==wt&&(ao(Et,Se,wt),1===Se.nodeType&&"SLOT-FB"!==Se.tagName&&(Se.hidden=null!=(D=Se["s-ih"])&&D)),Se&&"function"==typeof _t["s-rf"]&&_t["s-rf"](_t)}else 1===Se.nodeType&&(p&&(Se["s-ih"]=null!=(N=Se.hidden)&&N),Se.hidden=!0)}}if(la&&Gn(ue.$elm$),it.$flags$&=-2,ir.length=0,2&w.$flags$){const Ve=ue.$elm$.__childNodes||ue.$elm$.childNodes;for(const Se of Ve)Se["s-hn"]!==yt&&!Se["s-sh"]&&(p&&null==Se["s-ih"]&&(Se["s-ih"]=null!=(E=Se.hidden)&&E),Se.hidden=!0)}tt=void 0})(u,f,m)}catch(_){Ie(_,u.$hostElement$)}return null},U=u=>{const p=u.$hostElement$,_=u.$lazyInstance$,D=u.$ancestorComponent$;sr(_,"componentDidRender",void 0,p),64&u.$flags$?sr(_,"componentDidUpdate",void 0,p):(u.$flags$|=64,sp(p),sr(_,"componentDidLoad",void 0,p),u.$onReadyResolve$(p),D||ma()),u.$onInstanceResolve$(p),u.$onRenderResolve$&&(u.$onRenderResolve$(),u.$onRenderResolve$=void 0),512&u.$flags$&&jt(()=>lo(u,!1)),u.$flags$&=-517},ga=u=>{{const f=Ze(u),p=f.$hostElement$.isConnected;return p&&2==(18&f.$flags$)&&lo(f,!1),p}},ma=u=>{jt(()=>kl(ht,"appload",{detail:{namespace:"ionic"}}))},sr=(u,f,p,m)=>{if(u&&u[f])try{return u[f](p)}catch(_){Ie(_,m)}},sp=u=>u.classList.add("hydrated"),Vl=(u,f,p,m)=>{const _=Ze(u);if(!_)throw new Error(`Couldn't find host element for "${m.$tagName$}" as it is unknown to this Stencil runtime. This usually happens when integrating a 3rd party Stencil component with another Stencil component or application. Please reach out to the maintainers of the 3rd party Stencil component or report this on the Stencil Discord server (https://chat.stenciljs.com) or comment on this similar [GitHub issue](https://github.com/stenciljs/core/issues/5457).`);const D=_.$hostElement$,N=_.$instanceValues$.get(f),E=_.$flags$,T=_.$lazyInstance$;p=ce(p,m.$members$[f][0]);const w=Number.isNaN(N)&&Number.isNaN(p);if((!(8&E)||void 0===N)&&p!==N&&!w&&(_.$instanceValues$.set(f,p),T)){if(m.$watchers$&&128&E){const fe=m.$watchers$[f];fe&&fe.map(ue=>{try{T[ue](p,N,f)}catch(Ve){Ie(Ve,D)}})}if(2==(18&E)){if(T.componentShouldUpdate&&!1===T.componentShouldUpdate(p,N,f))return;lo(_,!1)}}},Xu=(u,f,p)=>{var m,_;const D=u.prototype;if(f.$members$||f.$watchers$||u.watchers){u.watchers&&!f.$watchers$&&(f.$watchers$=u.watchers);const N=Object.entries(null!=(m=f.$members$)?m:{});if(N.map(([E,[T]])=>{if(31&T||2&p&&32&T){const{get:w,set:F}=Object.getOwnPropertyDescriptor(D,E)||{};w&&(f.$members$[E][0]|=2048),F&&(f.$members$[E][0]|=4096),(1&p||!w)&&Object.defineProperty(D,E,{get(){{if(!(2048&f.$members$[E][0]))return((u,f)=>Ze(this).$instanceValues$.get(f))(0,E);const fe=Ze(this),ue=fe?fe.$lazyInstance$:D;return ue?ue[E]:void 0}},configurable:!0,enumerable:!0}),Object.defineProperty(D,E,{set(fe){const ue=Ze(this);if(F){const Ve=32&T?this[E]:ue.$hostElement$[E];return typeof Ve>"u"&&ue.$instanceValues$.get(E)?fe=ue.$instanceValues$.get(E):!ue.$instanceValues$.get(E)&&Ve&&ue.$instanceValues$.set(E,Ve),F.apply(this,[ce(fe,T)]),void Vl(this,E,fe=32&T?this[E]:ue.$hostElement$[E],f)}{if(!(1&p&&4096&f.$members$[E][0]))return Vl(this,E,fe,f),void(1&p&&!ue.$lazyInstance$&&ue.$onReadyPromise$.then(()=>{4096&f.$members$[E][0]&&ue.$lazyInstance$[E]!==ue.$instanceValues$.get(E)&&(ue.$lazyInstance$[E]=fe)}));const Ve=()=>{const Se=ue.$lazyInstance$[E];!ue.$instanceValues$.get(E)&&Se&&ue.$instanceValues$.set(E,Se),ue.$lazyInstance$[E]=ce(fe,T),Vl(this,E,ue.$lazyInstance$[E],f)};ue.$lazyInstance$?Ve():ue.$onReadyPromise$.then(()=>Ve())}}})}else 1&p&&64&T&&Object.defineProperty(D,E,{value(...w){var F;const fe=Ze(this);return null==(F=fe?.$onInstancePromise$)?void 0:F.then(()=>{var ue;return null==(ue=fe.$lazyInstance$)?void 0:ue[E](...w)})}})}),1&p){const E=new Map;D.attributeChangedCallback=function(T,w,F){it.jmp(()=>{var fe;const ue=E.get(T);if(this.hasOwnProperty(ue))F=this[ue],delete this[ue];else{if(D.hasOwnProperty(ue)&&"number"==typeof this[ue]&&this[ue]==F)return;if(null==ue){const Se=Ze(this),_t=Se?.$flags$;if(_t&&!(8&_t)&&128&_t&&F!==w){const Et=Se.$lazyInstance$,wt=null==(fe=f.$watchers$)?void 0:fe[T];wt?.forEach(Er=>{null!=Et[Er]&&Et[Er].call(Et,F,w,T)})}return}}const Ve=Object.getOwnPropertyDescriptor(D,ue);(F=(null!==F||"boolean"!=typeof this[ue])&&F)!==this[ue]&&(!Ve.get||Ve.set)&&(this[ue]=F)})},u.observedAttributes=Array.from(new Set([...Object.keys(null!=(_=f.$watchers$)?_:{}),...N.filter(([T,w])=>15&w[0]).map(([T,w])=>{var F;const fe=w[1]||T;return E.set(fe,T),512&w[0]&&(null==(F=f.$attrsToReflect$)||F.push([T,fe])),fe})]))}}return u},Bl=function(){var u=(0,oe.A)(function*(f,p,m,_){let D;if(!(32&p.$flags$)){if(p.$flags$|=32,m.$lazyBundleId$){const w=((u,f)=>{const m=u.$tagName$.replace(/-/g,"_"),_=u.$lazyBundleId$;if(!_)return;const D=lt.get(_);return D?D[m]:Y(8996)(`./${_}.entry.js`).then(N=>(lt.set(_,N),N[m]),N=>{Ie(N,f.$hostElement$)})})(m,p);if(w&&"then"in w){const fe=()=>{};D=yield w,fe()}else D=w;if(!D)throw new Error(`Constructor for "${m.$tagName$}#${p.$modeName$}" was not found`);D.isProxied||(m.$watchers$=D.watchers,Xu(D,m,2),D.isProxied=!0);const F=()=>{};p.$flags$|=8;try{new D(p)}catch(fe){Ie(fe,f)}p.$flags$&=-9,p.$flags$|=128,F(),jl(p.$lazyInstance$,f)}else D=f.constructor,customElements.whenDefined(f.localName).then(()=>p.$flags$|=128);if(D&&D.style){let w;"string"==typeof D.style?w=D.style:"string"!=typeof D.style&&(p.$modeName$=(u=>Pt.map(f=>f(u)).find(f=>!!f))(f),p.$modeName$&&(w=D.style[p.$modeName$]));const F=zu(m,p.$modeName$);if(!Ot.has(F)){const fe=()=>{};Hu(F,w,!!(1&m.$flags$)),fe()}}}const N=p.$ancestorComponent$,E=()=>lo(p,!0);N&&N["s-rc"]?N["s-rc"].push(E):E()});return function(p,m,_,D){return u.apply(this,arguments)}}(),jl=(u,f)=>{sr(u,"connectedCallback",void 0,f)},lp=u=>{if(!ht.document)return;const f=u["s-cr"]=ht.document.createComment("");f["s-cn"]=!0,ao(u,f,u.firstChild)},So=(u,f)=>{sr(u,"disconnectedCallback",void 0,f||u)},cp=function(){var u=(0,oe.A)(function*(f){if(!(1&it.$flags$)){const p=Ze(f);p.$rmListeners$&&(p.$rmListeners$.map(m=>m()),p.$rmListeners$=void 0),p?.$lazyInstance$?So(p.$lazyInstance$,f):p?.$onReadyPromise$&&p.$onReadyPromise$.then(()=>So(p.$lazyInstance$,f))}Io.has(f)&&Io.delete(f),f.shadowRoot&&Io.has(f.shadowRoot)&&Io.delete(f.shadowRoot)});return function(p){return u.apply(this,arguments)}}(),Xn=(u,f={})=>{var p;if(!ht.document)return void console.warn("Stencil: No document found. Skipping bootstrapping lazy components.");const _=[],D=f.exclude||[],N=ht.customElements,E=ht.document.head,T=E.querySelector("meta[charset]"),w=ht.document.createElement("style"),F=[];let fe,ue=!0;Object.assign(it,f),it.$resourcesUrl$=new URL(f.resourcesUrl||"./",ht.document.baseURI).href,it.$flags$|=2,(()=>{if(!ht.document)return;const u=ht.document.querySelectorAll(`[${Xr}]`);let f=0;for(;f<u.length;f++)Hu(u[f].getAttribute(Xr),pD(u[f].innerHTML),!0)})();let Ve=!1;if(u.map(Se=>{Se[1].map(_t=>{var Et;const wt={$flags$:_t[0],$tagName$:_t[1],$members$:_t[2],$listeners$:_t[3]};4&wt.$flags$&&(Ve=!0),wt.$members$=_t[2],wt.$listeners$=_t[3],wt.$attrsToReflect$=[],wt.$watchers$=null!=(Et=_t[4])?Et:{};const Er=wt.$tagName$,Kr=class extends HTMLElement{constructor(Lt){if(super(Lt),this.hasRegisteredEventListeners=!1,((u,f)=>{const p={$flags$:0,$hostElement$:u,$cmpMeta$:f,$instanceValues$:new Map};p.$onInstancePromise$=new Promise(_=>p.$onInstanceResolve$=_),p.$onReadyPromise$=new Promise(_=>p.$onReadyResolve$=_),u["s-p"]=[],u["s-rc"]=[];const m=p;u.__stencil__getHostRef=()=>m})(Lt=this,wt),1&wt.$flags$)if(Lt.shadowRoot){if("open"!==Lt.shadowRoot.mode)throw new Error(`Unable to re-use existing shadow root for ${wt.$tagName$}! Mode is set to ${Lt.shadowRoot.mode} but Stencil only supports open shadow roots.`)}else ta.call(Lt,wt)}connectedCallback(){const Lt=Ze(this);this.hasRegisteredEventListeners||(this.hasRegisteredEventListeners=!0,Hl(this,Lt,wt.$listeners$)),fe&&(clearTimeout(fe),fe=null),ue?F.push(this):it.jmp(()=>(u=>{if(!(1&it.$flags$)){const f=Ze(u),p=f.$cmpMeta$,m=()=>{};if(1&f.$flags$)Hl(u,f,p.$listeners$),f?.$lazyInstance$?jl(f.$lazyInstance$,u):f?.$onReadyPromise$&&f.$onReadyPromise$.then(()=>jl(f.$lazyInstance$,u));else{let _;if(f.$flags$|=1,_=u.getAttribute(tr),_){if(1&p.$flags$){const D=oa(u.shadowRoot,p,u.getAttribute("s-mode"));u.classList.remove(D+"-h",D+"-s")}else if(2&p.$flags$){const D=zu(p,u.getAttribute("s-mode"));u["s-sc"]=D}((u,f,p,m)=>{var _;const N=u.shadowRoot,E=[],T=[],w=[],F=N?[]:null,fe=Qi(f,null);let Ve;fe.$elm$=u,Object.entries((null==(_=m.$cmpMeta$)?void 0:_.$members$)||{}).forEach(([Vt,[ar,Rn]])=>{var Yn;if(!(31&ar))return;const Gl=u.getAttribute(Rn||Vt);if(null!==Gl){const dp=ce(Gl,ar);null==(Yn=m?.$instanceValues$)||Yn.set(Vt,dp)}});{const Vt=m.$cmpMeta$;Vt&&10&Vt.$flags$&&u["s-sc"]?(Ve=u["s-sc"],u.classList.add(Ve+"-h")):u["s-sc"]&&delete u["s-sc"]}ht.document&&(!it.$orgLocNodes$||!it.$orgLocNodes$.size)&&gi(ht.document.body,it.$orgLocNodes$=new Map),u[tr]=p,u.removeAttribute(tr),m.$vnode$=Zr(fe,E,T,F,u,u,p,w);let Se=0;const _t=E.length;let Et;for(;Se<_t;Se++){Et=E[Se];const Vt=Et.$hostId$+"."+Et.$nodeId$,ar=it.$orgLocNodes$.get(Vt),Rn=Et.$elm$;N||(Rn["s-hn"]=f.toUpperCase(),"slot"===Et.$tag$&&(Rn["s-cr"]=u["s-cr"])),"slot"===Et.$tag$&&(Et.$name$=Et.$elm$["s-sn"]||Et.$elm$.name||null,Et.$children$?(Et.$flags$|=2,Et.$elm$.childNodes.length||Et.$children$.forEach(Yn=>{Et.$elm$.appendChild(Yn.$elm$)})):Et.$flags$|=1),ar&&ar.isConnected&&(N&&""===ar["s-en"]&&ar.parentNode.insertBefore(Rn,ar.nextSibling),ar.parentNode.removeChild(ar),N||(Rn["s-oo"]=parseInt(Et.$nodeId$))),it.$orgLocNodes$.delete(Vt)}const wt=[],Er=w.length;let Lt,$t,Ci,In,Kr=0;for(;Kr<Er;Kr++)if(Lt=w[Kr],Lt&&Lt.length)for(Ci=Lt.length,$t=0;$t<Ci;$t++){if(In=Lt[$t],wt[In.hostId]||(wt[In.hostId]=it.$orgLocNodes$.get(In.hostId)),!wt[In.hostId])continue;const Vt=wt[In.hostId];(!Vt.shadowRoot||!N)&&(In.slot["s-cr"]=Vt["s-cr"],In.slot["s-cr"]=!In.slot["s-cr"]&&Vt.shadowRoot?Vt:(Vt.__childNodes||Vt.childNodes)[0],oo(In.node,In.slot,!1,In.node["s-oo"]),Kh(In.node)),Vt.shadowRoot&&In.node.parentElement!==Vt&&Vt.appendChild(In.node)}if(Ve&&T.length&&T.forEach(Vt=>{Vt.$elm$.parentElement.classList.add(Ve+"-s")}),N&&!N.childNodes.length){let Vt=0;const ar=F.length;if(ar){for(;Vt<ar;Vt++)N.appendChild(F[Vt]);Array.from(u.childNodes).forEach(Rn=>{"string"!=typeof Rn["s-sn"]&&(1===Rn.nodeType&&Rn.slot&&Rn.hidden?Rn.removeAttribute("hidden"):(8===Rn.nodeType||3===Rn.nodeType&&!Rn.wholeText.trim())&&Rn.parentNode.removeChild(Rn))})}}it.$orgLocNodes$.delete(u["s-id"]),m.$hostElement$=u})(u,p.$tagName$,_,f)}_||12&p.$flags$&&lp(u);{let D=u;for(;D=D.parentNode||D.host;)if(1===D.nodeType&&D.hasAttribute("s-id")&&D["s-p"]||D["s-p"]){$l(f,f.$ancestorComponent$=D);break}}p.$members$&&Object.entries(p.$members$).map(([D,[N]])=>{if(31&N&&u.hasOwnProperty(D)){const E=u[D];delete u[D],u[D]=E}}),Bl(u,f,p)}m()}})(this))}disconnectedCallback(){it.jmp(()=>cp(this)),it.raf(()=>{var Lt;const $t=Ze(this),Ci=F.findIndex(In=>In===this);Ci>-1&&F.splice(Ci,1),(null==(Lt=$t?.$vnode$)?void 0:Lt.$elm$)instanceof Node&&!$t.$vnode$.$elm$.isConnected&&delete $t.$vnode$.$elm$})}componentOnReady(){return Ze(this).$onReadyPromise$}};2&wt.$flags$&&(u=>{Qe(u),On(u),oD(u),Pr(u),na(u),Zh(u),kt(u),et(u),nr(u),iD(u),At(u)})(Kr.prototype),wt.$lazyBundleId$=Se[0],!D.includes(Er)&&!N.get(Er)&&(_.push(Er),N.define(Er,Xu(Kr,wt,1)))})}),_.length>0&&(Ve&&(w.textContent+=Xs),w.textContent+=_.sort()+"{visibility:hidden}.hydrated{visibility:inherit}",w.innerHTML.length)){w.setAttribute("data-styles","");const Se=null!=(p=it.$nonce$)?p:Zo(ht.document);null!=Se&&w.setAttribute("nonce",Se),E.insertBefore(w,T?T.nextSibling:E.firstChild)}ue=!1,F.length?F.map(Se=>Se.connectedCallback()):it.jmp(()=>fe=setTimeout(ma,30))},yi=(u,f)=>f,Hl=(u,f,p,m)=>{p&&ht.document&&p.map(([_,D,N])=>{const E=ya(ht.document,u,_),T=va(f,N),w=zl(_);it.ael(E,D,T,w),(f.$rmListeners$=f.$rmListeners$||[]).push(()=>it.rel(E,D,T,w))})},va=(u,f)=>p=>{var m;try{256&u.$flags$?null==(m=u.$lazyInstance$)||m[f](p):(u.$queuedListeners$=u.$queuedListeners$||[]).push([f,p])}catch(_){Ie(_,u.$hostElement$)}},ya=(u,f,p)=>4&p?u:8&p?ht:16&p?u.body:f,zl=u=>Yo?{passive:!!(1&u),capture:!!(2&u)}:!!(2&u)},6011:(pn,pt,Y)=>{"use strict";Y.r(pt),Y.d(pt,{GESTURE_CONTROLLER:()=>oe.G,createGesture:()=>De});var oe=Y(8607);const be=(I,P,K,q)=>{const J=!!Ue(I)&&{capture:!1,passive:!!q.passive};let X,ee;return I.__zone_symbol__addEventListener?(X="__zone_symbol__addEventListener",ee="__zone_symbol__removeEventListener"):(X="addEventListener",ee="removeEventListener"),I[X](P,K,J),()=>{I[ee](P,K,J)}},Ue=I=>{if(void 0===Je)try{const P=Object.defineProperty({},"passive",{get:()=>{Je=!0}});I.addEventListener("optsTest",()=>{},P)}catch{Je=!1}return!!Je};let Je;const ie=I=>I instanceof Document?I:I.ownerDocument,De=I=>{let P=!1,K=!1,q=!0,J=!1;const X=Object.assign({disableScroll:!1,direction:"x",gesturePriority:0,passive:!0,maxAngle:40,threshold:10},I),ee=X.canStart,G=X.onWillStart,Re=X.onStart,Te=X.onEnd,Le=X.notCaptured,k=X.onMove,ye=X.threshold,we=X.passive,Ne=X.blurOnStart,de={type:"pan",startX:0,startY:0,startTime:0,currentX:0,currentY:0,velocityX:0,velocityY:0,deltaX:0,deltaY:0,currentTime:0,event:void 0,data:void 0},Oe=((I,P,K)=>{const q=K*(Math.PI/180),J="x"===I,X=Math.cos(q),ee=P*P;let G=0,Re=0,Te=!1,Le=0;return{start(k,ye){G=k,Re=ye,Le=0,Te=!0},detect(k,ye){if(!Te)return!1;const we=k-G,Ne=ye-Re,de=we*we+Ne*Ne;if(de<ee)return!1;const Oe=Math.sqrt(de),We=(J?we:Ne)/Oe;return Le=We>X?1:We<-X?-1:0,Te=!1,!0},isGesture:()=>0!==Le,getDirection:()=>Le}})(X.direction,X.threshold,X.maxAngle),We=oe.G.createGesture({name:I.gestureName,priority:I.gesturePriority,disableScroll:I.disableScroll}),V=()=>{P&&(J=!1,k&&k(de))},R=()=>!!We.capture()&&(P=!0,q=!1,de.startX=de.currentX,de.startY=de.currentY,de.startTime=de.currentTime,G?G(de).then(x):x(),!0),x=()=>{Ne&&(()=>{if(typeof document<"u"){const qe=document.activeElement;qe?.blur&&qe.blur()}})(),Re&&Re(de),q=!0},te=()=>{P=!1,K=!1,J=!1,q=!0,We.release()},ne=qe=>{const Ke=P,St=q;if(te(),St){if(Ae(de,qe),Ke)return void(Te&&Te(de));Le&&Le(de)}},$e=((I,P,K,q,J)=>{let X,ee,G,Re,Te,Le,k,ye=0;const we=B=>{ye=Date.now()+2e3,P(B)&&(!ee&&K&&(ee=be(I,"touchmove",K,J)),G||(G=be(B.target,"touchend",de,J)),Re||(Re=be(B.target,"touchcancel",de,J)))},Ne=B=>{ye>Date.now()||P(B)&&(!Le&&K&&(Le=be(ie(I),"mousemove",K,J)),k||(k=be(ie(I),"mouseup",Oe,J)))},de=B=>{We(),q&&q(B)},Oe=B=>{at(),q&&q(B)},We=()=>{ee&&ee(),G&&G(),Re&&Re(),ee=G=Re=void 0},at=()=>{Le&&Le(),k&&k(),Le=k=void 0},re=()=>{We(),at()},V=(B=!0)=>{B?(X||(X=be(I,"touchstart",we,J)),Te||(Te=be(I,"mousedown",Ne,J))):(X&&X(),Te&&Te(),X=Te=void 0,re())};return{enable:V,stop:re,destroy:()=>{V(!1),q=K=P=void 0}}})(X.el,qe=>{const Ke=ge(qe);return!(K||!q||(He(qe,de),de.startX=de.currentX,de.startY=de.currentY,de.startTime=de.currentTime=Ke,de.velocityX=de.velocityY=de.deltaX=de.deltaY=0,de.event=qe,ee&&!1===ee(de))||(We.release(),!We.start()))&&(K=!0,0===ye?R():(Oe.start(de.startX,de.startY),!0))},qe=>{P?!J&&q&&(J=!0,Ae(de,qe),requestAnimationFrame(V)):(Ae(de,qe),Oe.detect(de.currentX,de.currentY)&&(!Oe.isGesture()||!R())&&ze())},ne,{passive:we}),ze=()=>{te(),$e.stop(),Le&&Le(de)};return{enable(qe=!0){qe||(P&&ne(void 0),te()),$e.enable(qe)},destroy(){We.destroy(),$e.destroy()}}},Ae=(I,P)=>{if(!P)return;const K=I.currentX,q=I.currentY,J=I.currentTime;He(P,I);const X=I.currentX,ee=I.currentY,Re=(I.currentTime=ge(P))-J;if(Re>0&&Re<100){const Le=(ee-q)/Re;I.velocityX=(X-K)/Re*.7+.3*I.velocityX,I.velocityY=.7*Le+.3*I.velocityY}I.deltaX=X-I.startX,I.deltaY=ee-I.startY,I.event=P},He=(I,P)=>{let K=0,q=0;if(I){const J=I.changedTouches;if(J&&J.length>0){const X=J[0];K=X.clientX,q=X.clientY}else void 0!==I.pageX&&(K=I.pageX,q=I.pageY)}P.currentX=K,P.currentY=q},ge=I=>I.timeStamp||Date.now()},8855:(pn,pt,Y)=>{"use strict";Y.d(pt,{m:()=>ge});var oe=Y(467),be=Y(9596),Ue=Y(1906),Je=Y(2734),ke=Y(1837),Z=Y(5756);const ie=I=>(0,Z.c)().duration(I?400:300),se=I=>{let P,K;const q=I.width+8,J=(0,Z.c)(),X=(0,Z.c)();I.isEndSide?(P=q+"px",K="0px"):(P=-q+"px",K="0px"),J.addElement(I.menuInnerEl).fromTo("transform",`translateX(${P})`,`translateX(${K})`);const G="ios"===(0,Je.e)(I),Re=G?.2:.25;return X.addElement(I.backdropEl).fromTo("opacity",.01,Re),ie(G).addAnimation([J,X])},De=I=>{let P,K;const q=(0,Je.e)(I),J=I.width;I.isEndSide?(P=-J+"px",K=J+"px"):(P=J+"px",K=-J+"px");const X=(0,Z.c)().addElement(I.menuInnerEl).fromTo("transform",`translateX(${K})`,"translateX(0px)"),ee=(0,Z.c)().addElement(I.contentEl).fromTo("transform","translateX(0px)",`translateX(${P})`),G=(0,Z.c)().addElement(I.backdropEl).fromTo("opacity",.01,.32);return ie("ios"===q).addAnimation([X,ee,G])},Ae=I=>{const P=(0,Je.e)(I),K=I.width*(I.isEndSide?-1:1)+"px",q=(0,Z.c)().addElement(I.contentEl).fromTo("transform","translateX(0px)",`translateX(${K})`);return ie("ios"===P).addAnimation(q)},ge=(()=>{const I=new Map,P=[],K=function(){var x=(0,oe.A)(function*(te){const ne=yield Te(te,!0);return!!ne&&ne.open()});return function(ne){return x.apply(this,arguments)}}(),q=function(){var x=(0,oe.A)(function*(te){const ne=yield void 0!==te?Te(te,!0):Le();return void 0!==ne&&ne.close()});return function(ne){return x.apply(this,arguments)}}(),J=function(){var x=(0,oe.A)(function*(te){const ne=yield Te(te,!0);return!!ne&&ne.toggle()});return function(ne){return x.apply(this,arguments)}}(),X=function(){var x=(0,oe.A)(function*(te,ne){const $e=yield Te(ne);return $e&&($e.disabled=!te),$e});return function(ne,$e){return x.apply(this,arguments)}}(),ee=function(){var x=(0,oe.A)(function*(te,ne){const $e=yield Te(ne);return $e&&($e.swipeGesture=te),$e});return function(ne,$e){return x.apply(this,arguments)}}(),G=function(){var x=(0,oe.A)(function*(te){if(null!=te){const ne=yield Te(te);return void 0!==ne&&ne.isOpen()}return void 0!==(yield Le())});return function(ne){return x.apply(this,arguments)}}(),Re=function(){var x=(0,oe.A)(function*(te){const ne=yield Te(te);return!!ne&&!ne.disabled});return function(ne){return x.apply(this,arguments)}}(),Te=function(){var x=(0,oe.A)(function*(te,ne=!1){if(yield B(),"start"===te||"end"===te){const ze=P.filter(Ke=>Ke.side===te&&!Ke.disabled);if(ze.length>=1)return ze.length>1&&ne&&(0,Je.m)(`menuController queried for a menu on the "${te}" side, but ${ze.length} menus were found. The first menu reference will be used. If this is not the behavior you want then pass the ID of the menu instead of its side.`,ze.map(Ke=>Ke.el)),ze[0].el;const qe=P.filter(Ke=>Ke.side===te);if(qe.length>=1)return qe.length>1&&ne&&(0,Je.m)(`menuController queried for a menu on the "${te}" side, but ${qe.length} menus were found. The first menu reference will be used. If this is not the behavior you want then pass the ID of the menu instead of its side.`,qe.map(Ke=>Ke.el)),qe[0].el}else if(null!=te)return R(ze=>ze.menuId===te);return R(ze=>!ze.disabled)||(P.length>0?P[0].el:void 0)});return function(ne){return x.apply(this,arguments)}}(),Le=function(){var x=(0,oe.A)(function*(){return yield B(),at()});return function(){return x.apply(this,arguments)}}(),k=function(){var x=(0,oe.A)(function*(){return yield B(),re()});return function(){return x.apply(this,arguments)}}(),ye=function(){var x=(0,oe.A)(function*(){return yield B(),V()});return function(){return x.apply(this,arguments)}}(),we=(x,te)=>{I.set(x,te)},Oe=function(){var x=(0,oe.A)(function*(te,ne,$e,ze){if(V())return!1;if(ne){const qe=yield Le();qe&&te.el!==qe&&(yield qe.setOpen(!1,!1))}return te._setOpen(ne,$e,ze)});return function(ne,$e,ze,qe){return x.apply(this,arguments)}}(),at=()=>R(x=>x._isOpen),re=()=>P.map(x=>x.el),V=()=>P.some(x=>x.isAnimating),R=x=>{const te=P.find(x);if(void 0!==te)return te.el},B=()=>Promise.all(Array.from(document.querySelectorAll("ion-menu")).map(x=>new Promise(te=>(0,ke.c)(x,te))));return we("reveal",Ae),we("push",De),we("overlay",se),null==be.d||be.d.addEventListener("ionBackButton",x=>{const te=at();te&&x.detail.register(Ue.MENU_BACK_BUTTON_PRIORITY,()=>te.close())}),{registerAnimation:we,get:Te,getMenus:k,getOpen:Le,isEnabled:Re,swipeGesture:ee,isAnimating:ye,isOpen:G,enable:X,toggle:J,close:q,open:K,_getOpenSync:at,_createAnimation:(x,te)=>{const ne=I.get(x);if(!ne)throw new Error("animation not registered");return ne(te)},_register:x=>{P.indexOf(x)<0&&P.push(x)},_unregister:x=>{const te=P.indexOf(x);te>-1&&P.splice(te,1)},_setOpen:Oe}})()},4590:(pn,pt,Y)=>{"use strict";Y.d(pt,{b:()=>Z,c:()=>ie,d:()=>se,e:()=>We,g:()=>V,l:()=>de,s:()=>at,t:()=>q,w:()=>Oe});var oe=Y(467),be=Y(2734),Ue=Y(1837);const Z="ionViewWillLeave",ie="ionViewDidLeave",se="ionViewWillUnload",De=R=>{R.tabIndex=-1,R.focus()},Ae=R=>null!==R.offsetParent,ge="ion-last-focus",K_saveViewFocus=x=>{if(be.l.get("focusManagerPriority",!1)){const ne=document.activeElement;null!==ne&&x?.contains(ne)&&ne.setAttribute(ge,"true")}},K_setViewFocus=x=>{const te=be.l.get("focusManagerPriority",!1);if(Array.isArray(te)&&!x.contains(document.activeElement)){const ne=x.querySelector(`[${ge}]`);if(ne&&Ae(ne))return void De(ne);for(const $e of te)switch($e){case"content":const ze=x.querySelector('main, [role="main"]');if(ze&&Ae(ze))return void De(ze);break;case"heading":const qe=x.querySelector('h1, [role="heading"][aria-level="1"]');if(qe&&Ae(qe))return void De(qe);break;case"banner":const Ke=x.querySelector('header, [role="banner"]');if(Ke&&Ae(Ke))return void De(Ke);break;default:(0,be.m)(`Unrecognized focus manager priority value ${$e}`)}De(x)}},q=R=>new Promise((B,x)=>{(0,be.w)(()=>{J(R),X(R).then(te=>{te.animation&&te.animation.destroy(),ee(R),B(te)},te=>{ee(R),x(te)})})}),J=R=>{const B=R.enteringEl,x=R.leavingEl;K_saveViewFocus(x),re(B,x,R.direction),R.showGoBack?B.classList.add("can-go-back"):B.classList.remove("can-go-back"),at(B,!1),B.style.setProperty("pointer-events","none"),x&&(at(x,!1),x.style.setProperty("pointer-events","none"))},X=function(){var R=(0,oe.A)(function*(B){const x=yield G(B);return x&&be.B.isBrowser?Re(x,B):Te(B)});return function(x){return R.apply(this,arguments)}}(),ee=R=>{const B=R.enteringEl,x=R.leavingEl;B.classList.remove("ion-page-invisible"),B.style.removeProperty("pointer-events"),void 0!==x&&(x.classList.remove("ion-page-invisible"),x.style.removeProperty("pointer-events")),K_setViewFocus(B)},G=function(){var R=(0,oe.A)(function*(B){return B.leavingEl&&B.animated&&0!==B.duration?B.animationBuilder?B.animationBuilder:"ios"===B.mode?(yield Promise.resolve().then(Y.bind(Y,2668))).iosTransitionAnimation:(yield Promise.resolve().then(Y.bind(Y,4227))).mdTransitionAnimation:void 0});return function(x){return R.apply(this,arguments)}}(),Re=function(){var R=(0,oe.A)(function*(B,x){yield Le(x,!0);const te=B(x.baseEl,x);we(x.enteringEl,x.leavingEl);const ne=yield ye(te,x);return x.progressCallback&&x.progressCallback(void 0),ne&&Ne(x.enteringEl,x.leavingEl),{hasCompleted:ne,animation:te}});return function(x,te){return R.apply(this,arguments)}}(),Te=function(){var R=(0,oe.A)(function*(B){const x=B.enteringEl,te=B.leavingEl,ne=be.l.get("focusManagerPriority",!1);return yield Le(B,ne),we(x,te),Ne(x,te),{hasCompleted:!0}});return function(x){return R.apply(this,arguments)}}(),Le=function(){var R=(0,oe.A)(function*(B,x){(void 0!==B.deepWait?B.deepWait:x)&&(yield Promise.all([We(B.enteringEl),We(B.leavingEl)])),yield k(B.viewIsReady,B.enteringEl)});return function(x,te){return R.apply(this,arguments)}}(),k=function(){var R=(0,oe.A)(function*(B,x){B&&(yield B(x))});return function(x,te){return R.apply(this,arguments)}}(),ye=(R,B)=>{const x=B.progressCallback,te=new Promise(ne=>{R.onFinish($e=>ne(1===$e))});return x?(R.progressStart(!0),x(R)):R.play(),te},we=(R,B)=>{de(B,Z),de(R,"ionViewWillEnter")},Ne=(R,B)=>{de(R,"ionViewDidEnter"),de(B,ie)},de=(R,B)=>{if(R){const x=new CustomEvent(B,{bubbles:!1,cancelable:!1});R.dispatchEvent(x)}},Oe=()=>new Promise(R=>(0,Ue.r)(()=>(0,Ue.r)(()=>R()))),We=function(){var R=(0,oe.A)(function*(B){const x=B;if(x){if(null!=x.componentOnReady){if(null!=(yield x.componentOnReady()))return}else if(null!=x.__registerHost)return void(yield new Promise(ne=>(0,Ue.r)(ne)));yield Promise.all(Array.from(x.children).map(We))}});return function(x){return R.apply(this,arguments)}}(),at=(R,B)=>{B?(R.setAttribute("aria-hidden","true"),R.classList.add("ion-page-hidden")):(R.hidden=!1,R.removeAttribute("aria-hidden"),R.classList.remove("ion-page-hidden"))},re=(R,B,x)=>{void 0!==R&&(R.style.zIndex="back"===x?"99":"101"),void 0!==B&&(B.style.zIndex="100")},V=R=>R.classList.contains("ion-page")?R:R.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")||R},9596:(pn,pt,Y)=>{"use strict";Y.d(pt,{d:()=>be,w:()=>oe});const oe=typeof window<"u"?window:void 0,be=typeof document<"u"?document:void 0},2668:(pn,pt,Y)=>{"use strict";Y.r(pt),Y.d(pt,{iosTransitionAnimation:()=>P,shadow:()=>se});var oe=Y(5756),be=Y(4590);Y(2734),Y(9596),Y(1837);const ie=q=>document.querySelector(`${q}.ion-cloned-element`),se=q=>q.shadowRoot||q,De=q=>{const J="ION-TABS"===q.tagName?q:q.querySelector("ion-tabs"),X="ion-content ion-header:not(.header-collapse-condense-inactive) ion-title.title-large";if(null!=J){const ee=J.querySelector("ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)");return null!=ee?ee.querySelector(X):null}return q.querySelector(X)},Ae=(q,J)=>{const X="ION-TABS"===q.tagName?q:q.querySelector("ion-tabs");let ee=[];if(null!=X){const G=X.querySelector("ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)");null!=G&&(ee=G.querySelectorAll("ion-buttons"))}else ee=q.querySelectorAll("ion-buttons");for(const G of ee){const Re=G.closest("ion-header"),Te=Re&&!Re.classList.contains("header-collapse-condense-inactive"),Le=G.querySelector("ion-back-button"),k=G.classList.contains("buttons-collapse");if(null!==Le&&("start"===G.slot||""===G.slot)&&(k&&Te&&J||!k))return Le}return null},ge=(q,J,X,ee,G,Re,Te,Le,k)=>{var ye,we;const Ne=J?`calc(100% - ${G.right+4}px)`:G.left-4+"px",de=J?"right":"left",Oe=J?"left":"right",We=J?"right":"left";let at=1,re=1,V=`scale(${re})`;const R="scale(1)";if(Re&&Te){const H=(null===(ye=Re.textContent)||void 0===ye?void 0:ye.trim())===(null===(we=Le.textContent)||void 0===we?void 0:we.trim());at=k.width/Te.width,re=(k.height-K)/Te.height,V=H?`scale(${at}, ${re})`:`scale(${re})`}const x=se(ee).querySelector("ion-icon").getBoundingClientRect(),te=J?x.width/2-(x.right-G.right)+"px":G.left-x.width/2+"px",ne=J?`-${window.innerWidth-G.right}px`:`${G.left}px`,$e=`${k.top}px`,ze=`${G.top}px`,St=X?[{offset:0,transform:`translate3d(${ne}, ${ze}, 0)`},{offset:1,transform:`translate3d(${te}, ${$e}, 0)`}]:[{offset:0,transform:`translate3d(${te}, ${$e}, 0)`},{offset:1,transform:`translate3d(${ne}, ${ze}, 0)`}],xt=X?[{offset:0,opacity:1,transform:R},{offset:1,opacity:0,transform:V}]:[{offset:0,opacity:0,transform:V},{offset:1,opacity:1,transform:R}],nt=X?[{offset:0,opacity:1,transform:"scale(1)"},{offset:.2,opacity:0,transform:"scale(0.6)"},{offset:1,opacity:0,transform:"scale(0.6)"}]:[{offset:0,opacity:0,transform:"scale(0.6)"},{offset:.6,opacity:0,transform:"scale(0.6)"},{offset:1,opacity:1,transform:"scale(1)"}],Wt=(0,oe.c)(),un=(0,oe.c)(),yn=(0,oe.c)(),Ze=ie("ion-back-button"),Q=se(Ze).querySelector(".button-text"),L=se(Ze).querySelector("ion-icon");Ze.text=ee.text,Ze.mode=ee.mode,Ze.icon=ee.icon,Ze.color=ee.color,Ze.disabled=ee.disabled,Ze.style.setProperty("display","block"),Ze.style.setProperty("position","fixed"),un.addElement(L),Wt.addElement(Q),yn.addElement(Ze),yn.beforeStyles({position:"absolute",top:"0px",[We]:"0px"}).beforeAddWrite(()=>{ee.style.setProperty("display","none"),Ze.style.setProperty(de,Ne)}).afterAddWrite(()=>{ee.style.setProperty("display",""),Ze.style.setProperty("display","none"),Ze.style.removeProperty(de)}).keyframes(St),Wt.beforeStyles({"transform-origin":`${de} top`}).keyframes(xt),un.beforeStyles({"transform-origin":`${Oe} center`}).keyframes(nt),q.addAnimation([Wt,un,yn])},I=(q,J,X,ee,G,Re,Te,Le,k)=>{var ye,we;const Ne=J?"right":"left",de=J?`calc(100% - ${G.right}px)`:`${G.left}px`,We=`${G.top}px`;let re=J?`-${window.innerWidth-Te.right-8}px`:`${Te.x+8}px`,V=.5;const R="scale(1)";let B=`scale(${V})`;if(Le&&k){re=J?`-${window.innerWidth-k.right-8}px`:k.x-8+"px";const gn=(null===(ye=Le.textContent)||void 0===ye?void 0:ye.trim())===(null===(we=ee.textContent)||void 0===we?void 0:we.trim());V=k.height/(Re.height-K),B=gn?`scale(${k.width/Re.width}, ${V})`:`scale(${V})`}const ne=Te.top+Te.height/2-G.height*V/2+"px",qe=X?[{offset:0,opacity:0,transform:`translate3d(${re}, ${ne}, 0) ${B}`},{offset:.1,opacity:0},{offset:1,opacity:1,transform:`translate3d(0px, ${We}, 0) ${R}`}]:[{offset:0,opacity:.99,transform:`translate3d(0px, ${We}, 0) ${R}`},{offset:.6,opacity:0},{offset:1,opacity:0,transform:`translate3d(${re}, ${ne}, 0) ${B}`}],Ke=ie("ion-title"),St=(0,oe.c)();Ke.innerText=ee.innerText,Ke.size=ee.size,Ke.color=ee.color,St.addElement(Ke),St.beforeStyles({"transform-origin":`${Ne} top`,height:`${G.height}px`,display:"",position:"relative",[Ne]:de}).beforeAddWrite(()=>{ee.style.setProperty("opacity","0")}).afterAddWrite(()=>{ee.style.setProperty("opacity",""),Ke.style.setProperty("display","none")}).keyframes(qe),q.addAnimation(St)},P=(q,J)=>{var X;try{const ee="cubic-bezier(0.32,0.72,0,1)",G="opacity",Re="transform",Te="0%",k="rtl"===q.ownerDocument.dir,ye=k?"-99.5%":"99.5%",we=k?"33%":"-33%",Ne=J.enteringEl,de=J.leavingEl,Oe="back"===J.direction,We=Ne.querySelector(":scope > ion-content"),at=Ne.querySelectorAll(":scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *"),re=Ne.querySelectorAll(":scope > ion-header > ion-toolbar"),V=(0,oe.c)(),R=(0,oe.c)();if(V.addElement(Ne).duration((null!==(X=J.duration)&&void 0!==X?X:0)||540).easing(J.easing||ee).fill("both").beforeRemoveClass("ion-page-invisible"),de&&null!=q){const ne=(0,oe.c)();ne.addElement(q),V.addAnimation(ne)}if(We||0!==re.length||0!==at.length?(R.addElement(We),R.addElement(at)):R.addElement(Ne.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")),V.addAnimation(R),Oe?R.beforeClearStyles([G]).fromTo("transform",`translateX(${we})`,`translateX(${Te})`).fromTo(G,.8,1):R.beforeClearStyles([G]).fromTo("transform",`translateX(${ye})`,`translateX(${Te})`),We){const ne=se(We).querySelector(".transition-effect");if(ne){const $e=ne.querySelector(".transition-cover"),ze=ne.querySelector(".transition-shadow"),qe=(0,oe.c)(),Ke=(0,oe.c)(),St=(0,oe.c)();qe.addElement(ne).beforeStyles({opacity:"1",display:"block"}).afterStyles({opacity:"",display:""}),Ke.addElement($e).beforeClearStyles([G]).fromTo(G,0,.1),St.addElement(ze).beforeClearStyles([G]).fromTo(G,.03,.7),qe.addAnimation([Ke,St]),R.addAnimation([qe])}}const B=Ne.querySelector("ion-header.header-collapse-condense"),{forward:x,backward:te}=((q,J,X,ee,G)=>{const Re=Ae(ee,X),Te=De(G),Le=De(ee),k=Ae(G,X),ye=null!==Re&&null!==Te&&!X,we=null!==Le&&null!==k&&X;if(ye){const Ne=Te.getBoundingClientRect(),de=Re.getBoundingClientRect(),Oe=se(Re).querySelector(".button-text"),We=Oe?.getBoundingClientRect(),re=se(Te).querySelector(".toolbar-title").getBoundingClientRect();I(q,J,X,Te,Ne,re,de,Oe,We),ge(q,J,X,Re,de,Oe,We,Te,re)}else if(we){const Ne=Le.getBoundingClientRect(),de=k.getBoundingClientRect(),Oe=se(k).querySelector(".button-text"),We=Oe?.getBoundingClientRect(),re=se(Le).querySelector(".toolbar-title").getBoundingClientRect();I(q,J,X,Le,Ne,re,de,Oe,We),ge(q,J,X,k,de,Oe,We,Le,re)}return{forward:ye,backward:we}})(V,k,Oe,Ne,de);if(re.forEach(ne=>{const $e=(0,oe.c)();$e.addElement(ne),V.addAnimation($e);const ze=(0,oe.c)();ze.addElement(ne.querySelector("ion-title"));const qe=(0,oe.c)(),Ke=Array.from(ne.querySelectorAll("ion-buttons,[menuToggle]")),St=ne.closest("ion-header"),gn=St?.classList.contains("header-collapse-condense-inactive");let Jt;Jt=Ke.filter(Oe?Wt=>{const un=Wt.classList.contains("buttons-collapse");return un&&!gn||!un}:Wt=>!Wt.classList.contains("buttons-collapse")),qe.addElement(Jt);const xt=(0,oe.c)();xt.addElement(ne.querySelectorAll(":scope > *:not(ion-title):not(ion-buttons):not([menuToggle])"));const cn=(0,oe.c)();cn.addElement(se(ne).querySelector(".toolbar-background"));const Gt=(0,oe.c)(),nt=ne.querySelector("ion-back-button");if(nt&&Gt.addElement(nt),$e.addAnimation([ze,qe,xt,cn,Gt]),qe.fromTo(G,.01,1),xt.fromTo(G,.01,1),Oe)gn||ze.fromTo("transform",`translateX(${we})`,`translateX(${Te})`).fromTo(G,.01,1),xt.fromTo("transform",`translateX(${we})`,`translateX(${Te})`),Gt.fromTo(G,.01,1);else if(B||ze.fromTo("transform",`translateX(${ye})`,`translateX(${Te})`).fromTo(G,.01,1),xt.fromTo("transform",`translateX(${ye})`,`translateX(${Te})`),cn.beforeClearStyles([G,"transform"]),St?.translucent?cn.fromTo("transform",k?"translateX(-100%)":"translateX(100%)","translateX(0px)"):cn.fromTo(G,.01,"var(--opacity)"),x||Gt.fromTo(G,.01,1),nt&&!x){const un=(0,oe.c)();un.addElement(se(nt).querySelector(".button-text")).fromTo("transform",k?"translateX(-100px)":"translateX(100px)","translateX(0px)"),$e.addAnimation(un)}}),de){const ne=(0,oe.c)(),$e=de.querySelector(":scope > ion-content"),ze=de.querySelectorAll(":scope > ion-header > ion-toolbar"),qe=de.querySelectorAll(":scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *");if($e||0!==ze.length||0!==qe.length?(ne.addElement($e),ne.addElement(qe)):ne.addElement(de.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")),V.addAnimation(ne),Oe){ne.beforeClearStyles([G]).fromTo("transform",`translateX(${Te})`,k?"translateX(-100%)":"translateX(100%)");const Ke=(0,be.g)(de);V.afterAddWrite(()=>{"normal"===V.getDirection()&&Ke.style.setProperty("display","none")})}else ne.fromTo("transform",`translateX(${Te})`,`translateX(${we})`).fromTo(G,1,.8);if($e){const Ke=se($e).querySelector(".transition-effect");if(Ke){const St=Ke.querySelector(".transition-cover"),gn=Ke.querySelector(".transition-shadow"),Jt=(0,oe.c)(),xt=(0,oe.c)(),cn=(0,oe.c)();Jt.addElement(Ke).beforeStyles({opacity:"1",display:"block"}).afterStyles({opacity:"",display:""}),xt.addElement(St).beforeClearStyles([G]).fromTo(G,.1,0),cn.addElement(gn).beforeClearStyles([G]).fromTo(G,.7,.03),Jt.addAnimation([xt,cn]),ne.addAnimation([Jt])}}ze.forEach(Ke=>{const St=(0,oe.c)();St.addElement(Ke);const gn=(0,oe.c)();gn.addElement(Ke.querySelector("ion-title"));const Jt=(0,oe.c)(),xt=Ke.querySelectorAll("ion-buttons,[menuToggle]"),cn=Ke.closest("ion-header"),Gt=cn?.classList.contains("header-collapse-condense-inactive"),nt=Array.from(xt).filter(L=>{const H=L.classList.contains("buttons-collapse");return H&&!Gt||!H});Jt.addElement(nt);const Wt=(0,oe.c)(),un=Ke.querySelectorAll(":scope > *:not(ion-title):not(ion-buttons):not([menuToggle])");un.length>0&&Wt.addElement(un);const yn=(0,oe.c)();yn.addElement(se(Ke).querySelector(".toolbar-background"));const Ze=(0,oe.c)(),Q=Ke.querySelector("ion-back-button");if(Q&&Ze.addElement(Q),St.addAnimation([gn,Jt,Wt,Ze,yn]),V.addAnimation(St),Ze.fromTo(G,.99,0),Jt.fromTo(G,.99,0),Wt.fromTo(G,.99,0),Oe){if(Gt||gn.fromTo("transform",`translateX(${Te})`,k?"translateX(-100%)":"translateX(100%)").fromTo(G,.99,0),Wt.fromTo("transform",`translateX(${Te})`,k?"translateX(-100%)":"translateX(100%)"),yn.beforeClearStyles([G,"transform"]),cn?.translucent?yn.fromTo("transform","translateX(0px)",k?"translateX(-100%)":"translateX(100%)"):yn.fromTo(G,"var(--opacity)",0),Q&&!te){const H=(0,oe.c)();H.addElement(se(Q).querySelector(".button-text")).fromTo("transform",`translateX(${Te})`,`translateX(${(k?-124:124)+"px"})`),St.addAnimation(H)}}else Gt||gn.fromTo("transform",`translateX(${Te})`,`translateX(${we})`).fromTo(G,.99,0).afterClearStyles([Re,G]),Wt.fromTo("transform",`translateX(${Te})`,`translateX(${we})`).afterClearStyles([Re,G]),Ze.afterClearStyles([G]),gn.afterClearStyles([G]),Jt.afterClearStyles([G])})}return V}catch(ee){throw ee}},K=10},4227:(pn,pt,Y)=>{"use strict";Y.r(pt),Y.d(pt,{mdTransitionAnimation:()=>Z});var oe=Y(5756),be=Y(4590);Y(2734),Y(9596),Y(1837);const Z=(ie,se)=>{var De,Ae,He;const P="back"===se.direction,q=se.leavingEl,J=(0,be.g)(se.enteringEl),X=J.querySelector("ion-toolbar"),ee=(0,oe.c)();if(ee.addElement(J).fill("both").beforeRemoveClass("ion-page-invisible"),P?ee.duration((null!==(De=se.duration)&&void 0!==De?De:0)||200).easing("cubic-bezier(0.47,0,0.745,0.715)"):ee.duration((null!==(Ae=se.duration)&&void 0!==Ae?Ae:0)||280).easing("cubic-bezier(0.36,0.66,0.04,1)").fromTo("transform","translateY(40px)","translateY(0px)").fromTo("opacity",.01,1),X){const G=(0,oe.c)();G.addElement(X),ee.addAnimation(G)}if(q&&P){ee.duration((null!==(He=se.duration)&&void 0!==He?He:0)||200).easing("cubic-bezier(0.47,0,0.745,0.715)");const G=(0,oe.c)();G.addElement((0,be.g)(q)).onFinish(Re=>{1===Re&&G.elements.length>0&&G.elements[0].style.setProperty("display","none")}).fromTo("transform","translateY(0px)","translateY(40px)").fromTo("opacity",1,0),ee.addAnimation(G)}return ee}},3217:(pn,pt,Y)=>{"use strict";Y.d(pt,{B:()=>Jt,F:()=>Ze,G:()=>xt,O:()=>cn,a:()=>q,b:()=>J,c:()=>Re,d:()=>Gt,e:()=>nt,f:()=>B,g:()=>te,h:()=>ze,i:()=>Ke,j:()=>Le,k:()=>k,m:()=>ee,n:()=>De,o:()=>V,q:()=>Ae,s:()=>gn});var oe=Y(467),be=Y(9596),Ue=Y(1837),Je=Y(1906),ke=Y(2734),Z=Y(1653),ie=Y(8607);const se='[tabindex]:not([tabindex^="-"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^="-"]):not([hidden]):not([disabled]), textarea:not([tabindex^="-"]):not([hidden]):not([disabled]), button:not([tabindex^="-"]):not([hidden]):not([disabled]), select:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable[disabled="false"]:not([tabindex^="-"]):not([hidden])',De=(Q,L)=>{const H=Q.querySelector(se);He(H,L??Q)},Ae=(Q,L)=>{const H=Array.from(Q.querySelectorAll(se));He(H.length>0?H[H.length-1]:null,L??Q)},He=(Q,L)=>{let H=Q;const Ie=Q?.shadowRoot;if(Ie&&(H=Ie.querySelector(se)||Q),H){const lt=H.closest("ion-radio-group");lt?lt.setFocus():(0,Ue.n)(H)}else L.focus()};let ge=0,I=0;const P=new WeakMap,K=Q=>({create:L=>ye(Q,L),dismiss:(L,H,Ie)=>We(document,L,H,Q,Ie),getTop:()=>(0,oe.A)(function*(){return V(document,Q)})()}),q=K("ion-alert"),J=K("ion-action-sheet"),ee=K("ion-modal"),Re=K("ion-popover"),Le=Q=>{typeof document<"u"&&Oe(document);const L=ge++;Q.overlayIndex=L},k=Q=>(Q.hasAttribute("id")||(Q.id="ion-overlay-"+ ++I),Q.id),ye=(Q,L)=>typeof window<"u"&&typeof window.customElements<"u"?window.customElements.whenDefined(Q).then(()=>{const H=document.createElement(Q);return H.classList.add("overlay-hidden"),Object.assign(H,Object.assign(Object.assign({},L),{hasController:!0})),ne(document).appendChild(H),new Promise(Ie=>(0,Ue.c)(H,Ie))}):Promise.resolve(),Ne=(Q,L)=>{let H=Q;const Ie=Q?.shadowRoot;Ie&&(H=Ie.querySelector(se)||Q),H?(0,Ue.n)(H):L.focus()},Oe=Q=>{0===ge&&(ge=1,Q.addEventListener("focus",L=>{((Q,L)=>{const H=V(L,"ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover"),Ie=Q.target;H&&Ie&&!H.classList.contains(Ze)&&(H.shadowRoot?(()=>{if(H.contains(Ie))H.lastFocus=Ie;else if("ION-TOAST"===Ie.tagName)Ne(H.lastFocus,H);else{const Ot=H.lastFocus;De(H),Ot===L.activeElement&&Ae(H),H.lastFocus=L.activeElement}})():(()=>{if(H===Ie)H.lastFocus=void 0;else if("ION-TOAST"===Ie.tagName)Ne(H.lastFocus,H);else{const Ot=(0,Ue.g)(H);if(!Ot.contains(Ie))return;const Pt=Ot.querySelector(".ion-overlay-wrapper");if(!Pt)return;if(Pt.contains(Ie)||Ie===Ot.querySelector("ion-backdrop"))H.lastFocus=Ie;else{const Xt=H.lastFocus;De(Pt,H),Xt===L.activeElement&&Ae(Pt,H),H.lastFocus=L.activeElement}}})())})(L,Q)},!0),Q.addEventListener("ionBackButton",L=>{const H=V(Q);H?.backdropDismiss&&L.detail.register(Je.OVERLAY_BACK_BUTTON_PRIORITY,()=>{H.dismiss(void 0,Jt)})}),(0,Je.shouldUseCloseWatcher)()||Q.addEventListener("keydown",L=>{if("Escape"===L.key){const H=V(Q);H?.backdropDismiss&&H.dismiss(void 0,Jt)}}))},We=(Q,L,H,Ie,lt)=>{const qt=V(Q,Ie,lt);return qt?qt.dismiss(L,H):Promise.reject("overlay does not exist")},re=(Q,L)=>((Q,L)=>(void 0===L&&(L="ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast"),Array.from(Q.querySelectorAll(L)).filter(H=>H.overlayIndex>0)))(Q,L).filter(H=>!(Q=>Q.classList.contains("overlay-hidden"))(H)),V=(Q,L,H)=>{const Ie=re(Q,L);return void 0===H?Ie[Ie.length-1]:Ie.find(lt=>lt.id===H)},R=(Q=!1)=>{const H=ne(document).querySelector("ion-router-outlet, ion-nav, #ion-view-container-root");H&&(Q?H.setAttribute("aria-hidden","true"):H.removeAttribute("aria-hidden"))},B=function(){var Q=(0,oe.A)(function*(L,H,Ie,lt,qt){var Ot,Pt;if(L.presented)return;"ION-TOAST"!==L.el.tagName&&(R(!0),document.body.classList.add(ie.B)),un(L.el),Wt(L.el),L.presented=!0,L.willPresent.emit(),null===(Ot=L.willPresentShorthand)||void 0===Ot||Ot.emit();const Xt=(0,ke.e)(L),En=L.enterAnimation?L.enterAnimation:ke.l.get(H,"ios"===Xt?Ie:lt);(yield $e(L,En,L.el,qt))&&(L.didPresent.emit(),null===(Pt=L.didPresentShorthand)||void 0===Pt||Pt.emit()),"ION-TOAST"!==L.el.tagName&&x(L.el),L.keyboardClose&&(null===document.activeElement||!L.el.contains(document.activeElement))&&L.el.focus(),L.el.removeAttribute("aria-hidden")});return function(H,Ie,lt,qt,Ot){return Q.apply(this,arguments)}}(),x=function(){var Q=(0,oe.A)(function*(L){let H=document.activeElement;if(!H)return;const Ie=H?.shadowRoot;Ie&&(H=Ie.querySelector(se)||H),yield L.onDidDismiss(),(null===document.activeElement||document.activeElement===document.body)&&H.focus()});return function(H){return Q.apply(this,arguments)}}(),te=function(){var Q=(0,oe.A)(function*(L,H,Ie,lt,qt,Ot,Pt){var Xt,En;if(!L.presented)return!1;const er=(void 0!==be.d?re(be.d):[]).filter(tr=>"ION-TOAST"!==tr.tagName);1===er.length&&er[0].id===L.el.id&&(R(!1),document.body.classList.remove(ie.B)),L.presented=!1;try{Wt(L.el),L.el.style.setProperty("pointer-events","none"),L.willDismiss.emit({data:H,role:Ie}),null===(Xt=L.willDismissShorthand)||void 0===Xt||Xt.emit({data:H,role:Ie});const tr=(0,ke.e)(L),Xr=L.leaveAnimation?L.leaveAnimation:ke.l.get(lt,"ios"===tr?qt:Ot);Ie!==xt&&(yield $e(L,Xr,L.el,Pt)),L.didDismiss.emit({data:H,role:Ie}),null===(En=L.didDismissShorthand)||void 0===En||En.emit({data:H,role:Ie}),(P.get(L)||[]).forEach(Xo=>Xo.destroy()),P.delete(L),L.el.classList.add("overlay-hidden"),L.el.style.removeProperty("pointer-events"),void 0!==L.el.lastFocus&&(L.el.lastFocus=void 0)}catch(tr){(0,ke.o)(`[${L.el.tagName.toLowerCase()}] - `,tr)}return L.el.remove(),yn(),!0});return function(H,Ie,lt,qt,Ot,Pt,Xt){return Q.apply(this,arguments)}}(),ne=Q=>Q.querySelector("ion-app")||Q.body,$e=function(){var Q=(0,oe.A)(function*(L,H,Ie,lt){Ie.classList.remove("overlay-hidden");const Ot=H(L.el,lt);(!L.animated||!ke.l.getBoolean("animated",!0))&&Ot.duration(0),L.keyboardClose&&Ot.beforeAddWrite(()=>{const Xt=Ie.ownerDocument.activeElement;Xt?.matches("input,ion-input, ion-textarea")&&Xt.blur()});const Pt=P.get(L)||[];return P.set(L,[...Pt,Ot]),yield Ot.play(),!0});return function(H,Ie,lt,qt){return Q.apply(this,arguments)}}(),ze=(Q,L)=>{let H;const Ie=new Promise(lt=>H=lt);return qe(Q,L,lt=>{H(lt.detail)}),Ie},qe=(Q,L,H)=>{const Ie=lt=>{(0,Ue.m)(Q,L,Ie),H(lt)};(0,Ue.f)(Q,L,Ie)},Ke=Q=>"cancel"===Q||Q===Jt,St=Q=>Q(),gn=(Q,L)=>{if("function"==typeof Q)return ke.l.get("_zoneGate",St)(()=>{try{return Q(L)}catch(Ie){throw Ie}})},Jt="backdrop",xt="gesture",cn=39,Gt=Q=>{let H,L=!1;const Ie=(0,Z.C)(),lt=(Pt=!1)=>{if(H&&!Pt)return{delegate:H,inline:L};const{el:Xt,hasController:En,delegate:Cr}=Q;return L=null!==Xt.parentNode&&!En,H=L?Cr||Ie:Cr,{inline:L,delegate:H}};return{attachViewToDom:function(){var Pt=(0,oe.A)(function*(Xt){const{delegate:En}=lt(!0);if(En)return yield En.attachViewToDom(Q.el,Xt);const{hasController:Cr}=Q;if(Cr&&void 0!==Xt)throw new Error("framework delegate is missing");return null});return function(En){return Pt.apply(this,arguments)}}(),removeViewFromDom:()=>{const{delegate:Pt}=lt();Pt&&void 0!==Q.el&&Pt.removeViewFromDom(Q.el.parentElement,Q.el)}}},nt=()=>{let Q;const L=()=>{Q&&(Q(),Q=void 0)};return{addClickListener:(Ie,lt)=>{L();const qt=void 0!==lt?document.getElementById(lt):null;qt?Q=((Pt,Xt)=>{const En=()=>{Xt.present()};return Pt.addEventListener("click",En),()=>{Pt.removeEventListener("click",En)}})(qt,Ie):(0,ke.m)(`[${Ie.tagName.toLowerCase()}] - A trigger element with the ID "${lt}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on an overlay component.`,Ie)},removeClickListener:L}},Wt=Q=>{void 0!==be.d&&(0,ke.a)("android")&&Q.setAttribute("aria-hidden","true")},un=Q=>{var L;if(void 0===be.d)return;const H=re(be.d);for(let Ie=H.length-1;Ie>=0;Ie--){const lt=H[Ie],qt=null!==(L=H[Ie+1])&&void 0!==L?L:Q;(qt.hasAttribute("aria-hidden")||"ION-TOAST"!==qt.tagName)&&lt.setAttribute("aria-hidden","true")}},yn=()=>{if(void 0===be.d)return;const Q=re(be.d);for(let L=Q.length-1;L>=0;L--){const H=Q[L];if(H.removeAttribute("aria-hidden"),"ION-TOAST"!==H.tagName)break}},Ze="ion-disable-focus-trap"},772:(pn,pt,Y)=>{"use strict";function oe(e,n){return Object.is(e,n)}let be=null,Ue=!1,Je=1;const ke=Symbol("SIGNAL");function Z(e){const n=be;return be=e,n}const Ae={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function He(e){if(Ue)throw new Error("");if(null===be)return;be.consumerOnSignalRead(e);const n=be.nextProducerIndex++;k(be),n<be.producerNode.length&&be.producerNode[n]!==e&&Le(be)&&Te(be.producerNode[n],be.producerIndexOfThis[n]),be.producerNode[n]!==e&&(be.producerNode[n]=e,be.producerIndexOfThis[n]=Le(be)?Re(e,be,n):0),be.producerLastReadVersion[n]=e.version}function I(e){if((!Le(e)||e.dirty)&&(e.dirty||e.lastCleanEpoch!==Je)){if(!e.producerMustRecompute(e)&&!ee(e))return e.dirty=!1,void(e.lastCleanEpoch=Je);e.producerRecomputeValue(e),e.dirty=!1,e.lastCleanEpoch=Je}}function P(e){if(void 0===e.liveConsumerNode)return;const n=Ue;Ue=!0;try{for(const t of e.liveConsumerNode)t.dirty||q(t)}finally{Ue=n}}function K(){return!1!==be?.consumerAllowSignalWrites}function q(e){e.dirty=!0,P(e),e.consumerMarkedDirty?.(e)}function J(e){return e&&(e.nextProducerIndex=0),Z(e)}function X(e,n){if(Z(n),e&&void 0!==e.producerNode&&void 0!==e.producerIndexOfThis&&void 0!==e.producerLastReadVersion){if(Le(e))for(let t=e.nextProducerIndex;t<e.producerNode.length;t++)Te(e.producerNode[t],e.producerIndexOfThis[t]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function ee(e){k(e);for(let n=0;n<e.producerNode.length;n++){const t=e.producerNode[n],r=e.producerLastReadVersion[n];if(r!==t.version||(I(t),r!==t.version))return!0}return!1}function G(e){if(k(e),Le(e))for(let n=0;n<e.producerNode.length;n++)Te(e.producerNode[n],e.producerIndexOfThis[n]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Re(e,n,t){if(ye(e),0===e.liveConsumerNode.length&&we(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=Re(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(t),e.liveConsumerNode.push(n)-1}function Te(e,n){if(ye(e),1===e.liveConsumerNode.length&&we(e))for(let r=0;r<e.producerNode.length;r++)Te(e.producerNode[r],e.producerIndexOfThis[r]);const t=e.liveConsumerNode.length-1;if(e.liveConsumerNode[n]=e.liveConsumerNode[t],e.liveConsumerIndexOfThis[n]=e.liveConsumerIndexOfThis[t],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,n<e.liveConsumerNode.length){const r=e.liveConsumerIndexOfThis[n],o=e.liveConsumerNode[n];k(o),o.producerIndexOfThis[r]=n}}function Le(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function k(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function ye(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function we(e){return void 0!==e.producerNode}const de=Symbol("UNSET"),Oe=Symbol("COMPUTING"),We=Symbol("ERRORED"),at={...Ae,value:de,dirty:!0,error:null,equal:oe,producerMustRecompute:e=>e.value===de||e.value===Oe,producerRecomputeValue(e){if(e.value===Oe)throw new Error("Detected cycle in computations.");const n=e.value;e.value=Oe;const t=J(e);let r;try{r=e.computation()}catch(o){r=We,e.error=o}finally{X(e,t)}n!==de&&n!==We&&r!==We&&e.equal(n,r)?e.value=n:(e.value=r,e.version++)}};let V=function re(){throw new Error};function R(){V()}let x=null;function ze(e,n){K()||R(),e.equal(e.value,n)||(e.value=n,function gn(e){e.version++,function ge(){Je++}(),P(e),x?.()}(e))}const St={...Ae,equal:oe,value:void 0};function nt(e){return"function"==typeof e}function Wt(e){const t=e(r=>{Error.call(r),r.stack=(new Error).stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}const un=Wt(e=>function(t){e(this),this.message=t?`${t.length} errors occurred during unsubscription:\n${t.map((r,o)=>`${o+1}) ${r.toString()}`).join("\n  ")}`:"",this.name="UnsubscriptionError",this.errors=t});function yn(e,n){if(e){const t=e.indexOf(n);0<=t&&e.splice(t,1)}}class Ze{constructor(n){this.initialTeardown=n,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let n;if(!this.closed){this.closed=!0;const{_parentage:t}=this;if(t)if(this._parentage=null,Array.isArray(t))for(const i of t)i.remove(this);else t.remove(this);const{initialTeardown:r}=this;if(nt(r))try{r()}catch(i){n=i instanceof un?i.errors:[i]}const{_finalizers:o}=this;if(o){this._finalizers=null;for(const i of o)try{H(i)}catch(s){n=n??[],s instanceof un?n=[...n,...s.errors]:n.push(s)}}if(n)throw new un(n)}}add(n){var t;if(n&&n!==this)if(this.closed)H(n);else{if(n instanceof Ze){if(n.closed||n._hasParent(this))return;n._addParent(this)}(this._finalizers=null!==(t=this._finalizers)&&void 0!==t?t:[]).push(n)}}_hasParent(n){const{_parentage:t}=this;return t===n||Array.isArray(t)&&t.includes(n)}_addParent(n){const{_parentage:t}=this;this._parentage=Array.isArray(t)?(t.push(n),t):t?[t,n]:n}_removeParent(n){const{_parentage:t}=this;t===n?this._parentage=null:Array.isArray(t)&&yn(t,n)}remove(n){const{_finalizers:t}=this;t&&yn(t,n),n instanceof Ze&&n._removeParent(this)}}Ze.EMPTY=(()=>{const e=new Ze;return e.closed=!0,e})();const Q=Ze.EMPTY;function L(e){return e instanceof Ze||e&&"closed"in e&&nt(e.remove)&&nt(e.add)&&nt(e.unsubscribe)}function H(e){nt(e)?e():e.unsubscribe()}const Ie={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},lt={setTimeout(e,n,...t){const{delegate:r}=lt;return r?.setTimeout?r.setTimeout(e,n,...t):setTimeout(e,n,...t)},clearTimeout(e){const{delegate:n}=lt;return(n?.clearTimeout||clearTimeout)(e)},delegate:void 0};function qt(e){lt.setTimeout(()=>{const{onUnhandledError:n}=Ie;if(!n)throw e;n(e)})}function Ot(){}const Pt=Cr("C",void 0,void 0);function Cr(e,n,t){return{kind:e,value:n,error:t}}let er=null;function qo(e){if(Ie.useDeprecatedSynchronousErrorHandling){const n=!er;if(n&&(er={errorThrown:!1,error:null}),e(),n){const{errorThrown:t,error:r}=er;if(er=null,t)throw r}}else e()}class Xr extends Ze{constructor(n){super(),this.isStopped=!1,n?(this.destination=n,L(n)&&n.add(this)):this.destination=qi}static create(n,t,r){return new Rr(n,t,r)}next(n){this.isStopped?it(function En(e){return Cr("N",e,void 0)}(n),this):this._next(n)}error(n){this.isStopped?it(function Xt(e){return Cr("E",void 0,e)}(n),this):(this.isStopped=!0,this._error(n))}complete(){this.isStopped?it(Pt,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(n){this.destination.next(n)}_error(n){try{this.destination.error(n)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}}const Wi=Function.prototype.bind;function Xo(e,n){return Wi.call(e,n)}class Xs{constructor(n){this.partialObserver=n}next(n){const{partialObserver:t}=this;if(t.next)try{t.next(n)}catch(r){ht(r)}}error(n){const{partialObserver:t}=this;if(t.error)try{t.error(n)}catch(r){ht(r)}else ht(n)}complete(){const{partialObserver:n}=this;if(n.complete)try{n.complete()}catch(t){ht(t)}}}class Rr extends Xr{constructor(n,t,r){let o;if(super(),nt(n)||!n)o={next:n??void 0,error:t??void 0,complete:r??void 0};else{let i;this&&Ie.useDeprecatedNextContext?(i=Object.create(n),i.unsubscribe=()=>this.unsubscribe(),o={next:n.next&&Xo(n.next,i),error:n.error&&Xo(n.error,i),complete:n.complete&&Xo(n.complete,i)}):o=n}this.destination=new Xs(o)}}function ht(e){Ie.useDeprecatedSynchronousErrorHandling?function tr(e){Ie.useDeprecatedSynchronousErrorHandling&&er&&(er.errorThrown=!0,er.error=e)}(e):qt(e)}function it(e,n){const{onStoppedNotification:t}=Ie;t&&lt.setTimeout(()=>t(e,n))}const qi={closed:!0,next:Ot,error:function Ys(e){throw e},complete:Ot},Yo="function"==typeof Symbol&&Symbol.observable||"@@observable";function Yr(e){return e}function hi(e){return 0===e.length?Yr:1===e.length?e[0]:function(t){return e.reduce((r,o)=>o(r),t)}}let Yt=(()=>{class e{constructor(t){t&&(this._subscribe=t)}lift(t){const r=new e;return r.source=this,r.operator=t,r}subscribe(t,r,o){const i=function Ks(e){return e&&e instanceof Xr||function Zs(e){return e&&nt(e.next)&&nt(e.error)&&nt(e.complete)}(e)&&L(e)}(t)?t:new Rr(t,r,o);return qo(()=>{const{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(t){try{return this._subscribe(t)}catch(r){t.error(r)}}forEach(t,r){return new(r=Yi(r))((o,i)=>{const s=new Rr({next:a=>{try{t(a)}catch(l){i(l),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(t){var r;return null===(r=this.source)||void 0===r?void 0:r.subscribe(t)}[Yo](){return this}pipe(...t){return hi(t)(this)}toPromise(t){return new(t=Yi(t))((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=n=>new e(n),e})();function Yi(e){var n;return null!==(n=e??Ie.Promise)&&void 0!==n?n:Promise}const Zi=Wt(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});let jt=(()=>{class e extends Yt{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(t){const r=new Qs(this,this);return r.operator=t,r}_throwIfClosed(){if(this.closed)throw new Zi}next(t){qo(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(const r of this.currentObservers)r.next(t)}})}error(t){qo(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=t;const{observers:r}=this;for(;r.length;)r.shift().error(t)}})}complete(){qo(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;const{observers:t}=this;for(;t.length;)t.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var t;return(null===(t=this.observers)||void 0===t?void 0:t.length)>0}_trySubscribe(t){return this._throwIfClosed(),super._trySubscribe(t)}_subscribe(t){return this._throwIfClosed(),this._checkFinalizedStatuses(t),this._innerSubscribe(t)}_innerSubscribe(t){const{hasError:r,isStopped:o,observers:i}=this;return r||o?Q:(this.currentObservers=null,i.push(t),new Ze(()=>{this.currentObservers=null,yn(i,t)}))}_checkFinalizedStatuses(t){const{hasError:r,thrownError:o,isStopped:i}=this;r?t.error(o):i&&t.complete()}asObservable(){const t=new Yt;return t.source=this,t}}return e.create=(n,t)=>new Qs(n,t),e})();class Qs extends jt{constructor(n,t){super(),this.destination=n,this.source=t}next(n){var t,r;null===(r=null===(t=this.destination)||void 0===t?void 0:t.next)||void 0===r||r.call(t,n)}error(n){var t,r;null===(r=null===(t=this.destination)||void 0===t?void 0:t.error)||void 0===r||r.call(t,n)}complete(){var n,t;null===(t=null===(n=this.destination)||void 0===n?void 0:n.complete)||void 0===t||t.call(n)}_subscribe(n){var t,r;return null!==(r=null===(t=this.source)||void 0===t?void 0:t.subscribe(n))&&void 0!==r?r:Q}}class wn extends jt{constructor(n){super(),this._value=n}get value(){return this.getValue()}_subscribe(n){const t=super._subscribe(n);return!t.closed&&n.next(this._value),t}getValue(){const{hasError:n,thrownError:t,_value:r}=this;if(n)throw t;return this._throwIfClosed(),r}next(n){super.next(this._value=n)}}function Js(e){return nt(e?.lift)}function hn(e){return n=>{if(Js(n))return n.lift(function(t){try{return e(t,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function en(e,n,t,r,o){return new Zo(e,n,t,r,o)}class Zo extends Xr{constructor(n,t,r,o,i,s){super(n),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=t?function(a){try{t(a)}catch(l){n.error(l)}}:super._next,this._error=o?function(a){try{o(a)}catch(l){n.error(l)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){n.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var n;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){const{closed:t}=this;super.unsubscribe(),!t&&(null===(n=this.onFinalize)||void 0===n||n.call(this))}}}function ct(e,n){return hn((t,r)=>{let o=0;t.subscribe(en(r,i=>{r.next(e.call(n,i,o++))}))})}class z extends Error{constructor(n,t){super(function mn(e,n){return`NG0${Math.abs(e)}${n?": "+n:""}`}(n,t)),this.code=n}}function Gn(e){return{toString:e}.toString()}const Dr="__parameters__";function br(e,n,t){return Gn(()=>{const r=function oo(e){return function(...t){if(e){const r=e(...t);for(const o in r)this[o]=r[o]}}}(n);function o(...i){if(this instanceof o)return r.apply(this,i),this;const s=new o(...i);return a.annotation=s,a;function a(l,c,d){const h=l.hasOwnProperty(Dr)?l[Dr]:Object.defineProperty(l,Dr,{value:[]})[Dr];for(;h.length<=d;)h.push(null);return(h[d]=h[d]||[]).push(s),l}}return t&&(o.prototype=Object.create(t.prototype)),o.prototype.ngMetadataName=e,o.annotationCls=o,o})}const S=globalThis;function Qe(e){for(let n in e)if(e[n]===Qe)return n;throw Error("Could not find renamed property on target object.")}function On(e,n){for(const t in n)n.hasOwnProperty(t)&&!e.hasOwnProperty(t)&&(e[t]=n[t])}function At(e){if("string"==typeof e)return e;if(Array.isArray(e))return"["+e.map(At).join(", ")+"]";if(null==e)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;const n=e.toString();if(null==n)return""+n;const t=n.indexOf("\n");return-1===t?n:n.substring(0,t)}function Pr(e,n){return null==e||""===e?null===n?"":n:null==n||""===n?e:e+" "+n}const Zh=Qe({__forward_ref__:Qe});function kt(e){return e.__forward_ref__=kt,e.toString=function(){return At(this())},e}function et(e){return na(e)?e():e}function na(e){return"function"==typeof e&&e.hasOwnProperty(Zh)&&e.__forward_ref__===kt}function Me(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function Zr(e){return{providers:e.providers||[],imports:e.imports||[]}}function gi(e){return Rl(e,mi)||Rl(e,Uu)}function Rl(e,n){return e.hasOwnProperty(n)?e[n]:null}function Ji(e){return e&&(e.hasOwnProperty(Nl)||e.hasOwnProperty(np))?e[Nl]:null}const mi=Qe({\u0275prov:Qe}),Nl=Qe({\u0275inj:Qe}),Uu=Qe({ngInjectableDef:Qe}),np=Qe({ngInjectorDef:Qe});class ce{constructor(n,t){this._desc=n,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,"number"==typeof t?this.__NG_ELEMENT_ID__=t:void 0!==t&&(this.\u0275prov=Me({token:this,providedIn:t.providedIn||"root",factory:t.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}}function ia(e){return e&&!!e.\u0275providers}const es=Qe({\u0275cmp:Qe}),sa=Qe({\u0275dir:Qe}),aa=Qe({\u0275pipe:Qe}),Gu=Qe({\u0275mod:Qe}),kr=Qe({\u0275fac:Qe}),vi=Qe({__NG_ELEMENT_ID__:Qe}),ts=Qe({__NG_ENV_ID__:Qe});function tt(e){return"string"==typeof e?e:null==e?"":String(e)}function da(e,n){throw new z(-201,!1)}var ut=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(ut||{});let fa;function Ll(){return fa}function qn(e){const n=fa;return fa=e,n}function ns(e,n,t){const r=gi(e);return r&&"root"==r.providedIn?void 0===r.value?r.value=r.factory():r.value:t&ut.Optional?null:void 0!==n?n:void da()}const ir={},pa="__NG_DI_FLAG__",os="ngTempTokenPath",Wu=/\n/gm,$l="__source";let lo;function co(e){const n=lo;return lo=e,n}function ip(e,n=ut.Default){if(void 0===lo)throw new z(-203,!1);return null===lo?ns(e,void 0,n):lo.get(e,n&ut.Optional?null:void 0,n)}function Pe(e,n=ut.Default){return(Ll()||ip)(et(e),n)}function U(e,n=ut.Default){return Pe(e,ga(n))}function ga(e){return typeof e>"u"||"number"==typeof e?e:(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function ma(e){const n=[];for(let t=0;t<e.length;t++){const r=et(e[t]);if(Array.isArray(r)){if(0===r.length)throw new z(900,!1);let o,i=ut.Default;for(let s=0;s<r.length;s++){const a=r[s],l=sp(a);"number"==typeof l?-1===l?o=a.token:i|=l:o=a}n.push(Pe(o,i))}else n.push(Pe(r))}return n}function sr(e,n){return e[pa]=n,e.prototype[pa]=n,e}function sp(e){return e[pa]}const Bl=sr(br("Optional"),8),Ul=sr(br("SkipSelf"),4);function So(e,n){return e.hasOwnProperty(kr)?e[kr]:null}function yi(e,n){e.forEach(t=>Array.isArray(t)?yi(t,n):n(t))}function Hl(e,n,t){n>=e.length?e.push(t):e.splice(n,0,t)}function va(e,n){return n>=e.length-1?e.pop():e.splice(n,1)[0]}function p(e,n,t){let r=_(e,n);return r>=0?e[1|r]=t:(r=~r,function u(e,n,t,r){let o=e.length;if(o==n)e.push(t,r);else if(1===o)e.push(r,e[0]),e[0]=t;else{for(o--,e.push(e[o-1],e[o]);o>n;)e[o]=e[o-2],o--;e[n]=t,e[n+1]=r}}(e,r,n,t)),r}function m(e,n){const t=_(e,n);if(t>=0)return e[1|t]}function _(e,n){return function N(e,n,t){let r=0,o=e.length>>t;for(;o!==r;){const i=r+(o-r>>1),s=e[i<<t];if(n===s)return i<<t;s>n?o=i:r=i+1}return~(o<<t)}(e,n,1)}const E={},T=[],w=new ce(""),F=new ce("",-1),fe=new ce("");class ue{get(n,t=ir){if(t===ir){const r=new Error(`NullInjectorError: No provider for ${At(n)}!`);throw r.name="NullInjectorError",r}return t}}var Ve=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Ve||{}),Se=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Se||{}),_t=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(_t||{});function Et(e,n,t){let r=e.length;for(;;){const o=e.indexOf(n,t);if(-1===o)return o;if(0===o||e.charCodeAt(o-1)<=32){const i=n.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}t=o+1}}function wt(e,n,t){let r=0;for(;r<t.length;){const o=t[r];if("number"==typeof o){if(0!==o)break;r++;const i=t[r++],s=t[r++],a=t[r++];e.setAttribute(n,s,a,i)}else{const i=o,s=t[++r];Kr(i)?e.setProperty(n,i,s):e.setAttribute(n,i,s),r++}}return r}function Er(e){return 3===e||4===e||6===e}function Kr(e){return 64===e.charCodeAt(0)}function Lt(e,n){if(null!==n&&0!==n.length)if(null===e||0===e.length)e=n.slice();else{let t=-1;for(let r=0;r<n.length;r++){const o=n[r];"number"==typeof o?t=o:0===t||$t(e,t,o,null,-1===t||2===t?n[++r]:null)}}return e}function $t(e,n,t,r,o){let i=0,s=e.length;if(-1===n)s=-1;else for(;i<e.length;){const a=e[i++];if("number"==typeof a){if(a===n){s=-1;break}if(a>n){s=i-1;break}}}for(;i<e.length;){const a=e[i];if("number"==typeof a)break;if(a===t){if(null===r)return void(null!==o&&(e[i+1]=o));if(r===e[i+1])return void(e[i+2]=o)}i++,null!==r&&i++,null!==o&&i++}-1!==s&&(e.splice(s,0,n),i=s+1),e.splice(i++,0,t),null!==r&&e.splice(i++,0,r),null!==o&&e.splice(i++,0,o)}const Ci="ng-template";function In(e,n,t,r){let o=0;if(r){for(;o<n.length&&"string"==typeof n[o];o+=2)if("class"===n[o]&&-1!==Et(n[o+1].toLowerCase(),t,0))return!0}else if(Vt(e))return!1;if(o=n.indexOf(1,o),o>-1){let i;for(;++o<n.length&&"string"==typeof(i=n[o]);)if(i.toLowerCase()===t)return!0}return!1}function Vt(e){return 4===e.type&&e.value!==Ci}function ar(e,n,t){return n===(4!==e.type||t?e.value:Ci)}function Rn(e,n,t){let r=4;const o=e.attrs,i=null!==o?function xR(e){for(let n=0;n<e.length;n++)if(Er(e[n]))return n;return e.length}(o):0;let s=!1;for(let a=0;a<n.length;a++){const l=n[a];if("number"!=typeof l){if(!s)if(4&r){if(r=2|1&r,""!==l&&!ar(e,l,t)||""===l&&1===n.length){if(Yn(r))return!1;s=!0}}else if(8&r){if(null===o||!In(e,o,l,t)){if(Yn(r))return!1;s=!0}}else{const c=n[++a],d=up(l,o,Vt(e),t);if(-1===d){if(Yn(r))return!1;s=!0;continue}if(""!==c){let h;if(h=d>i?"":o[d+1].toLowerCase(),2&r&&c!==h){if(Yn(r))return!1;s=!0}}}}else{if(!s&&!Yn(r)&&!Yn(l))return!1;if(s&&Yn(l))continue;s=!1,r=l|1&r}}return Yn(r)||s}function Yn(e){return!(1&e)}function up(e,n,t,r){if(null===n)return-1;let o=0;if(r||!t){let i=!1;for(;o<n.length;){const s=n[o];if(s===e)return o;if(3===s||6===s)i=!0;else{if(1===s||2===s){let a=n[++o];for(;"string"==typeof a;)a=n[++o];continue}if(4===s)break;if(0===s){o+=4;continue}}o+=i?1:2}return-1}return function OR(e,n){let t=e.indexOf(4);if(t>-1)for(t++;t<e.length;){const r=e[t];if("number"==typeof r)return-1;if(r===n)return t;t++}return-1}(n,e)}function Gl(e,n,t=!1){for(let r=0;r<n.length;r++)if(Rn(e,n[r],t))return!0;return!1}function RR(e,n){e:for(let t=0;t<n.length;t++){const r=n[t];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function mD(e,n){return e?":not("+n.trim()+")":n}function NR(e){let n=e[0],t=1,r=2,o="",i=!1;for(;t<e.length;){let s=e[t];if("string"==typeof s)if(2&r){const a=e[++t];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else 8&r?o+="."+s:4&r&&(o+=" "+s);else""!==o&&!Yn(s)&&(n+=mD(i,o),o=""),r=s,i=i||!Yn(r);t++}return""!==o&&(n+=mD(i,o)),n}function le(e){return Gn(()=>{const n=yD(e),t={...n,decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Ve.OnPush,directiveDefs:null,pipeDefs:null,dependencies:n.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Se.Emulated,styles:e.styles||T,_:null,schemas:e.schemas||null,tView:null,id:""};CD(t);const r=e.dependencies;return t.directiveDefs=Yu(r,!1),t.pipeDefs=Yu(r,!0),t.id=function VR(e){let n=0;const t=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(const o of t)n=Math.imul(31,n)+o.charCodeAt(0)|0;return n+=2147483648,"c"+n}(t),t})}function FR(e){return Ct(e)||Nn(e)}function LR(e){return null!==e}function is(e){return Gn(()=>({type:e.type,bootstrap:e.bootstrap||T,declarations:e.declarations||T,imports:e.imports||T,exports:e.exports||T,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function vD(e,n){if(null==e)return E;const t={};for(const r in e)if(e.hasOwnProperty(r)){const o=e[r];let i,s,a=_t.None;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i):(i=o,s=o),n?(t[i]=a!==_t.None?[r,a]:r,n[i]=s):t[i]=r}return t}function Fe(e){return Gn(()=>{const n=yD(e);return CD(n),n})}function lr(e){return{type:e.type,name:e.name,factory:null,pure:!1!==e.pure,standalone:!0===e.standalone,onDestroy:e.type.prototype.ngOnDestroy||null}}function Ct(e){return e[es]||null}function Nn(e){return e[sa]||null}function jn(e){return e[aa]||null}function Zn(e,n){const t=e[Gu]||null;if(!t&&!0===n)throw new Error(`Type ${At(e)} does not have '\u0275mod' property.`);return t}function yD(e){const n={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:n,inputTransforms:null,inputConfig:e.inputs||E,exportAs:e.exportAs||null,standalone:!0===e.standalone,signals:!0===e.signals,selectors:e.selectors||T,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:vD(e.inputs,n),outputs:vD(e.outputs),debugInfo:null}}function CD(e){e.features?.forEach(n=>n(e))}function Yu(e,n){if(!e)return null;const t=n?jn:FR;return()=>("function"==typeof e?e():e).map(r=>t(r)).filter(LR)}function Ca(e){return{\u0275providers:e}}function _D(...e){return{\u0275providers:fp(0,e),\u0275fromNgModule:!0}}function fp(e,...n){const t=[],r=new Set;let o;const i=s=>{t.push(s)};return yi(n,s=>{const a=s;Zu(a,i,[],r)&&(o||=[],o.push(a))}),void 0!==o&&DD(o,i),t}function DD(e,n){for(let t=0;t<e.length;t++){const{ngModule:r,providers:o}=e[t];hp(o,i=>{n(i,r)})}}function Zu(e,n,t,r){if(!(e=et(e)))return!1;let o=null,i=Ji(e);const s=!i&&Ct(e);if(i||s){if(s&&!s.standalone)return!1;o=e}else{const l=e.ngModule;if(i=Ji(l),!i)return!1;o=l}const a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){const l="function"==typeof s.dependencies?s.dependencies():s.dependencies;for(const c of l)Zu(c,n,t,r)}}else{if(!i)return!1;{if(null!=i.imports&&!a){let c;r.add(o);try{yi(i.imports,d=>{Zu(d,n,t,r)&&(c||=[],c.push(d))})}finally{}void 0!==c&&DD(c,n)}if(!a){const c=So(o)||(()=>new o);n({provide:o,useFactory:c,deps:T},o),n({provide:fe,useValue:o,multi:!0},o),n({provide:w,useValue:()=>Pe(o),multi:!0},o)}const l=i.providers;if(null!=l&&!a){const c=e;hp(l,d=>{n(d,c)})}}}return o!==e&&void 0!==e.providers}function hp(e,n){for(let t of e)ia(t)&&(t=t.\u0275providers),Array.isArray(t)?hp(t,n):n(t)}const BR=Qe({provide:String,useValue:Qe});function pp(e){return null!==e&&"object"==typeof e&&BR in e}function ss(e){return"function"==typeof e}const gp=new ce(""),Ku={},UR={};let mp;function Qu(){return void 0===mp&&(mp=new ue),mp}class Pn{}class _a extends Pn{get destroyed(){return this._destroyed}constructor(n,t,r,o){super(),this.parent=t,this.source=r,this.scopes=o,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,yp(n,s=>this.processProvider(s)),this.records.set(F,Da(void 0,this)),o.has("environment")&&this.records.set(Pn,Da(void 0,this));const i=this.records.get(gp);null!=i&&"string"==typeof i.value&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(fe,T,ut.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;const n=Z(null);try{for(const r of this._ngOnDestroyHooks)r.ngOnDestroy();const t=this._onDestroyHooks;this._onDestroyHooks=[];for(const r of t)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),Z(n)}}onDestroy(n){return this.assertNotDestroyed(),this._onDestroyHooks.push(n),()=>this.removeOnDestroy(n)}runInContext(n){this.assertNotDestroyed();const t=co(this),r=qn(void 0);try{return n()}finally{co(t),qn(r)}}get(n,t=ir,r=ut.Default){if(this.assertNotDestroyed(),n.hasOwnProperty(ts))return n[ts](this);r=ga(r);const i=co(this),s=qn(void 0);try{if(!(r&ut.SkipSelf)){let l=this.records.get(n);if(void 0===l){const c=function qR(e){return"function"==typeof e||"object"==typeof e&&e instanceof ce}(n)&&gi(n);l=c&&this.injectableDefInScope(c)?Da(vp(n),Ku):null,this.records.set(n,l)}if(null!=l)return this.hydrate(n,l)}return(r&ut.Self?Qu():this.parent).get(n,t=r&ut.Optional&&t===ir?null:t)}catch(a){if("NullInjectorError"===a.name){if((a[os]=a[os]||[]).unshift(At(n)),i)throw a;return function ap(e,n,t,r){const o=e[os];throw n[$l]&&o.unshift(n[$l]),e.message=function Vl(e,n,t,r=null){e=e&&"\n"===e.charAt(0)&&"\u0275"==e.charAt(1)?e.slice(2):e;let o=At(n);if(Array.isArray(n))o=n.map(At).join(" -> ");else if("object"==typeof n){let i=[];for(let s in n)if(n.hasOwnProperty(s)){let a=n[s];i.push(s+":"+("string"==typeof a?JSON.stringify(a):At(a)))}o=`{${i.join(", ")}}`}return`${t}${r?"("+r+")":""}[${o}]: ${e.replace(Wu,"\n  ")}`}("\n"+e.message,o,t,r),e.ngTokenPath=o,e[os]=null,e}(a,n,"R3InjectorError",this.source)}throw a}finally{qn(s),co(i)}}resolveInjectorInitializers(){const n=Z(null),t=co(this),r=qn(void 0);try{const i=this.get(w,T,ut.Self);for(const s of i)s()}finally{co(t),qn(r),Z(n)}}toString(){const n=[],t=this.records;for(const r of t.keys())n.push(At(r));return`R3Injector[${n.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new z(205,!1)}processProvider(n){let t=ss(n=et(n))?n:et(n&&n.provide);const r=function zR(e){return pp(e)?Da(void 0,e.useValue):Da(wD(e),Ku)}(n);if(!ss(n)&&!0===n.multi){let o=this.records.get(t);o||(o=Da(void 0,Ku,!0),o.factory=()=>ma(o.multi),this.records.set(t,o)),t=n,o.multi.push(n)}this.records.set(t,r)}hydrate(n,t){const r=Z(null);try{return t.value===Ku&&(t.value=UR,t.value=t.factory()),"object"==typeof t.value&&t.value&&function WR(e){return null!==e&&"object"==typeof e&&"function"==typeof e.ngOnDestroy}(t.value)&&this._ngOnDestroyHooks.add(t.value),t.value}finally{Z(r)}}injectableDefInScope(n){if(!n.providedIn)return!1;const t=et(n.providedIn);return"string"==typeof t?"any"===t||this.scopes.has(t):this.injectorDefTypes.has(t)}removeOnDestroy(n){const t=this._onDestroyHooks.indexOf(n);-1!==t&&this._onDestroyHooks.splice(t,1)}}function vp(e){const n=gi(e),t=null!==n?n.factory:So(e);if(null!==t)return t;if(e instanceof ce)throw new z(204,!1);if(e instanceof Function)return function HR(e){if(e.length>0)throw new z(204,!1);const t=function ju(e){return e&&(e[mi]||e[Uu])||null}(e);return null!==t?()=>t.factory(e):()=>new e}(e);throw new z(204,!1)}function wD(e,n,t){let r;if(ss(e)){const o=et(e);return So(o)||vp(o)}if(pp(e))r=()=>et(e.useValue);else if(function ED(e){return!(!e||!e.useFactory)}(e))r=()=>e.useFactory(...ma(e.deps||[]));else if(function bD(e){return!(!e||!e.useExisting)}(e))r=()=>Pe(et(e.useExisting));else{const o=et(e&&(e.useClass||e.provide));if(!function GR(e){return!!e.deps}(e))return So(o)||vp(o);r=()=>new o(...ma(e.deps))}return r}function Da(e,n,t=!1){return{factory:e,value:n,multi:t?[]:void 0}}function yp(e,n){for(const t of e)Array.isArray(t)?yp(t,n):t&&ia(t)?yp(t.\u0275providers,n):n(t)}function Ko(e,n){e instanceof _a&&e.assertNotDestroyed();const r=co(e),o=qn(void 0);try{return n()}finally{co(r),qn(o)}}const dn=0,he=1,Ye=2,Sn=3,uo=4,Un=5,cr=6,Ea=7,tn=8,Mn=9,Mo=10,rt=11,ql=12,MD=13,wa=14,fn=15,as=16,Ia=17,Qo=18,Sa=19,TD=20,Di=21,ed=22,Qr=23,Be=25,Dp=1,To=7,Ma=9,Cn=10;var nd=function(e){return e[e.None=0]="None",e[e.HasTransplantedViews=2]="HasTransplantedViews",e}(nd||{});function Fn(e){return Array.isArray(e)&&"object"==typeof e[Dp]}function Kn(e){return Array.isArray(e)&&!0===e[Dp]}function bp(e){return!!(4&e.flags)}function ls(e){return e.componentOffset>-1}function rd(e){return!(1&~e.flags)}function fo(e){return!!e.template}function Yl(e){return!!(512&e[Ye])}class iN{constructor(n,t,r){this.previousValue=n,this.currentValue=t,this.firstChange=r}isFirstChange(){return this.firstChange}}function ND(e,n,t,r){null!==n?n.applyValueToInputSignal(n,r):e[t]=r}function wr(){return PD}function PD(e){return e.type.prototype.ngOnChanges&&(e.setInput=aN),sN}function sN(){const e=FD(this),n=e?.current;if(n){const t=e.previous;if(t===E)e.previous=n;else for(let r in n)t[r]=n[r];e.current=null,this.ngOnChanges(n)}}function aN(e,n,t,r,o){const i=this.declaredInputs[r],s=FD(e)||function lN(e,n){return e[kD]=n}(e,{previous:E,current:null}),a=s.current||(s.current={}),l=s.previous,c=l[i];a[i]=new iN(c&&c.currentValue,t,l===E),ND(e,n,o,t)}wr.ngInherit=!0;const kD="__ngSimpleChanges__";function FD(e){return e[kD]||null}const Ao=function(e,n,t){};function Mt(e){for(;Array.isArray(e);)e=e[dn];return e}function Zl(e,n){return Mt(n[e])}function ur(e,n){return Mt(n[e.index])}function Kl(e,n){return e.data[n]}function cs(e,n){return e[n]}function Fr(e,n){const t=n[e];return Fn(t)?t:t[dn]}function Mp(e){return!(128&~e[Ye])}function Jr(e,n){return null==n?null:e[n]}function VD(e){e[Ia]=0}function BD(e){1024&e[Ye]||(e[Ye]|=1024,Mp(e)&&id(e))}function od(e){return!!(9216&e[Ye]||e[Qr]?.dirty)}function Tp(e){e[Mo].changeDetectionScheduler?.notify(8),64&e[Ye]&&(e[Ye]|=1024),od(e)&&id(e)}function id(e){e[Mo].changeDetectionScheduler?.notify(0);let n=Jo(e);for(;null!==n&&!(8192&n[Ye])&&(n[Ye]|=8192,Mp(n));)n=Jo(n)}function sd(e,n){if(!(256&~e[Ye]))throw new z(911,!1);null===e[Di]&&(e[Di]=[]),e[Di].push(n)}function Jo(e){const n=e[Sn];return Kn(n)?n[Sn]:n}const ot={lFrame:JD(null),bindingsEnabled:!0,skipHydrationRootTNode:null};let UD=!1;function HD(){return ot.bindingsEnabled}function us(){return null!==ot.skipHydrationRootTNode}function $(){return ot.lFrame.lView}function mt(){return ot.lFrame.tView}function ds(e){return ot.lFrame.contextLView=e,e[tn]}function fs(e){return ot.lFrame.contextLView=null,e}function Bt(){let e=zD();for(;null!==e&&64===e.type;)e=e.parent;return e}function zD(){return ot.lFrame.currentTNode}function ho(e,n){const t=ot.lFrame;t.currentTNode=e,t.isParent=n}function Op(){return ot.lFrame.isParent}function Rp(){ot.lFrame.isParent=!1}function qD(){return UD}function XD(e){UD=e}function dr(){const e=ot.lFrame;let n=e.bindingRootIndex;return-1===n&&(n=e.bindingRootIndex=e.tView.bindingStartIndex),n}function po(){return ot.lFrame.bindingIndex++}function ti(e){const n=ot.lFrame,t=n.bindingIndex;return n.bindingIndex=n.bindingIndex+e,t}function bN(e,n){const t=ot.lFrame;t.bindingIndex=t.bindingRootIndex=e,Np(n)}function Np(e){ot.lFrame.currentDirectiveIndex=e}function ld(e){ot.lFrame.currentQueryIndex=e}function wN(e){const n=e[he];return 2===n.type?n.declTNode:1===n.type?e[Un]:null}function KD(e,n,t){if(t&ut.SkipSelf){let o=n,i=e;for(;!(o=o.parent,null!==o||t&ut.Host||(o=wN(i),null===o||(i=i[wa],10&o.type))););if(null===o)return!1;n=o,e=i}const r=ot.lFrame=QD();return r.currentTNode=n,r.lView=e,!0}function Fp(e){const n=QD(),t=e[he];ot.lFrame=n,n.currentTNode=t.firstChild,n.lView=e,n.tView=t,n.contextLView=e,n.bindingIndex=t.bindingStartIndex,n.inI18n=!1}function QD(){const e=ot.lFrame,n=null===e?null:e.child;return null===n?JD(e):n}function JD(e){const n={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return null!==e&&(e.child=n),n}function eb(){const e=ot.lFrame;return ot.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}const tb=eb;function Lp(){const e=eb();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function Hn(){return ot.lFrame.selectedIndex}function hs(e){ot.lFrame.selectedIndex=e}function nn(){const e=ot.lFrame;return Kl(e.tView,e.selectedIndex)}let rb=!0;function Jl(){return rb}function xo(e){rb=e}function cd(e,n){for(let t=n.directiveStart,r=n.directiveEnd;t<r;t++){const i=e.data[t].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:l,ngAfterViewChecked:c,ngOnDestroy:d}=i;s&&(e.contentHooks??=[]).push(-t,s),a&&((e.contentHooks??=[]).push(t,a),(e.contentCheckHooks??=[]).push(t,a)),l&&(e.viewHooks??=[]).push(-t,l),c&&((e.viewHooks??=[]).push(t,c),(e.viewCheckHooks??=[]).push(t,c)),null!=d&&(e.destroyHooks??=[]).push(t,d)}}function ud(e,n,t){ob(e,n,3,t)}function dd(e,n,t,r){(3&e[Ye])===t&&ob(e,n,t,r)}function $p(e,n){let t=e[Ye];(3&t)===n&&(t&=16383,t+=1,e[Ye]=t)}function ob(e,n,t,r){const i=r??-1,s=n.length-1;let a=0;for(let l=void 0!==r?65535&e[Ia]:0;l<s;l++)if("number"==typeof n[l+1]){if(a=n[l],null!=r&&a>=r)break}else n[l]<0&&(e[Ia]+=65536),(a<i||-1==i)&&(ON(e,t,n,l),e[Ia]=(**********&e[Ia])+l+2),l++}function ib(e,n){Ao(4,e,n);const t=Z(null);try{n.call(e)}finally{Z(t),Ao(5,e,n)}}function ON(e,n,t,r){const o=t[r]<0,i=t[r+1],a=e[o?-t[r]:t[r]];o?e[Ye]>>14<e[Ia]>>16&&(3&e[Ye])===n&&(e[Ye]+=16384,ib(a,i)):ib(a,i)}const Ta=-1;class ec{constructor(n,t,r){this.factory=n,this.resolving=!1,this.canSeeViewProviders=t,this.injectImpl=r}}const Bp={};class ps{constructor(n,t){this.injector=n,this.parentInjector=t}get(n,t,r){r=ga(r);const o=this.injector.get(n,Bp,r);return o!==Bp||t===Bp?o:this.parentInjector.get(n,t,r)}}function jp(e){return e!==Ta}function tc(e){return 32767&e}function nc(e,n){let t=function FN(e){return e>>16}(e),r=n;for(;t>0;)r=r[wa],t--;return r}let Up=!0;function fd(e){const n=Up;return Up=e,n}const ab=255,lb=5;let $N=0;const Oo={};function hd(e,n){const t=cb(e,n);if(-1!==t)return t;const r=n[he];r.firstCreatePass&&(e.injectorIndex=n.length,Hp(r.data,e),Hp(n,null),Hp(r.blueprint,null));const o=pd(e,n),i=e.injectorIndex;if(jp(o)){const s=tc(o),a=nc(o,n),l=a[he].data;for(let c=0;c<8;c++)n[i+c]=a[s+c]|l[s+c]}return n[i+8]=o,i}function Hp(e,n){e.push(0,0,0,0,0,0,0,0,n)}function cb(e,n){return-1===e.injectorIndex||e.parent&&e.parent.injectorIndex===e.injectorIndex||null===n[e.injectorIndex+8]?-1:e.injectorIndex}function pd(e,n){if(e.parent&&-1!==e.parent.injectorIndex)return e.parent.injectorIndex;let t=0,r=null,o=n;for(;null!==o;){if(r=mb(o),null===r)return Ta;if(t++,o=o[wa],-1!==r.injectorIndex)return r.injectorIndex|t<<16}return Ta}function zp(e,n,t){!function VN(e,n,t){let r;"string"==typeof t?r=t.charCodeAt(0)||0:t.hasOwnProperty(vi)&&(r=t[vi]),null==r&&(r=t[vi]=$N++);const o=r&ab;n.data[e+(o>>lb)]|=1<<o}(e,n,t)}function ub(e,n,t){if(t&ut.Optional||void 0!==e)return e;da()}function db(e,n,t,r){if(t&ut.Optional&&void 0===r&&(r=null),!(t&(ut.Self|ut.Host))){const o=e[Mn],i=qn(void 0);try{return o?o.get(n,r,t&ut.Optional):ns(n,r,t&ut.Optional)}finally{qn(i)}}return ub(r,0,t)}function fb(e,n,t,r=ut.Default,o){if(null!==e){if(2048&n[Ye]&&!(r&ut.Self)){const s=function zN(e,n,t,r,o){let i=e,s=n;for(;null!==i&&null!==s&&2048&s[Ye]&&!(512&s[Ye]);){const a=hb(i,s,t,r|ut.Self,Oo);if(a!==Oo)return a;let l=i.parent;if(!l){const c=s[TD];if(c){const d=c.get(t,Oo,r);if(d!==Oo)return d}l=mb(s),s=s[wa]}i=l}return o}(e,n,t,r,Oo);if(s!==Oo)return s}const i=hb(e,n,t,r,Oo);if(i!==Oo)return i}return db(n,t,r,o)}function hb(e,n,t,r,o){const i=function UN(e){if("string"==typeof e)return e.charCodeAt(0)||0;const n=e.hasOwnProperty(vi)?e[vi]:void 0;return"number"==typeof n?n>=0?n&ab:HN:n}(t);if("function"==typeof i){if(!KD(n,e,r))return r&ut.Host?ub(o,0,r):db(n,t,r,o);try{let s;if(s=i(r),null!=s||r&ut.Optional)return s;da()}finally{tb()}}else if("number"==typeof i){let s=null,a=cb(e,n),l=Ta,c=r&ut.Host?n[fn][Un]:null;for((-1===a||r&ut.SkipSelf)&&(l=-1===a?pd(e,n):n[a+8],l!==Ta&&gb(r,!1)?(s=n[he],a=tc(l),n=nc(l,n)):a=-1);-1!==a;){const d=n[he];if(pb(i,a,d.data)){const h=jN(a,n,t,s,r,c);if(h!==Oo)return h}l=n[a+8],l!==Ta&&gb(r,n[he].data[a+8]===c)&&pb(i,a,n)?(s=d,a=tc(l),n=nc(l,n)):a=-1}}return o}function jN(e,n,t,r,o,i){const s=n[he],a=s.data[e+8],d=function gd(e,n,t,r,o){const i=e.providerIndexes,s=n.data,a=1048575&i,l=e.directiveStart,d=i>>20,v=o?a+d:e.directiveEnd;for(let y=r?a:a+d;y<v;y++){const C=s[y];if(y<l&&t===C||y>=l&&C.type===t)return y}if(o){const y=s[l];if(y&&fo(y)&&y.type===t)return l}return null}(a,s,t,null==r?ls(a)&&Up:r!=s&&!!(3&a.type),o&ut.Host&&i===a);return null!==d?gs(n,s,d,a):Oo}function gs(e,n,t,r){let o=e[t];const i=n.data;if(function RN(e){return e instanceof ec}(o)){const s=o;s.resolving&&function ca(e,n){throw n&&n.join(" > "),new z(-200,e)}(function yt(e){return"function"==typeof e?e.name||e.toString():"object"==typeof e&&null!=e&&"function"==typeof e.type?e.type.name||e.type.toString():tt(e)}(i[t]));const a=fd(s.canSeeViewProviders);s.resolving=!0;const c=s.injectImpl?qn(s.injectImpl):null;KD(e,r,ut.Default);try{o=e[t]=s.factory(void 0,i,e,r),n.firstCreatePass&&t>=r.directiveStart&&function xN(e,n,t){const{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=n.type.prototype;if(r){const s=PD(n);(t.preOrderHooks??=[]).push(e,s),(t.preOrderCheckHooks??=[]).push(e,s)}o&&(t.preOrderHooks??=[]).push(0-e,o),i&&((t.preOrderHooks??=[]).push(e,i),(t.preOrderCheckHooks??=[]).push(e,i))}(t,i[t],n)}finally{null!==c&&qn(c),fd(a),s.resolving=!1,tb()}}return o}function pb(e,n,t){return!!(t[n+(e>>lb)]&1<<e)}function gb(e,n){return!(e&ut.Self||e&ut.Host&&n)}class Ln{constructor(n,t){this._tNode=n,this._lView=t}get(n,t,r){return fb(this._tNode,this._lView,n,ga(r),t)}}function HN(){return new Ln(Bt(),$())}function Ut(e){return Gn(()=>{const n=e.prototype.constructor,t=n[kr]||Gp(n),r=Object.prototype;let o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){const i=o[kr]||Gp(o);if(i&&i!==t)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Gp(e){return na(e)?()=>{const n=Gp(et(e));return n&&n()}:So(e)}function mb(e){const n=e[he],t=n.type;return 2===t?n.declTNode:1===t?e[Un]:null}function Db(e,n=null,t=null,r){const o=bb(e,n,t,r);return o.resolveInjectorInitializers(),o}function bb(e,n=null,t=null,r,o=new Set){const i=[t||T,_D(e)];return r=r||("object"==typeof e?void 0:At(e)),new _a(i,n||Qu(),r||null,o)}class Zt{static{this.THROW_IF_NOT_FOUND=ir}static{this.NULL=new ue}static create(n,t){if(Array.isArray(n))return Db({name:""},t,n,"");{const r=n.name??"";return Db({name:r},n.parent,n.providers,r)}}static{this.\u0275prov=Me({token:Zt,providedIn:"any",factory:()=>Pe(F)})}static{this.__NG_ELEMENT_ID__=-1}}new ce("").__NG_ELEMENT_ID__=e=>{const n=Bt();if(null===n)throw new z(204,!1);if(2&n.type)return n.value;if(e&ut.Optional)return null;throw new z(204,!1)};function qp(e){return e.ngOriginalError}const wb=!0;let rc=(()=>{class e{static{this.__NG_ELEMENT_ID__=e1}static{this.__NG_ENV_ID__=t=>t}}return e})();class JN extends rc{constructor(n){super(),this._lView=n}onDestroy(n){return sd(this._lView,n),()=>function Ap(e,n){if(null===e[Di])return;const t=e[Di].indexOf(n);-1!==t&&e[Di].splice(t,1)}(this._lView,n)}}function e1(){return new JN($())}let bi=(()=>{class e{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new wn(!1)}get _hasPendingTasks(){return this.hasPendingTasks.value}add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);const t=this.taskId++;return this.pendingTasks.add(t),t}remove(t){this.pendingTasks.delete(t),0===this.pendingTasks.size&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static{this.\u0275prov=Me({token:e,providedIn:"root",factory:()=>new e})}}return e})();const Dt=class t1 extends jt{constructor(n=!1){super(),this.destroyRef=void 0,this.pendingTasks=void 0,this.__isAsync=n,function ID(){return void 0!==Ll()||null!=function op(){return lo}()}()&&(this.destroyRef=U(rc,{optional:!0})??void 0,this.pendingTasks=U(bi,{optional:!0})??void 0)}emit(n){const t=Z(null);try{super.next(n)}finally{Z(t)}}subscribe(n,t,r){let o=n,i=t||(()=>null),s=r;if(n&&"object"==typeof n){const l=n;o=l.next?.bind(l),i=l.error?.bind(l),s=l.complete?.bind(l)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));const a=super.subscribe({next:o,error:i,complete:s});return n instanceof Ze&&n.add(a),a}wrapInTimeout(n){return t=>{const r=this.pendingTasks?.add();setTimeout(()=>{n(t),void 0!==r&&this.pendingTasks?.remove(r)})}}};function vd(...e){}function Ib(e){let n,t;function r(){e=vd;try{void 0!==t&&"function"==typeof cancelAnimationFrame&&cancelAnimationFrame(t),void 0!==n&&clearTimeout(n)}catch{}}return n=setTimeout(()=>{e(),r()}),"function"==typeof requestAnimationFrame&&(t=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Sb(e){return queueMicrotask(()=>e()),()=>{e=vd}}const Xp="isAngularZone",yd=Xp+"_ID";let n1=0;class j{constructor(n){this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new Dt(!1),this.onMicrotaskEmpty=new Dt(!1),this.onStable=new Dt(!1),this.onError=new Dt(!1);const{enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=wb}=n;if(typeof Zone>"u")throw new z(908,!1);Zone.assertZonePatched();const s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,function i1(e){const n=()=>{!function o1(e){function n(){Ib(()=>{e.callbackScheduled=!1,Zp(e),e.isCheckStableRunning=!0,Yp(e),e.isCheckStableRunning=!1})}e.isCheckStableRunning||e.callbackScheduled||(e.callbackScheduled=!0,e.scheduleInRootZone?Zone.root.run(()=>{n()}):e._outer.run(()=>{n()}),Zp(e))}(e)},t=n1++;e._inner=e._inner.fork({name:"angular",properties:{[Xp]:!0,[yd]:t,[yd+t]:!0},onInvokeTask:(r,o,i,s,a,l)=>{if(function s1(e){return Ab(e,"__ignore_ng_zone__")}(l))return r.invokeTask(i,s,a,l);try{return Mb(e),r.invokeTask(i,s,a,l)}finally{(e.shouldCoalesceEventChangeDetection&&"eventTask"===s.type||e.shouldCoalesceRunChangeDetection)&&n(),Tb(e)}},onInvoke:(r,o,i,s,a,l,c)=>{try{return Mb(e),r.invoke(i,s,a,l,c)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!function a1(e){return Ab(e,"__scheduler_tick__")}(l)&&n(),Tb(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&("microTask"==s.change?(e._hasPendingMicrotasks=s.microTask,Zp(e),Yp(e)):"macroTask"==s.change&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}(s)}static isInAngularZone(){return typeof Zone<"u"&&!0===Zone.current.get(Xp)}static assertInAngularZone(){if(!j.isInAngularZone())throw new z(909,!1)}static assertNotInAngularZone(){if(j.isInAngularZone())throw new z(909,!1)}run(n,t,r){return this._inner.run(n,t,r)}runTask(n,t,r,o){const i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,n,r1,vd,vd);try{return i.runTask(s,t,r)}finally{i.cancelTask(s)}}runGuarded(n,t,r){return this._inner.runGuarded(n,t,r)}runOutsideAngular(n){return this._outer.run(n)}}const r1={};function Yp(e){if(0==e._nesting&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Zp(e){e.hasPendingMicrotasks=!!(e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&!0===e.callbackScheduled)}function Mb(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Tb(e){e._nesting--,Yp(e)}class Kp{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new Dt,this.onMicrotaskEmpty=new Dt,this.onStable=new Dt,this.onError=new Dt}run(n,t,r){return n.apply(t,r)}runGuarded(n,t,r){return n.apply(t,r)}runOutsideAngular(n){return n()}runTask(n,t,r,o){return n.apply(t,r)}}function Ab(e,n){return!(!Array.isArray(e)||1!==e.length)&&!0===e[0]?.data?.[n]}class Ro{constructor(){this._console=console}handleError(n){const t=this._findOriginalError(n);this._console.error("ERROR",n),t&&this._console.error("ORIGINAL ERROR",t)}_findOriginalError(n){let t=n&&qp(n);for(;t&&qp(t);)t=qp(t);return t||null}}const c1=new ce("",{providedIn:"root",factory:()=>{const e=U(j),n=U(Ro);return t=>e.runOutsideAngular(()=>n.handleError(t))}});function u1(){return Oa(Bt(),$())}function Oa(e,n){return new W(ur(e,n))}let W=(()=>{class e{constructor(t){this.nativeElement=t}static{this.__NG_ELEMENT_ID__=u1}}return e})();function ic(e){return!(128&~e.flags)}const eg=new Map;let h1=0;function tg(e){eg.delete(e[Sa])}const Cd="__ngContext__";function Qn(e,n){Fn(n)?(e[Cd]=n[Sa],function g1(e){eg.set(e[Sa],e)}(n)):e[Cd]=n}function jb(e){return Hb(e[ql])}function Ub(e){return Hb(e[uo])}function Hb(e){for(;null!==e&&!Kn(e);)e=e[uo];return e}let rg;const ac=new ce("",{providedIn:"root",factory:()=>R1}),R1="ng",Kb=new ce(""),wi=new ce("",{providedIn:"platform",factory:()=>"unknown"}),Qb=new ce("",{providedIn:"root",factory:()=>function Ei(){if(void 0!==rg)return rg;if(typeof document<"u")return document;throw new z(210,!1)}().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});let Jb=()=>null;function ug(e,n,t=!1){return Jb(e,n,t)}const aE=new ce("",{providedIn:"root",factory:()=>!1});class hE{constructor(n){this.changingThisBreaksApplicationSecurity=n}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see https://g.co/ng/security#xss)`}}function Lr(e){return e instanceof Function?e():e}var Si=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Si||{});let Eg;function wg(e,n){return Eg(e,n)}function $a(e,n,t,r,o){if(null!=r){let i,s=!1;Kn(r)?i=r:Fn(r)&&(s=!0,r=r[dn]);const a=Mt(r);0===e&&null!==t?null==o?LE(n,t,a):vs(n,t,a,o||null,!0):1===e&&null!==t?vs(n,t,a,o||null,!0):2===e?function vc(e,n,t){e.removeChild(null,n,t)}(n,a,s):3===e&&n.destroyNode(a),null!=i&&function VP(e,n,t,r,o){const i=t[To];i!==Mt(t)&&$a(n,e,r,i,o);for(let a=Cn;a<t.length;a++){const l=t[a];kd(l[he],l,e,n,r,i)}}(n,e,i,t,o)}}function Rd(e,n,t){return e.createElement(n,t)}function PE(e,n){n[Mo].changeDetectionScheduler?.notify(9),kd(e,n,n[rt],2,null,null)}function kE(e,n){const t=e[Ma],r=n[Sn];(Fn(r)||n[fn]!==r[Sn][fn])&&(e[Ye]|=nd.HasTransplantedViews),null===t?e[Ma]=[n]:t.push(n)}function Mg(e,n){const t=e[Ma],r=t.indexOf(n);t.splice(r,1)}function mc(e,n){if(e.length<=Cn)return;const t=Cn+n,r=e[t];if(r){const o=r[as];null!==o&&o!==e&&Mg(o,r),n>0&&(e[t-1][uo]=r[uo]);const i=va(e,Cn+n);!function OP(e,n){PE(e,n),n[dn]=null,n[Un]=null}(r[he],r);const s=i[Qo];null!==s&&s.detachView(i[he]),r[Sn]=null,r[uo]=null,r[Ye]&=-129}return r}function Nd(e,n){if(!(256&n[Ye])){const t=n[rt];t.destroyNode&&kd(e,n,t,3,null,null),function NP(e){let n=e[ql];if(!n)return Tg(e[he],e);for(;n;){let t=null;if(Fn(n))t=n[ql];else{const r=n[Cn];r&&(t=r)}if(!t){for(;n&&!n[uo]&&n!==e;)Fn(n)&&Tg(n[he],n),n=n[Sn];null===n&&(n=e),Fn(n)&&Tg(n[he],n),t=n&&n[uo]}n=t}}(n)}}function Tg(e,n){if(256&n[Ye])return;const t=Z(null);try{n[Ye]&=-129,n[Ye]|=256,n[Qr]&&G(n[Qr]),function FP(e,n){let t;if(null!=e&&null!=(t=e.destroyHooks))for(let r=0;r<t.length;r+=2){const o=n[t[r]];if(!(o instanceof ec)){const i=t[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){const a=o[i[s]],l=i[s+1];Ao(4,a,l);try{l.call(a)}finally{Ao(5,a,l)}}else{Ao(4,o,i);try{i.call(o)}finally{Ao(5,o,i)}}}}}(e,n),function kP(e,n){const t=e.cleanup,r=n[Ea];if(null!==t)for(let i=0;i<t.length-1;i+=2)if("string"==typeof t[i]){const s=t[i+3];s>=0?r[s]():r[-s].unsubscribe(),i+=2}else t[i].call(r[t[i+1]]);null!==r&&(n[Ea]=null);const o=n[Di];if(null!==o){n[Di]=null;for(let i=0;i<o.length;i++)(0,o[i])()}}(e,n),1===n[he].type&&n[rt].destroy();const r=n[as];if(null!==r&&Kn(n[Sn])){r!==n[Sn]&&Mg(r,n);const o=n[Qo];null!==o&&o.detachView(e)}tg(n)}finally{Z(t)}}function Ag(e,n,t){return function FE(e,n,t){let r=n;for(;null!==r&&168&r.type;)r=(n=r).parent;if(null===r)return t[dn];{const{componentOffset:o}=r;if(o>-1){const{encapsulation:i}=e.data[r.directiveStart+o];if(i===Se.None||i===Se.Emulated)return null}return ur(r,t)}}(e,n.parent,t)}function vs(e,n,t,r,o){e.insertBefore(n,t,r,o)}function LE(e,n,t){e.appendChild(n,t)}function $E(e,n,t,r,o){null!==r?vs(e,n,t,r,o):LE(e,n,t)}function xg(e,n){return e.parentNode(n)}function VE(e,n,t){return jE(e,n,t)}let Og,jE=function BE(e,n,t){return 40&e.type?ur(e,t):null};function Pd(e,n,t,r){const o=Ag(e,r,n),i=n[rt],a=VE(r.parent||n[Un],r,n);if(null!=o)if(Array.isArray(t))for(let l=0;l<t.length;l++)$E(i,o,t[l],a,!1);else $E(i,o,t,a,!1);void 0!==Og&&Og(i,r,n,t,o)}function ys(e,n){if(null!==n){const t=n.type;if(3&t)return ur(n,e);if(4&t)return Rg(-1,e[n.index]);if(8&t){const r=n.child;if(null!==r)return ys(e,r);{const o=e[n.index];return Kn(o)?Rg(-1,o):Mt(o)}}if(128&t)return ys(e,n.next);if(32&t)return wg(n,e)()||Mt(e[n.index]);{const r=HE(e,n);return null!==r?Array.isArray(r)?r[0]:ys(Jo(e[fn]),r):ys(e,n.next)}}return null}function HE(e,n){return null!==n?e[fn][Un].projection[n.projection]:null}function Rg(e,n){const t=Cn+e+1;if(t<n.length){const r=n[t],o=r[he].firstChild;if(null!==o)return ys(r,o)}return n[To]}function Ng(e,n,t,r,o,i,s){for(;null!=t;){if(128===t.type){t=t.next;continue}const a=r[t.index],l=t.type;if(s&&0===n&&(a&&Qn(Mt(a),r),t.flags|=2),32&~t.flags)if(8&l)Ng(e,n,t.child,r,o,i,!1),$a(n,e,o,a,i);else if(32&l){const c=wg(t,r);let d;for(;d=c();)$a(n,e,o,d,i);$a(n,e,o,a,i)}else 16&l?GE(e,n,r,t,o,i):$a(n,e,o,a,i);t=s?t.projectionNext:t.next}}function kd(e,n,t,r,o,i){Ng(t,r,e.firstChild,n,o,i,!1)}function GE(e,n,t,r,o,i){const s=t[fn],l=s[Un].projection[r.projection];if(Array.isArray(l))for(let c=0;c<l.length;c++)$a(n,e,o,l[c],i);else{let c=l;const d=s[Sn];ic(r)&&(c.flags|=128),Ng(e,n,c,d,o,i,!0)}}function WE(e,n,t){""===t?e.removeAttribute(n,"class"):e.setAttribute(n,"class",t)}function qE(e,n,t){const{mergedAttrs:r,classes:o,styles:i}=t;null!==r&&wt(e,n,r),null!==o&&WE(e,n,o),null!==i&&function jP(e,n,t){e.setAttribute(n,"style",t)}(e,n,i)}const st={};function Ht(e=1){XE(mt(),$(),Hn()+e,!1)}function XE(e,n,t,r){if(!r)if(3&~n[Ye]){const i=e.preOrderHooks;null!==i&&dd(n,i,0,t)}else{const i=e.preOrderCheckHooks;null!==i&&ud(n,i,t)}hs(t)}function g(e,n=ut.Default){const t=$();return null===t?Pe(e,n):fb(Bt(),t,et(e),n)}function YE(e,n,t,r,o,i){const s=Z(null);try{let a=null;o&_t.SignalBased&&(a=n[r][ke]),null!==a&&void 0!==a.transformFn&&(i=a.transformFn(i)),o&_t.HasDecoratorInputTransform&&(i=e.inputTransforms[r].call(n,i)),null!==e.setInput?e.setInput(n,a,i,t,r):ND(n,a,r,i)}finally{Z(s)}}function Fd(e,n,t,r,o,i,s,a,l,c,d){const h=n.blueprint.slice();return h[dn]=o,h[Ye]=204|r,(null!==c||e&&2048&e[Ye])&&(h[Ye]|=2048),VD(h),h[Sn]=h[wa]=e,h[tn]=t,h[Mo]=s||e&&e[Mo],h[rt]=a||e&&e[rt],h[Mn]=l||e&&e[Mn]||null,h[Un]=i,h[Sa]=function p1(){return h1++}(),h[cr]=d,h[TD]=c,h[fn]=2==n.type?e[fn]:h,h}function Cs(e,n,t,r,o){let i=e.data[n];if(null===i)i=function Pg(e,n,t,r,o){const i=zD(),s=Op(),l=e.data[n]=function ZP(e,n,t,r,o,i){let s=n?n.injectorIndex:-1,a=0;return us()&&(a|=128),{type:t,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:n,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}(0,s?i:i&&i.parent,t,n,r,o);return null===e.firstChild&&(e.firstChild=l),null!==i&&(s?null==i.child&&null!==l.parent&&(i.child=l):null===i.next&&(i.next=l,l.prev=i)),l}(e,n,t,r,o),function DN(){return ot.lFrame.inI18n}()&&(i.flags|=32);else if(64&i.type){i.type=t,i.value=r,i.attrs=o;const s=function Ql(){const e=ot.lFrame,n=e.currentTNode;return e.isParent?n:n.parent}();i.injectorIndex=null===s?-1:s.injectorIndex}return ho(i,!0),i}function yc(e,n,t,r){if(0===t)return-1;const o=n.length;for(let i=0;i<t;i++)n.push(r),e.blueprint.push(r),e.data.push(null);return o}function ZE(e,n,t,r,o){const i=Hn(),s=2&r;try{hs(-1),s&&n.length>Be&&XE(e,n,Be,!1),Ao(s?2:0,o),t(r,o)}finally{hs(i),Ao(s?3:1,o)}}function kg(e,n,t){if(bp(n)){const r=Z(null);try{const i=n.directiveEnd;for(let s=n.directiveStart;s<i;s++){const a=e.data[s];a.contentQueries&&a.contentQueries(1,t[s],s)}}finally{Z(r)}}}function Fg(e,n,t){HD()&&(function rk(e,n,t,r){const o=t.directiveStart,i=t.directiveEnd;ls(t)&&function uk(e,n,t){const r=ur(n,e),o=KE(t);let s=16;t.signals?s=4096:t.onPush&&(s=64);const a=Ld(e,Fd(e,o,null,s,r,n,null,e[Mo].rendererFactory.createRenderer(r,t),null,null,null));e[n.index]=a}(n,t,e.data[o+t.componentOffset]),e.firstCreatePass||hd(t,n),Qn(r,n);const s=t.initialInputs;for(let a=o;a<i;a++){const l=e.data[a],c=gs(n,e,a,t);Qn(c,n),null!==s&&dk(0,a-o,c,l,0,s),fo(l)&&(Fr(t.index,n)[tn]=gs(n,e,a,t))}}(e,n,t,ur(t,n)),!(64&~t.flags)&&nw(e,n,t))}function Lg(e,n,t=ur){const r=n.localNames;if(null!==r){let o=n.index+1;for(let i=0;i<r.length;i+=2){const s=r[i+1],a=-1===s?t(n,e):e[s];e[o++]=a}}}function KE(e){const n=e.tView;return null===n||n.incompleteFirstPass?e.tView=$g(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):n}function $g(e,n,t,r,o,i,s,a,l,c,d){const h=Be+r,v=h+o,y=function zP(e,n){const t=[];for(let r=0;r<n;r++)t.push(r<e?null:st);return t}(h,v),C="function"==typeof c?c():c;return y[he]={type:e,blueprint:y,template:t,queries:null,viewQuery:a,declTNode:n,data:y.slice().fill(null,h),bindingStartIndex:h,expandoStartIndex:v,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:"function"==typeof i?i():i,pipeRegistry:"function"==typeof s?s():s,firstChild:null,schemas:l,consts:C,incompleteFirstPass:!1,ssrId:d}}let QE=()=>null;function JE(e,n,t,r,o){for(let i in n){if(!n.hasOwnProperty(i))continue;const s=n[i];if(void 0===s)continue;r??={};let a,l=_t.None;Array.isArray(s)?(a=s[0],l=s[1]):a=s;let c=i;if(null!==o){if(!o.hasOwnProperty(i))continue;c=o[i]}0===e?ew(r,t,c,a,l):ew(r,t,c,a)}return r}function ew(e,n,t,r,o){let i;e.hasOwnProperty(t)?(i=e[t]).push(n,r):i=e[t]=[n,r],void 0!==o&&i.push(o)}function Sr(e,n,t,r,o,i,s,a){const l=ur(n,t);let d,c=n.inputs;!a&&null!=c&&(d=c[r])?(Hg(e,t,d,r,o),ls(n)&&function JP(e,n){const t=Fr(n,e);16&t[Ye]||(t[Ye]|=64)}(t,n.index)):3&n.type&&(r=function QP(e){return"class"===e?"className":"for"===e?"htmlFor":"formaction"===e?"formAction":"innerHtml"===e?"innerHTML":"readonly"===e?"readOnly":"tabindex"===e?"tabIndex":e}(r),o=null!=s?s(o,n.value||"",r):o,i.setProperty(l,r,o))}function Vg(e,n,t,r){if(HD()){const o=null===r?null:{"":-1},i=function ik(e,n){const t=e.directiveRegistry;let r=null,o=null;if(t)for(let i=0;i<t.length;i++){const s=t[i];if(Gl(n,s.selectors,!1))if(r||(r=[]),fo(s))if(null!==s.findHostDirectiveDefs){const a=[];o=o||new Map,s.findHostDirectiveDefs(s,a,o),r.unshift(...a,s),Bg(e,n,a.length)}else r.unshift(s),Bg(e,n,0);else o=o||new Map,s.findHostDirectiveDefs?.(s,r,o),r.push(s)}return null===r?null:[r,o]}(e,t);let s,a;null===i?s=a=null:[s,a]=i,null!==s&&tw(e,n,t,s,o,a),o&&function sk(e,n,t){if(n){const r=e.localNames=[];for(let o=0;o<n.length;o+=2){const i=t[n[o+1]];if(null==i)throw new z(-301,!1);r.push(n[o],i)}}}(t,r,o)}t.mergedAttrs=Lt(t.mergedAttrs,t.attrs)}function tw(e,n,t,r,o,i){for(let c=0;c<r.length;c++)zp(hd(t,n),e,r[c].type);!function lk(e,n,t){e.flags|=1,e.directiveStart=n,e.directiveEnd=n+t,e.providerIndexes=n}(t,e.data.length,r.length);for(let c=0;c<r.length;c++){const d=r[c];d.providersResolver&&d.providersResolver(d)}let s=!1,a=!1,l=yc(e,n,r.length,null);for(let c=0;c<r.length;c++){const d=r[c];t.mergedAttrs=Lt(t.mergedAttrs,d.hostAttrs),ck(e,t,n,l,d),ak(l,d,o),null!==d.contentQueries&&(t.flags|=4),(null!==d.hostBindings||null!==d.hostAttrs||0!==d.hostVars)&&(t.flags|=64);const h=d.type.prototype;!s&&(h.ngOnChanges||h.ngOnInit||h.ngDoCheck)&&((e.preOrderHooks??=[]).push(t.index),s=!0),!a&&(h.ngOnChanges||h.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(t.index),a=!0),l++}!function KP(e,n,t){const o=n.directiveEnd,i=e.data,s=n.attrs,a=[];let l=null,c=null;for(let d=n.directiveStart;d<o;d++){const h=i[d],v=t?t.get(h):null,C=v?v.outputs:null;l=JE(0,h.inputs,d,l,v?v.inputs:null),c=JE(1,h.outputs,d,c,C);const b=null===l||null===s||Vt(n)?null:fk(l,d,s);a.push(b)}null!==l&&(l.hasOwnProperty("class")&&(n.flags|=8),l.hasOwnProperty("style")&&(n.flags|=16)),n.initialInputs=a,n.inputs=l,n.outputs=c}(e,t,i)}function nw(e,n,t){const r=t.directiveStart,o=t.directiveEnd,i=t.index,s=function EN(){return ot.lFrame.currentDirectiveIndex}();try{hs(i);for(let a=r;a<o;a++){const l=e.data[a],c=n[a];Np(a),(null!==l.hostBindings||0!==l.hostVars||null!==l.hostAttrs)&&ok(l,c)}}finally{hs(-1),Np(s)}}function ok(e,n){null!==e.hostBindings&&e.hostBindings(1,n)}function Bg(e,n,t){n.componentOffset=t,(e.components??=[]).push(n.index)}function ak(e,n,t){if(t){if(n.exportAs)for(let r=0;r<n.exportAs.length;r++)t[n.exportAs[r]]=e;fo(n)&&(t[""]=e)}}function ck(e,n,t,r,o){e.data[r]=o;const i=o.factory||(o.factory=So(o.type)),s=new ec(i,fo(o),g);e.blueprint[r]=s,t[r]=s,function tk(e,n,t,r,o){const i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;null===s&&(s=e.hostBindingOpCodes=[]);const a=~n.index;(function nk(e){let n=e.length;for(;n>0;){const t=e[--n];if("number"==typeof t&&t<0)return t}return 0})(s)!=a&&s.push(a),s.push(t,r,i)}}(e,n,r,yc(e,t,o.hostVars,st),o)}function No(e,n,t,r,o,i){const s=ur(e,n);!function jg(e,n,t,r,o,i,s){if(null==i)e.removeAttribute(n,o,t);else{const a=null==s?tt(i):s(i,r||"",o);e.setAttribute(n,o,a,t)}}(n[rt],s,i,e.value,t,r,o)}function dk(e,n,t,r,o,i){const s=i[n];if(null!==s)for(let a=0;a<s.length;)YE(r,t,s[a++],s[a++],s[a++],s[a++])}function fk(e,n,t){let r=null,o=0;for(;o<t.length;){const i=t[o];if(0!==i)if(5!==i){if("number"==typeof i)break;if(e.hasOwnProperty(i)){null===r&&(r=[]);const s=e[i];for(let a=0;a<s.length;a+=3)if(s[a]===n){r.push(i,s[a+1],s[a+2],t[o+1]);break}}o+=2}else o+=2;else o+=4}return r}function rw(e,n,t,r){return[e,!0,0,n,null,r,null,t,null,null]}function ow(e,n){const t=e.contentQueries;if(null!==t){const r=Z(null);try{for(let o=0;o<t.length;o+=2){const s=t[o+1];if(-1!==s){const a=e.data[s];ld(t[o]),a.contentQueries(2,n[s],s)}}}finally{Z(r)}}}function Ld(e,n){return e[ql]?e[MD][uo]=n:e[ql]=n,e[MD]=n,n}function Ug(e,n,t){ld(0);const r=Z(null);try{n(e,t)}finally{Z(r)}}function $d(e,n){const t=e[Mn],r=t?t.get(Ro,null):null;r&&r.handleError(n)}function Hg(e,n,t,r,o){for(let i=0;i<t.length;){const s=t[i++],a=t[i++],l=t[i++];YE(e.data[s],n[s],r,a,l,o)}}function oi(e,n,t){const r=Zl(n,e);!function NE(e,n,t){e.setValue(n,t)}(e[rt],r,t)}function hk(e,n){const t=Fr(n,e),r=t[he];!function pk(e,n){for(let t=n.length;t<e.blueprint.length;t++)n.push(e.blueprint[t])}(r,t);const o=t[dn];null!==o&&null===t[cr]&&(t[cr]=ug(o,t[Mn])),zg(r,t,t[tn])}function zg(e,n,t){Fp(n);try{const r=e.viewQuery;null!==r&&Ug(1,r,t);const o=e.template;null!==o&&ZE(e,n,o,1,t),e.firstCreatePass&&(e.firstCreatePass=!1),n[Qo]?.finishViewCreation(e),e.staticContentQueries&&ow(e,n),e.staticViewQueries&&Ug(2,e.viewQuery,t);const i=e.components;null!==i&&function gk(e,n){for(let t=0;t<n.length;t++)hk(e,n[t])}(n,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{n[Ye]&=-5,Lp()}}function Va(e,n,t,r){const o=Z(null);try{const i=n.tView,l=Fd(e,i,t,4096&e[Ye]?4096:16,null,n,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null);l[as]=e[n.index];const d=e[Qo];return null!==d&&(l[Qo]=d.createEmbeddedView(i)),zg(i,l,t),l}finally{Z(o)}}function _s(e,n){return!n||null===n.firstChild||ic(e)}function Ba(e,n,t,r=!0){const o=n[he];if(function PP(e,n,t,r){const o=Cn+r,i=t.length;r>0&&(t[o-1][uo]=n),r<i-Cn?(n[uo]=t[o],Hl(t,Cn+r,n)):(t.push(n),n[uo]=null),n[Sn]=t;const s=n[as];null!==s&&t!==s&&kE(s,n);const a=n[Qo];null!==a&&a.insertView(e),Tp(n),n[Ye]|=128}(o,n,e,t),r){const s=Rg(t,e),a=n[rt],l=xg(a,e[To]);null!==l&&function RP(e,n,t,r,o,i){r[dn]=o,r[Un]=n,kd(e,r,t,1,o,i)}(o,e[Un],a,n,l,s)}const i=n[cr];null!==i&&null!==i.firstChild&&(i.firstChild=null)}function Cc(e,n,t,r,o=!1){for(;null!==t;){if(128===t.type){t=o?t.projectionNext:t.next;continue}const i=n[t.index];null!==i&&r.push(Mt(i)),Kn(i)&&cw(i,r);const s=t.type;if(8&s)Cc(e,n,t.child,r);else if(32&s){const a=wg(t,n);let l;for(;l=a();)r.push(l)}else if(16&s){const a=HE(n,t);if(Array.isArray(a))r.push(...a);else{const l=Jo(n[fn]);Cc(l[he],l,a,r,!0)}}t=o?t.projectionNext:t.next}return r}function cw(e,n){for(let t=Cn;t<e.length;t++){const r=e[t],o=r[he].firstChild;null!==o&&Cc(r[he],r,o,n)}e[To]!==e[dn]&&n.push(e[To])}let uw=[];const Ck={...Ae,consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{id(e.lView)},consumerOnSignalRead(){this.lView[Qr]=this}},Dk={...Ae,consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{let n=Jo(e.lView);for(;n&&!dw(n[he]);)n=Jo(n);n&&BD(n)},consumerOnSignalRead(){this.lView[Qr]=this}};function dw(e){return 2!==e.type}const bk=100;function Vd(e,n=!0,t=0){const r=e[Mo],o=r.rendererFactory;o.begin?.();try{!function Ek(e,n){const t=qD();try{XD(!0),Wg(e,n);let r=0;for(;od(e);){if(r===bk)throw new z(103,!1);r++,Wg(e,1)}}finally{XD(t)}}(e,t)}catch(s){throw n&&$d(e,s),s}finally{o.end?.(),r.inlineEffectRunner?.flush()}}function wk(e,n,t,r){const o=n[Ye];if(!(256&~o))return;n[Mo].inlineEffectRunner?.flush(),Fp(n);let a=!0,l=null,c=null;dw(e)?(c=function mk(e){return e[Qr]??function vk(e){const n=uw.pop()??Object.create(Ck);return n.lView=e,n}(e)}(n),l=J(c)):null===function ie(){return be}()?(a=!1,c=function _k(e){const n=e[Qr]??Object.create(Dk);return n.lView=e,n}(n),l=J(c)):n[Qr]&&(G(n[Qr]),n[Qr]=null);try{VD(n),function YD(e){return ot.lFrame.bindingIndex=e}(e.bindingStartIndex),null!==t&&ZE(e,n,t,2,r);const d=!(3&~o);if(d){const y=e.preOrderCheckHooks;null!==y&&ud(n,y,null)}else{const y=e.preOrderHooks;null!==y&&dd(n,y,0,null),$p(n,0)}if(function Ik(e){for(let n=jb(e);null!==n;n=Ub(n)){if(!(n[Ye]&nd.HasTransplantedViews))continue;const t=n[Ma];for(let r=0;r<t.length;r++)BD(t[r])}}(n),hw(n,0),null!==e.contentQueries&&ow(e,n),d){const y=e.contentCheckHooks;null!==y&&ud(n,y)}else{const y=e.contentHooks;null!==y&&dd(n,y,1),$p(n,1)}!function HP(e,n){const t=e.hostBindingOpCodes;if(null!==t)try{for(let r=0;r<t.length;r++){const o=t[r];if(o<0)hs(~o);else{const i=o,s=t[++r],a=t[++r];bN(s,i),a(2,n[i])}}}finally{hs(-1)}}(e,n);const h=e.components;null!==h&&gw(n,h,0);const v=e.viewQuery;if(null!==v&&Ug(2,v,r),d){const y=e.viewCheckHooks;null!==y&&ud(n,y)}else{const y=e.viewHooks;null!==y&&dd(n,y,2),$p(n,2)}if(!0===e.firstUpdatePass&&(e.firstUpdatePass=!1),n[ed]){for(const y of n[ed])y();n[ed]=null}n[Ye]&=-73}catch(d){throw id(n),d}finally{null!==c&&(X(c,l),a&&function yk(e){e.lView[Qr]!==e&&(e.lView=null,uw.push(e))}(c)),Lp()}}function hw(e,n){for(let t=jb(e);null!==t;t=Ub(t))for(let r=Cn;r<t.length;r++)pw(t[r],n)}function Sk(e,n,t){pw(Fr(n,e),t)}function pw(e,n){Mp(e)&&Wg(e,n)}function Wg(e,n){const r=e[he],o=e[Ye],i=e[Qr];let s=!!(0===n&&16&o);if(s||=!!(64&o&&0===n),s||=!!(1024&o),s||=!(!i?.dirty||!ee(i)),s||=!1,i&&(i.dirty=!1),e[Ye]&=-9217,s)wk(r,e,r.template,e[tn]);else if(8192&o){hw(e,1);const a=r.components;null!==a&&gw(e,a,1)}}function gw(e,n,t){for(let r=0;r<n.length;r++)Sk(e,n[r],t)}function _c(e,n){const t=qD()?64:1088;for(e[Mo].changeDetectionScheduler?.notify(n);e;){e[Ye]|=t;const r=Jo(e);if(Yl(e)&&!r)return e;e=r}return null}class Dc{get rootNodes(){const n=this._lView,t=n[he];return Cc(t,n,t.firstChild,[])}constructor(n,t,r=!0){this._lView=n,this._cdRefInjectingView=t,this.notifyErrorHandler=r,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[tn]}set context(n){this._lView[tn]=n}get destroyed(){return!(256&~this._lView[Ye])}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){const n=this._lView[Sn];if(Kn(n)){const t=n[8],r=t?t.indexOf(this):-1;r>-1&&(mc(n,r),va(t,r))}this._attachedToViewContainer=!1}Nd(this._lView[he],this._lView)}onDestroy(n){sd(this._lView,n)}markForCheck(){_c(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[Ye]&=-129}reattach(){Tp(this._lView),this._lView[Ye]|=128}detectChanges(){this._lView[Ye]|=1024,Vd(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new z(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;const n=Yl(this._lView),t=this._lView[as];null!==t&&!n&&Mg(t,this._lView),PE(this._lView[he],this._lView)}attachToAppRef(n){if(this._attachedToViewContainer)throw new z(902,!1);this._appRef=n;const t=Yl(this._lView),r=this._lView[as];null!==r&&!t&&kE(r,this._lView),Tp(this._lView)}}let go=(()=>{class e{static{this.__NG_ELEMENT_ID__=Ak}}return e})();const Mk=go,Tk=class extends Mk{constructor(n,t,r){super(),this._declarationLView=n,this._declarationTContainer=t,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(n,t){return this.createEmbeddedViewImpl(n,t)}createEmbeddedViewImpl(n,t,r){const o=Va(this._declarationLView,this._declarationTContainer,n,{embeddedViewInjector:t,dehydratedView:r});return new Dc(o)}};function Ak(){return function Bd(e,n){return 4&e.type?new Tk(n,e,Oa(e,n)):null}(Bt(),$())}let Fw=()=>null;function bs(e,n){return Fw(e,n)}class Ua{}const Ac=new ce("",{providedIn:"root",factory:()=>!1}),Lw=new ce(""),rm=new ce("");class DF{}class $w{}class EF{resolveComponentFactory(n){throw function bF(e){const n=Error(`No component factory found for ${At(e)}.`);return n.ngComponent=e,n}(n)}}class Wd{static{this.NULL=new EF}}class om{}let ii=(()=>{class e{constructor(){this.destroyNode=null}static{this.__NG_ELEMENT_ID__=()=>function wF(){const e=$(),t=Fr(Bt().index,e);return(Fn(t)?t:e)[rt]}()}}return e})(),IF=(()=>{class e{static{this.\u0275prov=Me({token:e,providedIn:"root",factory:()=>null})}}return e})();function Xd(e,n,t){let r=t?e.styles:null,o=t?e.classes:null,i=0;if(null!==n)for(let s=0;s<n.length;s++){const a=n[s];"number"==typeof a?i=a:1==i?o=Pr(o,a):2==i&&(r=Pr(r,a+": "+n[++s]+";"))}t?e.styles=r:e.stylesWithoutHost=r,t?e.classes=o:e.classesWithoutHost=o}class Uw extends Wd{constructor(n){super(),this.ngModule=n}resolveComponentFactory(n){const t=Ct(n);return new Rc(t,this.ngModule)}}function Hw(e,n){const t=[];for(const r in e){if(!e.hasOwnProperty(r))continue;const o=e[r];if(void 0===o)continue;const i=Array.isArray(o),s=i?o[0]:o;t.push(n?{propName:s,templateName:r,isSignal:!!((i?o[1]:_t.None)&_t.SignalBased)}:{propName:s,templateName:r})}return t}class Rc extends $w{get inputs(){const n=this.componentDef,t=n.inputTransforms,r=Hw(n.inputs,!0);if(null!==t)for(const o of r)t.hasOwnProperty(o.propName)&&(o.transform=t[o.propName]);return r}get outputs(){return Hw(this.componentDef.outputs,!1)}constructor(n,t){super(),this.componentDef=n,this.ngModule=t,this.componentType=n.type,this.selector=function PR(e){return e.map(NR).join(",")}(n.selectors),this.ngContentSelectors=n.ngContentSelectors?n.ngContentSelectors:[],this.isBoundToModule=!!t}create(n,t,r,o){const i=Z(null);try{let s=(o=o||this.ngModule)instanceof Pn?o:o?.injector;s&&null!==this.componentDef.getStandaloneInjector&&(s=this.componentDef.getStandaloneInjector(s)||s);const a=s?new ps(n,s):n,l=a.get(om,null);if(null===l)throw new z(407,!1);const h={rendererFactory:l,sanitizer:a.get(IF,null),inlineEffectRunner:null,changeDetectionScheduler:a.get(Ua,null)},v=l.createRenderer(null,this.componentDef),y=this.componentDef.selectors[0][0]||"div",C=r?function GP(e,n,t,r){const i=r.get(aE,!1)||t===Se.ShadowDom,s=e.selectRootElement(n,i);return function WP(e){QE(e)}(s),s}(v,r,this.componentDef.encapsulation,a):Rd(v,y,function TF(e){const n=e.toLowerCase();return"svg"===n?"svg":"math"===n?"math":null}(y));let b=512;this.componentDef.signals?b|=4096:this.componentDef.onPush||(b|=16);let M=null;null!==C&&(M=ug(C,a,!0));const O=$g(0,null,null,1,0,null,null,null,null,null,null),A=Fd(null,O,null,b,null,null,h,v,a,null,M);Fp(A);let Ee,Ge,vt=null;try{const Tt=this.componentDef;let vn,fi=null;Tt.findHostDirectiveDefs?(vn=[],fi=new Map,Tt.findHostDirectiveDefs(Tt,vn,fi),vn.push(Tt)):vn=[Tt];const TR=function xF(e,n){const t=e[he],r=Be;return e[r]=n,Cs(t,r,2,"#host",null)}(A,C);vt=function OF(e,n,t,r,o,i,s){const a=o[he];!function RF(e,n,t,r){for(const o of e)n.mergedAttrs=Lt(n.mergedAttrs,o.hostAttrs);null!==n.mergedAttrs&&(Xd(n,n.mergedAttrs,!0),null!==t&&qE(r,t,n))}(r,e,n,s);let l=null;null!==n&&(l=ug(n,o[Mn]));const c=i.rendererFactory.createRenderer(n,t);let d=16;t.signals?d=4096:t.onPush&&(d=64);const h=Fd(o,KE(t),null,d,o[e.index],e,i,c,null,null,l);return a.firstCreatePass&&Bg(a,e,r.length-1),Ld(o,h),o[e.index]=h}(TR,C,Tt,vn,A,h,v),Ge=Kl(O,Be),C&&function PF(e,n,t,r){if(r)wt(e,t,["ng-version","18.2.13"]);else{const{attrs:o,classes:i}=function kR(e){const n=[],t=[];let r=1,o=2;for(;r<e.length;){let i=e[r];if("string"==typeof i)2===o?""!==i&&n.push(i,e[++r]):8===o&&t.push(i);else{if(!Yn(o))break;o=i}r++}return{attrs:n,classes:t}}(n.selectors[0]);o&&wt(e,t,o),i&&i.length>0&&WE(e,t,i.join(" "))}}(v,Tt,C,r),void 0!==t&&function kF(e,n,t){const r=e.projection=[];for(let o=0;o<n.length;o++){const i=t[o];r.push(null!=i?Array.from(i):null)}}(Ge,this.ngContentSelectors,t),Ee=function NF(e,n,t,r,o,i){const s=Bt(),a=o[he],l=ur(s,o);tw(a,o,s,t,null,r);for(let d=0;d<t.length;d++)Qn(gs(o,a,s.directiveStart+d,s),o);nw(a,o,s),l&&Qn(l,o);const c=gs(o,a,s.directiveStart+s.componentOffset,s);if(e[tn]=o[tn]=c,null!==i)for(const d of i)d(c,n);return kg(a,s,o),c}(vt,Tt,vn,fi,A,[FF]),zg(O,A,null)}catch(Tt){throw null!==vt&&tg(vt),tg(A),Tt}finally{Lp()}return new AF(this.componentType,Ee,Oa(Ge,A),A,Ge)}finally{Z(i)}}}class AF extends DF{constructor(n,t,r,o,i){super(),this.location=r,this._rootLView=o,this._tNode=i,this.previousInputValues=null,this.instance=t,this.hostView=this.changeDetectorRef=new Dc(o,void 0,!1),this.componentType=n}setInput(n,t){const r=this._tNode.inputs;let o;if(null!==r&&(o=r[n])){if(this.previousInputValues??=new Map,this.previousInputValues.has(n)&&Object.is(this.previousInputValues.get(n),t))return;const i=this._rootLView;Hg(i[he],i,o,n,t),this.previousInputValues.set(n,t),_c(Fr(this._tNode.index,i),1)}}get injector(){return new Ln(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(n){this.hostView.onDestroy(n)}}function FF(){const e=Bt();cd($()[he],e)}let Vr=(()=>{class e{static{this.__NG_ELEMENT_ID__=LF}}return e})();function LF(){return function Ww(e,n){let t;const r=n[e.index];return Kn(r)?t=r:(t=rw(r,n,null,e),n[e.index]=t,Ld(n,t)),qw(t,n,e,r),new zw(t,e,n)}(Bt(),$())}const $F=Vr,zw=class extends $F{constructor(n,t,r){super(),this._lContainer=n,this._hostTNode=t,this._hostLView=r}get element(){return Oa(this._hostTNode,this._hostLView)}get injector(){return new Ln(this._hostTNode,this._hostLView)}get parentInjector(){const n=pd(this._hostTNode,this._hostLView);if(jp(n)){const t=nc(n,this._hostLView),r=tc(n);return new Ln(t[he].data[r+8],t)}return new Ln(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(n){const t=Gw(this._lContainer);return null!==t&&t[n]||null}get length(){return this._lContainer.length-Cn}createEmbeddedView(n,t,r){let o,i;"number"==typeof r?o=r:null!=r&&(o=r.index,i=r.injector);const s=bs(this._lContainer,n.ssrId),a=n.createEmbeddedViewImpl(t||{},i,s);return this.insertImpl(a,o,_s(this._hostTNode,s)),a}createComponent(n,t,r,o,i){const s=n&&!function Wl(e){return"function"==typeof e}(n);let a;if(s)a=t;else{const C=t||{};a=C.index,r=C.injector,o=C.projectableNodes,i=C.environmentInjector||C.ngModuleRef}const l=s?n:new Rc(Ct(n)),c=r||this.parentInjector;if(!i&&null==l.ngModule){const b=(s?c:this.parentInjector).get(Pn,null);b&&(i=b)}const d=Ct(l.componentType??{}),h=bs(this._lContainer,d?.id??null),y=l.create(c,o,h?.firstChild??null,i);return this.insertImpl(y.hostView,a,_s(this._hostTNode,h)),y}insert(n,t){return this.insertImpl(n,t,!0)}insertImpl(n,t,r){const o=n._lView;if(function fN(e){return Kn(e[Sn])}(o)){const a=this.indexOf(n);if(-1!==a)this.detach(a);else{const l=o[Sn],c=new zw(l,l[Un],l[Sn]);c.detach(c.indexOf(n))}}const i=this._adjustIndex(t),s=this._lContainer;return Ba(s,o,i,r),n.attachToViewContainerRef(),Hl(am(s),i,n),n}move(n,t){return this.insert(n,t)}indexOf(n){const t=Gw(this._lContainer);return null!==t?t.indexOf(n):-1}remove(n){const t=this._adjustIndex(n,-1),r=mc(this._lContainer,t);r&&(va(am(this._lContainer),t),Nd(r[he],r))}detach(n){const t=this._adjustIndex(n,-1),r=mc(this._lContainer,t);return r&&null!=va(am(this._lContainer),t)?new Dc(r):null}_adjustIndex(n,t=0){return n??this.length+t}};function Gw(e){return e[8]}function am(e){return e[8]||(e[8]=[])}let qw=function Yw(e,n,t,r){if(e[To])return;let o;o=8&t.type?Mt(r):function VF(e,n){const t=e[rt],r=t.createComment(""),o=ur(n,e);return vs(t,xg(t,o),r,function LP(e,n){return e.nextSibling(n)}(t,o),!1),r}(n,t),e[To]=o},lm=()=>!1;const rI=new Set;function fr(e){rI.has(e)||(rI.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}function Ha(e,n){fr("NgSignals");const t=function te(e){const n=Object.create(St);n.value=e;const t=()=>(He(n),n.value);return t[ke]=n,t}(e),r=t[ke];return n?.equal&&(r.equal=n.equal),t.set=o=>ze(r,o),t.update=o=>function qe(e,n){K()||R(),ze(e,n(e.value))}(r,o),t.asReadonly=iI.bind(t),t}function iI(){const e=this[ke];if(void 0===e.readonlyFn){const n=()=>this();n[ke]=e,e.readonlyFn=n}return e.readonlyFn}function sI(e){return function oI(e){return"function"==typeof e&&void 0!==e[ke]}(e)&&"function"==typeof e.set}function dt(e){let n=function vI(e){return Object.getPrototypeOf(e.prototype).constructor}(e.type),t=!0;const r=[e];for(;n;){let o;if(fo(e))o=n.\u0275cmp||n.\u0275dir;else{if(n.\u0275cmp)throw new z(903,!1);o=n.\u0275dir}if(o){if(t){r.push(o);const s=e;s.inputs=Zd(e.inputs),s.inputTransforms=Zd(e.inputTransforms),s.declaredInputs=Zd(e.declaredInputs),s.outputs=Zd(e.outputs);const a=o.hostBindings;a&&lL(e,a);const l=o.viewQuery,c=o.contentQueries;if(l&&sL(e,l),c&&aL(e,c),oL(e,o),On(e.outputs,o.outputs),fo(o)&&o.data.animation){const d=e.data;d.animation=(d.animation||[]).concat(o.data.animation)}}const i=o.features;if(i)for(let s=0;s<i.length;s++){const a=i[s];a&&a.ngInherit&&a(e),a===dt&&(t=!1)}}n=Object.getPrototypeOf(n)}!function iL(e){let n=0,t=null;for(let r=e.length-1;r>=0;r--){const o=e[r];o.hostVars=n+=o.hostVars,o.hostAttrs=Lt(o.hostAttrs,t=Lt(t,o.hostAttrs))}}(r)}function oL(e,n){for(const t in n.inputs){if(!n.inputs.hasOwnProperty(t)||e.inputs.hasOwnProperty(t))continue;const r=n.inputs[t];if(void 0!==r&&(e.inputs[t]=r,e.declaredInputs[t]=n.declaredInputs[t],null!==n.inputTransforms)){const o=Array.isArray(r)?r[0]:r;if(!n.inputTransforms.hasOwnProperty(o))continue;e.inputTransforms??={},e.inputTransforms[o]=n.inputTransforms[o]}}}function Zd(e){return e===E?{}:e===T?[]:e}function sL(e,n){const t=e.viewQuery;e.viewQuery=t?(r,o)=>{n(r,o),t(r,o)}:n}function aL(e,n){const t=e.contentQueries;e.contentQueries=t?(r,o,i)=>{n(r,o,i),t(r,o,i)}:n}function lL(e,n){const t=e.hostBindings;e.hostBindings=t?(r,o)=>{n(r,o),t(r,o)}:n}class Es{}class EI{}class ym extends Es{constructor(n,t,r,o=!0){super(),this.ngModuleType=n,this._parent=t,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new Uw(this);const i=Zn(n);this._bootstrapComponents=Lr(i.bootstrap),this._r3Injector=bb(n,t,[{provide:Es,useValue:this},{provide:Wd,useValue:this.componentFactoryResolver},...r],At(n),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){const n=this._r3Injector;!n.destroyed&&n.destroy(),this.destroyCbs.forEach(t=>t()),this.destroyCbs=null}onDestroy(n){this.destroyCbs.push(n)}}class Cm extends EI{constructor(n){super(),this.moduleType=n}create(n){return new ym(this.moduleType,n,[])}}class wI extends Es{constructor(n){super(),this.componentFactoryResolver=new Uw(this),this.instance=null;const t=new _a([...n.providers,{provide:Es,useValue:this},{provide:Wd,useValue:this.componentFactoryResolver}],n.parent||Qu(),n.debugName,new Set(["environment"]));this.injector=t,n.runEnvironmentInitializers&&t.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(n){this.injector.onDestroy(n)}}function _m(e,n,t=null){return new wI({providers:e,parent:n,debugName:t,runEnvironmentInitializers:!0}).injector}function Kd(e){return!!function Dm(e){return null!==e&&("function"==typeof e||"object"==typeof e)}(e)&&(Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e)}function Po(e,n,t){return e[n]=t}function _n(e,n,t){return!Object.is(e[n],t)&&(e[n]=t,!0)}function ws(e,n,t,r){const o=_n(e,n,t);return _n(e,n+1,r)||o}function kc(e,n,t,r,o,i,s,a,l,c){const d=t+Be,h=n.firstCreatePass?function _L(e,n,t,r,o,i,s,a,l){const c=n.consts,d=Cs(n,e,4,s||null,a||null);Vg(n,t,d,Jr(c,l)),cd(n,d);const h=d.tView=$g(2,d,r,o,i,n.directiveRegistry,n.pipeRegistry,null,n.schemas,c,null);return null!==n.queries&&(n.queries.template(n,d),h.queries=n.queries.embeddedTView(d)),d}(d,n,e,r,o,i,s,a,l):n.data[d];ho(h,!1);const v=II(n,e,h,t);Jl()&&Pd(n,e,v,h),Qn(v,e);const y=rw(v,e,v,h);return e[d]=y,Ld(e,y),function Xw(e,n,t){return lm(e,n,t)}(y,h,e),rd(h)&&Fg(n,e,h),null!=l&&Lg(e,h,c),h}function si(e,n,t,r,o,i,s,a){const l=$(),c=mt();return kc(l,c,e,n,t,r,o,Jr(c.consts,i),s,a),si}let II=function SI(e,n,t,r){return xo(!0),n[rt].createComment("")};var qa=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(qa||{});let RI=(()=>{class e{constructor(){this.impl=null}execute(){this.impl?.execute()}static{this.\u0275prov=Me({token:e,providedIn:"root",factory:()=>new e})}}return e})();class $c{constructor(){this.ngZone=U(j),this.scheduler=U(Ua),this.errorHandler=U(Ro,{optional:!0}),this.sequences=new Set,this.deferredRegistrations=new Set,this.executing=!1}static{this.PHASES=[qa.EarlyRead,qa.Write,qa.MixedReadWrite,qa.Read]}execute(){this.executing=!0;for(const n of $c.PHASES)for(const t of this.sequences)if(!t.erroredOrDestroyed&&t.hooks[n])try{t.pipelinedValue=this.ngZone.runOutsideAngular(()=>t.hooks[n](t.pipelinedValue))}catch(r){t.erroredOrDestroyed=!0,this.errorHandler?.handleError(r)}this.executing=!1;for(const n of this.sequences)n.afterRun(),n.once&&(this.sequences.delete(n),n.destroy());for(const n of this.deferredRegistrations)this.sequences.add(n);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear()}register(n){this.executing?this.deferredRegistrations.add(n):(this.sequences.add(n),this.scheduler.notify(6))}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}static{this.\u0275prov=Me({token:$c,providedIn:"root",factory:()=>new $c})}}function jr(e,n,t,r){const o=$();return _n(o,po(),n)&&(mt(),No(nn(),o,e,n,t,r)),jr}function tl(e,n,t,r,o,i){const a=ws(e,function ei(){return ot.lFrame.bindingIndex}(),t,o);return ti(2),a?n+tt(t)+r+tt(o)+i:st}function sf(e,n){return e<<17|n<<2}function xi(e){return e>>17&32767}function Nm(e){return 2|e}function Ss(e){return(131068&e)>>2}function Pm(e,n){return-131069&e|n<<2}function km(e){return 1|e}function a0(e,n,t,r){const o=e[t+1],i=null===n;let s=r?xi(o):Ss(o),a=!1;for(;0!==s&&(!1===a||i);){const c=e[s+1];l$(e[s],n)&&(a=!0,e[s+1]=r?km(c):Nm(c)),s=r?xi(c):Ss(c)}a&&(e[t+1]=r?Nm(o):km(o))}function l$(e,n){return null===e||null==n||(Array.isArray(e)?e[1]:e)===n||!(!Array.isArray(e)||"string"!=typeof n)&&_(e,n)>=0}function Tn(e,n,t){const r=$();return _n(r,po(),n)&&Sr(mt(),nn(),r,e,n,r[rt],t,!1),Tn}function Fm(e,n,t,r,o){const s=o?"class":"style";Hg(e,t,n.inputs[s],s,r)}function Oi(e,n){return function mo(e,n,t,r){const o=$(),i=mt(),s=ti(2);i.firstUpdatePass&&function m0(e,n,t,r){const o=e.data;if(null===o[t+1]){const i=o[Hn()],s=function g0(e,n){return n>=e.expandoStartIndex}(e,t);(function _0(e,n){return!!(e.flags&(n?8:16))})(i,r)&&null===n&&!s&&(n=!1),n=function v$(e,n,t,r){const o=function Pp(e){const n=ot.lFrame.currentDirectiveIndex;return-1===n?null:e[n]}(e);let i=r?n.residualClasses:n.residualStyles;if(null===o)0===(r?n.classBindings:n.styleBindings)&&(t=Bc(t=Lm(null,e,n,t,r),n.attrs,r),i=null);else{const s=n.directiveStylingLast;if(-1===s||e[s]!==o)if(t=Lm(o,e,n,t,r),null===i){let l=function y$(e,n,t){const r=t?n.classBindings:n.styleBindings;if(0!==Ss(r))return e[xi(r)]}(e,n,r);void 0!==l&&Array.isArray(l)&&(l=Lm(null,e,n,l[1],r),l=Bc(l,n.attrs,r),function C$(e,n,t,r){e[xi(t?n.classBindings:n.styleBindings)]=r}(e,n,r,l))}else i=function _$(e,n,t){let r;const o=n.directiveEnd;for(let i=1+n.directiveStylingLast;i<o;i++)r=Bc(r,e[i].hostAttrs,t);return Bc(r,n.attrs,t)}(e,n,r)}return void 0!==i&&(r?n.residualClasses=i:n.residualStyles=i),t}(o,i,n,r),function s$(e,n,t,r,o,i){let s=i?n.classBindings:n.styleBindings,a=xi(s),l=Ss(s);e[r]=t;let d,c=!1;if(Array.isArray(t)?(d=t[1],(null===d||_(t,d)>0)&&(c=!0)):d=t,o)if(0!==l){const v=xi(e[a+1]);e[r+1]=sf(v,a),0!==v&&(e[v+1]=Pm(e[v+1],r)),e[a+1]=function o$(e,n){return 131071&e|n<<17}(e[a+1],r)}else e[r+1]=sf(a,0),0!==a&&(e[a+1]=Pm(e[a+1],r)),a=r;else e[r+1]=sf(l,0),0===a?a=r:e[l+1]=Pm(e[l+1],r),l=r;c&&(e[r+1]=Nm(e[r+1])),a0(e,d,r,!0),a0(e,d,r,!1),function a$(e,n,t,r,o){const i=o?e.residualClasses:e.residualStyles;null!=i&&"string"==typeof n&&_(i,n)>=0&&(t[r+1]=km(t[r+1]))}(n,d,e,r,i),s=sf(a,l),i?n.classBindings=s:n.styleBindings=s}(o,i,n,t,s,r)}}(i,e,s,r),n!==st&&_n(o,s,n)&&function y0(e,n,t,r,o,i,s,a){if(!(3&n.type))return;const l=e.data,c=l[a+1],d=function i$(e){return!(1&~e)}(c)?C0(l,n,t,o,Ss(c),s):void 0;af(d)||(af(i)||function r$(e){return!(2&~e)}(c)&&(i=C0(l,null,t,o,a,s)),function BP(e,n,t,r,o){if(n)o?e.addClass(t,r):e.removeClass(t,r);else{let i=-1===r.indexOf("-")?void 0:Si.DashCase;null==o?e.removeStyle(t,r,i):("string"==typeof o&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Si.Important),e.setStyle(t,r,o,i))}}(r,s,Zl(Hn(),t),o,i))}(i,i.data[Hn()],o,o[rt],e,o[s+1]=function w$(e,n){return null==e||""===e||("string"==typeof n?e+=n:"object"==typeof e&&(e=At(function Ii(e){return e instanceof hE?e.changingThisBreaksApplicationSecurity:e}(e)))),e}(n,t),r,s)}(e,n,null,!0),Oi}function Lm(e,n,t,r,o){let i=null;const s=t.directiveEnd;let a=t.directiveStylingLast;for(-1===a?a=t.directiveStart:a++;a<s&&(i=n[a],r=Bc(r,i.hostAttrs,o),i!==e);)a++;return null!==e&&(t.directiveStylingLast=a),r}function Bc(e,n,t){const r=t?1:2;let o=-1;if(null!==n)for(let i=0;i<n.length;i++){const s=n[i];"number"==typeof s?o=s:o===r&&(Array.isArray(e)||(e=void 0===e?[]:["",e]),p(e,s,!!t||n[++i]))}return void 0===e?null:e}function C0(e,n,t,r,o,i){const s=null===n;let a;for(;o>0;){const l=e[o],c=Array.isArray(l),d=c?l[1]:l,h=null===d;let v=t[o+1];v===st&&(v=h?T:void 0);let y=h?m(v,r):d===r?v:void 0;if(c&&!af(y)&&(y=m(l,r)),af(y)&&(a=y,s))return a;const C=e[o+1];o=s?xi(C):Ss(C)}if(null!==n){let l=i?n.residualClasses:n.residualStyles;null!=l&&(a=m(l,r))}return a}function af(e){return void 0!==e}function rn(e,n,t,r){const o=$(),i=mt(),s=Be+e,a=o[rt],l=i.firstCreatePass?function q$(e,n,t,r,o,i){const s=n.consts,l=Cs(n,e,2,r,Jr(s,o));return Vg(n,t,l,Jr(s,i)),null!==l.attrs&&Xd(l,l.attrs,!1),null!==l.mergedAttrs&&Xd(l,l.mergedAttrs,!0),null!==n.queries&&n.queries.elementStart(n,l),l}(s,i,o,n,t,r):i.data[s],c=w0(i,o,l,a,n,e);o[s]=c;const d=rd(l);return ho(l,!0),qE(a,c,l),!function Ga(e){return!(32&~e.flags)}(l)&&Jl()&&Pd(i,o,c,l),0===function hN(){return ot.lFrame.elementDepthCount}()&&Qn(c,o),function pN(){ot.lFrame.elementDepthCount++}(),d&&(Fg(i,o,l),kg(i,l,o)),null!==r&&Lg(o,l),rn}function ln(){let e=Bt();Op()?Rp():(e=e.parent,ho(e,!1));const n=e;(function mN(e){return ot.skipHydrationRootTNode===e})(n)&&function _N(){ot.skipHydrationRootTNode=null}(),function gN(){ot.lFrame.elementDepthCount--}();const t=mt();return t.firstCreatePass&&(cd(t,e),bp(e)&&t.queries.elementEnd(e)),null!=n.classesWithoutHost&&function PN(e){return!!(8&e.flags)}(n)&&Fm(t,n,$(),n.classesWithoutHost,!0),null!=n.stylesWithoutHost&&function kN(e){return!!(16&e.flags)}(n)&&Fm(t,n,$(),n.stylesWithoutHost,!1),ln}function Ms(e,n,t,r){return rn(e,n,t,r),ln(),Ms}let w0=(e,n,t,r,o,i)=>(xo(!0),Rd(r,o,function nb(){return ot.lFrame.currentNamespace}()));function jc(){return $()}const Ts=void 0;var eV=["en",[["a","p"],["AM","PM"],Ts],[["AM","PM"],Ts,Ts],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],Ts,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],Ts,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",Ts,"{1} 'at' {0}",Ts],[".",",",";","%","+","-","E","\xd7","\u2030","\u221e","NaN",":"],["#,##0.###","#,##0%","\xa4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",function J$(e){const t=Math.floor(Math.abs(e)),r=e.toString().replace(/^[^.]*\.?/,"").length;return 1===t&&0===r?1:5}];let cl={};function hr(e){const n=function tV(e){return e.toLowerCase().replace(/_/g,"-")}(e);let t=A0(n);if(t)return t;const r=n.split("-")[0];if(t=A0(r),t)return t;if("en"===r)return eV;throw new z(701,!1)}function A0(e){return e in cl||(cl[e]=S.ng&&S.ng.common&&S.ng.common.locales&&S.ng.common.locales[e]),cl[e]}var on=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(on||{});const ff="en-US";let x0=ff,X0=(e,n,t)=>{};function sn(e,n,t,r){const o=$(),i=mt(),s=Bt();return Hm(i,o,o[rt],s,e,n,r),sn}function Hm(e,n,t,r,o,i,s){const a=rd(r),c=e.firstCreatePass&&function sw(e){return e.cleanup??=[]}(e),d=n[tn],h=function iw(e){return e[Ea]??=[]}(n);let v=!0;if(3&r.type||s){const b=ur(r,n),M=s?s(b):b,O=h.length,A=s?Ge=>s(Mt(Ge[r.index])):r.index;let Ee=null;if(!s&&a&&(Ee=function HV(e,n,t,r){const o=e.cleanup;if(null!=o)for(let i=0;i<o.length-1;i+=2){const s=o[i];if(s===t&&o[i+1]===r){const a=n[Ea],l=o[i+2];return a.length>l?a[l]:null}"string"==typeof s&&(i+=2)}return null}(e,n,o,r.index)),null!==Ee)(Ee.__ngLastListenerFn__||Ee).__ngNextListenerFn__=i,Ee.__ngLastListenerFn__=i,v=!1;else{i=Q0(r,n,d,i),X0(b,o,i);const Ge=t.listen(M,o,i);h.push(i,Ge),c&&c.push(o,A,O,O+1)}}else i=Q0(r,n,d,i);const y=r.outputs;let C;if(v&&null!==y&&(C=y[o])){const b=C.length;if(b)for(let M=0;M<b;M+=2){const vt=n[C[M]][C[M+1]].subscribe(i),Tt=h.length;h.push(i,vt),c&&c.push(o,r.index,Tt,-(Tt+1))}}}function K0(e,n,t,r){const o=Z(null);try{return Ao(6,n,t),!1!==t(r)}catch(i){return $d(e,i),!1}finally{Ao(7,n,t),Z(o)}}function Q0(e,n,t,r){return function o(i){if(i===Function)return r;_c(e.componentOffset>-1?Fr(e.index,n):n,5);let a=K0(n,t,r,i),l=o.__ngNextListenerFn__;for(;l;)a=K0(n,t,l,i)&&a,l=l.__ngNextListenerFn__;return a}}function Tr(e=1){return function IN(e){return(ot.lFrame.contextLView=function jD(e,n){for(;e>0;)n=n[wa],e--;return n}(e,ot.lFrame.contextLView))[tn]}(e)}function zV(e,n){let t=null;const r=function dp(e){const n=e.attrs;if(null!=n){const t=n.indexOf(5);if(!(1&t))return n[t+1]}return null}(e);for(let o=0;o<n.length;o++){const i=n[o];if("*"!==i){if(null===r?Gl(e,i,!0):RR(r,i))return o}else t=o}return t}function me(e){const n=$()[fn][Un];if(!n.projection){const r=n.projection=function ya(e,n){const t=[];for(let r=0;r<e;r++)t.push(n);return t}(e?e.length:1,null),o=r.slice();let i=n.child;for(;null!==i;){if(128!==i.type){const s=e?zV(i,e):0;null!==s&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function pe(e,n=0,t,r,o,i){const s=$(),a=mt(),l=r?e+1:null;null!==l&&kc(s,a,l,r,o,i,null,t);const c=Cs(a,Be+e,16,null,t||null);null===c.projection&&(c.projection=n),Rp();const h=!s[cr]||us();null===s[fn][Un].projection[c.projection]&&null!==l?function GV(e,n,t){const r=Be+t,o=n.data[r],i=e[r],s=bs(i,o.tView.ssrId);Ba(i,Va(e,o,void 0,{dehydratedView:s}),0,_s(o,s))}(s,a,l):h&&32&~c.flags&&function $P(e,n,t){GE(n[rt],0,n,t,Ag(e,t,n),VE(t.parent||n[Un],t,n))}(a,s,c)}function to(e,n=""){const t=$(),r=mt(),o=e+Be,i=r.firstCreatePass?Cs(r,o,1,n,null):r.data[o],s=vS(r,t,i,n,e);t[o]=s,Jl()&&Pd(r,t,s,i),ho(i,!1)}let vS=(e,n,t,r,o)=>(xo(!0),function Ig(e,n){return e.createText(n)}(n[rt],r));function Ri(e){return qc("",e,""),Ri}function qc(e,n,t){const r=$(),o=function el(e,n,t,r){return _n(e,po(),t)?n+tt(t)+r:st}(r,e,n,t);return o!==st&&oi(r,Hn(),o),qc}function Wm(e,n,t,r,o){const i=$(),s=tl(i,e,n,t,r,o);return s!==st&&oi(i,Hn(),s),Wm}function vf(e,n,t){sI(n)&&(n=n());const r=$();return _n(r,po(),n)&&Sr(mt(),nn(),r,e,n,r[rt],t,!1),vf}function qm(e,n){const t=sI(e);return t&&e.set(n),t}function yf(e,n){const t=$(),r=mt(),o=Bt();return Hm(r,t,t[rt],o,e,n),yf}function Xm(e,n,t,r,o){if(e=et(e),Array.isArray(e))for(let i=0;i<e.length;i++)Xm(e[i],n,t,r,o);else{const i=mt(),s=$(),a=Bt();let l=ss(e)?e:et(e.provide);const c=wD(e),d=1048575&a.providerIndexes,h=a.directiveStart,v=a.providerIndexes>>20;if(ss(e)||!e.multi){const y=new ec(c,o,g),C=Zm(l,n,o?d:d+v,h);-1===C?(zp(hd(a,s),i,l),Ym(i,e,n.length),n.push(l),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(y),s.push(y)):(t[C]=y,s[C]=y)}else{const y=Zm(l,n,d+v,h),C=Zm(l,n,d,d+v),M=C>=0&&t[C];if(o&&!M||!o&&!(y>=0&&t[y])){zp(hd(a,s),i,l);const O=function fB(e,n,t,r,o){const i=new ec(e,t,g);return i.multi=[],i.index=n,i.componentProviders=0,MS(i,o,r&&!t),i}(o?dB:uB,t.length,o,r,c);!o&&M&&(t[C].providerFactory=O),Ym(i,e,n.length,0),n.push(l),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(O),s.push(O)}else Ym(i,e,y>-1?y:C,MS(t[o?C:y],c,!o&&r));!o&&r&&M&&t[C].componentProviders++}}}function Ym(e,n,t,r){const o=ss(n),i=function jR(e){return!!e.useClass}(n);if(o||i){const l=(i?et(n.useClass):n).prototype.ngOnDestroy;if(l){const c=e.destroyHooks||(e.destroyHooks=[]);if(!o&&n.multi){const d=c.indexOf(t);-1===d?c.push(t,[r,l]):c[d+1].push(r,l)}else c.push(t,l)}}}function MS(e,n,t){return t&&e.componentProviders++,e.multi.push(n)-1}function Zm(e,n,t,r){for(let o=t;o<r;o++)if(n[o]===e)return o;return-1}function uB(e,n,t,r){return Km(this.multi,[])}function dB(e,n,t,r){const o=this.multi;let i;if(this.providerFactory){const s=this.providerFactory.componentProviders,a=gs(t,t[he],this.providerFactory.index,r);i=a.slice(0,s),Km(o,i);for(let l=s;l<a.length;l++)i.push(a[l])}else i=[],Km(o,i);return i}function Km(e,n){for(let t=0;t<e.length;t++)n.push((0,e[t])());return n}function Ft(e,n=[]){return t=>{t.providersResolver=(r,o)=>function cB(e,n,t){const r=mt();if(r.firstCreatePass){const o=fo(e);Xm(t,r.data,r.blueprint,o,!0),Xm(n,r.data,r.blueprint,o,!1)}}(r,o?o(e):e,n)}}let hB=(()=>{class e{constructor(t){this._injector=t,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(t){if(!t.standalone)return null;if(!this.cachedInjectors.has(t)){const r=fp(0,t.type),o=r.length>0?_m([r],this._injector,`Standalone[${t.type.name}]`):null;this.cachedInjectors.set(t,o)}return this.cachedInjectors.get(t)}ngOnDestroy(){try{for(const t of this.cachedInjectors.values())null!==t&&t.destroy()}finally{this.cachedInjectors.clear()}}static{this.\u0275prov=Me({token:e,providedIn:"environment",factory:()=>new e(Pe(Pn))})}}return e})();function Xc(e){fr("NgStandalone"),e.getStandaloneInjector=n=>n.get(hB).getOrCreateStandaloneInjector(e)}function Yc(e,n){const t=e[n];return t===st?void 0:t}function Ar(e,n){const t=mt();let r;const o=e+Be;t.firstCreatePass?(r=function MB(e,n){if(n)for(let t=n.length-1;t>=0;t--){const r=n[t];if(e===r.name)return r}}(n,t.pipeRegistry),t.data[o]=r,r.onDestroy&&(t.destroyHooks??=[]).push(o,r.onDestroy)):r=t.data[o];const i=r.factory||(r.factory=So(r.type)),a=qn(g);try{const l=fd(!1),c=i();return fd(l),function Gm(e,n,t,r){t>=e.data.length&&(e.data[t]=null,e.blueprint[t]=null),n[t]=r}(t,$(),o,c),c}finally{qn(a)}}function Ur(e,n,t){const r=e+Be,o=$(),i=cs(o,r);return Zc(o,r)?function xS(e,n,t,r,o,i){const s=n+t;return _n(e,s,o)?Po(e,s+1,i?r.call(i,o):r(o)):Yc(e,s+1)}(o,dr(),n,i.transform,t,i):i.transform(t)}function kS(e,n,t,r){const o=e+Be,i=$(),s=cs(i,o);return Zc(i,o)?function OS(e,n,t,r,o,i,s){const a=n+t;return ws(e,a,o,i)?Po(e,a+2,s?r.call(s,o,i):r(o,i)):Yc(e,a+2)}(i,dr(),n,s.transform,t,r,s):s.transform(t,r)}function Zc(e,n){return e[he].data[n].pure}let KS=(()=>{class e{log(t){console.log(t)}warn(t){console.warn(t)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();const nM=new ce("");function eu(e){return!!e&&"function"==typeof e.then}function rM(e){return!!e&&"function"==typeof e.subscribe}const oM=new ce("");let iM=(()=>{class e{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((t,r)=>{this.resolve=t,this.reject=r}),this.appInits=U(oM,{optional:!0})??[]}runInitializers(){if(this.initialized)return;const t=[];for(const o of this.appInits){const i=o();if(eu(i))t.push(i);else if(rM(i)){const s=new Promise((a,l)=>{i.subscribe({complete:a,error:l})});t.push(s)}}const r=()=>{this.done=!0,this.resolve()};Promise.all(t).then(()=>{r()}).catch(o=>{this.reject(o)}),0===t.length&&r(),this.initialized=!0}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();const wf=new ce("");let yo=(()=>{class e{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=U(c1),this.afterRenderManager=U(RI),this.zonelessEnabled=U(Ac),this.dirtyFlags=0,this.deferredDirtyFlags=0,this.externalTestViews=new Set,this.beforeRender=new jt,this.afterTick=new jt,this.componentTypes=[],this.components=[],this.isStable=U(bi).hasPendingTasks.pipe(ct(t=>!t)),this._injector=U(Pn)}get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}whenStable(){let t;return new Promise(r=>{t=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{t.unsubscribe()})}get injector(){return this._injector}bootstrap(t,r){const o=t instanceof $w;if(!this._injector.get(iM).done)throw!o&&function _i(e){const n=Ct(e)||Nn(e)||jn(e);return null!==n&&n.standalone}(t),new z(405,!1);let s;s=o?t:this._injector.get(Wd).resolveComponentFactory(t),this.componentTypes.push(s.componentType);const a=function Ij(e){return e.isBoundToModule}(s)?void 0:this._injector.get(Es),c=s.create(Zt.NULL,[],r||s.selector,a),d=c.location.nativeElement,h=c.injector.get(nM,null);return h?.registerApplication(d),c.onDestroy(()=>{this.detachView(c.hostView),If(this.components,c),h?.unregisterApplication(d)}),this._loadComponent(c),c}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){if(this._runningTick)throw new z(101,!1);const t=Z(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,Z(t),this.afterTick.next()}}synchronize(){let t=null;this._injector.destroyed||(t=this._injector.get(om,null,{optional:!0})),this.dirtyFlags|=this.deferredDirtyFlags,this.deferredDirtyFlags=0;let r=0;for(;0!==this.dirtyFlags&&r++<10;)this.synchronizeOnce(t)}synchronizeOnce(t){if(this.dirtyFlags|=this.deferredDirtyFlags,this.deferredDirtyFlags=0,7&this.dirtyFlags){const r=!!(1&this.dirtyFlags);this.dirtyFlags&=-8,this.dirtyFlags|=8,this.beforeRender.next(r);for(let{_lView:o,notifyErrorHandler:i}of this._views)Tj(o,i,r,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),7&this.dirtyFlags)return}else t?.begin?.(),t?.end?.();8&this.dirtyFlags&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){this.allViews.some(({_lView:t})=>od(t))?this.dirtyFlags|=2:this.dirtyFlags&=-8}attachView(t){const r=t;this._views.push(r),r.attachToAppRef(this)}detachView(t){const r=t;If(this._views,r),r.detachFromAppRef()}_loadComponent(t){this.attachView(t.hostView),this.tick(),this.components.push(t);const r=this._injector.get(wf,[]);[...this._bootstrapListeners,...r].forEach(o=>o(t))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(t=>t()),this._views.slice().forEach(t=>t.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(t){return this._destroyListeners.push(t),()=>If(this._destroyListeners,t)}destroy(){if(this._destroyed)throw new z(406,!1);const t=this._injector;t.destroy&&!t.destroyed&&t.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function If(e,n){const t=e.indexOf(n);t>-1&&e.splice(t,1)}function Tj(e,n,t,r){(t||od(e))&&Vd(e,n,t&&!r?0:1)}class Aj{constructor(n,t){this.ngModuleFactory=n,this.componentFactories=t}}let xj=(()=>{class e{compileModuleSync(t){return new Cm(t)}compileModuleAsync(t){return Promise.resolve(this.compileModuleSync(t))}compileModuleAndAllComponentsSync(t){const r=this.compileModuleSync(t),i=Lr(Zn(t).declarations).reduce((s,a)=>{const l=Ct(a);return l&&s.push(new Rc(l)),s},[]);return new Aj(r,i)}compileModuleAndAllComponentsAsync(t){return Promise.resolve(this.compileModuleAndAllComponentsSync(t))}clearCache(){}clearCacheFor(t){}getModuleId(t){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Nj=(()=>{class e{constructor(){this.zone=U(j),this.changeDetectionScheduler=U(Ua),this.applicationRef=U(yo)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function sv({ngZoneFactory:e,ignoreChangesOutsideZone:n,scheduleInRootZone:t}){return e??=()=>new j({...av(),scheduleInRootZone:t}),[{provide:j,useFactory:e},{provide:w,multi:!0,useFactory:()=>{const r=U(Nj,{optional:!0});return()=>r.initialize()}},{provide:w,multi:!0,useFactory:()=>{const r=U(kj);return()=>{r.initialize()}}},!0===n?{provide:Lw,useValue:!0}:[],{provide:rm,useValue:t??wb}]}function av(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}let kj=(()=>{class e{constructor(){this.subscription=new Ze,this.initialized=!1,this.zone=U(j),this.pendingTasks=U(bi)}initialize(){if(this.initialized)return;this.initialized=!0;let t=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(t=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{j.assertNotInAngularZone(),queueMicrotask(()=>{null!==t&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(t),t=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{j.assertInAngularZone(),t??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),tu=(()=>{class e{constructor(){this.appRef=U(yo),this.taskService=U(bi),this.ngZone=U(j),this.zonelessEnabled=U(Ac),this.disableScheduling=U(Lw,{optional:!0})??!1,this.zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run,this.schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}],this.subscriptions=new Ze,this.angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(yd):null,this.scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(U(rm,{optional:!0})??!1),this.cancelScheduledCallback=null,this.useMicrotaskScheduler=!1,this.runningTick=!1,this.pendingRenderTaskId=null,this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Kp||!this.zoneIsDefined)}notify(t){if(!this.zonelessEnabled&&5===t)return;switch(t){case 0:this.appRef.dirtyFlags|=2;break;case 3:case 2:case 4:case 5:case 1:this.appRef.dirtyFlags|=4;break;case 7:this.appRef.deferredDirtyFlags|=8;break;default:this.appRef.dirtyFlags|=8}if(!this.shouldScheduleTick())return;const r=this.useMicrotaskScheduler?Sb:Ib;this.pendingRenderTaskId=this.taskService.add(),this.cancelScheduledCallback=this.scheduleInRootZone?Zone.root.run(()=>r(()=>this.tick())):this.ngZone.runOutsideAngular(()=>r(()=>this.tick()))}shouldScheduleTick(){return!(this.disableScheduling||null!==this.pendingRenderTaskId||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(yd+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;!this.zonelessEnabled&&7&this.appRef.dirtyFlags&&(this.appRef.dirtyFlags|=1);const t=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(t),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Sb(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(t)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,null!==this.pendingRenderTaskId){const t=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(t)}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();const Ni=new ce("",{providedIn:"root",factory:()=>U(Ni,ut.Optional|ut.SkipSelf)||function Fj(){return typeof $localize<"u"&&$localize.locale||ff}()}),Mf=new ce("");function Tf(e){return!e.moduleRef}let Pi=null;let ae=(()=>{class e{static{this.__NG_ELEMENT_ID__=Wj}}return e})();function Wj(e){return function qj(e,n,t){if(ls(e)&&!t){const r=Fr(e.index,n);return new Dc(r,r)}return 175&e.type?new Dc(n[fn],n):null}(Bt(),$(),!(16&~e))}class bM{constructor(){}supports(n){return Kd(n)}create(n){return new Qj(n)}}const Kj=(e,n)=>n;class Qj{constructor(n){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=n||Kj}forEachItem(n){let t;for(t=this._itHead;null!==t;t=t._next)n(t)}forEachOperation(n){let t=this._itHead,r=this._removalsHead,o=0,i=null;for(;t||r;){const s=!r||t&&t.currentIndex<wM(r,o,i)?t:r,a=wM(s,o,i),l=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(t=t._next,null==s.previousIndex)o++;else{i||(i=[]);const c=a-o,d=l-o;if(c!=d){for(let v=0;v<c;v++){const y=v<i.length?i[v]:i[v]=0,C=y+v;d<=C&&C<c&&(i[v]=y+1)}i[s.previousIndex]=d-c}}a!==l&&n(s,a,l)}}forEachPreviousItem(n){let t;for(t=this._previousItHead;null!==t;t=t._nextPrevious)n(t)}forEachAddedItem(n){let t;for(t=this._additionsHead;null!==t;t=t._nextAdded)n(t)}forEachMovedItem(n){let t;for(t=this._movesHead;null!==t;t=t._nextMoved)n(t)}forEachRemovedItem(n){let t;for(t=this._removalsHead;null!==t;t=t._nextRemoved)n(t)}forEachIdentityChange(n){let t;for(t=this._identityChangesHead;null!==t;t=t._nextIdentityChange)n(t)}diff(n){if(null==n&&(n=[]),!Kd(n))throw new z(900,!1);return this.check(n)?this:null}onDestroy(){}check(n){this._reset();let o,i,s,t=this._itHead,r=!1;if(Array.isArray(n)){this.length=n.length;for(let a=0;a<this.length;a++)i=n[a],s=this._trackByFn(a,i),null!==t&&Object.is(t.trackById,s)?(r&&(t=this._verifyReinsertion(t,i,s,a)),Object.is(t.item,i)||this._addIdentityChange(t,i)):(t=this._mismatch(t,i,s,a),r=!0),t=t._next}else o=0,function yL(e,n){if(Array.isArray(e))for(let t=0;t<e.length;t++)n(e[t]);else{const t=e[Symbol.iterator]();let r;for(;!(r=t.next()).done;)n(r.value)}}(n,a=>{s=this._trackByFn(o,a),null!==t&&Object.is(t.trackById,s)?(r&&(t=this._verifyReinsertion(t,a,s,o)),Object.is(t.item,a)||this._addIdentityChange(t,a)):(t=this._mismatch(t,a,s,o),r=!0),t=t._next,o++}),this.length=o;return this._truncate(t),this.collection=n,this.isDirty}get isDirty(){return null!==this._additionsHead||null!==this._movesHead||null!==this._removalsHead||null!==this._identityChangesHead}_reset(){if(this.isDirty){let n;for(n=this._previousItHead=this._itHead;null!==n;n=n._next)n._nextPrevious=n._next;for(n=this._additionsHead;null!==n;n=n._nextAdded)n.previousIndex=n.currentIndex;for(this._additionsHead=this._additionsTail=null,n=this._movesHead;null!==n;n=n._nextMoved)n.previousIndex=n.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(n,t,r,o){let i;return null===n?i=this._itTail:(i=n._prev,this._remove(n)),null!==(n=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null))?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._reinsertAfter(n,i,o)):null!==(n=null===this._linkedRecords?null:this._linkedRecords.get(r,o))?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._moveAfter(n,i,o)):n=this._addAfter(new Jj(t,r),i,o),n}_verifyReinsertion(n,t,r,o){let i=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null);return null!==i?n=this._reinsertAfter(i,n._prev,o):n.currentIndex!=o&&(n.currentIndex=o,this._addToMoves(n,o)),n}_truncate(n){for(;null!==n;){const t=n._next;this._addToRemovals(this._unlink(n)),n=t}null!==this._unlinkedRecords&&this._unlinkedRecords.clear(),null!==this._additionsTail&&(this._additionsTail._nextAdded=null),null!==this._movesTail&&(this._movesTail._nextMoved=null),null!==this._itTail&&(this._itTail._next=null),null!==this._removalsTail&&(this._removalsTail._nextRemoved=null),null!==this._identityChangesTail&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(n,t,r){null!==this._unlinkedRecords&&this._unlinkedRecords.remove(n);const o=n._prevRemoved,i=n._nextRemoved;return null===o?this._removalsHead=i:o._nextRemoved=i,null===i?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(n,t,r),this._addToMoves(n,r),n}_moveAfter(n,t,r){return this._unlink(n),this._insertAfter(n,t,r),this._addToMoves(n,r),n}_addAfter(n,t,r){return this._insertAfter(n,t,r),this._additionsTail=null===this._additionsTail?this._additionsHead=n:this._additionsTail._nextAdded=n,n}_insertAfter(n,t,r){const o=null===t?this._itHead:t._next;return n._next=o,n._prev=t,null===o?this._itTail=n:o._prev=n,null===t?this._itHead=n:t._next=n,null===this._linkedRecords&&(this._linkedRecords=new EM),this._linkedRecords.put(n),n.currentIndex=r,n}_remove(n){return this._addToRemovals(this._unlink(n))}_unlink(n){null!==this._linkedRecords&&this._linkedRecords.remove(n);const t=n._prev,r=n._next;return null===t?this._itHead=r:t._next=r,null===r?this._itTail=t:r._prev=t,n}_addToMoves(n,t){return n.previousIndex===t||(this._movesTail=null===this._movesTail?this._movesHead=n:this._movesTail._nextMoved=n),n}_addToRemovals(n){return null===this._unlinkedRecords&&(this._unlinkedRecords=new EM),this._unlinkedRecords.put(n),n.currentIndex=null,n._nextRemoved=null,null===this._removalsTail?(this._removalsTail=this._removalsHead=n,n._prevRemoved=null):(n._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=n),n}_addIdentityChange(n,t){return n.item=t,this._identityChangesTail=null===this._identityChangesTail?this._identityChangesHead=n:this._identityChangesTail._nextIdentityChange=n,n}}class Jj{constructor(n,t){this.item=n,this.trackById=t,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}}class e2{constructor(){this._head=null,this._tail=null}add(n){null===this._head?(this._head=this._tail=n,n._nextDup=null,n._prevDup=null):(this._tail._nextDup=n,n._prevDup=this._tail,n._nextDup=null,this._tail=n)}get(n,t){let r;for(r=this._head;null!==r;r=r._nextDup)if((null===t||t<=r.currentIndex)&&Object.is(r.trackById,n))return r;return null}remove(n){const t=n._prevDup,r=n._nextDup;return null===t?this._head=r:t._nextDup=r,null===r?this._tail=t:r._prevDup=t,null===this._head}}class EM{constructor(){this.map=new Map}put(n){const t=n.trackById;let r=this.map.get(t);r||(r=new e2,this.map.set(t,r)),r.add(n)}get(n,t){const o=this.map.get(n);return o?o.get(n,t):null}remove(n){const t=n.trackById;return this.map.get(t).remove(n)&&this.map.delete(t),n}get isEmpty(){return 0===this.map.size}clear(){this.map.clear()}}function wM(e,n,t){const r=e.previousIndex;if(null===r)return r;let o=0;return t&&r<t.length&&(o=t[r]),r+n+o}function SM(){return new pv([new bM])}let pv=(()=>{class e{static{this.\u0275prov=Me({token:e,providedIn:"root",factory:SM})}constructor(t){this.factories=t}static create(t,r){if(null!=r){const o=r.factories.slice();t=t.concat(o)}return new e(t)}static extend(t){return{provide:e,useFactory:r=>e.create(t,r||SM()),deps:[[e,new Ul,new Bl]]}}find(t){const r=this.factories.find(o=>o.supports(t));if(null!=r)return r;throw new z(901,!1)}}return e})();function m2(e){try{const{rootComponent:n,appProviders:t,platformProviders:r}=e,o=function Gj(e=[]){if(Pi)return Pi;const n=function mM(e=[],n){return Zt.create({name:n,providers:[{provide:gp,useValue:"platform"},{provide:Mf,useValue:new Set([()=>Pi=null])},...e]})}(e);return Pi=n,function sM(){!function B(e){V=e}(()=>{throw new z(600,!1)})}(),function vM(e){e.get(Kb,null)?.forEach(t=>t())}(n),n}(r),i=[sv({}),{provide:Ua,useExisting:tu},...t||[]];return function hM(e){const n=Tf(e)?e.r3Injector:e.moduleRef.injector,t=n.get(j);return t.run(()=>{Tf(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();const r=n.get(Ro,null);let o;if(t.runOutsideAngular(()=>{o=t.onError.subscribe({next:i=>{r.handleError(i)}})}),Tf(e)){const i=()=>n.destroy(),s=e.platformInjector.get(Mf);s.add(i),n.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{const i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Mf);s.add(i),e.moduleRef.onDestroy(()=>{If(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return function Mj(e,n,t){try{const r=t();return eu(r)?r.catch(o=>{throw n.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw n.runOutsideAngular(()=>e.handleError(r)),r}}(r,t,()=>{const i=n.get(iM);return i.runInitializers(),i.donePromise.then(()=>{if(function iV(e){"string"==typeof e&&(x0=e.toLowerCase().replace(/_/g,"-"))}(n.get(Ni,ff)||ff),Tf(e)){const a=n.get(yo);return void 0!==e.rootComponent&&a.bootstrap(e.rootComponent),a}return function Uj(e,n){const t=e.injector.get(yo);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>t.bootstrap(r));else{if(!e.instance.ngDoBootstrap)throw new z(-403,!1);e.instance.ngDoBootstrap(t)}n.push(e)}(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}({r3Injector:new wI({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1}).injector,platformInjector:o,rootComponent:n})}catch(n){return Promise.reject(n)}}function iu(e,n){fr("NgSignals");const t=function Ne(e){const n=Object.create(at);n.computation=e;const t=()=>{if(I(n),He(n),n.value===We)throw n.error;return n.value};return t[ke]=n,t}(e);return n?.equal&&(t[ke].equal=n.equal),t}function Vo(e){const n=Z(null);try{return e()}finally{Z(n)}}let ZM=null;function ki(){return ZM}class G2{}const Hr=new ce("");let KM=(()=>{class e{historyGo(t){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:()=>U(W2),providedIn:"platform"})}}return e})(),W2=(()=>{class e extends KM{constructor(){super(),this._doc=U(Hr),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return ki().getBaseHref(this._doc)}onPopState(t){const r=ki().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",t,!1),()=>r.removeEventListener("popstate",t)}onHashChange(t){const r=ki().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",t,!1),()=>r.removeEventListener("hashchange",t)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(t){this._location.pathname=t}pushState(t,r,o){this._history.pushState(t,r,o)}replaceState(t,r,o){this._history.replaceState(t,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(t=0){this._history.go(t)}getState(){return this._history.state}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:()=>new e,providedIn:"platform"})}}return e})();function QM(e,n){if(0==e.length)return n;if(0==n.length)return e;let t=0;return e.endsWith("/")&&t++,n.startsWith("/")&&t++,2==t?e+n.substring(1):1==t?e+n:e+"/"+n}function JM(e){const n=e.match(/#|\?|$/),t=n&&n.index||e.length;return e.slice(0,t-("/"===e[t-1]?1:0))+e.slice(t)}function Os(e){return e&&"?"!==e[0]?"?"+e:e}let hl=(()=>{class e{historyGo(t){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:()=>U(X2),providedIn:"root"})}}return e})();const q2=new ce("");let X2=(()=>{class e extends hl{constructor(t,r){super(),this._platformLocation=t,this._removeListenerFns=[],this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??U(Hr).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}prepareExternalUrl(t){return QM(this._baseHref,t)}path(t=!1){const r=this._platformLocation.pathname+Os(this._platformLocation.search),o=this._platformLocation.hash;return o&&t?`${r}${o}`:r}pushState(t,r,o,i){const s=this.prepareExternalUrl(o+Os(i));this._platformLocation.pushState(t,r,s)}replaceState(t,r,o,i){const s=this.prepareExternalUrl(o+Os(i));this._platformLocation.replaceState(t,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}static{this.\u0275fac=function(r){return new(r||e)(Pe(KM),Pe(q2,8))}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Fi=(()=>{class e{constructor(t){this._subject=new Dt,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=t;const r=this._locationStrategy.getBaseHref();this._basePath=function K2(e){if(new RegExp("^(https?:)?//").test(e)){const[,t]=e.split(/\/\/[^\/]+/);return t}return e}(JM(eT(r))),this._locationStrategy.onPopState(o=>{this._subject.emit({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(t=!1){return this.normalize(this._locationStrategy.path(t))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(t,r=""){return this.path()==this.normalize(t+Os(r))}normalize(t){return e.stripTrailingSlash(function Z2(e,n){if(!e||!n.startsWith(e))return n;const t=n.substring(e.length);return""===t||["/",";","?","#"].includes(t[0])?t:n}(this._basePath,eT(t)))}prepareExternalUrl(t){return t&&"/"!==t[0]&&(t="/"+t),this._locationStrategy.prepareExternalUrl(t)}go(t,r="",o=null){this._locationStrategy.pushState(o,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+Os(r)),o)}replaceState(t,r="",o=null){this._locationStrategy.replaceState(o,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+Os(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(t=0){this._locationStrategy.historyGo?.(t)}onUrlChange(t){return this._urlChangeListeners.push(t),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{const r=this._urlChangeListeners.indexOf(t);this._urlChangeListeners.splice(r,1),0===this._urlChangeListeners.length&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(t="",r){this._urlChangeListeners.forEach(o=>o(t,r))}subscribe(t,r,o){return this._subject.subscribe({next:t,error:r,complete:o})}static{this.normalizeQueryParams=Os}static{this.joinWithSlash=QM}static{this.stripTrailingSlash=JM}static{this.\u0275fac=function(r){return new(r||e)(Pe(hl))}}static{this.\u0275prov=Me({token:e,factory:()=>function Y2(){return new Fi(Pe(hl))}(),providedIn:"root"})}}return e})();function eT(e){return e.replace(/\/index.html$/,"")}var gr=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(gr||{}),Kt=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(Kt||{}),zr=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(zr||{});const An={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function Vf(e,n){return ro(hr(e)[on.DateFormat],n)}function Bf(e,n){return ro(hr(e)[on.TimeFormat],n)}function jf(e,n){return ro(hr(e)[on.DateTimeFormat],n)}function no(e,n){const t=hr(e),r=t[on.NumberSymbols][n];if(typeof r>"u"){if(n===An.CurrencyDecimal)return t[on.NumberSymbols][An.Decimal];if(n===An.CurrencyGroup)return t[on.NumberSymbols][An.Group]}return r}function nT(e){if(!e[on.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[on.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function ro(e,n){for(let t=n;t>-1;t--)if(typeof e[t]<"u")return e[t];throw new Error("Locale data API: locale data undefined")}function yv(e){const[n,t]=e.split(":");return{hours:+n,minutes:+t}}const dU=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,Uf={},fU=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;var ai=function(e){return e[e.Short=0]="Short",e[e.ShortGMT=1]="ShortGMT",e[e.Long=2]="Long",e[e.Extended=3]="Extended",e}(ai||{}),Rt=function(e){return e[e.FullYear=0]="FullYear",e[e.Month=1]="Month",e[e.Date=2]="Date",e[e.Hours=3]="Hours",e[e.Minutes=4]="Minutes",e[e.Seconds=5]="Seconds",e[e.FractionalSeconds=6]="FractionalSeconds",e[e.Day=7]="Day",e}(Rt||{}),Nt=function(e){return e[e.DayPeriods=0]="DayPeriods",e[e.Days=1]="Days",e[e.Months=2]="Months",e[e.Eras=3]="Eras",e}(Nt||{});function hU(e,n,t,r){let o=function bU(e){if(iT(e))return e;if("number"==typeof e&&!isNaN(e))return new Date(e);if("string"==typeof e){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){const[o,i=1,s=1]=e.split("-").map(a=>+a);return Hf(o,i-1,s)}const t=parseFloat(e);if(!isNaN(e-t))return new Date(t);let r;if(r=e.match(dU))return function EU(e){const n=new Date(0);let t=0,r=0;const o=e[8]?n.setUTCFullYear:n.setFullYear,i=e[8]?n.setUTCHours:n.setHours;e[9]&&(t=Number(e[9]+e[10]),r=Number(e[9]+e[11])),o.call(n,Number(e[1]),Number(e[2])-1,Number(e[3]));const s=Number(e[4]||0)-t,a=Number(e[5]||0)-r,l=Number(e[6]||0),c=Math.floor(1e3*parseFloat("0."+(e[7]||0)));return i.call(n,s,a,l,c),n}(r)}const n=new Date(e);if(!iT(n))throw new Error(`Unable to convert "${e}" into a date`);return n}(e);n=li(t,n)||n;let a,s=[];for(;n;){if(a=fU.exec(n),!a){s.push(n);break}{s=s.concat(a.slice(1));const d=s.pop();if(!d)break;n=d}}let l=o.getTimezoneOffset();r&&(l=oT(r,l),o=function DU(e,n,t){const r=t?-1:1,o=e.getTimezoneOffset();return function _U(e,n){return(e=new Date(e.getTime())).setMinutes(e.getMinutes()+n),e}(e,r*(oT(n,o)-o))}(o,r,!0));let c="";return s.forEach(d=>{const h=function CU(e){if(_v[e])return _v[e];let n;switch(e){case"G":case"GG":case"GGG":n=an(Nt.Eras,Kt.Abbreviated);break;case"GGGG":n=an(Nt.Eras,Kt.Wide);break;case"GGGGG":n=an(Nt.Eras,Kt.Narrow);break;case"y":n=xn(Rt.FullYear,1,0,!1,!0);break;case"yy":n=xn(Rt.FullYear,2,0,!0,!0);break;case"yyy":n=xn(Rt.FullYear,3,0,!1,!0);break;case"yyyy":n=xn(Rt.FullYear,4,0,!1,!0);break;case"Y":n=qf(1);break;case"YY":n=qf(2,!0);break;case"YYY":n=qf(3);break;case"YYYY":n=qf(4);break;case"M":case"L":n=xn(Rt.Month,1,1);break;case"MM":case"LL":n=xn(Rt.Month,2,1);break;case"MMM":n=an(Nt.Months,Kt.Abbreviated);break;case"MMMM":n=an(Nt.Months,Kt.Wide);break;case"MMMMM":n=an(Nt.Months,Kt.Narrow);break;case"LLL":n=an(Nt.Months,Kt.Abbreviated,gr.Standalone);break;case"LLLL":n=an(Nt.Months,Kt.Wide,gr.Standalone);break;case"LLLLL":n=an(Nt.Months,Kt.Narrow,gr.Standalone);break;case"w":n=Cv(1);break;case"ww":n=Cv(2);break;case"W":n=Cv(1,!0);break;case"d":n=xn(Rt.Date,1);break;case"dd":n=xn(Rt.Date,2);break;case"c":case"cc":n=xn(Rt.Day,1);break;case"ccc":n=an(Nt.Days,Kt.Abbreviated,gr.Standalone);break;case"cccc":n=an(Nt.Days,Kt.Wide,gr.Standalone);break;case"ccccc":n=an(Nt.Days,Kt.Narrow,gr.Standalone);break;case"cccccc":n=an(Nt.Days,Kt.Short,gr.Standalone);break;case"E":case"EE":case"EEE":n=an(Nt.Days,Kt.Abbreviated);break;case"EEEE":n=an(Nt.Days,Kt.Wide);break;case"EEEEE":n=an(Nt.Days,Kt.Narrow);break;case"EEEEEE":n=an(Nt.Days,Kt.Short);break;case"a":case"aa":case"aaa":n=an(Nt.DayPeriods,Kt.Abbreviated);break;case"aaaa":n=an(Nt.DayPeriods,Kt.Wide);break;case"aaaaa":n=an(Nt.DayPeriods,Kt.Narrow);break;case"b":case"bb":case"bbb":n=an(Nt.DayPeriods,Kt.Abbreviated,gr.Standalone,!0);break;case"bbbb":n=an(Nt.DayPeriods,Kt.Wide,gr.Standalone,!0);break;case"bbbbb":n=an(Nt.DayPeriods,Kt.Narrow,gr.Standalone,!0);break;case"B":case"BB":case"BBB":n=an(Nt.DayPeriods,Kt.Abbreviated,gr.Format,!0);break;case"BBBB":n=an(Nt.DayPeriods,Kt.Wide,gr.Format,!0);break;case"BBBBB":n=an(Nt.DayPeriods,Kt.Narrow,gr.Format,!0);break;case"h":n=xn(Rt.Hours,1,-12);break;case"hh":n=xn(Rt.Hours,2,-12);break;case"H":n=xn(Rt.Hours,1);break;case"HH":n=xn(Rt.Hours,2);break;case"m":n=xn(Rt.Minutes,1);break;case"mm":n=xn(Rt.Minutes,2);break;case"s":n=xn(Rt.Seconds,1);break;case"ss":n=xn(Rt.Seconds,2);break;case"S":n=xn(Rt.FractionalSeconds,1);break;case"SS":n=xn(Rt.FractionalSeconds,2);break;case"SSS":n=xn(Rt.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":n=Gf(ai.Short);break;case"ZZZZZ":n=Gf(ai.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":n=Gf(ai.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":n=Gf(ai.Long);break;default:return null}return _v[e]=n,n}(d);c+=h?h(o,t,l):"''"===d?"'":d.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),c}function Hf(e,n,t){const r=new Date(0);return r.setFullYear(e,n,t),r.setHours(0,0,0),r}function li(e,n){const t=function J2(e){return hr(e)[on.LocaleId]}(e);if(Uf[t]??={},Uf[t][n])return Uf[t][n];let r="";switch(n){case"shortDate":r=Vf(e,zr.Short);break;case"mediumDate":r=Vf(e,zr.Medium);break;case"longDate":r=Vf(e,zr.Long);break;case"fullDate":r=Vf(e,zr.Full);break;case"shortTime":r=Bf(e,zr.Short);break;case"mediumTime":r=Bf(e,zr.Medium);break;case"longTime":r=Bf(e,zr.Long);break;case"fullTime":r=Bf(e,zr.Full);break;case"short":const o=li(e,"shortTime"),i=li(e,"shortDate");r=zf(jf(e,zr.Short),[o,i]);break;case"medium":const s=li(e,"mediumTime"),a=li(e,"mediumDate");r=zf(jf(e,zr.Medium),[s,a]);break;case"long":const l=li(e,"longTime"),c=li(e,"longDate");r=zf(jf(e,zr.Long),[l,c]);break;case"full":const d=li(e,"fullTime"),h=li(e,"fullDate");r=zf(jf(e,zr.Full),[d,h])}return r&&(Uf[t][n]=r),r}function zf(e,n){return n&&(e=e.replace(/\{([^}]+)}/g,function(t,r){return null!=n&&r in n?n[r]:t})),e}function Co(e,n,t="-",r,o){let i="";(e<0||o&&e<=0)&&(o?e=1-e:(e=-e,i=t));let s=String(e);for(;s.length<n;)s="0"+s;return r&&(s=s.slice(s.length-n)),i+s}function xn(e,n,t=0,r=!1,o=!1){return function(i,s){let a=function gU(e,n){switch(e){case Rt.FullYear:return n.getFullYear();case Rt.Month:return n.getMonth();case Rt.Date:return n.getDate();case Rt.Hours:return n.getHours();case Rt.Minutes:return n.getMinutes();case Rt.Seconds:return n.getSeconds();case Rt.FractionalSeconds:return n.getMilliseconds();case Rt.Day:return n.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}(e,i);if((t>0||a>-t)&&(a+=t),e===Rt.Hours)0===a&&-12===t&&(a=12);else if(e===Rt.FractionalSeconds)return function pU(e,n){return Co(e,3).substring(0,n)}(a,n);const l=no(s,An.MinusSign);return Co(a,n,l,r,o)}}function an(e,n,t=gr.Format,r=!1){return function(o,i){return function mU(e,n,t,r,o,i){switch(t){case Nt.Months:return function nU(e,n,t){const r=hr(e),i=ro([r[on.MonthsFormat],r[on.MonthsStandalone]],n);return ro(i,t)}(n,o,r)[e.getMonth()];case Nt.Days:return function tU(e,n,t){const r=hr(e),i=ro([r[on.DaysFormat],r[on.DaysStandalone]],n);return ro(i,t)}(n,o,r)[e.getDay()];case Nt.DayPeriods:const s=e.getHours(),a=e.getMinutes();if(i){const c=function sU(e){const n=hr(e);return nT(n),(n[on.ExtraData][2]||[]).map(r=>"string"==typeof r?yv(r):[yv(r[0]),yv(r[1])])}(n),d=function aU(e,n,t){const r=hr(e);nT(r);const i=ro([r[on.ExtraData][0],r[on.ExtraData][1]],n)||[];return ro(i,t)||[]}(n,o,r),h=c.findIndex(v=>{if(Array.isArray(v)){const[y,C]=v,b=s>=y.hours&&a>=y.minutes,M=s<C.hours||s===C.hours&&a<C.minutes;if(y.hours<C.hours){if(b&&M)return!0}else if(b||M)return!0}else if(v.hours===s&&v.minutes===a)return!0;return!1});if(-1!==h)return d[h]}return function eU(e,n,t){const r=hr(e),i=ro([r[on.DayPeriodsFormat],r[on.DayPeriodsStandalone]],n);return ro(i,t)}(n,o,r)[s<12?0:1];case Nt.Eras:return function rU(e,n){return ro(hr(e)[on.Eras],n)}(n,r)[e.getFullYear()<=0?0:1];default:throw new Error(`unexpected translation type ${t}`)}}(o,i,e,n,t,r)}}function Gf(e){return function(n,t,r){const o=-1*r,i=no(t,An.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case ai.Short:return(o>=0?"+":"")+Co(s,2,i)+Co(Math.abs(o%60),2,i);case ai.ShortGMT:return"GMT"+(o>=0?"+":"")+Co(s,1,i);case ai.Long:return"GMT"+(o>=0?"+":"")+Co(s,2,i)+":"+Co(Math.abs(o%60),2,i);case ai.Extended:return 0===r?"Z":(o>=0?"+":"")+Co(s,2,i)+":"+Co(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${e}"`)}}}const vU=0,Wf=4;function rT(e){const n=e.getDay(),t=0===n?-3:Wf-n;return Hf(e.getFullYear(),e.getMonth(),e.getDate()+t)}function Cv(e,n=!1){return function(t,r){let o;if(n){const i=new Date(t.getFullYear(),t.getMonth(),1).getDay()-1,s=t.getDate();o=1+Math.floor((s+i)/7)}else{const i=rT(t),s=function yU(e){const n=Hf(e,vU,1).getDay();return Hf(e,0,1+(n<=Wf?Wf:Wf+7)-n)}(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return Co(o,e,no(r,An.MinusSign))}}function qf(e,n=!1){return function(t,r){return Co(rT(t).getFullYear(),e,no(r,An.MinusSign),n)}}const _v={};function oT(e,n){e=e.replace(/:/g,"");const t=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(t)?n:t}function iT(e){return e instanceof Date&&!isNaN(e.valueOf())}function cT(e,n){n=encodeURIComponent(n);for(const t of e.split(";")){const r=t.indexOf("="),[o,i]=-1==r?[t,""]:[t.slice(0,r),t.slice(r+1)];if(o.trim()===n)return decodeURIComponent(i)}return null}class LU{constructor(n,t,r,o){this.$implicit=n,this.ngForOf=t,this.index=r,this.count=o}get first(){return 0===this.index}get last(){return this.index===this.count-1}get even(){return this.index%2==0}get odd(){return!this.even}}let Mv=(()=>{class e{set ngForOf(t){this._ngForOf=t,this._ngForOfDirty=!0}set ngForTrackBy(t){this._trackByFn=t}get ngForTrackBy(){return this._trackByFn}constructor(t,r,o){this._viewContainer=t,this._template=r,this._differs=o,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(t){t&&(this._template=t)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;const t=this._ngForOf;!this._differ&&t&&(this._differ=this._differs.find(t).create(this.ngForTrackBy))}if(this._differ){const t=this._differ.diff(this._ngForOf);t&&this._applyChanges(t)}}_applyChanges(t){const r=this._viewContainer;t.forEachOperation((o,i,s)=>{if(null==o.previousIndex)r.createEmbeddedView(this._template,new LU(o.item,this._ngForOf,-1,-1),null===s?void 0:s);else if(null==s)r.remove(null===i?void 0:i);else if(null!==i){const a=r.get(i);r.move(a,s),fT(a,o)}});for(let o=0,i=r.length;o<i;o++){const a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}t.forEachIdentityChange(o=>{fT(r.get(o.currentIndex),o)})}static ngTemplateContextGuard(t,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(g(Vr),g(go),g(pv))}}static{this.\u0275dir=Fe({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0})}}return e})();function fT(e,n){e.context.$implicit=n.item}let au=(()=>{class e{constructor(t,r){this._viewContainer=t,this._context=new $U,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=r}set ngIf(t){this._context.$implicit=this._context.ngIf=t,this._updateView()}set ngIfThen(t){hT("ngIfThen",t),this._thenTemplateRef=t,this._thenViewRef=null,this._updateView()}set ngIfElse(t){hT("ngIfElse",t),this._elseTemplateRef=t,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(t,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(g(Vr),g(go))}}static{this.\u0275dir=Fe({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0})}}return e})();class $U{constructor(){this.$implicit=null,this.ngIf=null}}function hT(e,n){if(n&&!n.createEmbeddedView)throw new Error(`${e} must be a TemplateRef, but received '${At(n)}'.`)}const JU=new ce(""),eH=new ce("");let gT=(()=>{class e{constructor(t,r,o){this.locale=t,this.defaultTimezone=r,this.defaultOptions=o}transform(t,r,o,i){if(null==t||""===t||t!=t)return null;try{return hU(t,r??this.defaultOptions?.dateFormat??"mediumDate",i||this.locale,o??this.defaultOptions?.timezone??this.defaultTimezone??void 0)}catch(s){throw function _o(e,n){return new z(2100,!1)}()}}static{this.\u0275fac=function(r){return new(r||e)(g(Ni,16),g(JU,24),g(eH,24))}}static{this.\u0275pipe=lr({name:"date",type:e,pure:!0,standalone:!0})}}return e})(),Nv=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=is({type:e})}static{this.\u0275inj=Zr({})}}return e})();function yT(e){return"server"===e}class CT{}class VH extends G2{constructor(){super(...arguments),this.supportsDOMEvents=!0}}class Lv extends VH{static makeCurrent(){!function z2(e){ZM??=e}(new Lv)}onAndCancel(n,t,r){return n.addEventListener(t,r),()=>{n.removeEventListener(t,r)}}dispatchEvent(n,t){n.dispatchEvent(t)}remove(n){n.remove()}createElement(n,t){return(t=t||this.getDefaultDocument()).createElement(n)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(n){return n.nodeType===Node.ELEMENT_NODE}isShadowRoot(n){return n instanceof DocumentFragment}getGlobalEventTarget(n,t){return"window"===t?window:"document"===t?n:"body"===t?n.body:null}getBaseHref(n){const t=function BH(){return uu=uu||document.querySelector("base"),uu?uu.getAttribute("href"):null}();return null==t?null:function jH(e){return new URL(e,document.baseURI).pathname}(t)}resetBaseElement(){uu=null}getUserAgent(){return window.navigator.userAgent}getCookie(n){return cT(document.cookie,n)}}let uu=null,HH=(()=>{class e{build(){return new XMLHttpRequest}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac})}}return e})();const $v=new ce("");let xT=(()=>{class e{constructor(t,r){this._zone=r,this._eventNameToPlugin=new Map,t.forEach(o=>{o.manager=this}),this._plugins=t.slice().reverse()}addEventListener(t,r,o){return this._findPluginFor(r).addEventListener(t,r,o)}getZone(){return this._zone}_findPluginFor(t){let r=this._eventNameToPlugin.get(t);if(r)return r;if(r=this._plugins.find(i=>i.supports(t)),!r)throw new z(5101,!1);return this._eventNameToPlugin.set(t,r),r}static{this.\u0275fac=function(r){return new(r||e)(Pe($v),Pe(j))}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac})}}return e})();class OT{constructor(n){this._doc=n}}const Vv="ng-app-id";let RT=(()=>{class e{constructor(t,r,o,i={}){this.doc=t,this.appId=r,this.nonce=o,this.platformId=i,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=yT(i),this.resetHostNodes()}addStyles(t){for(const r of t)1===this.changeUsageCount(r,1)&&this.onStyleAdded(r)}removeStyles(t){for(const r of t)this.changeUsageCount(r,-1)<=0&&this.onStyleRemoved(r)}ngOnDestroy(){const t=this.styleNodesInDOM;t&&(t.forEach(r=>r.remove()),t.clear());for(const r of this.getAllStyles())this.onStyleRemoved(r);this.resetHostNodes()}addHost(t){this.hostNodes.add(t);for(const r of this.getAllStyles())this.addStyleToHost(t,r)}removeHost(t){this.hostNodes.delete(t)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(t){for(const r of this.hostNodes)this.addStyleToHost(r,t)}onStyleRemoved(t){const r=this.styleRef;r.get(t)?.elements?.forEach(o=>o.remove()),r.delete(t)}collectServerRenderedStyles(){const t=this.doc.head?.querySelectorAll(`style[${Vv}="${this.appId}"]`);if(t?.length){const r=new Map;return t.forEach(o=>{null!=o.textContent&&r.set(o.textContent,o)}),r}return null}changeUsageCount(t,r){const o=this.styleRef;if(o.has(t)){const i=o.get(t);return i.usage+=r,i.usage}return o.set(t,{usage:r,elements:[]}),r}getStyleElement(t,r){const o=this.styleNodesInDOM,i=o?.get(r);if(i?.parentNode===t)return o.delete(r),i.removeAttribute(Vv),i;{const s=this.doc.createElement("style");return this.nonce&&s.setAttribute("nonce",this.nonce),s.textContent=r,this.platformIsServer&&s.setAttribute(Vv,this.appId),t.appendChild(s),s}}addStyleToHost(t,r){const o=this.getStyleElement(t,r),i=this.styleRef,s=i.get(r)?.elements;s?s.push(o):i.set(r,{elements:[o],usage:1})}resetHostNodes(){const t=this.hostNodes;t.clear(),t.add(this.doc.head)}static{this.\u0275fac=function(r){return new(r||e)(Pe(Hr),Pe(ac),Pe(Qb,8),Pe(wi))}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac})}}return e})();const Bv={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},jv=/%COMP%/g,qH=new ce("",{providedIn:"root",factory:()=>!0});function PT(e,n){return n.map(t=>t.replace(jv,e))}let kT=(()=>{class e{constructor(t,r,o,i,s,a,l,c=null){this.eventManager=t,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=l,this.nonce=c,this.rendererByCompId=new Map,this.platformIsServer=yT(a),this.defaultRenderer=new Uv(t,s,l,this.platformIsServer)}createRenderer(t,r){if(!t||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===Se.ShadowDom&&(r={...r,encapsulation:Se.Emulated});const o=this.getOrCreateRenderer(t,r);return o instanceof LT?o.applyToHost(t):o instanceof Hv&&o.applyStyles(),o}getOrCreateRenderer(t,r){const o=this.rendererByCompId;let i=o.get(r.id);if(!i){const s=this.doc,a=this.ngZone,l=this.eventManager,c=this.sharedStylesHost,d=this.removeStylesOnCompDestroy,h=this.platformIsServer;switch(r.encapsulation){case Se.Emulated:i=new LT(l,c,r,this.appId,d,s,a,h);break;case Se.ShadowDom:return new KH(l,c,t,r,s,a,this.nonce,h);default:i=new Hv(l,c,r,d,s,a,h)}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}static{this.\u0275fac=function(r){return new(r||e)(Pe(xT),Pe(RT),Pe(ac),Pe(qH),Pe(Hr),Pe(wi),Pe(j),Pe(Qb))}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac})}}return e})();class Uv{constructor(n,t,r,o){this.eventManager=n,this.doc=t,this.ngZone=r,this.platformIsServer=o,this.data=Object.create(null),this.throwOnSyntheticProps=!0,this.destroyNode=null}destroy(){}createElement(n,t){return t?this.doc.createElementNS(Bv[t]||t,n):this.doc.createElement(n)}createComment(n){return this.doc.createComment(n)}createText(n){return this.doc.createTextNode(n)}appendChild(n,t){(FT(n)?n.content:n).appendChild(t)}insertBefore(n,t,r){n&&(FT(n)?n.content:n).insertBefore(t,r)}removeChild(n,t){t.remove()}selectRootElement(n,t){let r="string"==typeof n?this.doc.querySelector(n):n;if(!r)throw new z(-5104,!1);return t||(r.textContent=""),r}parentNode(n){return n.parentNode}nextSibling(n){return n.nextSibling}setAttribute(n,t,r,o){if(o){t=o+":"+t;const i=Bv[o];i?n.setAttributeNS(i,t,r):n.setAttribute(t,r)}else n.setAttribute(t,r)}removeAttribute(n,t,r){if(r){const o=Bv[r];o?n.removeAttributeNS(o,t):n.removeAttribute(`${r}:${t}`)}else n.removeAttribute(t)}addClass(n,t){n.classList.add(t)}removeClass(n,t){n.classList.remove(t)}setStyle(n,t,r,o){o&(Si.DashCase|Si.Important)?n.style.setProperty(t,r,o&Si.Important?"important":""):n.style[t]=r}removeStyle(n,t,r){r&Si.DashCase?n.style.removeProperty(t):n.style[t]=""}setProperty(n,t,r){null!=n&&(n[t]=r)}setValue(n,t){n.nodeValue=t}listen(n,t,r){if("string"==typeof n&&!(n=ki().getGlobalEventTarget(this.doc,n)))throw new Error(`Unsupported event target ${n} for event ${t}`);return this.eventManager.addEventListener(n,t,this.decoratePreventDefault(r))}decoratePreventDefault(n){return t=>{if("__ngUnwrap__"===t)return n;!1===(this.platformIsServer?this.ngZone.runGuarded(()=>n(t)):n(t))&&t.preventDefault()}}}function FT(e){return"TEMPLATE"===e.tagName&&void 0!==e.content}class KH extends Uv{constructor(n,t,r,o,i,s,a,l){super(n,i,s,l),this.sharedStylesHost=t,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);const c=PT(o.id,o.styles);for(const d of c){const h=document.createElement("style");a&&h.setAttribute("nonce",a),h.textContent=d,this.shadowRoot.appendChild(h)}}nodeOrShadowRoot(n){return n===this.hostEl?this.shadowRoot:n}appendChild(n,t){return super.appendChild(this.nodeOrShadowRoot(n),t)}insertBefore(n,t,r){return super.insertBefore(this.nodeOrShadowRoot(n),t,r)}removeChild(n,t){return super.removeChild(null,t)}parentNode(n){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(n)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}}class Hv extends Uv{constructor(n,t,r,o,i,s,a,l){super(n,i,s,a),this.sharedStylesHost=t,this.removeStylesOnCompDestroy=o,this.styles=l?PT(l,r.styles):r.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}}class LT extends Hv{constructor(n,t,r,o,i,s,a,l){const c=o+"-"+r.id;super(n,t,r,i,s,a,l,c),this.contentAttr=function XH(e){return"_ngcontent-%COMP%".replace(jv,e)}(c),this.hostAttr=function YH(e){return"_nghost-%COMP%".replace(jv,e)}(c)}applyToHost(n){this.applyStyles(),this.setAttribute(n,this.hostAttr,"")}createElement(n,t){const r=super.createElement(n,t);return super.setAttribute(r,this.contentAttr,""),r}}const $T=["alt","control","meta","shift"],JH={"\b":"Backspace","\t":"Tab","\x7f":"Delete","\x1b":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},e3={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey};function VT(e){return{appProviders:[...l3,...e?.providers??[]],platformProviders:s3}}const s3=[{provide:wi,useValue:"browser"},{provide:Kb,useValue:function r3(){Lv.makeCurrent()},multi:!0},{provide:Hr,useFactory:function i3(){return function O1(e){rg=e}(document),document},deps:[]}],l3=[{provide:gp,useValue:"root"},{provide:Ro,useFactory:function o3(){return new Ro},deps:[]},{provide:$v,useClass:(()=>{class e extends OT{constructor(t){super(t)}supports(t){return!0}addEventListener(t,r,o){return t.addEventListener(r,o,!1),()=>this.removeEventListener(t,r,o)}removeEventListener(t,r,o){return t.removeEventListener(r,o)}static{this.\u0275fac=function(r){return new(r||e)(Pe(Hr))}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac})}}return e})(),multi:!0,deps:[Hr,j,wi]},{provide:$v,useClass:(()=>{class e extends OT{constructor(t){super(t)}supports(t){return null!=e.parseEventName(t)}addEventListener(t,r,o){const i=e.parseEventName(r),s=e.eventCallback(i.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>ki().onAndCancel(t,i.domEventName,s))}static parseEventName(t){const r=t.toLowerCase().split("."),o=r.shift();if(0===r.length||"keydown"!==o&&"keyup"!==o)return null;const i=e._normalizeKey(r.pop());let s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),$T.forEach(c=>{const d=r.indexOf(c);d>-1&&(r.splice(d,1),s+=c+".")}),s+=i,0!=r.length||0===i.length)return null;const l={};return l.domEventName=o,l.fullKey=s,l}static matchEventFullKeyCode(t,r){let o=JH[t.key]||t.key,i="";return r.indexOf("code.")>-1&&(o=t.code,i="code."),!(null==o||!o)&&(o=o.toLowerCase()," "===o?o="space":"."===o&&(o="dot"),$T.forEach(s=>{s!==o&&(0,e3[s])(t)&&(i+=s+".")}),i+=o,i===r)}static eventCallback(t,r,o){return i=>{e.matchEventFullKeyCode(i,t)&&o.runGuarded(()=>r(i))}}static _normalizeKey(t){return"esc"===t?"escape":t}static{this.\u0275fac=function(r){return new(r||e)(Pe(Hr))}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac})}}return e})(),multi:!0,deps:[Hr]},kT,RT,xT,{provide:om,useExisting:kT},{provide:CT,useClass:HH,deps:[]},[]];let c3=(()=>{class e{constructor(t){this._doc=t}getTitle(){return this._doc.title}setTitle(t){this._doc.title=t||""}static{this.\u0275fac=function(r){return new(r||e)(Pe(Hr))}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function Li(e){return!!e&&(e instanceof Yt||nt(e.lift)&&nt(e.subscribe))}function ve(e,n,t,r){var s,o=arguments.length,i=o<3?n:null===r?r=Object.getOwnPropertyDescriptor(n,t):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)i=Reflect.decorate(e,n,t,r);else for(var a=e.length-1;a>=0;a--)(s=e[a])&&(i=(o<3?s(i):o>3?s(n,t,i):s(n,t))||i);return o>3&&i&&Object.defineProperty(n,t,i),i}function $i(e){return this instanceof $i?(this.v=e,this):new $i(e)}function GT(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=function qv(e){var n="function"==typeof Symbol&&Symbol.iterator,t=n&&e[n],r=0;if(t)return t.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}(e),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(i){t[i]=e[i]&&function(s){return new Promise(function(a,l){!function o(i,s,a,l){Promise.resolve(l).then(function(c){i({value:c,done:a})},s)}(a,l,(s=e[i](s)).done,s.value)})}}}"function"==typeof SuppressedError&&SuppressedError;const Yv=e=>e&&"number"==typeof e.length&&"function"!=typeof e;function WT(e){return nt(e?.then)}function qT(e){return nt(e[Yo])}function XT(e){return Symbol.asyncIterator&&nt(e?.[Symbol.asyncIterator])}function YT(e){return new TypeError(`You provided ${null!==e&&"object"==typeof e?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}const ZT=function $3(){return"function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"}();function KT(e){return nt(e?.[ZT])}function QT(e){return function zT(e,n,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var o,r=t.apply(e,n||[]),i=[];return o=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",function s(y){return function(C){return Promise.resolve(C).then(y,h)}}),o[Symbol.asyncIterator]=function(){return this},o;function a(y,C){r[y]&&(o[y]=function(b){return new Promise(function(M,O){i.push([y,b,M,O])>1||l(y,b)})},C&&(o[y]=C(o[y])))}function l(y,C){try{!function c(y){y.value instanceof $i?Promise.resolve(y.value.v).then(d,h):v(i[0][2],y)}(r[y](C))}catch(b){v(i[0][3],b)}}function d(y){l("next",y)}function h(y){l("throw",y)}function v(y,C){y(C),i.shift(),i.length&&l(i[0][0],i[0][1])}}(this,arguments,function*(){const t=e.getReader();try{for(;;){const{value:r,done:o}=yield $i(t.read());if(o)return yield $i(void 0);yield yield $i(r)}}finally{t.releaseLock()}})}function JT(e){return nt(e?.getReader)}function Gr(e){if(e instanceof Yt)return e;if(null!=e){if(qT(e))return function V3(e){return new Yt(n=>{const t=e[Yo]();if(nt(t.subscribe))return t.subscribe(n);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}(e);if(Yv(e))return function B3(e){return new Yt(n=>{for(let t=0;t<e.length&&!n.closed;t++)n.next(e[t]);n.complete()})}(e);if(WT(e))return function j3(e){return new Yt(n=>{e.then(t=>{n.closed||(n.next(t),n.complete())},t=>n.error(t)).then(null,qt)})}(e);if(XT(e))return eA(e);if(KT(e))return function U3(e){return new Yt(n=>{for(const t of e)if(n.next(t),n.closed)return;n.complete()})}(e);if(JT(e))return function H3(e){return eA(QT(e))}(e)}throw YT(e)}function eA(e){return new Yt(n=>{(function z3(e,n){var t,r,o,i;return function UT(e,n,t,r){return new(t||(t=Promise))(function(i,s){function a(d){try{c(r.next(d))}catch(h){s(h)}}function l(d){try{c(r.throw(d))}catch(h){s(h)}}function c(d){d.done?i(d.value):function o(i){return i instanceof t?i:new t(function(s){s(i)})}(d.value).then(a,l)}c((r=r.apply(e,n||[])).next())})}(this,void 0,void 0,function*(){try{for(t=GT(e);!(r=yield t.next()).done;)if(n.next(r.value),n.closed)return}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=t.return)&&(yield i.call(t))}finally{if(o)throw o.error}}n.complete()})})(e,n).catch(t=>n.error(t))})}function ci(e,n,t,r=0,o=!1){const i=n.schedule(function(){t(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function tA(e,n=0){return hn((t,r)=>{t.subscribe(en(r,o=>ci(r,e,()=>r.next(o),n),()=>ci(r,e,()=>r.complete(),n),o=>ci(r,e,()=>r.error(o),n)))})}function nA(e,n=0){return hn((t,r)=>{r.add(e.schedule(()=>t.subscribe(r),n))})}function rA(e,n){if(!e)throw new Error("Iterable cannot be null");return new Yt(t=>{ci(t,n,()=>{const r=e[Symbol.asyncIterator]();ci(t,n,()=>{r.next().then(o=>{o.done?t.complete():t.next(o.value)})},0,!0)})})}function zn(e,n){return n?function Z3(e,n){if(null!=e){if(qT(e))return function G3(e,n){return Gr(e).pipe(nA(n),tA(n))}(e,n);if(Yv(e))return function q3(e,n){return new Yt(t=>{let r=0;return n.schedule(function(){r===e.length?t.complete():(t.next(e[r++]),t.closed||this.schedule())})})}(e,n);if(WT(e))return function W3(e,n){return Gr(e).pipe(nA(n),tA(n))}(e,n);if(XT(e))return rA(e,n);if(KT(e))return function X3(e,n){return new Yt(t=>{let r;return ci(t,n,()=>{r=e[ZT](),ci(t,n,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){return void t.error(s)}i?t.complete():t.next(o)},0,!0)}),()=>nt(r?.return)&&r.return()})}(e,n);if(JT(e))return function Y3(e,n){return rA(QT(e),n)}(e,n)}throw YT(e)}(e,n):Gr(e)}function Zv(e){return e[e.length-1]}function oA(e){return nt(Zv(e))?e.pop():void 0}function Jf(e){return function K3(e){return e&&nt(e.schedule)}(Zv(e))?e.pop():void 0}function je(...e){return zn(e,Jf(e))}const du=Wt(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"}),{isArray:Q3}=Array,{getPrototypeOf:J3,prototype:ez,keys:tz}=Object;function iA(e){if(1===e.length){const n=e[0];if(Q3(n))return{args:n,keys:null};if(function nz(e){return e&&"object"==typeof e&&J3(e)===ez}(n)){const t=tz(n);return{args:t.map(r=>n[r]),keys:t}}}return{args:e,keys:null}}const{isArray:rz}=Array;function Kv(e){return ct(n=>function oz(e,n){return rz(n)?e(...n):e(n)}(e,n))}function sA(e,n){return e.reduce((t,r,o)=>(t[r]=n[o],t),{})}function Qv(...e){const n=Jf(e),t=oA(e),{args:r,keys:o}=iA(e);if(0===r.length)return zn([],n);const i=new Yt(function iz(e,n,t=Yr){return r=>{aA(n,()=>{const{length:o}=e,i=new Array(o);let s=o,a=o;for(let l=0;l<o;l++)aA(n,()=>{const c=zn(e[l],n);let d=!1;c.subscribe(en(r,h=>{i[l]=h,d||(d=!0,a--),a||r.next(t(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}(r,n,o?s=>sA(o,s):Yr));return t?i.pipe(Kv(t)):i}function aA(e,n,t){e?ci(t,e,n):n()}function Jn(e,n,t=1/0){return nt(n)?Jn((r,o)=>ct((i,s)=>n(r,i,o,s))(Gr(e(r,o))),t):("number"==typeof n&&(t=n),hn((r,o)=>function sz(e,n,t,r,o,i,s,a){const l=[];let c=0,d=0,h=!1;const v=()=>{h&&!l.length&&!c&&n.complete()},y=b=>c<r?C(b):l.push(b),C=b=>{i&&n.next(b),c++;let M=!1;Gr(t(b,d++)).subscribe(en(n,O=>{o?.(O),i?y(O):n.next(O)},()=>{M=!0},void 0,()=>{if(M)try{for(c--;l.length&&c<r;){const O=l.shift();s?ci(n,s,()=>C(O)):C(O)}v()}catch(O){n.error(O)}}))};return e.subscribe(en(n,y,()=>{h=!0,v()})),()=>{a?.()}}(r,o,e,t)))}function fu(...e){return function az(){return function Jv(e=1/0){return Jn(Yr,e)}(1)}()(zn(e,Jf(e)))}function eh(e){return new Yt(n=>{Gr(e()).subscribe(n)})}function th(e,n){const t=nt(e)?e:()=>e,r=o=>o.error(t());return new Yt(n?o=>n.schedule(r,0,o):r)}const Bo=new Yt(e=>e.complete());function ey(){return hn((e,n)=>{let t=null;e._refCount++;const r=en(n,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount)return void(t=null);const o=e._connection,i=t;t=null,o&&(!i||o===i)&&o.unsubscribe(),n.unsubscribe()});e.subscribe(r),r.closed||(t=e.connect())})}class lA extends Yt{constructor(n,t){super(),this.source=n,this.subjectFactory=t,this._subject=null,this._refCount=0,this._connection=null,Js(n)&&(this.lift=n.lift)}_subscribe(n){return this.getSubject().subscribe(n)}getSubject(){const n=this._subject;return(!n||n.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;const{_connection:n}=this;this._subject=this._connection=null,n?.unsubscribe()}connect(){let n=this._connection;if(!n){n=this._connection=new Ze;const t=this.getSubject();n.add(this.source.subscribe(en(t,void 0,()=>{this._teardown(),t.complete()},r=>{this._teardown(),t.error(r)},()=>this._teardown()))),n.closed&&(this._connection=null,n=Ze.EMPTY)}return n}refCount(){return ey()(this)}}function mr(e,n){return hn((t,r)=>{let o=null,i=0,s=!1;const a=()=>s&&!o&&r.complete();t.subscribe(en(r,l=>{o?.unsubscribe();let c=0;const d=i++;Gr(e(l,d)).subscribe(o=en(r,h=>r.next(n?n(l,h,d,c++):h),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function jo(e){return e<=0?()=>Bo:hn((n,t)=>{let r=0;n.subscribe(en(t,o=>{++r<=e&&(t.next(o),e<=r&&t.complete())}))})}function Uo(e,n){return hn((t,r)=>{let o=0;t.subscribe(en(r,i=>e.call(n,i,o++)&&r.next(i)))})}function nh(e){return hn((n,t)=>{let r=!1;n.subscribe(en(t,o=>{r=!0,t.next(o)},()=>{r||t.next(e),t.complete()}))})}function cA(e=uz){return hn((n,t)=>{let r=!1;n.subscribe(en(t,o=>{r=!0,t.next(o)},()=>r?t.complete():t.error(e())))})}function uz(){return new du}function Ns(e,n){const t=arguments.length>=2;return r=>r.pipe(e?Uo((o,i)=>e(o,i,r)):Yr,jo(1),t?nh(n):cA(()=>new du))}function ml(e,n){return nt(n)?Jn(e,n,1):Jn(e,1)}function vr(e,n,t){const r=nt(e)||n||t?{next:e,error:n,complete:t}:e;return r?hn((o,i)=>{var s;null===(s=r.subscribe)||void 0===s||s.call(r);let a=!0;o.subscribe(en(i,l=>{var c;null===(c=r.next)||void 0===c||c.call(r,l),i.next(l)},()=>{var l;a=!1,null===(l=r.complete)||void 0===l||l.call(r),i.complete()},l=>{var c;a=!1,null===(c=r.error)||void 0===c||c.call(r,l),i.error(l)},()=>{var l,c;a&&(null===(l=r.unsubscribe)||void 0===l||l.call(r)),null===(c=r.finalize)||void 0===c||c.call(r)}))}):Yr}function ui(e){return hn((n,t)=>{let i,r=null,o=!1;r=n.subscribe(en(t,void 0,void 0,s=>{i=Gr(e(s,ui(e)(n))),r?(r.unsubscribe(),r=null,i.subscribe(t)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(t))})}function ty(e){return e<=0?()=>Bo:hn((n,t)=>{let r=[];n.subscribe(en(t,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(const o of r)t.next(o);t.complete()},void 0,()=>{r=null}))})}function rh(e){return hn((n,t)=>{try{n.subscribe(t)}finally{t.add(e)}})}function uA(e){return hn((n,t)=>{Gr(e).subscribe(en(t,()=>t.complete(),Ot)),!t.closed&&n.subscribe(t)})}const ft="primary",hu=Symbol("RouteTitle");class gz{constructor(n){this.params=n||{}}has(n){return Object.prototype.hasOwnProperty.call(this.params,n)}get(n){if(this.has(n)){const t=this.params[n];return Array.isArray(t)?t[0]:t}return null}getAll(n){if(this.has(n)){const t=this.params[n];return Array.isArray(t)?t:[t]}return[]}get keys(){return Object.keys(this.params)}}function vl(e){return new gz(e)}function mz(e,n,t){const r=t.path.split("/");if(r.length>e.length||"full"===t.pathMatch&&(n.hasChildren()||r.length<e.length))return null;const o={};for(let i=0;i<r.length;i++){const s=r[i],a=e[i];if(":"===s[0])o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function Ho(e,n){const t=e?ny(e):void 0,r=n?ny(n):void 0;if(!t||!r||t.length!=r.length)return!1;let o;for(let i=0;i<t.length;i++)if(o=t[i],!dA(e[o],n[o]))return!1;return!0}function ny(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function dA(e,n){if(Array.isArray(e)&&Array.isArray(n)){if(e.length!==n.length)return!1;const t=[...e].sort(),r=[...n].sort();return t.every((o,i)=>r[i]===o)}return e===n}function fA(e){return e.length>0?e[e.length-1]:null}function Vi(e){return Li(e)?e:eu(e)?zn(Promise.resolve(e)):je(e)}const yz={exact:function gA(e,n,t){if(!ks(e.segments,n.segments)||!oh(e.segments,n.segments,t)||e.numberOfChildren!==n.numberOfChildren)return!1;for(const r in n.children)if(!e.children[r]||!gA(e.children[r],n.children[r],t))return!1;return!0},subset:mA},hA={exact:function Cz(e,n){return Ho(e,n)},subset:function _z(e,n){return Object.keys(n).length<=Object.keys(e).length&&Object.keys(n).every(t=>dA(e[t],n[t]))},ignored:()=>!0};function pA(e,n,t){return yz[t.paths](e.root,n.root,t.matrixParams)&&hA[t.queryParams](e.queryParams,n.queryParams)&&!("exact"===t.fragment&&e.fragment!==n.fragment)}function mA(e,n,t){return vA(e,n,n.segments,t)}function vA(e,n,t,r){if(e.segments.length>t.length){const o=e.segments.slice(0,t.length);return!(!ks(o,t)||n.hasChildren()||!oh(o,t,r))}if(e.segments.length===t.length){if(!ks(e.segments,t)||!oh(e.segments,t,r))return!1;for(const o in n.children)if(!e.children[o]||!mA(e.children[o],n.children[o],r))return!1;return!0}{const o=t.slice(0,e.segments.length),i=t.slice(e.segments.length);return!!(ks(e.segments,o)&&oh(e.segments,o,r)&&e.children[ft])&&vA(e.children[ft],n,i,r)}}function oh(e,n,t){return n.every((r,o)=>hA[t](e[o].parameters,r.parameters))}class Ps{constructor(n=new zt([],{}),t={},r=null){this.root=n,this.queryParams=t,this.fragment=r}get queryParamMap(){return this._queryParamMap??=vl(this.queryParams),this._queryParamMap}toString(){return Ez.serialize(this)}}class zt{constructor(n,t){this.segments=n,this.children=t,this.parent=null,Object.values(t).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return sh(this)}}class pu{constructor(n,t){this.path=n,this.parameters=t}get parameterMap(){return this._parameterMap??=vl(this.parameters),this._parameterMap}toString(){return _A(this)}}function ks(e,n){return e.length===n.length&&e.every((t,r)=>t.path===n[r].path)}let Fs=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:()=>new ih,providedIn:"root"})}}return e})();class ih{parse(n){const t=new Pz(n);return new Ps(t.parseRootSegment(),t.parseQueryParams(),t.parseFragment())}serialize(n){const t=`/${gu(n.root,!0)}`,r=function Sz(e){const n=Object.entries(e).map(([t,r])=>Array.isArray(r)?r.map(o=>`${ah(t)}=${ah(o)}`).join("&"):`${ah(t)}=${ah(r)}`).filter(t=>t);return n.length?`?${n.join("&")}`:""}(n.queryParams);return`${t}${r}${"string"==typeof n.fragment?`#${function wz(e){return encodeURI(e)}(n.fragment)}`:""}`}}const Ez=new ih;function sh(e){return e.segments.map(n=>_A(n)).join("/")}function gu(e,n){if(!e.hasChildren())return sh(e);if(n){const t=e.children[ft]?gu(e.children[ft],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==ft&&r.push(`${o}:${gu(i,!1)}`)}),r.length>0?`${t}(${r.join("//")})`:t}{const t=function bz(e,n){let t=[];return Object.entries(e.children).forEach(([r,o])=>{r===ft&&(t=t.concat(n(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==ft&&(t=t.concat(n(o,r)))}),t}(e,(r,o)=>o===ft?[gu(e.children[ft],!1)]:[`${o}:${gu(r,!1)}`]);return 1===Object.keys(e.children).length&&null!=e.children[ft]?`${sh(e)}/${t[0]}`:`${sh(e)}/(${t.join("//")})`}}function yA(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function ah(e){return yA(e).replace(/%3B/gi,";")}function ry(e){return yA(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function lh(e){return decodeURIComponent(e)}function CA(e){return lh(e.replace(/\+/g,"%20"))}function _A(e){return`${ry(e.path)}${function Iz(e){return Object.entries(e).map(([n,t])=>`;${ry(n)}=${ry(t)}`).join("")}(e.parameters)}`}const Mz=/^[^\/()?;#]+/;function oy(e){const n=e.match(Mz);return n?n[0]:""}const Tz=/^[^\/()?;=#]+/,xz=/^[^=?&#]+/,Rz=/^[^&#]+/;class Pz{constructor(n){this.url=n,this.remaining=n}parseRootSegment(){return this.consumeOptional("/"),""===this.remaining||this.peekStartsWith("?")||this.peekStartsWith("#")?new zt([],{}):new zt([],this.parseChildren())}parseQueryParams(){const n={};if(this.consumeOptional("?"))do{this.parseQueryParam(n)}while(this.consumeOptional("&"));return n}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(""===this.remaining)return{};this.consumeOptional("/");const n=[];for(this.peekStartsWith("(")||n.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),n.push(this.parseSegment());let t={};this.peekStartsWith("/(")&&(this.capture("/"),t=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(n.length>0||Object.keys(t).length>0)&&(r[ft]=new zt(n,t)),r}parseSegment(){const n=oy(this.remaining);if(""===n&&this.peekStartsWith(";"))throw new z(4009,!1);return this.capture(n),new pu(lh(n),this.parseMatrixParams())}parseMatrixParams(){const n={};for(;this.consumeOptional(";");)this.parseParam(n);return n}parseParam(n){const t=function Az(e){const n=e.match(Tz);return n?n[0]:""}(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){const o=oy(this.remaining);o&&(r=o,this.capture(r))}n[lh(t)]=lh(r)}parseQueryParam(n){const t=function Oz(e){const n=e.match(xz);return n?n[0]:""}(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){const s=function Nz(e){const n=e.match(Rz);return n?n[0]:""}(this.remaining);s&&(r=s,this.capture(r))}const o=CA(t),i=CA(r);if(n.hasOwnProperty(o)){let s=n[o];Array.isArray(s)||(s=[s],n[o]=s),s.push(i)}else n[o]=i}parseParens(n){const t={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){const r=oy(this.remaining),o=this.remaining[r.length];if("/"!==o&&")"!==o&&";"!==o)throw new z(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):n&&(i=ft);const s=this.parseChildren();t[i]=1===Object.keys(s).length?s[ft]:new zt([],s),this.consumeOptional("//")}return t}peekStartsWith(n){return this.remaining.startsWith(n)}consumeOptional(n){return!!this.peekStartsWith(n)&&(this.remaining=this.remaining.substring(n.length),!0)}capture(n){if(!this.consumeOptional(n))throw new z(4011,!1)}}function DA(e){return e.segments.length>0?new zt([],{[ft]:e}):e}function bA(e){const n={};for(const[r,o]of Object.entries(e.children)){const i=bA(o);if(r===ft&&0===i.segments.length&&i.hasChildren())for(const[s,a]of Object.entries(i.children))n[s]=a;else(i.segments.length>0||i.hasChildren())&&(n[r]=i)}return function kz(e){if(1===e.numberOfChildren&&e.children[ft]){const n=e.children[ft];return new zt(e.segments.concat(n.segments),n.children)}return e}(new zt(e.segments,n))}function Ls(e){return e instanceof Ps}function EA(e){let n;const o=DA(function t(i){const s={};for(const l of i.children){const c=t(l);s[l.outlet]=c}const a=new zt(i.url,s);return i===e&&(n=a),a}(e.root));return n??o}function wA(e,n,t,r){let o=e;for(;o.parent;)o=o.parent;if(0===n.length)return iy(o,o,o,t,r);const i=function Lz(e){if("string"==typeof e[0]&&1===e.length&&"/"===e[0])return new SA(!0,0,e);let n=0,t=!1;const r=e.reduce((o,i,s)=>{if("object"==typeof i&&null!=i){if(i.outlets){const a={};return Object.entries(i.outlets).forEach(([l,c])=>{a[l]="string"==typeof c?c.split("/"):c}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return"string"!=typeof i?[...o,i]:0===s?(i.split("/").forEach((a,l)=>{0==l&&"."===a||(0==l&&""===a?t=!0:".."===a?n++:""!=a&&o.push(a))}),o):[...o,i]},[]);return new SA(t,n,r)}(n);if(i.toRoot())return iy(o,o,new zt([],{}),t,r);const s=function $z(e,n,t){if(e.isAbsolute)return new uh(n,!0,0);if(!t)return new uh(n,!1,NaN);if(null===t.parent)return new uh(t,!0,0);const r=ch(e.commands[0])?0:1;return function Vz(e,n,t){let r=e,o=n,i=t;for(;i>o;){if(i-=o,r=r.parent,!r)throw new z(4005,!1);o=r.segments.length}return new uh(r,!1,o-i)}(t,t.segments.length-1+r,e.numberOfDoubleDots)}(i,o,e),a=s.processChildren?vu(s.segmentGroup,s.index,i.commands):MA(s.segmentGroup,s.index,i.commands);return iy(o,s.segmentGroup,a,t,r)}function ch(e){return"object"==typeof e&&null!=e&&!e.outlets&&!e.segmentPath}function mu(e){return"object"==typeof e&&null!=e&&e.outlets}function iy(e,n,t,r,o){let s,i={};r&&Object.entries(r).forEach(([l,c])=>{i[l]=Array.isArray(c)?c.map(d=>`${d}`):`${c}`}),s=e===n?t:IA(e,n,t);const a=DA(bA(s));return new Ps(a,i,o)}function IA(e,n,t){const r={};return Object.entries(e.children).forEach(([o,i])=>{r[o]=i===n?t:IA(i,n,t)}),new zt(e.segments,r)}class SA{constructor(n,t,r){if(this.isAbsolute=n,this.numberOfDoubleDots=t,this.commands=r,n&&r.length>0&&ch(r[0]))throw new z(4003,!1);const o=r.find(mu);if(o&&o!==fA(r))throw new z(4004,!1)}toRoot(){return this.isAbsolute&&1===this.commands.length&&"/"==this.commands[0]}}class uh{constructor(n,t,r){this.segmentGroup=n,this.processChildren=t,this.index=r}}function MA(e,n,t){if(e??=new zt([],{}),0===e.segments.length&&e.hasChildren())return vu(e,n,t);const r=function jz(e,n,t){let r=0,o=n;const i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=t.length)return i;const s=e.segments[o],a=t[r];if(mu(a))break;const l=`${a}`,c=r<t.length-1?t[r+1]:null;if(o>0&&void 0===l)break;if(l&&c&&"object"==typeof c&&void 0===c.outlets){if(!AA(l,c,s))return i;r+=2}else{if(!AA(l,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}(e,n,t),o=t.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){const i=new zt(e.segments.slice(0,r.pathIndex),{});return i.children[ft]=new zt(e.segments.slice(r.pathIndex),e.children),vu(i,0,o)}return r.match&&0===o.length?new zt(e.segments,{}):r.match&&!e.hasChildren()?sy(e,n,t):r.match?vu(e,0,o):sy(e,n,t)}function vu(e,n,t){if(0===t.length)return new zt(e.segments,{});{const r=function Bz(e){return mu(e[0])?e[0].outlets:{[ft]:e}}(t),o={};if(Object.keys(r).some(i=>i!==ft)&&e.children[ft]&&1===e.numberOfChildren&&0===e.children[ft].segments.length){const i=vu(e.children[ft],n,t);return new zt(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{"string"==typeof s&&(s=[s]),null!==s&&(o[i]=MA(e.children[i],n,s))}),Object.entries(e.children).forEach(([i,s])=>{void 0===r[i]&&(o[i]=s)}),new zt(e.segments,o)}}function sy(e,n,t){const r=e.segments.slice(0,n);let o=0;for(;o<t.length;){const i=t[o];if(mu(i)){const l=Uz(i.outlets);return new zt(r,l)}if(0===o&&ch(t[0])){r.push(new pu(e.segments[n].path,TA(t[0]))),o++;continue}const s=mu(i)?i.outlets[ft]:`${i}`,a=o<t.length-1?t[o+1]:null;s&&a&&ch(a)?(r.push(new pu(s,TA(a))),o+=2):(r.push(new pu(s,{})),o++)}return new zt(r,{})}function Uz(e){const n={};return Object.entries(e).forEach(([t,r])=>{"string"==typeof r&&(r=[r]),null!==r&&(n[t]=sy(new zt([],{}),0,r))}),n}function TA(e){const n={};return Object.entries(e).forEach(([t,r])=>n[t]=`${r}`),n}function AA(e,n,t){return e==t.path&&Ho(n,t.parameters)}const yu="imperative";var bt=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(bt||{});class zo{constructor(n,t){this.id=n,this.url=t}}class dh extends zo{constructor(n,t,r="imperative",o=null){super(n,t),this.type=bt.NavigationStart,this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}}class $s extends zo{constructor(n,t,r){super(n,t),this.urlAfterRedirects=r,this.type=bt.NavigationEnd}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}}var Wr=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(Wr||{}),ay=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(ay||{});class Vs extends zo{constructor(n,t,r,o){super(n,t),this.reason=r,this.code=o,this.type=bt.NavigationCancel}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}}class Cu extends zo{constructor(n,t,r,o){super(n,t),this.reason=r,this.code=o,this.type=bt.NavigationSkipped}}class ly extends zo{constructor(n,t,r,o){super(n,t),this.error=r,this.target=o,this.type=bt.NavigationError}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}}class xA extends zo{constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o,this.type=bt.RoutesRecognized}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class Hz extends zo{constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o,this.type=bt.GuardsCheckStart}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class zz extends zo{constructor(n,t,r,o,i){super(n,t),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i,this.type=bt.GuardsCheckEnd}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}}class Gz extends zo{constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o,this.type=bt.ResolveStart}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class Wz extends zo{constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o,this.type=bt.ResolveEnd}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class qz{constructor(n){this.route=n,this.type=bt.RouteConfigLoadStart}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}}class Xz{constructor(n){this.route=n,this.type=bt.RouteConfigLoadEnd}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}}class Yz{constructor(n){this.snapshot=n,this.type=bt.ChildActivationStart}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class Zz{constructor(n){this.snapshot=n,this.type=bt.ChildActivationEnd}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class Kz{constructor(n){this.snapshot=n,this.type=bt.ActivationStart}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class Qz{constructor(n){this.snapshot=n,this.type=bt.ActivationEnd}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class cy{}class fh{constructor(n,t){this.url=n,this.navigationBehaviorOptions=t}}function Do(e){return e.outlet||ft}function _u(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let n=e.parent;n;n=n.parent){const t=n.routeConfig;if(t?._loadedInjector)return t._loadedInjector;if(t?._injector)return t._injector}return null}class o4{get injector(){return _u(this.route?.snapshot)??this.rootInjector}set injector(n){}constructor(n){this.rootInjector=n,this.outlet=null,this.route=null,this.children=new Bs(this.rootInjector),this.attachRef=null}}let Bs=(()=>{class e{constructor(t){this.rootInjector=t,this.contexts=new Map}onChildOutletCreated(t,r){const o=this.getOrCreateContext(t);o.outlet=r,this.contexts.set(t,o)}onChildOutletDestroyed(t){const r=this.getContext(t);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){const t=this.contexts;return this.contexts=new Map,t}onOutletReAttached(t){this.contexts=t}getOrCreateContext(t){let r=this.getContext(t);return r||(r=new o4(this.rootInjector),this.contexts.set(t,r)),r}getContext(t){return this.contexts.get(t)||null}static{this.\u0275fac=function(r){return new(r||e)(Pe(Pn))}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();class OA{constructor(n){this._root=n}get root(){return this._root.value}parent(n){const t=this.pathFromRoot(n);return t.length>1?t[t.length-2]:null}children(n){const t=uy(n,this._root);return t?t.children.map(r=>r.value):[]}firstChild(n){const t=uy(n,this._root);return t&&t.children.length>0?t.children[0].value:null}siblings(n){const t=dy(n,this._root);return t.length<2?[]:t[t.length-2].children.map(o=>o.value).filter(o=>o!==n)}pathFromRoot(n){return dy(n,this._root).map(t=>t.value)}}function uy(e,n){if(e===n.value)return n;for(const t of n.children){const r=uy(e,t);if(r)return r}return null}function dy(e,n){if(e===n.value)return[n];for(const t of n.children){const r=dy(e,t);if(r.length)return r.unshift(n),r}return[]}class bo{constructor(n,t){this.value=n,this.children=t}toString(){return`TreeNode(${this.value})`}}function yl(e){const n={};return e&&e.children.forEach(t=>n[t.value.outlet]=t),n}class RA extends OA{constructor(n,t){super(n),this.snapshot=t,fy(this,n)}toString(){return this.snapshot.toString()}}function NA(e){const n=function i4(e){const i=new ph([],{},{},"",{},ft,e,null,{});return new PA("",new bo(i,[]))}(e),t=new wn([new pu("",{})]),r=new wn({}),o=new wn({}),i=new wn({}),s=new wn(""),a=new Go(t,r,i,s,o,ft,e,n.root);return a.snapshot=n.root,new RA(new bo(a,[]),n)}class Go{constructor(n,t,r,o,i,s,a,l){this.urlSubject=n,this.paramsSubject=t,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=l,this.title=this.dataSubject?.pipe(ct(c=>c[hu]))??je(void 0),this.url=n,this.params=t,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(ct(n=>vl(n))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(ct(n=>vl(n))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}}function hh(e,n,t="emptyOnly"){let r;const{routeConfig:o}=e;return r=null===n||"always"!==t&&""!==o?.path&&(n.component||n.routeConfig?.loadComponent)?{params:{...e.params},data:{...e.data},resolve:{...e.data,...e._resolvedData??{}}}:{params:{...n.params,...e.params},data:{...n.data,...e.data},resolve:{...e.data,...n.data,...o?.data,...e._resolvedData}},o&&FA(o)&&(r.resolve[hu]=o.title),r}class ph{get title(){return this.data?.[hu]}constructor(n,t,r,o,i,s,a,l,c){this.url=n,this.params=t,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=l,this._resolve=c}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=vl(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=vl(this.queryParams),this._queryParamMap}toString(){return`Route(url:'${this.url.map(r=>r.toString()).join("/")}', path:'${this.routeConfig?this.routeConfig.path:""}')`}}class PA extends OA{constructor(n,t){super(t),this.url=n,fy(this,t)}toString(){return kA(this._root)}}function fy(e,n){n.value._routerState=e,n.children.forEach(t=>fy(e,t))}function kA(e){const n=e.children.length>0?` { ${e.children.map(kA).join(", ")} } `:"";return`${e.value}${n}`}function hy(e){if(e.snapshot){const n=e.snapshot,t=e._futureSnapshot;e.snapshot=t,Ho(n.queryParams,t.queryParams)||e.queryParamsSubject.next(t.queryParams),n.fragment!==t.fragment&&e.fragmentSubject.next(t.fragment),Ho(n.params,t.params)||e.paramsSubject.next(t.params),function vz(e,n){if(e.length!==n.length)return!1;for(let t=0;t<e.length;++t)if(!Ho(e[t],n[t]))return!1;return!0}(n.url,t.url)||e.urlSubject.next(t.url),Ho(n.data,t.data)||e.dataSubject.next(t.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function py(e,n){const t=Ho(e.params,n.params)&&function Dz(e,n){return ks(e,n)&&e.every((t,r)=>Ho(t.parameters,n[r].parameters))}(e.url,n.url);return t&&!(!e.parent!=!n.parent)&&(!e.parent||py(e.parent,n.parent))}function FA(e){return"string"==typeof e.title||null===e.title}let gy=(()=>{class e{constructor(){this.activated=null,this._activatedRoute=null,this.name=ft,this.activateEvents=new Dt,this.deactivateEvents=new Dt,this.attachEvents=new Dt,this.detachEvents=new Dt,this.parentContexts=U(Bs),this.location=U(Vr),this.changeDetector=U(ae),this.inputBinder=U(gh,{optional:!0}),this.supportsBindingToComponentInputs=!0}get activatedComponentRef(){return this.activated}ngOnChanges(t){if(t.name){const{firstChange:r,previousValue:o}=t.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(t){return this.parentContexts.getContext(t)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;const t=this.parentContexts.getContext(this.name);t?.route&&(t.attachRef?this.attach(t.attachRef,t.route):this.activateWith(t.route,t.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new z(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new z(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new z(4012,!1);this.location.detach();const t=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(t.instance),t}attach(t,r){this.activated=t,this._activatedRoute=r,this.location.insert(t.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(t.instance)}deactivate(){if(this.activated){const t=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new z(4013,!1);this._activatedRoute=t;const o=this.location,s=t.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,l=new my(t,a,o.injector);this.activated=o.createComponent(s,{index:o.length,injector:l,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275dir=Fe({type:e,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[wr]})}}return e})();class my{__ngOutletInjector(n){return new my(this.route,this.childContexts,n)}constructor(n,t,r){this.route=n,this.childContexts=t,this.parent=r}get(n,t){return n===Go?this.route:n===Bs?this.childContexts:this.parent.get(n,t)}}const gh=new ce("");function Du(e,n,t){if(t&&e.shouldReuseRoute(n.value,t.value.snapshot)){const r=t.value;r._futureSnapshot=n.value;const o=function a4(e,n,t){return n.children.map(r=>{for(const o of t.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return Du(e,r,o);return Du(e,r)})}(e,n,t);return new bo(r,o)}{if(e.shouldAttach(n.value)){const i=e.retrieve(n.value);if(null!==i){const s=i.route;return s.value._futureSnapshot=n.value,s.children=n.children.map(a=>Du(e,a)),s}}const r=function l4(e){return new Go(new wn(e.url),new wn(e.params),new wn(e.queryParams),new wn(e.fragment),new wn(e.data),e.outlet,e.component,e)}(n.value),o=n.children.map(i=>Du(e,i));return new bo(r,o)}}class vy{constructor(n,t){this.redirectTo=n,this.navigationBehaviorOptions=t}}const $A="ngNavigationCancelingError";function mh(e,n){const{redirectTo:t,navigationBehaviorOptions:r}=Ls(n)?{redirectTo:n,navigationBehaviorOptions:void 0}:n,o=VA(!1,Wr.Redirect);return o.url=t,o.navigationBehaviorOptions=r,o}function VA(e,n){const t=new Error(`NavigationCancelingError: ${e||""}`);return t[$A]=!0,t.cancellationCode=n,t}function BA(e){return!!e&&e[$A]}class d4{constructor(n,t,r,o,i){this.routeReuseStrategy=n,this.futureState=t,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(n){const t=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(t,r,n),hy(this.futureState.root),this.activateChildRoutes(t,r,n)}deactivateChildRoutes(n,t,r){const o=yl(t);n.children.forEach(i=>{const s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(n,t,r){const o=n.value,i=t?t.value:null;if(o===i)if(o.component){const s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(n,t,s.children)}else this.deactivateChildRoutes(n,t,r);else i&&this.deactivateRouteAndItsChildren(t,r)}deactivateRouteAndItsChildren(n,t){n.value.component&&this.routeReuseStrategy.shouldDetach(n.value.snapshot)?this.detachAndStoreRouteSubtree(n,t):this.deactivateRouteAndOutlet(n,t)}detachAndStoreRouteSubtree(n,t){const r=t.getContext(n.value.outlet),o=r&&n.value.component?r.children:t,i=yl(n);for(const s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){const s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(n.value.snapshot,{componentRef:s,route:n,contexts:a})}}deactivateRouteAndOutlet(n,t){const r=t.getContext(n.value.outlet),o=r&&n.value.component?r.children:t,i=yl(n);for(const s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(n,t,r){const o=yl(t);n.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new Qz(i.value.snapshot))}),n.children.length&&this.forwardEvent(new Zz(n.value.snapshot))}activateRoutes(n,t,r){const o=n.value,i=t?t.value:null;if(hy(o),o===i)if(o.component){const s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(n,t,s.children)}else this.activateChildRoutes(n,t,r);else if(o.component){const s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){const a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),hy(a.route.value),this.activateChildRoutes(n,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(n,null,s.children)}else this.activateChildRoutes(n,null,r)}}class jA{constructor(n){this.path=n,this.route=this.path[this.path.length-1]}}class vh{constructor(n,t){this.component=n,this.route=t}}function f4(e,n,t){const r=e._root;return bu(r,n?n._root:null,t,[r.value])}function Cl(e,n){const t=Symbol(),r=n.get(e,t);return r===t?"function"!=typeof e||function Bu(e){return null!==gi(e)}(e)?n.get(e):e:r}function bu(e,n,t,r,o={canDeactivateChecks:[],canActivateChecks:[]}){const i=yl(n);return e.children.forEach(s=>{(function p4(e,n,t,r,o={canDeactivateChecks:[],canActivateChecks:[]}){const i=e.value,s=n?n.value:null,a=t?t.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){const l=function g4(e,n,t){if("function"==typeof t)return t(e,n);switch(t){case"pathParamsChange":return!ks(e.url,n.url);case"pathParamsOrQueryParamsChange":return!ks(e.url,n.url)||!Ho(e.queryParams,n.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!py(e,n)||!Ho(e.queryParams,n.queryParams);default:return!py(e,n)}}(s,i,i.routeConfig.runGuardsAndResolvers);l?o.canActivateChecks.push(new jA(r)):(i.data=s.data,i._resolvedData=s._resolvedData),bu(e,n,i.component?a?a.children:null:t,r,o),l&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new vh(a.outlet.component,s))}else s&&Eu(n,a,o),o.canActivateChecks.push(new jA(r)),bu(e,null,i.component?a?a.children:null:t,r,o)})(s,i[s.value.outlet],t,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>Eu(a,t.getContext(s),o)),o}function Eu(e,n,t){const r=yl(e),o=e.value;Object.entries(r).forEach(([i,s])=>{Eu(s,o.component?n?n.children.getContext(i):null:n,t)}),t.canDeactivateChecks.push(new vh(o.component&&n&&n.outlet&&n.outlet.isActivated?n.outlet.component:null,o))}function wu(e){return"function"==typeof e}function UA(e){return e instanceof du||"EmptyError"===e?.name}const yh=Symbol("INITIAL_VALUE");function _l(){return mr(e=>Qv(e.map(n=>n.pipe(jo(1),function cz(...e){const n=Jf(e);return hn((t,r)=>{(n?fu(e,t,n):fu(e,t)).subscribe(r)})}(yh)))).pipe(ct(n=>{for(const t of n)if(!0!==t){if(t===yh)return yh;if(!1===t||b4(t))return t}return!0}),Uo(n=>n!==yh),jo(1)))}function b4(e){return Ls(e)||e instanceof vy}function HA(e){return function Xi(...e){return hi(e)}(vr(n=>{if("boolean"!=typeof n)throw mh(0,n)}),ct(n=>!0===n))}class yy{constructor(n){this.segmentGroup=n||null}}class Ch extends Error{constructor(n){super(),this.urlTree=n}}function Dl(e){return th(new yy(e))}class k4{constructor(n,t){this.urlSerializer=n,this.urlTree=t}lineralizeSegments(n,t){let r=[],o=t.root;for(;;){if(r=r.concat(o.segments),0===o.numberOfChildren)return je(r);if(o.numberOfChildren>1||!o.children[ft])return th(new z(4e3,!1));o=o.children[ft]}}applyRedirectCommands(n,t,r,o,i){if("string"!=typeof t){const a=t,{queryParams:l,fragment:c,routeConfig:d,url:h,outlet:v,params:y,data:C,title:b}=o,M=Ko(i,()=>a({params:y,data:C,queryParams:l,fragment:c,routeConfig:d,url:h,outlet:v,title:b}));if(M instanceof Ps)throw new Ch(M);t=M}const s=this.applyRedirectCreateUrlTree(t,this.urlSerializer.parse(t),n,r);if("/"===t[0])throw new Ch(s);return s}applyRedirectCreateUrlTree(n,t,r,o){const i=this.createSegmentGroup(n,t.root,r,o);return new Ps(i,this.createQueryParams(t.queryParams,this.urlTree.queryParams),t.fragment)}createQueryParams(n,t){const r={};return Object.entries(n).forEach(([o,i])=>{if("string"==typeof i&&":"===i[0]){const a=i.substring(1);r[o]=t[a]}else r[o]=i}),r}createSegmentGroup(n,t,r,o){const i=this.createSegments(n,t.segments,r,o);let s={};return Object.entries(t.children).forEach(([a,l])=>{s[a]=this.createSegmentGroup(n,l,r,o)}),new zt(i,s)}createSegments(n,t,r,o){return t.map(i=>":"===i.path[0]?this.findPosParam(n,i,o):this.findOrReturn(i,r))}findPosParam(n,t,r){const o=r[t.path.substring(1)];if(!o)throw new z(4001,!1);return o}findOrReturn(n,t){let r=0;for(const o of t){if(o.path===n.path)return t.splice(r),o;r++}return n}}const Cy={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function F4(e,n,t,r,o){const i=zA(e,n,t);return i.matched?(r=function Jz(e,n){return e.providers&&!e._injector&&(e._injector=_m(e.providers,n,`Route: ${e.path}`)),e._injector??n}(n,r),function R4(e,n,t,r){const o=n.canMatch;return o&&0!==o.length?je(o.map(s=>{const a=Cl(s,e);return Vi(function D4(e){return e&&wu(e.canMatch)}(a)?a.canMatch(n,t):Ko(e,()=>a(n,t)))})).pipe(_l(),HA()):je(!0)}(r,n,t).pipe(ct(s=>!0===s?i:{...Cy}))):je(i)}function zA(e,n,t){if("**"===n.path)return function L4(e){return{matched:!0,parameters:e.length>0?fA(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}(t);if(""===n.path)return"full"===n.pathMatch&&(e.hasChildren()||t.length>0)?{...Cy}:{matched:!0,consumedSegments:[],remainingSegments:t,parameters:{},positionalParamSegments:{}};const o=(n.matcher||mz)(t,e,n);if(!o)return{...Cy};const i={};Object.entries(o.posParams??{}).forEach(([a,l])=>{i[a]=l.path});const s=o.consumed.length>0?{...i,...o.consumed[o.consumed.length-1].parameters}:i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:t.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function GA(e,n,t,r){return t.length>0&&function B4(e,n,t){return t.some(r=>_h(e,n,r)&&Do(r)!==ft)}(e,t,r)?{segmentGroup:new zt(n,V4(r,new zt(t,e.children))),slicedSegments:[]}:0===t.length&&function j4(e,n,t){return t.some(r=>_h(e,n,r))}(e,t,r)?{segmentGroup:new zt(e.segments,$4(e,t,r,e.children)),slicedSegments:t}:{segmentGroup:new zt(e.segments,e.children),slicedSegments:t}}function $4(e,n,t,r){const o={};for(const i of t)if(_h(e,n,i)&&!r[Do(i)]){const s=new zt([],{});o[Do(i)]=s}return{...r,...o}}function V4(e,n){const t={};t[ft]=n;for(const r of e)if(""===r.path&&Do(r)!==ft){const o=new zt([],{});t[Do(r)]=o}return t}function _h(e,n,t){return(!(e.hasChildren()||n.length>0)||"full"!==t.pathMatch)&&""===t.path}class H4{}class W4{constructor(n,t,r,o,i,s,a){this.injector=n,this.configLoader=t,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new k4(this.urlSerializer,this.urlTree),this.absoluteRedirectCount=0,this.allowRedirects=!0}noMatchError(n){return new z(4002,`'${n.segmentGroup}'`)}recognize(){const n=GA(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(n).pipe(ct(({children:t,rootSnapshot:r})=>{const o=new bo(r,t),i=new PA("",o),s=function Fz(e,n,t=null,r=null){return wA(EA(e),n,t,r)}(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(n){const t=new ph([],Object.freeze({}),Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,Object.freeze({}),ft,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,n,ft,t).pipe(ct(r=>({children:r,rootSnapshot:t})),ui(r=>{if(r instanceof Ch)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof yy?this.noMatchError(r):r}))}processSegmentGroup(n,t,r,o,i){return 0===r.segments.length&&r.hasChildren()?this.processChildren(n,t,r,i):this.processSegment(n,t,r,r.segments,o,!0,i).pipe(ct(s=>s instanceof bo?[s]:[]))}processChildren(n,t,r,o){const i=[];for(const s of Object.keys(r.children))"primary"===s?i.unshift(s):i.push(s);return zn(i).pipe(ml(s=>{const a=r.children[s],l=function r4(e,n){const t=e.filter(r=>Do(r)===n);return t.push(...e.filter(r=>Do(r)!==n)),t}(t,s);return this.processSegmentGroup(n,l,a,s,o)}),function fz(e,n){return hn(function dz(e,n,t,r,o){return(i,s)=>{let a=t,l=n,c=0;i.subscribe(en(s,d=>{const h=c++;l=a?e(l,d,h):(a=!0,d),r&&s.next(l)},o&&(()=>{a&&s.next(l),s.complete()})))}}(e,n,arguments.length>=2,!0))}((s,a)=>(s.push(...a),s)),nh(null),function hz(e,n){const t=arguments.length>=2;return r=>r.pipe(e?Uo((o,i)=>e(o,i,r)):Yr,ty(1),t?nh(n):cA(()=>new du))}(),Jn(s=>{if(null===s)return Dl(r);const a=WA(s);return function q4(e){e.sort((n,t)=>n.value.outlet===ft?-1:t.value.outlet===ft?1:n.value.outlet.localeCompare(t.value.outlet))}(a),je(a)}))}processSegment(n,t,r,o,i,s,a){return zn(t).pipe(ml(l=>this.processSegmentAgainstRoute(l._injector??n,t,l,r,o,i,s,a).pipe(ui(c=>{if(c instanceof yy)return je(null);throw c}))),Ns(l=>!!l),ui(l=>{if(UA(l))return function U4(e,n,t){return 0===n.length&&!e.children[t]}(r,o,i)?je(new H4):Dl(r);throw l}))}processSegmentAgainstRoute(n,t,r,o,i,s,a,l){return Do(r)===s||s!==ft&&_h(o,i,r)?void 0===r.redirectTo?this.matchSegmentAgainstRoute(n,o,r,i,s,l):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(n,o,t,r,i,s,l):Dl(o):Dl(o)}expandSegmentAgainstRouteUsingRedirect(n,t,r,o,i,s,a){const{matched:l,parameters:c,consumedSegments:d,positionalParamSegments:h,remainingSegments:v}=zA(t,o,i);if(!l)return Dl(t);"string"==typeof o.redirectTo&&"/"===o.redirectTo[0]&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>31&&(this.allowRedirects=!1));const y=new ph(i,c,Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,qA(o),Do(o),o.component??o._loadedComponent??null,o,XA(o)),C=hh(y,a,this.paramsInheritanceStrategy);y.params=Object.freeze(C.params),y.data=Object.freeze(C.data);const b=this.applyRedirects.applyRedirectCommands(d,o.redirectTo,h,y,n);return this.applyRedirects.lineralizeSegments(o,b).pipe(Jn(M=>this.processSegment(n,r,t,M.concat(v),s,!1,a)))}matchSegmentAgainstRoute(n,t,r,o,i,s){const a=F4(t,r,o,n);return"**"===r.path&&(t.children={}),a.pipe(mr(l=>l.matched?this.getChildConfig(n=r._injector??n,r,o).pipe(mr(({routes:c})=>{const d=r._loadedInjector??n,{parameters:h,consumedSegments:v,remainingSegments:y}=l,C=new ph(v,h,Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,qA(r),Do(r),r.component??r._loadedComponent??null,r,XA(r)),b=hh(C,s,this.paramsInheritanceStrategy);C.params=Object.freeze(b.params),C.data=Object.freeze(b.data);const{segmentGroup:M,slicedSegments:O}=GA(t,v,y,c);if(0===O.length&&M.hasChildren())return this.processChildren(d,c,M,C).pipe(ct(Ee=>new bo(C,Ee)));if(0===c.length&&0===O.length)return je(new bo(C,[]));const A=Do(r)===i;return this.processSegment(d,c,M,O,A?ft:i,!0,C).pipe(ct(Ee=>new bo(C,Ee instanceof bo?[Ee]:[])))})):Dl(t)))}getChildConfig(n,t,r){return t.children?je({routes:t.children,injector:n}):t.loadChildren?void 0!==t._loadedRoutes?je({routes:t._loadedRoutes,injector:t._loadedInjector}):function O4(e,n,t,r){const o=n.canLoad;return void 0===o||0===o.length?je(!0):je(o.map(s=>{const a=Cl(s,e);return Vi(function v4(e){return e&&wu(e.canLoad)}(a)?a.canLoad(n,t):Ko(e,()=>a(n,t)))})).pipe(_l(),HA())}(n,t,r).pipe(Jn(o=>o?this.configLoader.loadChildren(n,t).pipe(vr(i=>{t._loadedRoutes=i.routes,t._loadedInjector=i.injector})):function P4(){return th(VA(!1,Wr.GuardRejected))}())):je({routes:[],injector:n})}}function X4(e){const n=e.value.routeConfig;return n&&""===n.path}function WA(e){const n=[],t=new Set;for(const r of e){if(!X4(r)){n.push(r);continue}const o=n.find(i=>r.value.routeConfig===i.value.routeConfig);void 0!==o?(o.children.push(...r.children),t.add(o)):n.push(r)}for(const r of t){const o=WA(r.children);n.push(new bo(r.value,o))}return n.filter(r=>!t.has(r))}function qA(e){return e.data||{}}function XA(e){return e.resolve||{}}function YA(e){const n=e.children.map(t=>YA(t)).flat();return[e,...n]}function _y(e){return mr(n=>{const t=e(n);return t?zn(t).pipe(ct(()=>n)):je(n)})}let ZA=(()=>{class e{buildTitle(t){let r,o=t.root;for(;void 0!==o;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===ft);return r}getResolvedTitleForRoute(t){return t.data[hu]}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:()=>U(e6),providedIn:"root"})}}return e})(),e6=(()=>{class e extends ZA{constructor(t){super(),this.title=t}updateTitle(t){const r=this.buildTitle(t);void 0!==r&&this.title.setTitle(r)}static{this.\u0275fac=function(r){return new(r||e)(Pe(c3))}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();const Iu=new ce("",{providedIn:"root",factory:()=>({})});let KA=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275cmp=le({type:e,selectors:[["ng-component"]],standalone:!0,features:[Xc],decls:1,vars:0,template:function(r,o){1&r&&Ms(0,"router-outlet")},dependencies:[gy],encapsulation:2})}}return e})();function Dy(e){const n=e.children&&e.children.map(Dy),t=n?{...e,children:n}:{...e};return!t.component&&!t.loadComponent&&(n||t.loadChildren)&&t.outlet&&t.outlet!==ft&&(t.component=KA),t}const Dh=new ce("");let QA=(()=>{class e{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=U(xj)}loadComponent(t){if(this.componentLoaders.get(t))return this.componentLoaders.get(t);if(t._loadedComponent)return je(t._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(t);const r=Vi(t.loadComponent()).pipe(ct(JA),vr(i=>{this.onLoadEndListener&&this.onLoadEndListener(t),t._loadedComponent=i}),rh(()=>{this.componentLoaders.delete(t)})),o=new lA(r,()=>new jt).pipe(ey());return this.componentLoaders.set(t,o),o}loadChildren(t,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return je({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);const i=function t6(e,n,t,r){return Vi(e.loadChildren()).pipe(ct(JA),Jn(o=>o instanceof EI||Array.isArray(o)?je(o):zn(n.compileModuleAsync(o))),ct(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,!0):(i=o.create(t).injector,s=i.get(Dh,[],{optional:!0,self:!0}).flat()),{routes:s.map(Dy),injector:i}}))}(r,this.compiler,t,this.onLoadEndListener).pipe(rh(()=>{this.childrenLoaders.delete(r)})),s=new lA(i,()=>new jt).pipe(ey());return this.childrenLoaders.set(r,s),s}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function JA(e){return function n6(e){return e&&"object"==typeof e&&"default"in e}(e)?e.default:e}let by=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:()=>U(r6),providedIn:"root"})}}return e})(),r6=(()=>{class e{shouldProcessUrl(t){return!0}extract(t){return t}merge(t,r){return t}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();const ex=new ce(""),nx=new ce("");let bh=(()=>{class e{get hasRequestedNavigation(){return 0!==this.navigationId}constructor(){this.currentNavigation=null,this.currentTransition=null,this.lastSuccessfulNavigation=null,this.events=new jt,this.transitionAbortSubject=new jt,this.configLoader=U(QA),this.environmentInjector=U(Pn),this.urlSerializer=U(Fs),this.rootContexts=U(Bs),this.location=U(Fi),this.inputBindingEnabled=null!==U(gh,{optional:!0}),this.titleStrategy=U(ZA),this.options=U(Iu,{optional:!0})||{},this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlHandlingStrategy=U(by),this.createViewTransition=U(ex,{optional:!0}),this.navigationErrorHandler=U(nx,{optional:!0}),this.navigationId=0,this.afterPreactivation=()=>je(void 0),this.rootComponentType=null,this.configLoader.onLoadEndListener=o=>this.events.next(new Xz(o)),this.configLoader.onLoadStartListener=o=>this.events.next(new qz(o))}complete(){this.transitions?.complete()}handleNavigationRequest(t){const r=++this.navigationId;this.transitions?.next({...this.transitions.value,...t,id:r})}setupNavigations(t,r,o){return this.transitions=new wn({id:0,currentUrlTree:r,currentRawUrl:r,extractedUrl:this.urlHandlingStrategy.extract(r),urlAfterRedirects:this.urlHandlingStrategy.extract(r),rawUrl:r,extras:{},resolve:()=>{},reject:()=>{},promise:Promise.resolve(!0),source:yu,restoredState:null,currentSnapshot:o.snapshot,targetSnapshot:null,currentRouterState:o,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe(Uo(i=>0!==i.id),ct(i=>({...i,extractedUrl:this.urlHandlingStrategy.extract(i.rawUrl)})),mr(i=>{let s=!1,a=!1;return je(i).pipe(mr(l=>{if(this.navigationId>i.id)return this.cancelNavigationTransition(i,"",Wr.SupersededByNewNavigation),Bo;this.currentTransition=i,this.currentNavigation={id:l.id,initialUrl:l.rawUrl,extractedUrl:l.extractedUrl,targetBrowserUrl:"string"==typeof l.extras.browserUrl?this.urlSerializer.parse(l.extras.browserUrl):l.extras.browserUrl,trigger:l.source,extras:l.extras,previousNavigation:this.lastSuccessfulNavigation?{...this.lastSuccessfulNavigation,previousNavigation:null}:null};const c=!t.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl();if(!c&&"reload"!==(l.extras.onSameUrlNavigation??t.onSameUrlNavigation)){const h="";return this.events.next(new Cu(l.id,this.urlSerializer.serialize(l.rawUrl),h,ay.IgnoredSameUrlNavigation)),l.resolve(!1),Bo}if(this.urlHandlingStrategy.shouldProcessUrl(l.rawUrl))return je(l).pipe(mr(h=>{const v=this.transitions?.getValue();return this.events.next(new dh(h.id,this.urlSerializer.serialize(h.extractedUrl),h.source,h.restoredState)),v!==this.transitions?.getValue()?Bo:Promise.resolve(h)}),function Y4(e,n,t,r,o,i){return Jn(s=>function z4(e,n,t,r,o,i,s="emptyOnly"){return new W4(e,n,t,r,o,s,i).recognize()}(e,n,t,r,s.extractedUrl,o,i).pipe(ct(({state:a,tree:l})=>({...s,targetSnapshot:a,urlAfterRedirects:l}))))}(this.environmentInjector,this.configLoader,this.rootComponentType,t.config,this.urlSerializer,this.paramsInheritanceStrategy),vr(h=>{i.targetSnapshot=h.targetSnapshot,i.urlAfterRedirects=h.urlAfterRedirects,this.currentNavigation={...this.currentNavigation,finalUrl:h.urlAfterRedirects};const v=new xA(h.id,this.urlSerializer.serialize(h.extractedUrl),this.urlSerializer.serialize(h.urlAfterRedirects),h.targetSnapshot);this.events.next(v)}));if(c&&this.urlHandlingStrategy.shouldProcessUrl(l.currentRawUrl)){const{id:h,extractedUrl:v,source:y,restoredState:C,extras:b}=l,M=new dh(h,this.urlSerializer.serialize(v),y,C);this.events.next(M);const O=NA(this.rootComponentType).snapshot;return this.currentTransition=i={...l,targetSnapshot:O,urlAfterRedirects:v,extras:{...b,skipLocationChange:!1,replaceUrl:!1}},this.currentNavigation.finalUrl=v,je(i)}{const h="";return this.events.next(new Cu(l.id,this.urlSerializer.serialize(l.extractedUrl),h,ay.IgnoredByUrlHandlingStrategy)),l.resolve(!1),Bo}}),vr(l=>{const c=new Hz(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(c)}),ct(l=>(this.currentTransition=i={...l,guards:f4(l.targetSnapshot,l.currentSnapshot,this.rootContexts)},i)),function E4(e,n){return Jn(t=>{const{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=t;return 0===s.length&&0===i.length?je({...t,guardsResult:!0}):function w4(e,n,t,r){return zn(e).pipe(Jn(o=>function x4(e,n,t,r,o){const i=n&&n.routeConfig?n.routeConfig.canDeactivate:null;return i&&0!==i.length?je(i.map(a=>{const l=_u(n)??o,c=Cl(a,l);return Vi(function _4(e){return e&&wu(e.canDeactivate)}(c)?c.canDeactivate(e,n,t,r):Ko(l,()=>c(e,n,t,r))).pipe(Ns())})).pipe(_l()):je(!0)}(o.component,o.route,t,n,r)),Ns(o=>!0!==o,!0))}(s,r,o,e).pipe(Jn(a=>a&&function m4(e){return"boolean"==typeof e}(a)?function I4(e,n,t,r){return zn(n).pipe(ml(o=>fu(function M4(e,n){return null!==e&&n&&n(new Yz(e)),je(!0)}(o.route.parent,r),function S4(e,n){return null!==e&&n&&n(new Kz(e)),je(!0)}(o.route,r),function A4(e,n,t){const r=n[n.length-1],i=n.slice(0,n.length-1).reverse().map(s=>function h4(e){const n=e.routeConfig?e.routeConfig.canActivateChild:null;return n&&0!==n.length?{node:e,guards:n}:null}(s)).filter(s=>null!==s).map(s=>eh(()=>je(s.guards.map(l=>{const c=_u(s.node)??t,d=Cl(l,c);return Vi(function C4(e){return e&&wu(e.canActivateChild)}(d)?d.canActivateChild(r,e):Ko(c,()=>d(r,e))).pipe(Ns())})).pipe(_l())));return je(i).pipe(_l())}(e,o.path,t),function T4(e,n,t){const r=n.routeConfig?n.routeConfig.canActivate:null;if(!r||0===r.length)return je(!0);const o=r.map(i=>eh(()=>{const s=_u(n)??t,a=Cl(i,s);return Vi(function y4(e){return e&&wu(e.canActivate)}(a)?a.canActivate(n,e):Ko(s,()=>a(n,e))).pipe(Ns())}));return je(o).pipe(_l())}(e,o.route,t))),Ns(o=>!0!==o,!0))}(r,i,e,n):je(a)),ct(a=>({...t,guardsResult:a})))})}(this.environmentInjector,l=>this.events.next(l)),vr(l=>{if(i.guardsResult=l.guardsResult,l.guardsResult&&"boolean"!=typeof l.guardsResult)throw mh(0,l.guardsResult);const c=new zz(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot,!!l.guardsResult);this.events.next(c)}),Uo(l=>!!l.guardsResult||(this.cancelNavigationTransition(l,"",Wr.GuardRejected),!1)),_y(l=>{if(l.guards.canActivateChecks.length)return je(l).pipe(vr(c=>{const d=new Gz(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(d)}),mr(c=>{let d=!1;return je(c).pipe(function Z4(e,n){return Jn(t=>{const{targetSnapshot:r,guards:{canActivateChecks:o}}=t;if(!o.length)return je(t);const i=new Set(o.map(l=>l.route)),s=new Set;for(const l of i)if(!s.has(l))for(const c of YA(l))s.add(c);let a=0;return zn(s).pipe(ml(l=>i.has(l)?function K4(e,n,t,r){const o=e.routeConfig,i=e._resolve;return void 0!==o?.title&&!FA(o)&&(i[hu]=o.title),function Q4(e,n,t,r){const o=ny(e);if(0===o.length)return je({});const i={};return zn(o).pipe(Jn(s=>function J4(e,n,t,r){const o=_u(n)??r,i=Cl(e,o);return Vi(i.resolve?i.resolve(n,t):Ko(o,()=>i(n,t)))}(e[s],n,t,r).pipe(Ns(),vr(a=>{if(a instanceof vy)throw mh(new ih,a);i[s]=a}))),ty(1),function pz(e){return ct(()=>e)}(i),ui(s=>UA(s)?Bo:th(s)))}(i,e,n,r).pipe(ct(s=>(e._resolvedData=s,e.data=hh(e,e.parent,t).resolve,null)))}(l,r,e,n):(l.data=hh(l,l.parent,e).resolve,je(void 0))),vr(()=>a++),ty(1),Jn(l=>a===s.size?je(t):Bo))})}(this.paramsInheritanceStrategy,this.environmentInjector),vr({next:()=>d=!0,complete:()=>{d||this.cancelNavigationTransition(c,"",Wr.NoDataFromResolver)}}))}),vr(c=>{const d=new Wz(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(d)}))}),_y(l=>{const c=d=>{const h=[];d.routeConfig?.loadComponent&&!d.routeConfig._loadedComponent&&h.push(this.configLoader.loadComponent(d.routeConfig).pipe(vr(v=>{d.component=v}),ct(()=>{})));for(const v of d.children)h.push(...c(v));return h};return Qv(c(l.targetSnapshot.root)).pipe(nh(null),jo(1))}),_y(()=>this.afterPreactivation()),mr(()=>{const{currentSnapshot:l,targetSnapshot:c}=i,d=this.createViewTransition?.(this.environmentInjector,l.root,c.root);return d?zn(d).pipe(ct(()=>i)):je(i)}),ct(l=>{const c=function s4(e,n,t){const r=Du(e,n._root,t?t._root:void 0);return new RA(r,n)}(t.routeReuseStrategy,l.targetSnapshot,l.currentRouterState);return this.currentTransition=i={...l,targetRouterState:c},this.currentNavigation.targetRouterState=c,i}),vr(()=>{this.events.next(new cy)}),((e,n,t,r)=>ct(o=>(new d4(n,o.targetRouterState,o.currentRouterState,t,r).activate(e),o)))(this.rootContexts,t.routeReuseStrategy,l=>this.events.next(l),this.inputBindingEnabled),jo(1),vr({next:l=>{s=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new $s(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects))),this.titleStrategy?.updateTitle(l.targetRouterState.snapshot),l.resolve(!0)},complete:()=>{s=!0}}),uA(this.transitionAbortSubject.pipe(vr(l=>{throw l}))),rh(()=>{!s&&!a&&this.cancelNavigationTransition(i,"",Wr.SupersededByNewNavigation),this.currentTransition?.id===i.id&&(this.currentNavigation=null,this.currentTransition=null)}),ui(l=>{if(a=!0,BA(l))this.events.next(new Vs(i.id,this.urlSerializer.serialize(i.extractedUrl),l.message,l.cancellationCode)),function c4(e){return BA(e)&&Ls(e.url)}(l)?this.events.next(new fh(l.url,l.navigationBehaviorOptions)):i.resolve(!1);else{const c=new ly(i.id,this.urlSerializer.serialize(i.extractedUrl),l,i.targetSnapshot??void 0);try{const d=Ko(this.environmentInjector,()=>this.navigationErrorHandler?.(c));if(d instanceof vy){const{message:h,cancellationCode:v}=mh(0,d);this.events.next(new Vs(i.id,this.urlSerializer.serialize(i.extractedUrl),h,v)),this.events.next(new fh(d.redirectTo,d.navigationBehaviorOptions))}else{this.events.next(c);const h=t.errorHandler(l);i.resolve(!!h)}}catch(d){this.options.resolveNavigationPromiseOnError?i.resolve(!1):i.reject(d)}}return Bo}))}))}cancelNavigationTransition(t,r,o){const i=new Vs(t.id,this.urlSerializer.serialize(t.extractedUrl),r,o);this.events.next(i),t.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){const t=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return t.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function s6(e){return e!==yu}let a6=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:()=>U(c6),providedIn:"root"})}}return e})();class l6{shouldDetach(n){return!1}store(n,t){}shouldAttach(n){return!1}retrieve(n){return null}shouldReuseRoute(n,t){return n.routeConfig===t.routeConfig}}let c6=(()=>{class e extends l6{static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=Ut(e)))(o||e)}})()}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),rx=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:()=>U(u6),providedIn:"root"})}}return e})(),u6=(()=>{class e extends rx{constructor(){super(...arguments),this.location=U(Fi),this.urlSerializer=U(Fs),this.options=U(Iu,{optional:!0})||{},this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.urlHandlingStrategy=U(by),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.currentUrlTree=new Ps,this.rawUrlTree=this.currentUrlTree,this.currentPageId=0,this.lastSuccessfulId=-1,this.routerState=NA(null),this.stateMemento=this.createStateMemento()}getCurrentUrlTree(){return this.currentUrlTree}getRawUrlTree(){return this.rawUrlTree}restoredState(){return this.location.getState()}get browserPageId(){return"computed"!==this.canceledNavigationResolution?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}getRouterState(){return this.routerState}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}registerNonRouterCurrentEntryChangeListener(t){return this.location.subscribe(r=>{"popstate"===r.type&&t(r.url,r.state)})}handleRouterEvent(t,r){if(t instanceof dh)this.stateMemento=this.createStateMemento();else if(t instanceof Cu)this.rawUrlTree=r.initialUrl;else if(t instanceof xA){if("eager"===this.urlUpdateStrategy&&!r.extras.skipLocationChange){const o=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl);this.setBrowserUrl(r.targetBrowserUrl??o,r)}}else t instanceof cy?(this.currentUrlTree=r.finalUrl,this.rawUrlTree=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl),this.routerState=r.targetRouterState,"deferred"===this.urlUpdateStrategy&&!r.extras.skipLocationChange&&this.setBrowserUrl(r.targetBrowserUrl??this.rawUrlTree,r)):t instanceof Vs&&(t.code===Wr.GuardRejected||t.code===Wr.NoDataFromResolver)?this.restoreHistory(r):t instanceof ly?this.restoreHistory(r,!0):t instanceof $s&&(this.lastSuccessfulId=t.id,this.currentPageId=this.browserPageId)}setBrowserUrl(t,r){const o=t instanceof Ps?this.urlSerializer.serialize(t):t;if(this.location.isCurrentPathEqualTo(o)||r.extras.replaceUrl){const s={...r.extras.state,...this.generateNgRouterState(r.id,this.browserPageId)};this.location.replaceState(o,"",s)}else{const i={...r.extras.state,...this.generateNgRouterState(r.id,this.browserPageId+1)};this.location.go(o,"",i)}}restoreHistory(t,r=!1){if("computed"===this.canceledNavigationResolution){const i=this.currentPageId-this.browserPageId;0!==i?this.location.historyGo(i):this.currentUrlTree===t.finalUrl&&0===i&&(this.resetState(t),this.resetUrlToCurrentUrlTree())}else"replace"===this.canceledNavigationResolution&&(r&&this.resetState(t),this.resetUrlToCurrentUrlTree())}resetState(t){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,t.finalUrl??this.rawUrlTree)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(t,r){return"computed"===this.canceledNavigationResolution?{navigationId:t,\u0275routerPageId:r}:{navigationId:t}}static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=Ut(e)))(o||e)}})()}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var Su=function(e){return e[e.COMPLETE=0]="COMPLETE",e[e.FAILED=1]="FAILED",e[e.REDIRECTING=2]="REDIRECTING",e}(Su||{});function d6(e){throw e}const f6={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},h6={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"};let xr=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}constructor(){this.disposed=!1,this.console=U(KS),this.stateManager=U(rx),this.options=U(Iu,{optional:!0})||{},this.pendingTasks=U(bi),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.navigationTransitions=U(bh),this.urlSerializer=U(Fs),this.location=U(Fi),this.urlHandlingStrategy=U(by),this._events=new jt,this.errorHandler=this.options.errorHandler||d6,this.navigated=!1,this.routeReuseStrategy=U(a6),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.config=U(Dh,{optional:!0})?.flat()??[],this.componentInputBindingEnabled=!!U(gh,{optional:!0}),this.eventsSubscription=new Ze,this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe({error:t=>{this.console.warn(t)}}),this.subscribeToNavigationEvents()}subscribeToNavigationEvents(){const t=this.navigationTransitions.events.subscribe(r=>{try{const o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(null!==o&&null!==i)if(this.stateManager.handleRouterEvent(r,i),r instanceof Vs&&r.code!==Wr.Redirect&&r.code!==Wr.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof $s)this.navigated=!0;else if(r instanceof fh){const s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),l={browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||"eager"===this.urlUpdateStrategy||s6(o.source),...s};this.scheduleNavigation(a,yu,null,l,{resolve:o.resolve,reject:o.reject,promise:o.promise})}(function g6(e){return!(e instanceof cy||e instanceof fh)})(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortSubject.next(o)}});this.eventsSubscription.add(t)}resetRootComponentType(t){this.routerState.root.component=t,this.navigationTransitions.rootComponentType=t}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),yu,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((t,r)=>{setTimeout(()=>{this.navigateToSyncWithBrowser(t,"popstate",r)},0)})}navigateToSyncWithBrowser(t,r,o){const i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){const l={...o};delete l.navigationId,delete l.\u0275routerPageId,0!==Object.keys(l).length&&(i.state=l)}const a=this.parseUrl(t);this.scheduleNavigation(a,r,s,i)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(t){this.config=t.map(Dy),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(t,r={}){const{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:l}=r,c=l?this.currentUrlTree.fragment:s;let h,d=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":d={...this.currentUrlTree.queryParams,...i};break;case"preserve":d=this.currentUrlTree.queryParams;break;default:d=i||null}null!==d&&(d=this.removeEmptyProps(d));try{h=EA(o?o.snapshot:this.routerState.snapshot.root)}catch{("string"!=typeof t[0]||"/"!==t[0][0])&&(t=[]),h=this.currentUrlTree.root}return wA(h,t,d,c??null)}navigateByUrl(t,r={skipLocationChange:!1}){const o=Ls(t)?t:this.parseUrl(t),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,yu,null,r)}navigate(t,r={skipLocationChange:!1}){return function p6(e){for(let n=0;n<e.length;n++)if(null==e[n])throw new z(4008,!1)}(t),this.navigateByUrl(this.createUrlTree(t,r),r)}serializeUrl(t){return this.urlSerializer.serialize(t)}parseUrl(t){try{return this.urlSerializer.parse(t)}catch{return this.urlSerializer.parse("/")}}isActive(t,r){let o;if(o=!0===r?{...f6}:!1===r?{...h6}:r,Ls(t))return pA(this.currentUrlTree,t,o);const i=this.parseUrl(t);return pA(this.currentUrlTree,i,o)}removeEmptyProps(t){return Object.entries(t).reduce((r,[o,i])=>(null!=i&&(r[o]=i),r),{})}scheduleNavigation(t,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,l,c;s?(a=s.resolve,l=s.reject,c=s.promise):c=new Promise((h,v)=>{a=h,l=v});const d=this.pendingTasks.add();return function ox(e,n){e.events.pipe(Uo(t=>t instanceof $s||t instanceof Vs||t instanceof ly||t instanceof Cu),ct(t=>t instanceof $s||t instanceof Cu?Su.COMPLETE:t instanceof Vs&&(t.code===Wr.Redirect||t.code===Wr.SupersededByNewNavigation)?Su.REDIRECTING:Su.FAILED),Uo(t=>t!==Su.REDIRECTING),jo(1)).subscribe(()=>{n()})}(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(d))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:t,extras:i,resolve:a,reject:l,promise:c,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),c.catch(h=>Promise.reject(h))}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();const Ey=new ce("");function sx(e){return e.routerState.root}function ax(){const e=U(Zt);return n=>{const t=e.get(yo);if(n!==t.components[0])return;const r=e.get(xr),o=e.get(lx);1===e.get(wy)&&r.initialNavigation(),e.get(cx,null,ut.Optional)?.setUpPreloading(),e.get(Ey,null,ut.Optional)?.init(),r.resetRootComponentType(t.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}const lx=new ce("",{factory:()=>new jt}),wy=new ce("",{providedIn:"root",factory:()=>1}),cx=new ce("");function dx(...e){const n=oA(e),{args:t,keys:r}=iA(e),o=new Yt(i=>{const{length:s}=t;if(!s)return void i.complete();const a=new Array(s);let l=s,c=s;for(let d=0;d<s;d++){let h=!1;Gr(t[d]).subscribe(en(i,v=>{h||(h=!0,c--),a[d]=v},()=>l--,void 0,()=>{(!l||!h)&&(c||i.next(r?sA(r,a):a),i.complete())}))}});return n?o.pipe(Kv(n)):o}let fx=(()=>{class e{constructor(t,r){this._renderer=t,this._elementRef=r,this.onChange=o=>{},this.onTouched=()=>{}}setProperty(t,r){this._renderer.setProperty(this._elementRef.nativeElement,t,r)}registerOnTouched(t){this.onTouched=t}registerOnChange(t){this.onChange=t}setDisabledState(t){this.setProperty("disabled",t)}static{this.\u0275fac=function(r){return new(r||e)(g(ii),g(W))}}static{this.\u0275dir=Fe({type:e})}}return e})(),js=(()=>{class e extends fx{static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=Ut(e)))(o||e)}})()}static{this.\u0275dir=Fe({type:e,features:[dt]})}}return e})();const Or=new ce(""),I6={provide:Or,useExisting:kt(()=>Iy),multi:!0},M6=new ce("");let Iy=(()=>{class e extends fx{constructor(t,r,o){super(t,r),this._compositionMode=o,this._composing=!1,null==this._compositionMode&&(this._compositionMode=!function S6(){const e=ki()?ki().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}())}writeValue(t){this.setProperty("value",t??"")}_handleInput(t){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(t)}_compositionStart(){this._composing=!0}_compositionEnd(t){this._composing=!1,this._compositionMode&&this.onChange(t)}static{this.\u0275fac=function(r){return new(r||e)(g(ii),g(W),g(M6,8))}}static{this.\u0275dir=Fe({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,o){1&r&&sn("input",function(s){return o._handleInput(s.target.value)})("blur",function(){return o.onTouched()})("compositionstart",function(){return o._compositionStart()})("compositionend",function(s){return o._compositionEnd(s.target.value)})},features:[Ft([I6]),dt]})}}return e})();function Bi(e){return null==e||("string"==typeof e||Array.isArray(e))&&0===e.length}const Bn=new ce(""),ji=new ce("");function Eh(e){return null}function Ex(e){return null!=e}function wx(e){return eu(e)?zn(e):e}function Ix(e){let n={};return e.forEach(t=>{n=null!=t?{...n,...t}:n}),0===Object.keys(n).length?null:n}function Sx(e,n){return n.map(t=>t(e))}function Mx(e){return e.map(n=>function A6(e){return!e.validate}(n)?n:t=>n.validate(t))}function Sy(e){return null!=e?function Tx(e){if(!e)return null;const n=e.filter(Ex);return 0==n.length?null:function(t){return Ix(Sx(t,n))}}(Mx(e)):null}function My(e){return null!=e?function Ax(e){if(!e)return null;const n=e.filter(Ex);return 0==n.length?null:function(t){return dx(Sx(t,n).map(wx)).pipe(ct(Ix))}}(Mx(e)):null}function xx(e,n){return null===e?[n]:Array.isArray(e)?[...e,n]:[e,n]}function Ty(e){return e?Array.isArray(e)?e:[e]:[]}function wh(e,n){return Array.isArray(e)?e.includes(n):e===n}function Nx(e,n){const t=Ty(n);return Ty(e).forEach(o=>{wh(t,o)||t.push(o)}),t}function Px(e,n){return Ty(n).filter(t=>!wh(e,t))}class kx{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(n){this._rawValidators=n||[],this._composedValidatorFn=Sy(this._rawValidators)}_setAsyncValidators(n){this._rawAsyncValidators=n||[],this._composedAsyncValidatorFn=My(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(n){this._onDestroyCallbacks.push(n)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(n=>n()),this._onDestroyCallbacks=[]}reset(n=void 0){this.control&&this.control.reset(n)}hasError(n,t){return!!this.control&&this.control.hasError(n,t)}getError(n,t){return this.control?this.control.getError(n,t):null}}class yr extends kx{get formDirective(){return null}get path(){return null}}class di extends kx{constructor(){super(...arguments),this._parent=null,this.name=null,this.valueAccessor=null}}class Fx{constructor(n){this._cd=n}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}}let Lx=(()=>{class e extends Fx{constructor(t){super(t)}static{this.\u0275fac=function(r){return new(r||e)(g(di,2))}}static{this.\u0275dir=Fe({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,o){2&r&&Oi("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)},features:[dt]})}}return e})();const Tu="VALID",Sh="INVALID",bl="PENDING",Au="DISABLED";class El{}class Vx extends El{constructor(n,t){super(),this.value=n,this.source=t}}class Oy extends El{constructor(n,t){super(),this.pristine=n,this.source=t}}class Ry extends El{constructor(n,t){super(),this.touched=n,this.source=t}}class Mh extends El{constructor(n,t){super(),this.status=n,this.source=t}}function Th(e){return null!=e&&!Array.isArray(e)&&"object"==typeof e}class ky{constructor(n,t){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=null,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this._status=iu(()=>this.statusReactive()),this.statusReactive=Ha(void 0),this._pristine=iu(()=>this.pristineReactive()),this.pristineReactive=Ha(!0),this._touched=iu(()=>this.touchedReactive()),this.touchedReactive=Ha(!1),this._events=new jt,this.events=this._events.asObservable(),this._onDisabledChange=[],this._assignValidators(n),this._assignAsyncValidators(t)}get validator(){return this._composedValidatorFn}set validator(n){this._rawValidators=this._composedValidatorFn=n}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(n){this._rawAsyncValidators=this._composedAsyncValidatorFn=n}get parent(){return this._parent}get status(){return Vo(this.statusReactive)}set status(n){Vo(()=>this.statusReactive.set(n))}get valid(){return this.status===Tu}get invalid(){return this.status===Sh}get pending(){return this.status==bl}get disabled(){return this.status===Au}get enabled(){return this.status!==Au}get pristine(){return Vo(this.pristineReactive)}set pristine(n){Vo(()=>this.pristineReactive.set(n))}get dirty(){return!this.pristine}get touched(){return Vo(this.touchedReactive)}set touched(n){Vo(()=>this.touchedReactive.set(n))}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(n){this._assignValidators(n)}setAsyncValidators(n){this._assignAsyncValidators(n)}addValidators(n){this.setValidators(Nx(n,this._rawValidators))}addAsyncValidators(n){this.setAsyncValidators(Nx(n,this._rawAsyncValidators))}removeValidators(n){this.setValidators(Px(n,this._rawValidators))}removeAsyncValidators(n){this.setAsyncValidators(Px(n,this._rawAsyncValidators))}hasValidator(n){return wh(this._rawValidators,n)}hasAsyncValidator(n){return wh(this._rawAsyncValidators,n)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(n={}){const t=!1===this.touched;this.touched=!0;const r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsTouched({...n,sourceControl:r}),t&&!1!==n.emitEvent&&this._events.next(new Ry(!0,r))}markAllAsTouched(n={}){this.markAsTouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:this}),this._forEachChild(t=>t.markAllAsTouched(n))}markAsUntouched(n={}){const t=!0===this.touched;this.touched=!1,this._pendingTouched=!1;const r=n.sourceControl??this;this._forEachChild(o=>{o.markAsUntouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:r})}),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,r),t&&!1!==n.emitEvent&&this._events.next(new Ry(!1,r))}markAsDirty(n={}){const t=!0===this.pristine;this.pristine=!1;const r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsDirty({...n,sourceControl:r}),t&&!1!==n.emitEvent&&this._events.next(new Oy(!1,r))}markAsPristine(n={}){const t=!1===this.pristine;this.pristine=!0,this._pendingDirty=!1;const r=n.sourceControl??this;this._forEachChild(o=>{o.markAsPristine({onlySelf:!0,emitEvent:n.emitEvent})}),this._parent&&!n.onlySelf&&this._parent._updatePristine(n,r),t&&!1!==n.emitEvent&&this._events.next(new Oy(!0,r))}markAsPending(n={}){this.status=bl;const t=n.sourceControl??this;!1!==n.emitEvent&&(this._events.next(new Mh(this.status,t)),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.markAsPending({...n,sourceControl:t})}disable(n={}){const t=this._parentMarkedDirty(n.onlySelf);this.status=Au,this.errors=null,this._forEachChild(o=>{o.disable({...n,onlySelf:!0})}),this._updateValue();const r=n.sourceControl??this;!1!==n.emitEvent&&(this._events.next(new Vx(this.value,r)),this._events.next(new Mh(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors({...n,skipPristineCheck:t},this),this._onDisabledChange.forEach(o=>o(!0))}enable(n={}){const t=this._parentMarkedDirty(n.onlySelf);this.status=Tu,this._forEachChild(r=>{r.enable({...n,onlySelf:!0})}),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent}),this._updateAncestors({...n,skipPristineCheck:t},this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(n,t){this._parent&&!n.onlySelf&&(this._parent.updateValueAndValidity(n),n.skipPristineCheck||this._parent._updatePristine({},t),this._parent._updateTouched({},t))}setParent(n){this._parent=n}getRawValue(){return this.value}updateValueAndValidity(n={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){const r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===Tu||this.status===bl)&&this._runAsyncValidator(r,n.emitEvent)}const t=n.sourceControl??this;!1!==n.emitEvent&&(this._events.next(new Vx(this.value,t)),this._events.next(new Mh(this.status,t)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.updateValueAndValidity({...n,sourceControl:t})}_updateTreeValidity(n={emitEvent:!0}){this._forEachChild(t=>t._updateTreeValidity(n)),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?Au:Tu}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(n,t){if(this.asyncValidator){this.status=bl,this._hasOwnPendingAsyncValidator={emitEvent:!1!==t};const r=wx(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(o=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(o,{emitEvent:t,shouldHaveEmitted:n})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();const n=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,n}return!1}setErrors(n,t={}){this.errors=n,this._updateControlsErrors(!1!==t.emitEvent,this,t.shouldHaveEmitted)}get(n){let t=n;return null==t||(Array.isArray(t)||(t=t.split(".")),0===t.length)?null:t.reduce((r,o)=>r&&r._find(o),this)}getError(n,t){const r=t?this.get(t):this;return r&&r.errors?r.errors[n]:null}hasError(n,t){return!!this.getError(n,t)}get root(){let n=this;for(;n._parent;)n=n._parent;return n}_updateControlsErrors(n,t,r){this.status=this._calculateStatus(),n&&this.statusChanges.emit(this.status),(n||r)&&this._events.next(new Mh(this.status,t)),this._parent&&this._parent._updateControlsErrors(n,t,r)}_initObservables(){this.valueChanges=new Dt,this.statusChanges=new Dt}_calculateStatus(){return this._allControlsDisabled()?Au:this.errors?Sh:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(bl)?bl:this._anyControlsHaveStatus(Sh)?Sh:Tu}_anyControlsHaveStatus(n){return this._anyControls(t=>t.status===n)}_anyControlsDirty(){return this._anyControls(n=>n.dirty)}_anyControlsTouched(){return this._anyControls(n=>n.touched)}_updatePristine(n,t){const r=!this._anyControlsDirty(),o=this.pristine!==r;this.pristine=r,this._parent&&!n.onlySelf&&this._parent._updatePristine(n,t),o&&this._events.next(new Oy(this.pristine,t))}_updateTouched(n={},t){this.touched=this._anyControlsTouched(),this._events.next(new Ry(this.touched,t)),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,t)}_registerOnCollectionChange(n){this._onCollectionChange=n}_setUpdateStrategy(n){Th(n)&&null!=n.updateOn&&(this._updateOn=n.updateOn)}_parentMarkedDirty(n){return!n&&!(!this._parent||!this._parent.dirty)&&!this._parent._anyControlsDirty()}_find(n){return null}_assignValidators(n){this._rawValidators=Array.isArray(n)?n.slice():n,this._composedValidatorFn=function L6(e){return Array.isArray(e)?Sy(e):e||null}(this._rawValidators)}_assignAsyncValidators(n){this._rawAsyncValidators=Array.isArray(n)?n.slice():n,this._composedAsyncValidatorFn=function $6(e){return Array.isArray(e)?My(e):e||null}(this._rawAsyncValidators)}}const wl=new ce("CallSetDisabledState",{providedIn:"root",factory:()=>Ah}),Ah="always";function xu(e,n,t=Ah){(function Ly(e,n){const t=function Ox(e){return e._rawValidators}(e);null!==n.validator?e.setValidators(xx(t,n.validator)):"function"==typeof t&&e.setValidators([t]);const r=function Rx(e){return e._rawAsyncValidators}(e);null!==n.asyncValidator?e.setAsyncValidators(xx(r,n.asyncValidator)):"function"==typeof r&&e.setAsyncValidators([r]);const o=()=>e.updateValueAndValidity();Rh(n._rawValidators,o),Rh(n._rawAsyncValidators,o)})(e,n),n.valueAccessor.writeValue(e.value),(e.disabled||"always"===t)&&n.valueAccessor.setDisabledState?.(e.disabled),function j6(e,n){n.valueAccessor.registerOnChange(t=>{e._pendingValue=t,e._pendingChange=!0,e._pendingDirty=!0,"change"===e.updateOn&&Ux(e,n)})}(e,n),function H6(e,n){const t=(r,o)=>{n.valueAccessor.writeValue(r),o&&n.viewToModelUpdate(r)};e.registerOnChange(t),n._registerOnDestroy(()=>{e._unregisterOnChange(t)})}(e,n),function U6(e,n){n.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,"blur"===e.updateOn&&e._pendingChange&&Ux(e,n),"submit"!==e.updateOn&&e.markAsTouched()})}(e,n),function B6(e,n){if(n.valueAccessor.setDisabledState){const t=r=>{n.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(t),n._registerOnDestroy(()=>{e._unregisterOnDisabledChange(t)})}}(e,n)}function Rh(e,n){e.forEach(t=>{t.registerOnValidatorChange&&t.registerOnValidatorChange(n)})}function Ux(e,n){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),n.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function Gx(e,n){const t=e.indexOf(n);t>-1&&e.splice(t,1)}function Wx(e){return"object"==typeof e&&null!==e&&2===Object.keys(e).length&&"value"in e&&"disabled"in e}Promise.resolve();const qx=class extends ky{constructor(n=null,t,r){super(function Ny(e){return(Th(e)?e.validators:e)||null}(t),function Py(e,n){return(Th(n)?n.asyncValidators:e)||null}(r,t)),this.defaultValue=null,this._onChange=[],this._pendingChange=!1,this._applyFormState(n),this._setUpdateStrategy(t),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),Th(t)&&(t.nonNullable||t.initialValueIsDefault)&&(this.defaultValue=Wx(n)?n.value:n)}setValue(n,t={}){this.value=this._pendingValue=n,this._onChange.length&&!1!==t.emitModelToViewChange&&this._onChange.forEach(r=>r(this.value,!1!==t.emitViewToModelChange)),this.updateValueAndValidity(t)}patchValue(n,t={}){this.setValue(n,t)}reset(n=this.defaultValue,t={}){this._applyFormState(n),this.markAsPristine(t),this.markAsUntouched(t),this.setValue(this.value,t),this._pendingChange=!1}_updateValue(){}_anyControls(n){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(n){this._onChange.push(n)}_unregisterOnChange(n){Gx(this._onChange,n)}registerOnDisabledChange(n){this._onDisabledChange.push(n)}_unregisterOnDisabledChange(n){Gx(this._onDisabledChange,n)}_forEachChild(n){}_syncPendingControls(){return!("submit"!==this.updateOn||(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),!this._pendingChange)||(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),0))}_applyFormState(n){Wx(n)?(this.value=this._pendingValue=n.value,n.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=n}},K6={provide:di,useExisting:kt(()=>Uy)},Zx=Promise.resolve();let Uy=(()=>{class e extends di{constructor(t,r,o,i,s,a){super(),this._changeDetectorRef=s,this.callSetDisabledState=a,this.control=new qx,this._registered=!1,this.name="",this.update=new Dt,this._parent=t,this._setValidators(r),this._setAsyncValidators(o),this.valueAccessor=function By(e,n){if(!n)return null;let t,r,o;return Array.isArray(n),n.forEach(i=>{i.constructor===Iy?t=i:function W6(e){return Object.getPrototypeOf(e.constructor)===js}(i)?r=i:o=i}),o||r||t||null}(0,i)}ngOnChanges(t){if(this._checkForErrors(),!this._registered||"name"in t){if(this._registered&&(this._checkName(),this.formDirective)){const r=t.name.previousValue;this.formDirective.removeControl({name:r,path:this._getPath(r)})}this._setUpControl()}"isDisabled"in t&&this._updateDisabled(t),function Vy(e,n){if(!e.hasOwnProperty("model"))return!1;const t=e.model;return!!t.isFirstChange()||!Object.is(n,t.currentValue)}(t,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(t){this.viewModel=t,this.update.emit(t)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&null!=this.options.updateOn&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!(!this.options||!this.options.standalone)}_setUpStandalone(){xu(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._isStandalone()||this._checkParentType(),this._checkName()}_checkParentType(){}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),this._isStandalone()}_updateValue(t){Zx.then(()=>{this.control.setValue(t,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(t){const r=t.isDisabled.currentValue,o=0!==r&&function fl(e){return"boolean"==typeof e?e:null!=e&&"false"!==e}(r);Zx.then(()=>{o&&!this.control.disabled?this.control.disable():!o&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(t){return this._parent?function xh(e,n){return[...n.path,e]}(t,this._parent):[t]}static{this.\u0275fac=function(r){return new(r||e)(g(yr,9),g(Bn,10),g(ji,10),g(Or,10),g(ae,8),g(wl,8))}}static{this.\u0275dir=Fe({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],features:[Ft([K6]),dt,wr]})}}return e})();function aO(e){return"number"==typeof e?e:parseFloat(e)}let Us=(()=>{class e{constructor(){this._validator=Eh}ngOnChanges(t){if(this.inputName in t){const r=this.normalizeInput(t[this.inputName].currentValue);this._enabled=this.enabled(r),this._validator=this._enabled?this.createValidator(r):Eh,this._onChange&&this._onChange()}}validate(t){return this._validator(t)}registerOnValidatorChange(t){this._onChange=t}enabled(t){return null!=t}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275dir=Fe({type:e,features:[wr]})}}return e})();const p8={provide:Bn,useExisting:kt(()=>Xy),multi:!0};let Xy=(()=>{class e extends Us{constructor(){super(...arguments),this.inputName="max",this.normalizeInput=t=>aO(t),this.createValidator=t=>function mx(e){return n=>{if(Bi(n.value)||Bi(e))return null;const t=parseFloat(n.value);return!isNaN(t)&&t>e?{max:{max:e,actual:n.value}}:null}}(t)}static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=Ut(e)))(o||e)}})()}static{this.\u0275dir=Fe({type:e,selectors:[["input","type","number","max","","formControlName",""],["input","type","number","max","","formControl",""],["input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(r,o){2&r&&jr("max",o._enabled?o.max:null)},inputs:{max:"max"},features:[Ft([p8]),dt]})}}return e})();const g8={provide:Bn,useExisting:kt(()=>Yy),multi:!0};let Yy=(()=>{class e extends Us{constructor(){super(...arguments),this.inputName="min",this.normalizeInput=t=>aO(t),this.createValidator=t=>function gx(e){return n=>{if(Bi(n.value)||Bi(e))return null;const t=parseFloat(n.value);return!isNaN(t)&&t<e?{min:{min:e,actual:n.value}}:null}}(t)}static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=Ut(e)))(o||e)}})()}static{this.\u0275dir=Fe({type:e,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(r,o){2&r&&jr("min",o._enabled?o.min:null)},inputs:{min:"min"},features:[Ft([g8]),dt]})}}return e})(),b8=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=is({type:e})}static{this.\u0275inj=Zr({})}}return e})(),w8=(()=>{class e{static withConfig(t){return{ngModule:e,providers:[{provide:wl,useValue:t.callSetDisabledState??Ah}]}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=is({type:e})}static{this.\u0275inj=Zr({imports:[b8]})}}return e})();var Hs=Y(467);Y(7837);var Ru=Y(6031);const H8=["addListener","removeListener"],z8=["addEventListener","removeEventListener"],G8=["on","off"];function kh(e,n,t,r){if(nt(t)&&(r=t,t=void 0),r)return kh(e,n,t).pipe(Kv(r));const[o,i]=function X8(e){return nt(e.addEventListener)&&nt(e.removeEventListener)}(e)?z8.map(s=>a=>e[s](n,a,t)):function W8(e){return nt(e.addListener)&&nt(e.removeListener)}(e)?H8.map(DO(e,n)):function q8(e){return nt(e.on)&&nt(e.off)}(e)?G8.map(DO(e,n)):[];if(!o&&Yv(e))return Jn(s=>kh(s,n,t))(Gr(e));if(!o)throw new TypeError("Invalid event target");return new Yt(s=>{const a=(...l)=>s.next(1<l.length?l:l[0]);return o(a),()=>i(a)})}function DO(e,n){return t=>r=>e[t](n,r)}const Jy=new ce("USERCONFIG");class wO{data;constructor(n={}){this.data=n,console.warn("[Ionic Warning]: NavParams has been deprecated in favor of using Angular's input API. Developers should migrate to either the @Input decorator or the Signals-based input API.")}get(n){return this.data[n]}}let Nu=(()=>{class e{zone=U(j);applicationRef=U(yo);config=U(Jy);create(t,r,o){return new tG(t,r,this.applicationRef,this.zone,o,this.config.useSetInputAPI??!1)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=Me({token:e,factory:e.\u0275fac})}return e})();class tG{environmentInjector;injector;applicationRef;zone;elementReferenceKey;enableSignalsSupport;elRefMap=new WeakMap;elEventsMap=new WeakMap;constructor(n,t,r,o,i,s){this.environmentInjector=n,this.injector=t,this.applicationRef=r,this.zone=o,this.elementReferenceKey=i,this.enableSignalsSupport=s}attachViewToDom(n,t,r,o){return this.zone.run(()=>new Promise(i=>{const s={...r};void 0!==this.elementReferenceKey&&(s[this.elementReferenceKey]=n),i(nG(this.zone,this.environmentInjector,this.injector,this.applicationRef,this.elRefMap,this.elEventsMap,n,t,s,o,this.elementReferenceKey,this.enableSignalsSupport))}))}removeViewFromDom(n,t){return this.zone.run(()=>new Promise(r=>{const o=this.elRefMap.get(t);if(o){o.destroy(),this.elRefMap.delete(t);const i=this.elEventsMap.get(t);i&&(i(),this.elEventsMap.delete(t))}r()}))}}const nG=(e,n,t,r,o,i,s,a,l,c,d,h)=>{const y=function U2(e,n){const t=Ct(e),r=n.elementInjector||Qu();return new Rc(t).create(r,n.projectableNodes,n.hostElement,n.environmentInjector)}(a,{environmentInjector:n,elementInjector:Zt.create({providers:oG(l),parent:t})}),C=y.instance,b=y.location.nativeElement;if(l)if(d&&void 0!==C[d]&&console.error(`[Ionic Error]: ${d} is a reserved property when using ${s.tagName.toLowerCase()}. Rename or remove the "${d}" property from ${a.name}.`),!0===h&&void 0!==y.setInput){const{modal:O,popover:A,...Ee}=l;for(const Ge in Ee)y.setInput(Ge,Ee[Ge]);void 0!==O&&Object.assign(C,{modal:O}),void 0!==A&&Object.assign(C,{popover:A})}else Object.assign(C,l);if(c)for(const O of c)b.classList.add(O);const M=IO(e,C,b);return s.appendChild(b),r.attachView(y.hostView),o.set(b,y),i.set(b,M),b},rG=[Ru.L,Ru.a,Ru.b,Ru.c,Ru.d],IO=(e,n,t)=>e.run(()=>{const r=rG.filter(o=>"function"==typeof n[o]).map(o=>{const i=s=>n[o](s.detail);return t.addEventListener(o,i),()=>t.removeEventListener(o,i)});return()=>r.forEach(o=>o())}),SO=new ce("NavParamsToken"),oG=e=>[{provide:SO,useValue:e},{provide:wO,useFactory:iG,deps:[SO]}],iG=e=>new wO(e),NO=new ce("");let DG=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(t){this.unsubscribeFromRouteData(t),this.subscribeToRouteData(t)}unsubscribeFromRouteData(t){this.outletDataSubscriptions.get(t)?.unsubscribe(),this.outletDataSubscriptions.delete(t)}subscribeToRouteData(t){const{activatedRoute:r}=t,o=Qv([r.queryParams,r.params,r.data]).pipe(mr(([i,s,a],l)=>(a={...i,...s,...a},0===l?je(a):Promise.resolve(a)))).subscribe(i=>{if(!t.isActivated||!t.activatedComponentRef||t.activatedRoute!==r||null===r.component)return void this.unsubscribeFromRouteData(t);const s=function H2(e){const n=Ct(e);if(!n)return null;const t=new Rc(n);return{get selector(){return t.selector},get type(){return t.componentType},get inputs(){return t.inputs},get outputs(){return t.outputs},get ngContentSelectors(){return t.ngContentSelectors},get isStandalone(){return n.standalone},get isSignal(){return n.signals}}}(r.component);if(s)for(const{templateName:a}of s.inputs)t.activatedComponentRef.setInput(a,i[a]);else this.unsubscribeFromRouteData(t)});this.outletDataSubscriptions.set(t,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=Me({token:e,factory:e.\u0275fac})}return e})();function EG(e){return e?.componentInputBindingEnabled?new DG:null}const PO=e=>"function"==typeof __zone_symbol__requestAnimationFrame?__zone_symbol__requestAnimationFrame(e):"function"==typeof requestAnimationFrame?requestAnimationFrame(e):setTimeout(e);let Lh=(()=>{class e{injector;elementRef;onChange=()=>{};onTouched=()=>{};lastValue;statusChanges;constructor(t,r){this.injector=t,this.elementRef=r}writeValue(t){this.elementRef.nativeElement.value=this.lastValue=t,Tl(this.elementRef)}handleValueChange(t,r){t===this.elementRef.nativeElement&&(r!==this.lastValue&&(this.lastValue=r,this.onChange(r)),Tl(this.elementRef))}_handleBlurEvent(t){t===this.elementRef.nativeElement?(this.onTouched(),Tl(this.elementRef)):t.closest("ion-radio-group")===this.elementRef.nativeElement&&this.onTouched()}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.elementRef.nativeElement.disabled=t}ngOnDestroy(){this.statusChanges&&this.statusChanges.unsubscribe()}ngAfterViewInit(){let t;try{t=this.injector.get(di)}catch{}if(!t)return;t.statusChanges&&(this.statusChanges=t.statusChanges.subscribe(()=>Tl(this.elementRef)));const r=t.control;r&&["markAsTouched","markAllAsTouched","markAsUntouched","markAsDirty","markAsPristine"].forEach(i=>{if(typeof r[i]<"u"){const s=r[i].bind(r);r[i]=(...a)=>{s(...a),Tl(this.elementRef)}}})}static \u0275fac=function(r){return new(r||e)(g(Zt),g(W))};static \u0275dir=Fe({type:e,hostBindings:function(r,o){1&r&&sn("ionBlur",function(s){return o._handleBlurEvent(s.target)})}})}return e})();const Tl=e=>{PO(()=>{const n=e.nativeElement,t=null!=n.value&&n.value.toString().length>0,r=RG(n);rC(n,r);const o=n.closest("ion-item");o&&rC(o,t?[...r,"item-has-value"]:r)})},RG=e=>{const n=e.classList,t=[];for(let r=0;r<n.length;r++){const o=n.item(r);null!==o&&NG(o,"ng-")&&t.push(`ion-${o.substring(3)}`)}return t},rC=(e,n)=>{const t=e.classList;t.remove("ion-valid","ion-invalid","ion-touched","ion-untouched","ion-dirty","ion-pristine"),t.add(...n)},NG=(e,n)=>e.substring(0,n.length)===n;class kO{ctrl;constructor(n){this.ctrl=n}create(n){return this.ctrl.create(n||{})}dismiss(n,t,r){return this.ctrl.dismiss(n,t,r)}getTop(){return this.ctrl.getTop()}}Y(5756),Y(4590),Y(2668),Y(4227),Y(6011);var FO=Y(2734),PG=(Y(1837),Y(6640)),LO=(Y(8855),Y(3217));Y(9596),Y(8607),Y(1906),Y(1653);const kG=function(){var e=(0,Hs.A)(function*(n,t){if(!(typeof window>"u"))return yield(0,FO.g)(),(0,FO.b)(JSON.parse('[["ion-menu_3",[[33,"ion-menu-button",{"color":[513],"disabled":[4],"menu":[1],"autoHide":[4,"auto-hide"],"type":[1],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]],[33,"ion-menu",{"contentId":[513,"content-id"],"menuId":[513,"menu-id"],"type":[1025],"disabled":[1028],"side":[513],"swipeGesture":[4,"swipe-gesture"],"maxEdgeStart":[2,"max-edge-start"],"isPaneVisible":[32],"isEndSide":[32],"isOpen":[64],"isActive":[64],"open":[64],"close":[64],"toggle":[64],"setOpen":[64]},[[16,"ionSplitPaneVisible","onSplitPaneChanged"],[2,"click","onBackdropClick"]],{"type":["typeChanged"],"disabled":["disabledChanged"],"side":["sideChanged"],"swipeGesture":["swipeGestureChanged"]}],[1,"ion-menu-toggle",{"menu":[1],"autoHide":[4,"auto-hide"],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]]]],["ion-input-password-toggle",[[33,"ion-input-password-toggle",{"color":[513],"showIcon":[1,"show-icon"],"hideIcon":[1,"hide-icon"],"type":[1025]},null,{"type":["onTypeChange"]}]]],["ion-fab_3",[[33,"ion-fab-button",{"color":[513],"activated":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1],"show":[4],"translucent":[4],"type":[1],"size":[1],"closeIcon":[1,"close-icon"]}],[1,"ion-fab",{"horizontal":[1],"vertical":[1],"edge":[4],"activated":[1028],"close":[64],"toggle":[64]},null,{"activated":["activatedChanged"]}],[1,"ion-fab-list",{"activated":[4],"side":[1]},null,{"activated":["activatedChanged"]}]]],["ion-refresher_2",[[0,"ion-refresher-content",{"pullingIcon":[1025,"pulling-icon"],"pullingText":[1,"pulling-text"],"refreshingSpinner":[1025,"refreshing-spinner"],"refreshingText":[1,"refreshing-text"]}],[32,"ion-refresher",{"pullMin":[2,"pull-min"],"pullMax":[2,"pull-max"],"closeDuration":[1,"close-duration"],"snapbackDuration":[1,"snapback-duration"],"pullFactor":[2,"pull-factor"],"disabled":[4],"nativeRefresher":[32],"state":[32],"complete":[64],"cancel":[64],"getProgress":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-back-button",[[33,"ion-back-button",{"color":[513],"defaultHref":[1025,"default-href"],"disabled":[516],"icon":[1],"text":[1],"type":[1],"routerAnimation":[16,"router-animation"]}]]],["ion-toast",[[33,"ion-toast",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"color":[513],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"cssClass":[1,"css-class"],"duration":[2],"header":[1],"layout":[1],"message":[1],"keyboardClose":[4,"keyboard-close"],"position":[1],"positionAnchor":[1,"position-anchor"],"buttons":[16],"translucent":[4],"animated":[4],"icon":[1],"htmlAttributes":[16,"html-attributes"],"swipeGesture":[1,"swipe-gesture"],"isOpen":[4,"is-open"],"trigger":[1],"revealContentToScreenReader":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"swipeGesture":["swipeGestureChanged"],"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-card_5",[[33,"ion-card",{"color":[513],"button":[4],"type":[1],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1]}],[32,"ion-card-content"],[33,"ion-card-header",{"color":[513],"translucent":[4]}],[33,"ion-card-subtitle",{"color":[513]}],[33,"ion-card-title",{"color":[513]}]]],["ion-item-option_3",[[33,"ion-item-option",{"color":[513],"disabled":[4],"download":[1],"expandable":[4],"href":[1],"rel":[1],"target":[1],"type":[1]}],[32,"ion-item-options",{"side":[1],"fireSwipeEvent":[64]}],[0,"ion-item-sliding",{"disabled":[4],"state":[32],"getOpenAmount":[64],"getSlidingRatio":[64],"open":[64],"close":[64],"closeOpened":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-accordion_2",[[49,"ion-accordion",{"value":[1],"disabled":[4],"readonly":[4],"toggleIcon":[1,"toggle-icon"],"toggleIconSlot":[1,"toggle-icon-slot"],"state":[32],"isNext":[32],"isPrevious":[32]},null,{"value":["valueChanged"]}],[33,"ion-accordion-group",{"animated":[4],"multiple":[4],"value":[1025],"disabled":[4],"readonly":[4],"expand":[1],"requestAccordionToggle":[64],"getAccordions":[64]},[[0,"keydown","onKeydown"]],{"value":["valueChanged"],"disabled":["disabledChanged"],"readonly":["readonlyChanged"]}]]],["ion-infinite-scroll_2",[[32,"ion-infinite-scroll-content",{"loadingSpinner":[1025,"loading-spinner"],"loadingText":[1,"loading-text"]}],[0,"ion-infinite-scroll",{"threshold":[1],"disabled":[4],"position":[1],"isLoading":[32],"complete":[64]},null,{"threshold":["thresholdChanged"],"disabled":["disabledChanged"]}]]],["ion-reorder_2",[[33,"ion-reorder",null,[[2,"click","onClick"]]],[0,"ion-reorder-group",{"disabled":[4],"state":[32],"complete":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-segment_2",[[33,"ion-segment-button",{"contentId":[513,"content-id"],"disabled":[1028],"layout":[1],"type":[1],"value":[8],"checked":[32],"setFocus":[64]},null,{"value":["valueChanged"]}],[33,"ion-segment",{"color":[513],"disabled":[4],"scrollable":[4],"swipeGesture":[4,"swipe-gesture"],"value":[1032],"selectOnFocus":[4,"select-on-focus"],"activated":[32]},[[16,"ionSegmentViewScroll","handleSegmentViewScroll"],[0,"keydown","onKeyDown"]],{"color":["colorChanged"],"swipeGesture":["swipeGestureChanged"],"value":["valueChanged"],"disabled":["disabledChanged"]}]]],["ion-chip",[[33,"ion-chip",{"color":[513],"outline":[4],"disabled":[4]}]]],["ion-input",[[38,"ion-input",{"color":[513],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"autofocus":[4],"clearInput":[4,"clear-input"],"clearInputIcon":[1,"clear-input-icon"],"clearOnEdit":[4,"clear-on-edit"],"counter":[4],"counterFormatter":[16,"counter-formatter"],"debounce":[2],"disabled":[516],"enterkeyhint":[1],"errorText":[1,"error-text"],"fill":[1],"inputmode":[1],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"max":[8],"maxlength":[2],"min":[8],"minlength":[2],"multiple":[4],"name":[1],"pattern":[1],"placeholder":[1],"readonly":[516],"required":[4],"shape":[1],"spellcheck":[4],"step":[1],"type":[1],"value":[1032],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},[[2,"click","onClickCapture"]],{"debounce":["debounceChanged"],"type":["onTypeChange"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-searchbar",[[34,"ion-searchbar",{"color":[513],"animated":[4],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"cancelButtonIcon":[1,"cancel-button-icon"],"cancelButtonText":[1,"cancel-button-text"],"clearIcon":[1,"clear-icon"],"debounce":[2],"disabled":[4],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"searchIcon":[1,"search-icon"],"showCancelButton":[1,"show-cancel-button"],"showClearButton":[1,"show-clear-button"],"spellcheck":[4],"type":[1],"value":[1025],"focused":[32],"noAnimate":[32],"setFocus":[64],"getInputElement":[64]},null,{"lang":["onLangChanged"],"dir":["onDirChanged"],"debounce":["debounceChanged"],"value":["valueChanged"],"showCancelButton":["showCancelButtonChanged"]}]]],["ion-toggle",[[33,"ion-toggle",{"color":[513],"name":[1],"checked":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[1],"enableOnOffLabels":[4,"enable-on-off-labels"],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"activated":[32]},null,{"disabled":["disabledChanged"]}]]],["ion-nav_2",[[1,"ion-nav",{"delegate":[16],"swipeGesture":[1028,"swipe-gesture"],"animated":[4],"animation":[16],"rootParams":[16,"root-params"],"root":[1],"push":[64],"insert":[64],"insertPages":[64],"pop":[64],"popTo":[64],"popToRoot":[64],"removeIndex":[64],"setRoot":[64],"setPages":[64],"setRouteId":[64],"getRouteId":[64],"getActive":[64],"getByIndex":[64],"canGoBack":[64],"getPrevious":[64],"getLength":[64]},null,{"swipeGesture":["swipeGestureChanged"],"root":["rootChanged"]}],[0,"ion-nav-link",{"component":[1],"componentProps":[16,"component-props"],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"]}]]],["ion-tab_2",[[1,"ion-tab",{"active":[1028],"delegate":[16],"tab":[1],"component":[1],"setActive":[64]},null,{"active":["changeActive"]}],[1,"ion-tabs",{"useRouter":[1028,"use-router"],"selectedTab":[32],"select":[64],"getTab":[64],"getSelected":[64],"setRouteId":[64],"getRouteId":[64]}]]],["ion-textarea",[[38,"ion-textarea",{"color":[513],"autocapitalize":[1],"autofocus":[4],"clearOnEdit":[4,"clear-on-edit"],"debounce":[2],"disabled":[4],"fill":[1],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"readonly":[4],"required":[4],"spellcheck":[4],"cols":[514],"rows":[2],"wrap":[1],"autoGrow":[516,"auto-grow"],"value":[1025],"counter":[4],"counterFormatter":[16,"counter-formatter"],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"shape":[1],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},[[2,"click","onClickCapture"]],{"debounce":["debounceChanged"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-backdrop",[[33,"ion-backdrop",{"visible":[4],"tappable":[4],"stopPropagation":[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]]],["ion-loading",[[34,"ion-loading",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"message":[1],"cssClass":[1,"css-class"],"duration":[2],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"spinner":[1025],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-breadcrumb_2",[[33,"ion-breadcrumb",{"collapsed":[4],"last":[4],"showCollapsedIndicator":[4,"show-collapsed-indicator"],"color":[1],"active":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"separator":[4],"target":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"]}],[33,"ion-breadcrumbs",{"color":[513],"maxItems":[2,"max-items"],"itemsBeforeCollapse":[2,"items-before-collapse"],"itemsAfterCollapse":[2,"items-after-collapse"],"collapsed":[32],"activeChanged":[32]},[[0,"collapsedClick","onCollapsedClick"]],{"maxItems":["maxItemsChanged"],"itemsBeforeCollapse":["maxItemsChanged"],"itemsAfterCollapse":["maxItemsChanged"]}]]],["ion-tab-bar_2",[[33,"ion-tab-button",{"disabled":[4],"download":[1],"href":[1],"rel":[1],"layout":[1025],"selected":[1028],"tab":[1],"target":[1]},[[8,"ionTabBarChanged","onTabBarChanged"]]],[33,"ion-tab-bar",{"color":[513],"selectedTab":[1,"selected-tab"],"translucent":[4],"keyboardVisible":[32]},null,{"selectedTab":["selectedTabChanged"]}]]],["ion-datetime-button",[[33,"ion-datetime-button",{"color":[513],"disabled":[516],"datetime":[1],"datetimePresentation":[32],"dateText":[32],"timeText":[32],"datetimeActive":[32],"selectedButton":[32]}]]],["ion-route_4",[[0,"ion-route",{"url":[1],"component":[1],"componentProps":[16,"component-props"],"beforeLeave":[16,"before-leave"],"beforeEnter":[16,"before-enter"]},null,{"url":["onUpdate"],"component":["onUpdate"],"componentProps":["onComponentProps"]}],[0,"ion-route-redirect",{"from":[1],"to":[1]},null,{"from":["propDidChange"],"to":["propDidChange"]}],[0,"ion-router",{"root":[1],"useHash":[4,"use-hash"],"canTransition":[64],"push":[64],"back":[64],"printDebug":[64],"navChanged":[64]},[[8,"popstate","onPopState"],[4,"ionBackButton","onBackButton"]]],[1,"ion-router-link",{"color":[513],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1]}]]],["ion-avatar_3",[[33,"ion-avatar"],[33,"ion-badge",{"color":[513]}],[1,"ion-thumbnail"]]],["ion-col_3",[[1,"ion-col",{"offset":[1],"offsetXs":[1,"offset-xs"],"offsetSm":[1,"offset-sm"],"offsetMd":[1,"offset-md"],"offsetLg":[1,"offset-lg"],"offsetXl":[1,"offset-xl"],"pull":[1],"pullXs":[1,"pull-xs"],"pullSm":[1,"pull-sm"],"pullMd":[1,"pull-md"],"pullLg":[1,"pull-lg"],"pullXl":[1,"pull-xl"],"push":[1],"pushXs":[1,"push-xs"],"pushSm":[1,"push-sm"],"pushMd":[1,"push-md"],"pushLg":[1,"push-lg"],"pushXl":[1,"push-xl"],"size":[1],"sizeXs":[1,"size-xs"],"sizeSm":[1,"size-sm"],"sizeMd":[1,"size-md"],"sizeLg":[1,"size-lg"],"sizeXl":[1,"size-xl"]},[[9,"resize","onResize"]]],[1,"ion-grid",{"fixed":[4]}],[1,"ion-row"]]],["ion-img",[[1,"ion-img",{"alt":[1],"src":[1],"loadSrc":[32],"loadError":[32]},null,{"src":["srcChanged"]}]]],["ion-input-otp",[[38,"ion-input-otp",{"autocapitalize":[1],"color":[513],"disabled":[516],"fill":[1],"inputmode":[1],"length":[2],"pattern":[1],"readonly":[516],"separators":[1],"shape":[1],"size":[1],"type":[1],"value":[1032],"inputValues":[32],"hasFocus":[32],"previousInputValues":[32],"setFocus":[64]},null,{"value":["valueChanged"],"separators":["processSeparators"],"length":["processSeparators"]}]]],["ion-progress-bar",[[33,"ion-progress-bar",{"type":[1],"reversed":[4],"value":[2],"buffer":[2],"color":[513]}]]],["ion-range",[[33,"ion-range",{"color":[513],"debounce":[2],"name":[1],"label":[1],"dualKnobs":[4,"dual-knobs"],"min":[2],"max":[2],"pin":[4],"pinFormatter":[16,"pin-formatter"],"snaps":[4],"step":[2],"ticks":[4],"activeBarStart":[1026,"active-bar-start"],"disabled":[4],"value":[1026],"labelPlacement":[1,"label-placement"],"ratioA":[32],"ratioB":[32],"pressedKnob":[32]},null,{"debounce":["debounceChanged"],"min":["minChanged"],"max":["maxChanged"],"step":["stepChanged"],"activeBarStart":["activeBarStartChanged"],"disabled":["disabledChanged"],"value":["valueChanged"]}]]],["ion-segment-content",[[1,"ion-segment-content"]]],["ion-segment-view",[[33,"ion-segment-view",{"disabled":[4],"isManualScroll":[32],"setContent":[64]},[[1,"scroll","handleScroll"],[1,"touchstart","handleScrollStart"],[1,"touchend","handleTouchEnd"]]]]],["ion-split-pane",[[33,"ion-split-pane",{"contentId":[513,"content-id"],"disabled":[4],"when":[8],"visible":[32],"isVisible":[64]},null,{"visible":["visibleChanged"],"disabled":["updateState"],"when":["updateState"]}]]],["ion-text",[[1,"ion-text",{"color":[513]}]]],["ion-select-modal",[[34,"ion-select-modal",{"header":[1],"multiple":[4],"options":[16]}]]],["ion-datetime_3",[[33,"ion-datetime",{"color":[1],"name":[1],"disabled":[4],"formatOptions":[16,"format-options"],"readonly":[4],"isDateEnabled":[16,"is-date-enabled"],"showAdjacentDays":[4,"show-adjacent-days"],"min":[1025],"max":[1025],"presentation":[1],"cancelText":[1,"cancel-text"],"doneText":[1,"done-text"],"clearText":[1,"clear-text"],"yearValues":[8,"year-values"],"monthValues":[8,"month-values"],"dayValues":[8,"day-values"],"hourValues":[8,"hour-values"],"minuteValues":[8,"minute-values"],"locale":[1],"firstDayOfWeek":[2,"first-day-of-week"],"titleSelectedDatesFormatter":[16,"title-selected-dates-formatter"],"multiple":[4],"highlightedDates":[16,"highlighted-dates"],"value":[1025],"showDefaultTitle":[4,"show-default-title"],"showDefaultButtons":[4,"show-default-buttons"],"showClearButton":[4,"show-clear-button"],"showDefaultTimeLabel":[4,"show-default-time-label"],"hourCycle":[1,"hour-cycle"],"size":[1],"preferWheel":[4,"prefer-wheel"],"showMonthAndYear":[32],"activeParts":[32],"workingParts":[32],"isTimePopoverOpen":[32],"forceRenderDate":[32],"confirm":[64],"reset":[64],"cancel":[64]},null,{"formatOptions":["formatOptionsChanged"],"disabled":["disabledChanged"],"min":["minChanged"],"max":["maxChanged"],"presentation":["presentationChanged"],"yearValues":["yearValuesChanged"],"monthValues":["monthValuesChanged"],"dayValues":["dayValuesChanged"],"hourValues":["hourValuesChanged"],"minuteValues":["minuteValuesChanged"],"value":["valueChanged"]}],[34,"ion-picker-legacy",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"buttons":[16],"columns":[16],"cssClass":[1,"css-class"],"duration":[2],"showBackdrop":[4,"show-backdrop"],"backdropDismiss":[4,"backdrop-dismiss"],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"getColumn":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}],[32,"ion-picker-legacy-column",{"col":[16]},null,{"col":["colChanged"]}]]],["ion-action-sheet",[[34,"ion-action-sheet",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"buttons":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"header":[1],"subHeader":[1,"sub-header"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-alert",[[34,"ion-alert",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"cssClass":[1,"css-class"],"header":[1],"subHeader":[1,"sub-header"],"message":[1],"buttons":[16],"inputs":[1040],"backdropDismiss":[4,"backdrop-dismiss"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},[[4,"keydown","onKeydown"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"],"buttons":["buttonsChanged"],"inputs":["inputsChanged"]}]]],["ion-modal",[[33,"ion-modal",{"hasController":[4,"has-controller"],"overlayIndex":[2,"overlay-index"],"delegate":[16],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"breakpoints":[16],"expandToScroll":[4,"expand-to-scroll"],"initialBreakpoint":[2,"initial-breakpoint"],"backdropBreakpoint":[2,"backdrop-breakpoint"],"handle":[4],"handleBehavior":[1,"handle-behavior"],"component":[1],"componentProps":[16,"component-props"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"animated":[4],"presentingElement":[16,"presenting-element"],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"keepContentsMounted":[4,"keep-contents-mounted"],"focusTrap":[4,"focus-trap"],"canDismiss":[4,"can-dismiss"],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"setCurrentBreakpoint":[64],"getCurrentBreakpoint":[64]},[[9,"resize","onWindowResize"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-picker",[[33,"ion-picker",{"exitInputMode":[64]},[[1,"touchstart","preventTouchStartPropagation"]]]]],["ion-picker-column",[[1,"ion-picker-column",{"disabled":[4],"value":[1032],"color":[513],"numericInput":[4,"numeric-input"],"ariaLabel":[32],"isActive":[32],"scrollActiveItemIntoView":[64],"setValue":[64],"setFocus":[64]},null,{"aria-label":["ariaLabelChanged"],"value":["valueChange"]}]]],["ion-picker-column-option",[[33,"ion-picker-column-option",{"disabled":[4],"value":[8],"color":[513],"ariaLabel":[32]},null,{"aria-label":["onAriaLabelChange"]}]]],["ion-popover",[[33,"ion-popover",{"hasController":[4,"has-controller"],"delegate":[16],"overlayIndex":[2,"overlay-index"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"component":[1],"componentProps":[16,"component-props"],"keyboardClose":[4,"keyboard-close"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"event":[8],"showBackdrop":[4,"show-backdrop"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"triggerAction":[1,"trigger-action"],"trigger":[1],"size":[1],"dismissOnSelect":[4,"dismiss-on-select"],"reference":[1],"side":[1],"alignment":[1025],"arrow":[4],"isOpen":[4,"is-open"],"keyboardEvents":[4,"keyboard-events"],"focusTrap":[4,"focus-trap"],"keepContentsMounted":[4,"keep-contents-mounted"],"presented":[32],"presentFromTrigger":[64],"present":[64],"dismiss":[64],"getParentPopover":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"trigger":["onTriggerChange"],"triggerAction":["onTriggerChange"],"isOpen":["onIsOpenChange"]}]]],["ion-checkbox",[[33,"ion-checkbox",{"color":[513],"name":[1],"checked":[1028],"indeterminate":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"setFocus":[64]}]]],["ion-item_8",[[33,"ion-item-divider",{"color":[513],"sticky":[4]}],[32,"ion-item-group"],[33,"ion-note",{"color":[513]}],[1,"ion-skeleton-text",{"animated":[4]}],[38,"ion-label",{"color":[513],"position":[1],"noAnimate":[32]},null,{"color":["colorChanged"],"position":["positionChanged"]}],[33,"ion-list-header",{"color":[513],"lines":[1]}],[33,"ion-item",{"color":[513],"button":[4],"detail":[4],"detailIcon":[1,"detail-icon"],"disabled":[516],"download":[1],"href":[1],"rel":[1],"lines":[1],"routerAnimation":[16,"router-animation"],"routerDirection":[1,"router-direction"],"target":[1],"type":[1],"multipleInputs":[32],"focusable":[32],"isInteractive":[32]},[[0,"ionColor","labelColorChanged"],[0,"ionStyle","itemStyle"]],{"button":["buttonChanged"]}],[32,"ion-list",{"lines":[1],"inset":[4],"closeSlidingItems":[64]}]]],["ion-app_8",[[0,"ion-app",{"setFocus":[64]}],[36,"ion-footer",{"collapse":[1],"translucent":[4],"keyboardVisible":[32]}],[1,"ion-router-outlet",{"mode":[1025],"delegate":[16],"animated":[4],"animation":[16],"swipeHandler":[16,"swipe-handler"],"commit":[64],"setRouteId":[64],"getRouteId":[64]},null,{"swipeHandler":["swipeHandlerChanged"]}],[1,"ion-content",{"color":[513],"fullscreen":[4],"fixedSlotPlacement":[1,"fixed-slot-placement"],"forceOverscroll":[1028,"force-overscroll"],"scrollX":[4,"scroll-x"],"scrollY":[4,"scroll-y"],"scrollEvents":[4,"scroll-events"],"getScrollElement":[64],"getBackgroundElement":[64],"scrollToTop":[64],"scrollToBottom":[64],"scrollByPoint":[64],"scrollToPoint":[64]},[[9,"resize","onResize"]]],[36,"ion-header",{"collapse":[1],"translucent":[4]}],[33,"ion-title",{"color":[513],"size":[1]},null,{"size":["sizeChanged"]}],[33,"ion-toolbar",{"color":[513]},[[0,"ionStyle","childrenStyle"]]],[38,"ion-buttons",{"collapse":[4]}]]],["ion-select_3",[[33,"ion-select",{"cancelText":[1,"cancel-text"],"color":[513],"compareWith":[1,"compare-with"],"disabled":[4],"fill":[1],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"interface":[1],"interfaceOptions":[8,"interface-options"],"justify":[1],"label":[1],"labelPlacement":[1,"label-placement"],"multiple":[4],"name":[1],"okText":[1,"ok-text"],"placeholder":[1],"selectedText":[1,"selected-text"],"toggleIcon":[1,"toggle-icon"],"expandedIcon":[1,"expanded-icon"],"shape":[1],"value":[1032],"required":[4],"isExpanded":[32],"hasFocus":[32],"open":[64]},null,{"disabled":["styleChanged"],"isExpanded":["styleChanged"],"placeholder":["styleChanged"],"value":["styleChanged"]}],[1,"ion-select-option",{"disabled":[4],"value":[8]}],[34,"ion-select-popover",{"header":[1],"subHeader":[1,"sub-header"],"message":[1],"multiple":[4],"options":[16]}]]],["ion-spinner",[[1,"ion-spinner",{"color":[513],"duration":[2],"name":[1],"paused":[4]}]]],["ion-radio_2",[[33,"ion-radio",{"color":[513],"name":[1],"disabled":[4],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"checked":[32],"buttonTabindex":[32],"setFocus":[64],"setButtonTabindex":[64]},null,{"value":["valueChanged"]}],[36,"ion-radio-group",{"allowEmptySelection":[4,"allow-empty-selection"],"compareWith":[1,"compare-with"],"name":[1],"value":[1032],"helperText":[1,"helper-text"],"errorText":[1,"error-text"],"setFocus":[64]},[[4,"keydown","onKeydown"]],{"value":["valueChanged"]}]]],["ion-ripple-effect",[[1,"ion-ripple-effect",{"type":[1],"addRipple":[64]}]]],["ion-button_2",[[33,"ion-button",{"color":[513],"buttonType":[1025,"button-type"],"disabled":[516],"expand":[513],"fill":[1537],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"download":[1],"href":[1],"rel":[1],"shape":[513],"size":[513],"strong":[4],"target":[1],"type":[1],"form":[1],"isCircle":[32]},null,{"disabled":["disabledChanged"],"aria-checked":["onAriaChanged"],"aria-label":["onAriaChanged"]}],[1,"ion-icon",{"mode":[1025],"color":[1],"ios":[1],"md":[1],"flipRtl":[4,"flip-rtl"],"name":[513],"src":[1],"icon":[8],"size":[1],"lazy":[4],"sanitize":[4],"svgContent":[32],"isVisible":[32]},null,{"name":["loadIcon"],"src":["loadIcon"],"icon":["loadIcon"],"ios":["loadIcon"],"md":["loadIcon"]}]]]]'),t)});return function(t,r){return e.apply(this,arguments)}}();!function(){if(typeof window<"u"&&void 0!==window.Reflect&&void 0!==window.customElements){var e=HTMLElement;window.HTMLElement=function(){return Reflect.construct(e,[],this.constructor)},HTMLElement.prototype=e.prototype,HTMLElement.prototype.constructor=HTMLElement,Object.setPrototypeOf(HTMLElement,e)}}();const Ce=["*"];let $O=(()=>{class e extends Lh{constructor(t,r){super(t,r)}writeValue(t){this.elementRef.nativeElement.checked=this.lastValue=t,Tl(this.elementRef)}_handleIonChange(t){this.handleValueChange(t,t.checked)}static \u0275fac=function(r){return new(r||e)(g(Zt),g(W))};static \u0275dir=Fe({type:e,selectors:[["ion-checkbox"],["ion-toggle"]],hostBindings:function(r,o){1&r&&sn("ionChange",function(s){return o._handleIonChange(s.target)})},features:[Ft([{provide:Or,useExisting:e,multi:!0}]),dt]})}return e})(),VO=(()=>{class e extends Lh{constructor(t,r){super(t,r)}_handleInputEvent(t){this.handleValueChange(t,t.value)}static \u0275fac=function(r){return new(r||e)(g(Zt),g(W))};static \u0275dir=Fe({type:e,selectors:[["ion-input",3,"type","number"],["ion-input-otp","type","text"],["ion-textarea"],["ion-searchbar"]],hostBindings:function(r,o){1&r&&sn("ionInput",function(s){return o._handleInputEvent(s.target)})},features:[Ft([{provide:Or,useExisting:e,multi:!0}]),dt]})}return e})();const WG=(e,n)=>{const t=e.prototype;n.forEach(r=>{Object.defineProperty(t,r,{get(){return this.el[r]},set(o){this.z.runOutsideAngular(()=>this.el[r]=o)},configurable:!0})})},qG=(e,n)=>{const t=e.prototype;n.forEach(r=>{t[r]=function(){const o=arguments;return this.z.runOutsideAngular(()=>this.el[r].apply(this.el,o))}})},It=(e,n,t)=>{t.forEach(r=>e[r]=kh(n,r))};function _e(e){return function(t){const{defineCustomElementFn:r,inputs:o,methods:i}=e;return void 0!==r&&r(),o&&WG(t,o),i&&qG(t,i),t}}let BO=(()=>{let e=class xC{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||xC)(g(ae),g(W),g(j))};static \u0275cmp=le({type:xC,selectors:[["ion-app"]],ngContentSelectors:Ce,decls:1,vars:0,template:function(r,o){1&r&&(me(),pe(0))},encapsulation:2,changeDetection:0})};return e=ve([_e({methods:["setFocus"]})],e),e})(),jO=(()=>{let e=class FC{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,It(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||FC)(g(ae),g(W),g(j))};static \u0275cmp=le({type:FC,selectors:[["ion-button"]],inputs:{buttonType:"buttonType",color:"color",disabled:"disabled",download:"download",expand:"expand",fill:"fill",form:"form",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",size:"size",strong:"strong",target:"target",type:"type"},ngContentSelectors:Ce,decls:1,vars:0,template:function(r,o){1&r&&(me(),pe(0))},encapsulation:2,changeDetection:0})};return e=ve([_e({inputs:["buttonType","color","disabled","download","expand","fill","form","href","mode","rel","routerAnimation","routerDirection","shape","size","strong","target","type"]})],e),e})(),oC=(()=>{let e=class $C{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||$C)(g(ae),g(W),g(j))};static \u0275cmp=le({type:$C,selectors:[["ion-card"]],inputs:{button:"button",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},ngContentSelectors:Ce,decls:1,vars:0,template:function(r,o){1&r&&(me(),pe(0))},encapsulation:2,changeDetection:0})};return e=ve([_e({inputs:["button","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","target","type"]})],e),e})(),iC=(()=>{let e=class VC{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||VC)(g(ae),g(W),g(j))};static \u0275cmp=le({type:VC,selectors:[["ion-card-content"]],inputs:{mode:"mode"},ngContentSelectors:Ce,decls:1,vars:0,template:function(r,o){1&r&&(me(),pe(0))},encapsulation:2,changeDetection:0})};return e=ve([_e({inputs:["mode"]})],e),e})(),sC=(()=>{let e=class BC{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||BC)(g(ae),g(W),g(j))};static \u0275cmp=le({type:BC,selectors:[["ion-card-header"]],inputs:{color:"color",mode:"mode",translucent:"translucent"},ngContentSelectors:Ce,decls:1,vars:0,template:function(r,o){1&r&&(me(),pe(0))},encapsulation:2,changeDetection:0})};return e=ve([_e({inputs:["color","mode","translucent"]})],e),e})(),aC=(()=>{let e=class UC{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||UC)(g(ae),g(W),g(j))};static \u0275cmp=le({type:UC,selectors:[["ion-card-title"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:Ce,decls:1,vars:0,template:function(r,o){1&r&&(me(),pe(0))},encapsulation:2,changeDetection:0})};return e=ve([_e({inputs:["color","mode"]})],e),e})(),UO=(()=>{let e=class WC{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,It(this,this.el,["ionScrollStart","ionScroll","ionScrollEnd"])}static \u0275fac=function(r){return new(r||WC)(g(ae),g(W),g(j))};static \u0275cmp=le({type:WC,selectors:[["ion-content"]],inputs:{color:"color",fixedSlotPlacement:"fixedSlotPlacement",forceOverscroll:"forceOverscroll",fullscreen:"fullscreen",scrollEvents:"scrollEvents",scrollX:"scrollX",scrollY:"scrollY"},ngContentSelectors:Ce,decls:1,vars:0,template:function(r,o){1&r&&(me(),pe(0))},encapsulation:2,changeDetection:0})};return e=ve([_e({inputs:["color","fixedSlotPlacement","forceOverscroll","fullscreen","scrollEvents","scrollX","scrollY"],methods:["getScrollElement","scrollToTop","scrollToBottom","scrollByPoint","scrollToPoint"]})],e),e})(),HO=(()=>{let e=class l_{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||l_)(g(ae),g(W),g(j))};static \u0275cmp=le({type:l_,selectors:[["ion-item"]],inputs:{button:"button",color:"color",detail:"detail",detailIcon:"detailIcon",disabled:"disabled",download:"download",href:"href",lines:"lines",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},ngContentSelectors:Ce,decls:1,vars:0,template:function(r,o){1&r&&(me(),pe(0))},encapsulation:2,changeDetection:0})};return e=ve([_e({inputs:["button","color","detail","detailIcon","disabled","download","href","lines","mode","rel","routerAnimation","routerDirection","target","type"]})],e),e})(),zO=(()=>{let e=class W_{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||W_)(g(ae),g(W),g(j))};static \u0275cmp=le({type:W_,selectors:[["ion-spinner"]],inputs:{color:"color",duration:"duration",name:"name",paused:"paused"},ngContentSelectors:Ce,decls:1,vars:0,template:function(r,o){1&r&&(me(),pe(0))},encapsulation:2,changeDetection:0})};return e=ve([_e({inputs:["color","duration","name","paused"]})],e),e})(),WO=(()=>{let e=class Q_{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,It(this,this.el,["ionChange","ionInput","ionBlur","ionFocus"])}static \u0275fac=function(r){return new(r||Q_)(g(ae),g(W),g(j))};static \u0275cmp=le({type:Q_,selectors:[["ion-textarea"]],inputs:{autoGrow:"autoGrow",autocapitalize:"autocapitalize",autofocus:"autofocus",clearOnEdit:"clearOnEdit",color:"color",cols:"cols",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",readonly:"readonly",required:"required",rows:"rows",shape:"shape",spellcheck:"spellcheck",value:"value",wrap:"wrap"},ngContentSelectors:Ce,decls:1,vars:0,template:function(r,o){1&r&&(me(),pe(0))},encapsulation:2,changeDetection:0})};return e=ve([_e({inputs:["autoGrow","autocapitalize","autofocus","clearOnEdit","color","cols","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","maxlength","minlength","mode","name","placeholder","readonly","required","rows","shape","spellcheck","value","wrap"],methods:["setFocus","getInputElement"]})],e),e})(),qO=(()=>{let e=class nD{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,It(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||nD)(g(ae),g(W),g(j))};static \u0275cmp=le({type:nD,selectors:[["ion-toggle"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",enableOnOffLabels:"enableOnOffLabels",errorText:"errorText",helperText:"helperText",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},ngContentSelectors:Ce,decls:1,vars:0,template:function(r,o){1&r&&(me(),pe(0))},encapsulation:2,changeDetection:0})};return e=ve([_e({inputs:["alignment","checked","color","disabled","enableOnOffLabels","errorText","helperText","justify","labelPlacement","mode","name","required","value"]})],e),e})();const _5={provide:Bn,useExisting:kt(()=>XO),multi:!0};let XO=(()=>{class e extends Xy{static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ut(e)))(o||e)}})();static \u0275dir=Fe({type:e,selectors:[["ion-input","type","number","max","","formControlName",""],["ion-input","type","number","max","","formControl",""],["ion-input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(r,o){2&r&&jr("max",o._enabled?o.max:null)},features:[Ft([_5]),dt]})}return e})();const D5={provide:Bn,useExisting:kt(()=>YO),multi:!0};let YO=(()=>{class e extends Yy{static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ut(e)))(o||e)}})();static \u0275dir=Fe({type:e,selectors:[["ion-input","type","number","min","","formControlName",""],["ion-input","type","number","min","","formControl",""],["ion-input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(r,o){2&r&&jr("min",o._enabled?o.min:null)},features:[Ft([D5]),dt]})}return e})(),b5=(()=>{class e extends kO{angularDelegate=U(Nu);injector=U(Zt);environmentInjector=U(Pn);constructor(){super(LO.m)}create(t){return super.create({...t,delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")})}static \u0275fac=function(r){return new(r||e)};static \u0275prov=Me({token:e,factory:e.\u0275fac})}return e})();class E5 extends kO{angularDelegate=U(Nu);injector=U(Zt);environmentInjector=U(Pn);constructor(){super(LO.c)}create(n){return super.create({...n,delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")})}}const w5=(e,n,t)=>()=>{const r=n.defaultView;if(r&&typeof window<"u"){(0,PG.s)({...e,_zoneGate:i=>t.run(i)});const o="__zone_symbol__addEventListener"in n.body?"__zone_symbol__addEventListener":"addEventListener";return kG(r,{exclude:["ion-tabs"],syncQueue:!0,raf:PO,jmp:i=>t.runOutsideAngular(i),ael(i,s,a,l){i[o](s,a,l)},rel(i,s,a,l){i.removeEventListener(s,a,l)}})}};let Vh=(()=>{class e{static forRoot(t={}){return{ngModule:e,providers:[{provide:Jy,useValue:t},{provide:oM,useFactory:w5,multi:!0,deps:[Jy,Hr,j]},Nu,{provide:NO,useFactory:EG,deps:[xr]}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=is({type:e});static \u0275inj=Zr({providers:[b5,E5],imports:[Nv]})}return e})(),I5=(()=>{class e{constructor(){this.title="driver-evaluation-app"}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275cmp=le({type:e,selectors:[["app-root"]],standalone:!0,features:[Xc],decls:3,vars:0,template:function(r,o){1&r&&(rn(0,"ion-app")(1,"ion-content"),Ms(2,"router-outlet"),ln()())},dependencies:[gy,Vh,BO,UO],encapsulation:2})}}return e})();class Bh{}class jh{}class qr{constructor(n){this.normalizedNames=new Map,this.lazyUpdate=null,n?"string"==typeof n?this.lazyInit=()=>{this.headers=new Map,n.split("\n").forEach(t=>{const r=t.indexOf(":");if(r>0){const o=t.slice(0,r),i=o.toLowerCase(),s=t.slice(r+1).trim();this.maybeSetNormalizedName(o,i),this.headers.has(i)?this.headers.get(i).push(s):this.headers.set(i,[s])}})}:typeof Headers<"u"&&n instanceof Headers?(this.headers=new Map,n.forEach((t,r)=>{this.setHeaderEntries(r,t)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(n).forEach(([t,r])=>{this.setHeaderEntries(t,r)})}:this.headers=new Map}has(n){return this.init(),this.headers.has(n.toLowerCase())}get(n){this.init();const t=this.headers.get(n.toLowerCase());return t&&t.length>0?t[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(n){return this.init(),this.headers.get(n.toLowerCase())||null}append(n,t){return this.clone({name:n,value:t,op:"a"})}set(n,t){return this.clone({name:n,value:t,op:"s"})}delete(n,t){return this.clone({name:n,value:t,op:"d"})}maybeSetNormalizedName(n,t){this.normalizedNames.has(t)||this.normalizedNames.set(t,n)}init(){this.lazyInit&&(this.lazyInit instanceof qr?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(n=>this.applyUpdate(n)),this.lazyUpdate=null))}copyFrom(n){n.init(),Array.from(n.headers.keys()).forEach(t=>{this.headers.set(t,n.headers.get(t)),this.normalizedNames.set(t,n.normalizedNames.get(t))})}clone(n){const t=new qr;return t.lazyInit=this.lazyInit&&this.lazyInit instanceof qr?this.lazyInit:this,t.lazyUpdate=(this.lazyUpdate||[]).concat([n]),t}applyUpdate(n){const t=n.name.toLowerCase();switch(n.op){case"a":case"s":let r=n.value;if("string"==typeof r&&(r=[r]),0===r.length)return;this.maybeSetNormalizedName(n.name,t);const o=("a"===n.op?this.headers.get(t):void 0)||[];o.push(...r),this.headers.set(t,o);break;case"d":const i=n.value;if(i){let s=this.headers.get(t);if(!s)return;s=s.filter(a=>-1===i.indexOf(a)),0===s.length?(this.headers.delete(t),this.normalizedNames.delete(t)):this.headers.set(t,s)}else this.headers.delete(t),this.normalizedNames.delete(t)}}setHeaderEntries(n,t){const r=(Array.isArray(t)?t:[t]).map(i=>i.toString()),o=n.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(n,o)}forEach(n){this.init(),Array.from(this.normalizedNames.keys()).forEach(t=>n(this.normalizedNames.get(t),this.headers.get(t)))}}class S5{encodeKey(n){return ZO(n)}encodeValue(n){return ZO(n)}decodeKey(n){return decodeURIComponent(n)}decodeValue(n){return decodeURIComponent(n)}}const T5=/%(\d[a-f0-9])/gi,A5={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function ZO(e){return encodeURIComponent(e).replace(T5,(n,t)=>A5[t]??n)}function Uh(e){return`${e}`}class Ui{constructor(n={}){if(this.updates=null,this.cloneFrom=null,this.encoder=n.encoder||new S5,n.fromString){if(n.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=function M5(e,n){const t=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{const i=o.indexOf("="),[s,a]=-1==i?[n.decodeKey(o),""]:[n.decodeKey(o.slice(0,i)),n.decodeValue(o.slice(i+1))],l=t.get(s)||[];l.push(a),t.set(s,l)}),t}(n.fromString,this.encoder)}else n.fromObject?(this.map=new Map,Object.keys(n.fromObject).forEach(t=>{const r=n.fromObject[t],o=Array.isArray(r)?r.map(Uh):[Uh(r)];this.map.set(t,o)})):this.map=null}has(n){return this.init(),this.map.has(n)}get(n){this.init();const t=this.map.get(n);return t?t[0]:null}getAll(n){return this.init(),this.map.get(n)||null}keys(){return this.init(),Array.from(this.map.keys())}append(n,t){return this.clone({param:n,value:t,op:"a"})}appendAll(n){const t=[];return Object.keys(n).forEach(r=>{const o=n[r];Array.isArray(o)?o.forEach(i=>{t.push({param:r,value:i,op:"a"})}):t.push({param:r,value:o,op:"a"})}),this.clone(t)}set(n,t){return this.clone({param:n,value:t,op:"s"})}delete(n,t){return this.clone({param:n,value:t,op:"d"})}toString(){return this.init(),this.keys().map(n=>{const t=this.encoder.encodeKey(n);return this.map.get(n).map(r=>t+"="+this.encoder.encodeValue(r)).join("&")}).filter(n=>""!==n).join("&")}clone(n){const t=new Ui({encoder:this.encoder});return t.cloneFrom=this.cloneFrom||this,t.updates=(this.updates||[]).concat(n),t}init(){null===this.map&&(this.map=new Map),null!==this.cloneFrom&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(n=>this.map.set(n,this.cloneFrom.map.get(n))),this.updates.forEach(n=>{switch(n.op){case"a":case"s":const t=("a"===n.op?this.map.get(n.param):void 0)||[];t.push(Uh(n.value)),this.map.set(n.param,t);break;case"d":if(void 0===n.value){this.map.delete(n.param);break}{let r=this.map.get(n.param)||[];const o=r.indexOf(Uh(n.value));-1!==o&&r.splice(o,1),r.length>0?this.map.set(n.param,r):this.map.delete(n.param)}}}),this.cloneFrom=this.updates=null)}}class x5{constructor(){this.map=new Map}set(n,t){return this.map.set(n,t),this}get(n){return this.map.has(n)||this.map.set(n,n.defaultValue()),this.map.get(n)}delete(n){return this.map.delete(n),this}has(n){return this.map.has(n)}keys(){return this.map.keys()}}function KO(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function QO(e){return typeof Blob<"u"&&e instanceof Blob}function JO(e){return typeof FormData<"u"&&e instanceof FormData}class Pu{constructor(n,t,r,o){let i;if(this.url=t,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=n.toUpperCase(),function O5(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}(this.method)||o?(this.body=void 0!==r?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new qr,this.context??=new x5,this.params){const s=this.params.toString();if(0===s.length)this.urlWithParams=t;else{const a=t.indexOf("?");this.urlWithParams=t+(-1===a?"?":a<t.length-1?"&":"")+s}}else this.params=new Ui,this.urlWithParams=t}serializeBody(){return null===this.body?null:"string"==typeof this.body||KO(this.body)||QO(this.body)||JO(this.body)||function R5(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}(this.body)?this.body:this.body instanceof Ui?this.body.toString():"object"==typeof this.body||"boolean"==typeof this.body||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return null===this.body||JO(this.body)?null:QO(this.body)?this.body.type||null:KO(this.body)?null:"string"==typeof this.body?"text/plain":this.body instanceof Ui?"application/x-www-form-urlencoded;charset=UTF-8":"object"==typeof this.body||"number"==typeof this.body||"boolean"==typeof this.body?"application/json":null}clone(n={}){const t=n.method||this.method,r=n.url||this.url,o=n.responseType||this.responseType,i=n.transferCache??this.transferCache,s=void 0!==n.body?n.body:this.body,a=n.withCredentials??this.withCredentials,l=n.reportProgress??this.reportProgress;let c=n.headers||this.headers,d=n.params||this.params;const h=n.context??this.context;return void 0!==n.setHeaders&&(c=Object.keys(n.setHeaders).reduce((v,y)=>v.set(y,n.setHeaders[y]),c)),n.setParams&&(d=Object.keys(n.setParams).reduce((v,y)=>v.set(y,n.setParams[y]),d)),new Pu(t,r,s,{params:d,headers:c,context:h,reportProgress:l,responseType:o,withCredentials:a,transferCache:i})}}var Hi=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Hi||{});class cC{constructor(n,t=200,r="OK"){this.headers=n.headers||new qr,this.status=void 0!==n.status?n.status:t,this.statusText=n.statusText||r,this.url=n.url||null,this.ok=this.status>=200&&this.status<300}}class Hh extends cC{constructor(n={}){super(n),this.type=Hi.ResponseHeader}clone(n={}){return new Hh({headers:n.headers||this.headers,status:void 0!==n.status?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}}class Gs extends cC{constructor(n={}){super(n),this.type=Hi.Response,this.body=void 0!==n.body?n.body:null}clone(n={}){return new Gs({body:void 0!==n.body?n.body:this.body,headers:n.headers||this.headers,status:void 0!==n.status?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}}class Al extends cC{constructor(n){super(n,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.message=this.status>=200&&this.status<300?`Http failure during parsing for ${n.url||"(unknown url)"}`:`Http failure response for ${n.url||"(unknown url)"}: ${n.status} ${n.statusText}`,this.error=n.error||null}}function uC(e,n){return{body:n,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}let zh=(()=>{class e{constructor(t){this.handler=t}request(t,r,o={}){let i;if(t instanceof Pu)i=t;else{let l,c;l=o.headers instanceof qr?o.headers:new qr(o.headers),o.params&&(c=o.params instanceof Ui?o.params:new Ui({fromObject:o.params})),i=new Pu(t,r,void 0!==o.body?o.body:null,{headers:l,context:o.context,params:c,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache})}const s=je(i).pipe(ml(l=>this.handler.handle(l)));if(t instanceof Pu||"events"===o.observe)return s;const a=s.pipe(Uo(l=>l instanceof Gs));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(ct(l=>{if(null!==l.body&&!(l.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return l.body}));case"blob":return a.pipe(ct(l=>{if(null!==l.body&&!(l.body instanceof Blob))throw new Error("Response is not a Blob.");return l.body}));case"text":return a.pipe(ct(l=>{if(null!==l.body&&"string"!=typeof l.body)throw new Error("Response is not a string.");return l.body}));default:return a.pipe(ct(l=>l.body))}case"response":return a;default:throw new Error(`Unreachable: unhandled observe type ${o.observe}}`)}}delete(t,r={}){return this.request("DELETE",t,r)}get(t,r={}){return this.request("GET",t,r)}head(t,r={}){return this.request("HEAD",t,r)}jsonp(t,r){return this.request("JSONP",t,{params:(new Ui).append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(t,r={}){return this.request("OPTIONS",t,r)}patch(t,r,o={}){return this.request("PATCH",t,uC(o,r))}post(t,r,o={}){return this.request("POST",t,uC(o,r))}put(t,r,o={}){return this.request("PUT",t,uC(o,r))}static{this.\u0275fac=function(r){return new(r||e)(Pe(Bh))}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac})}}return e})();const k5=/^\)\]\}',?\n/;function tR(e){if(e.url)return e.url;const n="X-Request-URL".toLocaleLowerCase();return e.headers.get(n)}let dC=(()=>{class e{constructor(){this.fetchImpl=U(L5,{optional:!0})?.fetch??((...t)=>globalThis.fetch(...t)),this.ngZone=U(j)}handle(t){return new Yt(r=>{const o=new AbortController;return this.doRequest(t,o.signal,r).then(fC,i=>r.error(new Al({error:i}))),()=>o.abort()})}doRequest(t,r,o){var i=this;return(0,Hs.A)(function*(){const s=i.createRequestInit(t);let a;try{const C=i.ngZone.runOutsideAngular(()=>i.fetchImpl(t.urlWithParams,{signal:r,...s}));(function $5(e){e.then(fC,fC)})(C),o.next({type:Hi.Sent}),a=yield C}catch(C){return void o.error(new Al({error:C,status:C.status??0,statusText:C.statusText,url:t.urlWithParams,headers:C.headers}))}const l=new qr(a.headers),c=a.statusText,d=tR(a)??t.urlWithParams;let h=a.status,v=null;if(t.reportProgress&&o.next(new Hh({headers:l,status:h,statusText:c,url:d})),a.body){const C=a.headers.get("content-length"),b=[],M=a.body.getReader();let A,Ee,O=0;const Ge=typeof Zone<"u"&&Zone.current;yield i.ngZone.runOutsideAngular((0,Hs.A)(function*(){for(;;){const{done:Tt,value:vn}=yield M.read();if(Tt)break;if(b.push(vn),O+=vn.length,t.reportProgress){Ee="text"===t.responseType?(Ee??"")+(A??=new TextDecoder).decode(vn,{stream:!0}):void 0;const fi=()=>o.next({type:Hi.DownloadProgress,total:C?+C:void 0,loaded:O,partialText:Ee});Ge?Ge.run(fi):fi()}}}));const vt=i.concatChunks(b,O);try{const Tt=a.headers.get("Content-Type")??"";v=i.parseBody(t,vt,Tt)}catch(Tt){return void o.error(new Al({error:Tt,headers:new qr(a.headers),status:a.status,statusText:a.statusText,url:tR(a)??t.urlWithParams}))}}0===h&&(h=v?200:0),h>=200&&h<300?(o.next(new Gs({body:v,headers:l,status:h,statusText:c,url:d})),o.complete()):o.error(new Al({error:v,headers:l,status:h,statusText:c,url:d}))})()}parseBody(t,r,o){switch(t.responseType){case"json":const i=(new TextDecoder).decode(r).replace(k5,"");return""===i?null:JSON.parse(i);case"text":return(new TextDecoder).decode(r);case"blob":return new Blob([r],{type:o});case"arraybuffer":return r.buffer}}createRequestInit(t){const r={},o=t.withCredentials?"include":void 0;if(t.headers.forEach((i,s)=>r[i]=s.join(",")),t.headers.has("Accept")||(r.Accept="application/json, text/plain, */*"),!t.headers.has("Content-Type")){const i=t.detectContentTypeHeader();null!==i&&(r["Content-Type"]=i)}return{body:t.serializeBody(),method:t.method,headers:r,credentials:o}}concatChunks(t,r){const o=new Uint8Array(r);let i=0;for(const s of t)o.set(s,i),i+=s.length;return o}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac})}}return e})();class L5{}function fC(){}function nR(e,n){return n(e)}const ku=new ce(""),rR=new ce(""),oR=new ce("",{providedIn:"root",factory:()=>!0});let iR=(()=>{class e extends Bh{constructor(t,r){super(),this.backend=t,this.injector=r,this.chain=null,this.pendingTasks=U(bi),this.contributeToStability=U(oR)}handle(t){if(null===this.chain){const r=Array.from(new Set([...this.injector.get(ku),...this.injector.get(rR,[])]));this.chain=r.reduceRight((o,i)=>function B5(e,n,t){return(r,o)=>Ko(t,()=>n(r,i=>e(i,o)))}(o,i,this.injector),nR)}if(this.contributeToStability){const r=this.pendingTasks.add();return this.chain(t,o=>this.backend.handle(o)).pipe(rh(()=>this.pendingTasks.remove(r)))}return this.chain(t,r=>this.backend.handle(r))}static{this.\u0275fac=function(r){return new(r||e)(Pe(jh),Pe(Pn))}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac})}}return e})();const q5=/^\)\]\}',?\n/;let aR=(()=>{class e{constructor(t){this.xhrFactory=t}handle(t){if("JSONP"===t.method)throw new z(-2800,!1);const r=this.xhrFactory;return(r.\u0275loadImpl?zn(r.\u0275loadImpl()):je(null)).pipe(mr(()=>new Yt(i=>{const s=r.build();if(s.open(t.method,t.urlWithParams),t.withCredentials&&(s.withCredentials=!0),t.headers.forEach((b,M)=>s.setRequestHeader(b,M.join(","))),t.headers.has("Accept")||s.setRequestHeader("Accept","application/json, text/plain, */*"),!t.headers.has("Content-Type")){const b=t.detectContentTypeHeader();null!==b&&s.setRequestHeader("Content-Type",b)}if(t.responseType){const b=t.responseType.toLowerCase();s.responseType="json"!==b?b:"text"}const a=t.serializeBody();let l=null;const c=()=>{if(null!==l)return l;const b=s.statusText||"OK",M=new qr(s.getAllResponseHeaders()),O=function X5(e){return"responseURL"in e&&e.responseURL?e.responseURL:/^X-Request-URL:/m.test(e.getAllResponseHeaders())?e.getResponseHeader("X-Request-URL"):null}(s)||t.url;return l=new Hh({headers:M,status:s.status,statusText:b,url:O}),l},d=()=>{let{headers:b,status:M,statusText:O,url:A}=c(),Ee=null;204!==M&&(Ee=typeof s.response>"u"?s.responseText:s.response),0===M&&(M=Ee?200:0);let Ge=M>=200&&M<300;if("json"===t.responseType&&"string"==typeof Ee){const vt=Ee;Ee=Ee.replace(q5,"");try{Ee=""!==Ee?JSON.parse(Ee):null}catch(Tt){Ee=vt,Ge&&(Ge=!1,Ee={error:Tt,text:Ee})}}Ge?(i.next(new Gs({body:Ee,headers:b,status:M,statusText:O,url:A||void 0})),i.complete()):i.error(new Al({error:Ee,headers:b,status:M,statusText:O,url:A||void 0}))},h=b=>{const{url:M}=c(),O=new Al({error:b,status:s.status||0,statusText:s.statusText||"Unknown Error",url:M||void 0});i.error(O)};let v=!1;const y=b=>{v||(i.next(c()),v=!0);let M={type:Hi.DownloadProgress,loaded:b.loaded};b.lengthComputable&&(M.total=b.total),"text"===t.responseType&&s.responseText&&(M.partialText=s.responseText),i.next(M)},C=b=>{let M={type:Hi.UploadProgress,loaded:b.loaded};b.lengthComputable&&(M.total=b.total),i.next(M)};return s.addEventListener("load",d),s.addEventListener("error",h),s.addEventListener("timeout",h),s.addEventListener("abort",h),t.reportProgress&&(s.addEventListener("progress",y),null!==a&&s.upload&&s.upload.addEventListener("progress",C)),s.send(a),i.next({type:Hi.Sent}),()=>{s.removeEventListener("error",h),s.removeEventListener("abort",h),s.removeEventListener("load",d),s.removeEventListener("timeout",h),t.reportProgress&&(s.removeEventListener("progress",y),null!==a&&s.upload&&s.upload.removeEventListener("progress",C)),s.readyState!==s.DONE&&s.abort()}})))}static{this.\u0275fac=function(r){return new(r||e)(Pe(CT))}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac})}}return e})();const hC=new ce(""),lR=new ce("",{providedIn:"root",factory:()=>"XSRF-TOKEN"}),cR=new ce("",{providedIn:"root",factory:()=>"X-XSRF-TOKEN"});class uR{}let K5=(()=>{class e{constructor(t,r,o){this.doc=t,this.platform=r,this.cookieName=o,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if("server"===this.platform)return null;const t=this.doc.cookie||"";return t!==this.lastCookieString&&(this.parseCount++,this.lastToken=cT(t,this.cookieName),this.lastCookieString=t),this.lastToken}static{this.\u0275fac=function(r){return new(r||e)(Pe(Hr),Pe(wi),Pe(lR))}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac})}}return e})();function Q5(e,n){const t=e.url.toLowerCase();if(!U(hC)||"GET"===e.method||"HEAD"===e.method||t.startsWith("http://")||t.startsWith("https://"))return n(e);const r=U(uR).getToken(),o=U(cR);return null!=r&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),n(e)}const CR={now:()=>(CR.delegate||Date).now(),delegate:void 0};class lq extends jt{constructor(n=1/0,t=1/0,r=CR){super(),this._bufferSize=n,this._windowTime=t,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=t===1/0,this._bufferSize=Math.max(1,n),this._windowTime=Math.max(1,t)}next(n){const{isStopped:t,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;t||(r.push(n),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(n)}_subscribe(n){this._throwIfClosed(),this._trimBuffer();const t=this._innerSubscribe(n),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!n.closed;s+=r?1:2)n.next(i[s]);return this._checkFinalizedStatuses(n),t}_trimBuffer(){const{_bufferSize:n,_timestampProvider:t,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*n;if(n<1/0&&i<r.length&&r.splice(0,r.length-i),!o){const s=t.now();let a=0;for(let l=1;l<r.length&&r[l]<=s;l+=2)a=l;a&&r.splice(0,a+1)}}}function pC(e,n,...t){if(!0===n)return void e();if(!1===n)return;const r=new Rr({next:()=>{r.unsubscribe(),e()}});return Gr(n(...t)).subscribe(r)}function _R(e,n,t){let r,o=!1;return e&&"object"==typeof e?({bufferSize:r=1/0,windowTime:n=1/0,refCount:o=!1,scheduler:t}=e):r=e??1/0,function cq(e={}){const{connector:n=()=>new jt,resetOnError:t=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,l,c=0,d=!1,h=!1;const v=()=>{a?.unsubscribe(),a=void 0},y=()=>{v(),s=l=void 0,d=h=!1},C=()=>{const b=s;y(),b?.unsubscribe()};return hn((b,M)=>{c++,!h&&!d&&v();const O=l=l??n();M.add(()=>{c--,0===c&&!h&&!d&&(a=pC(C,o))}),O.subscribe(M),!s&&c>0&&(s=new Rr({next:A=>O.next(A),error:A=>{h=!0,v(),a=pC(y,t,A),O.error(A)},complete:()=>{d=!0,v(),a=pC(y,r),O.complete()}}),Gr(b).subscribe(s))})(i)}}({connector:()=>new lq(r,n,t),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}class Fu{}let DR=(()=>{class e extends Fu{getTranslation(t){return je({})}static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ut(e)))(o||e)}})();static \u0275prov=Me({token:e,factory:e.\u0275fac})}return e})();class gC{}let bR=(()=>{class e{handle(t){return t.key}static \u0275fac=function(r){return new(r||e)};static \u0275prov=Me({token:e,factory:e.\u0275fac})}return e})();function Wh(e,n){if(e===n)return!0;if(null===e||null===n)return!1;if(e!=e&&n!=n)return!0;let o,i,s,t=typeof e;if(t==typeof n&&"object"==t){if(!Array.isArray(e)){if(Array.isArray(n))return!1;for(i in s=Object.create(null),e){if(!Wh(e[i],n[i]))return!1;s[i]=!0}for(i in n)if(!(i in s)&&typeof n[i]<"u")return!1;return!0}if(!Array.isArray(n))return!1;if((o=e.length)==n.length){for(i=0;i<o;i++)if(!Wh(e[i],n[i]))return!1;return!0}}return!1}function Gi(e){return typeof e<"u"&&null!==e}function mC(e){return e&&"object"==typeof e&&!Array.isArray(e)}function ER(e,n){let t=Object.assign({},e);return mC(e)&&mC(n)&&Object.keys(n).forEach(r=>{mC(n[r])?r in e?t[r]=ER(e[r],n[r]):Object.assign(t,{[r]:n[r]}):Object.assign(t,{[r]:n[r]})}),t}class qh{}let wR=(()=>{class e extends qh{templateMatcher=/{{\s?([^{}\s]*)\s?}}/g;interpolate(t,r){let o;return o="string"==typeof t?this.interpolateString(t,r):"function"==typeof t?this.interpolateFunction(t,r):t,o}getValue(t,r){let o="string"==typeof r?r.split("."):[r];r="";do{r+=o.shift(),!Gi(t)||!Gi(t[r])||"object"!=typeof t[r]&&o.length?o.length?r+=".":t=void 0:(t=t[r],r="")}while(o.length);return t}interpolateFunction(t,r){return t(r)}interpolateString(t,r){return r?t.replace(this.templateMatcher,(o,i)=>{let s=this.getValue(r,i);return Gi(s)?s:o}):t}static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ut(e)))(o||e)}})();static \u0275prov=Me({token:e,factory:e.\u0275fac})}return e})();class Xh{}let IR=(()=>{class e extends Xh{compile(t,r){return t}compileTranslations(t,r){return t}static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ut(e)))(o||e)}})();static \u0275prov=Me({token:e,factory:e.\u0275fac})}return e})();class SR{defaultLang;currentLang=this.defaultLang;translations={};langs=[];onTranslationChange=new Dt;onLangChange=new Dt;onDefaultLangChange=new Dt}const vC=new ce("USE_STORE"),yC=new ce("USE_DEFAULT_LANG"),CC=new ce("DEFAULT_LANGUAGE"),_C=new ce("USE_EXTEND");let Yh=(()=>{class e{store;currentLoader;compiler;parser;missingTranslationHandler;useDefaultLang;isolate;extend;loadingTranslations;pending=!1;_onTranslationChange=new Dt;_onLangChange=new Dt;_onDefaultLangChange=new Dt;_defaultLang;_currentLang;_langs=[];_translations={};_translationRequests={};get onTranslationChange(){return this.isolate?this._onTranslationChange:this.store.onTranslationChange}get onLangChange(){return this.isolate?this._onLangChange:this.store.onLangChange}get onDefaultLangChange(){return this.isolate?this._onDefaultLangChange:this.store.onDefaultLangChange}get defaultLang(){return this.isolate?this._defaultLang:this.store.defaultLang}set defaultLang(t){this.isolate?this._defaultLang=t:this.store.defaultLang=t}get currentLang(){return this.isolate?this._currentLang:this.store.currentLang}set currentLang(t){this.isolate?this._currentLang=t:this.store.currentLang=t}get langs(){return this.isolate?this._langs:this.store.langs}set langs(t){this.isolate?this._langs=t:this.store.langs=t}get translations(){return this.isolate?this._translations:this.store.translations}set translations(t){this.isolate?this._translations=t:this.store.translations=t}constructor(t,r,o,i,s,a=!0,l=!1,c=!1,d){this.store=t,this.currentLoader=r,this.compiler=o,this.parser=i,this.missingTranslationHandler=s,this.useDefaultLang=a,this.isolate=l,this.extend=c,d&&this.setDefaultLang(d)}setDefaultLang(t){if(t===this.defaultLang)return;let r=this.retrieveTranslations(t);typeof r<"u"?(null==this.defaultLang&&(this.defaultLang=t),r.pipe(jo(1)).subscribe(o=>{this.changeDefaultLang(t)})):this.changeDefaultLang(t)}getDefaultLang(){return this.defaultLang}use(t){if(t===this.currentLang)return je(this.translations[t]);let r=this.retrieveTranslations(t);return typeof r<"u"?(this.currentLang||(this.currentLang=t),r.pipe(jo(1)).subscribe(o=>{this.changeLang(t)}),r):(this.changeLang(t),je(this.translations[t]))}retrieveTranslations(t){let r;return(typeof this.translations[t]>"u"||this.extend)&&(this._translationRequests[t]=this._translationRequests[t]||this.getTranslation(t),r=this._translationRequests[t]),r}getTranslation(t){this.pending=!0;const r=this.currentLoader.getTranslation(t).pipe(_R(1),jo(1));return this.loadingTranslations=r.pipe(ct(o=>this.compiler.compileTranslations(o,t)),_R(1),jo(1)),this.loadingTranslations.subscribe({next:o=>{this.translations[t]=this.extend&&this.translations[t]?{...o,...this.translations[t]}:o,this.updateLangs(),this.pending=!1},error:o=>{this.pending=!1}}),r}setTranslation(t,r,o=!1){r=this.compiler.compileTranslations(r,t),this.translations[t]=(o||this.extend)&&this.translations[t]?ER(this.translations[t],r):r,this.updateLangs(),this.onTranslationChange.emit({lang:t,translations:this.translations[t]})}getLangs(){return this.langs}addLangs(t){t.forEach(r=>{-1===this.langs.indexOf(r)&&this.langs.push(r)})}updateLangs(){this.addLangs(Object.keys(this.translations))}getParsedResult(t,r,o){let i;if(r instanceof Array){let s={},a=!1;for(let l of r)s[l]=this.getParsedResult(t,l,o),Li(s[l])&&(a=!0);return a?dx(r.map(c=>Li(s[c])?s[c]:je(s[c]))).pipe(ct(c=>{let d={};return c.forEach((h,v)=>{d[r[v]]=h}),d})):s}if(t&&(i=this.parser.interpolate(this.parser.getValue(t,r),o)),typeof i>"u"&&null!=this.defaultLang&&this.defaultLang!==this.currentLang&&this.useDefaultLang&&(i=this.parser.interpolate(this.parser.getValue(this.translations[this.defaultLang],r),o)),typeof i>"u"){let s={key:r,translateService:this};typeof o<"u"&&(s.interpolateParams=o),i=this.missingTranslationHandler.handle(s)}return typeof i<"u"?i:r}get(t,r){if(!Gi(t)||!t.length)throw new Error('Parameter "key" required');if(this.pending)return this.loadingTranslations.pipe(ml(o=>Li(o=this.getParsedResult(o,t,r))?o:je(o)));{let o=this.getParsedResult(this.translations[this.currentLang],t,r);return Li(o)?o:je(o)}}getStreamOnTranslationChange(t,r){if(!Gi(t)||!t.length)throw new Error('Parameter "key" required');return fu(eh(()=>this.get(t,r)),this.onTranslationChange.pipe(mr(o=>{const i=this.getParsedResult(o.translations,t,r);return"function"==typeof i.subscribe?i:je(i)})))}stream(t,r){if(!Gi(t)||!t.length)throw new Error('Parameter "key" required');return fu(eh(()=>this.get(t,r)),this.onLangChange.pipe(mr(o=>{const i=this.getParsedResult(o.translations,t,r);return Li(i)?i:je(i)})))}instant(t,r){if(!Gi(t)||!t.length)throw new Error('Parameter "key" required');let o=this.getParsedResult(this.translations[this.currentLang],t,r);if(Li(o)){if(t instanceof Array){let i={};return t.forEach((s,a)=>{i[t[a]]=t[a]}),i}return t}return o}set(t,r,o=this.currentLang){this.translations[o][t]=this.compiler.compile(r,o),this.updateLangs(),this.onTranslationChange.emit({lang:o,translations:this.translations[o]})}changeLang(t){this.currentLang=t,this.onLangChange.emit({lang:t,translations:this.translations[t]}),null==this.defaultLang&&this.changeDefaultLang(t)}changeDefaultLang(t){this.defaultLang=t,this.onDefaultLangChange.emit({lang:t,translations:this.translations[t]})}reloadLang(t){return this.resetLang(t),this.getTranslation(t)}resetLang(t){this._translationRequests[t]=void 0,this.translations[t]=void 0}getBrowserLang(){if(typeof window>"u"||typeof window.navigator>"u")return;let t=window.navigator.languages?window.navigator.languages[0]:null;return t=t||window.navigator.language||window.navigator.browserLanguage||window.navigator.userLanguage,typeof t>"u"?void 0:(-1!==t.indexOf("-")&&(t=t.split("-")[0]),-1!==t.indexOf("_")&&(t=t.split("_")[0]),t)}getBrowserCultureLang(){if(typeof window>"u"||typeof window.navigator>"u")return;let t=window.navigator.languages?window.navigator.languages[0]:null;return t=t||window.navigator.language||window.navigator.browserLanguage||window.navigator.userLanguage,t}static \u0275fac=function(r){return new(r||e)(Pe(SR),Pe(Fu),Pe(Xh),Pe(qh),Pe(gC),Pe(yC),Pe(vC),Pe(_C),Pe(CC))};static \u0275prov=Me({token:e,factory:e.\u0275fac})}return e})(),MR=(()=>{class e{translate;_ref;value="";lastKey=null;lastParams=[];onTranslationChange;onLangChange;onDefaultLangChange;constructor(t,r){this.translate=t,this._ref=r}updateValue(t,r,o){let i=s=>{this.value=void 0!==s?s:t,this.lastKey=t,this._ref.markForCheck()};if(o){let s=this.translate.getParsedResult(o,t,r);Li(s.subscribe)?s.subscribe(i):i(s)}this.translate.get(t,r).subscribe(i)}transform(t,...r){if(!t||!t.length)return t;if(Wh(t,this.lastKey)&&Wh(r,this.lastParams))return this.value;let o;if(Gi(r[0])&&r.length)if("string"==typeof r[0]&&r[0].length){let i=r[0].replace(/(\')?([a-zA-Z0-9_]+)(\')?(\s)?:/g,'"$2":').replace(/:(\s)?(\')(.*?)(\')/g,':"$3"');try{o=JSON.parse(i)}catch{throw new SyntaxError(`Wrong parameter in TranslatePipe. Expected a valid Object, received: ${r[0]}`)}}else"object"==typeof r[0]&&!Array.isArray(r[0])&&(o=r[0]);return this.lastKey=t,this.lastParams=r,this.updateValue(t,o),this._dispose(),this.onTranslationChange||(this.onTranslationChange=this.translate.onTranslationChange.subscribe(i=>{this.lastKey&&i.lang===this.translate.currentLang&&(this.lastKey=null,this.updateValue(t,o,i.translations))})),this.onLangChange||(this.onLangChange=this.translate.onLangChange.subscribe(i=>{this.lastKey&&(this.lastKey=null,this.updateValue(t,o,i.translations))})),this.onDefaultLangChange||(this.onDefaultLangChange=this.translate.onDefaultLangChange.subscribe(()=>{this.lastKey&&(this.lastKey=null,this.updateValue(t,o))})),this.value}_dispose(){typeof this.onTranslationChange<"u"&&(this.onTranslationChange.unsubscribe(),this.onTranslationChange=void 0),typeof this.onLangChange<"u"&&(this.onLangChange.unsubscribe(),this.onLangChange=void 0),typeof this.onDefaultLangChange<"u"&&(this.onDefaultLangChange.unsubscribe(),this.onDefaultLangChange=void 0)}ngOnDestroy(){this._dispose()}static \u0275fac=function(r){return new(r||e)(g(Yh,16),g(ae,16))};static \u0275pipe=lr({name:"translate",type:e,pure:!1});static \u0275prov=Me({token:e,factory:e.\u0275fac})}return e})(),DC=(()=>{class e{static forRoot(t={}){return{ngModule:e,providers:[t.loader||{provide:Fu,useClass:DR},t.compiler||{provide:Xh,useClass:IR},t.parser||{provide:qh,useClass:wR},t.missingTranslationHandler||{provide:gC,useClass:bR},SR,{provide:vC,useValue:t.isolate},{provide:yC,useValue:t.useDefaultLang},{provide:_C,useValue:t.extend},{provide:CC,useValue:t.defaultLanguage},Yh]}}static forChild(t={}){return{ngModule:e,providers:[t.loader||{provide:Fu,useClass:DR},t.compiler||{provide:Xh,useClass:IR},t.parser||{provide:qh,useClass:wR},t.missingTranslationHandler||{provide:gC,useClass:bR},{provide:vC,useValue:t.isolate},{provide:yC,useValue:t.useDefaultLang},{provide:_C,useValue:t.extend},{provide:CC,useValue:t.defaultLanguage},Yh]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=is({type:e});static \u0275inj=Zr({})}return e})();class uq{http;prefix;suffix;constructor(n,t="/assets/i18n/",r=".json"){this.http=n,this.prefix=t,this.suffix=r}getTranslation(n){return this.http.get(`${this.prefix}${n}${this.suffix}`)}}function Lu(e,n){const t="object"==typeof n;return new Promise((r,o)=>{const i=new Rr({next:s=>{r(s),i.unsubscribe()},error:o,complete:()=>{t?r(n.defaultValue):o(new du)}});e.subscribe(i)})}function dq(e,n){if(1&e){const t=jc();rn(0,"div",4),Ar(1,"translate"),Ar(2,"translate"),sn("click",function(){const o=ds(t).$implicit;return fs(Tr().setRating(o.value))}),rn(3,"div",5)(4,"span",6),to(5),Ar(6,"translate"),ln(),rn(7,"div",7),Ms(8,"div",8),ln()()()}if(2&e){const t=n.$implicit,r=Tr();Oi("selected",r.currentRating===t.value),jr("aria-label",Ur(1,6,t.label)+": "+Ur(2,8,t.description)),Ht(5),Ri(Ur(6,10,t.label)),Ht(3),Oi("selected",r.currentRating===t.value)}}let fq=(()=>{class e{constructor(){this.currentRating=0,this.ratingChange=new Dt,this.ratingOptions=[{value:4,label:"RATING.VERY_GOOD",description:"RATING.VERY_GOOD_DESC"},{value:3,label:"RATING.GOOD",description:"RATING.GOOD_DESC"},{value:2,label:"RATING.BAD",description:"RATING.BAD_DESC"},{value:1,label:"RATING.VERY_BAD",description:"RATING.VERY_BAD_DESC"}]}setRating(t){this.ratingChange.emit(t)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275cmp=le({type:e,selectors:[["app-evaluation-card"]],inputs:{category:"category",currentRating:"currentRating"},outputs:{ratingChange:"ratingChange"},standalone:!0,features:[Xc],decls:8,vars:3,consts:[[1,"evaluation-card"],[3,"title"],[1,"rating-options"],["class","rating-option",3,"selected","click",4,"ngFor","ngForOf"],[1,"rating-option",3,"click"],[1,"option-content"],[1,"option-label"],[1,"option-circle"],[1,"circle-inner"]],template:function(r,o){1&r&&(rn(0,"div",0)(1,"ion-card")(2,"ion-card-header")(3,"ion-card-title",1),to(4),ln()(),rn(5,"ion-card-content")(6,"div",2),si(7,dq,9,12,"div",3),ln()()()()),2&r&&(Ht(3),Tn("title",o.category.description),Ht(),Ri(o.category.name),Ht(3),Tn("ngForOf",o.ratingOptions))},dependencies:[Nv,Mv,Vh,oC,iC,sC,aC,DC,MR],styles:[".evaluation-card[_ngcontent-%COMP%]{margin-bottom:20px;font-family:Cairo,sans-serif}.evaluation-card[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;border-radius:12px;box-shadow:0 4px 6px #0000001a}.evaluation-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]{padding-bottom:10px}.evaluation-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]{font-family:Cairo,sans-serif;font-weight:500;cursor:help}.evaluation-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]:hover{color:#ebc940}.evaluation-card[_ngcontent-%COMP%]   .rating-options[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;margin:20px 0}.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]{width:100%;height:56pt;background-color:#fff;border:1px solid #DFDFDF;border-radius:16pt;cursor:pointer;transition:all .2s ease;position:relative}.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]:hover{background-color:#fafafa}.evaluation-card[_ngcontent-%COMP%]   .rating-option.selected[_ngcontent-%COMP%]{background-color:#fff;border:1px solid #EBC940;box-shadow:0 0 0 4px #ebc94029}.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:0 24px;height:100%;font-family:Cairo,sans-serif;font-weight:500}.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-label[_ngcontent-%COMP%]{font-size:16px;color:#333}.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-circle[_ngcontent-%COMP%]{width:24px;height:24px;border-radius:50%;border:2px solid #ccc;display:flex;align-items:center;justify-content:center;transition:all .2s ease}.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-circle[_ngcontent-%COMP%]   .circle-inner[_ngcontent-%COMP%]{width:12px;height:12px;border-radius:50%;background-color:#ccc;transition:all .2s ease}.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-circle[_ngcontent-%COMP%]   .circle-inner.selected[_ngcontent-%COMP%]{background-color:#ebc940}.evaluation-card[_ngcontent-%COMP%]   .rating-option.selected[_ngcontent-%COMP%]   .option-circle[_ngcontent-%COMP%]{border-color:#ebc940;box-shadow:0 0 0 2px #ebc94029}.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]:not(.selected)   .option-circle[_ngcontent-%COMP%]{box-shadow:none}@media (max-width: 768px){.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]{height:48pt}.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-content[_ngcontent-%COMP%]{padding:0 20px}.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-label[_ngcontent-%COMP%]{font-size:14px}}@media (max-width: 480px){.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]{height:44pt}.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-content[_ngcontent-%COMP%]{padding:0 16px}.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-label[_ngcontent-%COMP%]{font-size:13px}.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-circle[_ngcontent-%COMP%]{width:20px;height:20px}.evaluation-card[_ngcontent-%COMP%]   .rating-option[_ngcontent-%COMP%]   .option-circle[_ngcontent-%COMP%]   .circle-inner[_ngcontent-%COMP%]{width:10px;height:10px}}"]})}}return e})(),hq=(()=>{class e{constructor(t){this.http=t,this.baseUrl="/api/evaluation",this.httpOptions={headers:new qr({"Content-Type":"application/json"})}}validateToken(t){return this.http.post(`${this.baseUrl}/validate`,{params:{token:t}},this.httpOptions).pipe(ct(r=>r.error?{success:!1,error:r.error.message,code:r.error.code?.toString()}:r.result||{success:!1,error:"No result in response"}))}getConfig(t){return this.http.post(`${this.baseUrl}/config`,{params:{token:t}},this.httpOptions).pipe(ct(r=>r.error?{success:!1,error:r.error.message,code:r.error.code?.toString()}:r.result||{success:!1,error:"No result in response"}))}submitEvaluation(t,r){return this.http.post(`${this.baseUrl}/submit`,{params:{token:t,evaluation_data:r}},this.httpOptions).pipe(ct(o=>o.error?{success:!1,error:o.error.message,code:o.error.code?.toString()}:o.result||{success:!1,error:"No result in response"}))}static{this.\u0275fac=function(r){return new(r||e)(Pe(zh))}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),pq=(()=>{class e{constructor(){this.evaluationDataSubject=new wn(null),this.evaluationData$=this.evaluationDataSubject.asObservable(),this.initializeMessageListener(),this.requestEvaluationData()}initializeMessageListener(){window.addEventListener("message",t=>{t.origin!==window.location.origin&&!t.origin.includes("localhost")||t.data&&"EVALUATION_DATA"===t.data.type&&this.evaluationDataSubject.next(t.data.payload)})}requestEvaluationData(){const t=new URLSearchParams(window.location.search),r=t.get("token"),o=t.get("driverName"),i=t.get("driverId"),s=t.get("linkId"),a=t.get("expiryDate");if(r&&o&&i&&s&&a){const l={token:r,driverName:o,driverId:parseInt(i),linkId:parseInt(s),expiryDate:a};this.evaluationDataSubject.next(l)}else window.parent!==window&&window.parent.postMessage({type:"REQUEST_EVALUATION_DATA"},"*")}sendMessage(t,r){window.parent!==window&&window.parent.postMessage({type:t,payload:r},"*")}notifyEvaluationComplete(t){this.sendMessage("EVALUATION_COMPLETE",t)}notifyEvaluationError(t){this.sendMessage("EVALUATION_ERROR","string"==typeof t?{error:t}:t)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),gq=(()=>{class e{constructor(t){this.http=t}loadStaticConfig(){return this.http.get("./assets/evaluation-config.json").pipe(ui(t=>(console.error("Failed to load static evaluation config:",t),je(this.getDefaultConfig()))))}loadEvaluationDataXml(){return this.http.get("./assets/evaluation_data.xml",{responseType:"text"}).pipe(ui(t=>(console.error("Failed to load evaluation data XML:",t),je(""))))}parseEvaluationXml(t){try{const o=(new DOMParser).parseFromString(t,"text/xml"),i=o.querySelector("parsererror");if(i)return console.error("XML parsing error:",i.textContent),je(null);const s=o.querySelector('field[name="evaluation_criteria"]');if(!s)return console.error("No evaluation criteria found in XML"),je(null);const a=s.textContent?.trim();if(!a)return console.error("Empty evaluation criteria in XML"),je(null);const l=JSON.parse(a),c=o.querySelector('field[name="evaluation_duration"]');return je({evaluation_duration:c?parseInt(c.textContent||"7"):7,criteria:l})}catch(r){return console.error("Error parsing evaluation XML:",r),je(null)}}getEvaluationConfig(){return this.loadStaticConfig().pipe(ui(()=>(console.log("Static JSON config failed, using default config..."),je(this.getDefaultConfig()))))}getDefaultConfig(){return{evaluation_duration:7,criteria:{categories:[{id:"question_1",name:"General Evaluation",description:"Please rate the overall performance",max_score:10,questions:[{id:"general_rating",text:"How would you rate the overall performance?",type:"rating",scale:10}]}]}}}static{this.\u0275fac=function(r){return new(r||e)(Pe(zh))}}static{this.\u0275prov=Me({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function mq(e,n){if(1&e&&(rn(0,"div",9)(1,"ion-card")(2,"ion-card-content")(3,"h2"),to(4),Ar(5,"translate"),ln(),rn(6,"p"),to(7),Ar(8,"translate"),Ar(9,"date"),ln()()()()),2&e){const t=Tr();Ht(4),Ri(Ur(5,3,t.evaluationTitle)),Ht(3),Wm("",Ur(8,5,"EXPIRES"),": ",kS(9,7,t.evaluationData.expiryDate,"medium"),"")}}function vq(e,n){1&e&&(rn(0,"div",10),Ms(1,"ion-spinner",11),rn(2,"p"),to(3),Ar(4,"translate"),ln()()),2&e&&(Ht(3),Ri(Ur(4,1,"LOADING_EVALUATION_FORM")))}function yq(e,n){if(1&e&&(rn(0,"div",12)(1,"h4"),to(2),Ar(3,"translate"),ln(),rn(4,"p"),to(5),ln()()),2&e){const t=Tr();Ht(2),Ri(Ur(3,2,"ERROR")),Ht(3),Ri(t.error)}}function Cq(e,n){if(1&e){const t=jc();rn(0,"app-evaluation-card",23),sn("ratingChange",function(o){const i=ds(t).$implicit;return fs(Tr(2).setRating(i.id,o))}),ln()}if(2&e){const t=n.$implicit,r=Tr(2);Tn("category",t)("currentRating",r.getRating(t.id))}}function _q(e,n){1&e&&Ms(0,"ion-spinner",11)}function Dq(e,n){if(1&e){const t=jc();rn(0,"div",13)(1,"div",14),si(2,Cq,1,2,"app-evaluation-card",15),ln(),rn(3,"div",16)(4,"ion-card")(5,"ion-card-header")(6,"ion-card-title",17),Ar(7,"translate"),to(8),Ar(9,"translate"),ln()(),rn(10,"ion-card-content")(11,"ion-item")(12,"ion-textarea",18),Ar(13,"translate"),yf("ngModelChange",function(o){ds(t);const i=Tr();return qm(i.feedback,o)||(i.feedback=o),fs(o)}),ln()()()()(),rn(14,"div",19)(15,"div",20)(16,"ion-button",21),sn("click",function(){return ds(t),fs(Tr().submitEvaluation())}),si(17,_q,1,0,"ion-spinner",22),to(18),Ar(19,"translate"),Ar(20,"translate"),ln()()()()}if(2&e){const t=Tr();Ht(2),Tn("ngForOf",t.config.criteria.categories),Ht(4),Tn("title",Ur(7,8,"FEEDBACK.FEEDBACK_DESCRIPTION")),Ht(2),qc(" ",Ur(9,10,"FEEDBACK.ADDITIONAL_FEEDBACK")," "),Ht(4),vf("ngModel",t.feedback),Tn("placeholder",Ur(13,12,"FEEDBACK.FEEDBACK_PLACEHOLDER")),Ht(4),Tn("disabled",t.isSubmitting||!t.hasValidRatings()),Ht(),Tn("ngIf",t.isSubmitting),Ht(),qc(" ",t.isSubmitting?Ur(19,14,"BUTTONS.SUBMITTING"):Ur(20,16,"BUTTONS.SUBMIT_EVALUATION")," ")}}(function n3(e,n){return m2({rootComponent:e,...VT(n)})})(I5,{providers:[function _6(e,...n){return Ca([{provide:Dh,multi:!0,useValue:e},[],{provide:Go,useFactory:sx,deps:[xr]},{provide:wf,multi:!0,useFactory:ax},n.map(t=>t.\u0275providers)])}([{path:"",component:(()=>{class e{constructor(t,r,o,i){this.apiService=t,this.iframeService=r,this.dataService=o,this.translate=i,this.destroy$=new jt,this.isLoading=!0,this.error=null,this.evaluationData=null,this.config=null,this.scores={},this.feedback="",this.evaluationTitle="Driver Evaluation",this.isSubmitting=!1,this.currentLanguage="ar",this.isArabic=!0,this.translate.setDefaultLang("ar"),this.translate.use("ar")}ngOnInit(){this.iframeService.evaluationData$.pipe(uA(this.destroy$)).subscribe(t=>{t&&(this.evaluationData=t,this.initializeEvaluation())})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}initializeEvaluation(){var t=this;return(0,Hs.A)(function*(){if(t.evaluationData)try{t.isLoading=!0,t.error=null;const r=yield Lu(t.apiService.validateToken(t.evaluationData.token));if(!r?.success){const o=r?.error||t.translate.instant("MESSAGES.INVALID_OR_EXPIRED_LINK");return t.error=o,void t.iframeService.notifyEvaluationError({error:o,isArabic:!0})}r.data&&(t.evaluationData={...t.evaluationData,driverName:r.data.driver_name,driverId:r.data.driver_id,linkId:r.data.link_id,expiryDate:r.data.expiry_date});try{console.log("\u{1f504} Requesting config from API with token:",t.evaluationData.token);const o=yield Lu(t.apiService.getConfig(t.evaluationData.token));console.log("\u{1f4e1} API Response received:",o),o?.success&&o.data?.criteria?.categories&&o.data.criteria.categories.length>0?(t.config=o.data,t.evaluationTitle=t.config.evaluation_title||"Driver Evaluation",console.log("\u2705 Using dynamic configuration from API:"),console.log("\u{1f4cb} Categories:",t.config.criteria.categories),console.log("\u{1f522} Number of categories:",t.config.criteria.categories.length),console.log("\u{1f4dd} Evaluation Title:",t.evaluationTitle)):(console.log("\u26a0\ufe0f API config failed or returned empty categories"),console.log("\u{1f4ca} Response success:",o?.success),console.log("\u{1f4ca} Response data:",o?.data),console.log("\u{1f4ca} Categories:",o?.data?.criteria?.categories),t.config=yield Lu(t.dataService.getEvaluationConfig()),console.log("\u{1f4c1} Using static configuration:",t.config.criteria.categories))}catch(o){console.log("\u274c API config error, using static configuration...",o),t.config=yield Lu(t.dataService.getEvaluationConfig()),console.log("\u{1f4c1} Using static configuration due to error:",t.config.criteria.categories)}t.isLoading=!1}catch(r){console.error("Initialization error:",r);const o=t.translate.instant("MESSAGES.FAILED_TO_LOAD");t.error=o,t.iframeService.notifyEvaluationError({error:o,isArabic:!0})}})()}setRating(t,r){this.scores[t]=r}getRating(t){return this.scores[t]||0}getRatingLabel(t){switch(t){case 4:return this.translate.instant("RATING.VERY_GOOD");case 3:return this.translate.instant("RATING.GOOD");case 2:return this.translate.instant("RATING.BAD");case 1:return this.translate.instant("RATING.VERY_BAD");default:return this.translate.instant("RATING.NOT_RATED")}}toggleLanguage(){this.currentLanguage=this.isArabic?"ar":"en",this.translate.use(this.currentLanguage);const t=document.querySelector(".evaluation-container");t&&t.setAttribute("dir","ar"===this.currentLanguage?"rtl":"ltr")}submitEvaluation(){var t=this;return(0,Hs.A)(function*(){if(!t.isSubmitting&&t.evaluationData)try{t.isSubmitting=!0;const r={scores:t.scores,evaluator:{name:"Anonymous",email:"<EMAIL>",phone:""},feedback:t.feedback},o=yield Lu(t.apiService.submitEvaluation(t.evaluationData.token,r));if(o?.success){const i=t.translate.instant("MESSAGES.EVALUATION_COMPLETED_SUCCESSFULLY"),s=t.translate.instant("MESSAGES.THANK_YOU_FEEDBACK"),a=t.translate.instant("MESSAGES.OVERALL_SCORE"),l=t.translate.instant("MESSAGES.LINK_NOW_INACTIVE");t.iframeService.notifyEvaluationComplete({success:!0,message:i,thankYou:s,overallScore:o.data?.overall_score||0,overallScoreLabel:a,linkInactiveMessage:l,isArabic:!0})}else{const i=o?.error||t.translate.instant("MESSAGES.SUBMISSION_ERROR");t.error=i,t.iframeService.notifyEvaluationError(i)}}catch(r){console.error("Submission error:",r);const o=t.translate.instant("MESSAGES.SUBMISSION_ERROR");t.error=o,t.iframeService.notifyEvaluationError({error:o,isArabic:!0})}finally{t.isSubmitting=!1}})()}getStarArray(t){return Array.from({length:t},(r,o)=>o+1)}getCategoryName(t){if(!this.config)return t;const r=this.config.criteria.categories.find(o=>o.id===t);return r&&r.name?r.name:this.translate.instant("CATEGORIES."+t.toUpperCase())||t}hasValidRatings(){if(!this.config)return!1;for(const t of this.config.criteria.categories)if(!this.scores[t.id]||0===this.scores[t.id])return!1;return!0}static{this.\u0275fac=function(r){return new(r||e)(g(hq),g(pq),g(gq),g(Yh))}}static{this.\u0275cmp=le({type:e,selectors:[["app-evaluation"]],standalone:!0,features:[Xc],decls:12,vars:9,consts:[["dir","rtl",1,"evaluation-container"],[1,"language-switch"],[1,"lang-toggle"],[1,"lang-label"],[1,"lang-toggle-switch",3,"ngModelChange","ionChange","ngModel"],["class","evaluation-header",4,"ngIf"],["class","loading-container",4,"ngIf"],["class","alert-danger",4,"ngIf"],["class","evaluation-form",4,"ngIf"],[1,"evaluation-header"],[1,"loading-container"],["name","crescent"],[1,"alert-danger"],[1,"evaluation-form"],[1,"categories-section"],[3,"category","currentRating","ratingChange",4,"ngFor","ngForOf"],[1,"feedback-section"],[3,"title"],["rows","4",3,"ngModelChange","ngModel","placeholder"],[1,"submit-section"],[1,"submit-actions"],["expand","block","size","large",1,"submit-button",3,"click","disabled"],["name","crescent",4,"ngIf"],[3,"ratingChange","category","currentRating"]],template:function(r,o){1&r&&(rn(0,"div",0)(1,"div",1)(2,"div",2)(3,"span",3),to(4,"AR"),ln(),rn(5,"ion-toggle",4),yf("ngModelChange",function(s){return qm(o.isArabic,s)||(o.isArabic=s),s}),sn("ionChange",function(){return o.toggleLanguage()}),ln(),rn(6,"span",3),to(7,"EN"),ln()()(),si(8,mq,10,10,"div",5)(9,vq,5,3,"div",6)(10,yq,6,4,"div",7)(11,Dq,21,18,"div",8),ln()),2&r&&(Ht(3),Oi("active","ar"===o.currentLanguage),Ht(2),vf("ngModel",o.isArabic),Ht(),Oi("active","en"===o.currentLanguage),Ht(2),Tn("ngIf",o.evaluationData),Ht(),Tn("ngIf",o.isLoading),Ht(),Tn("ngIf",o.error),Ht(),Tn("ngIf",!o.isLoading&&!o.error&&o.config))},dependencies:[Nv,Mv,au,gT,w8,Lx,Uy,Vh,jO,oC,iC,sC,aC,HO,zO,WO,qO,$O,VO,DC,MR,fq],styles:[".evaluation-container[_ngcontent-%COMP%]{max-width:900px;margin:0 auto;padding:20px;font-family:Cairo,sans-serif;font-weight:500;direction:rtl;text-align:right;min-height:100vh;box-sizing:border-box}.evaluation-container[dir=rtl][_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{text-align:right}.evaluation-container[dir=ltr][_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{text-align:left}.language-switch[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;margin-bottom:20px}.language-switch[_ngcontent-%COMP%]   .lang-toggle[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;font-family:Cairo,sans-serif;font-weight:500}.language-switch[_ngcontent-%COMP%]   .lang-toggle[_ngcontent-%COMP%]   .lang-label[_ngcontent-%COMP%]{font-size:14px;color:#666;transition:color .2s ease}.language-switch[_ngcontent-%COMP%]   .lang-toggle[_ngcontent-%COMP%]   .lang-label.active[_ngcontent-%COMP%]{color:#ebc940;font-weight:600}.language-switch[_ngcontent-%COMP%]   .lang-toggle[_ngcontent-%COMP%]   .lang-toggle-switch[_ngcontent-%COMP%]{--color: #EBC940;--color-checked: #EBC940;--handle-background: #fff;--handle-background-checked: #fff;--background: #ddd;--background-checked: #EBC940}.evaluation-header[_ngcontent-%COMP%]{margin-bottom:30px}.evaluation-header[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;border-radius:12px;box-shadow:0 4px 6px #0000001a}.evaluation-header[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{text-align:center;font-family:Cairo,sans-serif}.evaluation-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.8rem;margin-bottom:15px;font-weight:600;color:#333;font-family:Cairo,sans-serif}.evaluation-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1rem;margin-bottom:8px;color:#666;font-family:Cairo,sans-serif;font-weight:500}.evaluation-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{font-weight:600;color:#ebc940}.evaluation-form[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .5s ease-in}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.categories-section[_ngcontent-%COMP%], .feedback-section[_ngcontent-%COMP%]{margin-bottom:30px}.feedback-section[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;border-radius:12px;box-shadow:0 4px 6px #0000001a}.feedback-section[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]{padding-bottom:10px}.feedback-section[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]{font-family:Cairo,sans-serif;font-weight:500;cursor:help}.feedback-section[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]:hover{color:#ebc940}.feedback-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--background: transparent;--border-color: transparent;font-family:Cairo,sans-serif;--inner-border-width: 0}.feedback-section[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Cairo,sans-serif;font-weight:500}.feedback-section[_ngcontent-%COMP%]   ion-textarea[_ngcontent-%COMP%]{font-family:Cairo,sans-serif;--color: #333;border:1px #DFDFDF solid;border-radius:15px;--padding-start: 12px;--padding-end: 12px;--padding-top: 12px;--padding-bottom: 12px;margin-top:8px}.submit-section[_ngcontent-%COMP%]{margin-bottom:20px}.submit-actions[_ngcontent-%COMP%]{margin-top:20px;padding:20px 0 60px;margin-bottom:env(safe-area-inset-bottom,20px)}.submit-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]{--background: #EBC940;--background-activated: #d4b235;--background-hover: #d4b235;--color: #333;--border-radius: 16px;font-family:Cairo,sans-serif;font-weight:600;height:56px;font-size:16px}.submit-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]:disabled{--background: #ccc;--color: #666}.detailed-questions[_ngcontent-%COMP%]{margin-top:30px;padding-top:20px;border-top:1px solid #e0e0e0}.detailed-questions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#333;font-weight:600;margin-bottom:20px}.question-item[_ngcontent-%COMP%]{margin-bottom:25px;padding:15px;background:#f8f9fa;border-radius:8px}.question-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 15px;color:#333;font-weight:500;font-size:1.1rem}.step-actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;gap:15px;margin-top:30px;padding:20px 0}.step-actions[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{flex:1;height:50px;font-weight:600}.loading-container[_ngcontent-%COMP%]{text-align:center;padding:60px 20px}.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%]{width:50px;height:50px;margin-bottom:20px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:1.1rem}.alert-success[_ngcontent-%COMP%]{background-color:#d4edda;color:#155724;border:1px solid #c3e6cb;padding:15px;border-radius:8px;margin-bottom:20px}.alert-danger[_ngcontent-%COMP%]{background-color:#f8d7da;color:#721c24;border:1px solid #f5c6cb;padding:15px;border-radius:8px;margin-bottom:20px}.alert-danger[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 10px;font-weight:600}.alert-danger[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0}.review-summary[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#333;font-weight:600;margin:20px 0 15px}.ratings-summary[_ngcontent-%COMP%]{background:#f8f9fa;padding:15px;border-radius:8px;margin-bottom:20px}.rating-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:8px 0;border-bottom:1px solid #e9ecef}.rating-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.rating-item[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%]{font-weight:500;color:#333}.rating-item[_ngcontent-%COMP%]   .score-value[_ngcontent-%COMP%]{font-weight:600;color:#ebc940;font-size:1.1rem}.feedback-summary[_ngcontent-%COMP%]{background:#f8f9fa;padding:15px;border-radius:8px;margin-top:15px}.feedback-summary[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:10px 0 0;color:#555;line-height:1.5}.rating-display[_ngcontent-%COMP%]{margin-top:10px;font-weight:500;color:#ebc940}@media (max-width: 768px){.evaluation-container[_ngcontent-%COMP%]{padding:10px 10px 80px}.evaluation-header[_ngcontent-%COMP%]{padding:15px;margin-bottom:20px}.evaluation-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem}.step-actions[_ngcontent-%COMP%]{flex-direction:column}.step-actions[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-bottom:10px}.submit-actions[_ngcontent-%COMP%]{padding-bottom:100px;margin-bottom:max(env(safe-area-inset-bottom),40px)}.submit-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]{position:relative;z-index:10}}@media (max-width: 480px){.evaluation-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.5rem}.question-item[_ngcontent-%COMP%]{padding:10px}.submit-actions[_ngcontent-%COMP%]{padding-bottom:120px;margin-bottom:max(env(safe-area-inset-bottom),60px)}}ion-card[_ngcontent-%COMP%]{margin:0 0 20px;border-radius:12px;box-shadow:0 4px 6px #0000001a;font-family:Cairo,sans-serif}ion-card-header[_ngcontent-%COMP%]{padding-bottom:10px}ion-card-title[_ngcontent-%COMP%]{font-size:1.3rem;font-weight:600;color:#333;font-family:Cairo,sans-serif}ion-card-subtitle[_ngcontent-%COMP%]{color:#666;font-size:.95rem;font-family:Cairo,sans-serif}ion-item[_ngcontent-%COMP%]{--border-color: #e0e0e0;--background: transparent;margin-bottom:15px;font-family:Cairo,sans-serif}ion-label[_ngcontent-%COMP%]{font-weight:500;font-family:Cairo,sans-serif;color:#333}ion-input[_ngcontent-%COMP%], ion-textarea[_ngcontent-%COMP%]{--color: #333;--placeholder-color: #999}ion-button[_ngcontent-%COMP%]{--border-radius: 8px;font-weight:600;text-transform:none}ion-button[fill=outline][_ngcontent-%COMP%]{--color: #EBC940;--border-color: #EBC940}ion-button[color=success][_ngcontent-%COMP%]{--background: #28a745;--background-activated: #218838}ion-list[_ngcontent-%COMP%]{background:transparent;padding:0}ion-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--background: #f8f9fa;--border-color: #e9ecef;border-radius:6px;margin-bottom:8px}"]})}}return e})()},{path:"**",redirectTo:""}]),function J5(...e){const n=[zh,aR,iR,{provide:Bh,useExisting:iR},{provide:jh,useFactory:()=>U(dC,{optional:!0})??U(aR)},{provide:ku,useValue:Q5,multi:!0},{provide:hC,useValue:!0},{provide:uR,useClass:K5}];for(const t of e)n.push(...t.\u0275providers);return Ca(n)}(),_D(Vh.forRoot(),DC.forRoot({loader:{provide:Fu,useFactory:function Eq(e){return new uq(e,"./assets/i18n/",".json")},deps:[zh]},defaultLanguage:"ar"}))]}).catch(e=>console.error(e))},8996:(pn,pt,Y)=>{var oe={"./ion-accordion_2.entry.js":[2375,2076,2375],"./ion-action-sheet.entry.js":[8814,2076,8814],"./ion-alert.entry.js":[5222,2076,5222],"./ion-app_8.entry.js":[7720,2076,7720],"./ion-avatar_3.entry.js":[1049,1049],"./ion-back-button.entry.js":[3162,2076,3162],"./ion-backdrop.entry.js":[7240,7240],"./ion-breadcrumb_2.entry.js":[8314,2076,8314],"./ion-button_2.entry.js":[4591,4591],"./ion-card_5.entry.js":[8584,8584],"./ion-checkbox.entry.js":[3511,3511],"./ion-chip.entry.js":[6024,6024],"./ion-col_3.entry.js":[5100,5100],"./ion-datetime-button.entry.js":[7428,705,7428],"./ion-datetime_3.entry.js":[2885,705,2076,2885],"./ion-fab_3.entry.js":[4463,2076,4463],"./ion-img.entry.js":[4183,4183],"./ion-infinite-scroll_2.entry.js":[4171,2076,4171],"./ion-input-otp.entry.js":[7676,2076,7676],"./ion-input-password-toggle.entry.js":[6521,2076,6521],"./ion-input.entry.js":[9344,2076,9344],"./ion-item-option_3.entry.js":[5949,2076,5949],"./ion-item_8.entry.js":[3506,2076,3506],"./ion-loading.entry.js":[7372,2076,7372],"./ion-menu_3.entry.js":[2075,2076,2075],"./ion-modal.entry.js":[441,2076,441],"./ion-nav_2.entry.js":[5712,2076,5712],"./ion-picker-column-option.entry.js":[9013,9013],"./ion-picker-column.entry.js":[1459,2076,1459],"./ion-picker.entry.js":[6840,6840],"./ion-popover.entry.js":[6433,2076,6433],"./ion-progress-bar.entry.js":[9977,9977],"./ion-radio_2.entry.js":[8066,2076,8066],"./ion-range.entry.js":[8477,2076,8477],"./ion-refresher_2.entry.js":[5197,2076,5197],"./ion-reorder_2.entry.js":[7030,2076,7030],"./ion-ripple-effect.entry.js":[964,964],"./ion-route_4.entry.js":[8970,8970],"./ion-searchbar.entry.js":[8193,2076,8193],"./ion-segment-content.entry.js":[9073,9073],"./ion-segment-view.entry.js":[323,323],"./ion-segment_2.entry.js":[2560,2076,2560],"./ion-select-modal.entry.js":[770,770],"./ion-select_3.entry.js":[7076,2076,7076],"./ion-spinner.entry.js":[8805,2076,8805],"./ion-split-pane.entry.js":[5887,5887],"./ion-tab-bar_2.entry.js":[4406,2076,4406],"./ion-tab_2.entry.js":[1102,1102],"./ion-text.entry.js":[1577,1577],"./ion-textarea.entry.js":[2348,2076,2348],"./ion-toast.entry.js":[2415,2076,2415],"./ion-toggle.entry.js":[3814,2076,3814]};function be(Ue){if(!Y.o(oe,Ue))return Promise.resolve().then(()=>{var Z=new Error("Cannot find module '"+Ue+"'");throw Z.code="MODULE_NOT_FOUND",Z});var Je=oe[Ue],ke=Je[0];return Promise.all(Je.slice(1).map(Y.e)).then(()=>Y(ke))}be.keys=()=>Object.keys(oe),be.id=8996,pn.exports=be},467:(pn,pt,Y)=>{"use strict";function oe(Ue,Je,ke,Z,ie,se,De){try{var Ae=Ue[se](De),He=Ae.value}catch(ge){return void ke(ge)}Ae.done?Je(He):Promise.resolve(He).then(Z,ie)}function be(Ue){return function(){var Je=this,ke=arguments;return new Promise(function(Z,ie){var se=Ue.apply(Je,ke);function De(He){oe(se,Z,ie,De,Ae,"next",He)}function Ae(He){oe(se,Z,ie,De,Ae,"throw",He)}De(void 0)})}}Y.d(pt,{A:()=>be})}},pn=>{pn(pn.s=772)}]);