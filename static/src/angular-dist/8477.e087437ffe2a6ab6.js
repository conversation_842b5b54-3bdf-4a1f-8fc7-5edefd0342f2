"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[8477],{8477:(U,j,b)=>{b.r(j),b.d(j,{ion_range:()=>A});var I=b(467),r=b(2734),S=b(4657),s=b(1837),z=b(647),F=b(4576);const A=class{constructor(n){var e=this;(0,r.r)(this,n),this.ionChange=(0,r.d)(this,"ionChange",7),this.ionInput=(0,r.d)(this,"ionInput",7),this.ionFocus=(0,r.d)(this,"ionFocus",7),this.ionBlur=(0,r.d)(this,"ionBlur",7),this.ionKnobMoveStart=(0,r.d)(this,"ionKnobMoveStart",7),this.ionKnobMoveEnd=(0,r.d)(this,"ionKnobMoveEnd",7),this.rangeId="ion-r-"+O++,this.didLoad=!1,this.noUpdate=!1,this.hasFocus=!1,this.inheritedAttributes={},this.contentEl=null,this.initialContentScrollY=!0,this.ratioA=0,this.ratioB=0,this.name=this.rangeId,this.dualKnobs=!1,this.min=0,this.max=100,this.pin=!1,this.pinFormatter=t=>Math.round(t),this.snaps=!1,this.step=1,this.ticks=!0,this.disabled=!1,this.value=0,this.compareValues=(t,a)=>"object"==typeof t&&"object"==typeof a?t.lower!==a.lower||t.upper!==a.upper:t!==a,this.clampBounds=t=>(0,s.e)(this.min,t,this.max),this.ensureValueInBounds=t=>this.dualKnobs?{lower:this.clampBounds(t.lower),upper:this.clampBounds(t.upper)}:this.clampBounds(t),this.labelPlacement="start",this.setupGesture=(0,I.A)(function*(){const t=e.rangeSlider;t&&(e.gesture=(yield Promise.resolve().then(b.bind(b,6011))).createGesture({el:t,gestureName:"range",gesturePriority:100,threshold:10,onStart:()=>e.onStart(),onMove:a=>e.onMove(a),onEnd:a=>e.onEnd(a)}),e.gesture.enable(!e.disabled))}),this.handleKeyboard=(t,a)=>{const{ensureValueInBounds:o}=this;let i=this.step;i=i>0?i:1,i/=this.max-this.min,a||(i*=-1),"A"===t?this.ratioA=(0,s.e)(0,this.ratioA+i,1):this.ratioB=(0,s.e)(0,this.ratioB+i,1),this.ionKnobMoveStart.emit({value:o(this.value)}),this.updateValue(),this.emitValueChange(),this.ionKnobMoveEnd.emit({value:o(this.value)})},this.onBlur=()=>{this.hasFocus&&(this.hasFocus=!1,this.ionBlur.emit())},this.onFocus=()=>{this.hasFocus||(this.hasFocus=!0,this.ionFocus.emit())},this.onKnobFocus=t=>{if(this.hasFocus||(this.hasFocus=!0,this.ionFocus.emit()),this.dualKnobs&&this.el.shadowRoot){const a=this.el.shadowRoot.querySelector(".range-knob-a"),o=this.el.shadowRoot.querySelector(".range-knob-b");a?.classList.remove("ion-focused"),o?.classList.remove("ion-focused");const i="A"===t?a:o;i?.classList.add("ion-focused")}},this.onKnobBlur=()=>{setTimeout(()=>{var t;const a=null===(t=this.el.shadowRoot)||void 0===t?void 0:t.activeElement;if((!a||!a.classList.contains("range-knob-handle"))&&(this.hasFocus&&(this.hasFocus=!1,this.ionBlur.emit()),this.dualKnobs&&this.el.shadowRoot)){const i=this.el.shadowRoot.querySelector(".range-knob-a"),h=this.el.shadowRoot.querySelector(".range-knob-b");i?.classList.remove("ion-focused"),h?.classList.remove("ion-focused")}},0)}}debounceChanged(){const{ionInput:n,debounce:e,originalIonInput:t}=this;this.ionInput=void 0===e?t??n:(0,s.d)(n,e)}minChanged(n){(0,s.j)(n)||(this.min=0),this.noUpdate||this.updateRatio()}maxChanged(n){(0,s.j)(n)||(this.max=100),this.noUpdate||this.updateRatio()}stepChanged(n){(0,s.j)(n)||(this.step=1)}activeBarStartChanged(){const{activeBarStart:n}=this;void 0!==n&&(n>this.max?((0,r.m)(`[ion-range] - The value of activeBarStart (${n}) is greater than the max (${this.max}). Valid values are greater than or equal to the min value and less than or equal to the max value.`,this.el),this.activeBarStart=this.max):n<this.min&&((0,r.m)(`[ion-range] - The value of activeBarStart (${n}) is less than the min (${this.min}). Valid values are greater than or equal to the min value and less than or equal to the max value.`,this.el),this.activeBarStart=this.min))}disabledChanged(){this.gesture&&this.gesture.enable(!this.disabled)}valueChanged(n,e){this.compareValues(n,e)&&this.ionInput.emit({value:this.value}),this.noUpdate||this.updateRatio()}componentWillLoad(){this.el.hasAttribute("id")&&(this.rangeId=this.el.getAttribute("id")),this.inheritedAttributes=(0,s.i)(this.el),this.min=(0,s.j)(this.min)?this.min:0,this.max=(0,s.j)(this.max)?this.max:100,this.step=(0,s.j)(this.step)?this.step:1}componentDidLoad(){this.originalIonInput=this.ionInput,this.setupGesture(),this.updateRatio(),this.didLoad=!0}connectedCallback(){var n;this.updateRatio(),this.debounceChanged(),this.disabledChanged(),this.activeBarStartChanged(),this.didLoad&&this.setupGesture();const e=(0,S.f)(this.el);this.contentEl=null!==(n=e?.querySelector(".ion-content-scroll-host"))&&void 0!==n?n:e}disconnectedCallback(){this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}getValue(){var n;const e=null!==(n=this.value)&&void 0!==n?n:0;return this.dualKnobs?"object"==typeof e?e:{lower:0,upper:e}:"object"==typeof e?e.upper:e}emitValueChange(){this.value=this.ensureValueInBounds(this.value),this.ionChange.emit({value:this.value})}onStart(){this.ionKnobMoveStart.emit({value:this.ensureValueInBounds(this.value)})}onMove(n){const{contentEl:e,pressedKnob:t}=this,a=n.currentX;e&&void 0===this.pressedKnob&&(this.initialContentScrollY=(0,S.d)(e)),void 0===t&&this.setPressedKnob(a),this.update(a)}onEnd(n){var e;const{contentEl:t,initialContentScrollY:a}=this,o=null!==(e=n.currentX)&&void 0!==e?e:n.clientX;void 0===this.pressedKnob&&this.setPressedKnob(o),t&&void 0!==this.pressedKnob&&(0,S.r)(t,a),this.update(o),this.pressedKnob=void 0,this.emitValueChange(),this.ionKnobMoveEnd.emit({value:this.ensureValueInBounds(this.value)})}update(n){const e=this.rect;let t=(0,s.e)(0,(n-e.left)/e.width,1);(0,z.i)(this.el)&&(t=1-t),this.snaps&&(t=w(f(t,this.min,this.max,this.step),this.min,this.max)),"A"===this.pressedKnob?this.ratioA=t:this.ratioB=t,this.updateValue()}setPressedKnob(n){const e=this.rect=this.rangeSlider.getBoundingClientRect();let t=(0,s.e)(0,(n-e.left)/e.width,1);(0,z.i)(this.el)&&(t=1-t),this.pressedKnob=!this.dualKnobs||Math.abs(this.ratioA-t)<Math.abs(this.ratioB-t)?"A":"B",this.setFocus(this.pressedKnob)}get valA(){return f(this.ratioA,this.min,this.max,this.step)}get valB(){return f(this.ratioB,this.min,this.max,this.step)}get ratioLower(){if(this.dualKnobs)return Math.min(this.ratioA,this.ratioB);const{activeBarStart:n}=this;return null==n?0:w(n,this.min,this.max)}get ratioUpper(){return this.dualKnobs?Math.max(this.ratioA,this.ratioB):this.ratioA}updateRatio(){const n=this.getValue(),{min:e,max:t}=this;this.dualKnobs?(this.ratioA=w(n.lower,e,t),this.ratioB=w(n.upper,e,t)):this.ratioA=w(n,e,t)}updateValue(){this.noUpdate=!0;const{valA:n,valB:e}=this;this.value=this.dualKnobs?{lower:Math.min(n,e),upper:Math.max(n,e)}:n,this.noUpdate=!1}setFocus(n){if(this.el.shadowRoot){const e=this.el.shadowRoot.querySelector("A"===n?".range-knob-a":".range-knob-b");e&&e.focus()}}get hasStartSlotContent(){return null!==this.el.querySelector('[slot="start"]')}get hasEndSlotContent(){return null!==this.el.querySelector('[slot="end"]')}get hasLabel(){return void 0!==this.label||null!==this.el.querySelector('[slot="label"]')}renderRangeSlider(){var n;const{min:e,max:t,step:a,handleKeyboard:o,pressedKnob:i,disabled:h,pin:x,ratioLower:p,ratioUpper:u,pinFormatter:y,inheritedAttributes:_}=this;let k=100*p+"%",v=100-100*u+"%";const B=(0,z.i)(this.el),K=B?"right":"left",M=B?"left":"right",m=g=>({[K]:g[K]});!1===this.dualKnobs&&(this.valA<(null!==(n=this.activeBarStart)&&void 0!==n?n:this.min)?(k=100*u+"%",v=100-100*p+"%"):(k=100*p+"%",v=100-100*u+"%"));const C={[K]:k,[M]:v},L=[];if(this.snaps&&this.ticks)for(let g=e;g<=t;g+=a){const R=w(g,e,t),T=Math.min(p,u),V=Math.max(p,u),P={ratio:R,active:R>=T&&R<=V};P[K]=100*R+"%",L.push(P)}return(0,r.h)("div",{class:"range-slider",ref:g=>this.rangeSlider=g,onPointerUp:g=>{void 0===this.pressedKnob&&(this.onStart(),this.onEnd(g))}},L.map(g=>(0,r.h)("div",{style:m(g),role:"presentation",class:{"range-tick":!0,"range-tick-active":g.active},part:g.active?"tick-active":"tick"})),(0,r.h)("div",{class:"range-bar-container"},(0,r.h)("div",{class:"range-bar",role:"presentation",part:"bar"}),(0,r.h)("div",{class:{"range-bar":!0,"range-bar-active":!0,"has-ticks":L.length>0},role:"presentation",style:C,part:"bar-active"})),E(B,{knob:"A",pressed:"A"===i,value:this.valA,ratio:this.ratioA,pin:x,pinFormatter:y,disabled:h,handleKeyboard:o,min:e,max:t,inheritedAttributes:_,onKnobFocus:this.onKnobFocus,onKnobBlur:this.onKnobBlur}),this.dualKnobs&&E(B,{knob:"B",pressed:"B"===i,value:this.valB,ratio:this.ratioB,pin:x,pinFormatter:y,disabled:h,handleKeyboard:o,min:e,max:t,inheritedAttributes:_,onKnobFocus:this.onKnobFocus,onKnobBlur:this.onKnobBlur}))}render(){const{disabled:n,el:e,hasLabel:t,rangeId:a,pin:o,pressedKnob:i,labelPlacement:h,label:x}=this,p=(0,F.h)("ion-item",e),y=p&&!(t&&("start"===h||"fixed"===h)||this.hasStartSlotContent),k=p&&!(t&&"end"===h||this.hasEndSlotContent),v=(0,r.e)(this);return(0,s.a)(!0,e,this.name,JSON.stringify(this.getValue()),n),(0,r.h)(r.j,{key:"ef7b01f80515bcaeb2983934ad7f10a6bd5d13ec",onFocusin:this.onFocus,onFocusout:this.onBlur,id:a,class:(0,F.c)(this.color,{[v]:!0,"in-item":p,"range-disabled":n,"range-pressed":void 0!==i,"range-has-pin":o,[`range-label-placement-${h}`]:!0,"range-item-start-adjustment":y,"range-item-end-adjustment":k})},(0,r.h)("label",{key:"fd8aa90a9d52be9da024b907e68858dae424449d",class:"range-wrapper",id:"range-label"},(0,r.h)("div",{key:"2172b4f329c22017dd23475c80aac25ba6e753eb",class:{"label-text-wrapper":!0,"label-text-wrapper-hidden":!t},part:"label"},void 0!==x?(0,r.h)("div",{class:"label-text"},x):(0,r.h)("slot",{name:"label"})),(0,r.h)("div",{key:"3c318bf2ea0576646d4c010bf44573fd0f483186",class:"native-wrapper"},(0,r.h)("slot",{key:"6586fd6fc96271e73f8a86c202d1913ad1a26f96",name:"start"}),this.renderRangeSlider(),(0,r.h)("slot",{key:"74ac0bc2d2cb66ef708bb729f88b6ecbc1b2155d",name:"end"}))))}get el(){return(0,r.k)(this)}static get watchers(){return{debounce:["debounceChanged"],min:["minChanged"],max:["maxChanged"],step:["stepChanged"],activeBarStart:["activeBarStartChanged"],disabled:["disabledChanged"],value:["valueChanged"]}}},E=(n,{knob:e,value:t,ratio:a,min:o,max:i,disabled:h,pressed:x,pin:p,handleKeyboard:u,pinFormatter:y,inheritedAttributes:_,onKnobFocus:k,onKnobBlur:v})=>{const B=n?"right":"left",M=_["aria-label"];return(0,r.h)("div",{onKeyDown:m=>{const C=m.key;"ArrowLeft"===C||"ArrowDown"===C?(u(e,!1),m.preventDefault(),m.stopPropagation()):("ArrowRight"===C||"ArrowUp"===C)&&(u(e,!0),m.preventDefault(),m.stopPropagation())},onFocus:()=>k(e),onBlur:v,class:{"range-knob-handle":!0,"range-knob-a":"A"===e,"range-knob-b":"B"===e,"range-knob-pressed":x,"range-knob-min":t===o,"range-knob-max":t===i,"ion-activatable":!0,"ion-focusable":!0},style:(()=>{const m={};return m[B]=100*a+"%",m})(),role:"slider",tabindex:h?-1:0,"aria-label":void 0!==M?M:null,"aria-labelledby":void 0===M?"range-label":null,"aria-valuemin":o,"aria-valuemax":i,"aria-disabled":h?"true":null,"aria-valuenow":t},p&&(0,r.h)("div",{class:"range-pin",role:"presentation",part:"pin"},y(t)),(0,r.h)("div",{class:"range-knob",role:"presentation",part:"knob"}))},f=(n,e,t,a)=>{let o=(t-e)*n;return a>0&&(o=Math.round(o/a)*a+e),function l(n,...e){if(!(0,s.j)(n))return 0;const t=Math.max(...e.map(a=>function D(n){return(0,s.j)(n)&&n%1!=0?n.toString().split(".")[1].length:0}(a)));return Number(n.toFixed(t))}((0,s.e)(e,o,t),e,t,a)},w=(n,e,t)=>(0,s.e)(0,(n-e)/(t-e),1);let O=0;A.style={ios:":host{--knob-handle-size:calc(var(--knob-size) * 2);display:-ms-flexbox;display:flex;position:relative;-ms-flex:3;flex:3;-ms-flex-align:center;align-items:center;font-family:var(--ion-font-family, inherit);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.range-disabled){pointer-events:none}::slotted(ion-label){-ms-flex:initial;flex:initial}::slotted(ion-icon[slot]){font-size:24px}.range-slider{position:relative;-ms-flex:1;flex:1;width:100%;height:var(--height);contain:size layout style;cursor:-webkit-grab;cursor:grab;-ms-touch-action:pan-y;touch-action:pan-y}:host(.range-pressed) .range-slider{cursor:-webkit-grabbing;cursor:grabbing}.range-pin{position:absolute;background:var(--ion-color-base);color:var(--ion-color-contrast);text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box}.range-knob-handle{top:calc((var(--height) - var(--knob-handle-size)) / 2);-webkit-margin-start:calc(0px - var(--knob-handle-size) / 2);margin-inline-start:calc(0px - var(--knob-handle-size) / 2);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-pack:center;justify-content:center;width:var(--knob-handle-size);height:var(--knob-handle-size);text-align:center}.range-knob-handle{inset-inline-start:0}:host-context([dir=rtl]) .range-knob-handle{left:unset}[dir=rtl] .range-knob-handle{left:unset}@supports selector(:dir(rtl)){.range-knob-handle:dir(rtl){left:unset}}.range-knob-handle:active,.range-knob-handle:focus{outline:none}.range-bar-container{border-radius:var(--bar-border-radius);top:calc((var(--height) - var(--bar-height)) / 2);position:absolute;width:100%;height:var(--bar-height)}.range-bar-container{inset-inline-start:0}:host-context([dir=rtl]) .range-bar-container{left:unset}[dir=rtl] .range-bar-container{left:unset}@supports selector(:dir(rtl)){.range-bar-container:dir(rtl){left:unset}}.range-bar{border-radius:var(--bar-border-radius);position:absolute;width:100%;height:var(--bar-height);background:var(--bar-background);pointer-events:none}.range-knob{border-radius:var(--knob-border-radius);top:calc(50% - var(--knob-size) / 2);position:absolute;width:var(--knob-size);height:var(--knob-size);background:var(--knob-background);-webkit-box-shadow:var(--knob-box-shadow);box-shadow:var(--knob-box-shadow);z-index:2;pointer-events:none}.range-knob{inset-inline-start:calc(50% - var(--knob-size) / 2)}:host-context([dir=rtl]) .range-knob{left:unset}[dir=rtl] .range-knob{left:unset}@supports selector(:dir(rtl)){.range-knob:dir(rtl){left:unset}}:host(.range-pressed) .range-bar-active{will-change:left, right}:host(.in-item){width:100%}:host([slot=start]),:host([slot=end]){width:auto}:host(.in-item) ::slotted(ion-label){-ms-flex-item-align:center;align-self:center}.range-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit}::slotted([slot=label]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center}:host(.range-label-placement-start) .range-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.range-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-end) .range-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.range-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.range-label-placement-stacked) .range-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:stretch;align-items:stretch}:host(.range-label-placement-stacked) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top;-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host-context([dir=rtl]):host(.range-label-placement-stacked) .label-text-wrapper,:host-context([dir=rtl]).range-label-placement-stacked .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.range-label-placement-stacked:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.in-item.range-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.range-label-placement-stacked) .native-wrapper{margin-bottom:0px}:host{--knob-border-radius:50%;--knob-background:#ffffff;--knob-box-shadow:0px 0.5px 4px rgba(0, 0, 0, 0.12), 0px 6px 13px rgba(0, 0, 0, 0.12);--knob-size:26px;--bar-height:4px;--bar-background:var(--ion-color-step-900, var(--ion-background-color-step-900, #e6e6e6));--bar-background-active:var(--ion-color-primary, #0054e9);--bar-border-radius:2px;--height:42px}:host(.range-item-start-adjustment){-webkit-padding-start:24px;padding-inline-start:24px}:host(.range-item-end-adjustment){-webkit-padding-end:24px;padding-inline-end:24px}:host(.ion-color) .range-bar-active,:host(.ion-color) .range-tick-active{background:var(--ion-color-base)}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-has-pin:not(.range-label-placement-stacked)){padding-top:calc(8px + 0.75rem)}:host(.range-has-pin.range-label-placement-stacked) .label-text-wrapper{margin-bottom:calc(8px + 0.75rem)}.range-bar-active{bottom:0;width:auto;background:var(--bar-background-active)}.range-bar-active.has-ticks{border-radius:0;-webkit-margin-start:-2px;margin-inline-start:-2px;-webkit-margin-end:-2px;margin-inline-end:-2px}.range-tick{-webkit-margin-start:-2px;margin-inline-start:-2px;border-radius:0;position:absolute;top:17px;width:4px;height:8px;background:var(--ion-color-step-900, var(--ion-background-color-step-900, #e6e6e6));pointer-events:none}.range-tick-active{background:var(--bar-background-active)}.range-pin{-webkit-transform:translate3d(0,  100%,  0) scale(0.01);transform:translate3d(0,  100%,  0) scale(0.01);-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;min-width:28px;-webkit-transition:-webkit-transform 120ms ease;transition:-webkit-transform 120ms ease;transition:transform 120ms ease;transition:transform 120ms ease, -webkit-transform 120ms ease;background:transparent;color:var(--ion-text-color, #000);font-size:0.75rem;text-align:center}.range-knob-pressed .range-pin,.range-knob-handle.ion-focused .range-pin{-webkit-transform:translate3d(0, calc(-100% + 11px), 0) scale(1);transform:translate3d(0, calc(-100% + 11px), 0) scale(1)}:host(.range-disabled){opacity:0.3}",md:':host{--knob-handle-size:calc(var(--knob-size) * 2);display:-ms-flexbox;display:flex;position:relative;-ms-flex:3;flex:3;-ms-flex-align:center;align-items:center;font-family:var(--ion-font-family, inherit);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.range-disabled){pointer-events:none}::slotted(ion-label){-ms-flex:initial;flex:initial}::slotted(ion-icon[slot]){font-size:24px}.range-slider{position:relative;-ms-flex:1;flex:1;width:100%;height:var(--height);contain:size layout style;cursor:-webkit-grab;cursor:grab;-ms-touch-action:pan-y;touch-action:pan-y}:host(.range-pressed) .range-slider{cursor:-webkit-grabbing;cursor:grabbing}.range-pin{position:absolute;background:var(--ion-color-base);color:var(--ion-color-contrast);text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box}.range-knob-handle{top:calc((var(--height) - var(--knob-handle-size)) / 2);-webkit-margin-start:calc(0px - var(--knob-handle-size) / 2);margin-inline-start:calc(0px - var(--knob-handle-size) / 2);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-pack:center;justify-content:center;width:var(--knob-handle-size);height:var(--knob-handle-size);text-align:center}.range-knob-handle{inset-inline-start:0}:host-context([dir=rtl]) .range-knob-handle{left:unset}[dir=rtl] .range-knob-handle{left:unset}@supports selector(:dir(rtl)){.range-knob-handle:dir(rtl){left:unset}}.range-knob-handle:active,.range-knob-handle:focus{outline:none}.range-bar-container{border-radius:var(--bar-border-radius);top:calc((var(--height) - var(--bar-height)) / 2);position:absolute;width:100%;height:var(--bar-height)}.range-bar-container{inset-inline-start:0}:host-context([dir=rtl]) .range-bar-container{left:unset}[dir=rtl] .range-bar-container{left:unset}@supports selector(:dir(rtl)){.range-bar-container:dir(rtl){left:unset}}.range-bar{border-radius:var(--bar-border-radius);position:absolute;width:100%;height:var(--bar-height);background:var(--bar-background);pointer-events:none}.range-knob{border-radius:var(--knob-border-radius);top:calc(50% - var(--knob-size) / 2);position:absolute;width:var(--knob-size);height:var(--knob-size);background:var(--knob-background);-webkit-box-shadow:var(--knob-box-shadow);box-shadow:var(--knob-box-shadow);z-index:2;pointer-events:none}.range-knob{inset-inline-start:calc(50% - var(--knob-size) / 2)}:host-context([dir=rtl]) .range-knob{left:unset}[dir=rtl] .range-knob{left:unset}@supports selector(:dir(rtl)){.range-knob:dir(rtl){left:unset}}:host(.range-pressed) .range-bar-active{will-change:left, right}:host(.in-item){width:100%}:host([slot=start]),:host([slot=end]){width:auto}:host(.in-item) ::slotted(ion-label){-ms-flex-item-align:center;align-self:center}.range-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit}::slotted([slot=label]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center}:host(.range-label-placement-start) .range-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.range-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-end) .range-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.range-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.range-label-placement-stacked) .range-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:stretch;align-items:stretch}:host(.range-label-placement-stacked) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top;-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host-context([dir=rtl]):host(.range-label-placement-stacked) .label-text-wrapper,:host-context([dir=rtl]).range-label-placement-stacked .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.range-label-placement-stacked:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.in-item.range-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.range-label-placement-stacked) .native-wrapper{margin-bottom:0px}:host{--knob-border-radius:50%;--knob-background:var(--bar-background-active);--knob-box-shadow:none;--knob-size:18px;--bar-height:2px;--bar-background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.26);--bar-background-active:var(--ion-color-primary, #0054e9);--bar-border-radius:0;--height:42px;--pin-background:var(--ion-color-primary, #0054e9);--pin-color:var(--ion-color-primary-contrast, #fff)}::slotted(:not(ion-icon)[slot=start]),::slotted(:not(ion-icon)[slot=end]),.native-wrapper{font-size:0.75rem}:host(.range-item-start-adjustment){-webkit-padding-start:18px;padding-inline-start:18px}:host(.range-item-end-adjustment){-webkit-padding-end:18px;padding-inline-end:18px}:host(.ion-color) .range-bar{background:rgba(var(--ion-color-base-rgb), 0.26)}:host(.ion-color) .range-bar-active,:host(.ion-color) .range-knob,:host(.ion-color) .range-knob::before,:host(.ion-color) .range-pin,:host(.ion-color) .range-pin::before,:host(.ion-color) .range-tick{background:var(--ion-color-base);color:var(--ion-color-contrast)}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:14px;margin-inline-end:14px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:14px;margin-inline-start:14px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-has-pin:not(.range-label-placement-stacked)){padding-top:1.75rem}:host(.range-has-pin.range-label-placement-stacked) .label-text-wrapper{margin-bottom:1.75rem}.range-bar-active{bottom:0;width:auto;background:var(--bar-background-active)}.range-knob{-webkit-transform:scale(0.67);transform:scale(0.67);-webkit-transition-duration:120ms;transition-duration:120ms;-webkit-transition-property:background-color, border, -webkit-transform;transition-property:background-color, border, -webkit-transform;transition-property:transform, background-color, border;transition-property:transform, background-color, border, -webkit-transform;-webkit-transition-timing-function:ease;transition-timing-function:ease;z-index:2}.range-knob::before{border-radius:50%;position:absolute;width:var(--knob-size);height:var(--knob-size);-webkit-transform:scale(1);transform:scale(1);-webkit-transition:0.267s cubic-bezier(0, 0, 0.58, 1);transition:0.267s cubic-bezier(0, 0, 0.58, 1);background:var(--knob-background);content:"";opacity:0.13;pointer-events:none}.range-knob::before{inset-inline-start:0}.range-tick{position:absolute;top:calc((var(--height) - var(--bar-height)) / 2);width:var(--bar-height);height:var(--bar-height);background:var(--bar-background-active);z-index:1;pointer-events:none}.range-tick-active{background:transparent}.range-pin{padding-left:0;padding-right:0;padding-top:8px;padding-bottom:8px;border-radius:50%;-webkit-transform:translate3d(0,  0,  0) scale(0.01);transform:translate3d(0,  0,  0) scale(0.01);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:1.75rem;height:1.75rem;-webkit-transition:background 120ms ease, -webkit-transform 120ms ease;transition:background 120ms ease, -webkit-transform 120ms ease;transition:transform 120ms ease, background 120ms ease;transition:transform 120ms ease, background 120ms ease, -webkit-transform 120ms ease;background:var(--pin-background);color:var(--pin-color)}.range-pin::before{bottom:-1px;-webkit-margin-start:-13px;margin-inline-start:-13px;border-radius:50% 50% 50% 0;position:absolute;width:26px;height:26px;-webkit-transform:rotate(-45deg);transform:rotate(-45deg);-webkit-transition:background 120ms ease;transition:background 120ms ease;background:var(--pin-background);content:"";z-index:-1}.range-pin::before{inset-inline-start:50%}:host-context([dir=rtl]) .range-pin::before{left:unset}[dir=rtl] .range-pin::before{left:unset}@supports selector(:dir(rtl)){.range-pin::before:dir(rtl){left:unset}}.range-knob-pressed .range-pin,.range-knob-handle.ion-focused .range-pin{-webkit-transform:translate3d(0, calc(-100% + 4px), 0) scale(1);transform:translate3d(0, calc(-100% + 4px), 0) scale(1)}@media (any-hover: hover){.range-knob-handle:hover .range-knob:before{-webkit-transform:scale(2);transform:scale(2);opacity:0.13}}.range-knob-handle.ion-activated .range-knob:before,.range-knob-handle.ion-focused .range-knob:before,.range-knob-handle.range-knob-pressed .range-knob:before{-webkit-transform:scale(2);transform:scale(2)}.range-knob-handle.ion-focused .range-knob::before{opacity:0.13}.range-knob-handle.ion-activated .range-knob::before,.range-knob-handle.range-knob-pressed .range-knob::before{opacity:0.25}:host(:not(.range-has-pin)) .range-knob-pressed .range-knob,:host(:not(.range-has-pin)) .range-knob-handle.ion-focused .range-knob{-webkit-transform:scale(1);transform:scale(1)}:host(.range-disabled) .range-bar-active,:host(.range-disabled) .range-bar,:host(.range-disabled) .range-tick{background-color:var(--ion-color-step-250, var(--ion-background-color-step-250, #bfbfbf))}:host(.range-disabled) .range-knob{-webkit-transform:scale(0.55);transform:scale(0.55);outline:5px solid #fff;background-color:var(--ion-color-step-250, var(--ion-background-color-step-250, #bfbfbf))}:host(.range-disabled) .label-text-wrapper,:host(.range-disabled) ::slotted([slot=start]),:host(.range-disabled) ::slotted([slot=end]){opacity:0.38}'}},4576:(U,j,b)=>{b.d(j,{c:()=>S,g:()=>z,h:()=>r,o:()=>D});var I=b(467);const r=(l,d)=>null!==d.closest(l),S=(l,d)=>"string"==typeof l&&l.length>0?Object.assign({"ion-color":!0,[`ion-color-${l}`]:!0},d):d,z=l=>{const d={};return(l=>void 0!==l?(Array.isArray(l)?l:l.split(" ")).filter(c=>null!=c).map(c=>c.trim()).filter(c=>""!==c):[])(l).forEach(c=>d[c]=!0),d},F=/^[a-z][a-z0-9+\-.]*:/,D=function(){var l=(0,I.A)(function*(d,c,A,E){if(null!=d&&"#"!==d[0]&&!F.test(d)){const f=document.querySelector("ion-router");if(f)return c?.preventDefault(),f.push(d,A,E)}return!1});return function(c,A,E,f){return l.apply(this,arguments)}}()}}]);