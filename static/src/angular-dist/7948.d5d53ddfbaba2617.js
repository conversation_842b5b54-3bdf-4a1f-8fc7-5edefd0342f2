"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[7948],{7948:(b,p,d)=>{d.r(p),d.d(p,{startTapClick:()=>C});var l=d(9596),R=d(1837);d(2734);const C=n=>{if(void 0===l.d)return;let t,a,o,s=0;const g=n.getBoolean("animated",!0)&&n.getBoolean("rippleEffect",!0),u=new WeakMap,D=()=>{o&&clearTimeout(o),o=void 0,t&&(A(!1),t=void 0)},_=(e,i)=>{if(e&&e===t)return;o&&clearTimeout(o),o=void 0;const{x:c,y:r}=(0,R.q)(i);if(t){if(u.has(t))throw new Error("internal error");t.classList.contains(f)||T(t,c,r),A(!0)}if(e){const L=u.get(e);L&&(clearTimeout(L),u.delete(e)),e.classList.remove(f);const h=()=>{T(e,c,r),o=void 0};E(e)?h():o=setTimeout(h,I)}t=e},T=(e,i,c)=>{if(s=Date.now(),e.classList.add(f),!g)return;const r=w(e);null!==r&&(m(),a=r.addRipple(i,c))},m=()=>{void 0!==a&&(a.then(e=>e()),a=void 0)},A=e=>{m();const i=t;if(!i)return;const c=v-Date.now()+s;if(e&&c>0&&!E(i)){const r=setTimeout(()=>{i.classList.remove(f),u.delete(i)},v);u.set(i,r)}else i.classList.remove(f)};l.d.addEventListener("ionGestureCaptured",D),l.d.addEventListener("pointerdown",e=>{t||2===e.button||_(P(e),e)},!0),l.d.addEventListener("pointerup",e=>{_(void 0,e)},!0),l.d.addEventListener("pointercancel",D,!0)},P=n=>{if(void 0===n.composedPath)return n.target.closest(".ion-activatable");{const s=n.composedPath();for(let t=0;t<s.length-2;t++){const a=s[t];if(!(a instanceof ShadowRoot)&&a.classList.contains("ion-activatable"))return a}}},E=n=>n.classList.contains("ion-activatable-instant"),w=n=>{if(n.shadowRoot){const s=n.shadowRoot.querySelector("ion-ripple-effect");if(s)return s}return n.querySelector("ion-ripple-effect")},f="ion-activated",I=100,v=150}}]);