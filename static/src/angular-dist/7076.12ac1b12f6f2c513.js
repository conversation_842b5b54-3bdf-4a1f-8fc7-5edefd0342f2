"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[7076],{7076:(M,x,a)=>{a.r(x),a.d(x,{ion_select:()=>I,ion_select_option:()=>A,ion_select_popover:()=>Y});var b=a(467),o=a(2734),O=a(7241),f=a(7620),g=a(1837),m=a(3217),C=a(647),n=a(4576),c=a(4211),d=a(5514);a(9596),a(1906),a(1653),a(8607);const I=class{constructor(t){(0,o.r)(this,t),this.ionChange=(0,o.d)(this,"ionChange",7),this.ionCancel=(0,o.d)(this,"ionCancel",7),this.ionDismiss=(0,o.d)(this,"ionDismiss",7),this.ionFocus=(0,o.d)(this,"ionFocus",7),this.ionBlur=(0,o.d)(this,"ionBlur",7),this.ionStyle=(0,o.d)(this,"ionStyle",7),this.inputId="ion-sel-"+$++,this.helperTextId=`${this.inputId}-helper-text`,this.errorTextId=`${this.inputId}-error-text`,this.inheritedAttributes={},this.isExpanded=!1,this.hasFocus=!1,this.cancelText="Cancel",this.disabled=!1,this.interface="alert",this.interfaceOptions={},this.labelPlacement="start",this.multiple=!1,this.name=this.inputId,this.okText="OK",this.required=!1,this.onClick=e=>{const i=e.target,l=i.closest('[slot="start"], [slot="end"]');i===this.el||null===l?(this.setFocus(),this.open(e)):e.preventDefault()},this.onFocus=()=>{this.hasFocus=!0,this.ionFocus.emit()},this.onBlur=()=>{this.hasFocus=!1,this.ionBlur.emit()},this.onLabelClick=e=>{e.target===e.currentTarget&&e.stopPropagation()}}styleChanged(){this.emitStyle()}setValue(t){this.value=t,this.ionChange.emit({value:t})}connectedCallback(){var t=this;return(0,b.A)(function*(){const{el:e}=t;t.notchController=(0,O.c)(e,()=>t.notchSpacerEl,()=>t.labelSlot),t.updateOverlayOptions(),t.emitStyle(),t.mutationO=(0,c.w)(t.el,"ion-select-option",(0,b.A)(function*(){t.updateOverlayOptions(),(0,o.n)(t)}))})()}componentWillLoad(){this.inheritedAttributes=(0,g.b)(this.el,["aria-label"])}componentDidLoad(){this.emitStyle()}disconnectedCallback(){this.mutationO&&(this.mutationO.disconnect(),this.mutationO=void 0),this.notchController&&(this.notchController.destroy(),this.notchController=void 0)}open(t){var e=this;return(0,b.A)(function*(){if(e.disabled||e.isExpanded)return;e.isExpanded=!0;const i=e.overlay=yield e.createOverlay(t),l=()=>{const s=e.childOpts.findIndex(r=>r.value===e.value);if(s>-1){const r=i.querySelector(`.select-interface-option:nth-of-type(${s+1})`);if(r){const h=r.querySelector("ion-radio, ion-checkbox");h&&(r.scrollIntoView({block:"nearest"}),h.setFocus()),(0,g.n)(r)}}else{const r=i.querySelector("ion-radio:not(.radio-disabled), ion-checkbox:not(.checkbox-disabled)");r&&(r.setFocus(),(0,g.n)(r.closest("ion-item")))}};if("modal"===e.interface)i.addEventListener("ionModalWillPresent",l,{once:!0});else if("popover"===e.interface)i.addEventListener("ionPopoverWillPresent",l,{once:!0});else{const s=()=>{requestAnimationFrame(()=>{l()})};"alert"===e.interface?i.addEventListener("ionAlertWillPresent",s,{once:!0}):"action-sheet"===e.interface&&i.addEventListener("ionActionSheetWillPresent",s,{once:!0})}return i.onDidDismiss().then(()=>{e.overlay=void 0,e.isExpanded=!1,e.ionDismiss.emit(),e.setFocus()}),yield i.present(),i})()}createOverlay(t){let e=this.interface;return"action-sheet"===e&&this.multiple&&((0,o.m)(`[ion-select] - Interface cannot be "${e}" with a multi-value select. Using the "alert" interface instead.`),e="alert"),"popover"===e&&!t&&((0,o.m)(`[ion-select] - Interface cannot be a "${e}" without passing an event. Using the "alert" interface instead.`),e="alert"),"action-sheet"===e?this.openActionSheet():"popover"===e?this.openPopover(t):"modal"===e?this.openModal():this.openAlert()}updateOverlayOptions(){const t=this.overlay;if(!t)return;const e=this.childOpts,i=this.value;switch(this.interface){case"action-sheet":t.buttons=this.createActionSheetButtons(e,i);break;case"popover":const l=t.querySelector("ion-select-popover");l&&(l.options=this.createOverlaySelectOptions(e,i));break;case"modal":const s=t.querySelector("ion-select-modal");s&&(s.options=this.createOverlaySelectOptions(e,i));break;case"alert":t.inputs=this.createAlertInputs(e,this.multiple?"checkbox":"radio",i)}}createActionSheetButtons(t,e){const i=t.map(l=>{const s=w(l),r=Array.from(l.classList).filter(p=>"hydrated"!==p).join(" "),h=`${T} ${r}`;return{role:(0,f.i)(e,s,this.compareWith)?"selected":"",text:l.textContent,cssClass:h,handler:()=>{this.setValue(s)}}});return i.push({text:this.cancelText,role:"cancel",handler:()=>{this.ionCancel.emit()}}),i}createAlertInputs(t,e,i){return t.map(s=>{const r=w(s),h=Array.from(s.classList).filter(u=>"hydrated"!==u).join(" ");return{type:e,cssClass:`${T} ${h}`,label:s.textContent||"",value:r,checked:(0,f.i)(i,r,this.compareWith),disabled:s.disabled}})}createOverlaySelectOptions(t,e){return t.map(l=>{const s=w(l),r=Array.from(l.classList).filter(p=>"hydrated"!==p).join(" ");return{text:l.textContent||"",cssClass:`${T} ${r}`,value:s,checked:(0,f.i)(e,s,this.compareWith),disabled:l.disabled,handler:p=>{this.setValue(p),this.multiple||this.close()}}})}openPopover(t){var e=this;return(0,b.A)(function*(){const{fill:i,labelPlacement:l}=e,s=e.interfaceOptions,r=(0,o.e)(e),h="md"!==r,p=e.multiple,u=e.value;let y=t,k="auto";"floating"===l||"stacked"===l||"md"===r&&void 0!==i?k="cover":y=Object.assign(Object.assign({},t),{detail:{ionShadowTarget:e.nativeWrapperEl}});const _=Object.assign(Object.assign({mode:r,event:y,alignment:"center",size:k,showBackdrop:h},s),{component:"ion-select-popover",cssClass:["select-popover",s.cssClass],componentProps:{header:s.header,subHeader:s.subHeader,message:s.message,multiple:p,value:u,options:e.createOverlaySelectOptions(e.childOpts,u)}});return m.c.create(_)})()}openActionSheet(){var t=this;return(0,b.A)(function*(){const e=(0,o.e)(t),i=t.interfaceOptions,l=Object.assign(Object.assign({mode:e},i),{buttons:t.createActionSheetButtons(t.childOpts,t.value),cssClass:["select-action-sheet",i.cssClass]});return m.b.create(l)})()}openAlert(){var t=this;return(0,b.A)(function*(){const e=t.interfaceOptions,i=t.multiple?"checkbox":"radio",l=(0,o.e)(t),s=Object.assign(Object.assign({mode:l},e),{header:e.header?e.header:t.labelText,inputs:t.createAlertInputs(t.childOpts,i,t.value),buttons:[{text:t.cancelText,role:"cancel",handler:()=>{t.ionCancel.emit()}},{text:t.okText,handler:r=>{t.setValue(r)}}],cssClass:["select-alert",e.cssClass,t.multiple?"multiple-select-alert":"single-select-alert"]});return m.a.create(s)})()}openModal(){const{multiple:t,value:e,interfaceOptions:i}=this,l=(0,o.e)(this),s=Object.assign(Object.assign({},i),{mode:l,cssClass:["select-modal",i.cssClass],component:"ion-select-modal",componentProps:{header:i.header,multiple:t,value:e,options:this.createOverlaySelectOptions(this.childOpts,e)}});return m.m.create(s)}close(){return this.overlay?this.overlay.dismiss():Promise.resolve(!1)}hasValue(){return""!==this.getText()}get childOpts(){return Array.from(this.el.querySelectorAll("ion-select-option"))}get labelText(){const{label:t}=this;if(void 0!==t)return t;const{labelSlot:e}=this;return null!==e?e.textContent:void 0}getText(){const t=this.selectedText;return null!=t&&""!==t?t:U(this.childOpts,this.value,this.compareWith)}setFocus(){this.focusEl&&this.focusEl.focus()}emitStyle(){const{disabled:t}=this;this.ionStyle.emit({"interactive-disabled":t})}renderLabel(){const{label:t}=this;return(0,o.h)("div",{class:{"label-text-wrapper":!0,"label-text-wrapper-hidden":!this.hasLabel},part:"label"},void 0===t?(0,o.h)("slot",{name:"label"}):(0,o.h)("div",{class:"label-text"},t))}componentDidRender(){var t;null===(t=this.notchController)||void 0===t||t.calculateNotchWidth()}get labelSlot(){return this.el.querySelector('[slot="label"]')}get hasLabel(){return void 0!==this.label||null!==this.labelSlot}renderLabelContainer(){return"md"===(0,o.e)(this)&&"outline"===this.fill?[(0,o.h)("div",{class:"select-outline-container"},(0,o.h)("div",{class:"select-outline-start"}),(0,o.h)("div",{class:{"select-outline-notch":!0,"select-outline-notch-hidden":!this.hasLabel}},(0,o.h)("div",{class:"notch-spacer","aria-hidden":"true",ref:i=>this.notchSpacerEl=i},this.label)),(0,o.h)("div",{class:"select-outline-end"})),this.renderLabel()]:this.renderLabel()}renderSelectText(){const{placeholder:t}=this;let i=!1,l=this.getText();return""===l&&void 0!==t&&(l=t,i=!0),(0,o.h)("div",{"aria-hidden":"true",class:{"select-text":!0,"select-placeholder":i},part:i?"placeholder":"text"},l)}renderSelectIcon(){const t=(0,o.e)(this),{isExpanded:e,toggleIcon:i,expandedIcon:l}=this;let s;return s=e&&void 0!==l?l:i??("ios"===t?d.w:d.q),(0,o.h)("ion-icon",{class:"select-icon",part:"icon","aria-hidden":"true",icon:s})}get ariaLabel(){var t;const{placeholder:e,inheritedAttributes:i}=this,l=this.getText(),s=null!==(t=i["aria-label"])&&void 0!==t?t:this.labelText;let r=l;return""===r&&void 0!==e&&(r=e),void 0!==s&&(r=""===r?s:`${s}, ${r}`),r}renderListbox(){const{disabled:t,inputId:e,isExpanded:i,required:l}=this;return(0,o.h)("button",{disabled:t,id:e,"aria-label":this.ariaLabel,"aria-haspopup":"dialog","aria-expanded":`${i}`,"aria-describedby":this.getHintTextID(),"aria-invalid":this.getHintTextID()===this.errorTextId,"aria-required":`${l}`,onFocus:this.onFocus,onBlur:this.onBlur,ref:s=>this.focusEl=s})}getHintTextID(){const{el:t,helperText:e,errorText:i,helperTextId:l,errorTextId:s}=this;return t.classList.contains("ion-touched")&&t.classList.contains("ion-invalid")&&i?s:e?l:void 0}renderHintText(){const{helperText:t,errorText:e,helperTextId:i,errorTextId:l}=this;return[(0,o.h)("div",{id:i,class:"helper-text",part:"supporting-text helper-text"},t),(0,o.h)("div",{id:l,class:"error-text",part:"supporting-text error-text"},e)]}renderBottomContent(){const{helperText:t,errorText:e}=this;if(t||e)return(0,o.h)("div",{class:"select-bottom"},this.renderHintText())}render(){const{disabled:t,el:e,isExpanded:i,expandedIcon:l,labelPlacement:s,justify:r,placeholder:h,fill:p,shape:u,name:y,value:k,hasFocus:D}=this,_=(0,o.e)(this),P="floating"===s||"stacked"===s,G=!P&&void 0!==r,N=(0,C.i)(e)?"rtl":"ltr",L=(0,n.h)("ion-item",this.el),Z="md"===_&&"outline"!==p&&!L,z=this.hasValue(),J=null!==e.querySelector('[slot="start"], [slot="end"]');(0,g.a)(!0,e,y,W(k),t);const X="stacked"===s||"floating"===s&&(z||i||J);return(0,o.h)(o.j,{key:"c03fb65e8fc9f9aab295e07b282377d57d910519",onClick:this.onClick,class:(0,n.c)(this.color,{[_]:!0,"in-item":L,"in-item-color":(0,n.h)("ion-item.ion-color",e),"select-disabled":t,"select-expanded":i,"has-expanded-icon":void 0!==l,"has-value":z,"label-floating":X,"has-placeholder":void 0!==h,"has-focus":D,"ion-focusable":!0,[`select-${N}`]:!0,[`select-fill-${p}`]:void 0!==p,[`select-justify-${r}`]:G,[`select-shape-${u}`]:void 0!==u,[`select-label-placement-${s}`]:!0})},(0,o.h)("label",{key:"0d0c8ec55269adcac625f2899a547f4e7f3e3741",class:"select-wrapper",id:"select-label",onClick:this.onLabelClick},this.renderLabelContainer(),(0,o.h)("div",{key:"f6dfc93c0e23cbe75a2947abde67d842db2dad78",class:"select-wrapper-inner"},(0,o.h)("slot",{key:"957bfadf9f101f519091419a362d3abdc2be66f6",name:"start"}),(0,o.h)("div",{key:"ca349202a484e7f2e884533fd330f0b136754f7d",class:"native-wrapper",ref:Q=>this.nativeWrapperEl=Q,part:"container"},this.renderSelectText(),this.renderListbox()),(0,o.h)("slot",{key:"f0e62a6533ff1c8f62bd2d27f60b23385c4fa9ed",name:"end"}),!P&&this.renderSelectIcon()),P&&this.renderSelectIcon(),Z&&(0,o.h)("div",{key:"fb840d46bafafb09898ebeebbe8c181906a3d8a2",class:"select-highlight"})),this.renderBottomContent())}get el(){return(0,o.k)(this)}static get watchers(){return{disabled:["styleChanged"],isExpanded:["styleChanged"],placeholder:["styleChanged"],value:["styleChanged"]}}},w=t=>{const e=t.value;return void 0===e?t.textContent||"":e},W=t=>{if(null!=t)return Array.isArray(t)?t.join(","):t.toString()},U=(t,e,i)=>void 0===e?"":Array.isArray(e)?e.map(l=>S(t,l,i)).filter(l=>null!==l).join(", "):S(t,e,i)||"",S=(t,e,i)=>{const l=t.find(s=>(0,f.c)(e,w(s),i));return l?l.textContent:null};let $=0;const T="select-interface-option";I.style={ios:":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0}:host(.select-disabled){pointer-events:none}:host(.has-focus) button{border:2px solid #5e9ed6}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.select-bottom{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}:host(.has-focus.ion-valid),:host(.select-expanded.ion-valid),:host(.ion-touched.ion-invalid),:host(.select-expanded.ion-touched.ion-invalid){--border-color:var(--highlight-color)}.select-bottom .error-text{display:none;color:var(--highlight-color-invalid)}.select-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .select-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .select-bottom .helper-text{display:none}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-focus.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]:last-of-type){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]:first-of-type){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host{--border-width:0.55px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--highlight-height:0px}.select-icon{width:1.125rem;height:1.125rem;color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959))}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 1.125rem - 4px)}:host(.select-disabled){opacity:0.3}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}",md:":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0}:host(.select-disabled){pointer-events:none}:host(.has-focus) button{border:2px solid #5e9ed6}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.select-bottom{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}:host(.has-focus.ion-valid),:host(.select-expanded.ion-valid),:host(.ion-touched.ion-invalid),:host(.select-expanded.ion-touched.ion-invalid){--border-color:var(--highlight-color)}.select-bottom .error-text{display:none;color:var(--highlight-color-invalid)}.select-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .select-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .select-bottom .helper-text{display:none}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-focus.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]:last-of-type){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]:first-of-type){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.select-fill-solid){--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-500, var(--ion-background-color-step-500, gray));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-solid) .select-wrapper{border-bottom:var(--border-width) var(--border-style) var(--border-color)}:host(.select-expanded.select-fill-solid.ion-valid),:host(.has-focus.select-fill-solid.ion-valid),:host(.select-fill-solid.ion-touched.ion-invalid){--border-color:var(--highlight-color)}:host(.select-fill-solid) .select-bottom{border-top:none}@media (any-hover: hover){:host(.select-fill-solid:hover){--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}:host(.select-fill-solid.select-expanded),:host(.select-fill-solid.has-focus){--background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--border-color:var(--highlight-color)}:host(.select-fill-solid) .select-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0px;border-end-start-radius:0px}:host(.label-floating.select-fill-solid) .label-text-wrapper{max-width:calc(100% / 0.75)}:host(.in-item.select-expanded.select-fill-solid) .select-wrapper .select-icon,:host(.in-item.has-focus.select-fill-solid) .select-wrapper .select-icon,:host(.in-item.has-focus.ion-valid.select-fill-solid) .select-wrapper .select-icon,:host(.in-item.ion-touched.ion-invalid.select-fill-solid) .select-wrapper .select-icon{color:var(--highlight-color)}:host(.select-fill-outline){--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-outline.select-shape-round){--border-radius:28px;--padding-start:32px;--padding-end:32px}:host(.has-focus.select-fill-outline.ion-valid),:host(.select-fill-outline.ion-touched.ion-invalid){--border-color:var(--highlight-color)}@media (any-hover: hover){:host(.select-fill-outline:hover){--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}:host(.select-fill-outline.select-expanded),:host(.select-fill-outline.has-focus){--border-width:var(--highlight-height);--border-color:var(--highlight-color)}:host(.select-fill-outline) .select-bottom{border-top:none}:host(.select-fill-outline) .select-wrapper{border-bottom:none}:host(.select-ltr.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-fill-outline.select-label-placement-floating) .label-text-wrapper{position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .label-text-wrapper{position:relative;z-index:1}:host(.label-floating.select-fill-outline) .label-text-wrapper{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}:host(.select-fill-outline.select-label-placement-stacked) select,:host(.select-fill-outline.select-label-placement-floating) select{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}:host(.select-fill-outline) .select-outline-container{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-end{pointer-events:none}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-notch,:host(.select-fill-outline) .select-outline-end{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.select-fill-outline) .select-outline-notch{max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .notch-spacer{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none}:host(.select-fill-outline) .select-outline-start{-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color)}:host(.select-fill-outline) .select-outline-start{border-start-start-radius:var(--border-radius);border-start-end-radius:0px;border-end-end-radius:0px;border-end-start-radius:var(--border-radius)}:host(.select-fill-outline) .select-outline-start{width:calc(var(--padding-start) - 4px)}:host(.select-fill-outline) .select-outline-end{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color)}:host(.select-fill-outline) .select-outline-end{border-start-start-radius:0px;border-start-end-radius:var(--border-radius);border-end-end-radius:var(--border-radius);border-end-start-radius:0px}:host(.select-fill-outline) .select-outline-end{-ms-flex-positive:1;flex-grow:1}:host(.label-floating.select-fill-outline) .select-outline-notch{border-top:none}:host(.in-item.select-expanded.select-fill-outline) .select-wrapper .select-icon,:host(.in-item.has-focus.select-fill-outline) .select-wrapper .select-icon,:host(.in-item.has-focus.ion-valid.select-fill-outline) .select-wrapper .select-icon,:host(.in-item.ion-touched.ion-invalid.select-fill-outline) .select-wrapper .select-icon{color:var(--highlight-color)}:host{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--highlight-height:2px}:host(.select-label-placement-floating.select-expanded) .label-text-wrapper,:host(.select-label-placement-floating.has-focus) .label-text-wrapper,:host(.select-label-placement-stacked.select-expanded) .label-text-wrapper,:host(.select-label-placement-stacked.has-focus) .label-text-wrapper{color:var(--highlight-color)}:host(.has-focus.select-label-placement-floating.ion-valid) .label-text-wrapper,:host(.select-label-placement-floating.ion-touched.ion-invalid) .label-text-wrapper,:host(.has-focus.select-label-placement-stacked.ion-valid) .label-text-wrapper,:host(.select-label-placement-stacked.ion-touched.ion-invalid) .label-text-wrapper{color:var(--highlight-color)}.select-highlight{bottom:-1px;position:absolute;width:100%;height:var(--highlight-height);-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}.select-highlight{inset-inline-start:0}:host(.select-expanded) .select-highlight,:host(.has-focus) .select-highlight{-webkit-transform:scale(1);transform:scale(1)}:host(.in-item) .select-highlight{bottom:0}:host(.in-item) .select-highlight{inset-inline-start:0}.select-icon{width:0.8125rem;-webkit-transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray))}:host(.select-expanded:not(.has-expanded-icon)) .select-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.in-item.select-expanded) .select-wrapper .select-icon,:host(.in-item.has-focus) .select-wrapper .select-icon,:host(.in-item.has-focus.ion-valid) .select-wrapper .select-icon,:host(.in-item.ion-touched.ion-invalid) .select-wrapper .select-icon{color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray))}:host(.select-expanded) .select-wrapper .select-icon,:host(.has-focus.ion-valid) .select-wrapper .select-icon,:host(.ion-touched.ion-invalid) .select-wrapper .select-icon,:host(.has-focus) .select-wrapper .select-icon{color:var(--highlight-color)}:host(.select-shape-round){--border-radius:16px}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 0.8125rem - 4px)}:host(.select-disabled){opacity:0.38}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}"};const A=class{constructor(t){(0,o.r)(this,t),this.inputId="ion-selopt-"+K++,this.disabled=!1}render(){return(0,o.h)(o.j,{key:"3a70eea9fa03a9acba582180761d18347c72acee",role:"option",id:this.inputId,class:(0,o.e)(this)})}get el(){return(0,o.k)(this)}};let K=0;A.style=":host{display:none}";const Y=(()=>{let t=class{constructor(e){(0,o.r)(this,e),this.options=[]}findOptionFromEvent(e){const{options:i}=this;return i.find(l=>l.value===e.target.value)}callOptionHandler(e){const i=this.findOptionFromEvent(e),l=this.getValues(e);i?.handler&&(0,m.s)(i.handler,l)}dismissParentPopover(){const e=this.el.closest("ion-popover");e&&e.dismiss()}setChecked(e){const{multiple:i}=this,l=this.findOptionFromEvent(e);i&&l&&(l.checked=e.detail.checked)}getValues(e){const{multiple:i,options:l}=this;if(i)return l.filter(r=>r.checked).map(r=>r.value);const s=this.findOptionFromEvent(e);return s?s.value:void 0}renderOptions(e){const{multiple:i}=this;return!0===i?this.renderCheckboxOptions(e):this.renderRadioOptions(e)}renderCheckboxOptions(e){return e.map(i=>(0,o.h)("ion-item",{class:Object.assign({"item-checkbox-checked":i.checked},(0,n.g)(i.cssClass))},(0,o.h)("ion-checkbox",{value:i.value,disabled:i.disabled,checked:i.checked,justify:"start",labelPlacement:"end",onIonChange:l=>{this.setChecked(l),this.callOptionHandler(l),(0,o.n)(this)}},i.text)))}renderRadioOptions(e){const i=e.filter(l=>l.checked).map(l=>l.value)[0];return(0,o.h)("ion-radio-group",{value:i,onIonChange:l=>this.callOptionHandler(l)},e.map(l=>(0,o.h)("ion-item",{class:Object.assign({"item-radio-checked":l.value===i},(0,n.g)(l.cssClass))},(0,o.h)("ion-radio",{value:l.value,disabled:l.disabled,onClick:()=>this.dismissParentPopover(),onKeyUp:s=>{" "===s.key&&this.dismissParentPopover()}},l.text))))}render(){const{header:e,message:i,options:l,subHeader:s}=this,r=void 0!==s||void 0!==i;return(0,o.h)(o.j,{key:"ab931b49b59283825bd2afa3f7f995b0e6e05bef",class:(0,o.e)(this)},(0,o.h)("ion-list",{key:"3bd12b67832607596b912a73d5b3ae9b954b244d"},void 0!==e&&(0,o.h)("ion-list-header",{key:"97da930246edf7423a039c030d40e3ff7a5148a3"},e),r&&(0,o.h)("ion-item",{key:"c579df6ea8fac07bb0c59d34c69b149656863224"},(0,o.h)("ion-label",{key:"af699c5f465710ccb13b8cf8e7be66f0e8acfad1",class:"ion-text-wrap"},void 0!==s&&(0,o.h)("h3",{key:"df9a936d42064b134e843c7229f314a2a3ec7e80"},s),void 0!==i&&(0,o.h)("p",{key:"9c3ddad378df00f106afa94e9928cf68c17124dd"},i))),this.renderOptions(l)))}get el(){return(0,o.k)(this)}};return t.style={ios:".sc-ion-select-popover-ios-h ion-list.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-ios,ion-label.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-ios-h{overflow-y:auto}",md:".sc-ion-select-popover-md-h ion-list.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-md,ion-label.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-md-h{overflow-y:auto}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(container){display:none}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(label){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-item.sc-ion-select-popover-md{--inner-border-width:0}.item-radio-checked.sc-ion-select-popover-md{--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08);--background-focused:var(--ion-color-primary, #0054e9);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #0054e9);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-popover-md{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #0054e9)}"},t})()},4576:(M,x,a)=>{a.d(x,{c:()=>O,g:()=>g,h:()=>o,o:()=>C});var b=a(467);const o=(n,c)=>null!==c.closest(n),O=(n,c)=>"string"==typeof n&&n.length>0?Object.assign({"ion-color":!0,[`ion-color-${n}`]:!0},c):c,g=n=>{const c={};return(n=>void 0!==n?(Array.isArray(n)?n:n.split(" ")).filter(d=>null!=d).map(d=>d.trim()).filter(d=>""!==d):[])(n).forEach(d=>c[d]=!0),c},m=/^[a-z][a-z0-9+\-.]*:/,C=function(){var n=(0,b.A)(function*(c,d,E,j){if(null!=c&&"#"!==c[0]&&!m.test(c)){const v=document.querySelector("ion-router");if(v)return d?.preventDefault(),v.push(c,E,j)}return!1});return function(d,E,j,v){return n.apply(this,arguments)}}()}}]);