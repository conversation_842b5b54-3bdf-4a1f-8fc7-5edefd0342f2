{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-split-pane_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACoJ;AAEpJ,MAAMa,eAAe,GAAG,ooBAAooB;AAE5pB,MAAMC,cAAc,GAAG,6oBAA6oB;;AAEpqB;AACA,MAAMC,eAAe,GAAG,iBAAiB;AACzC,MAAMC,eAAe,GAAG,iBAAiB;AACzC,MAAMC,KAAK,GAAG;EACVC,EAAE,EAAE,kBAAkB;EACtBC,EAAE,EAAE,oBAAoB;EACxBC,EAAE,EAAE,oBAAoB;EACxBC,EAAE,EAAE,oBAAoB;EACxBC,EAAE,EAAE,qBAAqB;EACzBC,KAAK,EAAE;AACX,CAAC;AACD,MAAMC,SAAS,GAAG,MAAM;EACpBC,WAAWA,CAACC,OAAO,EAAE;IACjBzB,qDAAgB,CAAC,IAAI,EAAEyB,OAAO,CAAC;IAC/B,IAAI,CAACC,mBAAmB,GAAGxB,qDAAW,CAAC,IAAI,EAAE,qBAAqB,EAAE,CAAC,CAAC;IACtE,IAAI,CAACyB,OAAO,GAAG,KAAK;IACpB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,IAAI,GAAGb,KAAK,CAAC,IAAI,CAAC;EAC3B;EACAc,cAAcA,CAACH,OAAO,EAAE;IACpB,IAAI,CAACD,mBAAmB,CAACK,IAAI,CAAC;MAAEJ;IAAQ,CAAC,CAAC;EAC9C;EACA;AACJ;AACA;EACUK,SAASA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,oMAAA;MACd,OAAOC,OAAO,CAACC,OAAO,CAACH,KAAI,CAACN,OAAO,CAAC;IAAC;EACzC;EACMU,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAJ,oMAAA;MACtB;MACA;MACA,IAAI,OAAOK,cAAc,KAAK,WAAW,IAAIA,cAAc,IAAI,IAAI,EAAE;QACjE,MAAMA,cAAc,CAACC,WAAW,CAAC,gBAAgB,CAAC;MACtD;MACAF,MAAI,CAACG,gBAAgB,CAAC,CAAC;MACvBH,MAAI,CAACI,WAAW,CAAC,CAAC;IAAC;EACvB;EACAC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACC,GAAG,EAAE;MACV,IAAI,CAACA,GAAG,CAAC,CAAC;MACV,IAAI,CAACA,GAAG,GAAGC,SAAS;IACxB;EACJ;EACAH,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACE,GAAG,EAAE;MACV,IAAI,CAACA,GAAG,CAAC,CAAC;MACV,IAAI,CAACA,GAAG,GAAGC,SAAS;IACxB;IACA;IACA,IAAI,IAAI,CAACjB,QAAQ,EAAE;MACf,IAAI,CAACD,OAAO,GAAG,KAAK;MACpB;IACJ;IACA;IACA,MAAMmB,KAAK,GAAG,IAAI,CAACjB,IAAI;IACvB,IAAI,OAAOiB,KAAK,KAAK,SAAS,EAAE;MAC5B,IAAI,CAACnB,OAAO,GAAGmB,KAAK;MACpB;IACJ;IACA;IACA,MAAMC,UAAU,GAAG/B,KAAK,CAAC8B,KAAK,CAAC,IAAIA,KAAK;IACxC;IACA,IAAIC,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;MACzB,IAAI,CAACrB,OAAO,GAAG,KAAK;MACpB;IACJ;IACA;IACA,MAAMsB,QAAQ,GAAIC,CAAC,IAAK;MACpB,IAAI,CAACvB,OAAO,GAAGuB,CAAC,CAACC,OAAO;IAC5B,CAAC;IACD,MAAMC,SAAS,GAAGC,MAAM,CAACC,UAAU,CAACP,UAAU,CAAC;IAC/C;IACAK,SAAS,CAACG,WAAW,CAACN,QAAQ,CAAC;IAC/B,IAAI,CAACL,GAAG,GAAG,MAAMQ,SAAS,CAACI,cAAc,CAACP,QAAQ,CAAC;IACnD,IAAI,CAACtB,OAAO,GAAGyB,SAAS,CAACD,OAAO;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIV,gBAAgBA,CAAA,EAAG;IACf,MAAMgB,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMC,QAAQ,GAAG,IAAI,CAACC,EAAE,CAACD,QAAQ;IACjC,MAAME,EAAE,GAAG,IAAI,CAACD,EAAE,CAACE,iBAAiB;IACpC,IAAIC,SAAS,GAAG,KAAK;IACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,EAAE,EAAEG,CAAC,EAAE,EAAE;MACzB,MAAMC,KAAK,GAAGN,QAAQ,CAACK,CAAC,CAAC;MACzB,MAAME,MAAM,GAAGR,SAAS,KAAKZ,SAAS,IAAImB,KAAK,CAACE,EAAE,KAAKT,SAAS;MAChE,IAAIQ,MAAM,EAAE;QACR,IAAIH,SAAS,EAAE;UACX1D,qDAAe,CAAC,yDAAyD,CAAC;UAC1E;QACJ,CAAC,MACI;UACD+D,YAAY,CAACH,KAAK,EAAEC,MAAM,CAAC;UAC3BH,SAAS,GAAG,IAAI;QACpB;MACJ;IACJ;IACA,IAAI,CAACA,SAAS,EAAE;MACZ1D,qDAAe,CAAC,yDAAyD,CAAC;IAC9E;EACJ;EACAgE,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAG/D,qDAAU,CAAC,IAAI,CAAC;IAC7B,OAAQC,qDAAC,CAACE,iDAAI,EAAE;MAAE6D,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACF,IAAI,GAAG,IAAI;QACZ;QACA,CAAC,cAAcA,IAAI,EAAE,GAAG,IAAI;QAC5B,oBAAoB,EAAE,IAAI,CAAC1C;MAC/B;IAAE,CAAC,EAAEpB,qDAAC,CAAC,MAAM,EAAE;MAAE+D,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC5E;EACA,IAAIX,EAAEA,CAAA,EAAG;IAAE,OAAOhD,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW6D,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,SAAS,EAAE,CAAC,gBAAgB,CAAC;MAC7B,UAAU,EAAE,CAAC,aAAa,CAAC;MAC3B,MAAM,EAAE,CAAC,aAAa;IAC1B,CAAC;EAAE;AACP,CAAC;AACD,MAAML,YAAY,GAAGA,CAACR,EAAE,EAAEM,MAAM,KAAK;EACjC,IAAIQ,KAAK;EACT,IAAIC,QAAQ;EACZ,IAAIT,MAAM,EAAE;IACRQ,KAAK,GAAG3D,eAAe;IACvB4D,QAAQ,GAAG3D,eAAe;EAC9B,CAAC,MACI;IACD0D,KAAK,GAAG1D,eAAe;IACvB2D,QAAQ,GAAG5D,eAAe;EAC9B;EACA,MAAM6D,SAAS,GAAGhB,EAAE,CAACgB,SAAS;EAC9BA,SAAS,CAACC,GAAG,CAACH,KAAK,CAAC;EACpBE,SAAS,CAACE,MAAM,CAACH,QAAQ,CAAC;AAC9B,CAAC;AACDnD,SAAS,CAACuD,KAAK,GAAG;EACdC,GAAG,EAAEnE,eAAe;EACpBO,EAAE,EAAEN;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-split-pane.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, m as printIonWarning, e as getIonMode, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\n\nconst splitPaneIosCss = \":host{--side-width:100%;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;contain:strict}:host(.split-pane-visible) ::slotted(.split-pane-main){left:0;right:0;top:0;bottom:0;position:relative;-ms-flex:1;flex:1;-webkit-box-shadow:none;box-shadow:none;overflow:hidden;z-index:0}::slotted(.split-pane-side:not(ion-menu)){display:none}:host{--border:0.55px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--side-min-width:270px;--side-max-width:28%}\";\n\nconst splitPaneMdCss = \":host{--side-width:100%;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;contain:strict}:host(.split-pane-visible) ::slotted(.split-pane-main){left:0;right:0;top:0;bottom:0;position:relative;-ms-flex:1;flex:1;-webkit-box-shadow:none;box-shadow:none;overflow:hidden;z-index:0}::slotted(.split-pane-side:not(ion-menu)){display:none}:host{--border:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--side-min-width:270px;--side-max-width:28%}\";\n\n// TODO(FW-2832): types\nconst SPLIT_PANE_MAIN = 'split-pane-main';\nconst SPLIT_PANE_SIDE = 'split-pane-side';\nconst QUERY = {\n    xs: '(min-width: 0px)',\n    sm: '(min-width: 576px)',\n    md: '(min-width: 768px)',\n    lg: '(min-width: 992px)',\n    xl: '(min-width: 1200px)',\n    never: '',\n};\nconst SplitPane = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionSplitPaneVisible = createEvent(this, \"ionSplitPaneVisible\", 7);\n        this.visible = false;\n        /**\n         * If `true`, the split pane will be hidden.\n         */\n        this.disabled = false;\n        /**\n         * When the split-pane should be shown.\n         * Can be a CSS media query expression, or a shortcut expression.\n         * Can also be a boolean expression.\n         */\n        this.when = QUERY['lg'];\n    }\n    visibleChanged(visible) {\n        this.ionSplitPaneVisible.emit({ visible });\n    }\n    /**\n     * @internal\n     */\n    async isVisible() {\n        return Promise.resolve(this.visible);\n    }\n    async connectedCallback() {\n        // TODO: connectedCallback is fired in CE build\n        // before WC is defined. This needs to be fixed in Stencil.\n        if (typeof customElements !== 'undefined' && customElements != null) {\n            await customElements.whenDefined('ion-split-pane');\n        }\n        this.styleMainElement();\n        this.updateState();\n    }\n    disconnectedCallback() {\n        if (this.rmL) {\n            this.rmL();\n            this.rmL = undefined;\n        }\n    }\n    updateState() {\n        if (this.rmL) {\n            this.rmL();\n            this.rmL = undefined;\n        }\n        // Check if the split-pane is disabled\n        if (this.disabled) {\n            this.visible = false;\n            return;\n        }\n        // When query is a boolean\n        const query = this.when;\n        if (typeof query === 'boolean') {\n            this.visible = query;\n            return;\n        }\n        // When query is a string, let's find first if it is a shortcut\n        const mediaQuery = QUERY[query] || query;\n        // Media query is empty or null, we hide it\n        if (mediaQuery.length === 0) {\n            this.visible = false;\n            return;\n        }\n        // Listen on media query\n        const callback = (q) => {\n            this.visible = q.matches;\n        };\n        const mediaList = window.matchMedia(mediaQuery);\n        // TODO FW-5869\n        mediaList.addListener(callback);\n        this.rmL = () => mediaList.removeListener(callback);\n        this.visible = mediaList.matches;\n    }\n    /**\n     * Attempt to find the main content\n     * element inside of the split pane.\n     * If found, set it as the main node.\n     *\n     * We assume that the main node\n     * is available in the DOM on split\n     * pane load.\n     */\n    styleMainElement() {\n        const contentId = this.contentId;\n        const children = this.el.children;\n        const nu = this.el.childElementCount;\n        let foundMain = false;\n        for (let i = 0; i < nu; i++) {\n            const child = children[i];\n            const isMain = contentId !== undefined && child.id === contentId;\n            if (isMain) {\n                if (foundMain) {\n                    printIonWarning('[ion-split-pane] - Cannot have more than one main node.');\n                    return;\n                }\n                else {\n                    setPaneClass(child, isMain);\n                    foundMain = true;\n                }\n            }\n        }\n        if (!foundMain) {\n            printIonWarning('[ion-split-pane] - Does not have a specified main node.');\n        }\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'd5e30df12f1f1f855da4c66f98076b9dce762c59', class: {\n                [mode]: true,\n                // Used internally for styling\n                [`split-pane-${mode}`]: true,\n                'split-pane-visible': this.visible,\n            } }, h(\"slot\", { key: '3e30d7cf3bc1cf434e16876a0cb2a36377b8e00f' })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"visible\": [\"visibleChanged\"],\n        \"disabled\": [\"updateState\"],\n        \"when\": [\"updateState\"]\n    }; }\n};\nconst setPaneClass = (el, isMain) => {\n    let toAdd;\n    let toRemove;\n    if (isMain) {\n        toAdd = SPLIT_PANE_MAIN;\n        toRemove = SPLIT_PANE_SIDE;\n    }\n    else {\n        toAdd = SPLIT_PANE_SIDE;\n        toRemove = SPLIT_PANE_MAIN;\n    }\n    const classList = el.classList;\n    classList.add(toAdd);\n    classList.remove(toRemove);\n};\nSplitPane.style = {\n    ios: splitPaneIosCss,\n    md: splitPaneMdCss\n};\n\nexport { SplitPane as ion_split_pane };\n"], "names": ["r", "registerInstance", "d", "createEvent", "m", "printIonWarning", "e", "getIonMode", "h", "j", "Host", "k", "getElement", "splitPaneIosCss", "splitPaneMdCss", "SPLIT_PANE_MAIN", "SPLIT_PANE_SIDE", "QUERY", "xs", "sm", "md", "lg", "xl", "never", "SplitPane", "constructor", "hostRef", "ionSplitPaneVisible", "visible", "disabled", "when", "visibleChanged", "emit", "isVisible", "_this", "_asyncToGenerator", "Promise", "resolve", "connectedCallback", "_this2", "customElements", "whenDefined", "styleMainElement", "updateState", "disconnectedCallback", "rmL", "undefined", "query", "mediaQuery", "length", "callback", "q", "matches", "mediaList", "window", "matchMedia", "addListener", "removeListener", "contentId", "children", "el", "nu", "childElementCount", "<PERSON><PERSON><PERSON>", "i", "child", "is<PERSON><PERSON>", "id", "setPaneClass", "render", "mode", "key", "class", "watchers", "toAdd", "toRemove", "classList", "add", "remove", "style", "ios", "ion_split_pane"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}