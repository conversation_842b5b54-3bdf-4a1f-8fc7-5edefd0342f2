"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[2415],{2415:(U,k,l)=>{l.r(k),l.d(k,{ion_toast:()=>Y});var w=l(467),i=l(2734),E=l(6640),b=l(1837),P=l(7930),u=l(3217),D=l(4576),a=l(5756),p=l(9596),h=l(6011);l(1906),l(1653),l(8607);const O=(t,e)=>Math.floor(t/2-e/2),K=(t,e)=>{const n=(0,a.c)(),o=(0,a.c)(),{position:r,top:s,bottom:c}=e,d=(0,b.g)(t).querySelector(".toast-wrapper");switch(o.addElement(d),r){case"top":o.fromTo("transform","translateY(-100%)",`translateY(${s})`);break;case"middle":const f=O(t.clientHeight,d.clientHeight);d.style.top=`${f}px`,o.fromTo("opacity",.01,1);break;default:o.fromTo("transform","translateY(100%)",`translateY(${c})`)}return n.easing("cubic-bezier(.155,1.105,.295,1.12)").duration(400).addAnimation(o)},F=(t,e)=>{const n=(0,a.c)(),o=(0,a.c)(),{position:r,top:s,bottom:c}=e,d=(0,b.g)(t).querySelector(".toast-wrapper");switch(o.addElement(d),r){case"top":o.fromTo("transform",`translateY(${s})`,"translateY(-100%)");break;case"middle":o.fromTo("opacity",.99,0);break;default:o.fromTo("transform",`translateY(${c})`,"translateY(100%)")}return n.easing("cubic-bezier(.36,.66,.04,1)").duration(300).addAnimation(o)},N=(t,e)=>{const n=(0,a.c)(),o=(0,a.c)(),{position:r,top:s,bottom:c}=e,d=(0,b.g)(t).querySelector(".toast-wrapper");switch(o.addElement(d),r){case"top":d.style.setProperty("transform",`translateY(${s})`),o.fromTo("opacity",.01,1);break;case"middle":const f=O(t.clientHeight,d.clientHeight);d.style.top=`${f}px`,o.fromTo("opacity",.01,1);break;default:d.style.setProperty("transform",`translateY(${c})`),o.fromTo("opacity",.01,1)}return n.easing("cubic-bezier(.36,.66,.04,1)").duration(400).addAnimation(o)},V=t=>{const e=(0,a.c)(),n=(0,a.c)(),r=(0,b.g)(t).querySelector(".toast-wrapper");return n.addElement(r).fromTo("opacity",.99,0),e.easing("cubic-bezier(.36,.66,.04,1)").duration(300).addAnimation(n)},Y=class{constructor(t){(0,i.r)(this,t),this.didPresent=(0,i.d)(this,"ionToastDidPresent",7),this.willPresent=(0,i.d)(this,"ionToastWillPresent",7),this.willDismiss=(0,i.d)(this,"ionToastWillDismiss",7),this.didDismiss=(0,i.d)(this,"ionToastDidDismiss",7),this.didPresentShorthand=(0,i.d)(this,"didPresent",7),this.willPresentShorthand=(0,i.d)(this,"willPresent",7),this.willDismissShorthand=(0,i.d)(this,"willDismiss",7),this.didDismissShorthand=(0,i.d)(this,"didDismiss",7),this.delegateController=(0,u.d)(this),this.lockController=(0,P.c)(),this.triggerController=(0,u.e)(),this.customHTMLEnabled=i.l.get("innerHTMLTemplatesEnabled",E.E),this.presented=!1,this.revealContentToScreenReader=!1,this.hasController=!1,this.duration=i.l.getNumber("toastDuration",0),this.layout="baseline",this.keyboardClose=!1,this.position="bottom",this.translucent=!1,this.animated=!0,this.isOpen=!1,this.dispatchCancelHandler=e=>{if((0,u.i)(e.detail.role)){const o=this.getButtons().find(r=>"cancel"===r.role);this.callButtonHandler(o)}},this.createSwipeGesture=e=>{(this.gesture=((t,e,n)=>{const o=(0,b.g)(t).querySelector(".toast-wrapper"),r=t.clientHeight,s=o.getBoundingClientRect();let c=0;const d="middle"===t.position?.5:0,f="top"===t.position?-1:1,x=O(r,s.height),j=[{offset:0,transform:`translateY(-${x+s.height}px)`},{offset:.5,transform:"translateY(0px)"},{offset:1,transform:`translateY(${x+s.height}px)`}],m=(0,a.c)("toast-swipe-to-dismiss-animation").addElement(o).duration(100);switch(t.position){case"middle":c=r+s.height,m.keyframes(j),m.progressStart(!0,.5);break;case"top":c=s.bottom,m.keyframes([{offset:0,transform:`translateY(${e.top})`},{offset:1,transform:"translateY(-100%)"}]),m.progressStart(!0,0);break;default:c=r-s.top,m.keyframes([{offset:0,transform:`translateY(${e.bottom})`},{offset:1,transform:"translateY(100%)"}]),m.progressStart(!0,0)}const $=v=>v*f/c,S=(0,h.createGesture)({el:o,gestureName:"toast-swipe-to-dismiss",gesturePriority:u.O,direction:"y",onMove:v=>{const T=d+$(v.deltaY);m.progressStep(T)},onEnd:v=>{const T=v.velocityY,B=(v.deltaY+1e3*T)/c*f;S.enable(!1);let y=!0,I=1,_=0,L=0;if("middle"===t.position){y=B>=.25||B<=-.25,I=1,_=0;const R=o.getBoundingClientRect(),H=R.top-x,W=(x+R.height)*(v.deltaY<=0?-1:1);m.keyframes([{offset:0,transform:`translateY(${H}px)`},{offset:1,transform:`translateY(${y?`${W}px`:"0px"})`}]),L=W-H}else y=B>=.5,I=y?1:0,_=$(v.deltaY),L=(y?1-_:_)*c;const ot=Math.min(Math.abs(L)/Math.abs(T),200);m.onFinish(()=>{y?(n(),m.destroy()):("middle"===t.position?m.keyframes(j).progressStart(!0,.5):m.progressStart(!0,0),S.enable(!0))},{oneTimeCallback:!0}).progressEnd(I,_,ot)}});return S})(this.el,e,()=>{this.dismiss(void 0,u.G)})).enable(!0)},this.destroySwipeGesture=()=>{const{gesture:e}=this;void 0!==e&&(e.destroy(),this.gesture=void 0)},this.prefersSwipeGesture=()=>{const{swipeGesture:e}=this;return"vertical"===e}}swipeGestureChanged(){this.destroySwipeGesture(),this.presented&&this.prefersSwipeGesture()&&this.createSwipeGesture(this.lastPresentedPosition)}onIsOpenChange(t,e){!0===t&&!1===e?this.present():!1===t&&!0===e&&this.dismiss()}triggerChanged(){const{trigger:t,el:e,triggerController:n}=this;t&&n.addClickListener(e,t)}connectedCallback(){(0,u.j)(this.el),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener()}componentWillLoad(){var t;null!==(t=this.htmlAttributes)&&void 0!==t&&t.id||(0,u.k)(this.el)}componentDidLoad(){!0===this.isOpen&&(0,b.r)(()=>this.present()),this.triggerChanged()}present(){var t=this;return(0,w.A)(function*(){const e=yield t.lockController.lock();yield t.delegateController.attachViewToDom();const{el:n,position:o}=t,s=function z(t,e,n,o){let r;if(r="md"===n?"top"===t?8:-8:"top"===t?10:-10,e&&p.w){!function G(t,e){null===t.offsetParent&&(0,i.m)("[ion-toast] - The positionAnchor element for ion-toast was found in the DOM, but appears to be hidden. This may lead to unexpected positioning of the toast.",e)}(e,o);const s=e.getBoundingClientRect();return"top"===t?r+=s.bottom:"bottom"===t&&(r-=p.w.innerHeight-s.top),{top:`${r}px`,bottom:`${r}px`}}return{top:`calc(${r}px + var(--ion-safe-area-top, 0px))`,bottom:`calc(${r}px - var(--ion-safe-area-bottom, 0px))`}}(o,t.getAnchorElement(),(0,i.e)(t),n);t.lastPresentedPosition=s,yield(0,u.f)(t,"toastEnter",K,N,{position:o,top:s.top,bottom:s.bottom}),t.revealContentToScreenReader=!0,t.duration>0&&(t.durationTimeout=setTimeout(()=>t.dismiss(void 0,"timeout"),t.duration)),t.prefersSwipeGesture()&&t.createSwipeGesture(s),e()})()}dismiss(t,e){var n=this;return(0,w.A)(function*(){var o,r;const s=yield n.lockController.lock(),{durationTimeout:c,position:g,lastPresentedPosition:d}=n;c&&clearTimeout(c);const f=yield(0,u.g)(n,t,e,"toastLeave",F,V,{position:g,top:null!==(o=d?.top)&&void 0!==o?o:"",bottom:null!==(r=d?.bottom)&&void 0!==r?r:""});return f&&(n.delegateController.removeViewFromDom(),n.revealContentToScreenReader=!1),n.lastPresentedPosition=void 0,n.destroySwipeGesture(),s(),f})()}onDidDismiss(){return(0,u.h)(this.el,"ionToastDidDismiss")}onWillDismiss(){return(0,u.h)(this.el,"ionToastWillDismiss")}getButtons(){return this.buttons?this.buttons.map(e=>"string"==typeof e?{text:e}:e):[]}getAnchorElement(){const{position:t,positionAnchor:e,el:n}=this;if(void 0!==e){if("middle"===t&&void 0!==e)return void(0,i.m)('[ion-toast] - The positionAnchor property is ignored when using position="middle".',this.el);if("string"==typeof e){const o=document.getElementById(e);return null===o?void(0,i.m)(`[ion-toast] - An anchor element with an ID of "${e}" was not found in the DOM.`,n):o}if(e instanceof HTMLElement)return e;(0,i.m)("[ion-toast] - Invalid positionAnchor value:",e,n)}}buttonClick(t){var e=this;return(0,w.A)(function*(){const n=t.role;return(0,u.i)(n)||(yield e.callButtonHandler(t))?e.dismiss(void 0,n):Promise.resolve()})()}callButtonHandler(t){return(0,w.A)(function*(){if(t?.handler)try{if(!1===(yield(0,u.s)(t.handler)))return!1}catch(e){(0,i.o)("[ion-toast] - Exception in callButtonHandler:",e)}return!0})()}renderButtons(t,e){if(0===t.length)return;const n=(0,i.e)(this);return(0,i.h)("div",{class:{"toast-button-group":!0,[`toast-button-group-${e}`]:!0}},t.map(r=>(0,i.h)("button",Object.assign({},r.htmlAttributes,{type:"button",class:Q(r),tabIndex:0,onClick:()=>this.buttonClick(r),part:q(r)}),(0,i.h)("div",{class:"toast-button-inner"},r.icon&&(0,i.h)("ion-icon",{"aria-hidden":"true",icon:r.icon,slot:void 0===r.text?"icon-only":void 0,class:"toast-button-icon"}),r.text),"md"===n&&(0,i.h)("ion-ripple-effect",{type:void 0!==r.icon&&void 0===r.text?"unbounded":"bounded"}))))}renderToastMessage(t,e=null){const{customHTMLEnabled:n,message:o}=this;return n?(0,i.h)("div",{key:t,"aria-hidden":e,class:"toast-message",part:"message",innerHTML:(0,E.a)(o)}):(0,i.h)("div",{key:t,"aria-hidden":e,class:"toast-message",part:"message"},o)}renderHeader(t,e=null){return(0,i.h)("div",{key:t,class:"toast-header","aria-hidden":e,part:"header"},this.header)}render(){const{layout:t,el:e,revealContentToScreenReader:n,header:o,message:r}=this,s=this.getButtons(),c=s.filter(x=>"start"===x.side),g=s.filter(x=>"start"!==x.side),d=(0,i.e)(this),f={"toast-wrapper":!0,[`toast-${this.position}`]:!0,[`toast-layout-${t}`]:!0};return"stacked"===t&&c.length>0&&g.length>0&&(0,i.m)("[ion-toast] - This toast is using start and end buttons with the stacked toast layout. We recommend following the best practice of using either start or end buttons with the stacked toast layout.",e),(0,i.h)(i.j,Object.assign({key:"d1ecd90c87700aad4685e230cdd430aa286b8791",tabindex:"-1"},this.htmlAttributes,{style:{zIndex:`${6e4+this.overlayIndex}`},class:(0,D.c)(this.color,Object.assign(Object.assign({[d]:!0},(0,D.g)(this.cssClass)),{"overlay-hidden":!0,"toast-translucent":this.translucent})),onIonToastWillDismiss:this.dispatchCancelHandler}),(0,i.h)("div",{key:"4bfc863417324de69e222054d5cf9c452038b41e",class:f},(0,i.h)("div",{key:"3417940afec0392e81b7d54c7cb00f3ab6c30d47",class:"toast-container",part:"container"},this.renderButtons(c,"start"),void 0!==this.icon&&(0,i.h)("ion-icon",{key:"6bf878fbc85c01e1e5faa9d97d46255a6511a952",class:"toast-icon",part:"icon",icon:this.icon,lazy:!1,"aria-hidden":"true"}),(0,i.h)("div",{key:"54b500348a9c37660c3aff37436d9188e4374947",class:"toast-content",role:"status","aria-atomic":"true","aria-live":"polite"},!n&&void 0!==o&&this.renderHeader("oldHeader","true"),!n&&void 0!==r&&this.renderToastMessage("oldMessage","true"),n&&void 0!==o&&this.renderHeader("header"),n&&void 0!==r&&this.renderToastMessage("header")),this.renderButtons(g,"end"))))}get el(){return(0,i.k)(this)}static get watchers(){return{swipeGesture:["swipeGestureChanged"],isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}},Q=t=>({"toast-button":!0,"toast-button-icon-only":void 0!==t.icon&&void 0===t.text,[`toast-button-${t.role}`]:void 0!==t.role,"ion-focusable":!0,"ion-activatable":!0}),q=t=>(0,u.i)(t.role)?"button cancel":"button";Y.style={ios:":host{--border-width:0;--border-style:none;--border-color:initial;--box-shadow:none;--min-width:auto;--width:auto;--min-height:auto;--height:auto;--max-height:auto;--white-space:normal;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;color:var(--color);font-family:var(--ion-font-family, inherit);contain:strict;z-index:1001;pointer-events:none}:host{inset-inline-start:0}:host(.overlay-hidden){display:none}:host(.ion-color){--button-color:inherit;color:var(--ion-color-contrast)}:host(.ion-color) .toast-button-cancel{color:inherit}:host(.ion-color) .toast-wrapper{background:var(--ion-color-base)}.toast-wrapper{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);pointer-events:auto}.toast-wrapper{inset-inline-start:var(--start);inset-inline-end:var(--end)}.toast-wrapper.toast-top{-webkit-transform:translate3d(0,  -100%,  0);transform:translate3d(0,  -100%,  0);top:0}.toast-wrapper.toast-bottom{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);bottom:0}.toast-container{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;height:inherit;min-height:inherit;max-height:inherit;contain:content}.toast-layout-stacked .toast-container{-ms-flex-wrap:wrap;flex-wrap:wrap}.toast-layout-baseline .toast-content{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center}.toast-icon{-webkit-margin-start:16px;margin-inline-start:16px}.toast-content{min-width:0}.toast-message{-ms-flex:1;flex:1;white-space:var(--white-space)}.toast-button-group{display:-ms-flexbox;display:flex}.toast-layout-stacked .toast-button-group{-ms-flex-pack:end;justify-content:end;width:100%}.toast-button{border:0;outline:none;color:var(--button-color);z-index:0}.toast-icon,.toast-button-icon{font-size:1.4em}.toast-button-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}@media (any-hover: hover){.toast-button:hover{cursor:pointer}}:host{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-radius:14px;--button-color:var(--ion-color-primary, #0054e9);--color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));--max-width:700px;--max-height:478px;--start:10px;--end:10px;font-size:clamp(14px, 0.875rem, 43.4px)}.toast-wrapper{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;display:block;position:absolute;z-index:10}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.toast-translucent) .toast-wrapper{background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}:host(.ion-color.toast-translucent) .toast-wrapper{background:rgba(var(--ion-color-base-rgb), 0.8)}}.toast-wrapper.toast-middle{opacity:0.01}.toast-content{-webkit-padding-start:15px;padding-inline-start:15px;-webkit-padding-end:15px;padding-inline-end:15px;padding-top:15px;padding-bottom:15px}.toast-header{margin-bottom:2px;font-weight:500}.toast-button{-webkit-padding-start:15px;padding-inline-start:15px;-webkit-padding-end:15px;padding-inline-end:15px;padding-top:10px;padding-bottom:10px;min-height:44px;-webkit-transition:background-color, opacity 100ms linear;transition:background-color, opacity 100ms linear;border:0;background-color:transparent;font-family:var(--ion-font-family);font-size:clamp(17px, 1.0625rem, 21.998px);font-weight:500;overflow:hidden}.toast-button.ion-activated{opacity:0.4}@media (any-hover: hover){.toast-button:hover{opacity:0.6}}",md:":host{--border-width:0;--border-style:none;--border-color:initial;--box-shadow:none;--min-width:auto;--width:auto;--min-height:auto;--height:auto;--max-height:auto;--white-space:normal;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;color:var(--color);font-family:var(--ion-font-family, inherit);contain:strict;z-index:1001;pointer-events:none}:host{inset-inline-start:0}:host(.overlay-hidden){display:none}:host(.ion-color){--button-color:inherit;color:var(--ion-color-contrast)}:host(.ion-color) .toast-button-cancel{color:inherit}:host(.ion-color) .toast-wrapper{background:var(--ion-color-base)}.toast-wrapper{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);pointer-events:auto}.toast-wrapper{inset-inline-start:var(--start);inset-inline-end:var(--end)}.toast-wrapper.toast-top{-webkit-transform:translate3d(0,  -100%,  0);transform:translate3d(0,  -100%,  0);top:0}.toast-wrapper.toast-bottom{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);bottom:0}.toast-container{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;height:inherit;min-height:inherit;max-height:inherit;contain:content}.toast-layout-stacked .toast-container{-ms-flex-wrap:wrap;flex-wrap:wrap}.toast-layout-baseline .toast-content{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center}.toast-icon{-webkit-margin-start:16px;margin-inline-start:16px}.toast-content{min-width:0}.toast-message{-ms-flex:1;flex:1;white-space:var(--white-space)}.toast-button-group{display:-ms-flexbox;display:flex}.toast-layout-stacked .toast-button-group{-ms-flex-pack:end;justify-content:end;width:100%}.toast-button{border:0;outline:none;color:var(--button-color);z-index:0}.toast-icon,.toast-button-icon{font-size:1.4em}.toast-button-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}@media (any-hover: hover){.toast-button:hover{cursor:pointer}}:host{--background:var(--ion-color-step-800, var(--ion-background-color-step-800, #333333));--border-radius:4px;--box-shadow:0 3px 5px -1px rgba(0, 0, 0, 0.2), 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);--button-color:var(--ion-color-primary, #0054e9);--color:var(--ion-color-step-50, var(--ion-text-color-step-950, #f2f2f2));--max-width:700px;--start:8px;--end:8px;font-size:0.875rem}.toast-wrapper{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;display:block;position:absolute;opacity:0.01;z-index:10}.toast-content{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:14px;padding-bottom:14px}.toast-header{margin-bottom:2px;font-weight:500;line-height:1.25rem}.toast-message{line-height:1.25rem}.toast-layout-baseline .toast-button-group-start{-webkit-margin-start:8px;margin-inline-start:8px}.toast-layout-stacked .toast-button-group-start{-webkit-margin-end:8px;margin-inline-end:8px;margin-top:8px}.toast-layout-baseline .toast-button-group-end{-webkit-margin-end:8px;margin-inline-end:8px}.toast-layout-stacked .toast-button-group-end{-webkit-margin-end:8px;margin-inline-end:8px;margin-bottom:8px}.toast-button{-webkit-padding-start:15px;padding-inline-start:15px;-webkit-padding-end:15px;padding-inline-end:15px;padding-top:10px;padding-bottom:10px;position:relative;background-color:transparent;font-family:var(--ion-font-family);font-size:0.875rem;font-weight:500;letter-spacing:0.84px;text-transform:uppercase;overflow:hidden}.toast-button-cancel{color:var(--ion-color-step-100, var(--ion-text-color-step-900, #e6e6e6))}.toast-button-icon-only{border-radius:50%;-webkit-padding-start:9px;padding-inline-start:9px;-webkit-padding-end:9px;padding-inline-end:9px;padding-top:9px;padding-bottom:9px;width:36px;height:36px}@media (any-hover: hover){.toast-button:hover{background-color:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08)}.toast-button-cancel:hover{background-color:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.08)}}"}},4576:(U,k,l)=>{l.d(k,{c:()=>E,g:()=>P,h:()=>i,o:()=>D});var w=l(467);const i=(a,p)=>null!==p.closest(a),E=(a,p)=>"string"==typeof a&&a.length>0?Object.assign({"ion-color":!0,[`ion-color-${a}`]:!0},p):p,P=a=>{const p={};return(a=>void 0!==a?(Array.isArray(a)?a:a.split(" ")).filter(h=>null!=h).map(h=>h.trim()).filter(h=>""!==h):[])(a).forEach(h=>p[h]=!0),p},u=/^[a-z][a-z0-9+\-.]*:/,D=function(){var a=(0,w.A)(function*(p,h,A,M){if(null!=p&&"#"!==p[0]&&!u.test(p)){const C=document.querySelector("ion-router");if(C)return h?.preventDefault(),C.push(p,A,M)}return!1});return function(h,A,M,C){return a.apply(this,arguments)}}()}}]);