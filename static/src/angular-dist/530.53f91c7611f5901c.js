"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[530],{530:(E,K,m)=>{m.r(K),m.d(K,{startInputShims:()=>X});var g=m(467),l=m(9596),C=m(4657),y=m(1837),R=m(9043);m(2734),m(6780);const P=new WeakMap,M=(e,t,o,s=0,r=!1)=>{P.has(e)!==o&&(o?H(e,t,s,r):$(e,t))},H=(e,t,o,s=!1)=>{const r=t.parentNode,n=t.cloneNode(!1);n.classList.add("cloned-input"),n.tabIndex=-1,s&&(n.disabled=!0),r.appendChild(n),P.set(e,n);const a="rtl"===e.ownerDocument.dir?9999:-9999;e.style.pointerEvents="none",t.style.transform=`translate3d(${a}px,${o}px,0) scale(0)`},$=(e,t)=>{const o=P.get(e);o&&(P.delete(e),o.remove()),e.style.pointerEvents="",t.style.transform=""},T="input, textarea, [no-blur], [contenteditable]",j="$ionPaddingTimer",B=(e,t,o)=>{const s=e[j];s&&clearTimeout(s),t>0?e.style.setProperty("--keyboard-offset",`${t}px`):e[j]=setTimeout(()=>{e.style.setProperty("--keyboard-offset","0px"),o&&o()},120)},F=(e,t,o)=>{e.addEventListener("focusout",()=>{t&&B(t,0,o)},{once:!0})};let w=0;const x="data-ionic-skip-scroll-assist",V=(e,t,o,s,r,n,i,a=!1)=>{const v=n&&(void 0===i||i.mode===R.a.None);let L=!1;const u=void 0!==l.w?l.w.innerHeight:0,f=S=>{!1!==L?N(e,t,o,s,S.detail.keyboardHeight,v,a,u,!1):L=!0},c=()=>{L=!1,null==l.w||l.w.removeEventListener("ionKeyboardDidShow",f),e.removeEventListener("focusout",c)},h=function(){var S=(0,g.A)(function*(){t.hasAttribute(x)?t.removeAttribute(x):(N(e,t,o,s,r,v,a,u),null==l.w||l.w.addEventListener("ionKeyboardDidShow",f),e.addEventListener("focusout",c))});return function(){return S.apply(this,arguments)}}();return e.addEventListener("focusin",h),()=>{e.removeEventListener("focusin",h),null==l.w||l.w.removeEventListener("ionKeyboardDidShow",f),e.removeEventListener("focusout",c)}},O=e=>{var t;if(document.activeElement===e)return;const o=e.getAttribute("id"),s=e.closest(`label[for="${o}"]`),r=null===(t=document.activeElement)||void 0===t?void 0:t.closest(`label[for="${o}"]`);null!==s&&s===r||(e.setAttribute(x,"true"),e.focus())},N=function(){var e=(0,g.A)(function*(t,o,s,r,n,i,a=!1,v=0,L=!0){if(!s&&!r)return;const u=((e,t,o,s)=>{var r;return((e,t,o,s)=>{const r=e.top,n=e.bottom,i=t.top,v=i+15,u=Math.min(t.bottom,s-o)-50-n,f=v-r,c=Math.round(u<0?-u:f>0?-f:0),h=Math.min(c,r-i),D=Math.abs(h)/.3;return{scrollAmount:h,scrollDuration:Math.min(400,Math.max(150,D)),scrollPadding:o,inputSafeY:4-(r-v)}})((null!==(r=e.closest("ion-item,[ion-item]"))&&void 0!==r?r:e).getBoundingClientRect(),t.getBoundingClientRect(),o,s)})(t,s||r,n,v);if(s&&Math.abs(u.scrollAmount)<4)return O(o),void(i&&null!==s&&(B(s,w),F(o,s,()=>w=0)));if(M(t,o,!0,u.inputSafeY,a),O(o),(0,y.r)(()=>t.click()),i&&s&&(w=u.scrollPadding,B(s,w)),typeof window<"u"){let f;const c=function(){var S=(0,g.A)(function*(){void 0!==f&&clearTimeout(f),window.removeEventListener("ionKeyboardDidShow",h),window.removeEventListener("ionKeyboardDidShow",c),s&&(yield(0,C.c)(s,0,u.scrollAmount,u.scrollDuration)),M(t,o,!1,u.inputSafeY),O(o),i&&F(o,s,()=>w=0)});return function(){return S.apply(this,arguments)}}(),h=()=>{window.removeEventListener("ionKeyboardDidShow",h),window.addEventListener("ionKeyboardDidShow",c)};if(s){const S=yield(0,C.g)(s);if(L&&u.scrollAmount>S.scrollHeight-S.clientHeight-S.scrollTop)return"password"===o.type?(u.scrollAmount+=50,window.addEventListener("ionKeyboardDidShow",h)):window.addEventListener("ionKeyboardDidShow",c),void(f=setTimeout(c,1e3))}c()}});return function(o,s,r,n,i,a){return e.apply(this,arguments)}}(),X=function(){var e=(0,g.A)(function*(t,o){if(void 0===l.d)return;const s="ios"===o,r="android"===o,n=t.getNumber("keyboardHeight",290),i=t.getBoolean("scrollAssist",!0),a=t.getBoolean("hideCaretOnScroll",s),v=t.getBoolean("inputBlurring",!1),L=t.getBoolean("scrollPadding",!0),u=Array.from(l.d.querySelectorAll("ion-input, ion-textarea")),f=new WeakMap,c=new WeakMap,h=yield R.K.getResizeMode(),S=function(){var _=(0,g.A)(function*(d){yield new Promise(I=>(0,y.c)(d,I));const p=d.shadowRoot||d,A=p.querySelector("input")||p.querySelector("textarea"),b=(0,C.f)(d),W=b?null:d.closest("ion-footer");if(A){if(b&&a&&!f.has(d)){const I=((e,t,o)=>{if(!o||!t)return()=>{};const s=a=>{(e=>e===e.getRootNode().activeElement)(t)&&M(e,t,a)},r=()=>M(e,t,!1),n=()=>s(!0),i=()=>s(!1);return(0,y.f)(o,"ionScrollStart",n),(0,y.f)(o,"ionScrollEnd",i),t.addEventListener("blur",r),()=>{(0,y.m)(o,"ionScrollStart",n),(0,y.m)(o,"ionScrollEnd",i),t.removeEventListener("blur",r)}})(d,A,b);f.set(d,I)}if("date"!==A.type&&"datetime-local"!==A.type&&(b||W)&&i&&!c.has(d)){const I=V(d,A,b,W,n,L,h,r);c.set(d,I)}}});return function(p){return _.apply(this,arguments)}}();v&&(()=>{let e=!0,t=!1;const o=document;(0,y.f)(o,"ionScrollStart",()=>{t=!0}),o.addEventListener("focusin",()=>{e=!0},!0),o.addEventListener("touchend",i=>{if(t)return void(t=!1);const a=o.activeElement;if(!a||a.matches(T))return;const v=i.target;v!==a&&(v.matches(T)||v.closest(T)||(e=!1,setTimeout(()=>{e||a.blur()},50)))},!1)})();for(const _ of u)S(_);l.d.addEventListener("ionInputDidLoad",_=>{S(_.detail)}),l.d.addEventListener("ionInputDidUnload",_=>{(_=>{if(a){const d=f.get(_);d&&d(),f.delete(_)}if(i){const d=c.get(_);d&&d(),c.delete(_)}})(_.detail)})});return function(o,s){return e.apply(this,arguments)}}()}}]);