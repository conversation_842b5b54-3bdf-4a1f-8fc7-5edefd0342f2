"use strict";(self.webpackChunkdriver_evaluation_app=self.webpackChunkdriver_evaluation_app||[]).push([[323],{323:(p,r,o)=>{o.r(r),o.d(r,{ion_segment_view:()=>u});var c=o(467),t=o(2734);const u=(()=>{let a=class{constructor(s){(0,t.r)(this,s),this.ionSegmentViewScroll=(0,t.d)(this,"ionSegmentViewScroll",7),this.scrollEndTimeout=null,this.isTouching=!1,this.disabled=!1}handleScroll(s){var e;const{scrollLeft:n,scrollWidth:i,clientWidth:d}=s.target;this.ionSegmentViewScroll.emit({scrollRatio:n/(i-d),isManualScroll:null===(e=this.isManualScroll)||void 0===e||e}),this.resetScrollEndTimeout()}handleScrollStart(){this.scrollEndTimeout&&(clearTimeout(this.scrollEndTimeout),this.scrollEndTimeout=null),this.isTouching=!0}handleTouchEnd(){this.isTouching=!1}resetScrollEndTimeout(){this.scrollEndTimeout&&(clearTimeout(this.scrollEndTimeout),this.scrollEndTimeout=null),this.scrollEndTimeout=setTimeout(()=>{this.checkForScrollEnd()},100)}checkForScrollEnd(){this.isTouching||(this.isManualScroll=void 0)}setContent(s){var e=this;return(0,c.A)(function*(n,i=!0){const l=e.getSegmentContents().findIndex(_=>_.id===n);-1!==l&&(e.isManualScroll=!1,e.resetScrollEndTimeout(),e.el.scrollTo({top:0,left:l*e.el.offsetWidth,behavior:i?"smooth":"instant"}))}).apply(this,arguments)}getSegmentContents(){return Array.from(this.el.querySelectorAll("ion-segment-content"))}render(){const{disabled:s,isManualScroll:e}=this;return(0,t.h)(t.j,{key:"754a374e89fd4dd682eb00497e717242a6f83357",class:{"segment-view-disabled":s,"segment-view-scroll-disabled":!1===e}},(0,t.h)("slot",{key:"77366044eb61f0d4bba305bd6f0ef8fd1e25194b"}))}get el(){return(0,t.k)(this)}};return a.style={ios:":host{display:-ms-flexbox;display:flex;height:100%;overflow-x:scroll;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;scrollbar-width:none;-ms-overflow-style:none}:host::-webkit-scrollbar{display:none}:host(.segment-view-disabled){-ms-touch-action:none;touch-action:none;overflow-x:hidden}:host(.segment-view-scroll-disabled){pointer-events:none}:host(.segment-view-disabled){opacity:0.3}",md:":host{display:-ms-flexbox;display:flex;height:100%;overflow-x:scroll;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;scrollbar-width:none;-ms-overflow-style:none}:host::-webkit-scrollbar{display:none}:host(.segment-view-disabled){-ms-touch-action:none;touch-action:none;overflow-x:hidden}:host(.segment-view-scroll-disabled){pointer-events:none}:host(.segment-view-disabled){opacity:0.3}"},a})()}}]);