<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Driver Evaluations Tree View - Specific for evaluation analysis -->
        <record id="view_tree_driver_evaluations" model="ir.ui.view">
            <field name="name">driver_evaluations_tree_analysis</field>
            <field name="model">rb_delivery.user</field>
            <field name="priority">20</field>
            <field name="arch" type="xml">
                <tree string="Driver Evaluations Analysis" create="false" edit="false">
                    <field name="username" string="Driver Name"/>
                    <field name="average_evaluation_score" string="Avg Score %" widget="progressbar"/>
                    <field name="last_evaluation_score" string="Last Score %" widget="progressbar"/>
                    <field name="total_evaluations" string="Total Evaluations"/>
                    <field name="last_evaluation_date" string="Last Evaluation"/>
                    <field name="role_code" invisible="1"/>
                </tree>
            </field>
        </record>

        <!-- Driver Evaluations Search View - Specific for evaluation analysis -->
        <record id="view_search_driver_evaluations" model="ir.ui.view">
            <field name="name">driver_evaluations_search_analysis</field>
            <field name="model">rb_delivery.user</field>
            <field name="priority">20</field>
            <field name="arch" type="xml">
                <search string="Driver Evaluations Analysis">
                    <field name="username" string="Driver Name"/>
                    <field name="average_evaluation_score"/>
                    <field name="last_evaluation_score"/>

                    <!-- Filters -->
                    <filter name="filter_above_75" string="Above 75%"
                            domain="[('average_evaluation_score', '>=', 75)]"/>
                    <filter name="filter_50_75" string="50% - 75%"
                            domain="[('average_evaluation_score', '>=', 50), ('average_evaluation_score', '&lt;', 75)]"/>
                    <filter name="filter_25_50" string="25% - 50%"
                            domain="[('average_evaluation_score', '>=', 25), ('average_evaluation_score', '&lt;', 50)]"/>
                    <filter name="filter_below_25" string="Below 25%"
                            domain="[('average_evaluation_score', '&lt;', 25)]"/>

                    <separator/>
                    <filter name="filter_has_evaluations" string="Has Evaluations"
                            domain="[('total_evaluations', '>', 0)]"/>
                    <filter name="filter_no_evaluations" string="No Evaluations"
                            domain="[('total_evaluations', '=', 0)]"/>

                    <!-- Group By -->
                    <group expand="0" string="Group By">
                        <filter name="group_by_score_range" string="Score Range"
                                context="{'group_by': 'score_range'}"/>
                        <filter name="group_by_evaluation_count" string="Evaluation Count"
                                context="{'group_by': 'evaluation_count_range'}"/>
                        <filter name="group_by_last_evaluation_month" string="Last Evaluation Month"
                                context="{'group_by': 'last_evaluation_date:month'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Driver Evaluations Graph View - Specific for evaluation analysis -->
        <record id="view_graph_driver_evaluations" model="ir.ui.view">
            <field name="name">driver_evaluations_graph_analysis</field>
            <field name="model">rb_delivery.user</field>
            <field name="priority">20</field>
            <field name="arch" type="xml">
                <graph string="Driver Evaluation Statistics" type="bar">
                    <field name="username" type="row"/>
                    <field name="average_evaluation_score" type="measure"/>
                    <field name="total_evaluations" type="measure"/>
                </graph>
            </field>
        </record>

        <!-- Driver Evaluations Pivot View - Specific for evaluation analysis -->
        <record id="view_pivot_driver_evaluations" model="ir.ui.view">
            <field name="name">driver_evaluations_pivot_analysis</field>
            <field name="model">rb_delivery.user</field>
            <field name="priority">20</field>
            <field name="arch" type="xml">
                <pivot string="Driver Evaluation Analysis">
                    <field name="score_range" type="row"/>
                    <field name="evaluation_count_range" type="col"/>
                    <field name="average_evaluation_score" type="measure"/>
                    <field name="total_evaluations" type="measure"/>
                </pivot>
            </field>
        </record>

        <record id="view_form_rb_delivery_user_evaluation" model="ir.ui.view">
            <field name="name">rb_delivery_user_form_evaluation</field>
            <field name="model">rb_delivery.user</field>
            <field name="inherit_id" ref="rb_delivery.view_form_rb_delivery_user"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@name='button_box']" position="inside">
                    <button name="open_evaluations"
                            type="object"
                            class="btn btn-sm oe_stat_button"
                            attrs="{'invisible': [('role_code', '!=', 'rb_delivery.role_driver')]}">
                         <div class="fa fa-fw fa-list-alt o_button_icon"/>
                         <span class="o_stat_text">Open Evaluations</span>
                    </button>
                </xpath>
                <xpath expr="//notebook" position="inside">
                    <page string="Driver Evaluations" attrs="{'invisible': [('role_code', '!=', 'rb_delivery.role_driver')]}">
                        <group>
                            <group name="evaluation_stats" string="Evaluation Statistics">
                                <field name="average_evaluation_score" widget="progressbar"/>
                                <field name="last_evaluation_score" widget="progressbar"/>
                                <field name="total_evaluations"/>
                                <field name="last_evaluation_date"/>
                            </group>
                        </group>
                        <separator string="Recent Evaluations"/>
                        <field name="evaluation_result_ids"
                               context="{'default_user_id': active_id}"
                               domain="[('user_id', '=', active_id)]">
                            <tree limit="10" default_order="submission_date desc">
                                <field name="submission_date"/>
                                <field name="score" widget="progressbar"/>
                                <field name="feedback"/>
                            </tree>
                        </field>
                    </page>
                </xpath>
            </field>
        </record>

        <!-- Driver Evaluations Action -->
        <record id="action_driver_evaluations" model="ir.actions.act_window">
            <field name="name">Driver Evaluations</field>
            <field name="res_model">rb_delivery.user</field>
            <field name="view_mode">tree,graph,pivot,form</field>
            <field name="view_ids" eval="[(5, 0, 0),
                                          (0, 0, {'view_mode': 'tree', 'view_id': ref('view_tree_driver_evaluations')}),
                                          (0, 0, {'view_mode': 'graph', 'view_id': ref('view_graph_driver_evaluations')}),
                                          (0, 0, {'view_mode': 'pivot', 'view_id': ref('view_pivot_driver_evaluations')})]"/>
            <field name="search_view_id" ref="view_search_driver_evaluations"/>
            <field name="domain">[('role_code', '=', 'rb_delivery.role_driver')]</field>
            <field name="context">{
                'search_default_filter_has_evaluations': 1,
                'search_default_group_by_score_range': 1
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No driver evaluations found!
                </p>
                <p>
                    Driver evaluations will appear here once evaluation links are generated and completed.
                </p>
            </field>
        </record>

        <!-- Menu Item -->
        <menuitem id="menu_driver_evaluations"
                  name="Driver Evaluations"
                  parent="rb_delivery.menu_rb_delivery_user"
                  action="action_driver_evaluations"
                  sequence="10"/>

    </data>
</odoo>
