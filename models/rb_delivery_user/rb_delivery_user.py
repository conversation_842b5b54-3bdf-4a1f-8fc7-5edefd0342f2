# -*- coding: utf-8 -*-

import uuid
from datetime import datetime, timedelta
from odoo import models, fields, api, _
from odoo.exceptions import UserError


class rb_delivery_user_evaluation(models.Model):
    _inherit = 'rb_delivery.user'

    evaluation_link_ids = fields.One2many(
        'olivery_driver_evaluation.link',
        'user_id',
        string='Evaluation Links',
        help='Evaluation links generated for this user'
    )

    evaluation_result_ids = fields.One2many(
        'olivery_driver_evaluation.result',
        'user_id',
        string='Evaluation Results',
        help='Evaluation results for this user'
    )
    
    last_evaluation_date = fields.Datetime(
        string='Last Evaluation Date',
        compute='_compute_last_evaluation_date',
        store=True,
        help='Date of the most recent evaluation'
    )
    
    last_evaluation_score = fields.Float(
        string='Last Evaluation Score',
        compute='_compute_last_evaluation_score',
        store=True,
        help='Score from the most recent evaluation'
    )
    
    average_evaluation_score = fields.Float(
        string='Average Evaluation Score',
        compute='_compute_average_evaluation_score',
        store=True,
        help='Average score across all evaluations'
    )
    
    total_evaluations = fields.Integer(
        string='Total Evaluations',
        compute='_compute_total_evaluations',
        store=True,
        help='Total number of completed evaluations'
    )

    score_range = fields.Selection([
        ('excellent', 'Excellent (75%+)'),
        ('good', 'Good (50-75%)'),
        ('fair', 'Fair (25-50%)'),
        ('poor', 'Poor (<25%)'),
        ('no_data', 'No Evaluations')
    ], string='Score Range', compute='_compute_score_range', store=True)

    evaluation_count_range = fields.Selection([
        ('many', '10+ Evaluations'),
        ('some', '5-9 Evaluations'),
        ('few', '1-4 Evaluations'),
        ('none', 'No Evaluations')
    ], string='Evaluation Count Range', compute='_compute_evaluation_count_range', store=True)

    @api.depends('evaluation_result_ids')
    def _compute_last_evaluation_date(self):
        for user in self:
            if user.evaluation_result_ids:
                user.last_evaluation_date = max(user.evaluation_result_ids.mapped('submission_date'))
            else:
                user.last_evaluation_date = False

    @api.depends('evaluation_result_ids')
    def _compute_last_evaluation_score(self):
        for user in self:
            if user.evaluation_result_ids:
                latest_result = user.evaluation_result_ids.sorted('submission_date', reverse=True)[0]
                user.last_evaluation_score = latest_result.score
            else:
                user.last_evaluation_score = 0.0

    @api.depends('evaluation_result_ids')
    def _compute_average_evaluation_score(self):
        for user in self:
            if user.evaluation_result_ids:
                scores = user.evaluation_result_ids.mapped('score')
                user.average_evaluation_score = sum(scores) / len(scores)
            else:
                user.average_evaluation_score = 0.0

    @api.depends('evaluation_result_ids')
    def _compute_total_evaluations(self):
        for user in self:
            user.total_evaluations = len(user.evaluation_result_ids)

    @api.depends('average_evaluation_score')
    def _compute_score_range(self):
        for user in self:
            if user.average_evaluation_score >= 75:
                user.score_range = 'excellent'
            elif user.average_evaluation_score >= 50:
                user.score_range = 'good'
            elif user.average_evaluation_score >= 25:
                user.score_range = 'fair'
            elif user.average_evaluation_score > 0:
                user.score_range = 'poor'
            else:
                user.score_range = 'no_data'

    @api.depends('total_evaluations')
    def _compute_evaluation_count_range(self):
        for user in self:
            if user.total_evaluations >= 10:
                user.evaluation_count_range = 'many'
            elif user.total_evaluations >= 5:
                user.evaluation_count_range = 'some'
            elif user.total_evaluations >= 1:
                user.evaluation_count_range = 'few'
            else:
                user.evaluation_count_range = 'none'
            
    def test_button_action(self):
        """Simple test method to verify button is working"""
        self.ensure_one()
        raise UserError(_('Button is working! User: %s') % self.username)

    def generate_evaluation_link(self):
        self.ensure_one()

        EvaluationLink = self.env['olivery_driver_evaluation.link']
        link = EvaluationLink.create({
            'user_id': self.id,
        })

        return link

    def open_evaluations(self):
        """Open evaluations for this driver"""
        self.ensure_one()
        return {
            'name': _('Driver Evaluations'),
            'type': 'ir.actions.act_window',
            'res_model': 'olivery_driver_evaluation.result',
            'view_mode': 'tree,form',
            'domain': [('user_id', '=', self.id)],
            'context': {
                'default_user_id': self.id,
                'search_default_user_id': self.id,
            },
            'target': 'current',
        }

