# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class evaluation_config(models.Model):
    _name = 'olivery_driver_evaluation.config'
    _description = 'Driver Evaluation Configuration'
    _rec_name = 'name'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    def get_groups(self):
        groups = self.env['res.groups'].sudo().search([('category_id.code','=','model_rb_delivery')])
        return [('id', 'in', groups.ids)]
    
    @api.model
    def get_status(self):
     status_list=[]
     next_statuses=self.env['rb_delivery.status'].search(['|',('status_type','=',False),('status_type','=','olivery_order')])

     for status in next_statuses:
         status_list.append((status.name,status.title))
     return status_list
    
    @api.model
    def get_default_status(self):
        status=self.env['rb_delivery.status'].search([('default','=',True),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
        return status.name if fields else None

    name = fields.Char(
        string='Configuration Name',
        required=True,
        default='Default Evaluation Configuration'
    )

    evaluation_title = fields.Char(
        string='Evaluation Title',
        default='Driver Evaluation',
        help='Title displayed in the evaluation form',
        track_visibility='onchange'
    )
    
    eligible_role_ids = fields.Many2many(
        'res.groups',
        'olivery_eval_config_role_rel',
        'config_id',
        'role_id',
        domain=get_groups,
        string='Eligible Roles',
        help='User roles that are eligible for evaluation'
    )

    send_on_status = fields.Many2many('rb_delivery.status',string="Status",track_visibility="on_change", domain=[('status_type', 'in', ['olivery_order', False])])
    
    message = fields.Text(string="Message", track_visibility="on_change", help="Message to be sent with the evaluation link")

    device = fields.Many2one('olivery_whatsapp_multi.whatsapp_device',string='Device', track_visibility="on_change")

    evaluation_duration = fields.Integer(
        string='Evaluation Duration (Days)',
        default=7,
        required=True,
        help='Number of days before evaluation link expires'
    )
    
    max_attempts = fields.Integer(
        string='Maximum Attempts',
        default=1,
        required=True,
        help='Maximum number of evaluation attempts per user'
    )
    
    active = fields.Boolean(
        string='Active',
        default=True,
        help='Whether this configuration is currently active',
        track_visibility='onchange'
    )
    
    evaluation_criteria = fields.Text(
        string='Evaluation Criteria',
        help='JSON configuration for evaluation criteria and questions'
    )

    # Dynamic Question Configuration
    question_1_title = fields.Char(
        string='Question 1 Title',
        default='Driving Skills',
        help='Title for the first evaluation question'
    )
    question_1_description = fields.Text(
        string='Question 1 Description',
        default='Overall driving ability, safety awareness, and adherence to traffic rules',
        help='Description for the first evaluation question'
    )
    question_1_active = fields.Boolean(
        string='Question 1 Active',
        default=True,
        help='Whether question 1 is active and visible'
    )

    question_2_title = fields.Char(
        string='Question 2 Title',
        default='Punctuality',
        help='Title for the second evaluation question'
    )
    question_2_description = fields.Text(
        string='Question 2 Description',
        default='Timeliness in pickup, delivery, and scheduling',
        help='Description for the second evaluation question'
    )
    question_2_active = fields.Boolean(
        string='Question 2 Active',
        default=True,
        help='Whether question 2 is active and visible'
    )

    question_3_title = fields.Char(
        string='Question 3 Title',
        default='Customer Service',
        help='Title for the third evaluation question'
    )
    question_3_description = fields.Text(
        string='Question 3 Description',
        default='Quality of customer interaction and service attitude',
        help='Description for the third evaluation question'
    )
    question_3_active = fields.Boolean(
        string='Question 3 Active',
        default=True,
        help='Whether question 3 is active and visible'
    )

    question_4_title = fields.Char(
        string='Question 4 Title',
        default='Vehicle Maintenance',
        help='Title for the fourth evaluation question'
    )
    question_4_description = fields.Text(
        string='Question 4 Description',
        default='Care and maintenance of delivery vehicle',
        help='Description for the fourth evaluation question'
    )
    question_4_active = fields.Boolean(
        string='Question 4 Active',
        default=True,
        help='Whether question 4 is active and visible'
    )

    question_5_title = fields.Char(
        string='Question 5 Title',
        default='Professionalism',
        help='Title for the fifth evaluation question'
    )
    question_5_description = fields.Text(
        string='Question 5 Description',
        default='Professional behavior, appearance, and communication',
        help='Description for the fifth evaluation question'
    )
    question_5_active = fields.Boolean(
        string='Question 5 Active',
        default=True,
        help='Whether question 5 is active and visible'
    )
    
    notification_whatsapp = fields.Boolean(
        string='Send Whatsapp Notifications',
        default=True,
        help='Send Whatsapp Notifications when evaluation links are generated'
    )
    
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.user.company_id
    )

    url_host = fields.Char('URL Host', default='http://localhost')

    @api.constrains('evaluation_duration')
    def _check_evaluation_duration(self):
        for record in self:
            if record.evaluation_duration <= 0:
                raise ValidationError(_('Evaluation duration must be greater than 0 days.'))

    @api.constrains('max_attempts')
    def _check_max_attempts(self):
        for record in self:
            if record.max_attempts <= 0:
                raise ValidationError(_('Maximum attempts must be greater than 0.'))

    def get_dynamic_criteria(self):
        self.ensure_one()
        categories = []

        for i in range(1, 6):
            title_field = f'question_{i}_title'
            desc_field = f'question_{i}_description'
            active_field = f'question_{i}_active'

            if getattr(self, active_field, False) and getattr(self, title_field, False):
                categories.append({
                    'id': f'question_{i}',
                    'name': getattr(self, title_field),
                    'description': getattr(self, desc_field, ''),
                    'max_score': 4,
                    'questions': [{
                        'id': f'rating_{i}',
                        'text': f'How would you rate: {getattr(self, title_field)}?',
                        'type': 'selection',
                        'scale': 4
                    }]
                })

        return {
            'categories': categories
        }

    @api.model
    def get_active_config(self):
        config = self.search([('active', '=', True)], limit=1)
        if not config:
            config = self.create({
                'name': 'Default Configuration',
                'evaluation_duration': 7,
                'max_attempts': 1,
                'active': True,
            })
        return config

    def open_release_document(self):
        self.ensure_one()
        return {
            'type': 'ir.actions.act_url',
            'url': 'https://app.clickup.com/37005098/v/dc/1399ta-12075/1399ta-36735',
            'target': 'new',
        }
