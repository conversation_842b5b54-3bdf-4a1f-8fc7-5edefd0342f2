# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* olivery_driver_evaluation
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-31 18:14+0000\n"
"PO-Revision-Date: 2025-07-31 18:14+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: olivery_driver_evaluation
#: selection:rb_delivery.user,evaluation_count_range:0
msgid "1-4 Evaluations"
msgstr "1-4 تقييمات"

#. module: olivery_driver_evaluation
#: selection:rb_delivery.user,evaluation_count_range:0
msgid "10+ Evaluations"
msgstr "10+ تقييمات"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_search_driver_evaluations
msgid "25% - 50%"
msgstr "25% - 50%"

#. module: olivery_driver_evaluation
#: selection:rb_delivery.user,evaluation_count_range:0
msgid "5-9 Evaluations"
msgstr "5-9 تقييمات"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_search_driver_evaluations
msgid "50% - 75%"
msgstr "50% - 75%"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_form_rb_delivery_user_evaluation
msgid "<span class=\"o_stat_text\">Open Evaluations</span>"
msgstr "<span class=\"o_stat_text\">فتح التقييمات</span>"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_config_form
msgid "<span class=\"o_stat_text\">Release</span>\n"
"                                    <span class=\"o_stat_text\">Document</span>"
msgstr "<span class=\"o_stat_text\">وثيقة</span>\n"
"                                    <span class=\"o_stat_text\">الإصدار</span>"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_link_wizard_form
msgid "<strong>Generate Evaluation Links</strong>"
msgstr "<strong>إنشاء روابط التقييم</strong>"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_link_wizard_form
msgid "<strong>Warning:</strong> Force generate is enabled. This will create new links even for users who already have active evaluation links."
msgstr "<strong>تحذير:</strong> تم تفعيل الإنشاء القسري. سيتم إنشاء روابط جديدة حتى للمستخدمين الذين لديهم روابط تقييم نشطة بالفعل."

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_search_driver_evaluations
msgid "Above 75%"
msgstr "أعلى من 75%"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__active
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_config_search
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_link_search
#: selection:olivery_driver_evaluation.link,state:0
msgid "Active"
msgstr "نشط"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_config_search
msgid "Active Status"
msgstr "حالة النشاط"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__activity_ids
msgid "Activities"
msgstr "الأنشطة"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_result__link_id
msgid "Associated evaluation link"
msgstr "رابط التقييم المرتبط"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_rb_delivery_user__average_evaluation_score
msgid "Average Evaluation Score"
msgstr "متوسط درجة التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_rb_delivery_user__average_evaluation_score
msgid "Average score across all evaluations"
msgstr "متوسط النتيجة عبر جميع التقييمات"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_tree_driver_evaluations
msgid "Avg Score %"
msgstr "متوسط النتيجة %"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_link_form
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_result_form
msgid "Basic Information"
msgstr "المعلومات الأساسية"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_config_form
msgid "Basic Settings"
msgstr "الإعدادات الأساسية"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_search_driver_evaluations
msgid "Below 25%"
msgstr "أقل من 25%"

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/models/rb_delivery_user/rb_delivery_user.py:129
#, python-format
msgid "Button is working! User: %s"
msgstr "الزر يعمل! المستخدم: %s"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_link_wizard_form
msgid "Cancel"
msgstr "إلغاء"

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/models/evaluation_link/evaluation_link.py:156
#, python-format
msgid "Cannot regenerate token for used evaluation link."
msgstr "لا يمكن إعادة إنشاء رمز لرابط تقييم مستخدم."

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__company_id
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_link__company_id
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_result__company_id
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_config_search
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_link_search
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_result_search
msgid "Company"
msgstr "الشركة"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_link__evaluation_url
msgid "Complete URL for the evaluation"
msgstr "الرابط الكامل للتقييم"

#. module: olivery_driver_evaluation
#: model:ir.ui.menu,name:olivery_driver_evaluation.menu_driver_evaluation_config
msgid "Configuration"
msgstr "الإعدادات"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__name
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_config_form
msgid "Configuration Name"
msgstr "اسم الإعداد"

#. module: olivery_driver_evaluation
#: model_terms:ir.actions.act_window,help:olivery_driver_evaluation.action_evaluation_config
msgid "Configure evaluation settings including duration, maximum attempts,\n"
"                    eligible roles, and evaluation criteria."
msgstr "تكوين إعدادات التقييم بما في ذلك المدة والحد الأقصى للمحاولات\n"
"                    والأدوار المؤهلة ومعايير التقييم."

#. module: olivery_driver_evaluation
#: model_terms:ir.actions.act_window,help:olivery_driver_evaluation.action_evaluation_config
msgid "Create your first evaluation configuration!"
msgstr "أنشئ إعداد التقييم الأول الخاص بك!"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_link__created_date
msgid "Created Date"
msgstr "تاريخ الإنشاء"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__create_uid
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_link__create_uid
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_result__create_uid
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_wizard__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__create_date
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_link__create_date
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_result__create_date
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_wizard__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_link_search
msgid "Creation Date"
msgstr "تاريخ الإنشاء"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_rb_delivery_user__last_evaluation_date
msgid "Date of the most recent evaluation"
msgstr "تاريخ أحدث تقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_link__expiry_date
msgid "Date when the evaluation link expires"
msgstr "التاريخ الذي ينتهي فيه رابط التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_link__used_date
msgid "Date when the evaluation was completed"
msgstr "التاريخ الذي تم فيه إكمال التقييم"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_result_form
msgid "Detailed Data"
msgstr "البيانات التفصيلية"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__device
msgid "Device"
msgstr "جهاز"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__display_name
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_link__display_name
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_result__display_name
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_wizard__display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_link__user_id
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_result__user_id
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_link_search
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_result_search
msgid "Driver"
msgstr "سائق"

#. module: olivery_driver_evaluation
#: model:ir.ui.menu,name:olivery_driver_evaluation.menu_driver_evaluation_root
msgid "Driver Evaluation"
msgstr "تقييم السائق"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_pivot_driver_evaluations
msgid "Driver Evaluation Analysis"
msgstr "تحليل تقييم السائق"

#. module: olivery_driver_evaluation
#: model:ir.model,name:olivery_driver_evaluation.model_olivery_driver_evaluation_config
msgid "Driver Evaluation Configuration"
msgstr "إعداد تقييم السائق"

#. module: olivery_driver_evaluation
#: model:ir.model,name:olivery_driver_evaluation.model_olivery_driver_evaluation_link
msgid "Driver Evaluation Link"
msgstr "رابط تقييم السائق"

#. module: olivery_driver_evaluation
#: model:ir.model,name:olivery_driver_evaluation.model_olivery_driver_evaluation_result
msgid "Driver Evaluation Result"
msgstr "نتيجة تقييم السائق"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_graph_driver_evaluations
msgid "Driver Evaluation Statistics"
msgstr "إحصائيات تقييم السائق"

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/models/rb_delivery_user/rb_delivery_user.py:145
#: model:ir.actions.act_window,name:olivery_driver_evaluation.action_driver_evaluations
#: model:ir.ui.menu,name:olivery_driver_evaluation.menu_driver_evaluations
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_form_rb_delivery_user_evaluation
#, python-format
msgid "Driver Evaluations"
msgstr "تقييمات السائق"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_search_driver_evaluations
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_tree_driver_evaluations
msgid "Driver Evaluations Analysis"
msgstr "تحليل تقييمات السائق"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_search_driver_evaluations
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_tree_driver_evaluations
msgid "Driver Name"
msgstr "اسم السائق"

#. module: olivery_driver_evaluation
#: model_terms:ir.actions.act_window,help:olivery_driver_evaluation.action_driver_evaluations
msgid "Driver evaluations will appear here once evaluation links are generated and completed."
msgstr "ستظهر تقييمات السائق هنا بمجرد إنشاء روابط التقييم وإكمالها."

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_link__user_id
msgid "Driver to be evaluated"
msgstr "السائق المراد تقييمه"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_result__user_id
msgid "Driver who was evaluated"
msgstr "السائق الذي تم تقييمه"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_wizard__user_ids
msgid "Drivers"
msgstr "السائقون"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_result_form
msgid "Dynamic Question Scores"
msgstr "درجات الأسئلة الديناميكية"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_config_form
msgid "Dynamic Questions"
msgstr "الأسئلة الديناميكية"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__eligible_role_ids
msgid "Eligible Roles"
msgstr "الأدوار المؤهلة"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.evaluation_app_template
msgid "Evaluating:"
msgstr "جاري التقييم:"

#. module: olivery_driver_evaluation
#: model:ir.actions.act_window,name:olivery_driver_evaluation.action_evaluation_config
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_config_form
msgid "Evaluation Configuration"
msgstr "إعداد التقييم"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_config_tree
msgid "Evaluation Configurations"
msgstr "إعدادات التقييم"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_search_driver_evaluations
msgid "Evaluation Count"
msgstr "عدد التقييمات"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_rb_delivery_user__evaluation_count_range
msgid "Evaluation Count Range"
msgstr "نطاق عدد التقييمات"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__evaluation_criteria
msgid "Evaluation Criteria"
msgstr "معايير التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_result__evaluation_data
msgid "Evaluation Data"
msgstr "بيانات التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__evaluation_duration
msgid "Evaluation Duration (Days)"
msgstr "مدة التقييم (بالأيام)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_result__link_id
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_link_form
msgid "Evaluation Link"
msgstr "رابط التقييم"

#. module: olivery_driver_evaluation
#: model:ir.actions.act_window,name:olivery_driver_evaluation.action_evaluation_link
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_rb_delivery_user__evaluation_link_ids
#: model:ir.ui.menu,name:olivery_driver_evaluation.menu_evaluation_links
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_link_tree
msgid "Evaluation Links"
msgstr "روابط التقييم"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_result_form
msgid "Evaluation Result"
msgstr "نتيجة التقييم"

#. module: olivery_driver_evaluation
#: model:ir.actions.act_window,name:olivery_driver_evaluation.action_evaluation_result
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_rb_delivery_user__evaluation_result_ids
#: model:ir.ui.menu,name:olivery_driver_evaluation.menu_evaluation_results
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_result_tree
msgid "Evaluation Results"
msgstr "نتائج التقييم"

#. module: olivery_driver_evaluation
#: model:ir.ui.menu,name:olivery_driver_evaluation.menu_evaluation_config
msgid "Evaluation Settings"
msgstr "إعدادات التقييم"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_form_rb_delivery_user_evaluation
msgid "Evaluation Statistics"
msgstr "احصائات التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__evaluation_title
msgid "Evaluation Title"
msgstr "عنوان التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_link__token
msgid "Evaluation Token"
msgstr "رمز التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_link__evaluation_url
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_link_form
msgid "Evaluation URL"
msgstr "رابط التقييم"

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/models/evaluation_config/evaluation_config.py:185
#, python-format
msgid "Evaluation duration must be greater than 0 days."
msgstr "يجب أن تكون مدة التقييم أكثر من 0 أيام."

#. module: olivery_driver_evaluation
#: model_terms:ir.actions.act_window,help:olivery_driver_evaluation.action_evaluation_link
msgid "Evaluation links are generated automatically when requested for drivers.\n"
"                    You can manage and monitor all evaluation links from here."
msgstr "يتم إنشاء روابط التقييم تلقائياً عند طلبها للسائقين.\n"
"                    يمكنك إدارة ومراقبة جميع روابط التقييم من هنا."

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_rb_delivery_user__evaluation_link_ids
msgid "Evaluation links generated for this user"
msgstr "روابط التقييم المُنشأة لهذا المستخدم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_rb_delivery_user__evaluation_result_ids
msgid "Evaluation results for this user"
msgstr "نتائج التقييم لهذا المستخدم"

#. module: olivery_driver_evaluation
#: model_terms:ir.actions.act_window,help:olivery_driver_evaluation.action_evaluation_result
msgid "Evaluation results will appear here once drivers complete their evaluations.\n"
"                    You can view detailed scores, feedback, and track performance over time."
msgstr "ستظهر نتائج التقييم هنا بمجرد إكمال السائقين لتقييماتهم.\n"
"                    يمكنك عرض الدرجات التفصيلية والتعليقات وتتبع الأداء عبر الوقت."

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/models/evaluation_link/evaluation_link.py:125
#, python-format
msgid "Evaluation token must be unique."
msgstr "يجب أن يكون رمز التقييم فريداً."

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_link__evaluator_info
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_link_form
msgid "Evaluator Information"
msgstr "معلومات المُقيِّم"

#. module: olivery_driver_evaluation
#: selection:rb_delivery.user,score_range:0
msgid "Excellent (75%+)"
msgstr "ممتاز (75%+)"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_link_search
#: selection:olivery_driver_evaluation.link,state:0
msgid "Expired"
msgstr "منتهي الصلاحية"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_link__expiry_date
msgid "Expiry Date"
msgstr "تاريخ انتهاء الصلاحية"

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/models/evaluation_link_wizard/evaluation_link_wizard.py:67
#, python-format
msgid "Failed to generate links for %d users:\n"
"%s"
msgstr "فشل في إنشاء روابط لـ %d مستخدمين:\n"
"%s"

#. module: olivery_driver_evaluation
#: selection:rb_delivery.user,score_range:0
msgid "Fair (25-50%)"
msgstr "مقبول (25-50%)"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_result_form
msgid "Feedback"
msgstr "التعليقات"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__message_follower_ids
msgid "Followers"
msgstr "المتابعون"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__message_channel_ids
msgid "Followers (Channels)"
msgstr "المتابعون (القنوات)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعون (الشركاء)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_wizard__force_generate
msgid "Force Generate"
msgstr "إنشاء قسري"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_result__feedback
msgid "General Feedback"
msgstr "التقييم العام"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_result__feedback
msgid "General feedback about the driver"
msgstr "تعليقات عامة حول السائق"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_result_form
msgid "General feedback about the driver..."
msgstr "تعليقات عامة حول السائق..."

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_result__submission_date
msgid "Submission Date"
msgstr "تاريخ الإرسال"

#. module: olivery_driver_evaluation
#: model:ir.actions.act_window,name:olivery_driver_evaluation.action_evaluation_link_wizard
#: model:ir.actions.act_window,name:olivery_driver_evaluation.action_evaluation_link_wizard_context
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_link_wizard_form
msgid "Generate Evaluation Links"
msgstr "إنشاء روابط التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model,name:olivery_driver_evaluation.model_olivery_driver_evaluation_wizard
msgid "Generate Evaluation Links Wizard"
msgstr "معالج إنشاء روابط التقييم"

#. module: olivery_driver_evaluation
#: model:ir.ui.menu,name:olivery_driver_evaluation.menu_generate_evaluation_links
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_link_wizard_form
msgid "Generate Links"
msgstr "إنشاء الروابط"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_wizard__force_generate
msgid "Generate links even if active links already exist for the user"
msgstr "إنشاء روابط حتى لو كانت هناك روابط نشطة موجودة بالفعل للمستخدم"

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/models/evaluation_link_wizard/evaluation_link_wizard.py:73
#, python-format
msgid "Generated Evaluation Links"
msgstr "تم إنشاء روابط التقييم"

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/models/evaluation_link_wizard/evaluation_link_wizard.py:88
#, python-format
msgid "Generation Failed"
msgstr "فشل الإنشاء"

#. module: olivery_driver_evaluation
#: selection:rb_delivery.user,score_range:0
msgid "Good (50-75%)"
msgstr "جيد (50-75%)"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_config_search
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_link_search
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_result_search
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_search_driver_evaluations
msgid "Group By"
msgstr "تجميع حسب"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_search_driver_evaluations
msgid "Has Evaluations"
msgstr "لديه تقييمات"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_result_search
msgid "High Scores (80+)"
msgstr "درجات عالية (80+)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__id
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_link__id
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_result__id
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_wizard__id
msgid "ID"
msgstr "المعرف"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_config__message_unread
msgid "If checked new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_config__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_config__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_config_search
msgid "Inactive"
msgstr "غير نشط"

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/models/evaluation_link_wizard/evaluation_link_wizard.py:115
#, python-format
msgid "Ineligible Users Selected"
msgstr "تم اختيار مستخدمين غير مؤهلين"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_link__evaluator_info
msgid "Information about the person conducting the evaluation"
msgstr "معلومات حول الشخص الذي يقوم بالتقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_link__is_expired
msgid "Is Expired"
msgstr "منتهي الصلاحية"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_link__is_used
msgid "Is Used"
msgstr "مُستخدم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_config__evaluation_criteria
msgid "JSON configuration for evaluation criteria and questions"
msgstr "إعداد JSON لمعايير التقييم والأسئلة"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_result__evaluation_data
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_result_form
msgid "JSON data containing detailed evaluation responses"
msgstr "بيانات JSON تحتوي على ردود التقييم التفصيلية"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_tree_driver_evaluations
msgid "Last Evaluation"
msgstr "آخر تقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_rb_delivery_user__last_evaluation_date
msgid "Last Evaluation Date"
msgstr "تاريخ التقييم الأخير"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_search_driver_evaluations
msgid "Last Evaluation Month"
msgstr "شهر التقييم الأخير"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_rb_delivery_user__last_evaluation_score
msgid "Last Evaluation Score"
msgstr "درجة التقييم الأخيرة"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config____last_update
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_link____last_update
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_result____last_update
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_wizard____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_tree_driver_evaluations
msgid "Last Score %"
msgstr "آخر نتيجة %"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__write_uid
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_link__write_uid
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_result__write_uid
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_wizard__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__write_date
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_link__write_date
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_result__write_date
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_wizard__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.evaluation_app_template
msgid "Loading Driver Evaluation"
msgstr "جاري تحميل تقييم السائق"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_result_search
msgid "Low Scores (<60)"
msgstr "درجات منخفضة (<60)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: olivery_driver_evaluation
#: model:ir.ui.menu,name:olivery_driver_evaluation.menu_driver_evaluation_management
msgid "Management"
msgstr "الإدارة"

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/models/evaluation_config/evaluation_config.py:191
#, python-format
msgid "Maximum attempts must be greater than 0."
msgstr "يجب أن يكون الحد الأقصى للمحاولات أكبر من 0."

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_result_search
msgid "Medium Scores (60-79)"
msgstr "درجات متوسطة (60-79)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__message
msgid "Message"
msgstr "الرسالة"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_config__message
msgid "Message to be sent with the evaluation link"
msgstr "الرسالة التي سيتم إرسالها مع رابط التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "موعد النشاط التالي"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__activity_summary
msgid "Next Activity Summary"
msgstr "ملخص النشاط التالي"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__activity_type_id
msgid "Next Activity Type"
msgstr "نوع النشاط التالي"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_search_driver_evaluations
#: selection:rb_delivery.user,evaluation_count_range:0
#: selection:rb_delivery.user,score_range:0
msgid "No Evaluations"
msgstr "لا يوجد تقييمات"

#. module: olivery_driver_evaluation
#: model_terms:ir.actions.act_window,help:olivery_driver_evaluation.action_driver_evaluations
msgid "No driver evaluations found!"
msgstr "لم يتم العثور على تقييمات للسائقين!"

#. module: olivery_driver_evaluation
#: model_terms:ir.actions.act_window,help:olivery_driver_evaluation.action_evaluation_link
msgid "No evaluation links found!"
msgstr "لم يتم العثور على روابط تقييم!"

#. module: olivery_driver_evaluation
#: model_terms:ir.actions.act_window,help:olivery_driver_evaluation.action_evaluation_result
msgid "No evaluation results found!"
msgstr "لم يتم العثور على نتائج تقييم!"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_config__evaluation_duration
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_config_form
msgid "Number of days before evaluation link expires"
msgstr "عدد الأيام قبل انتهاء صلاحية رابط التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__message_has_error_counter
msgid "Number of error"
msgstr "عدد الأخطاء"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_config__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "عدد الرسائل التي تتطلب إجراء"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_config__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_config__message_unread_counter
msgid "Number of unread messages"
msgstr "عدد الرسائل الجديدة"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__send_on_status
msgid "Status"
msgstr "الحالة"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_result__score
msgid "Overall Score"
msgstr "النتيجة الإجمالية"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_result__score
msgid "Overall evaluation score (0-100)"
msgstr "النتيجة الإجمالية للتقييم (0-100)"

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/models/evaluation_result/evaluation_result.py:102
#, python-format
msgid "Overall score must be between 0 and 100."
msgstr "يجب أن تكون النتيجة الإجمالية بين 0 و 100."

#. module: olivery_driver_evaluation
#: selection:olivery_driver_evaluation.config,activity_state:0
msgid "Overdue"
msgstr "متأخر"

#. module: olivery_driver_evaluation
#: selection:olivery_driver_evaluation.config,activity_state:0
msgid "Planned"
msgstr "مخطط"

#. module: olivery_driver_evaluation
#: code:addons/olivery_driver_evaluation/models/evaluation_link_wizard/evaluation_link_wizard.py:37
#, python-format
msgid "Please select at least one driver."
msgstr "يرجى اختيار سائق واحد على الأقل."

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.evaluation_app_template
msgid "Please wait while we load the evaluation form..."
msgstr "يرجى الانتظار أثناء تحميل نموذج التقييم..."

#. module: olivery_driver_evaluation
#: selection:rb_delivery.user,score_range:0
msgid "Poor (<25%)"
msgstr "ضعيف (<25%)"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_config_form
msgid "Question 1"
msgstr "السؤال 1"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__question_1_active
msgid "Question 1 Active"
msgstr "السؤال 1 نشط"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_result__question_1_score
msgid "Question 1 Score"
msgstr "درجة السؤال 1"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__question_1_title
msgid "Question 1 Title"
msgstr "عنوان السؤال 1"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_config_form
msgid "Question 2"
msgstr "السؤال 2"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__question_2_active
msgid "Question 2 Active"
msgstr "السؤال 2 نشط"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_result__question_2_score
msgid "Question 2 Score"
msgstr "درجة السؤال 2"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__question_2_title
msgid "Question 2 Title"
msgstr "عنوان السؤال 2"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_config_form
msgid "Question 3"
msgstr "السؤال 3"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__question_3_active
msgid "Question 3 Active"
msgstr "السؤال 3 نشط"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_result__question_3_score
msgid "Question 3 Score"
msgstr "درجة السؤال 3"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__question_3_title
msgid "Question 3 Title"
msgstr "عنوان السؤال 3"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_config_form
msgid "Question 4"
msgstr "السؤال 4"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__question_4_active
msgid "Question 4 Active"
msgstr "السؤال 4 نشط"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_result__question_4_score
msgid "Question 4 Score"
msgstr "درجة السؤال 4"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__question_4_title
msgid "Question 4 Title"
msgstr "عنوان السؤال 4"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_config_form
msgid "Question 5"
msgstr "السؤال 5"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__question_5_active
msgid "Question 5 Active"
msgstr "السؤال 5 نشط"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_result__question_5_score
msgid "Question 5 Score"
msgstr "درجة السؤال 5"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__question_5_title
msgid "Question 5 Title"
msgstr "عنوان السؤال 5"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_result_form
msgid "Questions 1-3"
msgstr "الأسئلة 1-3"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_result_form
msgid "Questions 4-5"
msgstr "الأسئلة 4-5"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_form_rb_delivery_user_evaluation
msgid "Recent Evaluations"
msgstr "اخر التقييمات"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_link_form
msgid "Regenerate Token"
msgstr "إعادة إنشاء الرمز"

#. module: olivery_driver_evaluation
#: model:ir.ui.menu,name:olivery_driver_evaluation.menu_driver_evaluation_reports
msgid "Reports"
msgstr "التقارير"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__activity_user_id
msgid "Responsible User"
msgstr "المستخدم المسؤول"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_rb_delivery_user__score_range
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_search_driver_evaluations
msgid "Score Range"
msgstr "نطاق النتيجة"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_result__question_1_score
msgid "Score for question 1 (0-10)"
msgstr "الدرجة للسؤال 1 (0-10)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_result__question_2_score
msgid "Score for question 2 (0-10)"
msgstr "الدرجة للسؤال 2 (0-10)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_result__question_3_score
msgid "Score for question 3 (0-10)"
msgstr "الدرجة للسؤال 3 (0-10)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_result__question_4_score
msgid "Score for question 4 (0-10)"
msgstr "الدرجة للسؤال 4 (0-10)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_result__question_5_score
msgid "Score for question 5 (0-10)"
msgstr "الدرجة للسؤال 5 (0-10)"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_rb_delivery_user__last_evaluation_score
msgid "Score from the most recent evaluation"
msgstr "الدرجة من أحدث تقييم"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_config_search
msgid "Search Evaluation Configurations"
msgstr "بحث إعدادات التقييم"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_link_search
msgid "Search Evaluation Links"
msgstr "بحث روابط التقييم"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_result_search
msgid "Search Evaluation Results"
msgstr "بحث نتائج التقييم"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_wizard__user_ids
msgid "Select drivers for whom to generate evaluation links"
msgstr "حدد السائقين الذين سيتم إنشاء روابط التقييم لهم"

#. module: olivery_driver_evaluation
#: model_terms:ir.ui.view,arch_db:olivery_driver_evaluation.view_evaluation_config_form
msgid "Send Settings"
msgstr "إعدادات الإرسال"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,field_description:olivery_driver_evaluation.field_olivery_driver_evaluation_config__notification_whatsapp
msgid "Send Whatsapp Notifications"
msgstr "إرسال إشعارات واتساب"

#. module: olivery_driver_evaluation
#: model:ir.model.fields,help:olivery_driver_evaluation.field_olivery_driver_evaluation_config__notification_whatsapp
msgid "Send Whatsapp Notifications when evaluation links are generated"
msgstr "إرسال إشعارات واتساب عند إنشاء روابط التقييم"

